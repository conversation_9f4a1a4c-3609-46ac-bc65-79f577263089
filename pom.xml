<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
      <groupId>com.wosai</groupId>
      <artifactId>common-parent</artifactId>
      <version>1.0-SNAPSHOT</version>
    </parent>

    <groupId>com.wosai</groupId>
    <artifactId>mpay-sdk-homebrew</artifactId>
    <version>2.4.91</version>

    <packaging>jar</packaging>

    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.encoding>utf-8</maven.compiler.encoding>
        <project.build.sourceEncoding>utf-8</project.build.sourceEncoding>
        <additionalparam>-Xdoclint:none</additionalparam>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpasyncclient</artifactId>
            <version>4.1.3</version>
        </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-simple</artifactId>
        <version>${slf4j.version}</version>
          <scope>provided</scope>
      </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>23.0</version>
        </dependency>
	  <dependency>
		  <groupId>com.squareup.okhttp3</groupId>
		  <artifactId>okhttp</artifactId>
		  <version>4.12.0</version>
	  </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.62</version>
        </dependency>
        <dependency>
            <groupId>cfca.sadk</groupId>
            <artifactId>SADK</artifactId>
            <version>3.7.0.1</version>
        </dependency>
        <dependency>
            <groupId>cfca</groupId>
            <artifactId>logback-cfca-jdk1.6</artifactId>
            <version>4.2.1.0</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.11.0</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
            <version>1.62</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-util</artifactId>
            <version>1.1.8</version>
        </dependency>

        <dependency>
            <groupId>com.ccb</groupId>
            <artifactId>mis</artifactId>
            <version>1.0.8-20250117-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.cmblife</groupId>
            <artifactId>cmblife-ssl-sdk</artifactId>
            <version>1.0.0-202208262-RELEASE</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/junit/junit -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ccb</groupId>
            <artifactId>ccbpay-api-java</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.17</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.11</version>
        </dependency>
        <!-- 注意：此包会导致HTTP接口响应格式转换为xml，所以默认不导入，由业务方自己控制导入 -->
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>2.1.2</version>
            <scope>provided</scope>
        </dependency>
        <!-- ZXing Core -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.5.0</version>
        </dependency>
        <!-- ZXing Java SE Integration -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.5.0</version>
        </dependency>
        <!-- JPOS -->
        <dependency>
            <groupId>org.jpos</groupId>
            <artifactId>jpos</artifactId>
            <version>2.1.8</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <skip>true</skip>
                    <fork>true</fork>
                    <compilerArgument>-XDignore.symbol.file</compilerArgument>
                    <verbose>true</verbose>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <showWarnings>true</showWarnings>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>

        <pluginManagement>
            <plugins>
            </plugins>
        </pluginManagement>
    </build>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </snapshotRepository>
    </distributionManagement>
</project>
