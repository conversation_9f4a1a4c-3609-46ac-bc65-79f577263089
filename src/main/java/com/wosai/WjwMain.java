//package com.wosai;
//
//import cfca.sadk.algorithm.common.PKIException;
//import cfca.sadk.algorithm.sm2.SM2PrivateKey;
//import com.wosai.mpay.api.abc.ABCClient;
//import com.wosai.mpay.api.fjnx.DcepSignature;
//import com.wosai.mpay.api.weixin.*;
//import com.wosai.mpay.exception.MpayApiNetworkError;
//import com.wosai.mpay.exception.MpayException;
//import com.wosai.mpay.util.HttpClientUtils;
//import com.wosai.mpay.util.JsonUtil;
//import com.wosai.mpay.util.SM2Util;
//import okhttp3.*;
//import okhttp3.internal.Util;
//
//import java.io.IOException;
//import java.util.Map;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Executors;
//
//import static com.wosai.mpay.api.alipay.AlipayV2NewClient.SIGN_TYPE_HAIKE;
//
///**
// * Created by wujianwei on 2022/3/4.
// */
//public class WjwMain {
//
//    public static void main(String[] args) throws MpayException, PKIException, IOException {
////        String ke = "MDAwNHsicGF5bW9kZSI6IjIiLCJ0cmFuc3R5cGUiOiIxIiwiY2hhbm5lbG5vIjoiMiIsInRlcm1pbmFsX3NlcmlhbG5vIjoiNzg5NTAzNjg2MTA1NDczNSIsInJlc3Bjb2RlIjoiODEiLCJyZXN1bHQiOiLR6cepyqew3CIsIm1lcmNoYW50X2NvZGUiOiIxMTMxMzAzNTEyMjAxMDQiLCJ0ZXJtaW5hbF9jb2RlIjoiMTJnYzA4NTUiLCJjb3VudG5vIjoiOTUwMTIwMDAwMjg5NzEzIiwiYW1vdW50IjoiMC4wMSIsInBheWFtb3VudCI6IjAuMDAiLCJtYWNkYXRhIjoiSmRIb0VybGVDaGNueXZZSUtrMUc4Y1RVVlpaUmgvRkl6cWphbEsvcllUbXdYYUFmN1pQZk5lOWN6anVYQUQ2NmZhUThjMkpjM2x4NFVZSmhPVmVHVlo2SG80MlI5R0VCWkRiWDdweTZSRUNRT2tQV0E5ZllTVFlPdXM5bHRuWlgweldnQklqbjNubC9lQmZGanVUcWxndHJqQmVBZVBmWEtaTGJJVG9NcGdvPSJ9";
////        String gbk = new ABCClient().getDecodeBase64(ke, "gbk");
////        System.out.println(gbk);
//        String mchPrivateKey = "MIICKjCCAdCgAwIBAgIGAYx/u3n6MAoGCCqBHM9VAYN1MHUxEzARBgNVBAMTClMwMDAwMDAwMDIx\n" +
//                "IjAgBgNVBAsTGUZVSklBTiBSVVJBTCBDUkVESVQgVU5JT04xCzAJBgNVBAoTAklUMQ8wDQYDVQQH\n" +
//                "EwZGVVpIT1UxDzANBgNVBAgTBkZVSklBTjELMAkGA1UEBhMCQ04wHhcNMjMxMjE5MDE0MDQyWhcN\n" +
//                "MzMxMjE5MDE0MDQyWjB1MRMwEQYDVQQDEwpTMDAwMDAwMDAyMSIwIAYDVQQLExlGVUpJQU4gUlVS\n" +
//                "QUwgQ1JFRElUIFVOSU9OMQswCQYDVQQKEwJJVDEPMA0GA1UEBxMGRlVaSE9VMQ8wDQYDVQQIEwZG\n" +
//                "VUpJQU4xCzAJBgNVBAYTAkNOMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE3BtAxxo5HZEcd2wy\n" +
//                "1RnhobUeRa29JzabKbAnX1aukbMaRC1bzXtUbv7JuroiFVJUS535GUyrcvn6VZOpqmZ47KNMMEow\n" +
//                "CwYDVR0PBAQDAgbAMAkGA1UdEwQCMAAwEQYJYIZIAYb4QgEBBAQDAgeAMB0GA1UdJQQWMBQGCCsG\n" +
//                "AQUFBwMCBggrBgEFBQcDBDAKBggqgRzPVQGDdQNIADBFAiBIgdwtxAc3mUZI+PlmylF8NqUAAVPV\n" +
//                "4gsj9+J/VX4RWQIhAOZJOlUHxgxyb9X6uArwyt5uWjcWMVc+KyfHw3CjuMW2";
//
//        System.out.println(SM2Util.getSM2PrivateKey("123456", mchPrivateKey.replaceAll("\\n", "")));
//
//        SM2PrivateKey sm2PrivateKey = SM2Util.getSM2PrivateKey("123456", "MIIClwIBATBHBgoqgRzPVQYBBAIBBgcqgRzPVQFoBDCrgg5ftvRQCFvpdnSjj3XRDTh3w1n4LSVg\n" +
//                "kaa2AEaNg620vKVt01P2WgrAr/+OCEswggJHBgoqgRzPVQYBBAIBBIICNzCCAjMwggHaoAMCAQIC\n" +
//                "BgGO3yTycTAKBggqgRzPVQGDdTB6MRgwFgYDVQQDEw80MTczOTExNTMxMTBaMzMxIjAgBgNVBAsT\n" +
//                "GUZVSklBTiBSVVJBTCBDUkVESVQgVU5JT04xCzAJBgNVBAoTAklUMQ8wDQYDVQQHEwZGVVpIT1Ux\n" +
//                "DzANBgNVBAgTBkZVSklBTjELMAkGA1UEBhMCQ04wHhcNMjQwNDE1MDAyNTI0WhcNMzQwNDE1MDAy\n" +
//                "NTI0WjB6MRgwFgYDVQQDEw80MTczOTExNTMxMTBaMzMxIjAgBgNVBAsTGUZVSklBTiBSVVJBTCBD\n" +
//                "UkVESVQgVU5JT04xCzAJBgNVBAoTAklUMQ8wDQYDVQQHEwZGVVpIT1UxDzANBgNVBAgTBkZVSklB\n" +
//                "TjELMAkGA1UEBhMCQ04wWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAAQoIZzPPWFWg00ORr6fhU2n\n" +
//                "zrs32V4GsCaLXqkCXKOERtQzepYzFHhalipxAniIyG/oIWavw8tXG5tUavp+sgtio0wwSjALBgNV\n" +
//                "HQ8EBAMCBsAwCQYDVR0TBAIwADARBglghkgBhvhCAQEEBAMCB4AwHQYDVR0lBBYwFAYIKwYBBQUH\n" +
//                "AwIGCCsGAQUFBwMEMAoGCCqBHM9VAYN1A0cAMEQCIB1eoJP/1pUVv0aLkSulJgzymFrtyruwAamD\n" +
//                "WgS7TRv5AiBQo+BADuwnzdvjUdjtagtkPt4w/keIMC80HJ8d/1u4+Q==");
//        System.out.println(sm2PrivateKey);
////        byte[] publicKey = SM2Util.getPublicKey(("MIICKDCCAc6gAwIBAgIGAXggboIUMAoGCCqBHM9VAYN1MHQxEjAQBgNVBAMTCTkxMDA2MDAwMDEi\n" +
////                "MCAGA1UECxMZRlVKSUFOIFJVUkFMIENSRURJVCBVTklPTjELMAkGA1UEChMCSVQxDzANBgNVBAcT\n" +
////                "BkZVWkhPVTEPMA0GA1UECBMGRlVKSUFOMQswCQYDVQQGEwJDTjAeFw0yMTAzMTEwODM2NTZaFw0z\n" +
////                "MTAzMTEwODM2NTZaMHQxEjAQBgNVBAMTCTkxMDA2MDAwMDEiMCAGA1UECxMZRlVKSUFOIFJVUkFM\n" +
////                "IENSRURJVCBVTklPTjELMAkGA1UEChMCSVQxDzANBgNVBAcTBkZVWkhPVTEPMA0GA1UECBMGRlVK\n" +
////                "SUFOMQswCQYDVQQGEwJDTjBZMBMGByqGSM49AgEGCCqBHM9VAYItA0IABB/JbasrZOrzz6GTL83y\n" +
////                "TihQK3Dic23syaXxii8U3Ol8EA/wjQOP/WnjTgIzbW8W8C4Dm9HECSQJrR0EyQhsdESjTDBKMAsG\n" +
////                "A1UdDwQEAwIGwDAJBgNVHRMEAjAAMBEGCWCGSAGG+EIBAQQEAwIHgDAdBgNVHSUEFjAUBggrBgEF\n" +
////                "BQcDAgYIKwYBBQUHAwQwCgYIKoEcz1UBg3UDSAAwRQIgW5mP3FU6G6nUv6oUW4+R2BGi+XEls6wb\n" +
////                "/1zI22tIznwCIQDqRkufbIs4u1qdJTiuyeRbkVnDIEaXaO1ZWn7qhjoBow==").getBytes());
////        System.out.println(publicKey);
//
//        byte[] publicKey1 = DcepSignature.getPublicKey("MIICKDCCAc6gAwIBAgIGAXggboIUMAoGCCqBHM9VAYN1MHQxEjAQBgNVBAMTCTkxMDA2MDAwMDEi\n" +
//                "MCAGA1UECxMZRlVKSUFOIFJVUkFMIENSRURJVCBVTklPTjELMAkGA1UEChMCSVQxDzANBgNVBAcT\n" +
//                "BkZVWkhPVTEPMA0GA1UECBMGRlVKSUFOMQswCQYDVQQGEwJDTjAeFw0yMTAzMTEwODM2NTZaFw0z\n" +
//                "MTAzMTEwODM2NTZaMHQxEjAQBgNVBAMTCTkxMDA2MDAwMDEiMCAGA1UECxMZRlVKSUFOIFJVUkFM\n" +
//                "IENSRURJVCBVTklPTjELMAkGA1UEChMCSVQxDzANBgNVBAcTBkZVWkhPVTEPMA0GA1UECBMGRlVK\n" +
//                "SUFOMQswCQYDVQQGEwJDTjBZMBMGByqGSM49AgEGCCqBHM9VAYItA0IABB/JbasrZOrzz6GTL83y\n" +
//                "TihQK3Dic23syaXxii8U3Ol8EA/wjQOP/WnjTgIzbW8W8C4Dm9HECSQJrR0EyQhsdESjTDBKMAsG\n" +
//                "A1UdDwQEAwIGwDAJBgNVHRMEAjAAMBEGCWCGSAGG+EIBAQQEAwIHgDAdBgNVHSUEFjAUBggrBgEF\n" +
//                "BQcDAgYIKwYBBQUHAwQwCgYIKoEcz1UBg3UDSAAwRQIgW5mP3FU6G6nUv6oUW4+R2BGi+XEls6wb\n" +
//                "/1zI22tIznwCIQDqRkufbIs4u1qdJTiuyeRbkVnDIEaXaO1ZWn7qhjoBow==");
//        System.out.println(publicKey1);
//
////        testAsync();
//
//    }
//
//    public static final void testSync(){
//        WeixinClient client = new WeixinClient();
//        client.setConnectTimeout(2000);
//        client.setReadTimeout(12000);
//        ExecutorService ex = Executors.newFixedThreadPool(200);
//        for (int i = 0; i < 1000; i ++) {
//            ex.submit(() -> {
//                RequestBuilder builder = new RequestBuilder();
//                builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
//                builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
//                builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
//                builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
//                builder.set(BusinessFields.OUT_TRADE_NO, "14488687438215665");
//                Map<String,Object> request = builder.build();
//                Map<String, Object> result;
//                try {
//                    result = client.call("https://httpbin.org/delay/11", SIGN_TYPE_HAIKE, null, request);
//                    System.out.println(result);
//                } catch (MpayApiNetworkError | MpayException e) {
//                    e.printStackTrace();
//                }
//            });
//        }
//    }
//
//    public static final void testAsync(){
//        WeixinClient client = new WeixinClient();
//        client.setConnectTimeout(2000);
//        client.setReadTimeout(12000);
//        ExecutorService ex = Executors.newFixedThreadPool(200);
//        for (int i = 0; i < 1000; i ++) {
//            ex.submit(() -> {
//                RequestBuilder builder = new RequestBuilder();
//                builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
//                builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
//                builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
//                builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
//                builder.set(BusinessFields.OUT_TRADE_NO, "14488687438215665");
//                Map<String,Object> request = builder.build();
//                Map<String, Object> result;
//                String url = "https://httpbin.org/delay/11";
//                String requestStr = JsonUtil.toJsonStr(request);
//                Request.Builder requestBuilder = new Request.Builder()
//                        .url(url)
//                        .post(RequestBody.create(MediaType.parse("application/json"), requestStr));
//                Request req = requestBuilder.build();
//
//                HttpClientUtils.getHttpClient(WeixinClient.class.toString(), null, null, 2000, 12000).newCall(req).enqueue(new Callback() {
//                    @Override
//                    public void onFailure(Call call, IOException e) {
//                        e.printStackTrace();
//                    }
//
//                    @Override
//                    public void onResponse(Call call, Response response) throws IOException {
//                        System.out.println(response.body().string());
//                        Util.closeQuietly(response);
//                    }
//                });
//            });
//        }
//
//    }
//}
