package com.wosai.mpay.exception;

/**
 * Created by jian<PERSON> on 14/11/15.
 */
public class WeixinException extends MpayException {
    private String returnCode;
    private String returnMsg;
    private String resultCode;
    private String errCode;
    private String errCodeMsg;


    public WeixinException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 通信状态码 SUCCESS/FAIL
     * @return
     */
    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    /**
     * 通信返回信息
     * @return
     */
    public String getReturnMsg() {
        return returnMsg;
    }

    public void setReturnMsg(String returnMsg) {
        this.returnMsg = returnMsg;
    }

    /**
     * 业务状态码SUCCESS/FAIL
     * @return
     */
    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    /**
     * 业务错误代码
     * @return
     */
    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    /**
     * 业务错误描述
     * @return
     */
    public String getErrCodeMsg() {
        return errCodeMsg;
    }

    public void setErrCodeMsg(String errCodeMsg) {
        this.errCodeMsg = errCodeMsg;
    }
}
