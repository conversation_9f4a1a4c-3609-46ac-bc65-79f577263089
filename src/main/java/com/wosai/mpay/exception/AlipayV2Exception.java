package com.wosai.mpay.exception;

public class AlipayV2Exception extends MpayException {
    private long code;
    private String msg;
    private String subCode;
    private String subMsg;

    public AlipayV2Exception(String code, String msg, String subCode, String subMsg, Throwable cause) {
        super(String.format("%s: %s - %s (%s)", code, subCode, msg, subMsg), cause);
        this.code = Long.parseLong(code);
        this.msg = msg;
        this.subCode = subCode;
        this.subMsg = subMsg;
    }

    public AlipayV2Exception(String code, String msg, String subCode, String subMsg) {
        this(code, msg, subCode, subMsg, null);
    }

    public AlipayV2Exception(Object code, Object msg, Object subCode, Object subMsg) {
        this(code.toString(), msg.toString(), subCode.toString(), subMsg.toString());
    }

    public AlipayV2Exception(Object code, Object msg, Object subCode, Object subMsg, Throwable cause) {
        this(code.toString(), msg.toString(), subCode.toString(), subMsg.toString(), cause);
    }

    public long getCode() {
        return code;
    }
    public String getMsg() {
        return msg;
    }
    public String getSubCode() {
        return subCode;
    }
    public String getSubMsg() {
        return subMsg;
    }

    private static final long serialVersionUID = 1L;
    
}
