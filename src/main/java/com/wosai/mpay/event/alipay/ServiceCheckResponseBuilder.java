package com.wosai.mpay.event.alipay;

import java.util.LinkedHashMap;
import java.util.Map;

import com.wosai.mpay.api.alipay.AlipayV2Config;
import com.wosai.mpay.api.alipay.EventProtocolTags;

public class ServiceCheckResponseBuilder {

    public Map<String, Object> build() {
        
        Map<String, Object> doc = new LinkedHashMap<String, Object>();
        Map<String, Object> root = new LinkedHashMap<String, Object>();
        Map<String, Object> response = new LinkedHashMap<String, Object>();
        
        doc.put(EventProtocolTags.ALIPAY, root);
        root.put(EventProtocolTags.RESPONSE, response);
        response.put("biz_content", AlipayV2Config.PUBLIC_KEY);
        response.put("success", "true");
        return doc;
    }

}
