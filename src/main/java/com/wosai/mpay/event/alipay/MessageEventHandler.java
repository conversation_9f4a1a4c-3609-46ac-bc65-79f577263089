package com.wosai.mpay.event.alipay;

import java.util.Map;

import com.wosai.mpay.api.alipay.EventBusinessFields;
import com.wosai.mpay.util.XmlUtils;

public class MessageEventHandler implements EventHandler {
    private Map<String, Executor> executorMap;

    public MessageEventHandler() {
        
    }

    @Override
    public Map<String, Object> handle(Map<String, String> request) {
        String content = request.get("biz_content");
        
        Map<String, Object> bizContent = XmlUtils.parse(content);
        String msgType = (String)bizContent.get(EventBusinessFields.MSG_TYPE);
        String eventType = (String)bizContent.get(EventBusinessFields.EVENT_TYPE);
        String key = msgType;
        if (eventType!=null){
            key += ":" + eventType;
        }
        Executor executor = executorMap.get(key);
        if (executor != null) {
            executor.execute(request);
        }
        
        String userId = (String)bizContent.get(EventBusinessFields.FROM_USER_ID);
        String createTime = (String)bizContent.get(EventBusinessFields.CREATE_TIME);
        
        return new MessageAckResponseBuilder().toUserId(userId).createTime(createTime).build();
    }

}
