package com.wosai.mpay.event.alipay;

import java.util.LinkedHashMap;
import java.util.Map;

import com.wosai.mpay.api.alipay.AlipayV2Config;
import com.wosai.mpay.api.alipay.EventBusinessFields;
import com.wosai.mpay.api.alipay.EventProtocolTags;

public class MessageAckResponseBuilder {
    String toUserId;
    String createTime;

    public Map<String, Object> build() {
        Map<String, Object> doc = new LinkedHashMap<String, Object>();
        Map<String, Object> root = new LinkedHashMap<String, Object>();
        Map<String, Object> response = new LinkedHashMap<String, Object>();
        Map<String, Object> xml = new LinkedHashMap<String, Object>();

        doc.put(EventProtocolTags.ALIPAY, root);
        root.put(EventProtocolTags.RESPONSE, response);
        response.put(EventProtocolTags.XML, xml);
        
        xml.put(EventBusinessFields.TO_USER_ID, toUserId);
        xml.put(EventBusinessFields.APP_ID, AlipayV2Config.APP_ID);
        xml.put(EventBusinessFields.CREATE_TIME, createTime);
        xml.put(EventBusinessFields.MSG_TYPE, "ack");

        return doc;
    }

    public MessageAckResponseBuilder toUserId(String toUserId) {
        this.toUserId = toUserId;
        return this;
    }
    
    public MessageAckResponseBuilder createTime(String createTime) {
        this.createTime = createTime;
        return this;
    }
}
