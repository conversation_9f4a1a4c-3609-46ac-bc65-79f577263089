package com.wosai.mpay.api.jd;

/**
 * Created by jian<PERSON> on 23/11/15.
 */
public class JDConstants {
    /** 支付异步通知地址 **/
    public static final String NOTIFY_URL = "https://shouqianba.com";

    /** UTF-8字符集 **/
    public static final String CHARSET_UTF8     = "UTF-8";

    /** 人民币 **/
    public static final String CURRENCY_CNY = "CNY";

    /** 默认时间格式 **/
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /** is_success **/
    public static final String IS_SUCCESS_TRUE = "Y";
    public static final String IS_SUCCESS_FALSE = "N";

    /** 订单状态 **/
    public static final String ORDER_STATUS_CREATED = "0";//新建
    public static final String ORDER_STATUS_PAY_INPROGRESS = "1" ;//处理中
    public static final String ORDER_STATUS_PAY_SUCCESS = "2"; //支付成功
    public static final String ORDER_STATUS_PAY_FAIL = "3"; //支付失败
    public static final String ORDER_STATUS_REFUND_INPROGRESS = "4"; //退款中
    public static final String ORDER_STATUS_REFUND_SUCCESS = "5"; //退款成功
    public static final String ORDER_STATUS_REFUND_FAIL = "6"; //退款失败
    public static final String ORDER_STATUS_REFUND_PART = "7"; //部分退款
    public static final String ORDER_STATUS_CANCEL_SUCCESS = "9"; //撤单成功

    /** 订单异步通知付款状态 **/
    public static final String PAY_STATUS_SUCCESS = "0"; //支付成功
    public static final String PAY_STATUS_FAIL = "1";//支付失败

    /** 退款接口返回状态 **/
    public static final String REFUND_STATUS_SUCCESS = "0"; //退款成功
    public static final String REFUND_STATUS_FAIL = "1"; //退款失败
    public static final String REFUND_STATUS_INPROGREE = "2";//退单处理中

    /** 撤单接口返回状态 **/
    public static final String CANCEL_STATUS_SUCCESS = "0"; //撤单成功
    public static final String CANCEL_STATUS_FAIL = "1"; //撤单失败

    /** b2c c2b 错误码 **/
    public static final String FAIL_CODE_MERCHANT_NO_IS_NULL = "000101"; //商户号不能为空
    public static final String FAIL_CODE_INVALIDE_MONEY_FORMAT = "000102"; //金额格式不对
    public static final String FAIL_CODE_NOTIFY_URL_IS_NULL = "000103"; //通知回掉地址不能为空
    public static final String FAIL_CODE_MERCHANT_NOT_EXISTS = "000104"; //商户不存在
    public static final String FAIL_CODE_ORDER_NOT_EXISTS = "000105"; //订单不存在
    public static final String FAIL_CODE_ORDER_REFUNDED = "000106"; //订单已经退款成功
    public static final String FAIL_CODE_REFUND_REQUEST_REPEAT = "000107"; //退单请求重复
    public static final String FAIL_CODE_ORDER_STATUS_ERROR = "000108"; //订单状态不正确,不能退单
    public static final String FAIL_CODE_SIGN_ERROR = "000109"; //签名验证不正确
    public static final String FAIL_CODE_ORDER_AMOUNT_ERROR = "000110"; //订单金额不对
    public static final String FAIL_CODE_GET_USER_FAIL = "000111"; //无法获取用户身份
    public static final String FAIL_CODE_GET_MERCHANT_FAIL = "000113"; //读取商户信息异常
    public static final String FAIL_CODE_CREATE_ORDER_FAIL = "000114"; //生成订单错误
    public static final String FAIL_CODE_SEED_IS_NULL = "000116"; //请求 seed(二维码内容)不能为空
    public static final String FAIL_CODE_PAY_AMOUNT_IS_NULL = "000117"; //请求支付金额不能为空
    public static final String FAIL_CODE_OVER_SINGLE_LIMIT = "000118"; //超过单次最大付款限额
    public static final String FAIL_CODE_INVALID_QRCODE = "000119"; //二维码无效
    public static final String FAIL_CODE_PAY_FAIL = "000120"; //支付失败
    public static final String FAIL_CODE_MISS_DEFAULT_PAY_METHOD = "000121"; //用户未设置默认支付方式
    public static final String FAIL_CODE_ORDER_PAY_INPROGRESS = "000122"; //订单正在支付中
    public static final String FAIL_CODE_INVALID_ORDER_SN = "000123"; //订单号不能空(包括空字符串,空白字符串)
    public static final String FAIL_CODE_ORDER_PAY_SUCCESSED = "000124"; //订单已经被支付
    public static final String FAIL_CODE_ORDER_SN_TOO_LONG = "000128"; //订单号长度不能超过 32 个字符
    public static final String FAIL_CODE_URL_TOO_LONG_ = "000129"; //url 地址不能超过 1024 个字符
    public static final String FAIL_CODE_REFUND_AMOUNT_NOT_ENNOUGH = "000132"; //该订单可退金额不足
    public static final String FAIL_CODE_REFUND_NO_IS_NULL = "000133"; //部分退单退单号不能为空
    public static final String FAIL_CODE_REFUND_NO_TOO_LONG = "000135"; //退单号长度不能超过 32 个字符
    public static final String FAIL_CODE_ORDER_CANCELED = "000138"; //订单已撤销成功
    public static final String FAIL_CODE_SYSTEM_ERROR = "000999"; //服务器内部错误


    //wap支付交易类型
    public static final String WAP_TYPE_PAY = "C";//下单
    public static final String WAP_TYPE_REFUND = "R"; //退款
    public static final String WAP_TYPE_QUERY = "Q"; //查询
    //wap交易状态
    public static final String WAP_STATUS_SUCCESS = "S";//成功
    public static final String WAP_STATUS_FAIL = "F";//失败
    public static final String WAP_STATUS_IN_PROGRESS = "I";//处理中
    //wap code
    public static final String WAP_CODE_SUCCESS = "0000000";//成功

    //wap交易来源
    public static final String WAP_TRADE_SOURCE_PC = "OUT_PC";
    public static final String WAP_TRADE_SOURCE_APP = "OUT_APP";

    //wap版本号
    public static final String WAP_VERSION = "1.0.0";

    //wap连接方式
    public static final String WAP_METHOD_DIRECT = "A";//直连
    public static final String WAP_METHOD_JUMP = "B";//跳转

    //wap 行业类别
    public static final String BUSINESS_TYPE_DEFAULT = "20QB2201";

    //wap支付错误码
    public static final String CODE_EXPARTNER_INFO_UNCORRECT = "EXPARTNER_INFO_UNCORRECT"; //传入商户接口信息不正确 
    public static final String CODE_ILLEGAL_SIGN = "ILLEGAL_SIGN"; //签名验证出错 
    public static final String CODE_ILLEGAL_ARGUMENT = "ILLEGAL_ARGUMENT"; //输入参数有错误 
    public static final String CODE_ILLEGAL_AUTHORITY = "ILLEGAL_AUTHORITY"; //权限不正确
    public static final String CODE_CUSTOMER_NOT_EXIST = "CUSTOMER_NOT_EXIST"; //提交会员不存在 
    public static final String CODE_ILLEGAL_ENCRYPT = "ILLEGAL_ENCRYPT"; //加密不正确 
    public static final String CODE_ILLEGAL_SECURITY_PROFILE = "ILLEGAL_SECURITY_PROFILE"; //未找到匹配的密钥配置 
    public static final String CODE_ILLEGAL_CHARSET = "ILLEGAL_CHARSET"; //字符集不合法 
    public static final String CODE_ILLEGAL_CLIENT_IP = "ILLEGAL_CLIENT_IP"; //客户端IP地址无权访问服务 
    public static final String CODE_SYSTEM_ERROR = "SYSTEM_ERROR"; //系统错误 

    public static final String CODE_OUT_TRADE_NO_EXIST = "OUT_TRADE_NO_EXIST"; //外部交易号已经存在 
    public static final String CODE_TRADE_NOT_EXIST = "TRADE_NOT_EXIST"; //交易不存在 
    public static final String CODE_ILLEGAL_TRADE_TYPE = "ILLEGAL_TRADE_TYPE"; //无效交易类型 
    public static final String CODE_BUYER_USER_NOT_EXIST = "BUYER_USER_NOT_EXIST"; //买家会员不存在 
    public static final String CODE_SELLER_USER_NOT_EXIST = "SELLER_USER_NOT_EXIST"; //卖家会员不存在 
    public static final String CODE_BUYER_SELLER_EQUAL = "BUYER_SELLER_EQUAL"; //买家、卖家是同一帐户 
    public static final String CODE_ILLEGAL_SIGN_TYPE = "ILLEGAL_SIGN_TYPE"; //签名类型不正确 
    public static final String CODE_COMMISION_ID_NOT_EXIST = "COMMISION_ID_NOT_EXIST"; //佣金收取帐户不存在 
    public static final String CODE_COMMISION_SELLER_DUPLICATE = "COMMISION_SELLER_DUPLICATE"; //收取佣金帐户和卖家是同一帐户 
    public static final String CODE_COMMISION_FEE_OUT_OF_RANGE = "COMMISION_FEE_OUT_OF_RANGE"; //佣金金额超出范围 
    public static final String CODE_TOTAL_FEE_OUT_OF_RANGE = "TOTAL_FEE_OUT_OF_RANGE"; //交易总金额超出范围 
    public static final String CODE_ILLEGAL_AMOUNT_FORMAT = "ILLEGAL_AMOUNT_FORMAT"; //非法金额格式
    public static final String CODE_ILLEGAL_TRADE_AMMOUT = "ILLEGAL_TRADE_AMMOUT"; //交易金额不正确
    public static final String CODE_ILLEGAL_TRADE_CURRENCY = "ILLEGAL_TRADE_CURRENCY"; //交易币种不正确
    public static final String CODE_SELF_TIMEOUT_NOT_SUPPORT = "SELF_TIMEOUT_NOT_SUPPORT"; //不支持自定义超时 
    public static final String CODE_COMMISION_NOT_SUPPORT = "COMMISION_NOT_SUPPORT"; //不支持佣金 
    public static final String CODE_VIRTUAL_NOT_SUPPORT = "VIRTUAL_NOT_SUPPORT"; //不支持虚拟収货方式 
    public static final String CODE_SESSION_TIMEOUT = "SESSION_TIMEOUT"; //session超时 
    public static final String CODE_PAYMENT_LIMITED = "PAYMENT_LIMITED"; //支付受限
    public static final String CODE_ILLEGAL_MESSAGE_CODE = "ILLEGAL_MESSAGE_CODE"; //短信验证码不正确
    public static final String CODE_MESSAGE_CODE_TOO_OFETN = "MESSAGE_CODE_TOO_OFETN"; //短信验证码次数过多
    public static final String CODE_MESSAGE_CODE_SEND_FAILED = "MESSAGE_CODE_SEND_FAILED"; //短信验证码发送失败
    public static final String CODE_ILLEGAL_BANK_CARD_NO = "ILLEGAL_BANK_CARD_NO"; //卡号不正确
    public static final String CODE_ILLEGAL_BANK_CARD_CVV = "ILLEGAL_BANK_CARD_CVV"; //CVV不正确
    public static final String CODE_ILLEGAL_BANK_CARD_VALID_PERIOD = "ILLEGAL_BANK_CARD_VALID_PERIOD"; //卡有效期不正确
    public static final String CODE_ILLEGAL_ID_CARD_NO = "ILLEGAL_ID_CARD_NO"; //身份证号码不正确
    public static final String CODE_ILLEGAL_BANK_CARD_NAME = "ILLEGAL_BANK_CARD_NAME"; //持卡人姓名不正确
    public static final String CODE_ILLEGAL_BANK_CARD_TYPE = "ILLEGAL_BANK_CARD_TYPE"; //卡类型不正确
    public static final String CODE_BANKCARD_NOT_OPEN_FASTPAY = "BANKCARD_NOT_OPEN_FASTPAY"; //银行卡号未开通快捷业务
    public static final String CODE_REFUND_OUT_OF_TOTAL_FEE = "REFUND_OUT_OF_TOTAL_FEE"; //退款金额超出可退额度
    public static final String CODE_REFUND_ERROR = "REFUND_ERROR"; //退款金额不正确
    public static final String CODE_REFUND_FAILED = "REFUND_FAILED"; //退款失败
    public static final String CODE_CURRENT_PAY_CANNOT_REVOKE = "CURRENT_PAY_CANNOT_REVOKE"; //当前支付请求状态无法撤销
    public static final String CODE_CURRENT_USER_DIFFERENT_FROM = "CURRENT_USER_DIFFERENT_FROM"; //当前用户和已登录绑定用户不一致
    public static final String CODE_NOT_CURRENT_USER_BANKCARD = "NOT_CURRENT_USER_BANKCARD"; //非当前用户的签约快捷卡支付
    public static final String CODE_PAYCARD_NOT_THE_SIGNCARD = "PAYCARD_NOT_THE_SIGNCARD"; //支付与签约时卡不一致
    public static final String CODE_TRADE_DOING = "TRADE_DOING"; //交易处理中
    public static final String CODE_SIGN_FAILED = "SIGN_FAILED"; //签约失败
    public static final String CODE_ILLEGAL_PAY_TYPE = "ILLEGAL_PAY_TYPE"; //无效支付类型
    public static final String CODE_USER_STATE_ERROR = "USER_STATE_ERROR"; //用户状态错误
    public static final String CODE_INFORMATION_CHECK_FAILED = "INFORMATION_CHECK_FAILED"; //信息校验失败
    public static final String CODE_ORIGINAL_DEAL_NOT_ALLOWED = "ORIGINAL_DEAL_NOT_ALLOWED"; //原交易不允许此操作
    public static final String CODE_CURRENT_DEAL_NOT_ALLOWED = "CURRENT_DEAL_NOT_ALLOWED"; //当前交易不允许此操作
    public static final String CODE_ORIGINAL_DEAL_NOT_EXIST = "ORIGINAL_DEAL_NOT_EXIST"; //无原交易信息
    public static final String CODE_PAY_OPERATE_FREQUENT = "PAY_OPERATE_FREQUENT"; //支付操作频繁
    public static final String CODE_THE_DEAL_ALREADY_SUCCEED = "THE_DEAL_ALREADY_SUCCEED"; //该交易已支付成功
    public static final String CODE_BANK_SYSTEM_NOT_NORMAL = "BANK_SYSTEM_NOT_NORMAL"; //银行系统异常
    public static final String CODE_INTERNAL_SYSTEM_NOT_NORMAL = "INTERNAL_SYSTEM_NOT_NORMAL"; //内部系统异常
    public static final String CODE_BANK_OPERATE_FAILED = "BANK_OPERATE_FAILED"; //银行支付失败
}
