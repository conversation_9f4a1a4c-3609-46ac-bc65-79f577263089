package com.wosai.mpay.api.jd;

/**
 * Created by jian<PERSON> on 23/11/15.
 */
public class BusinessFields {
    public static final String ORDER_NO = "order_no"; //订单号
    public static final String AMOUNT = "amount"; //订单金额
    public static final String SEED = "seed"; //付款码
    public static final String NOTIFY_URL = "notify_url"; //回调地址
    public static final String REFUND_NO = "refund_no"; //退款编号
    public static final String CANCEL_NO = "cancel_no"; //撤单编号
    public static final String TRADE_NAME = "trade_name"; //交易摘要
    public static final String TRADE_DESCRIBLE = "trade_describle";//交易描述 [此字段正确]
    public static final String EXPIRE = "expire";//过期时间 二维码有效时间单位为分

    public static final String WAP_VERSION = "VERSION"; //版本号
    public static final String WAP_MERCHANT = "MERCHANT"; //商户号
    public static final String WAP_METHOD = "METHOD";//接入方式 M:直连， A:跳转
    public static final String WAP_TRADE_NO = "TRADE_NO" ; //网银在线交易号
    public static final String WAP_TYPE = "TYPE"; //交易类型
    public static final String WAP_TRADE = "TRADE";//商户订单号
    public static final String WAP_ORDER = "ORDER";//原商户订单号
    public static final String WAP_AMOUNT = "AMOUNT";//交易金额
    public static final String WAP_CURRENCY = "CURRENCY";//交易币种
    public static final String WAP_USER = "USER";//用户
    public static final String WAP_DATETIME = "DATETIME";//交易时间
    public static final String WAP_NOTE = "NOTE";//交易备注
    public static final String WAP_URL = "URL";//返回地址
    public static final String WAP_NOTICE = "NOTICE";//通知地址
    public static final String WAP_ORDER_TRADE = "ORDER_TRADE";//商品订单号
    public static final String WAP_ORDER_INFO = "ORDER_INFO"; //商品信息描述
    public static final String WAP_STATUS = "STATUS";//交易状态
    public static final String WAP_CODE = "CODE";//返回代码
    public static final String WAP_DESC = "DESC";//返回信息
    public static final String WAP_BUSINESS_TYPE = "BUSINESS_TYPE";//业务类型
    public static final String WAP_TRADE_SOURCE = "TRADE_SOURCE";//交易来源
    public static final String WAP_EXTEND_PARAMS = "extend_params";//订单扩展信息
}
