package com.wosai.mpay.api.jd;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JDSignature;
import com.wosai.mpay.util.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * Created by jianfree on 23/11/15.
 */
public class JDClient {
    public static final Logger logger = LoggerFactory.getLogger(JDClient.class);
    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    private ObjectMapper objectMapper = new ObjectMapper();
    public  Map<String, Object> call(String serviceUrl, String key, Map<String, Object> request) throws MpayException, MpayApiNetworkError {
        Map<String, String> params = new HashMap<String, String>();
        for(String para: request.keySet()){
            if(request.get(para) != null){
                params.put(para, request.get(para).toString());
            }
        }
        params.put(ProtocalFields.SIGN, JDSignature.getSign((Map<String, Object>)(Object)params, key, JDConstants.CHARSET_UTF8).toLowerCase());
        logger.debug("request {}", params);
//        String response = WebUtils.doPost(null, null, serviceUrl, params, JDConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        String response = HttpClientUtils.doPost(JDClient.class.getName(),null, null, serviceUrl, params, JDConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        logger.debug("response {}", response);
        return parseJDResponse(response);
    }

    /**
     * 解析京东返回内容
     * @param response
     * @return
     * @throws MpayException
     */
    private Map<String, Object> parseJDResponse(String response) throws MpayException {
        try {
            Map<String, Object> result = objectMapper.readValue(response.getBytes(JDConstants.CHARSET_UTF8), Map.class);
            return result;
        } catch (IOException e) {
            throw new MpayException("can not transfer response to map", e);
        }
    }
    private static String generateTradeNo() {
        Random random = new Random();
        return String.format("%d%d", System.currentTimeMillis(), random.nextInt(10000));
    }

    public static void main(String[] args) throws MpayException, MpayApiNetworkError {
//        testPay();
//        testCancel();
//        testRefund();
//        testCancel();
//        testQuery();
//        testPrecreate();
    }

    public static void testPay() throws MpayException, MpayApiNetworkError {
        JDClient client = new JDClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocalFields.MERCHANT_NO, JDConfig.MERCHANT_NO);
        builder.set(BusinessFields.ORDER_NO, generateTradeNo());
        builder.set(BusinessFields.AMOUNT, "0.02");
        builder.set(BusinessFields.SEED, "1814488142671883734251");
        builder.set(BusinessFields.NOTIFY_URL, JDConfig.NOTIFY_URL);
        Map<String, Object> result = client.call(JDConfig.PAY, JDConfig.MERCHANT_KEY, builder.build());

    }

    public static void testCancel() throws MpayException, MpayApiNetworkError {
        JDClient client = new JDClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocalFields.MERCHANT_NO, JDConfig.MERCHANT_NO);
        builder.set(BusinessFields.ORDER_NO, "14489583626491604");
        builder.set(BusinessFields.CANCEL_NO, generateTradeNo());
        builder.set(BusinessFields.AMOUNT, "0.02");
        Map<String, Object> result = client.call(JDConfig.CANCEL, JDConfig.MERCHANT_KEY, builder.build());
    }

    public static void testQuery() throws MpayException, MpayApiNetworkError {
        JDClient client = new JDClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocalFields.MERCHANT_NO, JDConfig.MERCHANT_NO);
        builder.set(BusinessFields.ORDER_NO, "7894259244096354");
        Map<String, Object> result = client.call(JDConfig.QUERY, JDConfig.MERCHANT_KEY, builder.build());
        System.out.println(result);
    }

    public static void testRefund() throws MpayException, MpayApiNetworkError {
        JDClient client = new JDClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocalFields.MERCHANT_NO, JDConfig.MERCHANT_NO);
        builder.set(BusinessFields.ORDER_NO, "14489583626491604");
        builder.set(BusinessFields.REFUND_NO, generateTradeNo());
        builder.set(BusinessFields.AMOUNT, "0.01");
        builder.set(BusinessFields.NOTIFY_URL, JDConfig.NOTIFY_URL);
        Map<String, Object> result = client.call(JDConfig.REFUND, JDConfig.MERCHANT_KEY, builder.build());
    }
    public static void testPrecreate() throws  MpayException, MpayApiNetworkError {
        JDClient client = new JDClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocalFields.MERCHANT_NO, JDConfig.MERCHANT_NO);
        builder.set(BusinessFields.ORDER_NO, generateTradeNo());
        builder.set(BusinessFields.AMOUNT, "0.01");
        builder.set(BusinessFields.TRADE_NAME, "苹果手机");
        builder.set(BusinessFields.TRADE_DESCRIBLE, "苹果手机,苹果手机");
        builder.set(BusinessFields.NOTIFY_URL, JDConfig.NOTIFY_URL);
        builder.set(BusinessFields.EXPIRE, "10");
        Map<String, Object> result = client.call(JDConfig.PRE_CREATE, JDConfig.MERCHANT_KEY, builder.build());
    }



    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
}
