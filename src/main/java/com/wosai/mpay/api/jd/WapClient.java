package com.wosai.mpay.api.jd;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Base64;
import com.wosai.mpay.util.JDSignature;
import com.wosai.mpay.util.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * Created by jianfree on 17/1/16.
 */
public class WapClient {
    public static final Logger logger = LoggerFactory.getLogger(WapClient.class);
    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    private ObjectMapper objectMapper = new ObjectMapper();
    public Map<String, Object> call(String serviceUrl, String key, Map<String, Object> request) throws MpayException, MpayApiNetworkError {
        String data = getBase64Data(request, JDConstants.CHARSET_UTF8);
        Map<String, String> postData = new HashMap<String, String>();
        postData.put(ProtocalFields.WAP_CHAR, JDConstants.CHARSET_UTF8);
        postData.put(ProtocalFields.WAP_DATA, data);
        postData.put(ProtocalFields.WAP_SIGN, JDSignature.getMd5Sign(data, key, JDConstants.CHARSET_UTF8));
        logger.debug("request {} postData {}", request, postData);
        String response = WebUtils.doPost(null, null, serviceUrl, postData, JDConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        logger.debug("response {}", response);
        Map result = parseJDResponse(response);
        logger.debug("result {}", result);
        return result;
    }

    public String getBase64Data(Map<String, Object> request, String charset) throws MpayException {
        try {
            String data = Base64.encode(objectMapper.writeValueAsString(request).getBytes(charset));
            return data;
        } catch (Exception e) {
            throw new MpayException("Base64 decode error: ", e);
        }
    }

    /**
     * 解析京东返回内容
     * @param response
     * @return
     * @throws MpayException
     */
    private Map<String, Object> parseJDResponse(String response) throws MpayException {
        Map<String, Object> map = new HashMap<String, Object>();
        String [] params = response.split("&");
        for (int i = 0; i < params.length; i++) {
            int equalsIndex = params[i].indexOf("=");
            if(equalsIndex != -1){
                map.put(params[i].substring(0, equalsIndex), params[i].substring(equalsIndex + 1));
            }
        }
        String data = map.get(ProtocalFields.WAP_DATA) + "";
        try {
            map.put(ProtocalFields.WAP_DATA, objectMapper.readValue(Base64.decode(data), Map.class));
        } catch (IOException e) {
            throw new MpayException("can not parse jd response: ", e);
        }
        return map;
    }



    public static void main(String[] args) throws MpayException, MpayApiNetworkError {
//        testPrecreate();
        testQuery();
    }

    private static String generateTradeNo() {
        Random random = new Random();
        return String.format("%d%d", System.currentTimeMillis(), random.nextInt(10000));
    }

    public static void testPrecreate() throws  MpayException, MpayApiNetworkError {
        WapClient client = new WapClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.WAP_VERSION, JDConstants.WAP_VERSION);
        builder.set(BusinessFields.WAP_MERCHANT, "110147622003");
        builder.set(BusinessFields.WAP_METHOD, JDConstants.WAP_METHOD_DIRECT);
        builder.set(BusinessFields.WAP_TYPE, JDConstants.WAP_TYPE_PAY);
        builder.set(BusinessFields.WAP_TRADE, generateTradeNo());
        builder.set(BusinessFields.WAP_AMOUNT, "1");
        builder.set(BusinessFields.WAP_CURRENCY, JDConstants.CURRENCY_CNY);
        builder.set(BusinessFields.WAP_USER, "wjw");
        builder.set(BusinessFields.WAP_NOTICE, "http://upay.test.shouqianba.com/upay");
        builder.set(BusinessFields.WAP_ORDER_INFO, "ipad 32G 苹果");
        builder.set(BusinessFields.WAP_TRADE_SOURCE, JDConstants.WAP_TRADE_SOURCE_APP);
        builder.set(BusinessFields.WAP_BUSINESS_TYPE, JDConstants.BUSINESS_TYPE_DEFAULT);
        Map<String, Object> result = client.call(JDConfig.WAP, "360080003476220012abc", builder.build());
    }

    public static void testQuery() throws MpayException, MpayApiNetworkError {
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.WAP_VERSION, JDConstants.WAP_VERSION);
        builder.set(BusinessFields.WAP_MERCHANT, "110147622003");
        builder.set(BusinessFields.WAP_TYPE, JDConstants.WAP_TYPE_QUERY);
        builder.set(BusinessFields.WAP_TRADE, "7893259247301338");
        Map<String,Object> result = new WapClient().call(JDConfig.WAP, "360080003476220012abc", builder.build());

    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
}
