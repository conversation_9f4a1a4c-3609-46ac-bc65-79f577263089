package com.wosai.mpay.api.jd;

/**
 * Created by jian<PERSON> on 16/1/16.
 */
public class WapFields {
    /** js-sdk 提交参数 **/
    public static final String MERCHANT_TOKEN = "merchantToken"; //可不填
    public static final String MERCHANT_JD_PIN = "merchantJdPin"; //可不填
    public static final String MERCHANT_OUTER_ORDERNUM = "merchantOuterOrderNum"; //外部商户订单号
    public static final String MERCHANT_USER_ID = "merchantUserId" ;//商户账户体系的用户ID
    public static final String MERCHANT_MOBILE = "merchantMobile"; //用户在商户的手机号
    public static final String MERCHANT_NUM = "merchantNum"; //商户号
    public static final String MERCHANT_REMARK = "merchantRemark"; //商户备注
    public static final String MERCHANT_TRADE_NUM = "merchantTradeNum"; //交易流水号
    public static final String MERCHANT_TRADE_NAME = "merchantTradeName"; //商品名称
    public static final String MERCHANT_TRADE_DESCRIPTION = "merchantTradeDescription"; //交易描述
    public static final String MERCHANT_TRADE_TIME = "merchantTradeTime"; //交易时间
    public static final String MERCHANT_TRADE_AMOUNT = "merchantTradeAmount"; //交易金额
    public static final String MERCHANT_CURRENCY = "merchantCurrency"; //固定填CNY
    public static final String MERCHANT_NOTIFY_URL = "merchantNotifyUrl"; //异步通知页面地址
    public static final String MERCHANT_SIGN = "merchantSign"; //用户交易信息签名
    public static final String DATA = "data"; //扩展字段
    public static final String CP_TRADE_NUM = "cpTradeNum"; //这里商户通过之前的下单接口返回的交易流水号



}
