package com.wosai.mpay.api.jd;

/**
 * Created by jian<PERSON> on 23/11/15.
 */
public class JDConfig {
    public static final String PAY = "https://pcplatform.jdpay.com/api/pay"; //扣款接口
    public static final String QUERY = "https://payscc.jdpay.com/order/query"; //查询接口
    public static final String REFUND = "https://payscc.jdpay.com/order/refund"; //退款接口
    public static final String CANCEL = "https://payscc.jdpay.com/order/cancel"; //撤单接口
    public static final String PRE_CREATE = "https://payscc.jdpay.com/req/code"; //预下单
    public static final String WAP = "https://tmapi.jdpay.com/jd.htm";//wap网关接口地址

    /** 支付异步通知地址 **/
    public static final String NOTIFY_URL = "https://shouqianba.com";

    public static final String MERCHANT_NO = "23236484";
    public static final String MERCHANT_KEY = "";
}
