package com.wosai.mpay.api.jd;

/**
 * Created by jian<PERSON> on 23/11/15.
 */
public class ResponseFields {

    public static final String IS_SUCCESS = "is_success";//成功标志
    public static final String FAIL_CODE = "fail_code";//失败code
    public static final String FAIL_REASON = "fail_reason";//失败原因
    public static final String DATA = "data";//返回数据
    public static final String TRADE_NO = "trade_no";//交易流水号
    public static final String USER = "user";//支付用户 的名称
    public static final String PROMOTION_AMOUNT = "promotionAmount";//营销金额
    public static final String AMOUNT = "amount";//支付金额，商品金额
    public static final String PAY_TIME = "pay_time";//支付时间
    public static final String STATUS = "status";//订单状态
    public static final String QRCODE = "qrcode";//生成二维码图片中包含的文本字符
    public static final String NOTIFY_DATA = "DATA";//异步通知base64的内容
    public static final String NOTIFY_SIGN = "SIGN";//DATA通过MD5加密过后签名数据
    
    
    public static final String RESPONSE_KEY_STATUS = DATA + "." + STATUS;
    public static final String RESPONSE_KEY_QRCODE = DATA + "." + QRCODE;
}
