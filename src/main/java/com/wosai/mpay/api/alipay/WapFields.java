package com.wosai.mpay.api.alipay;

public class WapFields {
    public static final String PARTNER = "partner";     // 2088101000137799
    public static final String INPUT_CHARSET = "_input_charset";
    public static final String FORMAT = "format";       // xml
    public static final String VERSION = "v";           // 2.0
    public static final String SERVICE = "service";     // alipay.wap.auth.authAndExecute
    public static final String REQ_ID = "req_id";       // 请求ID
    public static final String SEC_ID = "sec_id";       // 0001, MD5
    public static final String SIGN = "sign";           // 签名
    public static final String REQ_DATA = "req_data";   // 业务参数
    
    public static final String DIRECT_TRADE_CREATE_REQ = "direct_trade_create_req";
    public static final String AUTH_AND_EXECUTE_REQ = "auth_and_execute_req"; 

    public static final String SUBECT = "subject";
    public static final String OUT_TRADE_NO = "out_trade_no";
    public static final String TOTAL_FEE = "total_fee";
    public static final String SELLER_ACCOUNT_NAME = "seller_account_name";
    public static final String CALL_BACK_URL = "call_back_url";
    public static final String NOTIFY_URL = "notify_url";
    public static final String OUT_USER = "out_user";
    public static final String MERCHANT_URL = "merchant_url";
    public static final String PAY_EXPIRE = "pay_expire";
    
    public static final String RES_DATA = "res_data";
    public static final String RES_ERROR = "res_error";
}
