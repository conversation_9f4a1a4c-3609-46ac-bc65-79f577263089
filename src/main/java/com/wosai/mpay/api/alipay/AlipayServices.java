package com.wosai.mpay.api.alipay;

public class AlipayServices {
    public static final String ALIPAY_SERVICE_CHECK = "alipay.service.check";
    public static final String ALIPAY_MOBILE_PUBLIC_MESSAGE_NOTIFY = "alipay.mobile.public.message.notify";

    // 先享后付
    public static final String API_FITNESS_NEWSAVEORUPDATE = "api.fitness.newSaveOrUpdate";                                 // 新商户新增/修改
    public static final String API_FITNESS_ORDER_REFUND = "api.fitness.orderRefund";                                        // 订单退款
    public static final String API_FITNESS_SUBSCRIPTION_QUERY = "api.fitness.subscriptionQuery";                            // 订购查询
    public static final String API_FITNESS_SUBSCRIPTION_PAUSE = "api.fitness.subscriptionPause";                            // 订购暂停
    public static final String API_FITNESS_SUBSCRIPTION_REGAIN = "api.fitness.subscriptionRegain";                          // 订购恢复
    public static final String API_FITNESS_SUBSCRIPTION_POSTPONE = "api.fitness.subscriptionPostpone";                      // 订购延期
    public static final String API_FITNESS_SUBSCRIPTION_SURRENDER = "api.fitness.subscriptionSurrender";                    // 订购解约
    public static final String API_FITNESS_SUBSCRIPTION_SPECIFIC_CANCEL = "api.fitness.subscriptionSpecificCancel";         // 指定期数取消
    public static final String API_FITNESS_ORDER_QUERY = "api.fitness.orderQuery";                                          // 订单查询
    public static final String API_FITNESS_LOGO_UPLOAD = "api.fitness.logoUpload";                                          // 图标上传
    public static final String SPI_FITNESS_MERCHANT_SYNC_STATUS = "spi.fitness.merchant.syncStatus";                        // 商户状态变化通知
    public static final String SPI_FITNESS_SUBSCRIPTION_SYNC_SUBSCRIPTION = "spi.fitness.subscription.syncSubscription";    // 订购变化通知
    public static final String SPI_FITNESS_ORDER_SYNC_ORDER = "spi.fitness.order.syncOrder";                                // 订单变化通知

}
