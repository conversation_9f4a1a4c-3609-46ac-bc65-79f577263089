package com.wosai.mpay.api.alipay;

public class AlipayV1Methods {

    public static final String ALIPAY_ACQUIRE_CREATEANDPAY = "alipay.acquire.createandpay";
    public static final String ALIPAY_ACQUIRE_REFUND = "alipay.acquire.refund";
    public static final String ALIPAY_ACQUIRE_PRECREATE = "alipay.acquire.precreate";
    public static final String ALIPAY_ACQUIRE_QUERY = "alipay.acquire.query";
    public static final String ALIPAY_ACQUIRE_CANCEL = "alipay.acquire.cancel";

    public static final String ALIPAY_WAP_CREATE_DIRECT_PAY_BY_USER = "alipay.wap.create.direct.pay.by.user";

}
