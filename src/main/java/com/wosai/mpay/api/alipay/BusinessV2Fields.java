package com.wosai.mpay.api.alipay;

public abstract class BusinessV2Fields {
    // 当面付2.0
    public static final String OUT_TRADE_NO = "out_trade_no";
    public static final String IDC_FLAG = "idc_flag";  //网联专用   交易发往网联条码支付IDC标识，具体规则见 4.2 网联 条码支付 IDC 标识。
    public static final String SCENE = "scene";
    public static final String AUTH_CODE = "auth_code";
    public static final String AUTH_NO = "auth_no"; //支付宝资金授权单号
    public static final String PRODUCT_CODE = "product_code";
    public static final String SELLER_ID = "seller_id";
    public static final String TOTAL_AMOUNT = "total_amount";
    public static final String TRANS_CURRENCY = "trans_currency";    //标价币种, total_amount 对应的币种单位。目前仅支持 人民币:CNY
    public static final String SETTLE_CURRENCY = "settle_currency";   //商户指定的结算币种，目前仅 支持人民币:CNY
    public static final String SETTLE_AMOUNT = "settle_amount";   //结算币种订单金额
    public static final String DISCOUTABLE_AMOUNT = "discountable_amount";
    public static final String UNDISCOUNTABLE_AMOUNT = "undiscountable_amount";
    public static final String SUBJECT = "subject";
    public static final String BODY = "body";
    public static final String GOODS_DETAIL = "goods_detail";
    public static final String OPERATOR_ID = "operator_id";
    public static final String STORE_ID = "store_id";
    public static final String ALIPAY_STORE_ID = "alipay_store_id";
    public static final String TERMINAL_ID = "terminal_id";
    public static final String EXTEND_PARAMS = "extend_params";
    public static final String TIME_EXPIRE = "time_expire";
    public static final String TIMEOUT_EXPRESS = "timeout_express"; //该笔订单允许的最晚付款时间，逾期将关闭交易。取值范围：1m～15d。m-分钟，h-小时，d-天，1c-当天（1c-当天的情况下，无论交易何时创建，都在0点关闭）。 该参数数值不接受小数点， 如 1.5h，可转换为 90m。
    public static final String AGREEMENT_PARAMS = "agreement_params";  // 代扣业务需要传入协议相关 信息
    public static final String QR_CODE_TIMEOUT_EXPRESS = "qr_code_timeout_express"; //该笔订单允许的最晚付款时间，逾期将关闭交易。取值范围：1m～15d。m-分钟，h-小时，d-天，1c-当天（1c-当天的情况下，无论交易何时创建，都在0点关闭）。 该参数数值不接受小数点， 如 1.5h，可转换为 90m。
    public static final String DISABLE_PAY_CHANNELS = "disable_pay_channels"; //禁用渠道，用户不可用指定渠道支付当有多个渠道时用“,”分隔注，与enable_pay_channels互斥
    public static final String ENABLE_PAY_CHANNELS = "enable_pay_channels"; //可用渠道，用户只能在指定渠道范围内支付当有多个渠道时用“,”分隔注，与disable_pay_channels互斥
    public static final String MERCHANT_ORDER_NO = "merchant_order_no";   //商户的原始订单号
    public static final String EXT_USER_INFO = "ext_user_info";     //外部指定买家
    public static final String AUTH_CONNFIRM_MODE = "auth_confirm_mode";     //预授权确认模式，授权转交易 请求中传入，适用于预授权转 交易业务使用目前只支持 PRE_AUTH(预授权产品码) COMPLETE:转交易支付完成 结束 预 授 权 ， 解 冻 剩 余 金 额 ;NOT_COMPLETE: 转交易支付完成不结束预授 权，不解冻剩余金额
    public static final String TERMINAL_PARAMS = "terminal_params";    //json 格式;商户传入终端设 备。
    public static final String BUSINESS_PARAMS = "business_params";    //商户传入业务信息，应用于安 全，营销等参数直传场景，格 式为 json 格式。
    public static final String FQ_SIGNATURE = "fqSignature";            // BUSINESS_PARAMS 花呗分期动态口令
    public static final String EDU_SCHOOL_ID = "edu_school_id";         // BUSINESS_PARAMS
    public static final String EDU_SCENE = "edu_scene";                 // BUSINESS_PARAMS
    public static final String ENABLE_THIRDPARTY_SUBSIDY = "enable_thirdparty_subsidy"; // 花呗分期商户贴息标识
    public static final String ROYALTY_INFO = "royalty_info";
    public static final String QR_CODE = "qr_code";
    public static final String TRADE_NO = "trade_no";
    public static final String OPEN_ID = "open_id";
    public static final String BUYER_LOGON_ID = "buyer_logon_id";
    public static final String BUYER_USER_ID = "buyer_user_id";
    public static final String BUYER_ID = "buyer_id";
    public static final String BUYER_OPEN_ID = "buyer_open_id"; // 买家支付宝用户唯一标识
    public static final String OP_BUYER_OPEN_ID = "op_buyer_open_id"; // 买家支付宝用户唯一标识
    public static final String TRADE_STATUS = "trade_status";
    public static final String RECEIPT_AMOUNT = "receipt_amount";
    public static final String INVOICE_AMOUNT = "invoice_amount";
    public static final String BUYER_PAY_AMOUNT = "buyer_pay_amount";
    public static final String POINT_AMOUNT = "point_amount";
    public static final String SEND_PAY_DATE = "send_pay_date";
    public static final String GMT_PAYMENT = "gmt_payment";
    public static final String STORE_NAME = "store_name";
    public static final String FUND_BILL_LIST = "fund_bill_list";
    public static final String FUND_CHANNEL = "fund_channel";
    public static final String FUNDCHANNEL = "fundChannel";
    public static final String FUNDTYPE = "fundType";
    public static final String FUND_TYPE = "fund_type";
    public static final String AMOUNT = "amount";
    public static final String REAL_AMOUNT = "real_amount";
    public static final String RETRY_FLAG = "retry_flag";
    public static final String ACTION = "action";
    public static final String OUT_REQUEST_NO = "out_request_no";
    public static final String REFUND_AMOUNT = "refund_amount";
    public static final String REFUND_REASON = "refund_reason";
    public static final String FUND_CHANGE = "fund_change";
    public static final String REFUND_FEE = "refund_fee";
    public static final String GMT_REFUND_PAY = "gmt_refund_pay";
    public static final String REFUND_DETAIL_ITEM_LIST = "refund_detail_item_list";
    public static final String OP_APP_ID = "op_app_id"; // 支付宝小程序appid

    public static final String IMAGE_ID = "image_id";
    public static final String STATUS_CODE = "status_code";
    public static final String STATUS_DESC = "status_desc";
    public static final String AUDIT_STATUS = "audit_status";
    public static final String AUDIT_DESC = "audit_desc";
    public static final String IS_ONLINE = "is_online";
    
    public static final String SHOP_ID = "shop_id";
    public static final String SHOP_ID_C = "shopId";
    public static final String STORE_ID_C = "storeId";
    public static final String CATEGORY_ID = "categoryId";
    public static final String CATEGORY_ID_C = "categoryId";
    public static final String BRAND_NAME = "brandName";
    public static final String BRAND_NAME_C = "brandName";
    public static final String BRAND_LOGO = "brandLogo";
    public static final String BRAND_LOGO_C = "brandLogo";
    public static final String SLOGAN = "slogan";
    public static final String MAIN_SHOP_NAME = "mainShopName";
    public static final String MAIN_SHOP_NAME_C = "mainShopName";
    public static final String BRANCH_SHOP_NAME = "branchShopName";
    public static final String BRANCH_SHOP_NAME_C = "branchShopName";
    public static final String PROVINCE_CODE = "provinceCode";
    public static final String PROVINCE_CODE_C = "provinceCode";
    public static final String CITY_CODE = "cityCode";
    public static final String CITY_CODE_C = "cityCode";
    public static final String DISTRICT_CODE = "districtCode";
    public static final String DISTRICT_CODE_C = "districtCode";
    public static final String ADDRESS = "address";
    public static final String LONGITUDE = "longitude";
    public static final String LATITUDE = "latitude";
    public static final String PHONE = "phone";
    public static final String MOBILE = "mobile";
    public static final String MAIN_IMAGE = "mainImage";
    public static final String MAIN_IMAGE_C = "mainImage";
    public static final String AUDIT_IMAGES = "auditImages";
    public static final String AUDIT_IMAGES_C = "auditImages";
    public static final String OPENING_HOURS = "openingHours";
    public static final String OPENING_HOURS_C = "openingHours";
    public static final String WIFI = "wifi";
    public static final String PARKING = "parking";
    public static final String VALUE_ADDED = "valueAdded";
    public static final String VALUE_ADDED_C = "valueAdded";
    public static final String AVERAGE_PRICE = "averagePrice";
    public static final String AVERAGE_PRICE_C = "averagePrice";
    public static final String ISV_PID = "isvPid";
    public static final String ISV_PID_C = "isvPid";
    public static final String LICENCE = "licence";
    public static final String LICENCE_CODE = "licenceCode";
    public static final String LICENCE_CODE_C = "licenceCode";
    public static final String LICENCE_NAME = "licenceName";
    public static final String LICENCE_NAME_C = "licenceName";
    public static final String AUTH_LETTER = "authLetter";
    public static final String AUTH_LETTER_C = "authLetter";
    public static final String IS_OPERATING_ONLINE = "isOperatingOnline";
    public static final String IS_OPERATING_ONLINE_C = "isOperatingOnline";
    public static final String ONLINE_IMAGE = "onlineImage";
    public static final String ONLINE_IMAGE_C = "onlineImage";
    public static final String NOTIFY_URL = "notifyUrl";
    public static final String IS_SHOW = "is_show";
    public static final String IS_SHOW_C = "isShow";


    public static final String CODE = "code";
    public static final String MSG = "msg";
    public static final String SUB_CODE = "sub_code";
    public static final String SUB_MSG = "sub_msg";

    public static final String NOTIFY_ACTION_TYPE = "notify_action_type";
    
    public static final String SUB_MERCHANT = "sub_merchant";
    public static final String MERCHANT_ID = "merchant_id";

    public static final String ROYALTY_PARAMETERS = "royalty_parameters"; //分账明细信息

    //goodsDetail
    public static final String GOODS_ID = "goods_id";  //商品的编号
    public static final String ALIPAY_GOODS_ID = "alipay_goods_id";  //支付宝定义的统一商品编号
    public static final String GOODS_NAME = "goods_name";  //商品名称
    public static final String QUANTITY = "quantity";  //商品数量
    public static final String PRICE = "price";  //商品单价，单位为元
    public static final String GOODS_CATEGORY = "goods_category";  //商品类目
    public static final String GOODS_BODY = "body";  //商品 述信息
    public static final String SHOW_URL = "show_url";  //商品的展示地址

    //ExtendParams
    public static final String EXTEND_PARAMS_SYS_SERVICE_PROVIDER_ID = "sys_service_provider_id";   //系统商编号。该参数作为系统 商返佣数据 取的依据，请填 写系统商签约协议的 PID
    public static final String EXTEND_PARAMS_HB_FQ_NUM = "hb_fq_num";   //使用花呗分期要进行的分期 数
    public static final String EXTEND_PARAMS_HB_FQ_SELLER_PERCENT = "hb_fq_seller_percent";   //使用花呗分期需要卖家承担 的手续费比例的百分值，传入 100 代表 100%
    public static final String EXTEND_PARAMS_INDUSTRY_REFLUX_INFO = "industry_reflux_info";   //行业数据回流信息
    public static final String EXTEND_PARAMS_CARD_TYPE = "card_type";   //卡类型
    public static final String EXTEND_PARAMS_CREDIT_TRADE_SCENE =  "creditTradeScene"; //信用交易场景

    //AgreementParams
    public static final String AGREEMENT_NO = "agreement_no";   //支付宝系统中用以唯一标识 用户签约记录的编号(用户签 约成功后的协议号)
    public static final String AUTH_CONFIRM_NO = "auth_confirm_no";   //鉴权确认码，在需要做支付鉴 权校验时，该参数不能为空
    public static final String APPLY_TOKEN = "apply_token";   //鉴权申请 token，其格式和内 容由支付宝定义。在需要做支 付鉴权校验时，该参数不能为 空。


    //RoyaltyInfo
    public static final String ROYALTY_TYPE = "royalty_type";   //分账类型卖家的分账类型，目前只支持传入 ROYALTY(普 通分账类型)。
    public static final String ROYALTY_DETAIL_INFOS = "royalty_detail_infos";   //分账明细的信息，可以 述多 条分账指令，json 数组，详 见下文说明。


    //RoyaltyDetailInfos royalty_parameters
    public static final String SERIAL_NO = "serial_no";   //分账序列号，表示分账执行的 顺序，必须为正整数
    public static final String TRANS_IN_TYPE = "trans_in_type";   //接受分账金额的账户类型: userId:支付宝账号，对应 的支付宝唯一用户号。 bankIndex:分账到银行账户 的银行编号。目前暂时只支持 分账到一个银行编号。 storeId :分账到门店对应 的银行卡编号。默认值为 userId。
    public static final String BATCH_NO = "batch_no";   //分账批次号，目前需要和转入 账号类型为 bankIndex 配合 使用。
    public static final String OUT_RELATION_ID = "out_relation_id";   //商户分账的外部关联号，用于 关联到每一笔分账信息，商户 需保证其唯一性。如果为空， 该值则默认为“商户网站唯一 订单号+分账序列号
    public static final String TRANS_OUT_TYPE = "trans_out_type";   //要分账的账户类型。目前只支 持 userId:支付宝账号对应 的支付宝唯一用户号。
    public static final String TRANS_OUT = "trans_out";   //如果转出账号类型为 userId，本参数为一用户号。 以 2088 开头的纯 16 位数字。 要分账的支付宝账号对应的 支付宝唯
    public static final String TRANS_IN = "trans_in";   //如果转入账号类型为 userId，本参数为接受分账 金额的支付宝账号对应的支 付宝唯一用户号，以 2088 开 头的纯 16 位 数 字 。 如 果 转入账号类型为 bankIndex，本参数为 28 位 的银行编号(商户和支付宝签 约时确定)。如果转入账号类 型为 storeId，本参数为商户 的门店 ID。
    public static final String ROYALTY_AMOUNT = "amount";   //分账的金额，单位为元
    public static final String DESC = "desc";   //分账 述信息
    public static final String AMOUNT_PERCENTAGE = "amount_percentage";   //分账的比例，值为 20 代表按 20%的比例分账

    public static final String ROYALTY_FINISH = "royalty_finish";       // 交易分账是否完结
    public static final String ROYALTY_FREEZE = "royalty_freeze";       // 冻结标识

    //SubMerchant
    public static final String SUB_MERCHANT_ID = "merchant_id";   //间连受理商户的支付宝商户 编号，通过间连商户入驻后得 到。间连业务下必传，并且需 要按规范传递受理商户编号。
    public static final String SUB_MERCHANT_TYPE = "merchant_type";   //商户 id 类型

    //ExtUserInfo
    public static final String NAME = "name";   //姓名 注 : need_check_info=T 时，该参数才有效
    public static final String EXT_MOBILE = "moblie";   //手机号 注:该参数暂不校验
    public static final String CERT_TYPE = "cert_type";   //身 份 证 : IDENTITY_CARD 、 护照:PASSPORT 、 军 官证: OFFICER_CARD 、 士 兵 证 : SOLDIER_CARD 、 户 口 本:HOKOU 等。注 : need_check_info=T 时 该参数才有效
    public static final String CERT_NO = "cert_no";   //证件号 注 : need_check_info=T 时该参数才有效
    public static final String MIN_AGE = "min_age";   //允许的最小买家年龄，买家年 龄必须大于等于所传数值 注: 1.need_check_info=T 时 该参数才有效 2. min_age 为整数，必须大 于等于 0
    public static final String FIX_BUYER = "fix_buyer";   //是否强制校验付款人身份信 息 T:强制校验，F:不强制
    public static final String NEED_CHECK_INFO = "need_check_info";   //是否强制校验身份信息 T:强制校验，F:不强制




    public static final String PAY_CURRENCY = "pay_currency";   //支付币种
    public static final String PAY_AMOUNT = "pay_amount";   //支付币种订单金额
    public static final String SETTLE_TRANS_RATE = "settle_trans_rate";   //结算币种兑换标价币种汇率
    public static final String TRANS_PAY_RATE = "trans_pay_rate";   //标价币种兑换支付币种汇率
    public static final String CARD_BALANCE = "card_balance";   //支付宝卡余额
    public static final String DISCOUNT_GOODS_DETAIL = "discount_goods_detail";   //本次交易支付所使用的单品 券优惠的商品优惠信息
    public static final String ASYNC_PAYMENT_MODE = "async_payment_mode";   //异步支付模式，先享后付业务 会返回该参 数 ， 目 前 有 三种值: ASYNC_DELAY_PAY( 异 步 延时付 ASYNC_DELAY_PAY( 异 步 延 时 付款 ); ASYNC_REALTIME_PAY( 异 步 准 实 时 付 款 ); SYNC_DIRECT_PAY( 同 步 直 接扣款);
    public static final String VOUCHER_DETAIL_LIST = "voucher_detail_list";   //本交易支付时使用的所有优 惠券信息，详见下文说明。
    public static final String AUTH_TRADE_PAY_MODE = "auth_trade_pay_mode";   //预授权支付模式，该参数仅在 信用预授权支付场景下返回。 信用预授权支付: CREDIT_PREAUTH_PAY
    public static final String BUYER_USER_TYPE = "buyer_user_type";   //买家用户类型。CORPORATE: 企业用户;PRIVATE:个人用 户。
    public static final String MDISCOUNT_AMOUNT = "mdiscount_amount";   //商家优惠金额
    public static final String DISCOUNT_AMOUNT = "discount_amount";   //商家优惠金额




    public static final String BANK_CODE = "bank_code";   //银行卡支付时的银行代码


    public static final String OUT_BIZ_NO = "out_biz_no";   //商户业务号
    public static final String SELLER_EMAIL = "seller_email";   //卖家支付宝账号
    public static final String PAY_TIMEOUT = "pay_timeout";     // 该笔订单允许的最晚付款时间，逾期将关闭该笔订单 
    public static final String PAYER_LOGON_ID = "payer_logon_id"; // 收款方支付宝账号（Email或手机号）    
    public static final String PAYER_USER_ID = "payer_user_id"; // 付款方支付宝用户号   
    public static final String PAYEE_LOGON_ID = "payee_logon_id"; // 收款方支付宝账号（Email或手机号）
    public static final String PAYEE_USER_ID = "payee_user_id"; // 收款方的支付宝唯一用户号,以2088开头的16位纯数字组成
    public static final String GMT_TRANS = "gmt_trans"; //资金授权成功时间 格式：YYYY-MM-DD HH:MM:SS  
    public static final String STATUS = "status";
    public static final String REMARK = "remark";
    public static final String AUTH_CODE_TYPE = "auth_code_type";   // 授权码类型 
    public static final String ORDER_TITLE = "order_title"; // 业务订单的简单描述，如商品名称等 
    public static final String OUT_ORDER_NO = "out_order_no";   // 商户授权资金订单号
    public static final String EXTRA_PARAM = "extra_param"; // 业务扩展参数，用于商户的特定业务信息的传递
    public static final String CATEGORY = "category";  // 当面资金授权业务对应的类目
    public static final String SERVICE_ID = "serviceId";  // 资金授权信用服务ID
    public static final String SECONDARY_MERCHANT_ID = "secondaryMerchantId"; //二级商户id
    public static final String REQUEST_ORG_ID = "requestOrgId"; //二级商户所属机构id
    public static final String ORDER_STR = "order_str"; //小程序资金预授权冻结请求参数
    public static final String REQUEST_ORG_PID = "request_org_pid"; //二级商户所属机构pid

    public static final String ORG_PID = "org_pid"; //收单机构的标识，填写该机构在支付宝的pid
    
    public static String ORGID = "orgid";//四方机构号	通联分配的四方机构号

    public static final String RECEIVER_LIST = "receiver_list"; //分账接收方列表
    public static final String TYPE = "type";//分账方类型
    public static final String ACCOUNT = "account";//分账方帐号
    public static final String RECEIVER_NAME = "name";//分账方全称
    public static final String MEMO = "memo";//分账关系描述
    public static final String PAGE_NUM = "page_num";
    public static final String PAGE_SIZE = "page_size";
    public static final String QUERY_OPTIONS = "query_options";

    public static final String TRADE_SETTLE_INFO = "trade_settle_info";//返回的交易结算信息，包含分账、补差等信息
    public static final String TRADE_SETTLE_DETAIL_LIST = "trade_settle_detail_list";//交易结算明细信息
    public static final String OPERATION_TYPE = "operation_type";//结算操作类型。包含replenish、replenish_refund、transfer、transfer_refund等类型
    public static final String OPERATION_SERIAL_NO = "operation_serial_no";//商户操作序列号。商户发起请求的外部请求号。
    public static final String OPERATION_DT = "operation_dt";//操作日期
    public static final String TRADE_SETTLE_DETAIL_TRANS_OUT = "trans_out";//转出账号
    public static final String TRADE_SETTLE_DETAIL_TRANS_IN = "trans_in";//转入账号
    public static final String TRADE_SETTLE_DETAIL_AMOUNT = "amount";//实际操作金额，单位为元，两位小数。该参数的值为分账或补差或结算时传入
    public static final String SEND_BACK_FEE = "send_back_fee";// 商户实际退款给用户的金额，单位为元，支持两位小数	
    public static final String AUTH_CONFIRM_MODE = "auth_confirm_mode"; // 传入COMPLETE，无需调用解冻接口，支付宝端在扣款成功后会自动解冻剩余金额，同时该笔授权订单完成；若auth_confirm_mode=NOT_COMPLETE，在收到支付成功通知后，商户自行调用解冻接口将余额进行解冻

  //垫资保付相关字段
    public static final String ADVANCE_PAYMENT_TYPE = "advance_payment_type"; //支付模式类型,若值为ENJOY_PAY_V2表示当前交易允许走先享后付2.0垫资
    public static final String ADVANCE_AMOUNT = "advance_amount"; //先享后付2.0垫资金额,不返回表示没有走垫资，非空表示垫资支付的金额

    // 花呗分期商家贴息相关参数
    public static final String ACTIVITY_NAME = "activity_name"; //花呗分期商家贴息活动名称，长度不能超过30个汉字或字符
    public static final String START_TIME = "start_time";   // 活动开始时间,精确到秒
    public static final String END_TIME = "end_time";   // 活动结束时间,精确到秒
    public static final String MIN_MONEY_LIMIT = "min_money_limit";   // 免息金额下限不能少于100，交易额度若小于该值，则不做花呗分期免息补贴，单位是元。
    public static final String MAX_MONEY_LIMIT = "max_money_limit";   // 免息金额上限不能大于300000, 不能低于免息金额下限，交易额度若高于该上限值则不做花呗分期免息补贴，单位是元
    public static final String AMOUNT_BUDGET = "amount_budget";   // 花呗分期贴息预算金额，单位是元
    public static final String INSTALL_NUM_LIST = "install_num_list";   // 花呗分期数集合
    public static final String BUDGET_WARNING_MONEY = "budget_warning_money";   // 预算提醒金额，不能高于预算金额，预算达到该金额时会触发告警通知，单位是元
    public static final String BUDGET_WARNING_MAIL_LIST = "budget_warning_mail_list";   // 预算提醒邮件列表，邮箱必须符合邮箱地址规则，提醒邮箱个数不能超过5个,多个邮箱用逗号隔开
    public static final String BUDGET_WARNING_MOBILE_LIST = "budget_warning_mobile_list";   // 预算提醒手机号列表，符合手机号码规则，提醒手机号个数不能超过5个，多个手机号用逗号隔开
    public static final String MERCHANT_INFO = "merchant_info";   // 贴息对象门店信息
    public static final String ACQUIRE_MODE = "acquire_mode";   // 描述说当模式，目前取值有直连/间连/境外
    public static final String MERCHANT_TYPE = "merchant_type";   // 用于区分店铺的粒度，取值 有PID/SMID/PID_MID/PID_MID_SID/PID_ORGID 等
    public static final String ORIGIN_CONFIG_INFO = "origin_config_info";   // 原始ID拼接串，根据粒度的不同，最多4个ID的拼接
    public static final String AGGR_ID = "aggr_id";   // 商家活动ID
    public static final String SUBSIDY_SCOPE = "subsidy_scope"; // 贴息模式， ALL：全场贴息 ORDER：订单贴息

    public static final String ORIG_REQUEST_NO = "orig_request_no"; // 原始业务请求单号。如对某一次退款进行履约时，该字段传退款时的退款请求号  
    public static final String BIZ_TYPE = "biz_type"; // 交易信息同步对应的业务类型，具体值与支付宝约定；
    public static final String ORDER_BIZ_INFO = "order_biz_info"; // 商户传入同步信息，具体值要和支付宝约定；用于芝麻信用租车、单次授权等信息同步场景，格式为json格式

    public static final String MERCHANT_NAME = "merchant_name";
    public static final String AREA_INFO = "area_info";
    public static final String TERMINAL_INFO = "terminal_info";
    public static final String TERM_INFO_LOCATION = "location";
    public static final String TERM_INFO_TERMINAL_TYPE = "terminal_type";
    public static final String TERM_INFO_TERMINAL_ID = "terminal_id";
    public static final String TERM_INFO_SERIAL_NUM = "serial_num"; // 终端设备的硬件序列号
    public static final String TERM_INFO_TERMINAL_IP = "terminal_ip";

    public static final String RISK_INFO = "risk_info";

    public static final String BIZ_SCENE = "biz_scene";     // 业务场景
    public static final String SOURCE = "source";   // 业务来源，业务接入的约定标识，代表业务的调用方。例如 :ISV公司名称缩写
    public static final String HBFQ_SUB_MERCHANT_ID = "sub_merchant_id"; // 花呗分期吱口令子商户号
    public static final String SMID = "smid";   // 二级商户唯一标识
    public static final String TEL = "tel";   // 收件人联系方式,手机号.用于物流派送时联系收件人
    public static final String QRCODE_PROVINCE_CODE = "province_code";   // 省份编码,用于物流派送的收件地址.地址库版本:LBS:2020Q1
    public static final String QRCODE_CITY_CODE = "city_code";   // 市编码,用于物流派送的收件地址.地址库版本LBS:2020Q1
    public static final String AREA_CODE = "area_code";   // 区编码,用于物流派送的收件地址.地址库版本LBS:2020Q1
    public static final String STREET_CODE = "street_code";   // 街道编码,用于物流派送的收件地址.地址库版本LBS:2020Q1
    public static final String MATERIALS = "materials";   // 街道编码,用于物流派送的收件地址.地址库版本LBS:2020Q1
    public static final String CODE_TYPE = "code_type";   // 街道编码,用于物流派送的收件地址.地址库版本LBS:2020Q1
    public static final String APPLY = "apply";          // 本次物料申领是否成功,成功为true,失败为false
    public static final String PROCESS_ID = "process_id";          // 码申请流程id
    public static final String FBQR_CODE_EXT_INFO = "fqqr_code_ext_info";          // 扩展字段
    public static final String IS_SKIP_MCC_CHECK = "IS_SKIP_MCC_CHECK";          // 扩展字段

    /* 支付宝芝麻先享 */
    public static final String OUT_AGREEMENT_NO =  "out_agreement_no"; //商户外部协议号
    public static final String CREDIT_AGREEMENT_ID =  "credit_agreement_id"; //芝麻开通协议号
    public static final String ORDER_AMOUNT =  "order_amount"; //订单金额
    //只有当传递了order_amount时，该参数才有意义;
    // 1）该参数不传时，默认为ORDER_AMOUNT。
    // 2）传ORDER_AMOUNT时，表示order_amount传入的金额为后付金额，在发起扣款时，最大扣款支付金额为order_amount传入的值；
    // 3）传RISK_AMOUNT时，表示ORDER_AMOUNT传入的金额为风险预估金额，在发起扣款时，最大扣款支付金额为商户签约时约定的上限额度。
    public static final String AMOUNT_TYPE =  "amount_type";
    public static final String ZM_SERVICE_ID =  "zm_service_id"; //芝麻信用服务ID
    public static final String ZM_CATEGORY_ID =  "category_id"; //芝麻外部类目
    //产品码，不填默认为 CREDIT_PAY_AFTER_USE。芝麻先享产品为CREDIT_PAY_AFTER_USE，其他产品请根据对应的技术支持文档传入
    public static final String ZM_PRODUCT_CODE =  PRODUCT_CODE;
    //当用户进入芝麻先享下单页面后，点击左上角的回退按钮，中断开通流程，跳转回商户的页面地址。不传该链接时，默认返回上一级页面，由外部app唤起支付宝的情况，会返回支付宝首页
    public static final String CANCEL_BACK_LINK =  "cancel_back_link";
    //用户芝麻先享开通并下单流程结束后，不区分用户开通并下单成功/失败，跳转回商家页面，该字段代表跳转回商家的页面地址。不传该链接时，默认返回上一级页面，由外部app唤起支付宝的情况，会返回支付宝首页
    public static final String RETURN_BACK_LINK =  "return_back_link";
    public static final String CREDIT_BIZ_ORDER_ID =  "credit_biz_order_id"; //信用服务订单号
    //用户此订单是否守约。
    //传true时，用户在芝麻信用-守约记录中，该笔订单是已守约状态；
    //传false时，用户在芝麻信用-守约记录中，该笔订单是已取消状态
    public static final String IS_FULFILLED =  "is_fulfilled";
    // 传 true 时，支付宝会在后台定时轮询扣款，无需商家重复发起，且芝麻先享产品支持智能扣款能力，显著提高商家回款效率。
    // 传入 true 时，不会同步返回支付结果，商家需监听 扣款异步通知。
    public static final String IS_ASYNC_PAY =  "is_async_pay";


    //信用服务订单状态
    //INIT: 下单状态；TRADE_CLOSED: 订单取消或者交易全额退款； TRADE_FINISHED：扣款成功状态
    public static final String ORDER_STATUS =  "order_status";
    //订单创建时间
    public static final String CREATE_TIME =  "create_time";

    //信用服务开通/授权状态
    //VALID: 有效，INVALID: 无效
    public static final String AGREEMENT_STATUS =  "agreement_status";
    /**  支付宝先享后付 - 周期付           **/
    public static final String DATA = "data";      // 订购信息
    public static final String SUBSCRIPTION_NO = "subscriptionNo";      // 订购编号
    public static final String SUBSCRIPTION_CANCEL_TYPE = "subscriptionCancelType"; // 解约类型
    public static final String MERCHANT_PID = "merchantPid";                    // 商户id
    public static final String ORDER_NO = "orderNo";                            // 订单号
    public static final String ACTUAL_DEDUCTION_TIME = "actualDeductionTime";   // 实际扣款时间
    public static final String FITNESS_ORDER_STATUS = "orderStatus";                // 订单状态
    public static final String SUBSCRIPTION_STATUS = "subscriptionStatus";  // 订购状态
    public static final String OUT_SUBSCRIPTION_NO = "outSubscriptionNo";      // 外部订购编号
    public static final String PRODUCT_NO = "productNo";      // 产品编号
    public static final String PERIOD = "period";      // 产品扣款期数
    public static final String DEDUCTION_AMOUNT = "deductionAmount";      // 扣减金额
    public static final String USER_ID = "userId";      // 用户id

    public static final String FITNESS_SETTLE_DETAILS = "settleDetails";
    public static final String FITNESS_SETTLE_RATE = "rate";
    public static final String FITNESS_SETTLE_TRANS_IN = "transIn";
    public static final String FITNESS_SETTLE_STATUS = "status";
    public static final String FITNESS_SETTLE_AMOUNT = "amount";

    public static final String FITNESS_FAIL_REASON = "failReason";
    public static final String FITNESS_DEDUCTION_FAIL_REASON = "deductionFailReason";

    public static final String FITNESS_DEDUCTION_PLAN_LIST = "deductionPlanList"; //扣款计划
    public static final String FITNESS_DEDUCTION_PLAN_LIST_DEDUCTION_DATE = "deductionDate"; //扣款日期 例如2022-01-01 15:00:00

    public static final String SCENE_TYPE = "scene_type";
    public static final String TRADING = "trading";
}
