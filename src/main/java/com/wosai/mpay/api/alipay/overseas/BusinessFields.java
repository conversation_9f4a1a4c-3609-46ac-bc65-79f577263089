package com.wosai.mpay.api.alipay.overseas;

public class BusinessFields {
    //barcode pay request field
    public static final String ALIPAY_SELLER_ID = "alipay_seller_id";           //Same value with partner ID
    public static final String QUANTITY = "quantity";                           //Quantity of commodity
    public static final String TRANS_NAME = "trans_name";                       //The name of the transaction which will be shown in the transaction record’s list.
    public static final String PARTNER_TRANS_ID = "partner_trans_id";           //The transaction Id on the partner system which could be a sale order id and payment order id. Partner is required to ensure its uniqueness in each call, this is also the key parameter for issue investigation
    public static final String CURRENCY = "currency";                           //the currency used for labelling the price of the transaction, this is also the settlement currency Alipay settled to the partner
    public static final String TRANS_CURRENCY = "trans_currency";               //Pricing currency for the transaction, if it is not CNY, the CNY amount user will be charged will be calculated based on trans_currency and exchange rate.	
    public static final String TRANS_AMOUNT = "trans_amount";                   //the transaction amount in the currency given above; Range: 0.01-100000000.00. Two digits after decimal point.
    public static final String BUYER_IDENTITY_CODE = "buyer_identity_code";     //Used as identification of an <PERSON><PERSON>y user. This dynamic value, starts with 25～30 and has 16~24 digits, must be read from the user’s Alipay wallet in real time
    public static final String IDENTITY_CODE_TYPE = "identity_code_type";       //The identity code type could be QRcode or barcode
    public static final String TRANS_CREATE_TIME = "trans_create_time";         //The time that the partner system creates the transaction. Format：YYYYMMDDHHMMSS
    public static final String MEMO = "memo";                                   //Transaction notes
    public static final String BIZ_PRODUCT = "biz_product";                     //Product name ,for now it’s an static value which is mandatory Value: OVERSEAS_MBARCODE_PAY
    public static final String EXTEND_INFO = "extend_info";                     //Containing the extended parameters of the request, it’s in JSON format.
    public static final String SECONDARY_MCH_NAME = "secondary_merchant_name";          //sub-merchant name which will be recorded in user’s statement. The max length is 64.
    public static final String SECONDARY_MCH_ID = "secondary_merchant_id";              //sub-merchant ID which is used for distinguish each specific sub merchants. The max length is 32.
    public static final String SECONDARY_MCH_INDUSTRY = "secondary_merchant_industry"; //Industry classification identifier of sub-merchant which allocated by Alipay. Such like: catering industry: 5812 department stores: 5311 lodging industry: 7011 taxi industry: 4121
    public static final String TIMESTAMP = "timestamp";                                 //Time stamp of the merchant server sending request, accurate to the millisecond.
    public static final String OUT_TRADE_NO = "out_trade_no";                           //Unique order No. in Alipay’s merchant’s website
    public static final String TRADE_NO = "trade_no";                                   //The trade serial number of the trade in Alipay system. 16 bits at least and 64 bits at most.If out_trade_no and trade_no are transmitted at the same time, trade_no shall govern.
    public static final String ALIPAY_TRANS_ID = "alipay_trans_id";                     //On the partner’s payment request, the alipay system creates a transaction id to handle it. The alipay_trans_id has one-one association with partner + partner_trans_id.
    public static final String PARTNER_REFUND_TRANS_ID = "partner_refund_id";           //The refund order id on partner system. partner_refund_id cannot be same as partner_transaction_id partner_refund_id together with partner identify a refund transaction
    public static final String REFUND_AMOUNT = "refund_amount";                         //Less than or equal to the original transaction amount and the left transaction amount if ever refunded.
    public static final String REFUND_IS_SYNC = "is_sync";                              //The refund request is processed synchronously or asynchronously. Value: Y or N. Default value is N, which is processed asynchronously. If the value is set as Y, notify_url will become meaningless
    public static final String NOTIFY_URL = "notify_url";   //The url to which Alipay sends payment result in async mode. It is in POST method. Note: For information security, the url must be on https protocol. This URL cannot have extra parameters. For example, “https://xxx.com/foo.php?star=obama” is not allowed.
    public static final String BIZ_DATA = "biz_data";   	//Business data。Format：JSON
    public static final String BIZ_TYPE = "biz_type";   	//Business type that is defined by Alipay, this case is “OVERSEASHOPQRCODE”
    public static final String TERMINAL_TIMESTAMP = "terminal_timestamp";          //Time stamp of the terminal sending request, accurate to the millisecond.	
    public static final String SELLER_ID = "seller_id";          		//Unique Alipay user ID corresponding to Seller’s Alipay account16 numbers beginning with 2088.If both sell_id and seller-email are null, the default value of this parameter to be filled out shall be the value of merchant.
    public static final String SELLER_EMAIL = "seller_email";           //Seller’s Alipay account, may be email or phone number If seller_id is not null, the value of seller_id shall be the seller’s ID and this parameter can be neglected.
    public static final String BODY = "body";          					//Specific description of the trade. In case of a variety of goods, please accumulate the character strings descrbing the goods, and transmit the same to body.	
    public static final String SHOW_URL = "show_url";          			//Hyperlink for the show of goods on the webpage of checkout counter.	
    public static final String SUBJECT = "subject";          			//Goods title/trade tile/order subject/order key word etc.The length of this parameter is up to 128 Chinese characters.
    public static final String PRODUCT_CODE = "product_code";           //Order placement used to distinguish the business type:   OVERSEAS_MBARCODE_PAY: face to face payment overseas
    public static final String TOTAL_FEE = "total_fee";          		//Total fee of this order. The range of values is [0.01, 100000000], such value can have up to two digits after the decimal point.	
	public static final String PRICE = "price";							//Unit price of the goods in the order. If this parameter is transmitted at request, the condition of total_fee=price×quantity must be met.	
	public static final String GOODS_DETAIL = "goods_detail";			//Description of the details of goods in the format of json The maximum allowable goods number is 50.
	public static final String IT_B_PAY = "it_b_pay";					//Set the overtime of non-payment trade, the trade will be closed automatically once the time is up.Range of values: 1m～15d.m-minute, h-hour, d-day, 1c-current day (Whenever the trade is created, it will be closed at 0:00).Demical point of the numerical value of this parameter is rejected, for example, 1.5h can be tansformed to 90m.To realize this function, Alipay is needed to be advised to set close time.
	public static final String PASSBACK_PARAMETERS = "passback_parameters";					//If merchant transfer this parameter by the request string, Alipay will feedback this parameter by the asynchronous notify (parameter name: extra_common_param).	
	public static final String PARTNER = "partner";						//Unique Alipay user number corresponding to authorized Alipay account number.Composed of 16 numbers beginning with 2088.
	public static final String PAYMENT_INST = "payment_inst";			// To inidcate the type of wallet this QRCode can be scaned, if this field is set to ALIPAYCN, then the qrcode can only be scaned by Alipay Mainland Wallet. Vice-versa. This field is mandatory.
	public static final String PAYMENT_INST_ALIPAYCN = "ALIPAYCN";
	public static final String PAYMENT_INST_ALIPAYHK = "ALIPAYHK";
	
	public static final String EXTEND_PARAMS = "extend_params";			//Be used for transmitting specific business information of the merchant; this parameter will only be valid if merchant and Alipay agree to transmit this parameter and reach an agreement on the implication of this parameter.
	public static final String SEND_FORMAT = "sendFormat";
	public static final String STORE_ID = "store_id";					//The unique id of the merchant’s shop which assigned by the merchant.				
	public static final String STORE_NAME = "store_name";				//The name of the merchant’s shop which used to show in the customer’s Alipay wallet and the reconciliation file	
	public static final String TERMINAL_ID = "terminal_id";				//Terminal ID for submitting request.
	public static final String SYS_SERVICE_PROVIDER_ID = "sys_service_provider_id"; //The technical provider id. This parameter is used to identify the payment system provider.
	public static final Object QR_CODE = "qr_code";						// Value of the QR code	
    public static final String BUYER_ID = "buyer_id";
    public static final String OUT_RETURN_NO = "out_return_no";


}
