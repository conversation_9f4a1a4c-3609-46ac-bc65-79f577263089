package com.wosai.mpay.api.alipay;


import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TimeZone;
import java.util.TreeMap;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.BuilderException;


public class RequestV2Builder {
    private static ObjectMapper om = new ObjectMapper();
    Map<String, String> request;
    Map<String, Object> bizContent;
    
    public RequestV2Builder () {
        request = new TreeMap<String, String>();
        bizContent = new LinkedHashMap<String, Object> ();

        DateFormat df = new SimpleDateFormat(AlipayConstants.DATE_TIME_FORMAT);
        df.setTimeZone(TimeZone.getTimeZone(AlipayConstants.DATE_TIMEZONE));

        request.put(ProtocolV2Fields.TIMESTAMP, df.format(new Date()));
        request.put(ProtocolV2Fields.VERSION, "1.0");

    }

    public Map<String, String> build() throws BuilderException {
        if (!request.containsKey(ProtocolV2Fields.METHOD)) {
            throw new BuilderException();
        }

        if (!bizContent.isEmpty()) {
            try {
                request.put(ProtocolV2Fields.BIZ_CONTENT, om.writeValueAsString(bizContent));
            }
            catch (JsonProcessingException e) {
                throw new BuilderException("unable to serialize bizContent in JSON format.", e);
            }
        }
        return request;
    }
    
    public void set(String field, String value) {
        request.put(field,  value);
    }
    
    public void bizSet(String field, Object value) {
        bizContent.put(field, value);
    }

    public Map<String,String> getRequest(){
        return request;
    }

    public Map<String,Object> getBizContent(){
        return bizContent;
    }
}
