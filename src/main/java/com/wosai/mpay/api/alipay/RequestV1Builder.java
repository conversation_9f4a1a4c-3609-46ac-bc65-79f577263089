package com.wosai.mpay.api.alipay;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeMap;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.BuilderException;


public class RequestV1Builder {
    private static ObjectMapper om = new ObjectMapper();
    private Map<String, String> request;
    private Map<String, Object> extendParams;

    public RequestV1Builder() {
        request = new TreeMap<String, String>();
        extendParams = new LinkedHashMap<String, Object> ();

    }

    public Map<String, String> build() throws BuilderException {
        if (!request.containsKey(ProtocolV1Fields.SERVICE)) {
            throw new BuilderException("alipay v1 request missing service param");
        }

        if (!extendParams.isEmpty()) {
            try {
                request.put(BusinessV1Fields.EXTEND_PARAMS, om.writeValueAsString(extendParams));
            }
            catch (JsonProcessingException e) {
                throw new BuilderException("unable to serialize bizContent in JSON format.", e);
            }
        }
        return request;
    }
    
    public void set(String field, String value) {
        request.put(field,  value);
    }
    
    public void setExtend(String field, String value) {
        extendParams.put(field, value);
    }
}
