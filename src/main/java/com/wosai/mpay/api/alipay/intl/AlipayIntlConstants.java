package com.wosai.mpay.api.alipay.intl;

import java.util.Arrays;
import java.util.List;

public class AlipayIntlConstants {
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ssZZ"; //默认时间格式
    public static final String DATE_TIMEZONE = "GMT+8";             //Date默认时区
    public static final String SIGN_TYPE_MD5 = "MD5";               //签名方式 MD5
    public static final String DEFAULT_INPUT_CHARSET = "UTF-8";       //默认字符集utf-8
    
    public static final String SERVICE_NAME_PAY_BARCODE = "alipay.intl.acquiring.offline.pay";      // b2c支付
    public static final String SERVICE_NAME_CANCEL = "alipay.intl.acquiring.common.payCancel";      // 撤单接口名称
    public static final String SERVICE_NAME_REFUND = "alipay.intl.acquiring.common.refund"; 		// 退款接口名称
    public static final String SERVICE_NAME_QUERY = "alipay.intl.acquiring.offline.payQuery";       // 查单接口名称*
    public static final Boolean REFUND_IS_SYNC_YES = true;
    
    public static final String IDENTITY_CODE_TYPE_BARCODE = "barcode";
    
    public static final String GATEWAY_URL = "https://isupergw.alipaydev.com";
    
    public static final String RESP_RESULT_STSTUS_SUCCESS = "S";                                //success
    public static final String RESP_RESULT_STSTUS_FAIL = "F";                                   //fail
    public static final String RESP_RESULT_STSTUS_UNKNOWN = "U";                                //unknown

    
    public static final String RESP_BIZ_CODE_ID_SUCCESS = "00000000";                           //Success
    public static final String RESP_BIZ_CODE_ID_PROCESS_FAIL = "00000019";                      //General business failure. No retry.
    public static final String RESP_BIZ_CODE_ID_UNKNOWN_EXCEPTION = "00000901";                 //API failed due to unknown reason
    public static final String RESP_BIZ_CODE_ID_REQUEST_TRAFFIC_EXCEED_LIMIT = "00000024";      //Request traffic exceed limit.
    
    /**接口返回通用错误码*/
    public static final String RESP_RESULT_CODE_REPEAT_REQ_INCONSISTENT = "REPEAT_REQ_INCONSISTENT";       //Repeated submit, and requests are inconsistent.
    public static final String RESP_RESULT_CODE_PAYMENT_IN_PROCESS = "PAYMENT_IN_PROCESS";                 //The payment is still under process.
    public static final String RESP_RESULT_CODE_ORDER_IS_CLOSED = "ORDER_IS_CLOSED";                       //Order status is closed.
    public static final String RESP_RESULT_CODE_ORDER_STATUS_INVALID = "ORDER_STATUS_INVALID";             //Order status is invalid.
    public static final String RESP_RESULT_CODE_ORDER_NOT_EXIST = "ORDER_NOT_EXIST";                       //Order does not exist
    public static final String RESP_RESULT_CODE_USER_NOT_EXIST = "USER_NOT_EXIST";                         //User does not exist
    public static final String RESP_RESULT_CODE_USER_STATUS_ABNORMAL = "USER_STATUS_ABNORMAL";             //User status is not normal.
    public static final String RESP_RESULT_CODE_CURRENCY_NOT_SUPPORT = "CURRENCY_NOT_SUPPORT";             //Transaction currency is invalid
    public static final String RESP_RESULT_CODE_AMOUNT_EXCEED_LIMIT = "AMOUNT_EXCEED_LIMIT";               //Amount exceeds limits.
    public static final String RESP_RESULT_CODE_COUNT_EXCEED_LIMIT = "COUNT_EXCEED_LIMIT";                 //Count exceeds limit.
    public static final String RESP_RESULT_CODE_USER_BALANCE_NOT_ENOUGH = "USER_BALANCE_NOT_ENOUGH";       //User balance is not enough
    public static final String RESP_RESULT_CODE_IDENTITY_CODE_INVALID = "IDENTITY_CODE_INVALID";           //The identity code is invalid.
    public static final String RESP_RESULT_CODE_IDENTITY_CODE_NOT_SUPPORT = "IDENTITY_CODE_NOT_SUPPORT";   //The format of identity code is NOT supported, e.g. the identity code is from an unsupported wallet.
    public static final String RESP_RESULT_CODE_MERCHANT_BALANCE_NOT_ENOUGH = "MERCHANT_BALANCE_NOT_ENOUGH";  //The merchant balance is not enough.
    public static final String RESP_RESULT_CODE_REFUND_AMOUNT_EXCEED = "REFUND_AMOUNT_EXCEED";             //refund amount exceeds
    public static final String RESP_RESULT_CODE_PROCESS_FAIL = "PROCESS_FAIL";                             //General business failure. No retry.
    public static final String RESP_RESULT_CODE_REQUEST_TRAFFIC_EXCEED_LIMIT = "REQUEST_TRAFFIC_EXCEED_LIMIT";         //Request traffic exceed limit.
    public static final String RESP_RESULT_CODE_UNKNOWN_EXCEPTION = "UNKNOWN_EXCEPTION";                   //API failed due to unknown reason.
    public static final String RESP_RESULT_CODE_USER_AMOUNT_EXCEED_LIMIT = "USER_AMOUNT_EXCEED_LIMIT";     //User amount exceeds limits
    public static final String RESP_RESULT_CODE_USER_REALNAME_STATUS_INVALID = "USER_REALNAME_STATUS_INVALID";  //This user needs to complete realname validation.
    public static final String RESP_RESULT_CODE_USER_NO_PAYMENT_INSTRUMENT = "USER_NO_PAYMENT_INSTRUMENT";      //This user has no payment instrument.
    public static final String RESP_RESULT_CODE_USER_BALANCE_PAY_OFF = "USER_BALANCE_PAY_OFF";           //This user turns the basic payment capability off.
    public static final String RESP_RESULT_CODE_RISK_REJECT = "RISK_REJECT";             //Payment is declined by risk control.
    public static final String RESP_RESULT_CODE_KEY_NO_FOUND = "KEY_NO_FOUND";
    
	public static final String DEFAULT_VERSION = "2.0.0";
	public static final String CLIENT_ID = "Test_ClientId_0011";
	public static final String PRODUCT_CODE_OFFLINE_PAY = "OFFLINE_PAY";
	public static final String CODE_TYPE_BARCODE = "BARCODE";
	public static final String MERCHANT_ID = "Test_ClientId_0011";
	public static final String RSA_KEY = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCs4MgIXkq5xMGlM+tPVnuQCCwWsRhMEQwPWhK/L9Ez5b/EQ9VAdf0TMYpYxyjTPYrwRaDc7pc2eouaOvOsQimdPdaLhDTAi4Dk8ltIi94ETASYivXHTrNnVxNCCukkWUH3dycom0B3Lt4o82bqt1I/CzUgSjzETUS4qv65eGD3pm9WEsPhIM3m40bhGKo+tIxoqqFywGe7al1oBk300dQIdntgDO069fOtXfyAlI9Cr/hSwYrPZjlC2cEHKFh4z4udg5KqRjQ7LM8lplAmq6mexhWpxJfRGflCjyNvXIhwufnMmTtGbH5LY/E1vriaNziJJs9LjzmIjSAKpWD+1mdTAgMBAAECggEAKlHAReQnMMCMks9Z3AsUplaJzGg/xWcq55sqGakv8h9cyUO9sVDwycEH1+GxCbz4BQdWcJ7rX0eyYufpfJKZs0dO/Mo5S6UawXDejazHhY/CVEE4FzkZy8NSEImxNteuwVBmwpb2ufY8JhQnbm+/73Tj5Wqm43ryxq39OxPP0O+C/cFqXjtZbNdhnFxJIzp9t51wBiwDFHUvC8Jhve2CLIdNrG/BtLsv2QH2Vz0HU8YTxNXu3mHvDrOqBgHhPAMQSfp7EJZhYz5hWPmUo5IIx92UyfgBkNBQ6WThaYgjJH8v4WsgUCni5Da4V6DacBXTl+fmKEwttsqt4ZA9iPjD6QKBgQDgXIWSwqkEP5jl7WHwNhrdHbf4OgC2chQvfDVHPX/OSzt3U8ydg7Vcje5qUDZiIedQpIKL/366NqDJcP5kts3JBPP2lleIOOU79OwcbOxtIsNFD5zSpe5N6x8HtSz148NJjbdA5q7kEAGiTAVOsy3x558DE1blO08F7wNpsnkx5wKBgQDFQbP/YuWDqZt/WrwMZWGEPSBmXhqUiks5ifHXjtE3B7XyOlguU2T2m2jhJDwNCpzoa4i853nhPNkqPV0z2/r1Jby8V5FCZQ7OWolAdPog3T91GGU2ZzgWnCL5hkzsPEd5KwADexqZgyY8nt3HZW2mup+GY1za3lmuXJOB3dMJtQKBgGU2t/duI4tMGXDYdw9DiTjpGD4+BGZJ7ntBgFOEFez9mXUKc8s3SJDZYo3JY88/VjfWDyKS/xuIW16kv861fP5LYreCOTi12gwDQKTOKpZNUWQNWATbYDb8c8IpZIAE4OuEp//6UTmHUwVhYB+Ry49Omzhq+A9vgwW9Pn/nSK9PAoGBALmSiVsO2CtYaeCTJOxoiChXNV740vrBa+myu+F2C59A0swSVZknfYl/RVjhtNj0Dg+d8uL8xa2/h/J0DnXWUy9EFF3/57lVx2scw5jvM82tN6DalYVr79ldhI3I6h8oynjUmkusCac4OcW4E58d7d14HtHYMqf5nNcrYt4/KOYtAoGBAIrk5EwEX0ea7N3cRFqw+EGSVoeEd/bUn++/+fk9/Ew2uob96umZhLl0N7Vq0J5PLFzEzsZhYqC95zJvVOXC+i60QTiNF2c1mLV1A09IuxAEejeICZEUi/7XWCUGZlJtHG14x8DFX/AfkFUXE1w+eLoVEBzG54SNEf5Aw8Brevt5";
	public static final String RESULT_CODE_SUCCESS = "SUCCESS";
	public static final String PAY_STATUS_WAIT_PAY = "WAIT_PAY";
	public static final String PAY_STATUS_SUCCESS = "PAY_SUCCESS";
	public static final String PAY_STATUS_CANCELLED = "PAY_CANCELLED";
	public static final String PAY_STATUS_FAILED = "PAY_FAILED";
    

	public static final List<String> PAY_FAIL_ERR_CODE_LISTS = Arrays.asList(
	        RESP_RESULT_CODE_KEY_NO_FOUND, RESP_RESULT_CODE_REQUEST_TRAFFIC_EXCEED_LIMIT,
	        RESP_RESULT_CODE_RISK_REJECT, RESP_RESULT_CODE_USER_NOT_EXIST, RESP_RESULT_CODE_USER_STATUS_ABNORMAL,
	        RESP_RESULT_CODE_CURRENCY_NOT_SUPPORT, RESP_RESULT_CODE_AMOUNT_EXCEED_LIMIT,
	        RESP_RESULT_CODE_COUNT_EXCEED_LIMIT, RESP_RESULT_CODE_USER_BALANCE_NOT_ENOUGH,
	        RESP_RESULT_CODE_IDENTITY_CODE_NOT_SUPPORT, RESP_RESULT_CODE_ORDER_IS_CLOSED,
	        RESP_RESULT_CODE_ORDER_STATUS_INVALID, RESP_RESULT_CODE_USER_NO_PAYMENT_INSTRUMENT,
	        RESP_RESULT_CODE_USER_BALANCE_PAY_OFF    
	);
}
