package com.wosai.mpay.api.alipay;

import java.io.IOException;
import java.security.Security;
import java.util.Map;
import java.util.Random;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.AlipaySignature;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.WebUtils;
import com.wosai.mpay.util.XmlUtils;

public class AlipayV1Client {
    private static final Logger logger = LoggerFactory.getLogger(AlipayV1Client.class);

    private String signType = AlipayConstants.SIGN_TYPE_MD5;
    private String charset = AlipayConstants.CHARSET_GBK;
    private int connectTimeout = 3000;
    private int readTimeout = 15000;
    private String gatewayUrl = AlipayV1Config.GATEWAY;


    static {
        //清除安全设置
        Security.setProperty("jdk.certpath.disabledAlgorithms", "");
    }

    public AlipayV1Client() {
        this(AlipayConstants.CHARSET_GBK);
    }
    
    public AlipayV1Client(String charset) {
        this.charset = charset;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }
    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    private void sign(String partner, String key, Map<String, String> request) throws MpayException {
        request.put(ProtocolV1Fields.PARTNER, partner);
        String sign = AlipaySignature.md5Sign(request, key, charset);
        request.put(ProtocolV1Fields.SIGN, sign);
        request.put(ProtocolV1Fields.SIGN_TYPE, signType);
    }

    public Map<String, Object> call(String gatewayUrl, String partner, String key, Map<String, String> request) throws MpayException, MpayApiNetworkError {
        sign(partner, key, request);
        logger.debug("request {}", request);
        String resp = HttpClientUtils.doPost(AlipayV1Client.class.getName(), null, null, gatewayUrl, request, charset, connectTimeout, readTimeout);
//        String resp = WebUtils.doPost(null, null, gatewayUrl, request, charset, connectTimeout, readTimeout);
        logger.debug("response {}", resp.replaceAll("\\n", ""));
        Map<String, Object> result = XmlUtils.parse(resp);
        if (result.isEmpty()) {
            throw new MpayApiUnknownResponse(resp);
        }
        return result;
    }

    public String buildQuery(String gatewayUrl, String partner, String key, Map<String, String> request) throws MpayException, MpayApiNetworkError {
        sign(partner, key, request);

        logger.debug("build request {}", request);
        try {
            return WebUtils.buildQuery(request, charset);
        }
        catch (IOException e) {
            throw new MpayApiConnectError("failed to build alipayV1 query string", e);
        }
    }
    
    public String buildUrl(String gatewayUrl, String partner, String key, Map<String, String> request) throws MpayException, MpayApiNetworkError {
        return String.format("%s?%s", gatewayUrl, buildQuery(gatewayUrl, partner, key, request));
    }

    private static String generateTradeNo() {
        Random random = new Random();
        return String.format("%d%d", System.currentTimeMillis(), random.nextInt(10000));
    }
    
    public static void main(String[] args) throws Exception {
//        testWapCreateDirectPayByUser();
        testCreateAndPay();
//        testRefund();
//        testCancel();
//        testQuery();
//        testPrecreate();
    }

    public static void testCreateAndPay() throws MpayException, MpayApiNetworkError, BuilderException {
        RequestV1Builder builder = new RequestV1Builder();
        builder.set(ProtocolV1Fields.INPUT_CHARSET, "gbk");
        builder.set(ProtocolV1Fields.SERVICE, AlipayV1Methods.ALIPAY_ACQUIRE_CREATEANDPAY);
        builder.set(BusinessV1Fields.SUBJECT, "测试收银");
        String outTradeNo = generateTradeNo();
        builder.set(BusinessV1Fields.OUT_TRADE_NO, outTradeNo);
        builder.set(BusinessV1Fields.PRODUCT_CODE, AlipayConstants.PRODUCT_BARCODE_PAY_OFFLINE);
        builder.set(BusinessV1Fields.TOTAL_FEE, "0.02");
        builder.set(BusinessV1Fields.IT_B_PAY, "1m");
        builder.set(BusinessV1Fields.DYNAMIC_ID_TYPE, "bar_code");
        builder.set(BusinessV1Fields.DYNAMIC_ID, "285996308064134461");
        builder.setExtend(BusinessV1Fields.EX_AGENT_ID, AlipayV1Config.SQB_AGENTID);

        Map<String, String> req = builder.build();

        AlipayV1Client client = new AlipayV1Client(AlipayConstants.CHARSET_GBK);
        Map<String, Object> result = client.call(client.gatewayUrl, AlipayV1Config.SQB_PARTNERID,
                                                 AlipayV1Config.SQB_APPKEY,
                                                 req);
    }

    public static void testRefund() throws  Exception {
        RequestV1Builder builder = getDefaultRequestV1Builder();
        builder.set(ProtocolV1Fields.SERVICE, AlipayV1Methods.ALIPAY_ACQUIRE_REFUND);
        builder.set(BusinessV1Fields.OUT_TRADE_NO, "14473223624393173");
        builder.set(BusinessV1Fields.OUT_REQUEST_NO, "144732236243931732");
        builder.set(BusinessV1Fields.REFUND_AMOUNT, "0.01");
        Map<String, String> req = builder.build();

        AlipayV1Client client = new AlipayV1Client();
        Map<String, Object> result = client.call(client.gatewayUrl, AlipayV1Config.SQB_PARTNERID,
                AlipayV1Config.SQB_APPKEY,
                req);
    }

    public static void testCancel() throws BuilderException, MpayException, MpayApiNetworkError {
        RequestV1Builder builder = getDefaultRequestV1Builder();
        builder.set(ProtocolV1Fields.SERVICE, AlipayV1Methods.ALIPAY_ACQUIRE_CANCEL);
        builder.set(BusinessV1Fields.OUT_TRADE_NO, "14480800312239235");
        AlipayV1Client client = new AlipayV1Client();
        Map<String, Object> result = client.call(client.gatewayUrl, AlipayV1Config.SQB_PARTNERID, AlipayV1Config.SQB_APPKEY, builder.build());
    }

    public  static void testQuery() throws BuilderException, MpayException, MpayApiNetworkError {
        RequestV1Builder builder = new RequestV1Builder();
        builder.set(ProtocolV1Fields.INPUT_CHARSET, "gbk");
        builder.set(ProtocolV1Fields.SERVICE, AlipayV1Methods.ALIPAY_ACQUIRE_QUERY);
        builder.set(BusinessV1Fields.OUT_TRADE_NO, "14492122200038936");
        AlipayV1Client client = new AlipayV1Client(AlipayConstants.CHARSET_GBK);
        Map<String, Object> result = client.call(client.gatewayUrl, AlipayV1Config.SQB_PARTNERID, AlipayV1Config.SQB_APPKEY, builder.build());

    }

    public static void testPrecreate()  throws BuilderException, MpayException, MpayApiNetworkError {
        RequestV1Builder builder = getDefaultRequestV1Builder();
        builder.set(ProtocolV1Fields.SERVICE, AlipayV1Methods.ALIPAY_ACQUIRE_PRECREATE);
//        builder.set(ProtocolV1Fields.NOTIFY_URL, AlipayV1Config.NOTIFY_URL);
        builder.set(BusinessV1Fields.OUT_TRADE_NO, generateTradeNo());
        builder.set(BusinessV1Fields.SUBJECT, "魅族mx5");
        builder.set(BusinessV1Fields.BODY, "魅族mx5 一部");
        builder.set(BusinessV1Fields.PRODUCT_CODE, AlipayConstants.PRODUCT_QR_CODE_OFFLINE);
        builder.set(BusinessV1Fields.TOTAL_FEE, "0.03");
        builder.set(BusinessV1Fields.IT_B_PAY, "12h");
        builder.setExtend(BusinessV1Fields.EX_AGENT_ID, AlipayV1Config.SQB_AGENTID);
        AlipayV1Client client = new AlipayV1Client();
        Map<String, Object> result = client.call(client.gatewayUrl, AlipayV1Config.SQB_PARTNERID, AlipayV1Config.SQB_APPKEY, builder.build());
    }

    public static void testWapCreateDirectPayByUser() throws Exception {
        RequestV1Builder builder = new RequestV1Builder();
        builder.set(ProtocolV1Fields.INPUT_CHARSET, "utf-8");
        builder.set(ProtocolV1Fields.SERVICE, AlipayV1Methods.ALIPAY_WAP_CREATE_DIRECT_PAY_BY_USER);
        builder.set(BusinessV1Fields.SUBJECT, "测试收银");
        String outTradeNo = generateTradeNo();
        builder.set(BusinessV1Fields.OUT_TRADE_NO, outTradeNo);
        builder.set(BusinessV1Fields.TOTAL_FEE, "0.02");
        builder.set(BusinessV1Fields.SELLER_ID, WapConfig.SQB_PARTNERID);
        builder.set(BusinessV1Fields.PAYMENT_TYPE, "1");
        builder.set(BusinessV1Fields.IT_B_PAY, "15m");
//        builder.setExtend(BusinessV1Fields.EX_AGENT_ID, AlipayV1Config.SQB_AGENTID);

        Map<String, String> req = builder.build();
        AlipayV1Client client = new AlipayV1Client(AlipayConstants.CHARSET_UTF8);
        
        String url = client.buildUrl(client.gatewayUrl, WapConfig.SQB_PARTNERID, WapConfig.SQB_APPKEY, req);
        System.out.println(url);
        
    }
    public static RequestV1Builder getDefaultRequestV1Builder () {
        RequestV1Builder builder = new RequestV1Builder();
        builder.set(ProtocolV1Fields.PARTNER, AlipayV1Config.SQB_PARTNERID);
        builder.set(ProtocolV1Fields.INPUT_CHARSET, "gbk");
        return builder;
    }

}
