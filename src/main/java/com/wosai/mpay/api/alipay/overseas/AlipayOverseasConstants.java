package com.wosai.mpay.api.alipay.overseas;

import java.util.Arrays;
import java.util.List;

/**
 * Created by xuchmao on 17/8/2.
 */
public class AlipayOverseasConstants {
    public static final String DATE_TIME_FORMAT = "yyyyMMddhhmmss"; //默认时间格式
    public static final String DATE_TIMEZONE = "GMT+8";             //Date默认时区
    public static final String SIGN_TYPE_MD5 = "MD5";               //签名方式 MD5
    public static final String SIGN_TYPE_RSA = "RSA";               //签名方式 RSA
    public static final String SIGN_TYPE_RSA2 = "RSA2";             //签名方式 RSA2
    public static final String DEFAULT_INPUT_CHARSET = "GBK";       //默认字符集GBK

    //barcode https://global.alipay.com/service/barcode/9
    //csb     https://global.alipay.com/service/merchant_QR_Code_hk/2
    public static final String SERVICE_NAME_PAY_BARCODE = "alipay.acquire.overseas.spot.pay";       // b2c支付
    public static final String SERVICE_NAME_PAY_PRECREATE = "alipay.acquire.precreate";       		// csb 支付接口名称
    public static final String SERVICE_NAME_CANCEL = "alipay.acquire.cancel";               		// 撤单接口名称
    public static final String SERVICE_NAME_REFUND = "alipay.acquire.overseas.spot.refund"; 		// 退款接口名称
    public static final String SERVICE_NAME_QUERY = "alipay.acquire.overseas.query";        		// 查单接口名称*
    public static final String SERVICE_NAME_ACQUIRE_CREATE = "alipay.acquire.create";               // wap 支付接口
    public static final String SERVICE_NAME_REFUND_QUERY = "alipay.acquire.refund.query";           //退款查询接口
    
    
    //wap https://global.alipay.com/service/wap/6
    public static final String WAP_SERVICE_NAME_PAY = "create_forex_trade_wap";
    public static final String WAP_SERVICE_NAME_REFUND = "forex_refund";
    public static final String WAP_SERVICE_NAME_QUERY = "single_trade_query";
    
    
    public static final String BIZ_PRODUCT_OVERSEAS_MBARCODE_PAY = "OVERSEAS_MBARCODE_PAY";
    public static final String REFUND_IS_SYNC_YES = "Y";
    public static final String REFUND_IS_SYNC_NO = "N";
    
    public static final String BIZ_TYPE_OVERSEA_SHOP_QRCODE = "OVERSEASHOPQRCODE";
    
    public static final String IDENTITY_CODE_TYPE_BARCODE = "barcode";
    
    public static final String GATEWAY_URL = "https://intlmapi.alipay.com/gateway.do";
    
    public static final String RESP_CODE_SUCCESS = "T";                 //请求提交成功
    public static final String RESP_CODE_FAIL = "F";                    //请求提交失败
    
    public static final String RESP_BIZ_CODE_SUCCESS = "SUCCESS";    //业务请求成功
    public static final String RESP_BIZ_CODE_FAIL = "FAIL";          //业务请求状态失败
    public static final String RESP_BIZ_CODE_FAILED = "FAILED";      //业务请求状态失败
    public static final String RESP_BIZ_CODE_UNKNOW = "UNKNOW";      //业务请求状态未知
    public static final String RESP_BIZ_ERROR_CODE= "error";         //业务请求失败时错误码
    
    public static final String TRANS_STATUS_WAIT_BUYER_PAY = "WAIT_BUYER_PAY";  //查单接口返回，等待支付
    public static final String TRANS_STATUS_TRADE_SUCCESS = "TRADE_SUCCESS";    //查单接口返回，支付成功
    public static final String TRANS_STATUS_TRADE_CLOSED = "TRADE_CLOSED";      //查单接口返回，订单关闭
    
    /**接口返回通用错误码*/
    public static final String RESP_ERROR_CODE_SYSTEM_ERROR = "SYSTEM_ERROR";                             //系统错误
    public static final String RESP_ERROR_CODE_ILLEGAL_SIGN = "ILLEGAL_SIGN";                             //签名错误
    public static final String RESP_ERROR_CODE_HAS_NO_PRIVILEGE = "HAS_NO_PRIVILEGE";                     //无操作权限
    public static final String RESP_ERROR_CODE_INVALID_PARAMETER = "INVALID_PARAMETER";                   //参数值错误
    public static final String RESP_ERROR_CODE_ILLEGAL_ARGUMENT = "ILLEGAL_ARGUMENT";                     //参数字段错误
    public static final String RESP_ERROR_CODE_ILLEGAL_PARTNER = "ILLEGAL_PARTNER";                       //Partner ID 不正确
    public static final String RESP_ERROR_CODE_ILLEGAL_EXTERFACE = "ILLEGAL_EXTERFACE";                   //接口配置错误
    public static final String RESP_ERROR_CODE_ILLEGAL_PARTNER_EXTERFACE = "ILLEGAL_PARTNER_EXTERFACE";   //Partner ID 接口未授权
    public static final String RESP_ERROR_CODE_ILLEGAL_SIGN_TYPE = "ILLEGAL_SIGN_TYPE";                   //签名类型错误
    public static final String RESP_ERROR_CODE_NOT_FOUND= "NOT_FOUND";                                    //订单未找到

    /**alipay overseas 付款(pay+precreate) 错误码*/
    public static final String RESP_PAY_ERROR_CODE_TRADE_BUYER_NOT_MATCH = "TRADE_BUYER_NOT_MATCH";                                  //Buyer not match. merchant may ask and scan the user to show the last barcode which is using for previous payment or merchant can refresh the partner_trans_id to generate a new payment
    public static final String RESP_PAY_ERROR_CODE_TRADE_HAS_CLOSE = "TRADE_HAS_CLOSE";                                              //订单已经关闭
    public static final String RESP_PAY_ERROR_CODE_TRADE_STATUS_ERROR = "TRADE_STATUS_ERROR";                                        //订单状态错误。重新发起支付
    public static final String RESP_PAY_ERROR_CODE_EXIST_FORBIDDEN_WORD = "EXIST_FORBIDDEN_WORD";                                    //订单包含敏感词
    public static final String RESP_PAY_ERROR_CODE_SELLER_NOT_EXIST = "SELLER_NOT_EXIST";                                            //alipay_seller_id 不正确
    public static final String RESP_PAY_ERROR_CODE_BUYER_NOT_EXIST = "BUYER_NOT_EXIST";                                              //buyer_identity_code 不正确
    public static final String RESP_PAY_ERROR_CODE_BUYER_ENABLE_STATUS_FORBID = "BUYER_ENABLE_STATUS_FORBID";                        //消费者账户不可用
    public static final String RESP_PAY_ERROR_CODE_BUYER_SELLER_EQUAL = "BUYER_SELLER_EQUAL";                                        //消费者和商家不可以是同一人
    public static final String RESP_PAY_ERROR_CODE_CLIENT_VERSION_NOT_MATCH = "CLIENT_VERSION_NOT_MATCH";                            //消费者客户端需升级
    public static final String RESP_PAY_ERROR_CODE_SOUNDWAVE_PARSER_FAIL = "SOUNDWAVE_PARSER_FAIL";                                  //条码错误
    public static final String RESP_PAY_ERROR_CODE_CONTEXT_INCONSISTENT = "CONTEXT_INCONSISTENT";
    public static final String RESP_PAY_ERROR_CODE_PRODUCT_AMOUNT_LIMIT_ERROR = "PRODUCT_AMOUNT_LIMIT_ERROR";
    public static final String RESP_PAY_ERROR_CODE_BUYER_BALANCE_NOT_ENOUGH = "BUYER_BALANCE_NOT_ENOUGH";                            //消费者账户余额不足
    public static final String RESP_PAY_ERROR_CODE_TOTAL_FEE_EXCEED = "TOTAL_FEE_EXCEED";                                            //交易金额超出上限 Trade amount is exceed the limit.
    public static final String RESP_PAY_ERROR_CODE_BUYER_PAYMENT_AMOUNT_DAY_LIMIT_ERROR = "BUYER_PAYMENT_AMOUNT_DAY_LIMIT_ERROR";    //消费者日交易金额超出上限
    public static final String RESP_PAY_ERROR_CODE_BUYER_PAYMENT_AMOUNT_MONTH_LIMIT_ERROR = "BUYER_PAYMENT_AMOUNT_MONTH_LIMIT_ERROR";//消费者月交易金额超出上限
    public static final String RESP_PAY_ERROR_CODE_ERROR_BUYER_CERTIFY_LEVEL_LIMIT = "ERROR_BUYER_CERTIFY_LEVEL_LIMIT";
    public static final String RESP_PAY_ERROR_CODE_ERROR_SELLER_CERTIFY_LEVEL_LIMIT = "ERROR_SELLER_CERTIFY_LEVEL_LIMIT";
    public static final String RESP_PAY_ERROR_CODE_PAYMENT_REQUEST_HAS_RISK = "PAYMENT_REQUEST_HAS_RISK";                            //交易有风险，被支付宝拦截。使用其他方式支付
    public static final String RESP_PAY_ERROR_CODE_NO_PAYMENT_INSTRUMENTS_AVAILABLE = "NO_PAYMENT_INSTRUMENTS_AVAILABLE";            //付款设备不可用
    public static final String RESP_PAY_ERROR_CODE_BUYER_BANKCARD_BALANCE_NOT_ENOUGH = "BUYER_BANKCARD_BALANCE_NOT_ENOUGH";
    public static final String RESP_PAY_ERROR_CODE_PAYMENT_FAIL = "PAYMENT_FAIL";                                                    //支付失败，请使用相同订单号进行重试
    public static final String RESP_PAY_ERROR_CODE_MOBILE_PAYMENT_SWITCH_OFF = "MOBILE_PAYMENT_SWITCH_OFF";
    public static final String RESP_PAY_ERROR_CODE_USER_FACE_PAYMENT_SWITCH_OFF = "USER_FACE_PAYMENT_SWITCH_OFF";
    public static final String RESP_PAY_ERROR_CODE_ERROR_BALANCE_PAYMENT_DISABLE = "ERROR_BALANCE_PAYMENT_DISABLE";
    public static final String RESP_PAY_ERROR_CODE_EXCHANGE_AMOUNT_OR_CURRENCY_ERROR = "EXCHANGE_AMOUNT_OR_CURRENCY_ERROR";
    public static final String RESP_PAY_ERROR_CODE_NOT_SUPPORT_PAYMENT_INST = "NOT_SUPPORT_PAYMENT_INST";                            //不支持的付款设备
    
    /**alipay overseas 撤单(cancel) 错误码*/
    public static final String RESP_CANCEL_ERROR_CODE_REASON_TRADE_BEEN_FREEZEN = "REASON_TRADE_BEEN_FREEZEN";                       //订单被冻结
    public static final String RESP_CANCEL_ERROR_CODE_BUYER_ERROR = "BUYER_ERROR";                                                   //消费者账户不存在
    public static final String RESP_CANCEL_ERROR_CODE_BUYER_ENABLE_STATUS_FORBID = "BUYER_ENABLE_STATUS_FORBID";                     //消费者账户不允许退款
    public static final String RESP_CANCEL_ERROR_CODE_SELLER_ERROR = "SELLER_ERROR";                                                 //卖家账户不存在
    public static final String RESP_CANCEL_ERROR_CODE_TRADE_NOT_EXIST = "TRADE_NOT_EXIST";                                           //订单不存在
    public static final String RESP_CANCEL_ERROR_CODE_TRADE_STATUS_ERROR = "TRADE_STATUS_ERROR";                                     //订单状态错误
    public static final String RESP_CANCEL_ERROR_CODE_TRADE_HAS_FINISHED = "TRADE_HAS_FINISHED";                                     //订单已经完成。无法撤单
    public static final String RESP_CANCEL_ERROR_CODE_MERCHANT_BALANCE_NOT_ENOUGH = "MERCHANT_BALANCE_NOT_ENOUGH";                   //卖家账户余额不足
    public static final String RESP_CANCEL_ERROR_CODE_TRADE_CANCEL_TIME_OUT = "TRADE_CANCEL_TIME_OUT";                               //撤单超时。
    
    /**alipay overseas 查单(query) 错误码*/
    public static final String RESP_QUERY_ERROR_CODE_REASON_TRADE_BEEN_FREEZEN = "REASON_TRADE_BEEN_FREEZEN";                        //订单被冻结
    public static final String RESP_QUERY_ERROR_CODE_TRADE_NOT_EXIST = "TRADE_NOT_EXIST";                                            //订单不存在
    
    /**alipay overseas 退款(refund) 错误码*/
    public static final String RESP_REFUND_ERROR_CODE_REASON_TRADE_BEEN_FREEZEN = "REASON_TRADE_BEEN_FREEZEN";                       //订单被冻结
    public static final String RESP_REFUND_ERROR_CODE_TRADE_NOT_EXIST = "TRADE_NOT_EXIST";                                           //订单不存在
    public static final String RESP_REFUND_ERROR_CODE_TRADE_STATUS_ERROR = "TRADE_STATUS_ERROR";                                     //Corresponding trade status is not allowed for current operate.
    public static final String RESP_REFUND_ERROR_CODE_REFUND_AMT_RESTRICTION = "REFUND_AMT_RESTRICTION";                             //退款金额受限制
    public static final String RESP_REFUND_ERROR_CODE_REQUEST_AMOUNT_EXCEED = "REQUEST_AMOUNT_EXCEED";                               //退款金额受限制
    public static final String RESP_REFUND_ERROR_CODE_RETURN_AMOUNT_EXCEED = "RETURN_AMOUNT_EXCEED";                                 //退款金额受限制
    public static final String RESP_REFUND_ERROR_CODE_MORE_THAN_ALLOW_REFUND_FEE = "MORE_THAN_ALLOW_REFUND_FEE";                     //退款金额受限制
    public static final String RESP_REFUND_ERROR_CODE_REFUND_CURRENCY_NOT_EQUAL_PAY = "REFUND_CURRENCY_NOT_EQUAL_PAY";               //The refund currency is not allowed for current operate.
    public static final String RESP_REFUND_ERROR_CODE_DISCORDANT_REPEAT_REQUEST = "DISCORDANT_REPEAT_REQUEST";                       //退款流水号重复
    public static final String RESP_REFUND_ERROR_CODE_TRADE_HAS_CLOSE = "TRADE_HAS_CLOSE";                                           //订单已经关闭。消费者未完成支付或者支付超时未完成；订单已经全额退款。此时无法退款
    public static final String RESP_REFUND_ERROR_CODE_CURRENCY_NOT_SAME = "CURRENCY_NOT_SAME";                                       //退款货币类型与交易类型不一致
    public static final String RESP_REFUND_ERROR_CODE_BUYER_NOT_EXIST = "BUYER_NOT_EXIST";                                           //消费者账户状态异常。The buyer’s account has been cancelled.
    public static final String RESP_REFUND_ERROR_CODE_BUYER_ENABLE_STATUS_FORBID = "BUYER_ENABLE_STATUS_FORBID";                     //消费者账户状态异常。The buyer’s account has been cancelled.
    public static final String RESP_REFUND_ERROR_CODE_REASON_TRADE_REFUND_FEE_ERR = "REASON_TRADE_REFUND_FEE_ERR";                   //The incoming refund amount is too small, resulting in conversion into another currency for the amount of 0
    public static final String RESP_REFUND_ERROR_CODE_INVALID_ROUNDED_AMOUNT = "INVALID_ROUNDED_AMOUNT";                             //请求的退款金额换算成另一个币种金额后，两个币种的金额不能同时退完。
    public static final String RESP_REFUND_ERROR_CODE_SELLER_BALANCE_NOT_ENOUGH = "SELLER_BALANCE_NOT_ENOUGH";                       //Merchant 's balance is insufficient. Only when is_sync is Y will it return
    public static final String RESP_REFUND_ERROR_CODE_SERVICE_REFUSE = "SERVICE_REFUSE";                                             //The time period (00:00-01:30) does not support refund the trade which is not paid on current day. Only when is_sync is Y will it return
    public static final String RESP_REFUND_ERROR_CODE_TRADE_SETTLE_ERROR = "TRADE_SETTLE_ERROR";                                     //The Alipay system is abnormal, please contact Alipay technical support. Only when is_sync is Y will it return
    public static final String RESP_REFUND_ERROR_CODE_REFUND_CHARGE_ERROR = "REFUND_CHARGE_ERROR";                                   //The Alipay system is abnormal, please try again or contact Alipay technical support.
	public static final String OVERSEAS_MBARCODE_PAY = "OVERSEAS_MBARCODE_PAY";														 //Order placement used to distinguish the business type: OVERSEAS_MBARCODE_PAY: face to face payment overseas
    
	public static final List<String> PROTOCAL_ERR_CODE_LISTS = Arrays.asList(
	        RESP_ERROR_CODE_ILLEGAL_SIGN, RESP_ERROR_CODE_HAS_NO_PRIVILEGE,
	        RESP_ERROR_CODE_INVALID_PARAMETER, RESP_ERROR_CODE_ILLEGAL_ARGUMENT,
	        RESP_ERROR_CODE_ILLEGAL_PARTNER, RESP_ERROR_CODE_ILLEGAL_EXTERFACE,
	        RESP_ERROR_CODE_ILLEGAL_PARTNER_EXTERFACE, RESP_ERROR_CODE_ILLEGAL_SIGN_TYPE
    );
    
    public static final List<String> PAY_FAIL_ERR_CODE_LISTS = Arrays.asList(
            RESP_PAY_ERROR_CODE_BUYER_ENABLE_STATUS_FORBID, RESP_PAY_ERROR_CODE_TRADE_BUYER_NOT_MATCH,
            RESP_PAY_ERROR_CODE_TRADE_HAS_CLOSE, RESP_PAY_ERROR_CODE_TRADE_STATUS_ERROR,
            RESP_PAY_ERROR_CODE_EXIST_FORBIDDEN_WORD, RESP_PAY_ERROR_CODE_SELLER_NOT_EXIST,
            RESP_PAY_ERROR_CODE_BUYER_SELLER_EQUAL, RESP_PAY_ERROR_CODE_CLIENT_VERSION_NOT_MATCH,
            RESP_PAY_ERROR_CODE_SOUNDWAVE_PARSER_FAIL, RESP_PAY_ERROR_CODE_CONTEXT_INCONSISTENT,
            RESP_PAY_ERROR_CODE_PRODUCT_AMOUNT_LIMIT_ERROR, RESP_PAY_ERROR_CODE_BUYER_BALANCE_NOT_ENOUGH,
            RESP_PAY_ERROR_CODE_TOTAL_FEE_EXCEED, RESP_PAY_ERROR_CODE_BUYER_PAYMENT_AMOUNT_DAY_LIMIT_ERROR,
            RESP_PAY_ERROR_CODE_BUYER_PAYMENT_AMOUNT_MONTH_LIMIT_ERROR, RESP_PAY_ERROR_CODE_ERROR_BUYER_CERTIFY_LEVEL_LIMIT,
            RESP_PAY_ERROR_CODE_ERROR_SELLER_CERTIFY_LEVEL_LIMIT, RESP_PAY_ERROR_CODE_PAYMENT_REQUEST_HAS_RISK,
            RESP_PAY_ERROR_CODE_NO_PAYMENT_INSTRUMENTS_AVAILABLE, RESP_PAY_ERROR_CODE_BUYER_BANKCARD_BALANCE_NOT_ENOUGH,
            RESP_PAY_ERROR_CODE_MOBILE_PAYMENT_SWITCH_OFF, RESP_PAY_ERROR_CODE_USER_FACE_PAYMENT_SWITCH_OFF,
            RESP_PAY_ERROR_CODE_ERROR_BALANCE_PAYMENT_DISABLE, RESP_PAY_ERROR_CODE_EXCHANGE_AMOUNT_OR_CURRENCY_ERROR,
            RESP_PAY_ERROR_CODE_NOT_SUPPORT_PAYMENT_INST, RESP_PAY_ERROR_CODE_BUYER_NOT_EXIST
    );
}
