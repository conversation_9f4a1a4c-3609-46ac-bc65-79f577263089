package com.wosai.mpay.api.alipay;

public abstract class ProtocolV2Fields {
    public static final String APP_ID = "app_id";
    public static final String METHOD = "method";
    public static final String CHARSET = "charset";
    public static final String SIGN = "sign";
    public static final String SIGN_TYPE = "sign_type";
    public static final String TIMESTAMP = "timestamp";
    public static final String AUTH_TOKEN = "auth_token";
    public static final String APP_AUTH_TOKEN = "app_auth_token";
    public static final String BIZ_CONTENT = "biz_content";
    public static final String VERSION = "version";
    public static final String SERVICE = "service";
    public static final String NOTIFY_URL = "notify_url";

    public static final String RESP_SUFFIX = "_response";
    public static final String RETURN_URL = "return_url";
    public static final String TIME_EXPIRE = "time_expire";
    public static final String CERT_ID = "cert_id";
    public static final String ISV_APP_ID = "isv_app_id";
    public static final String UTC_TIMESTAMP = "utc_timestamp";

}
