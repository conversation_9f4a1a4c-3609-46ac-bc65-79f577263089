package com.wosai.mpay.api.alipay.intl;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import com.wosai.mpay.exception.BuilderException;

public class RequestBuilder {
    Map<String, String> header;
    Map<String, Object> body;
    
    public RequestBuilder(){
    	header = new LinkedHashMap<String, String>();
    	body = new LinkedHashMap<String, Object>();
    }
    
    @SuppressWarnings("serial")
    public Map<String, Object> build() throws BuilderException {
        return new HashMap<String, Object>(){{
        	put(BusinessFields.REQUEST, new LinkedHashMap<String, Object>(){{
        		put(BusinessFields.HEAD, header);
        		put(BusinessFields.BODY, body);
        	}});
        }};
    }
    
    public void headSet(String field, String value) {
    	header.put(field,  value);
    }
    
    
    public void bodySet(String field, Object value) {
    	body.put(field, value);
    }
}