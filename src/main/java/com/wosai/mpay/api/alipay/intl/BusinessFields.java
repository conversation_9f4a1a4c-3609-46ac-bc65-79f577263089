package com.wosai.mpay.api.alipay.intl;

public class BusinessFields {
	public static final String REQUEST = "request";
	public static final String HEAD = "head";
	public static final String BODY = "body";	
	public static final String VERSION = "version";
	public static final String FUNCTION = "function";                   //According to specifications defined by each business domain
	public static final String CLINET_ID = "clientId";                  //Provided by <PERSON><PERSON><PERSON>, used to identify partner and application system
	public static final String REQ_TIME = "reqTime";                    //DateTime with timezone, which	follows the ISO-8601 standard.
	public static final String REQ_MSG_ID = "reqMsgId";                 //Each request will	be assigned with a unique id (uuid). The reqMsgId identify a unique system request, it is not used to	identity a unique	business request.
	public static final String RESERVE = "reserve";                     //DateTime with timezone, which	follows the ISO-8601 standard.
    public static final String PRODUCT_CODE = "productCode";           //OFFLINE_PAY
	public static final String MERCHANT_TRANS_ID = "merchantTransId";
	public static final String CODE_TYPE = "codeType";
	public static final String IDENTITY_CODE = "identityCode";
	public static final String ORDER_INFO = "order";
	public static final String ORDER_AMOUNT = "orderAmount";
	public static final String CURRENCY = "currency";                   //the currency used for labelling the price of the transaction, this is also the settlement currency Alipay settled to the partner
	public static final String VALUE = "value";
	public static final String ORDER_TITLE = "orderTitle";
	public static final String ORDER_DETAIL = "orderDetail";
	public static final String PARTNER_ID = "partnerId";
	public static final String ACQUIREMENT_ID = "acquirementId";
	public static final String MERCHANT_REFUND_ID = "merchantRefundId";
	public static final String REFUND_AMOUNT = "refundAmount";         //Less than or equal to the original transaction amount and the left transaction amount if ever refunded.
	public static final String MERCHANT_ID = "merchantId";
	public static final String EXTEND_INFO = "extendInfo";
	public static final String SELLER = "seller";
	public static final String SELLER_ID = "sellerId";
	public static final String SELLER_NAME = "sellerName";
	public static final String MCC = "mcc";
	public static final String STORE_ID = "storeId";
	public static final String STORE_NAME = "storeName";
	public static final String TERMINAL_ID = "terminalId";
	public static final String REFUND_IS_SYNC = "isSync";

}
