package com.wosai.mpay.api.alipay;

import java.io.IOException;
import java.security.Security;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.AlipayV2Exception;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.AlipaySignature;
import com.wosai.mpay.util.WebUtils;
import com.wosai.pantheon.util.MapUtil;

public class AlipayApiClient {
    private static final Logger logger = LoggerFactory.getLogger(AlipayApiClient.class);
    protected static final ObjectMapper om = new ObjectMapper();

    protected int connectTimeout = 3000;
    protected int readTimeout = 15000;
    protected String signType = AlipayConstants.SIGN_TYPE_RSA;
    protected String charset = AlipayConstants.CHARSET_UTF8;
    protected String gatewayUrl = AlipayV2Config.GATEWAY;
    
    static {
        //清除安全设置
        Security.setProperty("jdk.certpath.disabledAlgorithms", "");
    }
    
    public AlipayApiClient() {
        this(AlipayConstants.CHARSET_UTF8);
    }
    public AlipayApiClient(String charset) {
        this(charset, AlipayConstants.SIGN_TYPE_RSA2);
    }
    public AlipayApiClient(String charset, String signType) {
        this.charset = charset;
        this.signType = signType;
    }

    public Map<String, Object> call(String gatewayUrl, String signType, String privateKey, Map<String, String> request) throws MpayException, MpayApiNetworkError {
        request.remove(ProtocolV2Fields.SIGN);

        for(Object mapKey: request.keySet().toArray()){
            if(request.get(mapKey) == null || "".equals(request.get(mapKey))){
                request.remove(mapKey);
            }
        }
        String sign;
        if (AlipayConstants.SIGN_TYPE_RSA2.equals(signType)){
             sign = AlipaySignature.rsa256Sign(request, privateKey, charset);
        }else{
             sign = AlipaySignature.rsaSign(request, privateKey, charset);
        }
        
        request.put(ProtocolV2Fields.SIGN, sign);
        logger.debug("request {}", request);
        try {
            String resp = WebUtils.doPost(null, null, gatewayUrl, request, charset, connectTimeout, readTimeout);
            Map<String, Object> result = om.readValue(resp, new TypeReference<Map<String,Object>>() {});
            Map<String, Object> response = (Map<String, Object>) MapUtil.getObject(result, "response");
            if (response == null) {
                throw new AlipayV2Exception("99999", "invalid alipay response", null, null);
            }
            logger.debug("response {}", resp);
            return response;
        }catch(IOException e) {
            throw new AlipayV2Exception("99999", "IO error invoking alipay api", null, null, e);
        }
        
    }

}
