package com.wosai.mpay.api.alipay;

public class BusinessV1Fields {
    public static final String OUT_TRADE_NO = "out_trade_no";
    public static final String OUT_REQUEST_NO = "out_request_no";
    public static final String SUBJECT = "subject";
    public static final String BODY = "body";
    public static final String PRODUCT_CODE = "product_code";
    public static final String TOTAL_FEE = "total_fee";
    public static final String REFUND_AMOUNT = "refund_amount";
    public static final String DYNAMIC_ID_TYPE = "dynamic_id_type";
    public static final String DYNAMIC_ID = "dynamic_id";
    public static final String IT_B_PAY = "it_b_pay";
    public static final String ROYALTY_TYPE = "royalty_type";
    public static final String ROYALTY_PARAMETERS = "royalty_parameters";
    public static final String EXTEND_PARAMS = "extend_params";
    public static final String SELLER_ID = "seller_id";
    public static final String PAYMENT_TYPE = "payment_type";
    
    
    public static final String ROYALTY_SERIAL_NO = "serialNo";
    public static final String ROYALTY_TRANS_IN = "transIn";
    public static final String ROYALTY_TRANS_OUT = "transOut";
    public static final String ROYALTY_AMOUNT = "amount";
    
    public static final String EX_AGENT_ID = "AGENT_ID";
    public static final String EX_STORE_TYPE = "STORE_TYPE";
    public static final String EX_STORE_ID = "STORE_ID";
    public static final String EX_TERMINAL_ID = "TERMINAL_ID";
    
}
