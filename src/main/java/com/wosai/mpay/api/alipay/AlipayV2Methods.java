package com.wosai.mpay.api.alipay;

public class AlipayV2Methods {
    
    /**
     * 单发消息 
     */
    public static final String ALIPAY_MOBILE_PUBLIC_MESSAGE_CUSTOM_SEND = "alipay.mobile.public.message.custom.send";
    /**
     * 群发消息 
     */
    public static final String ALIPAY_MOBILE_PUBLIC_MESSAGE_TOTAL_SEND = "alipay.mobile.public.message.total.send";
    /**
     * 单发模版消息
     */
    public static final String ALIPAY_MOBILE_PUBLIC_MESSAGE_SINGLE_SEND = "alipay.mobile.public.message.single.send";
    /**
     * 创建菜单
     */
    public static final String ALIPAY_MOBILE_PUBLIC_MENU_ADD= "alipay.mobile.public.menu.add";
    /**
     * 更新菜单
     */
    public static final String ALIPAY_MOBILE_PUBLIC_MENU_UPDATE = "alipay.mobile.public.menu.update";
    /**
     * 查询菜单
     */
    public static final String ALIPAY_MOBILE_PUBLIC_MENU_GET = "alipay.mobile.public.menu.get";

    /**
     * 根据用户付款码获取用户信息
     */
    public static final String ALIPAY_MOBILE_SHAKE_USER_QUERY="alipay.mobile.shake.user.query";
    
    /**
     * 条码支付（B扫C）
     */
    public static final String ALIPAY_TRADE_TRADE = "alipay.trade.pay";
    /**
     *
     */
    public static final String ALIPAY_TRADE_CREATE = "alipay.trade.create";
    /**
     * 预下单（C扫B）
     */
    public static final String ALIPAY_TRADE_PRECREATE = "alipay.trade.precreate";
    /**
     * 查询订单
     */
    public static final String ALIPAY_TRADE_QUERY = "alipay.trade.query";
    /**
     * 撤销订单
     */
    public static final String ALIPAY_TRADE_CANCEL = "alipay.trade.cancel";
    /**
     * 关闭订单
     */
    public static final String ALIPAY_TRADE_CLOSE = "alipay.trade.close";
    /**
     * 申请退款
     */
    public static final String ALIPAY_TRADE_REFUND = "alipay.trade.refund";
    /**
     * 查询退款订单
     */
    public static final String ALIPAY_TRADE_FASTPAY_REFUND_QUERY = "alipay.trade.fastpay.refund.query";
    /**
     *h5支付
     */
    public static final String ALIPAY_TRADE_WAP_PAY = "alipay.trade.wap.pay";

    /**
     * app支付
     */
    public static final String ALIPAY_TRADE_APP_PAY = "alipay.trade.app.pay";
    
    /**
     * 资金授权冻结（b2c）
     */
    public static final String ALIPAY_FUND_AUTH_ORDER_FREEZE = "alipay.fund.auth.order.freeze";
    
    /**
     * 线上资金授权冻结（小程序、app、h5）
     */
    public static final String ALIPAY_FUND_AUTH_ORDER_APP_FREEZE = "alipay.fund.auth.order.app.freeze";
    
    /**
     * 资金授权操作查询接口 
     */
    public static final String ALIPAY_FUND_ORDER_QUERY = "alipay.fund.auth.operation.detail.query";
    
    /**
     * 资金授权操作撤单接口 
     */
    public static final String ALIPAY_FUND_ORDER_CANCEL = "alipay.fund.auth.operation.cancel";
    
    /**
     * 资金授权操作解冻接口 
     */
    public static final String ALIPAY_FUND_ORDER_UNFREEZE = "alipay.fund.auth.order.unfreeze";


    /**
     * 分账关系绑定
     */
    public static final String ALIPAY_TRADE_ROYALTY_RELATION_BIND = "alipay.trade.royalty.relation.bind";

    /**
     * 分账关系解绑
     */
    public static final String ALIPAY_TRADE_ROYALTY_RELATION_UNBIND = "alipay.trade.royalty.relation.unbind";
    /**
     * 分账关系查询
     */
    public static final String ALIPAY_TRADE_ROYALTY_RELATION_BATCH_QUERY = "alipay.trade.royalty.relation.batchquery";

    /**
     * 统一收单交易结算接口
     */
    public static final String ALIPAY_TRADE_ORDER_SETTLE = "alipay.trade.order.settle";


    /**
     * 新版花呗分期商家贴息活动创建接口
     */
    public static final String ALIPAY_PCREDIT_HUABEI_MERCHANT_ACTIVITY_CREATE = "alipay.pcredit.huabei.merchant.activity.create";

    /**
     * 新版花呗分期商家贴息活动修改接口
     */
    public static final String ALIPAY_PCREDIT_HUABEI_MERCHANT_ACTIVITY_MODIFY = "alipay.pcredit.huabei.merchant.activity.modify";

    /**
     * 新版花呗分期商家贴息活动发布接口
     */
    public static final String ALIPAY_PCREDIT_HUABEI_MERCHANT_ACTIVITY_ONLINE = "alipay.pcredit.huabei.merchant.activity.online";

    /**
     * 新版花呗分期商家贴息活动下线接口
     */
    public static final String ALIPAY_PCREDIT_HUABEI_MERCHANT_ACTIVITY_OFFLINE = "alipay.pcredit.huabei.merchant.activity.offline";
    
    /**
     * 花呗分期商户贴息优惠账单查询
     */
    public static final String ALIPAY_PCREDIT_HUABEI_MERCHANT_BILL_QUERY = "alipay.pcredit.huabei.merchant.bill.query";
    
    /**
     * 交易同步接口
     */
    public static final String ALIPAY_TRADE_ORDERINFO_SYNC = "alipay.trade.orderinfo.sync";

    /**
     * 花呗分期吱口令码申领
     */
    public static final String ALIPAY_PCREDIT_HUABEI_SHARDCODE_HBFQ_CREATE = "alipay.pcredit.huabei.sharecode.hbfq.create";

    /**
     * 花呗分期码申请
     */
    public static final String ALIPAY_PCREDIT_HUABEI_FQQRCODE_HBFQ_CREATE = "alipay.pcredit.huabei.fqqrcode.hbfq.create";

    /**
     * 芝麻先享 信用服务开通/授权
     */
    public static final String ALIPAY_ZHIMA_CREDIT_PAYAFTERUSE_CREDITAGREEMENT_SIGN = "zhima.credit.payafteruse.creditagreement.sign";

    /**
     * 芝麻先享 查询服务开通/授权信息
     */
    public static final String ALIPAY_ZHIMA_CREDIT_PAYAFTERUSE_CREDITAGREEMENT_QUERY = "zhima.credit.payafteruse.creditagreement.query";


    /**
     * 芝麻先享信用服务下单（免用户确认场景）
     */
    public static final String ALIPAY_ZHIMA_CREDIT_PAYAFTERUSE_CREDITBIZORDER_ORDER = "zhima.credit.payafteruse.creditbizorder.order";

    /**
     * 芝麻先享信用服务下单（用户确认场景）
     */
    public static final String ALIPAY_ZHIMA_CREDIT_PAYAFTERUSE_CREDITBIZORDER_CREATE = "zhima.credit.payafteruse.creditbizorder.create";

    /**
     * 信用服务订单查询
     */
    public static final String ALIPAY_ZHIMA_CREDIT_PAYAFTERUSE_CREDITBIZORDER_QUERY = "zhima.credit.payafteruse.creditbizorder.query";

    /**
     * 结束信用服务订单
     */
    public static final String ALIPAY_ZHIMA_CREDIT_PAYAFTERUSE_CREDITBIZORDER_FINISH = "zhima.credit.payafteruse.creditbizorder.finish";

    /**
     * 获取营销文案
     */
    public static final String ALIPAY_FLOW_MARKETING_CONSULT = "alipay.pay.app.marketing.consult";

    /**
     * 花呗分期单通道前置发奖
     */
    public static final String ALIPAY_HB_FQ_AWARD_RECEIVE = "alipay.pcredit.huabei.fq.award.receive";
}
