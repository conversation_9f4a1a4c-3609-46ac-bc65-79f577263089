package com.wosai.mpay.api.alipay.marketing;

public abstract class ProtocolV3Fields {
    public static final String APP_ID = "app_id";
    public static final String APP_CERT_SN = "app_cert_sn";
    public static final String HTTP_METHOD = "httpMethod";

    public static final String NONCE = "nonce";
    public static final String AUTHORIZATION = "authorization";
    public static final String ALIPAY_REQUEST_ID = "alipay-request-id";
    public static final String ALIPAY_ENCRYPT_TYPE = "alipay-encrypt-type";
    public static final String TIMESTAMP = "timestamp";
    public static final String ALIPAY_ROOT_CERT_SN = "alipay-root-cert-sn";
    public static final String ALIPAY_APP_AUTH_TOKEN = "alipay-app-auth-token";

    public static final String HTTP_REQUEST_URL = "httpRequestUrl";
    public static final String HTTP_REQUEST_BODY = "httpRequestBody";
    public static final String APP_AUTH_TOKEN = "appAuthToken";
    public static final String AUTH_STRING = "authString";

    public static final String URI_CREATE_TOKEN = "/v3/alipay/merchant/indirect/sharetoken/create";
    public static final String ALIPAY_FLOW_MARKETING_CONSULT = "/v3/alipay/pay/app/marketing/consult";


}
