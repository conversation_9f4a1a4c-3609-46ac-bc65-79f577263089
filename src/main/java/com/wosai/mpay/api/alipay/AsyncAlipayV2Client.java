package com.wosai.mpay.api.alipay;

import com.wosai.mpay.api.HttpResourceCallback;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.AsyncClientUtil;
import com.wosai.mpay.util.TracingUtil;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.message.BasicNameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/9/9.
 */
public class AsyncAlipayV2Client {

    public static final Logger logger = LoggerFactory.getLogger(AsyncAlipayV2Client.class);
    protected int connectTimeout = 2000;
    protected int readTimeout = 5000;

    private RequestConfig requestConfig;
    private CloseableHttpAsyncClient client;

    public AsyncAlipayV2Client() {
        initClient();
    }

    public AsyncAlipayV2Client(int readTimeout, int connectTimeout){
        this.readTimeout = readTimeout;
        this.connectTimeout = connectTimeout;
        initClient();
    }

    public void initClient(){
        requestConfig = RequestConfig.custom().setConnectionRequestTimeout(connectTimeout).setConnectTimeout(connectTimeout).setSocketTimeout(readTimeout).build();
        client = AsyncClientUtil.getCloseableHttpAsyncClient(null, null);
    }

    public void call(String gatewayUrl, String signType, String privateKey, Map<String, String> request, HttpResourceCallback<Map<String, Object>> callback){
        try{
            doCall(gatewayUrl, signType, privateKey, request, callback);
        }catch (Throwable e){
            callback.onError(e);
        }
    }

    private void doCall(String gatewayUrl, String signType, String privateKey, Map<String, String> request, HttpResourceCallback<Map<String, Object>> callback) throws MpayException {
        AlipayV2NewClient.preProcess(signType, privateKey, request);
        AsyncClientUtil.logRequest(logger, request);
        HttpPost httpPost = new HttpPost(gatewayUrl);
        List<NameValuePair> params = new ArrayList<>();
        for(String key: request.keySet()){
            params.add(new BasicNameValuePair(key, request.get(key)));
        }
        try {
            httpPost.setEntity(new UrlEncodedFormEntity(params, AlipayConstants.CHARSET_UTF8));
        } catch (UnsupportedEncodingException e) {
        }
        httpPost.setConfig(requestConfig);
        long start = System.currentTimeMillis();
        client.execute(httpPost, AsyncClientUtil.getFutureCallback(logger, AlipayConstants.CHARSET_UTF8, AsyncClientUtil.ResponseType.STRING, HttpResourceCallback.<String>create(
                TracingUtil.getTraceCarrierItem(),
                (response, t) -> {
                    AsyncClientUtil.logResponse(logger, gatewayUrl, System.currentTimeMillis() - start, response, t);
                    if(t != null){
                        callback.onError(t);
                        return;
                    }else{
                        Map<String,Object> result = null;
                        try {
                            result = AlipayV2NewClient.postProcess(response);
                        } catch (MpayApiUnknownResponse e) {
                            callback.onError(e);
                            return;
                        }
                        callback.onComplete(result);
                        return;
                    }
                }
        )));
    }

}
