package com.wosai.mpay.api.alipay.marketing;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.UUIDGenerator;
import com.wosai.pantheon.util.StringUtil;

import java.util.*;


public class RequestV3Builder {
    private static ObjectMapper om = new ObjectMapper();
    Map<String, Object> bizRequest;
    Map<String, String> commonRequest;

    private RequestV3Builder() {
    }

    public RequestV3Builder(String appId, String appCertSn, String httpMethod, String httpRequestUrl, String appAuthToken) {
        bizRequest = new HashMap<>();
        commonRequest = new HashMap<>();
        commonRequest.put(ProtocolV3Fields.APP_ID, appId);
        commonRequest.put(ProtocolV3Fields.HTTP_METHOD, httpMethod);
        commonRequest.put(ProtocolV3Fields.HTTP_REQUEST_URL, httpRequestUrl);
        if (StringUtil.isNotEmpty(appCertSn)) {
            commonRequest.put(ProtocolV3Fields.APP_CERT_SN, appCertSn);
        }
        if (StringUtil.isNotEmpty(appAuthToken)) {
            commonRequest.put(ProtocolV3Fields.APP_AUTH_TOKEN, appAuthToken);
        }
    }

    public String getContent() {
        StringBuilder authStringBuilder = new StringBuilder();
        authStringBuilder.append(AlipayConstants.APP_ID).append("=").append(commonRequest.get(ProtocolV3Fields.APP_ID)).append(",");

        if (commonRequest.containsKey(ProtocolV3Fields.APP_CERT_SN)) {
            authStringBuilder.append(ProtocolV3Fields.APP_CERT_SN).append("=").append(commonRequest.get(ProtocolV3Fields.APP_CERT_SN));
        }
        authStringBuilder.append(ProtocolV3Fields.NONCE).append("=").append(UUIDGenerator.getUUID()).append(",")
                .append(AlipayConstants.TIMESTAMP).append("=").append(System.currentTimeMillis());
        commonRequest.put(ProtocolV3Fields.AUTH_STRING, authStringBuilder.toString());

        StringBuilder contentBuilder = new StringBuilder();
        contentBuilder.append(authStringBuilder.toString()).append("\n");
        contentBuilder.append(commonRequest.get(ProtocolV3Fields.HTTP_METHOD)).append("\n");
        contentBuilder.append(commonRequest.get(ProtocolV3Fields.HTTP_REQUEST_URL)).append("\n");
        String httpRequestBody = JsonUtil.toJsonStr(bizRequest);
        if (StringUtil.isNotEmpty(httpRequestBody)) {
            contentBuilder.append(httpRequestBody).append("\n");
        }
        if (commonRequest.containsKey(ProtocolV3Fields.APP_AUTH_TOKEN)) {
            contentBuilder.append(commonRequest.get(ProtocolV3Fields.APP_AUTH_TOKEN)).append("\n");
        }
        return contentBuilder.toString();
    }

    public Map<String, Object> getBizRequest() {
        return bizRequest;
    }

    public void setBizRequest(Map<String, Object> bizRequest) {
        this.bizRequest = bizRequest;
    }

    public void putBiz(String key, Object value) {
        this.bizRequest.put(key, value);
    }
}
