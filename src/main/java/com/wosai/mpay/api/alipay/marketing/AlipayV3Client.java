package com.wosai.mpay.api.alipay.marketing;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.api.alipay.*;
import com.wosai.mpay.exception.*;
import com.wosai.mpay.util.*;
import com.wosai.pantheon.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.security.Security;
import java.util.HashMap;
import java.util.Map;

public class AlipayV3Client {
    private static final Logger logger = LoggerFactory.getLogger(AlipayV3Client.class);

    private String signType = AlipayConstants.SIGN_RSA2_ALGORITHMS;
    private String charset = AlipayConstants.CHARSET_UTF8;
    private String cType = "application/json";
    private int connectTimeout = 3000;
    private int readTimeout = 15000;
    private String gatewayUrl = AlipayV1Config.GATEWAY;
    private static final ObjectMapper om = new ObjectMapper();

    static {
        //清除安全设置
        Security.setProperty("jdk.certpath.disabledAlgorithms", "");
    }

    public AlipayV3Client() {
        this(AlipayConstants.CHARSET_UTF8);
    }

    public AlipayV3Client(String charset) {
        this.charset = charset;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }


    private String getSign(RequestV3Builder requestV3Builder, String privateKey) throws MpayException {
        return AlipaySignature.rsa256Sign(requestV3Builder.getContent(), privateKey, charset);
    }

    private String getAuthorization(RequestV3Builder requestV3Builder, String sign) {
        //authorization: ${签名算法} ${authString},sign=${signature}
        return new StringBuilder().append("ALIPAY-SHA256withRSA").append(" ")
                .append(requestV3Builder.commonRequest.get(ProtocolV3Fields.AUTH_STRING)).append(",sign=").append(sign).toString();
    }

    public Map<String, Object> callPost(String gatewayUrl, String privateKey, RequestV3Builder requestV3Builder) throws MpayException, MpayApiNetworkError, JsonProcessingException {
        String sign = getSign(requestV3Builder, privateKey);
        String requestId = UUIDGenerator.getUUID();
        String authorization = getAuthorization(requestV3Builder, sign);
        logger.debug("request {} | requestId {}", JsonUtil.toJsonStr(requestV3Builder.getBizRequest()), requestId);
        Map<String, String> header = new HashMap<>();
        header.put(ProtocolV3Fields.AUTHORIZATION, authorization);
        header.put(ProtocolV3Fields.ALIPAY_REQUEST_ID, requestId);

        String postParams = om.writeValueAsString(requestV3Builder.bizRequest);
        //使用 "application/json" contentType发起请求
        String resp = HttpClientUtils.doPost(AlipayV3Client.class.getName(), null, null, gatewayUrl, cType, postParams, header, charset, connectTimeout, readTimeout);
        logger.debug("response {} | requestId {}", resp, requestId);
        return postProcess(resp);
    }

    public static Map<String, Object> postProcess(String response) throws MpayApiUnknownResponse {
        Map<String, Object> result = null;
        try {
            result = om.readValue(response, new TypeReference<Map<String, Object>>() {
            });
        } catch (IOException e) {
            throw new MpayApiUnknownResponse("invalid alipay response", e);
        }
        return result;
    }

    public static void main(String[] args) {
        test();
//        test2();
    }

    public static void test() {
        AlipayV3Client alipayV3Client = new AlipayV3Client();
        RequestV3Builder requestV3Builder = new RequestV3Builder("2021004122672578", null, "POST", "/v3/alipay/pay/app/marketing/consult", null);
        requestV3Builder.putBiz("biz_scene", "ALIPAY_PROMO");
        requestV3Builder.putBiz("total_amount", "188.88");
//        requestV3Builder.putBiz("out_trade_no","test");
        requestV3Builder.putBiz("ext_params", MapUtil.hashMap("outOpenId", "ou0423hdosu237",
                "secondaryMerchantId", "2088920310612312", "appSource", "wechat"));

        String privateKey = "";
        Map<String, Object> stringObjectMap = null;
        try {
            stringObjectMap = alipayV3Client.callPost("https://openapipre.alipay.com/v3/alipay/pay/app/marketing/consult", privateKey, requestV3Builder);
        } catch (MpayException e) {
            e.printStackTrace();
        } catch (MpayApiNetworkError mpayApiNetworkError) {
            mpayApiNetworkError.printStackTrace();
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        System.out.println(stringObjectMap);
    }

    public static void test2() {
        AlipayV3Client alipayV3Client = new AlipayV3Client();
        RequestV3Builder requestV3Builder = new RequestV3Builder("2021004122672578", null, "POST", "/v3/alipay/merchant/indirect/sharetoken/create", null);
        requestV3Builder.putBiz("biz_scene", "ISV_PAY_PAGE");
        requestV3Builder.putBiz("out_open_id", "ou0423hdosu237");
        requestV3Builder.putBiz("out_biz_no", "test111");
        requestV3Builder.putBiz("pay_amount", "88.88");
        requestV3Builder.putBiz("sub_merchant_id", "2088610441986366");
        requestV3Builder.putBiz("merchant_name", "上海李婉玉演示店");
        requestV3Builder.putBiz("biz_link", "https://qr-wap-pay.iwosai.com/20101009793000018693?preStoreInfoKey=ztppc9geiPZlNOh1iaIsieshgiPj9lwL");


        String privateKey = "";
        Map<String, Object> stringObjectMap = null;
        try {
            stringObjectMap = alipayV3Client.callPost("https://openapi.alipay.com/v3/alipay/merchant/indirect/sharetoken/create", privateKey, requestV3Builder);
        } catch (MpayException e) {
            e.printStackTrace();
        } catch (MpayApiNetworkError mpayApiNetworkError) {
            mpayApiNetworkError.printStackTrace();
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        System.out.println(stringObjectMap);
    }

}
