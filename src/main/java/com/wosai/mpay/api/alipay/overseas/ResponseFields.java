package com.wosai.mpay.api.alipay.overseas;

public class ResponseFields {
    public static final String IS_SUCCESS = "is_success";                   // 'T' indicates that a request is rejected by Alipay gateway. 'F' for rejected
    public static final String SIGN = "sign";                               //Signature value
    public static final String SIGN_TYPE = "sign_type";                     //The type could be DSA, RSA or MD5; Upper case only.
    public static final String RESULT_CODE = "result_code";                 //To describe the response status of a request:SUCCESS, FAIL/FAILED, UNKNOW.
    public static final String ERROR = "error";                             //To describe the reason of the result_code when it is FAIL/FAILED UNKNOW, leave it blank when result_code is SUCCESS/ UNKNOW.
    
    public static final String ALIPAY = "alipay";
    public static final String REQUEST = "request";
    public static final String RESPONSE = "response";
    
    //pay & query & refund interface common return
    public static final String ALIPAY_BUYER_LOGIN_ID = "alipay_buyer_login_id";     //The buyer’s Alipay login Id, the id might be an email or mobile number. The id is partially masked for privacy.
    public static final String ALIPAY_BUYER_USER_ID = "alipay_buyer_user_id";       //This ID stands for each Alipay account number ,unique in Alipay system start with “2088”
    public static final String PARTNER_TRANS_ID = "partner_trans_id";               //Equal to the partner_trans_id given in the request
    public static final String ALIPAY_TRANS_ID = "alipay_trans_id";                 //On the partner’s payment request, the Alipay system creates a transaction id to handle it. The alipay_trans_id has one-one association with partner + partner_trans_id.
    public static final String ALIPAY_PAY_TIME = "alipay_pay_time";                 //The time of the transaction has been paid. Format：YYYYMMDDHHMMSS
    public static final String CURRENCY = "currency";                               //The currency used for labelling the price of the transaction;
    public static final String TRANS_AMOUNT = "trans_amount";                       //he transaction amount in the currency given above; Range: 0.01-*********.00. Two digits after decimal point.
    public static final String EXCHANGE_RATE = "exchange_rate";                     //The rate of conversion of the currency given in the request to CNY. The conversion happens at the time when Alipay’s trade order is created.
    public static final String TRANS_AMOUNT_CNY = "trans_amount_cny";               //transaction amount in CNY. It is the exact amount that the buyer has paid.
    public static final String M_DISCOUNT_FOREX_AMOUNT = "m_discount_forex_amount"; //If coupons/vouchers are used in the transaction, the discount amount redeened in the settlement currency will be returned. Otherwise, no return.
    public static final String PAYMENT_INST = "payment_inst";                       //This flag identify the wallet type. If the “result_code” is UNKNOW, there is not such parameter. You should invoke “query” interface to get payment status and wallet type.
    
    //query interface return
    public static final String ALIPAY_TRANS_STATUS = "alipay_trans_status";         //WAIT_BUYER_PAY, TRADE_SUCCESS, TRADE_CLOSED
    
    //cancel interface return
    public static final String TRADE_NO = "trade_no";                               //Trade number of the trade in Alipay system 16 bits at least and 64 bits at most
    public static final String OUT_TRADE_NO = "out_trade_no";                       //Unique order ID in order system in corresponding merchant’s website
    public static final String ACTION = "action";                                   //The action of cancel. close: only closed the transaction, but no refund. refund: had a refund.Y
    public static final String RETRY_FLAG = "retry_flag";                           //Y: The cancel failed due to retriable error  N: The cancel failed due to non-retriable error
    public static final String DETAIL_ERROR_CODE = "detail_error_code";             //Give cause description to the response code returned. Please refer to “8.1 Business Error Code”. If the response code of result_code is SUCCESS, this parameter shall not be returned.
    public static final String DETAIL_ERROR_DES = "detail_error_des";               //Give literal statement as to the detailed error code. If the response code of result_code is SUCCESS, this parameter shall not be returned.
    
    //refund interface return
    public static final String REFUND_AMOUNT = "refund_amount";                     //Less than or equal to the original transaction amount and the left transaction amount if ever refunded.
    public static final String REFUND_AMOUNT_CNY = "refund_amount_cny";             //Refund amount in CNY. It is the exact amount that the Alipay has refunded.
    public static final String PARTNER_REFUND_ID = "partner_refund_id";             //The refund order id on partner system. partner_refund_id together with partner identify a refund transaction

    public static final Object RESPONSE_CODE = "response_code";
    public static final Object REFUND_RESULT_CODE = "refund_result_code";
    
}
