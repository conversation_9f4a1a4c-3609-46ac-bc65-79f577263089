package com.wosai.mpay.api.alipay.intl;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wosai.mpay.exception.AlipayV2Exception;
import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.RsaSignature;

public class AlipayIntlClient {
    public static final Logger logger = LoggerFactory.getLogger(AlipayIntlClient.class);
    private int connectTimeout = 3000;
    private int readTimeout = 15000;
    private String charset = AlipayIntlConstants.DEFAULT_INPUT_CHARSET;
    private String DEFAULT_CONTENT_TYPE = "application/json";
    
    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }
    
    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
    
    public void setCharset(String charset) {
        this.charset = charset;
    }
    
    public Map<String,Object> call(String gatewayUrl, String privateKey, Map<String, Object> request) throws MpayException, MpayApiNetworkError {
        request.remove(ProtocolFields.SIGNATURE);
        request.put(ProtocolFields.SIGNATURE, getSign(JsonUtil.toJsonStr(request.get(BusinessFields.REQUEST)), privateKey,charset));
        String requestStr = JsonUtil.toJsonStr(request);
        
        logger.debug("request {}", requestStr);
        try {
            String resp = HttpClientUtils.doPost(AlipayIntlClient.class.getName(), null, null, gatewayUrl, DEFAULT_CONTENT_TYPE, requestStr, charset, connectTimeout, readTimeout);
            logger.debug("response {}",resp.replaceAll("\\n", ""));
            Map<String, Object> response = JsonUtil.jsonStrToObject(resp, Map.class);
            if (response == null) {
                throw new AlipayV2Exception("99999", "invalid alipay response", null, null);
            }
            return (Map<String, Object>) response.get(ResponseFields.RESPONSE);
        } catch (IOException e) {
            throw new AlipayV2Exception("99999", "IO error invoking alipay api", null, null, e);
        }
    }
    
    public static String getSign(String content, String key,  String charset) throws MpayException {
        return RsaSignature.sign(content, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, key, charset);
    }

    
    public static void main(String[] args) throws BuilderException, MpayException, MpayApiNetworkError {
    	System.out.println("pay");
        testPay();
//        System.out.println("query");
//        testQuery();
//        System.out.println("cancel");
//        testCancel();
//        System.out.println("query");
//        testQuery();
//        System.out.println("refund");
//        testRefund();
    }


    public static void testPay() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayIntlClient client = new AlipayIntlClient();
        RequestBuilder builder = new RequestBuilder();
        builder.headSet(BusinessFields.VERSION, AlipayIntlConstants.DEFAULT_VERSION);
        builder.headSet(BusinessFields.FUNCTION, AlipayIntlConstants.SERVICE_NAME_PAY_BARCODE);
        builder.headSet(BusinessFields.CLINET_ID, AlipayIntlConstants.CLIENT_ID);
        builder.headSet(BusinessFields.REQ_TIME, getReqeustTime(System.currentTimeMillis(), AlipayIntlConstants.DATE_TIME_FORMAT));
        builder.headSet(BusinessFields.REQ_MSG_ID, "shouqianba-" + UUID.randomUUID().toString().replaceAll("-", ""));
        builder.headSet("reserve", "{}");
        
        builder.bodySet(BusinessFields.MERCHANT_ID, AlipayIntlConstants.MERCHANT_ID);
        builder.bodySet(BusinessFields.PRODUCT_CODE, AlipayIntlConstants.PRODUCT_CODE_OFFLINE_PAY);
        builder.bodySet(BusinessFields.MERCHANT_TRANS_ID, "7894259245224096");
        builder.bodySet(BusinessFields.CODE_TYPE, AlipayIntlConstants.CODE_TYPE_BARCODE);
        builder.bodySet(BusinessFields.IDENTITY_CODE, "288455877868145526");
        builder.bodySet(BusinessFields.ORDER_INFO, new HashMap<String, Object>(){{
        	put(BusinessFields.ORDER_AMOUNT, new LinkedHashMap(){{
        		put(BusinessFields.CURRENCY, "USD");
        		put(BusinessFields.VALUE, "100");
        	}});
        	put(BusinessFields.SELLER, new LinkedHashMap(){{
        		put(BusinessFields.SELLER_ID, "208820000000000000023");
        		put(BusinessFields.SELLER_NAME, "Test");
        		put("mcc", "3450");
        	}});
        	put(BusinessFields.ORDER_TITLE, "wosai");
        	put("orderDetail","456");
        	
        }});
        builder.bodySet("extendInfo", new HashMap<String, Object>(){{
        	put("storeId", "123");
        	put("storeName","Alibaba");
        }});
        Map<String, Object> request1 = builder.build();
        System.out.println(JsonUtil.toJsonStr(request1));
        String req = "{\"request\":{\"head\":{\"version\":\"2.0.0\",\"function\":\"alipay.intl.acquiring.offline.pay\",\"clientId\":\"Test_ClientId_0011\",\"reqTime\":\"2018-12-08T18:09:25+08:00\",\"reqMsgId\":\"shouqianba-573b4109fad111e89ea893d635232b33\",\"reserve\":\"{}\"},\"body\":{\"merchantId\":\"Test_ClientId_0011\",\"productCode\":\"OFFLINE_PAY\",\"merchantTransId\":\"7894259245224096\",\"codeType\":\"BARCODE\",\"identityCode\":\"288455877868145526\",\"order\":{\"seller\":{\"sellerId\":\"21680002795638\",\"sellerName\":\"ss\",\"mcc\":\"3450\"},\"orderAmount\":{\"currency\":\"USD\",\"value\":\"1\"},\"orderDetail\":\"mock\",\"orderTitle\":\"mock\"},\"extendInfo\":{\"storeName\":\"Alibaba\",\"storeId\":\"123\"}}}}";
        System.out.println(req);
        Map<String,Object> request = JsonUtil.jsonStrToObject(req, Map.class);
        Map<String,Object> result = client.call("https://isupergw.alipaydev.com/api/alipay/intl/acquiring/offline/pay.htm", AlipayIntlConstants.RSA_KEY, request);
        System.out.println(result);
    }
    
    public static void testCancel() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayIntlClient client = new AlipayIntlClient();
        RequestBuilder builder = new RequestBuilder();
        builder.headSet(BusinessFields.VERSION, AlipayIntlConstants.DEFAULT_VERSION);
        builder.headSet(BusinessFields.FUNCTION, AlipayIntlConstants.SERVICE_NAME_CANCEL);
        builder.headSet(BusinessFields.CLINET_ID, AlipayIntlConstants.CLIENT_ID);
        builder.headSet(BusinessFields.REQ_TIME, getReqeustTime(System.currentTimeMillis(), AlipayIntlConstants.DATE_TIME_FORMAT));
        builder.headSet(BusinessFields.REQ_MSG_ID, "shouqianba-" + UUID.randomUUID().toString().replaceAll("-", ""));
        
        builder.bodySet(BusinessFields.MERCHANT_ID, AlipayIntlConstants.MERCHANT_ID);
        builder.bodySet(BusinessFields.MERCHANT_TRANS_ID, "7893259243524571231");
        
        Map<String, Object> request = builder.build();
        Map<String,Object> result = client.call("https://isupergw.alipaydev.com/api/alipay/intl/acquiring/common/paycancel.htm", AlipayIntlConstants.RSA_KEY, request);
        System.out.println(result);
    }
    
    public static void testQuery() throws BuilderException, MpayException, MpayApiNetworkError {
	    TimeZone tz = TimeZone.getTimeZone("UTC");
        AlipayIntlClient client = new AlipayIntlClient();
        RequestBuilder builder = new RequestBuilder();
        builder.headSet(BusinessFields.VERSION, AlipayIntlConstants.DEFAULT_VERSION);
        builder.headSet(BusinessFields.FUNCTION, AlipayIntlConstants.SERVICE_NAME_QUERY);
        builder.headSet(BusinessFields.CLINET_ID, AlipayIntlConstants.CLIENT_ID);
        builder.headSet(BusinessFields.REQ_TIME, getReqeustTime(System.currentTimeMillis(), AlipayIntlConstants.DATE_TIME_FORMAT));
        builder.headSet(BusinessFields.REQ_MSG_ID, "shouqianba-" + UUID.randomUUID().toString().replaceAll("-", ""));
        
        builder.bodySet(BusinessFields.MERCHANT_ID, AlipayIntlConstants.MERCHANT_ID);
        builder.bodySet(BusinessFields.MERCHANT_TRANS_ID, "7893259243521072");
        
        Map<String, Object> request = builder.build();
        Map<String,Object> result = client.call("https://isupergw.alipaydev.com/api/alipay/intl/acquiring/offline/payquery.htm", AlipayIntlConstants.RSA_KEY, request);
        System.out.println(result);
    }
    
    public static void testRefund() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayIntlClient client = new AlipayIntlClient();
        RequestBuilder builder = new RequestBuilder();
        builder.headSet(BusinessFields.VERSION, AlipayIntlConstants.DEFAULT_VERSION);
        builder.headSet(BusinessFields.FUNCTION, AlipayIntlConstants.SERVICE_NAME_REFUND);
        builder.headSet(BusinessFields.CLINET_ID, AlipayIntlConstants.CLIENT_ID);
        builder.headSet(BusinessFields.REQ_TIME, getReqeustTime(System.currentTimeMillis(), AlipayIntlConstants.DATE_TIME_FORMAT));
        builder.headSet(BusinessFields.REQ_MSG_ID, "shouqianba-" + UUID.randomUUID().toString().replaceAll("-", ""));
        
        builder.bodySet(BusinessFields.MERCHANT_ID, AlipayIntlConstants.MERCHANT_ID);
        builder.bodySet(BusinessFields.ACQUIREMENT_ID, "2018111522001461530500834624");
        builder.bodySet(BusinessFields.MERCHANT_REFUND_ID, "7893259243524572");
        builder.bodySet(BusinessFields.REFUND_AMOUNT, new HashMap(){{
        	put(BusinessFields.VALUE, "1");
        	put(BusinessFields.CURRENCY, "USD");
        }});
        
        Map<String, Object> request = builder.build();
        Map<String,Object> result = client.call("https://isupergw.alipaydev.com/api/alipay/intl/acquiring/common/grefund.htm", AlipayIntlConstants.RSA_KEY, request);
        System.out.println(result);
    }

    public static String getReqeustTime(long time, String pattern){
        return new SimpleDateFormat(pattern).format(new Date(time));
    }
}
