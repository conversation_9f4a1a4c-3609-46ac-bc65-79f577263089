package com.wosai.mpay.api.alipay.overseas;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.BuilderException;

import java.util.*;

public class RequestBuilder {
    private static ObjectMapper om = new ObjectMapper();
    Map<String, String> request;
    Map<String, Object> extendInfo;
    Map<String, Object> bizData;
    Map<String, Object> extendParams;
    
    public RequestBuilder(){
        request = new TreeMap<String, String>();
        extendInfo = new LinkedHashMap<String, Object>();
        bizData = new LinkedHashMap<String, Object>();
        extendParams = new LinkedHashMap<String, Object>();
    }
    
    public Map<String, String> build() throws BuilderException {
        if (!request.containsKey(ProtocolFields.SERVICE)) {
            throw new BuilderException();
        }
        
        if (!extendInfo.isEmpty()) {
            try {
                request.put(BusinessFields.EXTEND_INFO, om.writeValueAsString(extendInfo));
            }
            catch (JsonProcessingException e) {
                throw new BuilderException("unable to serialize bizContent in JSON format.", e);
            }
        }
        if (!bizData.isEmpty()) {
            try {
                request.put(BusinessFields.BIZ_DATA, om.writeValueAsString(bizData));
            }
            catch (JsonProcessingException e) {
                throw new BuilderException("unable to serialize bizContent in JSON format.", e);
            }
        }
        if (!extendParams.isEmpty()) {
            try {
                request.put(BusinessFields.EXTEND_PARAMS, om.writeValueAsString(extendParams));
            }
            catch (JsonProcessingException e) {
                throw new BuilderException("unable to serialize bizContent in JSON format.", e);
            }
        }
        return request;
    }
    
    public void set(String field, String value) {
        request.put(field,  value);
    }
    
    public void extendInfoSet(String field, Object value) {
        extendInfo.put(field, value);
    }
    
    public void bizDataSet(String field, Object value) {
    	bizData.put(field, value);
    }
    
    public void extendParamsSet(String field, Object value) {
    	extendParams.put(field, value);
    }
}