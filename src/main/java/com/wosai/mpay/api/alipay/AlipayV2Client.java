package com.wosai.mpay.api.alipay;

import java.io.IOException;
import java.security.Security;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.AlipayV2Exception;
import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.AlipaySignature;
import com.wosai.mpay.util.WebUtils;

@Deprecated
/**
 * 用 AlipayV2NewClient 替代
 */
public class AlipayV2Client {
    private static final Logger logger = LoggerFactory.getLogger(AlipayV2Client.class);
    protected static final ObjectMapper om = new ObjectMapper();

    protected int connectTimeout = 3000;
    protected int readTimeout = 15000;
    protected String signType = AlipayConstants.SIGN_TYPE_RSA;
    protected String charset = AlipayConstants.CHARSET_UTF8;
    protected String gatewayUrl = AlipayV2Config.GATEWAY;
    
    static {
        //清除安全设置
        Security.setProperty("jdk.certpath.disabledAlgorithms", "");
    }
    
    public AlipayV2Client() {
        this(AlipayConstants.CHARSET_UTF8);
    }
    public AlipayV2Client(String charset) {
        this(charset, AlipayConstants.SIGN_TYPE_RSA);
    }
    public AlipayV2Client(String charset, String signType) {
        this.charset = charset;
        this.signType = signType;
    }

    public Map<String, Object> call(String gatewayUrl, String appId, String privateKey, Map<String, String> request) throws MpayException, MpayApiNetworkError {
        request.remove(ProtocolV2Fields.SIGN);
        request.put(ProtocolV2Fields.CHARSET, charset);
        String signType = getSignType(request);
        request.put(ProtocolV2Fields.SIGN_TYPE, signType);
        request.put(ProtocolV2Fields.APP_ID, appId);

        for(Object mapKey: request.keySet().toArray()){
            if(request.get(mapKey) == null || "".equals(request.get(mapKey))){
                request.remove(mapKey);
            }
        }
        String sign;
        if (AlipayConstants.SIGN_TYPE_RSA2.equals(signType)){
             sign = AlipaySignature.rsa256Sign(request, privateKey, charset);
        }else{
             sign = AlipaySignature.rsaSign(request, privateKey, charset);
        }
        
        request.put(ProtocolV2Fields.SIGN, sign);
        logger.debug("request {}", request);
        try {
            String resp = WebUtils.doPost(null, null, gatewayUrl, request, charset, connectTimeout, readTimeout);
            
            Map<String, Object> result = om.readValue(resp, new TypeReference<Map<String,Object>>() {});
            
            Map<String, Object> response = null;
            for (String key: result.keySet()) {
                if (key.endsWith(ProtocolV2Fields.RESP_SUFFIX)){
                    response = (Map<String,Object>)result.get(key);
                }
            }
            if (response == null) {
                throw new AlipayV2Exception("99999", "invalid alipay response", null, null);
            }
            logger.debug("response {}", resp);
            return response;
        }catch(IOException e) {
            throw new AlipayV2Exception("99999", "IO error invoking alipay api", null, null, e);
        }
        
    }


    protected String getSignType (Map request){
        String signType ;
        if(request.get(AlipayConstants.SIGN_TYPE) == null || "".equals(request.get(AlipayConstants.SIGN_TYPE))){
            signType = this.signType;
        }else{
            signType = AlipayConstants.SIGN_TYPE_RSA2.equals( (String) request.get(AlipayConstants.SIGN_TYPE)) ? AlipayConstants.SIGN_TYPE_RSA2 : this.signType;
        }
        return  signType;
    }

    public static void main(String[] args) throws MpayException, BuilderException, MpayApiNetworkError {
//        testPay();
        testQuery();
        testRefundQuery();
//        testCancel();
//        testRefund();
//        testPrecreate();
        
//        testDepositCancel();
        testDepositQuery();
    }
    
    private static void testDepositQuery() throws MpayApiNetworkError, MpayException, BuilderException {
        AlipayV2Client  client = new AlipayV2Client();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.APP_ID, "2015102000490218");
        builder.set(ProtocolV2Fields.CHARSET,AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, AlipayConstants.SIGN_TYPE_RSA);
        builder.bizSet(BusinessV2Fields.EXTEND_PARAMS, new HashMap(){{
            put(BusinessV2Fields.EXTEND_PARAMS_SYS_SERVICE_PROVIDER_ID, "2088011691288213");
        }});
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_FUND_ORDER_QUERY);
//        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN,"234234234");
        builder.bizSet(BusinessV2Fields.OUT_ORDER_NO,"7893259243055072");
        builder.bizSet(BusinessV2Fields.OUT_REQUEST_NO,"7893259243055070");
        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call(client.gatewayUrl, AlipayV2Config.APP_ID, "***", request);
        System.out.println(result);
        
    }


    private static void testDepositCancel() throws MpayApiNetworkError, MpayException, BuilderException {
        AlipayV2Client  client = new AlipayV2Client();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.APP_ID, "2088011691288213");
        builder.set(ProtocolV2Fields.CHARSET,AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, AlipayConstants.SIGN_TYPE_RSA);
        builder.bizSet(BusinessV2Fields.EXTEND_PARAMS, new HashMap(){{
            put(BusinessV2Fields.EXTEND_PARAMS_SYS_SERVICE_PROVIDER_ID, "2015102000490218");
        }});
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_FUND_ORDER_CANCEL);
//        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN,"234234234");
        builder.bizSet(BusinessV2Fields.OUT_ORDER_NO,"7893259243000570");
        builder.bizSet(BusinessV2Fields.OUT_REQUEST_NO,"7893259243000570");
        builder.bizSet(BusinessV2Fields.REMARK,"授权撤销");
        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call(client.gatewayUrl, AlipayV2Config.APP_ID, "***", request);
        System.out.println(result);
        
    }
    
    
    public static void testPay() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayV2Client  client = new AlipayV2Client();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_TRADE_TRADE);
//        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN,"234234234");
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO,"78924567890978999");
        builder.bizSet(BusinessV2Fields.SCENE,AlipayConstants.SCENE_BAR_CODE);
        builder.bizSet(BusinessV2Fields.AUTH_CODE,"284958077078424984");
        builder.bizSet(BusinessV2Fields.TOTAL_AMOUNT,"0.01");
        builder.bizSet(BusinessV2Fields.UNDISCOUNTABLE_AMOUNT,"0.01");
        builder.bizSet(BusinessV2Fields.SUBJECT,"甜甜圈");
        builder.bizSet(BusinessV2Fields.BODY,"what r u 弄啥嘞");
        //builder.bizSet(BusinessV2Fields.GOODS_DETAIL,"");
        builder.bizSet(BusinessV2Fields.OPERATOR_ID,"Rain");
        //builder.bizSet(BusinessV2Fields.STORE_ID,"49");
        //builder.bizSet(BusinessV2Fields.TERMINAL_ID,"345345");
        //builder.bizSet(BusinessV2Fields.EXTEND_PARAMS,"{\"sys_service_provider_id\":\"2015102000490218\"}");
        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call(client.gatewayUrl, AlipayV2Config.APP_ID, AlipayV2Config.PRIVATE_KEY, request);
        System.out.println(result);


    }
    public static  void testQuery() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayV2Client  client = new AlipayV2Client();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_TRADE_QUERY);
        //        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN,"234234234");
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO,"7893259243055072");
        builder.bizSet(BusinessV2Fields.OUT_REQUEST_NO,"7893259243055070");
        Map<String,String> request =builder.build();
        Map<String,Object> result = client.call(client.gatewayUrl, AlipayV2Config.APP_ID, AlipayV2Config.PRIVATE_KEY, request);
        System.out.println(result);

    }
    
    public static  void testRefundQuery() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayV2Client  client = new AlipayV2Client();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_TRADE_FASTPAY_REFUND_QUERY);
        //        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN,"234234234");
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO,"7893259243055072");
        builder.bizSet(BusinessV2Fields.OUT_REQUEST_NO,"7893259243055070");
        Map<String,String> request =builder.build();
        Map<String,Object> result = client.call(client.gatewayUrl, AlipayV2Config.APP_ID, AlipayV2Config.PRIVATE_KEY, request);
        System.out.println(result);

    }


    public static void testRefund() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayV2Client client = new AlipayV2Client();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_TRADE_REFUND);
        //        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN,"234234234");
        builder.bizSet(BusinessV2Fields.TRADE_NO,"2015112021001004840021529741");
        builder.bizSet(BusinessV2Fields.OUT_REQUEST_NO,"23423");
        builder.bizSet(BusinessV2Fields.REFUND_AMOUNT,"0.01");
        builder.bizSet(BusinessV2Fields.REFUND_REASON, "正常退款");
//        builder.bizSet(BusinessV2Fields.STORE_ID,"2324");
//        builder.bizSet(BusinessV2Fields.TERMINAL_ID,"22");
        Map<String,String> request = builder.build();
        Map<String,Object> result  = client.call(client.gatewayUrl, AlipayV2Config.APP_ID, AlipayV2Config.PRIVATE_KEY, request);
        System.out.println(result);


    }

    public static void testCancel() throws BuilderException, MpayException, MpayApiNetworkError{
        AlipayV2Client client = new AlipayV2Client();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_TRADE_CANCEL);
        //        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN,"234234234");
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO,"14492122200038936");
        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call(client.gatewayUrl, AlipayV2Config.APP_ID, AlipayV2Config.PRIVATE_KEY, request);
        System.out.println(result);
    }

    public static void testPrecreate()throws BuilderException, MpayException, MpayApiNetworkError{
        AlipayV2Client  client = new AlipayV2Client();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_TRADE_PRECREATE);
        builder.set(ProtocolV2Fields.NOTIFY_URL,"https://shouqianba.com");
//        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN,"234234234");
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO,"7892456789097890");
        builder.bizSet(BusinessV2Fields.TOTAL_AMOUNT,"0.01");
        builder.bizSet(BusinessV2Fields.UNDISCOUNTABLE_AMOUNT,"0.01");
        builder.bizSet(BusinessV2Fields.SUBJECT,"甜甜圈");
        builder.bizSet(BusinessV2Fields.BODY,"what r u 弄啥嘞");
        //builder.bizSet(BusinessV2Fields.GOODS_DETAIL,"");
        builder.bizSet(BusinessV2Fields.OPERATOR_ID,"Rain");
        //builder.bizSet(BusinessV2Fields.STORE_ID,"49");
        //builder.bizSet(BusinessV2Fields.TERMINAL_ID,"345345");
        //builder.bizSet(BusinessV2Fields.EXTEND_PARAMS,"{\"sys_service_provider_id\":\"2015102000490218\"}");
        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call(client.gatewayUrl, AlipayV2Config.APP_ID, AlipayV2Config.PRIVATE_KEY, request);
        System.out.println(result);

    }

}
