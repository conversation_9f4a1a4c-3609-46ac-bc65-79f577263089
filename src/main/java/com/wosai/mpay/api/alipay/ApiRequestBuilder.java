package com.wosai.mpay.api.alipay;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeMap;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.BuilderException;

public class ApiRequestBuilder {
    private static ObjectMapper om = new ObjectMapper();
    Map<String, String> request;
    Map<String, Object> bizContent;
    
    public ApiRequestBuilder () {
        request = new TreeMap<String, String>();
        bizContent = new LinkedHashMap<String, Object> ();

        request.put(ProtocolV2Fields.VERSION, "1.0");

    }

    public Map<String, String> build() throws BuilderException {
        if (!request.containsKey(ProtocolV2Fields.SERVICE)) {
            throw new BuilderException();
        }

        if (!bizContent.isEmpty()) {
            try {
                request.put(ProtocolV2Fields.BIZ_CONTENT, om.writeValueAsString(bizContent));
            }
            catch (JsonProcessingException e) {
                throw new BuilderException("unable to serialize bizContent in JSON format.", e);
            }
        }
        return request;
    }
    
    public void set(String field, String value) {
        request.put(field,  value);
    }
    
    public void bizSet(String field, Object value) {
        bizContent.put(field, value);
    }

    public Map<String,String> getRequest(){
        return request;
    }

    public Map<String,Object> getBizContent(){
        return bizContent;
    }
}
