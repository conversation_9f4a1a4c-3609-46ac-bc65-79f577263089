package com.wosai.mpay.api.alipay;

/**
 * Created by jian<PERSON> on 25/4/16.
 */
public class WapV2Fields {

    public static final String TRADE_NO = "tradeNO"; //交易号
    public static final String PARTNER_ID= "partnerID";//商户id
    public static final String BIZ_TYPE = "bizType";//交易类型
    public static final String BIZ_SUB_TYPE = "bizSubType"; //交易值类型
    public static final String DISPLAY_PAY_RESULT = "displayPayResult";//是否显示支付结果页
    public static final String BIZ_CONTEXT = "bizContext"; //支付额外的参数
    public static final String ORDER_STR = "orderStr";
    public static final String H5_PAY_REDIRECT_URL = "h5_pay_redirect_url";
    public static final String REDIRECT_URL = "redirect_url";
    public static final String TRADE_INFO = "trade_info";
    public static final String TYPE = "type";
    public static final String SIGN_STR = "sign_str";
    public static final String ZM_SERVICE_ID = "zm_service_id";
}
