package com.wosai.mpay.api.alipay;

import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

public class AlipayConstants {

    public static final String SIGN_TYPE        = "sign_type";

    public static final String SIGN_TYPE_RSA    = "RSA";

    public static final String SIGN_TYPE_RSA2   = "RSA2";

    public static final String SIGN_TYPE_MD5    = "MD5";

    public static final String SIGN_TYPE_SM2    = "SM2";

    public static final String SIGN_ALGORITHMS  = "SHA1WithRSA";

    public static final String SIGN_RSA2_ALGORITHMS = "SHA256WithRSA";

    public static final String APP_ID           = "app_id";

    public static final String FORMAT           = "format";

    public static final String METHOD           = "method";

    public static final String TIMESTAMP        = "timestamp";

    public static final String VERSION          = "version";

    public static final String SIGN             = "sign";

    public static final String ALIPAY_SDK       = "alipay_sdk";

    public static final String ACCESS_TOKEN     = "auth_token";

    public static final String TERMINAL_TYPE    = "terminal_type";

    public static final String TERMINAL_INFO    = "terminal_info";

    public static final String CHARSET          = "charset";

    public static final String NOTIFY_URL       = "notify_url";

    public static final String PID              = "pid";


    /**  接口版本  **/
    public static final String VERSION_ONE  = "1.0";

    /** 分账类型 **/
    public static final String ROYALTY_TYPE_TRANSFER = "transfer";
    public static final String ROYALTY_TYPE_TRANSFER_REFUND = "transfer_refund";


    public static final String EDU_SCENE_SCHOOL_CANTEEN = "SCHOOL_CANTEEN";


    /** 默认时间格式 **/
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**  Date默认时区 **/
    public static final String DATE_TIMEZONE    = "GMT+8";

    /** UTF-8字符集 **/
    public static final String CHARSET_UTF8     = "UTF-8";

    /** GBK字符集 **/
    public static final String CHARSET_GBK      = "GBK";

    /** JSON 应格式 */
    public static final String FORMAT_JSON      = "json";

    /** XML 应格式 */
    public static final String FORMAT_XML       = "xml";

    /** SDK版本号 */
    public static final String SDK_VERSION      = "alipay-sdk-java-dynamicVersionNo";

    public static final String PROD_CODE        = "prod_code";

    /** 老版本失败节点 */
    public static final String ERROR_RESPONSE   = "error_response";

    /** 新版本节点后缀 */
    public static final String RESPONSE_SUFFIX  = "_response";
    
    public static final String AUDITING = "AUDITING";
    public static final String AUDIT_FAILED = "AUDIT_FAILED";
    public static final String AUDIT_SUCCESS = "AUDIT_SUCCESS";
    
    public static final String TRUE = "T";
    public static final String FALSE = "F";

    public static final String PRODUCT_BARCODE_PAY_OFFLINE = "BARCODE_PAY_OFFLINE";
    public static final String PRODUCT_QR_CODE_OFFLINE = "QR_CODE_OFFLINE";


    public static final String SCENE_BAR_CODE = "bar_code";
    public static final String SCENE_SECURITY_CODE = "security_code";
    public static final String SCENE_OFFLINE_CODE = "codec_double_offline_code";
    public static final String SCENE_ZHIMA_CREDIT_CODE = "ZHIMA_CREDIT_CODE";

    public static final String PRODUCT_CODE_FACE_TO_FACE_PAYMENT = "FACE_TO_FACE_PAYMENT";
    public static final String PRODUCT_CODE_PRE_AUTH = "PRE_AUTH";
    public static final String PRODUCT_CODE_PRE_AUTH_ONLINE = "PRE_AUTH_ONLINE";
    public static final String PRODUCT_CODE_GENERAL_WITHHOLDING = "GENERAL_WITHHOLDING";
    public static final String PRODUCT_CODE_CREDIT_PAY_AFTER_USE = "CREDIT_PAY_AFTER_USE";  //芝麻先享
    public static final String PRODUCT_CODE_JSAPI_PAY = "JSAPI_PAY";                        //JS API 支付宝小程序


    public static final String AUTH_CONFIRM_MODE_COMPLETE = "COMPLETE"; // 无需调用解冻接口，支付宝端在扣款成功后会自动解冻剩余金额，同时该笔授权订单完成
    public static final String AUTH_CONFIRM_MODE_NOT_COMPLETE = "NOT_COMPLETE"; // 若auth_confirm_mode=NOT_COMPLETE，在收到支付成功通知后，商户自行调用解冻接口将余额进行解冻
    
    /** 信用卡 */
    public static final String NO_CREDIT = "credit_group";


    /**alipayV1结果返回码*/
    public static final String RESULT_CODE_V1_ORDER_FAIL = "ORDER_FAIL"; //下单失败
    public static final String RESULT_CODE_V1_ORDER_SUCCESS_PAY_SUCCESS = "ORDER_SUCCESS_PAY_SUCCESS"; //下单成功并且支付成功
    public static final String RESULT_CODE_V1_ORDER_SUCCESS_PAY_FAIL = "ORDER_SUCCESS_PAY_FAIL"; //下单成功支付失败
    public static final String RESULT_CODE_V1_ORDER_SUCCESS_PAY_INPROCESS = "ORDER_SUCCESS_PAY_INPROCESS"; //下单成功支付处理中
    public static final String RESULT_CODE_V1_UNKNOWN = "UNKNOWN"; //处理结果未知
    public static final String RESULT_CODE_V1_SUCCESS = "SUCCESS"; //成功
    public static final String RESULT_CODE_V1_FAIL = "FAIL"; //失败
    public static final String RESULT_CODE_V1_PROCESS_EXCEPTION = "PROCESS_EXCEPTION"; //处理异常


    /**alipay detail_error_code*/
    public static final String DETAIL_ERROR_CODE_V1_BUYER_PAYMENT_AMOUNT_DAY_LIMIT_ERROR = "BUYER_PAYMENT_AMOUNT_DAY_LIMIT_ERROR";//买家的付款日限额超限
    public static final String DETAIL_ERROR_CODE_V1_PULL_MOBILE_CASHIER_FAIL = "PULL_MOBILE_CASHIER_FAIL";
    public static final String DETAIL_ERROR_CODE_V1_TRADE_NOT_EXIST = "TRADE_NOT_EXIST"; //交易不存在
    public static final String DETAIL_ERROR_CODE_V1_REASON_TRADE_REFUND_FEE_ERR = "REASON_TRADE_REFUND_FEE_ERR";
    public static final String DETAIL_ERROR_CODE_V1_SELLER_BALANCE_NOT_ENOUGH = "SELLER_BALANCE_NOT_ENOUGH";    // 卖家账户金额不足



    /**alipayV2结果返回码*/
    public static final String V2_RETURN_CODE_INSUFFICIENT_TOKEN_PERMISSIONS = "20001"; //Insufficient Token Permissions
    public static final String V2_RETURN_CODE_SUCCESS = "10000";//业务全部处理成功
    public static final String V2_RETURN_CODE_FAIL = "40004";//业务处理失败
    public static final String V2_RETURN_CODE_INSUFFICIENT_PERMITIONS = "40006";//权限不足
    public static final String V2_RETURN_CODE_REQUIRE_PARAMETER = "40001";//缺少必选参数
    public static final String V2_RETURN_CODE_INVALID_PARAMETER = "40002";//缺少必选参数
    public static final String V2_RETURN_CODE_INPROG = "10003";//业务处理中,该结果码只有在声波支付请求API时才返回，代表付款还在进行中，需要调用查询接口查询最终的支付结果
    public static final String V2_RETURN_CODE_UNKNOW ="20000";//业务出现未知错误或者系统异常,如果支付接口返回，需要调用查询接口确认订单状态或者发起撤销

    /**alipayV2,错误码*/
    public static final String RESULT_CODE_V2_SYSTEM_ERROR = "ACQ.SYSTEM_ERROR";//接口返回错误
    public static final String RESULT_CODE_V2_INVALID_PARAMETER = "ACQ.INVALID_PARAMETER";//参数无效
    public static final String RESULT_CODE_V2_FREQUENCY_LIMITED = "ACQ.FREQUENCY_LIMITED";//请求限流
    public static final String RESULT_CODE_V2_INVALID_AUTH_TOKEN = "aop.invalid-auth-token";
    public static final String RESULT_CODE_V2_AUTH_TOKEN_TIME_OUT = "aop.auth-token-time-out";
    
    /**alipayV2,付款(pay+precreate),错误码*/
    public static final String RESULT_CODE_V2_PAY_ACCESS_FORBIDDEN = "ACQ.ACCESS_FORBIDDEN";//无权限使用接口
    public static final String RESULT_CODE_V2_PAY_EXIST_FORBIDDEN_WORD = "ACQ.EXIST_FORBIDDEN_WORD";//订单信息中包含违禁词
    public static final String RESULT_CODE_V2_PAY_PARTNER_ERROR = "ACQ.PARTNER_ERROR";//应用APP_ID填写错误
    public static final String RESULT_CODE_V2_PAY_TOTAL_FEE_EXCEED = "ACQ.TOTAL_FEE_EXCEED";//订单总金额超过限额
    public static final String RESULT_CODE_V2_PAY_PAYMENT_AUTH_CODE_INVALID = "ACQ.PAYMENT_AUTH_CODE_INVALID";//支付授权码无效
    public static final String RESULT_CODE_V2_PAY_CONTEXT_INCONSISTENT = "ACQ.CONTEXT_INCONSISTENT";//交易信息被篡改
    public static final String RESULT_CODE_V2_PAY_TRADE_HAS_SUCCESS = "ACQ.TRADE_HAS_SUCCESS";//交易已被支付
    public static final String RESULT_CODE_V2_PAY_TRADE_HAS_CLOSE = "ACQ.TRADE_HAS_CLOSE";//交易已经关闭
    public static final String RESULT_CODE_V2_PAY_BUYER_BALANCE_NOT_ENOUGH = "ACQ.BUYER_BALANCE_NOT_ENOUGH";//买家余额不足
    public static final String RESULT_CODE_V2_PAY_BUYER_BANKCARD_BALANCE_NOT_ENOUGH = "ACQ.BUYER_BANKCARD_BALANCE_NOT_ENOUGH";//用户银行卡余额不足
    public static final String RESULT_CODE_V2_PAY_ERROR_BALANCE_PAYMENT_DISABLE = "ACQ.ERROR_BALANCE_PAYMENT_DISABLE";//余额支付功能关闭
    public static final String RESULT_CODE_V2_PAY_BUYER_SELLER_EQUAL = "ACQ.BUYER_SELLER_EQUAL";//买卖家不能相同
    public static final String RESULT_CODE_V2_PAY_TRADE_BUYER_NOT_MATCH = "ACQ.TRADE_BUYER_NOT_MATCH";//交易买家不匹配
    public static final String RESULT_CODE_V2_PAY_BUYER_ENABLE_STATUS_FORBID = "ACQ.BUYER_ENABLE_STATUS_FORBID";//买家状态非法
    public static final String RESULT_CODE_V2_PAY_PULL_MOBILE_CASHIER_FAIL = "ACQ.PULL_MOBILE_CASHIER_FAIL";//唤起移动收银台失败
    public static final String RESULT_CODE_V2_PAY_MOBILE_PAYMENT_SWITCH_OFF = "ACQ.MOBILE_PAYMENT_SWITCH_OFF";//用户的无线支付开关关闭
    public static final String RESULT_CODE_V2_PAY_PAYMENT_FAIL = "ACQ.PAYMENT_FAIL";//支付失败
    public static final String RESULT_CODE_V2_PAY_BUYER_PAYMENT_AMOUNT_DAY_LIMIT_ERROR = "ACQ.BUYER_PAYMENT_AMOUNT_DAY_LIMIT_ERROR";//买家付款日限额超限
    public static final String RESULT_CODE_V2_PAY_BEYOND_PAY_RESTRICTION = "ACQ.BEYOND_PAY_RESTRICTION";//商户收款额度超限
    public static final String RESULT_CODE_V2_PAY_BEYOND_PER_RECEIPT_RESTRICTION = "ACQ.BEYOND_PER_RECEIPT_RESTRICTION";//商户收款金额超过月限额
    public static final String RESULT_CODE_V2_PAY_BUYER_PAYMENT_AMOUNT_MONTH_LIMIT_ERROR = "ACQ.BUYER_PAYMENT_AMOUNT_MONTH_LIMIT_ERROR";//买家付款月额度超限
    public static final String RESULT_CODE_V2_PAY_SELLER_BEEN_BLOCKED = "ACQ.SELLER_BEEN_BLOCKED";//商家账号被冻结
    public static final String RESULT_CODE_V2_PAY_ERROR_BUYER_CERTIFY_LEVEL_LIMIT = "ACQ.ERROR_BUYER_CERTIFY_LEVEL_LIMIT";//买家未通过人行认证
    public static final String RESULT_CODE_V2_PAY_PAYMENT_REQUEST_HAS_RISK = "ACQ.PAYMENT_REQUEST_HAS_RISK";//支付有风险
    public static final String RESULT_CODE_V2_PAY_NO_PAYMENT_INSTRUMENTS_AVAILABLE = "ACQ.NO_PAYMENT_INSTRUMENTS_AVAILABLE";//没用可用的支付工具
    public static final String RESULT_CODE_V2_PAY_USER_FACE_PAYMENT_SWITCH_OFF="ACQ.USER_FACE_PAYMENT_SWITCH_OFF";	//用户当面付付款开关关闭
    public static final String RESULT_CODE_V2_PAY_INVALID_STORE_ID = "ACQ.INVALID_STORE_ID"; //商户门店编号无效
    public static final String RESULT_CODE_V2_PAY_SUB_MERCHANT_CREATE_FAIL = "ACQ.SUB_MERCHANT_CREATE_FAIL"; //二级商户创建失败
    public static final String RESULT_CODE_V2_PAY_SUB_MERCHANT_TYPE_INVALID = "ACQ.SUB_MERCHANT_TYPE_INVALID"; //二级商户类型非法
    public static final String RESULT_CODE_V2_PAY_AGREEMENT_NOT_EXIST = "ACQ.AGREEMENT_NOT_EXIST"; //用户协议不存在
    public static final String RESULT_CODE_V2_PAY_AGREEMENT_INVALID = "ACQ.AGREEMENT_INVALID"; //用户协议失效
    public static final String RESULT_CODE_V2_PAY_AGREEMENT_STATUS_NOT_NORMAL = "ACQ.AGREEMENT_STATUS_NOT_NORMAL"; //用户协议状态非NORMAL
    public static final String RESULT_CODE_V2_PAY_MERCHANT_AGREEMENT_NOT_EXIST = "ACQ.MERCHANT_AGREEMENT_NOT_EXIST"; //商户协议不存在
    public static final String RESULT_CODE_V2_PAY_MERCHANT_AGREEMENT_INVALID = "ACQ.MERCHANT_AGREEMENT_INVALID"; //商户协议已失效
    public static final String RESULT_CODE_V2_PAY_MERCHANT_STATUS_NOT_NORMAL = "ACQ.MERCHANT_STATUS_NOT_NORMAL"; //商户协议状态非正常状态
    public static final String RESULT_CODE_V2_PAY_CARD_USER_NOT_MATCH = "ACQ.CARD_USER_NOT_MATCH"; //脱机记录用户信息不匹配
    public static final String RESULT_CODE_V2_PAY_CARD_TYPE_ERROR = "ACQ.CARD_TYPE_ERROR"; //卡类型错误
    public static final String RESULT_CODE_V2_PAY_CERT_EXPIRED = "ACQ.CERT_EXPIRED"; //凭证过期
    public static final String RESULT_CODE_V2_PAY_AMOUNT_OR_CURRENCY_ERROR = "ACQ.AMOUNT_OR_CURRENCY_ERROR"; //订单金额或币种信息错误
    public static final String RESULT_CODE_V2_PAY_CURRENCY_NOT_SUPPORT = "ACQ.CURRENCY_NOT_SUPPORT"; //订单币种不支持
    public static final String RESULT_CODE_V2_PAY_MERCHANT_UNSUPPORT_ADVANCE = "ACQ.MERCHANT_UNSUPPORT_ADVANCE"; //先享后付2.0准入失败,商户不支持垫资支付产品
    public static final String RESULT_CODE_V2_PAY_BUYER_UNSUPPORT_ADVANCE = "ACQ.BUYER_UNSUPPORT_ADVANCE"; //先享后付2.0准入失败,买家不满足垫资条件
    public static final String RESULT_CODE_V2_PAY_ORDER_UNSUPPORT_ADVANCE = "ACQ.ORDER_UNSUPPORT_ADVANCE"; //订单不支持先享后付垫资
    public static final String RESULT_CODE_V2_PAY_CYCLE_PAY_DATE_NOT_MATCH = "ACQ.CYCLE_PAY_DATE_NOT_MATCH"; //扣款日期不在签约时的允许范围之内
    public static final String RESULT_CODE_V2_PAY_CYCLE_PAY_SINGLE_FEE_EXCEED = "ACQ.CYCLE_PAY_SINGLE_FEE_EXCEED"; //周期扣款的单笔金额超过签约时限制
    public static final String RESULT_CODE_V2_PAY_CYCLE_PAY_TOTAL_FEE_EXCEED = "ACQ.CYCLE_PAY_TOTAL_FEE_EXCEED"; //周期扣款的累计金额超过签约时限制
    public static final String RESULT_CODE_V2_PAY_CYCLE_PAY_TOTAL_TIMES_EXCEED = "ACQ.CYCLE_PAY_TOTAL_TIMES_EXCEED"; //周期扣款的总次数超过签约时限制
    public static final String RESULT_CODE_V2_PAY_SECONDARY_MERCHANT_STATUS_ERROR = "ACQ.SECONDARY_MERCHANT_STATUS_ERROR"; //商户状态异常
    public static final String RESULT_CODE_V2_PAY_BUYER_NOT_EXIST = "ACQ.BUYER_NOT_EXIST"; //获取顾客账户信息失败
    public static final String RESULT_CODE_V2_PAY_NOT_SUPPORT_PAYMENT_INST = "ACQ.NOT_SUPPORT_PAYMENT_INST"; //顾客支付宝版本低不支持支付
    public static final String RESULT_CODE_V2_PAY_PRODUCT_AMOUNT_LIMIT_ERROR = "ACQ.PRODUCT_AMOUNT_LIMIT_ERROR"; //顾客在本商户付款额度超限


    /**alipayV2, query,错误码*/
    public static final String RESULT_CODE_V2_QUERY_TRADE_NOT_EXIST="ACQ.TRADE_NOT_EXIST";	//交易不存在

    /**alipayV2,cancel 错误码*/
    public static final String RESULT_CODE_V2_CANCEL_SELLER_BALANCE_NOT_ENOUGH = "ACQ.SELLER_BALANCE_NOT_ENOUGH";//卖家余额不足
    public static final String RESULT_CODE_V2_CANCEL_REASON_TRADE_BEEN_FREEZEN = "ACQ.REASON_TRADE_BEEN_FREEZEN";//交易被冻结

    /**alipayV2,refund 错误码*/
    public static final String RESULT_CODE_V2_REFUND_SELLER_BALANCE_NOT_ENOUGH = "ACQ.SELLER_BALANCE_NOT_ENOUGH";//卖家余额不足
    public static final String RESULT_CODE_V2_REFUND_REFUND_AMT_NOT_EQUAL_TOTAL = "ACQ.REFUND_AMT_NOT_EQUAL_TOTAL";//退款金额超限
    public static final String RESULT_CODE_V2_REFUND_REASON_TRADE_BEEN_FREEZEN = "ACQ.REASON_TRADE_BEEN_FREEZEN";//请求退款的交易被冻结
    public static final String RESULT_CODE_V2_REFUND_TRADE_NOT_EXIST = "ACQ.TRADE_NOT_EXIST";//交易不存在
    public static final String RESULT_CODE_V2_REFUND_TRADE_HAS_FINISHED = "ACQ.TRADE_HAS_FINISHED";//交易已完结
    public static final String RESULT_CODE_V2_REFUND_TRADE_STATUS_ERROR = "ACQ.TRADE_STATUS_ERROR";//交易状态非法
    public static final String RESULT_CODE_V2_REFUND_DISCORDANT_REPEAT_REQUEST = "ACQ.DISCORDANT_REPEAT_REQUEST";//不一致的请求
    public static final String RESULT_CODE_V2_REFUND_REASON_TRADE_REFUND_FEE_ERR = "ACQ.REASON_TRADE_REFUND_FEE_ERR";//退款金额无效


    /**alipayV2,预授权 错误码*/
    public static final String RESULT_CODE_DEPOSIT_ILLEGAL_ARGUMENT = "ILLEGAL_ARGUMENT";//授权失败，预授权冻结参数异常或参数缺失，请顾客刷新付款码后重新收款    
    public static final String RESULT_CODE_DEPOSIT_EXIST_FORBIDDEN_WORD = "EXIST_FORBIDDEN_WORD";//授权失败，订单信息中包含违禁词
    public static final String RESULT_CODE_DEPOSIT_ACCESS_FORBIDDEN = "ACCESS_FORBIDDEN";//授权失败，本商户没有权限使用该产品，建议顾客使用其他方式付款    
    public static final String RESULT_CODE_DEPOSIT_UNIQUE_VIOLATION = "UNIQUE_VIOLATION";//授权失败，商户订单号重复，请收银员取消本笔订单并重新授权
    public static final String RESULT_CODE_DEPOSIT_PAYER_USER_STATUS_LIMIT = "PAYER_USER_STATUS_LIMIT";//授权失败，订单信息中包含违禁词
    public static final String RESULT_CODE_DEPOSIT_PAYMENT_AUTH_CODE_INVALID = "PAYMENT_AUTH_CODE_INVALID";//授权失败，获取顾客账户信息失败，请顾客刷新付款码后重新收款，如再次授权失败  
    public static final String RESULT_CODE_DEPOSIT_PAYER_NOT_EXIST = "PAYER_NOT_EXIST";//授权失败，获取顾客账户信息失败，请顾客刷新付款码后重新收款  
    public static final String RESULT_CODE_DEPOSIT_MONEY_NOT_ENOUGH = "MONEY_NOT_ENOUGH";//授权失败，订单信息中包含违禁词
    public static final String RESULT_CODE_DEPOSIT_ORDER_ALREADY_CLOSED = "ORDER_ALREADY_CLOSED";//授权失败，本笔授权订单已关闭
    public static final String RESULT_CODE_DEPOSIT_FREEZE_ALREADY_SUCCESS = "FREEZE_ALREADY_SUCCESS";//授权失败，订单信息中包含违禁词
    public static final String RESULT_CODE_DEPOSIT_ERROR_BALANCE_PAYMENT_DISABLE = "ERROR_BALANCE_PAYMENT_DISABLE";//授权失败，顾客余额支付功能开关关闭，请用户打开余额支付功能开关    
    public static final String RESULT_CODE_DEPOSIT_PULL_MOBILE_CASHIER_FAIL = "PULL_MOBILE_CASHIER_FAIL";//授权失败，顾客手机唤起收银台失败，请顾客检查手机网络，刷新付款码后重新预授权，并让顾客在付款码页面等待确认    
    public static final String RESULT_CODE_DEPOSIT_USER_FACE_PAYMENT_SWITCH_OFF = "USER_FACE_PAYMENT_SWITCH_OFF";//授权失败，顾客当面付付款开关关闭，请用户在手机上打开当面付付款开关    
    public static final String RESULT_CODE_DEPOSIT_ORDER_ALREADY_FINISH = "ORDER_ALREADY_FINISH";//本笔授权订单已经完结，不允许进行资金撤销操作，确认请求资金解冻的资金授权订单号是否正确   
    public static final String RESULT_CODE_DEPOSIT_PAYEE_NOT_EXIST = "PAYEE_NOT_EXIST";//授权失败，收款方账号不存在  
    public static final String RESULT_CODE_DEPOSIT_PAYEE_USER_STATUS_LIMIT = "PAYEE_USER_STATUS_LIMIT";//授权失败，收款方账号异常   
    public static final String RESULT_CODE_DEPOSIT_PAYER_PAYEE_EQUAL = "PAYER_PAYEE_EQUAL";//授权失败，收付款方信息不能相同    
    public static final String RESULT_CODE_DEPOSIT_NO_PAYMENT_INSTRUMENTS_AVAILABLE = "NO_PAYMENT_INSTRUMENTS_AVAILABLE";//授权失败，用户没用可用的支付工具    
    public static final String RESULT_CODE_DEPOSIT_CLIENT_VERSION_NOT_MATCH = "CLIENT_VERSION_NOT_MATCH";//授权失败，顾客手机支付宝客户端版本过低，请更新到最新版本 
    
    /**alipayV2,预授权撤销 错误码*/
    public static final String RESULT_CODE_DEPOSIT_CANCEL_OPERATION_TIME_OUT = "CANCEL_OPERATION_TIME_OUT";//撤销操作超过允许时间范围   
    public static final String RESULT_CODE_DEPOSIT_REQUEST_AMOUNT_EXCEED = "REQUEST_AMOUNT_EXCEED";//撤销触发的解冻金额超过冻结金额     
    public static final String RESULT_CODE_DEPOSIT_CANCEL_ORDER_ALREADY_CLOSED = "ORDER_ALREADY_CLOSED";//授权订单已经关闭，无法再进行资金操作

    public static final String RESULT_CODE_DEPOSIT_AUTH_OPERATION_NOT_EXIST = "AUTH_OPERATION_NOT_EXIST"; //支付宝资金操作流水不存在
    public static final String RESULT_CODE_DEPOSIT_AUTH_ORDER_NOT_EXIST = "AUTH_ORDER_NOT_EXIST"; //支付宝资金授权订单不存在    

    /**alipayV2,芝麻先享信用下单 错误码*/
    public static final String RESULT_CODE_DEPOSIT_CREDIT_AGREEMENT_INVALID = "CREDIT_AGREEMENT_INVALID";//芝麻开通协议已失效
    public static final String RESULT_CODE_DEPOSIT_SIGN_SALES_CHANGE = "SIGN_SALES_CHANGE";//销售方案属性变更，请调用信用下单（用户确认场景）
    public static final String RESULT_CODE_DEPOSIT_ZHIMA_CHECK_NO_PASS = "ZHIMA_CHECK_NO_PASS";//芝麻校验未通过
    public static final String RESULT_CODE_DEPOSIT_ZHIMA_EVALUATE_NO_PASS = "ZHIMA_EVALUATE_NO_PASS";//芝麻校验未通过

    /** 分账错误码 */

    public static final String RESULT_CODE_SETTLE_DISCORDANT_REPEAT_REQUEST = "DISCORDANT_REPEAT_REQUEST"; //请求被篡改
    public static final String RESULT_CODE_SETTLE_INVALID_PARAMETER = "INVALID_PARAMETER"; //参数无效
    public static final String RESULT_CODE_SETTLE_INVALID_RECEIVER_TYPE = "INVALID_RECEIVER_TYPE"; //分账收款方类型参数非法
    public static final String RESULT_CODE_SETTLE_PARTNER_ERROR = "PARTNER_ERROR"; //应用APP_ID填写错误
    public static final String RESULT_CODE_SETTLE_PRODUCT_UNSIGN = "PRODUCT_UNSIGN"; //未签约分账产品
    public static final String RESULT_CODE_SETTLE_RECEIVER_LIST_EMPTY = "RECEIVER_LIST_EMPTY"; //分账收款方列表为空
    public static final String RESULT_CODE_SETTLE_RECEIVER_LIST_OVERLOAD = "RECEIVER_LIST_OVERLOAD"; //分账收款方列表数量超限
    public static final String RESULT_CODE_SETTLE_RELATION_QUANTITY_LIMIT = "RELATION_QUANTITY_LIMIT"; //达到分账关系集数量上限
    public static final String RESULT_CODE_SETTLE_SYSTEM_ERROR = "SYSTEM_ERROR"; //系统繁忙
    public static final String RESULT_CODE_SETTLE_TRADE_NOT_EXIST = "TRADE_NOT_EXIST"; //交易不存在
    public static final String RESULT_CODE_SETTLE_TRADE_SETTLE_ERROR = "TRADE_SETTLE_ERROR"; //分账处理失败
    public static final String RESULT_CODE_SETTLE_TRADE_STATUS_ERROR = "TRADE_STATUS_ERROR"; //交易状态不合法
    public static final String RESULT_CODE_SETTLE_USERNAME_NOT_MATCH = "USERNAME_NOT_MATCH"; //分账接收方全称不匹配
    public static final String RESULT_CODE_SETTLE_USER_NOT_EXIST = "USER_NOT_EXIST"; //分账接收方不存在

    /**交易状态*/
    public static final String TRADE_STATUS_WAIT_BUYER_PAY = "WAIT_BUYER_PAY";
    public static final String TRADE_STATUS_TRADE_CLOSED = "TRADE_CLOSED";
    public static final String TRADE_STATUS_TRADE_SUCCESS = "TRADE_SUCCESS";
    public static final String TRADE_STATUS_TRADE_FINISHED = "TRADE_FINISHED";
    public static final String TRADE_STATUS_TRADE_PENDING = "TRADE_PENDING";


    // WAP 手机网页即时到帐
    public static final String WAP_SEC_ID_RSA = "0001";
    public static final String WAP_SEC_ID_MD5 = "MD5";

    // query 查单接口 query_options 选项
    public static final String QUERY_OPTIONS_TRADE_SETTLE_INFO = "TRADE_SETTLE_INFO";
    public static final String QUERY_OPTIONS_VOUCHER_DETAIL_LIST = "voucher_detail_list"; // 查询优惠券信息
    public static final String QUERY_OPTIONS_DISCOUNT_GOODS_DETAIL = "discount_goods_detail"; // 查询单品券优惠信息
    public static final String QUERY_OPTIONS_FUND_BILL_LIST = "fund_bill_list"; // 查询资金渠道
    public static final String QUERY_OPTIONS_REFUND_DETAIL_ITEM_LIST = "refund_detail_item_list"; // 退款时返回资金渠道

    /** 通知动作类型 **/
    public static final String NOTIFY_ACTION_TYPE_PAY_BY_ACCOUNT_ACTION = "payByAccountAction";//支付
    public static final String NOTIFY_ACTION_TYPE_CREATE_DIRECT_PAY_BUYER_ACTION = "createDirectPayTradeByBuyerAction";//创建
    public static final String NOTIFY_ACTION_TYPE_REFUND_FP_ACTION = "refundFPAction";//退款
    public static final String NOTIFY_ACTION_TYPE_REVERSE_ACTION = "reverseAction";//撤销
    public static final String NOTIFY_ACTION_TYPE_CLOSE_TRADE_ACTION = "closeTradeAction";//关闭
    public static final String NOTIFY_ACTION_TYPE_FINISH_FP_ACTION = "finishFPAction";//交易完成
    
    // 资金预授权明细的状态  INIT：初始  SUCCESS: 成功  CLOSED：关闭
    public static final String STATUS_SUCCESS = "SUCCESS";
    public static final String STATUS_INIT = "INIT";
    public static final String STATUS_CLOSED = "CLOSED";

    /**
     * 业务场景 例如:ISV_PAY:ISV的支付场 景
     */
    public static final String SHARECODE_BIZ_SCENE = "ISV_PAY";

    /** 信用交易场景 */
    public static final String CREDIT_PAY_UNCERTAIN_FEE_SCENE = "CREDIT_PAY_UNCERTAIN_FEE";


    public static final String ADVANCE_PAYMENT_TYPE_ENJOY_PAY_V2 = "ENJOY_PAY_V2"; //垫资保付标记

    public static final Set<String> PAY_FAIL_ERR_CODE_LISTS = Arrays.asList(
            RESULT_CODE_V2_INVALID_PARAMETER, RESULT_CODE_V2_INVALID_AUTH_TOKEN,
            RESULT_CODE_V2_PAY_ACCESS_FORBIDDEN, RESULT_CODE_V2_PAY_EXIST_FORBIDDEN_WORD,
            RESULT_CODE_V2_PAY_PARTNER_ERROR, RESULT_CODE_V2_PAY_TOTAL_FEE_EXCEED, RESULT_CODE_V2_PAY_PAYMENT_AUTH_CODE_INVALID,
            RESULT_CODE_V2_PAY_CONTEXT_INCONSISTENT, RESULT_CODE_V2_PAY_TRADE_HAS_CLOSE, RESULT_CODE_V2_PAY_BUYER_BALANCE_NOT_ENOUGH,
            RESULT_CODE_V2_PAY_BUYER_BANKCARD_BALANCE_NOT_ENOUGH, RESULT_CODE_V2_PAY_ERROR_BALANCE_PAYMENT_DISABLE,
            RESULT_CODE_V2_PAY_BUYER_SELLER_EQUAL, RESULT_CODE_V2_PAY_TRADE_BUYER_NOT_MATCH,
            RESULT_CODE_V2_PAY_BUYER_ENABLE_STATUS_FORBID, RESULT_CODE_V2_PAY_PULL_MOBILE_CASHIER_FAIL,
            RESULT_CODE_V2_PAY_MOBILE_PAYMENT_SWITCH_OFF,RESULT_CODE_V2_PAY_BEYOND_PER_RECEIPT_RESTRICTION,
            RESULT_CODE_V2_PAY_BUYER_PAYMENT_AMOUNT_DAY_LIMIT_ERROR, RESULT_CODE_V2_PAY_BEYOND_PAY_RESTRICTION,
            RESULT_CODE_V2_PAY_BUYER_PAYMENT_AMOUNT_MONTH_LIMIT_ERROR, RESULT_CODE_V2_PAY_SELLER_BEEN_BLOCKED,
            RESULT_CODE_V2_PAY_ERROR_BUYER_CERTIFY_LEVEL_LIMIT, RESULT_CODE_V2_PAY_PAYMENT_REQUEST_HAS_RISK,
            RESULT_CODE_V2_PAY_NO_PAYMENT_INSTRUMENTS_AVAILABLE, RESULT_CODE_V2_PAY_USER_FACE_PAYMENT_SWITCH_OFF,
            RESULT_CODE_V2_PAY_PAYMENT_FAIL,RESULT_CODE_V2_PAY_INVALID_STORE_ID,
            RESULT_CODE_V2_PAY_SUB_MERCHANT_CREATE_FAIL,RESULT_CODE_V2_PAY_SUB_MERCHANT_TYPE_INVALID,
            RESULT_CODE_V2_PAY_AGREEMENT_NOT_EXIST,RESULT_CODE_V2_PAY_AGREEMENT_INVALID,
            RESULT_CODE_V2_PAY_AGREEMENT_STATUS_NOT_NORMAL,RESULT_CODE_V2_PAY_MERCHANT_AGREEMENT_NOT_EXIST,
            RESULT_CODE_V2_PAY_MERCHANT_AGREEMENT_INVALID,RESULT_CODE_V2_PAY_MERCHANT_STATUS_NOT_NORMAL,
            RESULT_CODE_V2_PAY_CARD_USER_NOT_MATCH,RESULT_CODE_V2_PAY_CARD_TYPE_ERROR,
            RESULT_CODE_V2_PAY_CERT_EXPIRED,RESULT_CODE_V2_PAY_AMOUNT_OR_CURRENCY_ERROR,
            RESULT_CODE_V2_PAY_CURRENCY_NOT_SUPPORT,RESULT_CODE_V2_PAY_MERCHANT_UNSUPPORT_ADVANCE,
            RESULT_CODE_V2_PAY_BUYER_UNSUPPORT_ADVANCE,RESULT_CODE_V2_PAY_ORDER_UNSUPPORT_ADVANCE,
            RESULT_CODE_V2_PAY_CYCLE_PAY_DATE_NOT_MATCH,RESULT_CODE_V2_PAY_CYCLE_PAY_SINGLE_FEE_EXCEED,
            RESULT_CODE_V2_PAY_CYCLE_PAY_TOTAL_FEE_EXCEED,RESULT_CODE_V2_PAY_CYCLE_PAY_TOTAL_TIMES_EXCEED,
            RESULT_CODE_V2_PAY_SECONDARY_MERCHANT_STATUS_ERROR,RESULT_CODE_V2_PAY_BUYER_NOT_EXIST,
            RESULT_CODE_V2_PAY_NOT_SUPPORT_PAYMENT_INST, RESULT_CODE_V2_PAY_PRODUCT_AMOUNT_LIMIT_ERROR
    ).stream().collect(Collectors.toSet());
    
    public static final Set<String> DEPOSIT_FAIL_ERR_CODE_LISTS = Arrays.asList(
            RESULT_CODE_DEPOSIT_ILLEGAL_ARGUMENT, RESULT_CODE_DEPOSIT_EXIST_FORBIDDEN_WORD, RESULT_CODE_DEPOSIT_ACCESS_FORBIDDEN,
            RESULT_CODE_DEPOSIT_UNIQUE_VIOLATION, RESULT_CODE_DEPOSIT_PAYER_USER_STATUS_LIMIT,RESULT_CODE_DEPOSIT_PAYMENT_AUTH_CODE_INVALID,
            RESULT_CODE_DEPOSIT_PAYER_NOT_EXIST, RESULT_CODE_DEPOSIT_PAYMENT_AUTH_CODE_INVALID, RESULT_CODE_DEPOSIT_MONEY_NOT_ENOUGH,
            RESULT_CODE_DEPOSIT_ORDER_ALREADY_CLOSED, RESULT_CODE_DEPOSIT_FREEZE_ALREADY_SUCCESS, RESULT_CODE_DEPOSIT_ERROR_BALANCE_PAYMENT_DISABLE,
            RESULT_CODE_DEPOSIT_PULL_MOBILE_CASHIER_FAIL, RESULT_CODE_DEPOSIT_USER_FACE_PAYMENT_SWITCH_OFF, RESULT_CODE_DEPOSIT_ORDER_ALREADY_FINISH,
            RESULT_CODE_DEPOSIT_PAYEE_NOT_EXIST, RESULT_CODE_DEPOSIT_PAYEE_USER_STATUS_LIMIT, RESULT_CODE_DEPOSIT_PAYER_PAYEE_EQUAL,
            RESULT_CODE_DEPOSIT_NO_PAYMENT_INSTRUMENTS_AVAILABLE, RESULT_CODE_DEPOSIT_CLIENT_VERSION_NOT_MATCH
    ).stream().collect(Collectors.toSet());

    public static final Set<String> DEPOSIT_ZM_CHECK_NO_PASS_ERR_CODE_LISTS = new HashSet<>(Arrays.asList(
            RESULT_CODE_DEPOSIT_CREDIT_AGREEMENT_INVALID, RESULT_CODE_DEPOSIT_SIGN_SALES_CHANGE,
            RESULT_CODE_DEPOSIT_ZHIMA_CHECK_NO_PASS, RESULT_CODE_DEPOSIT_ZHIMA_EVALUATE_NO_PASS
    ));

    public static final Object SUBSIDY_SCOPE_ORDER = "ORDER";
    
    public static final String BIZ_TYPE_CREDIT_AUTH = "CREDIT_AUTH";    //信用授权场景
    public static final String BIZ_TYPE_CREDIT_DEDUCT = "CREDIT_DEDUCT";    //信用代扣场景
    public static final String BIZ_TYPE_PAY_AFTER = "pay_after";    //芝麻先享

    public static final String SYNC_STATUS_COMPLETE = "COMPLETE"; // 同步用户已履约
    public static final String SYNC_STATUS_CLOSED = "CLOSED";  // 同步履约已取消
    public static final String SYNC_STATUS_VIOLATED = "VIOLATED";  // 用户已违约
    
    public static final String TERM_INFO_TERMINAL_TYPE = "11"; //条码支付辅助受理终端

    public static final String IS_SKIP_MCC_CHECK_Y = "Y";

    /**  支付宝先享后付 - 周期付   **/
    public static final String SUBSCRIPTION_CANCEL_TYPE_NORMAL = "NORMAL";   // 解约类型：正常取消
    public static final String SUBSCRIPTION_CANCEL_TYPE_DEFAULT = "DEFAULT"; // 解约类型：违约取消
    public static final String AGREEMENT_STATUS_VALID = "VALID";  // 开通/授权状态 VALID:有效
    public static final String AGREEMENT_STATUS_INVALID = "INVALID";  // 开通/授权状态 INVALID:无效

    /** 芝麻先享 amount_type */
    public static final String AMOUNT_TYPE_ORDER_AMOUNT = "ORDER_AMOUNT"; // 表示order_amount传入的金额为后付金额，在发起扣款时，最大扣款支付金额为order_amount传入的值
    public static final String AMOUNT_TYPE_RISK_AMOUNT = "RISK_AMOUNT"; // 表示ORDER_AMOUNT传入的金额为风险预估金额，在发起扣款时，最大扣款支付金额为商户签约时约定的上限额度

    /**
     * 微信花呗引流常量
     */
    public static final String APP_SOURCE_WECHAT = "wechat"; //来源
    public static final String APP_SOURCE_ALIPAY_AGGCODE_SCAN = "alipay_aggcode_scan"; //标识通过⽀付宝端外哪个⻚⾯进来
    public static final String BIZ_SCENE_ALIPAY_PROMO = "ALIPAY_PROMO";
    public static final String BIZ_SCENE_ISV_PAY_PAGE = "ISV_PAY_PAGE";
    public static final String CHANNEL_NAME_HUABEI = "花呗";

    /**
     * 花呗分期单通道前置发奖
     */


    public static final String ACQUIRE_MODE_BANK_AGENT_MODE = "bankAgentMode";//间连
    public static final String ACQUIRE_MODE_NORMAL_ORDER_MODE = "normalOrderMode";//直连
    public static final String SCENE_TYPE_OFFLINE = "offline";//线下
    public static final String SCENE_TYPE_ONLINE = "online";//线上

    public static final String ONLINE_PAYMENT = "线上支付";
    public static final String INITIATOR_SCAN = "主扫";
    public static final String RECEIVER_SCAN = "被扫";
    public static final String TOUCH_PAYMENT = "碰一碰";
    public static final String FACE_SCAN = "扫脸";

    /**
     * 芝麻先享信用服务开通/授权接口所允许的字段
     */
    public static final Set<String> DEPOSIT_AUTH_APPLY_ALLOWED_FIELDS = new HashSet<String>(){{
        addAll(Arrays.asList(
                "service_id", "category_id", "authorization_code"
        ));
    }};

    /**
     * 芝麻先享信用服务开通/授权查询接口所允许的字段
     */
    public static final Set<String> DEPOSIT_AUTH_QUERY_ALLOWED_FIELDS = new HashSet<String>(){{
        addAll(Lists.newArrayList(
                "credit_agreement_id", "authorization_code"
        ));
    }};

    public static final String ORDER_STATUS_PAUSED = "PAUSED"; //已暂停
    public static final String ORDER_STATUS_ORDERING = "ORDERING"; //下单中
    public static final String ORDER_STATUS_ORDERED = "ORDERED";//下单成功
    public static final String ORDER_STATUS_PAID = "PAID"; // 已支付
    public static final String ORDER_STATUS_REFUNDED = "REFUNDED"; // 已退款
    public static final String ORDER_STATUS_PAY_FAILED = "PAY_FAILED"; // 支付失败
    public static final String ORDER_STATUS_UNCREATED = "UNCREATED"; //未生成订单
    public static final String ORDER_STATUS_CANCEL = "CANCEL"; // 已取消

    public static final String SUBSCRIPTION_STATUS_SUBSCRIBING = "SUBSCRIBING"; //订购中
    public static final String SUBSCRIPTION_STATUS_NORMAL = "NORMAL";   // 正常
    public static final String SUBSCRIPTION_STATUS_PAUSED = "PAUSED";   //暂停订购
    public static final String SUBSCRIPTION_STATUS_SURRENDER = "SURRENDER"; // 已解约
    public static final String SUBSCRIPTION_STATUS_END = "END"; // 已完结
    public static final String SUBSCRIPTION_STATUS_CANCEL = "CANCEL"; // 超时取消
    public static final String SUBSCRIPTION_STATUS_DEFAULT_CANCEL = "DEFAULT_CANCEL"; // 违约取消
    
}
