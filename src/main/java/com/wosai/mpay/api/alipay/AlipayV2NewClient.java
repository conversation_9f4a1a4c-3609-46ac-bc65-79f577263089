package com.wosai.mpay.api.alipay;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;

import com.wosai.mpay.api.weixin.BusinessFields;
import com.wosai.mpay.api.weixin.ResponseFields;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.api.SSLEnvFlag;
import com.wosai.mpay.api.nucc.ProtocolFields;
import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.pantheon.util.MapUtil;

/**
 *
 */
public class AlipayV2NewClient {
    private static final Logger logger = LoggerFactory.getLogger(AlipayV2NewClient.class);
    protected static final ObjectMapper om = new ObjectMapper();

    public static final String SIGN_TYPE_ALIPAY = "alipay";
    public static final String SIGN_TYPE_NUCC = "nucc";
    public static final String SIGN_TYPE_UNIONPAY = "unionpay";
    public static final String SIGN_TYPE_HAIKE = "haike";
    public static final String SIGN_TYPE_RSA2 = "RSA2";
    public static final String SIGN_TYPE_SM2 = "SM2";

    protected int connectTimeout = 1000;
    protected int readTimeout = 5000;


    public Map<String, Object> call(String gatewayUrl, String signType, String privateKey, Map<String, String> request) throws MpayException, MpayApiNetworkError {
        preProcess(signType, privateKey, request);
        logger.debug("request {}", request);
        //由于某些支付通道的测试环境会出现证书不符等各种异常的情况，故此处做特殊处理
        SSLContext sslContext = SSLEnvFlag.turnOffSSl() ? SSLUtil.getUnsafeSSLContext() : null;
        HostnameVerifier hostnameVerifier = SSLEnvFlag.getNotVerifyHostNames().size() > 0 ? SSLUtil.getUnsafeHostnameVerifier(SSLEnvFlag.getNotVerifyHostNames()) : null;
        String resp = HttpClientUtils.doPost(AlipayV2NewClient.class.getName(), sslContext, hostnameVerifier, gatewayUrl, request, AlipayConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        logger.debug("response {}", resp);
        return postProcess(resp);
    }

    public static void preProcess(String signType, String privateKey, Map<String, String> request) throws MpayException {
        request.remove(ProtocolV2Fields.SIGN);

        for(Object mapKey: request.keySet().toArray()){
            if(request.get(mapKey) == null || "".equals(request.get(mapKey))){
                request.remove(mapKey);
            }
        }
        String sign = null;
        if (SIGN_TYPE_ALIPAY.equals(signType)) {
            sign = AlipaySignature.rsaSign(request, privateKey, AlipayConstants.CHARSET_UTF8);
        } else if (SIGN_TYPE_NUCC.equals(signType) || SIGN_TYPE_UNIONPAY.equals(signType) || SIGN_TYPE_RSA2.equals(signType)) {
            sign = RsaSignature.sign(request, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, privateKey);
        }else if(SIGN_TYPE_SM2.equals(signType)) {
            String userId = MapUtil.getString(request, ProtocolV2Fields.CERT_ID);
            String signContent = RsaSignature.getSignCheckContent(request);
            try {
                sign = SM2Util.unionpaySign(userId, privateKey, signContent);
            } catch (Exception e) {
                throw new MpayException("签名失败", e);
            }
        }
        ////海科走前置机加签, 故 sign 上送 null
        request.put(ProtocolV2Fields.SIGN, sign);
    }

    public static Map<String,Object> postProcess(String response) throws MpayApiUnknownResponse {
        Map<String, Object> result = null;
        try {
            result = om.readValue(response, new TypeReference<Map<String,Object>>() {});
        } catch (IOException e) {
            throw new MpayApiUnknownResponse("invalid alipay response", e);
        }

        Map<String, Object> resp = null;
        for (String key: result.keySet()) {
            if (key.endsWith(ProtocolV2Fields.RESP_SUFFIX)){
                resp = (Map<String,Object>)result.get(key);
            }
        }
        if (resp == null) {
            throw new MpayApiUnknownResponse("invalid alipay response");
        }
        return resp;

    }


    public String buildH5RedirectUrl(String gatewayUrl, String signType, String privateKey, Map<String, String> request) throws MpayException, IOException {
        return gatewayUrl+"?"+buildRequestParams(signType,privateKey,request);
    }

    public String buildRequestParams(String signType, String privateKey, Map<String, String> request) throws MpayException, IOException {
        request.remove(ProtocolV2Fields.SIGN);
        for(Object mapKey: request.keySet().toArray()){
            if(request.get(mapKey) == null || "".equals(request.get(mapKey))){
                request.remove(mapKey);
            }
        }
        String sign = null;
        if(SIGN_TYPE_RSA2.equals(signType)){
            sign = RsaSignature.sign(request, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, privateKey);
        }else{
            sign = AlipaySignature.rsaSign(request, privateKey, AlipayConstants.CHARSET_UTF8);
        }
        request.put(ProtocolV2Fields.SIGN, sign);
        return WebUtils.buildQuery(request,AlipayConstants.CHARSET_UTF8);
    }


    public static void main(String[] args) throws MpayException, BuilderException, MpayApiNetworkError, NoSuchAlgorithmException {
//        testPay();
//        testNuccPay();
//        testQuery();
//        testLklUnionQuery();
//        testZmAuthApply();
        testZmQuery();
    }


    public static void testPay() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayV2NewClient client = new AlipayV2NewClient();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, AlipayConstants.SIGN_TYPE_RSA);
        builder.set(ProtocolV2Fields.APP_ID, AlipayV2Config.APP_ID);
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_TRADE_TRADE);
//        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN,"234234234");
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO,"78924567890978999");
        builder.bizSet(BusinessV2Fields.SCENE,AlipayConstants.SCENE_BAR_CODE);
        builder.bizSet(BusinessV2Fields.AUTH_CODE,"284958077078424984");
        builder.bizSet(BusinessV2Fields.TOTAL_AMOUNT,"0.01");
        builder.bizSet(BusinessV2Fields.UNDISCOUNTABLE_AMOUNT,"0.01");
        builder.bizSet(BusinessV2Fields.SUBJECT,"甜甜圈");
        builder.bizSet(BusinessV2Fields.BODY,"what r u 弄啥嘞");
        //builder.bizSet(BusinessV2Fields.GOODS_DETAIL,"");
        builder.bizSet(BusinessV2Fields.OPERATOR_ID,"Rain");
        //builder.bizSet(BusinessV2Fields.STORE_ID,"49");
        //builder.bizSet(BusinessV2Fields.TERMINAL_ID,"345345");
        //builder.bizSet(BusinessV2Fields.EXTEND_PARAMS,"{\"sys_service_provider_id\":\"2015102000490218\"}");
        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call(AlipayV2Config.GATEWAY, SIGN_TYPE_ALIPAY, AlipayV2Config.PRIVATE_KEY, request);
        System.out.println(result);


    }

    public static void testNuccPay() throws BuilderException, MpayException, MpayApiNetworkError {
        String privateKey = ("MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDNoT4bYV6XTVo+\n" +
                "ghU80kH6dPcGkOfjs+aRyfXIGUMghNcSvuWK8jkNqC7+mEs+fjFOeRGfAoB5R9OY\n" +
                "ryvPhxM0BHFXhJ0884yvitwVYVT0YrKiYq8Rb1tN5KHtRl5NTKonOtWPWryoBz51\n" +
                "qzTZFQU0v2mKCrB8fGClR429vVSxPs7OERCWV4BuiTFW2+o1R7IfFX0+MB9tKgWK\n" +
                "VOuXK3gpP2jOkqTyPqJl+NumK/S7QE7gtcUmh+4wuDKUbxAGfPSOrEQMknnRujzi\n" +
                "NX8w9tV9hGIrhqERaVw0lxYvdifqoWlHBfnZagVqoSMgfJVMr60XGSCWIdZvog7u\n" +
                "07K2jiApAgMBAAECggEAadtaJ6pGkclmbct1t2veP1s7WAv89IHGbsLzXmFy5yi7\n" +
                "t5DChncP2/H6z2IDSlaYd3doFN2q2cSaL33uJdW5kwu+dXl1oM0YXb40cmU64Nx2\n" +
                "bZYz3dvfuwe6PHX/UffnmG53RmdFu5KPGvBaMm7Z0T/AyymZlVxKPIvLciQnl157\n" +
                "tFHCz5ocGgoLb0khlp8j/3P2VOOk6pJbMmwx38PtQp25OBzxotl42mRoF8gHNBXH\n" +
                "lpYbcjZLmXE/7TX/YvBd0+VA0C/o1lYSto5vhr0cx7l37E1RN1W7ZIuSokmh/LIc\n" +
                "xvFxlBTXzws7LTUz/4X1/7oCME5LDcn3jGskw7iDoQKBgQDrm9bviWuBAey69W9T\n" +
                "gpP9eum+/hUsWGBE55/JtA4FTFq15T1EO9dMWrvX6M6PnLiAJpI8ZAFF+MdBqQUm\n" +
                "EyxKIILjg+dMRwf6aoddqHt3MOXVfyZxLy+LeVYHMlgHo5SqUfDZM/LPk0ARF8TJ\n" +
                "TdpTG/*******************************/K5DZg3mn71uwcnCB5nwdu2rhWC\n" +
                "/FcZ3+oZ7cLnWFAJrloirMo8kk9tAoIx6cM+ZWy1PccD5qYsHBQ+xX1uJpjtyVmj\n" +
                "78mk3tekAqp69wf1c7lRDj46Fsci8ivE6dXqn81JwLbAS0anAa/PbbNMBietHtCA\n" +
                "gomjHPksvQKBgC1re4HuAfV6w4I/MljtAU6KVWlmXfqQhu6BoIIn3dQTpiEOskLn\n" +
                "Llgf3bp/vOJemgrKZMKTnNM6ZF86EC4I4C7iGZl2oi5IOzeBNdtOUY7mtEf2HRkQ\n" +
                "uZ+vom/8uo+ub0huR3n308VEY8Nny53rDj2bVb1r7rSx8yIkidXMC8tZAoGBANoB\n" +
                "haHiLLlKPYuL0XPeUWVRc7GUI9nFVTIhHLAfnyI7r+Did/5qRILvDMo7jQp11yny\n" +
                "yVK5zm+uXuE8jkod/9ccBn6TZMcon7HpiFy6H7ll7IjdP8PNbjZb6nXtwdMkb3bN\n" +
                "H7C2yq27P9az3LWaXLzOcpOsscwndBTgyoIBBFUVAoGAWT2kZbf9N7Hk5+nttbsI\n" +
                "OREa8wgrL9ins9HO+eaFZ10o7VF4q9JBLRZxA4YQ8fDWv+hl8lhJs8tOB8qPMz/W\n" +
                "lQWA6EWrmi/ddc0tLgqbh0ovGtYdio55hxaUtjtkjPP2ubvRgqh9QtEy2JsEDgpw\n" +
                "aVnpxZtZdtxGfiTlkjj/XJ4=").replaceAll("\n", "");
        AlipayV2NewClient client = new AlipayV2NewClient();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.CHARSET, "utf-8");
        builder.set(ProtocolV2Fields.SIGN_TYPE, AlipayConstants.SIGN_TYPE_RSA);
        builder.set(ProtocolFields.PID, "2088421809493486");
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_TRADE_TRADE);
//        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN,"234234234");
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO,"789245678909789991");
        builder.bizSet(ProtocolFields.IDC_FLAG,"77");
        builder.bizSet(BusinessV2Fields.SCENE,AlipayConstants.SCENE_BAR_CODE);
        builder.bizSet(BusinessV2Fields.AUTH_CODE,"284958077078424984");
        builder.bizSet(BusinessV2Fields.TOTAL_AMOUNT,"0.01");
        builder.bizSet(BusinessV2Fields.UNDISCOUNTABLE_AMOUNT,"0.01");
        builder.bizSet(BusinessV2Fields.SUBJECT,"甜甜圈");
        builder.bizSet(BusinessV2Fields.BODY,"what r u 弄啥嘞");
        //builder.bizSet(BusinessV2Fields.GOODS_DETAIL,"");
        builder.bizSet(BusinessV2Fields.OPERATOR_ID,"Rain");
        //builder.bizSet(BusinessV2Fields.STORE_ID,"49");
        //builder.bizSet(BusinessV2Fields.TERMINAL_ID,"345345");
        builder.bizSet(BusinessV2Fields.EXTEND_PARAMS, Collections.singletonMap("sys_service_provider_id", "2015102000490218"));
        builder.bizSet(BusinessV2Fields.SUB_MERCHANT,Collections.singletonMap("merchant_id", "2088131391519832"));
        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call("https://**************:9443/gateway/alipay", SIGN_TYPE_NUCC, privateKey, request);
        System.out.println(result);


    }

    public static  void testQuery() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayV2NewClient client = new AlipayV2NewClient();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, AlipayConstants.SIGN_TYPE_RSA);
        builder.set(ProtocolV2Fields.APP_ID, AlipayV2Config.APP_ID);
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_TRADE_QUERY);
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, "202302BB285cc045bdf44edeabfc2d33a4c48A96");
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO,"7895281119572558");
//        builder.bizSet(BusinessV2Fields.QUERY_OPTIONS, Arrays.asList("hbfq", "fund_bill_list"));
        Map<String,String> request =builder.build();
        Map<String,Object> result = client.call(AlipayV2Config.GATEWAY, SIGN_TYPE_ALIPAY, AlipayV2Config.PRIVATE_KEY, request);
        System.out.println(result);

    }

    public static  void testZmQuery() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayV2NewClient client = new AlipayV2NewClient();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, AlipayConstants.SIGN_TYPE_RSA);
        builder.set(ProtocolV2Fields.APP_ID, AlipayV2Config.APP_ID);
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_ZHIMA_CREDIT_PAYAFTERUSE_CREDITBIZORDER_QUERY);
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, "");
        builder.bizSet(BusinessV2Fields.OUT_ORDER_NO,"7894259268757072");
//        builder.bizSet(BusinessV2Fields.QUERY_OPTIONS, Arrays.asList("hbfq", "fund_bill_list"));
        Map<String,String> request =builder.build();
        Map<String,Object> result = client.call(AlipayV2Config.GATEWAY, SIGN_TYPE_ALIPAY, AlipayV2Config.PRIVATE_KEY, request);
        System.out.println(result);

    }
    
    public static  void testLklUnionQuery() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayV2NewClient client = new AlipayV2NewClient();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, AlipayConstants.SIGN_TYPE_RSA2);
        builder.set(ProtocolV2Fields.APP_ID, "1266000048220000");
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_TRADE_QUERY);
        builder.set(ProtocolV2Fields.CERT_ID, "4100064787");
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO,"7895055139958050");
        builder.bizSet("query_options", Arrays.asList("hbfq", "fund_bill_list"));
        builder.bizSet(BusinessV2Fields.SUB_MERCHANT, MapUtil.hashMap(BusinessV2Fields.MERCHANT_ID, "2088610528550930"));
        builder.bizSet(BusinessV2Fields.EXTRA_PARAM, MapUtil.hashMap(BusinessV2Fields.SECONDARY_MERCHANT_ID, "2088610528550930", BusinessV2Fields.REQUEST_ORG_ID, "2088421809493486"));
        builder.bizSet(BusinessV2Fields.EXTEND_PARAMS, MapUtil.hashMap(BusinessV2Fields.EXTEND_PARAMS_SYS_SERVICE_PROVIDER_ID, "2088421809493486"));
        builder.bizSet(BusinessV2Fields.ORG_PID, "2088421809493486");

        Map<String,String> request =builder.build();
        Map<String,Object> result = client.call("https://apay1.95516.com/ali/aligateway", SIGN_TYPE_UNIONPAY, "", request);
        System.out.println(result);

    }

    public static void testZmAuthApply() throws BuilderException, MpayException, MpayApiNetworkError, NoSuchAlgorithmException {

        String privateKey = "";
        AlipayV2NewClient client = new AlipayV2NewClient();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, AlipayConstants.SIGN_TYPE_RSA2);
        builder.set(ProtocolV2Fields.APP_ID, "2015102000490218");
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, "");


        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_ZHIMA_CREDIT_PAYAFTERUSE_CREDITAGREEMENT_SIGN);


        String outAgreementNo = Digest.md5(String.valueOf(System.currentTimeMillis()).getBytes(StandardCharsets.UTF_8));
        //商户外部协议号
        builder.set(BusinessV2Fields.OUT_AGREEMENT_NO, outAgreementNo);

        //芝麻信用服务ID
        builder.bizSet(BusinessV2Fields.ZM_SERVICE_ID, "");
        //芝麻外部类目
        builder.bizSet(BusinessV2Fields.ZM_CATEGORY_ID, "pay_for_portable_battery_0000");


        Map<String,String> request =builder.build();
        System.out.println(JsonUtil.objectToJsonString(request));

        Map<String, Object> result = new HashMap<>();
        String responseBodyStr = "";
        try {
            request = builder.build();

            request.put(AlipayConstants.FORMAT, AlipayConstants.FORMAT_JSON);
            AlipayV2NewClient.preProcess(AlipayV2NewClient.SIGN_TYPE_RSA2, privateKey, request);
            for(String key : request.keySet()) {
                request.put(key, URLEncoder.encode(request.get(key), AlipayConstants.CHARSET_UTF8));
            }
            responseBodyStr = RsaSignature.getSignCheckContent(request);
        }catch (Exception ex) {
            logger.error("failed to call alipayV2 auth apply");
            throw new RuntimeException("alipayV2 deposit auth apply error", ex);
        }

        if (!StringUtils.isEmpty(outAgreementNo)) {
            result.put(BusinessFields.AUTHORIZATION_CODE, outAgreementNo);
            result.put(ResponseFields.APPLY_PERMISSIONS_TOKEN, responseBodyStr);
        }

        System.out.println(JsonUtil.objectToJsonString(result));

    }

    public static void testZmPreFreeze() throws BuilderException, MpayException, MpayApiNetworkError, NoSuchAlgorithmException {

        String privateKey = "";
        AlipayV2NewClient client = new AlipayV2NewClient();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, AlipayConstants.SIGN_TYPE_RSA2);
        builder.set(ProtocolV2Fields.APP_ID, "2015102000490218");
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, "");


        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_ZHIMA_CREDIT_PAYAFTERUSE_CREDITBIZORDER_ORDER);


        String outAgreementNo = Digest.md5(String.valueOf(System.currentTimeMillis()).getBytes(StandardCharsets.UTF_8));
        //商户外部协议号
        builder.set(BusinessV2Fields.OUT_AGREEMENT_NO, outAgreementNo);

        //商户外部订单号
        builder.bizSet(BusinessV2Fields.OUT_ORDER_NO, System.currentTimeMillis() + "");
        //订单金额
        builder.bizSet(BusinessV2Fields.ORDER_AMOUNT, "1");
        builder.bizSet(BusinessV2Fields.AMOUNT_TYPE, AlipayConstants.AMOUNT_TYPE_ORDER_AMOUNT);
        //芝麻信用服务ID
        builder.bizSet(BusinessV2Fields.ZM_SERVICE_ID, "");
        //芝麻外部类目
        builder.bizSet(BusinessV2Fields.ZM_CATEGORY_ID, "pay_for_portable_battery_0000");
        //产品码
        builder.bizSet(BusinessV2Fields.PRODUCT_CODE, AlipayConstants.PRODUCT_CODE_CREDIT_PAY_AFTER_USE);
        //订单标题
        builder.bizSet(BusinessV2Fields.SUBJECT, "subject");
        //订单描述
        builder.bizSet(BusinessV2Fields.BODY, "body");


        Map<String,String> request =builder.build();
        System.out.println(JsonUtil.objectToJsonString(request));
        Map<String,Object> result = client.call(AlipayV2Config.GATEWAY, SIGN_TYPE_RSA2, privateKey, request);

        System.out.println(JsonUtil.objectToJsonString(result));

    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
}
