package com.wosai.mpay.api.alipay;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Random;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiReadError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.AlipaySignature;
import com.wosai.mpay.util.StringUtils;
import com.wosai.mpay.util.WebUtils;
import com.wosai.mpay.util.XmlUtils;

public class WapClient {
    private static final Logger logger = LoggerFactory.getLogger(WapClient.class);
    
    private static final Pattern fieldSep = Pattern.compile("&");
    private static final Pattern kvSep = Pattern.compile("=");

    private String charset = AlipayConstants.CHARSET_UTF8;
    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }
    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    private void sign(String partner, String key, Map<String, String> request) throws MpayException {
        request.put(WapFields.PARTNER, partner);
        request.put(WapFields.INPUT_CHARSET, charset.toLowerCase());
        request.put(WapFields.SEC_ID, AlipayConstants.WAP_SEC_ID_MD5);
        String sign = AlipaySignature.md5Sign(request, key, charset);
        request.put(WapFields.SIGN, sign);
        
    }

    public Map<String, Object> call(String partner, String key, Map<String, String> request) throws MpayException, MpayApiNetworkError {

        sign(partner, key, request);

        logger.debug("request {}", request);
        String resp = WebUtils.doPost(null, null, WapConfig.GATEWAY, request, charset, connectTimeout, readTimeout);
        logger.debug("response {}", resp);
        if (StringUtils.isEmpty(resp)) {
            throw new MpayApiUnknownResponse(resp);
        }
        
        Map<String, Object> result = new LinkedHashMap<String, Object>();
        String[] fields = fieldSep.split(resp);
        for (String field: fields) {
            String[] kv = kvSep.split(field);
            if (kv.length != 2) {
                continue;
            }
            String k = kv[0];
            String v = kv[1];
            try {
                v = URLDecoder.decode(v, charset);
            }
            catch (UnsupportedEncodingException e) {
                throw new MpayApiReadError("failed to read alipay legacy wap pay response", e);
            }
            if (WapFields.RES_DATA.equals(k) || WapFields.RES_ERROR.equals(k)) {
                result.put(k, XmlUtils.parse(v));
            }else{
                result.put(k, v);
            }
        }
        return result;
    }
    
    public String buildQuery(String partner, String key, Map<String, String> request) throws MpayException, MpayApiNetworkError {
        sign(partner, key, request);

        logger.debug("request {}", request);
        try {
            return WebUtils.buildQuery(request, charset);
        }
        catch (IOException e) {
            throw new MpayApiConnectError("failed to build alipay legacy wap query string", e);
        }
    }

    private static String generateTradeNo() {
        Random random = new Random();
        return String.format("%d%d", System.currentTimeMillis(), random.nextInt(10000));
    }

    public static void main(String[] args) throws Exception {
        WapRequestBuilder builder = new WapRequestBuilder();
        builder.setReqDataRoot(WapFields.DIRECT_TRADE_CREATE_REQ);
        builder.set(WapFields.SERVICE, WapMethods.ALIPAY_WAP_TRADE_CREATE_DIRECT);
        builder.setReqData(WapFields.SUBECT, "wap测试");
        String outTradeNo = generateTradeNo();
        builder.setReqData(WapFields.OUT_TRADE_NO, outTradeNo);
        builder.setReqData(WapFields.TOTAL_FEE, "10.01");
        builder.setReqData(WapFields.SELLER_ACCOUNT_NAME, WapConfig.SQB_SELLER_ACCOUNT_NAME);
        builder.setReqData(WapFields.CALL_BACK_URL, "http://api.coupon.dev.wosai.cn/AlipayC");
        builder.setReqData(WapFields.NOTIFY_URL, "http://api.coupon.dev.wosai.cn/AlipayC");
        builder.setReqData(WapFields.MERCHANT_URL, "http://www.wosai.cn");
        builder.set(WapFields.REQ_ID, outTradeNo);
        
        Map<String, String> request = builder.build();
        WapClient client = new WapClient();
        Map<String, Object> result = client.call(WapConfig.SQB_PARTNERID, WapConfig.SQB_APPKEY, request);

        System.out.println(result);
    }
}
