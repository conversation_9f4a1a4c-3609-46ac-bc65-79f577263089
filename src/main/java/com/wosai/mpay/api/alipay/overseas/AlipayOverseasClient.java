package com.wosai.mpay.api.alipay.overseas;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;

import com.wosai.mpay.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wosai.mpay.exception.AlipayV2Exception;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.AlipaySignature;
import com.wosai.mpay.util.WebUtils;
import com.wosai.mpay.util.XmlUtils;

public class AlipayOverseasClient {
    public static final Logger logger = LoggerFactory.getLogger(AlipayOverseasClient.class);
    private int connectTimeout = 3000;
    private int readTimeout = 15000;
    private String charset = AlipayOverseasConstants.DEFAULT_INPUT_CHARSET;

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }
    
    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
    
    public void setCharset(String charset) {
        this.charset = charset;
    }
    
    public Map<String,Object> call(String gatewayUrl, String appId, String signType, String privateKey, Map<String,String> request) throws MpayException, MpayApiNetworkError {
        signType = StringUtils.isEmpty(signType) ? AlipayOverseasConstants.SIGN_TYPE_MD5 : signType;

        request.put(ProtocolFields.PARTNER, appId);
        request.put(ProtocolFields.INPUT_CHARSET,charset);
        request.remove(ProtocolFields.SIGN);
        request.remove(ProtocolFields.SIGN_TYPE);
    
        for (Object mapKey : request.keySet().toArray()) {
            if (request.get(mapKey) == null || "".equals(request.get(mapKey))) {
                request.remove(mapKey);
            }
        }
        String sign = getSign(request, signType, privateKey,charset);
        request.put(ProtocolFields.SIGN, sign);
        if (!AlipayOverseasConstants.SIGN_TYPE_MD5.equals(signType)) {
            request.put(ProtocolFields.SIGN_TYPE, signType);
        }

        logger.debug("request {}", request);
        try {
            String resp = WebUtils.doPost(null, null, gatewayUrl, request, charset, connectTimeout, readTimeout);
            logger.debug("response {}",resp.replaceAll("\\n", ""));
            Map<String, Object> response = XmlUtils.parse(resp);
            if (response == null) {
                throw new AlipayV2Exception("99999", "invalid alipay response", null, null);
            }
            return response;
        } catch (IOException e) {
            throw new AlipayV2Exception("99999", "IO error invoking alipay api", null, null, e);
        }
    }
    
    public static String getSign(Map<String,String> content, String signType, String key,  String charset) throws MpayException {
        ArrayList<String> list = new ArrayList<String>();
        for(Map.Entry<String,String> entry:content.entrySet()){
            if(entry.getValue()!=""){
                list.add(entry.getKey() + "=" + entry.getValue() + "&");
            }
        }
        int size = list.size();
        String [] arrayToSort = list.toArray(new String[size]);
        Arrays.sort(arrayToSort, String.CASE_INSENSITIVE_ORDER);
        StringBuilder sb = new StringBuilder();
        for(int i = 0; i < size; i ++) {
            sb.append(arrayToSort[i]);
        }
        String result = sb.substring(0, sb.length() - 1);
        if (AlipayOverseasConstants.SIGN_TYPE_MD5.equals(signType)) {
            result = AlipaySignature.md5Sign(result, key, charset);
        } else if(AlipayOverseasConstants.SIGN_TYPE_RSA.equals(signType)) {
            result = AlipaySignature.rsaSign(result, key, charset);
        } else if(AlipayOverseasConstants.SIGN_TYPE_RSA2.equals(signType)) {
            result = AlipaySignature.rsa256Sign(result, key, charset);
        } else {
            throw new MpayException("invalid sign type");
        }
        return result;
    }
}
