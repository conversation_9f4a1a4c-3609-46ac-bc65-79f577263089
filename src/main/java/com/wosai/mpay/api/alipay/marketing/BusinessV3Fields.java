package com.wosai.mpay.api.alipay.marketing;

public abstract class BusinessV3Fields {

    public static final String OUT_OPEN_ID = "out_open_id";
    public static final String APP_SOURCE = "app_source";
    public static final String BIZ_SCENE = "biz_scene";//业务场景，用于区分商户具体的咨场景，示例：ALIPAY_PROMO
    public static final String OUT_TRADE_NO = "out_trade_no";//商户订单号,64个字符以内、可包含字母、数字、下划线；需保证在商户端不重复
    public static final String TOTAL_AMOUNT = "total_amount";//订单总金额，单位为元，精确到小数点后两位，取值范围[0.01,100000000]（total_amount与undiscountable_amount两个字段需至少有一个不能为空）
    public static final String EXT_SECONDARY_MERCHANT_ID = "secondaryMerchantId";
    public static final String OPERATION_LIST = "operation_list";
    public static final String EXT_PARAMS = "ext_params";
    public static final String EXT_APP_SOURCE = "appSource";
    public static final String EXT_OUT_OPEN_ID = "outOpenId";

    public static final String MERCHANT_NAME = "merchant_name";
    public static final String BIZ_LINK = "biz_link";
    public static final String EXPIRE_TIME = "expire_time";
    public static final String SUB_MERCHANT_ID = "sub_merchant_id";
    public static final String CHANNEL_INFO = "channel_info";
    public static final String OUT_BIZ_NO = "out_biz_no";
    public static final String PAY_AMOUNT = "pay_amount";

    public static final String GUIDE_TEXT_1 = "guide_text_1";
    public static final String GUIDE_TEXT_2 = "guide_text_2";

    public static final String EXPIRE_DATE = "expire_date";
    public static final String SHARE_TOKEN = "share_token";

    public static final String USER_ID = "user_id";
    public static final String PRODUCT_CODE = "product_code";
    public static final String GOODS_DETAIL = "goods_detail";
    public static final String EXTEND_PARAMS = "extend_params";


}
