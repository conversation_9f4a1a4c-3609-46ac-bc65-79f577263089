package com.wosai.mpay.api.alipay.intl;

public class ProtocolFields {
    public static final String SERVICE = "service";                         //Interface name
    public static final String SIGNATURE = "signature";                               //Signature value
    public static final String SIGN_TYPE = "sign_type";                     //The type could be DSA, RSA or MD5; Upper case only.
    public static final String PARTNER = "partner";                         //Assigned by Alipay once the contract is ready, unique identifier in Alipay system to mark each partner and its related products that enabled with it
    public static final String INPUT_CHARSET = "_input_charset";            //Character set of parameter code; GBK by default.
    public static final String TIMESTAMP = "timestamp";                     //Time stamp of the merchant server sending request, accurate to the millisecond.
    public static final String TERMINAL_TIMESTAMP = "terminal_timestamp";   //Time stamp of the terminal sending request, accurate to the millisecond.
    public static final String NOTIFY_URL = "notify_url";                   //The url to which <PERSON><PERSON><PERSON> sends payment result in async mode. It is in POST method.
    

}
