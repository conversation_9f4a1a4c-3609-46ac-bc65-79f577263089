package com.wosai.mpay.api.alipay;

import java.io.ByteArrayOutputStream;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeMap;

import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.util.XmlUtils;


public class WapRequestBuilder {
    private Map<String, String> request;
    private Map<String, Object> reqData;
    private String rootTag = "xml";

    public WapRequestBuilder () {
        request = new TreeMap<String, String>();
        reqData = new LinkedHashMap<String, Object> ();
        
        request.put(WapFields.PARTNER, AlipayV1Config.SQB_PARTNERID);
        request.put(WapFields.FORMAT, "xml");
        request.put(WapFields.VERSION, "2.0");
    }

    public Map<String, String> build() throws BuilderException {
        if (!request.containsKey(WapFields.SERVICE)) {
            throw new BuilderException("alipay wap request missing service param");
        }
        if (!request.containsKey(WapFields.REQ_ID)) {
            throw new BuilderException("alipay wap request missing req_id");
        }
        if (reqData.isEmpty()) {
            throw new BuilderException("missing req_data");
        }

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            String openTag = String.format("<%s>", rootTag);
            String closeTag = String.format("</%s>", rootTag);

            baos.write(openTag.getBytes("UTF-8"));
            XmlUtils.writeMulti(reqData, "UTF-8", baos);
            baos.write(closeTag.getBytes("UTF-8"));

            request.put(WapFields.REQ_DATA, new String(baos.toByteArray(), "UTF-8"));
        }
        catch (Exception ex) {
            throw new BuilderException("alipay wap req_data error", ex);
        }

        return request;
    }
    
    public void set(String field, String value) {
        request.put(field,  value);
    }
    
    public void setReqDataRoot(String root) {
        this.rootTag = root;
    }
    public void setReqData(String field, String value) {
        reqData.put(field, value);
    }
}
