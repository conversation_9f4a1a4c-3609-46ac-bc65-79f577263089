package com.wosai.mpay.api.mcash;

import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;
import com.wosai.mpay.api.mcash.MCashConstants.MCashMethodEnum;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Base64;
import java.util.Map;



/**
 * <AUTHOR> Date: 2020/3/12 Time: 2:19 下午
 */
public class MCashClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(MCashClient.class);

    public int connectTimeout = 3000;
    public int readTimeout = 15000;

    public MCashClient() {
    }

    public MCashClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map<String,Object> call(String gateway, String key, MCashMethodEnum mCashMethodEnum, Map<String,Object> request)
            throws MpayException, MpayApiNetworkError {
        String signContent = getSignContent(key, mCashMethodEnum, request);
        String sign = Base64.getEncoder().encodeToString(Hashing.sha1().hashString(signContent, Charsets.UTF_8).asBytes());
        request.put(BusinessFields.HASH, sign);

        LOGGER.debug("MCashClient request: {}", JsonUtil.objectToJsonString(request));
        String responseStr = HttpClientUtils.doPost(MCashClient.class.getName(), null, null, gateway, request
                , "utf-8", connectTimeout, readTimeout);
        LOGGER.debug("MCashClient response: {}", responseStr);

        try{
            Map<String,Object> result = JsonUtil.jsonStringToObject(responseStr, Map.class);
            return result;
        }catch (Exception e){
            throw new MpayApiUnknownResponse("parse response error: " + e.getMessage(), e);
        }
    }


    private static String getSignContent(String key, MCashMethodEnum mCashMethodEnum, Map<String, Object> request) {
        String type = String.valueOf(request.get(BusinessFields.TYPE));
        String trxId = String.valueOf(request.get(BusinessFields.TRX_ID));
        String amount = String.valueOf(request.get(BusinessFields.AMOUNT));
        String merchant = String.valueOf(request.get(BusinessFields.MERCHANT));
        String expiredIn = String.valueOf(request.get(BusinessFields.EXPIRED_IN));
        String qrcode = String.valueOf(request.get(BusinessFields.QRCODE));

        StringBuilder signContentBuilder = new StringBuilder();
        switch (mCashMethodEnum) {
            case METHOD_BSC_PAY:
                signContentBuilder.append(key).append(type).append(trxId).append(qrcode).append(amount).append(merchant);
                break;
            case METHOD_CSB_PAY:
                signContentBuilder.append(key).append(type).append(trxId).append(expiredIn).append(amount).append(merchant);
                break;
            case METHOD_WAP_PAY:
                signContentBuilder.append(key).append(type).append(trxId).append(amount).append(merchant);
                break;
            case METHOD_QUERY:
            case METHOD_QUERY_V2:
                signContentBuilder.append(key).append(type).append(trxId).append(merchant);
                break;
            case METHOD_CANCEL:
                signContentBuilder.append(key).append(trxId).append(merchant);
                break;
        }
        return signContentBuilder.toString();
    }
}
