package com.wosai.mpay.api.mcash;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Date: 2020/3/12 Time: 11:35 上午
 */
public class MCashTest {

    private static void backendTest() throws Throwable {
        String url = "http://newbackend.mcash.my/cross/backend/entry";
//        String url = "https://www.mcash.my/cross/backend/entry";

        String trxId = System.currentTimeMillis() + "";
        Map<String, Object> request = new HashMap<>();
        request.put(BusinessFields.MERCHANT, "beez_fintech");
        request.put(BusinessFields.TYPE, MCashConstants.TYPE_PAYMENT);
        request.put(BusinessFields.TRX_ID, trxId);
        request.put(BusinessFields.AMOUNT, "1");
        request.put(BusinessFields.DESCRIPTION, "一个榴莲");
        request.put(BusinessFields.MID, "beez_fintech");
        request.put(BusinessFields.EXPIRED_IN, "5");

        System.out.println("trxId: " + trxId);

        MCashClient client = new MCashClient();
        client.setConnectTimeout(10000);
        client.setReadTimeout(10000);
        Map<String, Object> result = client.call(url, "key", MCashConstants.MCashMethodEnum.METHOD_CSB_PAY, request);
        System.out.println(result);
    }


    public static void userIdTest() throws Throwable {
        String url = "http://newbackend.mcash.my/cross/backend/entry/user";
//        String url = "https://www.mcash.my/cross/backend/entry/user";

        String trxId = System.currentTimeMillis() + "";
        Map<String, Object> request = new HashMap<>();
        request.put(BusinessFields.MERCHANT, "beez_fintech");
        request.put(BusinessFields.TYPE, MCashConstants.TYPE_PAYMENT);
        request.put(BusinessFields.TRX_ID, trxId);
        request.put(BusinessFields.AMOUNT, "1");
        request.put(BusinessFields.DESCRIPTION, "一个榴莲");
        request.put(BusinessFields.QRCODE, "APP-USER-1650788183");
        request.put(BusinessFields.MID, "beez_fintech");

        System.out.println("trxId: " + trxId);

        MCashClient client = new MCashClient();
        client.setConnectTimeout(10000);
        client.setReadTimeout(10000);
        Map<String, Object> result = client.call(url, "key", MCashConstants.MCashMethodEnum.METHOD_BSC_PAY, request);
        System.out.println(result);
    }

    public static void queryStatus() throws Throwable {
        String url = "http://newbackend.mcash.my/cross/backend/entry";
//        String url = "https://www.mcash.my/cross/backend/entry";
        Map<String, Object> request = new HashMap<>();
        request.put(BusinessFields.MERCHANT, "beez_fintech");
        request.put(BusinessFields.TYPE, MCashConstants.TYPE_CHECK_TRX);
        request.put(BusinessFields.TRX_ID, "1585277644284");

        MCashClient client = new MCashClient();
        client.setConnectTimeout(10000);
        client.setReadTimeout(10000);
        Map<String, Object> result = client.call(url, "key", MCashConstants.MCashMethodEnum.METHOD_QUERY, request);
        System.out.println(result);
    }

    public static void cancel() throws Throwable {
        String url = "http://newbackend.mcash.my/cross/backend/void";
        Map<String, Object> request = new HashMap<>();
        request.put(BusinessFields.MERCHANT, "beez_fintech");
        request.put(BusinessFields.TRX_ID, "1585277644284");

        MCashClient client = new MCashClient();
        client.setConnectTimeout(10000);
        client.setReadTimeout(10000);
        Map<String, Object> result = client.call(url, "key", MCashConstants.MCashMethodEnum.METHOD_CANCEL, request);
        System.out.println(result);
    }

    public static void query_v2() throws Throwable {
        String url = "http://newbackend.mcash.my/cross/transaction/status/v2";
        Map<String, Object> request = new HashMap<>();
        request.put(BusinessFields.TYPE, "transaction_status");
        request.put(BusinessFields.MERCHANT, "beez_fintech");
        request.put(BusinessFields.TRX_ID, "1585277644284");

        MCashClient client = new MCashClient();
        client.setConnectTimeout(10000);
        client.setReadTimeout(10000);
        Map<String, Object> result = client.call(url, "key", MCashConstants.MCashMethodEnum.METHOD_QUERY, request);
        System.out.println(result);
    }


    public static void main(String... args) {
        try {
//            backendTest();
//            userIdTest();
            queryStatus();
//            cancel();
            query_v2();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
    }
}
