package com.wosai.mpay.api.mcash;

/**
 * <AUTHOR> Date: 2020/3/12 Time: 10:36 上午
 */
public class MCashConstants {
    public static final String TYPE_PAYMENT = "payment";
    public static final String TYPE_CHECK_TRX = "check_trx";
    public static final String TYPE_TRANSACTION_STATUS = "transaction_status";

    public static final int STATUS_SUCCESS = 1;
    public static final int STATUS_FAIL = 0;

    public static final String QUERY_V2_STATUS_SUCCESS = "TRANSACTION SUCCESS";
    public static final String QUERY_V2_STATUS_FAIL = "TRANSACTION FAILED";

    public static final String QUERY_V2_TYPE_PAYMENT = "payment";
    public static final String QUERY_V2_TYPE_VOID = "void";



    public enum MCashMethodEnum {
        METHOD_BSC_PAY, METHOD_CSB_PAY, METHOD_WAP_PAY, METHOD_QUERY, METHOD_CANCEL, METHOD_QUERY_V2
    }

}
