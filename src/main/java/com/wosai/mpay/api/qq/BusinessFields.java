package com.wosai.mpay.api.qq;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/3/2.
 */
public class BusinessFields {

    public static final String VER = "ver";//版本号，ver默认值是1.0，目前版本ver取值应为2.0
    public static final String DESC = "desc";//商品描述，32个字符以内
    public static final String BARGAINOR_ID = "bargainor_id";//卖家财付通商户号。交易成功后，资金进入到该商户号。

    public static final String SP_BILLNO = "sp_billno";//商户系统内部的定单号，32个字符内、可包含字母
    public static final String TOTAL_FEE = "total_fee";//订单总金额，单位为分
    public static final String FEE_TYPE = "fee_type";//现金支付币种，目前只支持人民币。1：人民币

    public static final String NOTIFY_URL = "notify_url";//接收手Q支付通知的URL，需给绝对路径，255字符内格式如:http://wap.tenpay.com/tenpay
    public static final String ATTACH = "attach";//商户附加信息，可做扩展参数，127字符内
    public static final String TIME_START = "time_start";//订单生成时间，格式为yyyymmddhhmmss，如2009年12月25日9点10分10秒表示为20091225091010。时区为GMT+8 beijing。该时间取自商户服务器
    public static final String TIME_EXPIRE = "time_expire";//订单失效时间，格式同上
    public static final String AUTH_CODE = "auth_code";//授权码
    public static final String SP_DEVICE_ID = "sp_device_id";//POS设备的ID，作为唯一标识
    public static final String COUPON_ID = "coupon_id";//需要为当前交易指定优惠资格时，可传入该字段
    public static final String PAY_CHANNEL = "pay_channel";//目前固定为1。
    public static final String SIGN_SP_ID = "sign_sp_id";//合法的财付通商户号。说明详见：普通受理模式&sign_sp_id


    public static final String BANK_TYPE = "bank_type";//银行类型
    public static final String BANK_BILLNO = "bank_billno";//银行订单号，若为财付通余额支付则为空
    public static final String TIME_END = "time_end";//支付完成时间,格式为yyyyMMddhhmmss
    public static final String PURCHASE_ALIA = "purchase_alia";//对应买家账号的一个加密串

    public static final String PARTNER = "partner";//财付通商户号。目标订单应该是partner的实际交易。

    public static final String OUT_TRADE_NO = "out_trade_no";//商户系统内部的订单号。
    public static final String TRANSACTION_ID = "transaction_id";//财付通交易单号。


    public static final String OUT_REFUND_NO  = "out_refund_no";//商户退款单号，32个字符内、可包含字母,确保在商户系统唯一。同个退款单号多次请求，财付通当一个单处理，只会退一次款。如果出现退款不成功，请采用原退款单号重新发起，避免出现重复退款。
    public static final String REFUND_FEE  = "refund_fee";//退款总金额,单位为分,可以做部分退款
    public static final String OP_USER_ID  = "op_user_id";//操作员帐号,默认为商户号
    public static final String OP_USER_PASSWD  = "op_user_passwd";//账号密码，明文密码做MD5后的值。注意：默认情况下，请求必须传入该字段。如果要免密发起，需要事先向财付通发起邮件申请。

    public static final String REFUND_ID = "refund_id";//财付通退款单号
}
