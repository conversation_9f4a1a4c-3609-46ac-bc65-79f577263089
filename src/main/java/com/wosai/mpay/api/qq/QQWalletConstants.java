package com.wosai.mpay.api.qq;

import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/3/2.
 * QQ钱包技术对接人: 吴凯，18917568812
 */
public class QQWalletConstants {


    public static final String SIGN_SP_ID = "1309312501";

    public static final String CHARSET_UTF8_INT = "1";//字符集的取值;1 UTF-8固定为1

    public static final String CHARSET_UTF8_STRING = "UTF-8";
    public static final String CHARSET_GB2312_STRING = "GB2312";

    public static final String VER = "2.0";//版本号取值,目前是应为 2.0
    public static final String SERVICE_VERSION_1_1 = "1.1";//接口版本号,退款的时候填1.1
    public static final String FEE_TYPE_RMB = "1";//现金支付币种，目前只支持人民币。1：人民币
    public static final String  DATE_TIME_FORMAT = "yyyyMMddHHmmss";

    /** sign type **/
    public static final String SIGN_TYPE_MD5 = "MD5";
    public static final String BANK_TYPE_CFT= "CFT";//银行类型。值为CFT时，表示余额支付。
    public static final String DEFAULT_PAY_CHANNEL = "1";//支付渠道,目前固定为1
    public static final String SIGN_KEY_INDEX = "1";//多密钥支持的密钥序号

    public static final String TRADE_STATE_SUCCESS = "0";//支付结果状态码。0表示成功,其它为失败。
    public static final String PAY_RESULT_SUCCESS = "0";//支付结果：0—成功；其它—失败

    /**通讯/交易 结果状态**/
    public static final String RET_CODE_SUCCESS = "0";//返回状态码，0表示成功，其他未定义；返回非0状态码时，请参考错误码类型。
    public static final String RET_CODE_TRADE_STATE_USERPAYING = "********";//提示用户输入支付密码，如果用户手机无网络信号，请换用其他收款方式。
    public static final String RET_CODE_ILLEGAL_SIGN = "********";//商户签名校验失败	确认签名规则是否符合接口文档中的描述。
    public static final String RET_CODE_NO_PRIVILEGE = "66227002";//商户没有手Q支付权限	请联系手Q相关同事，确认商户号的权限。
    public static final String RET_CODE_CHARSET_CONVERION_ERROR_ = "66227004";//字符集转换失败（请使用在GBK字符集之内的utf8字符）	请确认请求的实际编码与请求中的charset参数是否一致。
    public static final String RET_CODE_INVALID_AUTH_CODE = "66227006";//用户授权码无效	提示用户关闭付款码，然后重新开通。
    public static final String RET_CODE_ILLEGAL_CERTIFICATE = "66227007";//用户证书非法	提示用户关闭付款码，然后重新开通。
    public static final String RET_CODE_NOT_ENOUGH = "66227008";//余额不足	用户的财付通余额资金不足，请更换为银行卡支付。
    public static final String RET_CODE_TRADE_CLOSED_CANCEL_FAIL = "66227009";//撤单失败，订单已撤单	订单已经成功关闭，请勿重复提交 关闭订单 请求。
    public static final String RET_CODE_CANCEL_FAIL = "66227010";//撤单失败，订单已支付成功	订单已经支付成功，此时无法完成关闭订单操作。可以调用 撤销订单（交易冲正）接口，或者 按照支付成功，给用户发货。
    public static final String RET_CODE_TRADE_REFUNDED_CANCEL_FAIL = "66227011";//撤单失败，订单已退款	订单已经在退款流程中，请勿再次提交 关闭订单 请求。可以请用户重新支付。
    public static final String RET_CODE_TRADE_NOT_EXIST = "********";//订单不存在	该错误码通常是在调用查询订单接口返回。收到该错误码时，表示订单还没有完成支付，可以再次查询，直到得到订单的最终状态，或者是调用撤销订单（交易冲正）接口接口。
    public static final String RET_CODE_TRADE_CLOSED = "********";//订单已经关闭。	订单已经被关闭，不能尝试支付。
    public static final String RET_CODE_NOT_ENOUGH_MONEY_IN_BANK_CARD = "********";//银行卡可用余额不足	可以提醒用户更换 其它银行卡或者财付通余额进行支付。
    public static final String RET_CODE_TRADE_WAITED_TO_CONFIRM = "*********";//您的操作已提交，请确认是否已生效

    public static final String RET_CODE_BANK_CARD_LOCKED = "********";//银行卡被锁定
    public static final String RET_CODE_ILLEGAL_BANK_CARD = "********";//银行卡状态异常
    public static final String RET_CODE_UNMATCHED_PHONE_NUMBER = "********";//您的银行卡预留手机不符，请核对后再试。若您的银行卡预留手机号已变更，请在当前应用或网站重新绑定银行卡。
    public static final String RET_CODE_BANK_CARD_EXPIRE = "********";//银行卡快捷签约状态不存在或者已过期
    public static final String RET_CODE_TRADE_CLOSED_CANNOT_RETRY = "************";//订单已经被关闭，请勿尝试支付	创建新的订单，引导用户支付。
    public static final String RET_CODE_TRADE_CLOSE_CANNOT_RETRY = "********";//订单已经被关闭，请勿尝试支付	创建新的订单，引导用户支付。
    public static final String RET_CODE_ILLEGAL_RELATIONSHIP = "********";//商户号之间不存在有效的受理关系。	请向手Q支付申请受理关系。


    public static final List<String> RET_CODE_UNKNOWLIST = Arrays.asList(RET_CODE_TRADE_CLOSED_CANCEL_FAIL, RET_CODE_CANCEL_FAIL, RET_CODE_TRADE_REFUNDED_CANCEL_FAIL, RET_CODE_TRADE_CLOSED, RET_CODE_TRADE_CLOSED_CANNOT_RETRY, RET_CODE_TRADE_CLOSE_CANNOT_RETRY, RET_CODE_TRADE_WAITED_TO_CONFIRM);
    public static final List<String> RET_CODE_TRADE_IN_PROG = Arrays.asList(RET_CODE_TRADE_STATE_USERPAYING, RET_CODE_TRADE_NOT_EXIST);

    public static final String TRADE_MODE_IMMEDIATELY  = "1";//交易模式,1-即时到账

    public static final String NOT_SPLIT = "0";//是否分账，0无分账，1分账
    public static final String IS_SPLIT = "1";//是否分账，0无分账，1分账

    public static final String NOT_REFUND = "0";//是否退款，0无退款，1退款
    public static final String IS_REFUND = "1";//是否退款，0无退款，1退款

    public static final String REFUND_SUCC_4 = "4";
    public static final String REFUND_SUCC_10 = "10";

    public static final String REFUND_FAIL_3 = "3";
    public static final String REFUND_FAIL_5 = "5";
    public static final String REFUND_FAIL_6 = "6";

    public static final String REFUND_IN_PROG_8 = "8";
    public static final String REFUND_IN_PROG_9 = "9";
    public static final String REFUND_IN_PROG_11 = "11";

    public static final String REFUND_UNKNOW_1 = "1";
    public static final String REFUND_UNKNOW_2 = "2";


    public static final String REFUND_ROLLBACK = "7";//转入代发，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，资金回流到商户的现金帐号，需要商户人工干预，通过线下或者财付通转账的方式进行退款。


    public static final List<String> REFUND_SUCC_LIST = Arrays.asList(REFUND_SUCC_4, REFUND_SUCC_10, REFUND_IN_PROG_9, REFUND_IN_PROG_8, REFUND_IN_PROG_11);//退款成功
    public static final List<String> REFUND_FAIL_LIST = Arrays.asList(REFUND_FAIL_3,REFUND_FAIL_5,REFUND_FAIL_6);//退款失败
    public static final List<String> REFUND_UNKNOW = Arrays.asList(REFUND_UNKNOW_1,REFUND_UNKNOW_2);//未确定，需要商户原退款单号重新发起。
    // public static final List<String> REFUND_INPROG = Arrays.asList(REFUND_IN_PROG_8,REFUND_IN_PROG_9,REFUND_IN_PROG_11);//退款处理中








}
