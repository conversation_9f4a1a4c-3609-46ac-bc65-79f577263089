package com.wosai.mpay.api.qq;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.api.applepay.*;
import com.wosai.mpay.api.qq.QQWalletConfig;
import com.wosai.mpay.exception.AlipayV2Exception;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.WebUtils;
import com.wosai.mpay.util.WeixinSignature;
import com.wosai.mpay.util.XmlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import java.io.*;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by maoyu
 */
public class QQWalletClient {
    public  static final Logger logger = LoggerFactory.getLogger(QQWalletClient.class);
    private static final ObjectMapper om = new ObjectMapper();
    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public SSLContext getSSLContext(byte[] certData, String password) {
        InputStream inputStream = new ByteArrayInputStream(certData);
        SSLContext sslContext = null;
        try {
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(inputStream, password.toCharArray());//设置证书密码
            sslContext = SSLContext.getInstance("TLS");
            KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
            kmf.init(keyStore, password.toCharArray());
            sslContext.init(kmf.getKeyManagers(), null, new SecureRandom());
        } catch (Exception e) {
            logger.error("getSSLContext error: {}", e.getMessage(), e);
        }
        return sslContext;
    }


    public static SSLContext getSSLContext() {
        InputStream instream = null;
        try {
            //加载本地的证书进行https加密传输
            String certPath = "/Users/<USER>/Desktop/1309312501_20160329145336.pfx";//微信证书路径
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            File file = new File(certPath);
            if (file.exists()) {
                instream = new FileInputStream(file);
            } else {
                instream = QQWalletClient.class.getResourceAsStream(certPath);
            }
            keyStore.load(instream, "609533".toCharArray());//设置证书密码
            SSLContext sslContext = SSLContext.getInstance("TLS");
            KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
            kmf.init(keyStore, "609533".toCharArray());
            sslContext.init(kmf.getKeyManagers(), null, new SecureRandom());
            return sslContext;
        } catch (Exception e) {
            logger.error("getSSLContext error: {}", e.getMessage(), e);
        } finally {
            if (instream != null) {
                try {
                    instream.close();
                } catch (IOException e) {
                }
            }
        }
        return null;
    }

    public Map<String, Object> call(String serviceUrl, String signKey, SSLContext sslContext, Map<String, Object> request) throws MpayException, MpayApiNetworkError {
        Map<String, String> params = new HashMap<String, String>();
        for (String para : request.keySet()) {
            if (request.get(para) != null) {
                params.put(para, request.get(para).toString());
            }
        }
        try {
            params.put(ProtocolFields.SIGN, WeixinSignature.getSign(request, signKey, QQWalletConstants.CHARSET_UTF8_STRING));
            logger.debug("request {}", params);
//            System.out.println(params);
//            String resp = WebUtils.doGet(sslContext, null, serviceUrl, params);
            String resp = HttpClientUtils.doGet(QQWalletClient.class.getName(), sslContext, null, serviceUrl, params, QQWalletConstants.CHARSET_UTF8_STRING, connectTimeout, readTimeout);
            Map<String, Object> result = null;
            if (resp.startsWith("{")) {
                //json
                result = om.readValue(resp, new TypeReference<Map<String, Object>>() {
                });
            } else if (resp.startsWith("<")) {
                //xml
                result = XmlUtils.parse(resp);
            }
            logger.debug("response {}", resp);
//            System.out.println(resp);
            if (result == null || result.size() < 1) {
                throw new AlipayV2Exception("99999", "invalid QQWallet response", null, null);
            }
            return result;
        } catch (IOException e) {
            throw new AlipayV2Exception("99999", "IO error invoking QQWallet api", null, null, e);
        }

    }

    private byte[] getBytes(String string, String charset) throws MpayException {
        if (string == null) {
            return null;
        } else {
            try {
                return string.getBytes(charset);
            } catch (UnsupportedEncodingException e) {
                throw new MpayException("UnsupportedEncodingException :", e);
            }
        }
    }


    public static void main(String[] args) throws MpayException, MpayApiNetworkError {
//        testBSCPay();
//        testOrderQuery();
//        testRefund();
//        testRefundQuery();
//        testReverse();
//        System.out.println(getSSLContext());
    }


    public static void testBSCPay() throws MpayException, MpayApiNetworkError {
        QQWalletClient client = new QQWalletClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.CHARSET, QQWalletConstants.CHARSET_UTF8_INT);
        builder.set(BusinessFields.VER, QQWalletConstants.VER);
        builder.set(BusinessFields.DESC, "test-QQwallet-pay");
        builder.set(BusinessFields.BARGAINOR_ID, "1320324001");
        builder.set(BusinessFields.SP_BILLNO, "20160330170200");
        builder.set(BusinessFields.TOTAL_FEE, "1");
        builder.set(BusinessFields.FEE_TYPE, QQWalletConstants.FEE_TYPE_RMB);
        builder.set(BusinessFields.ATTACH, "no");
        builder.set(BusinessFields.TIME_START, "20160330170100");
        builder.set(BusinessFields.TIME_EXPIRE, "20160401123011");
        builder.set(BusinessFields.AUTH_CODE, "910712204512180319");
        builder.set(BusinessFields.SP_DEVICE_ID, "23");
        builder.set(BusinessFields.COUPON_ID, "1");
        builder.set(BusinessFields.PAY_CHANNEL, QQWalletConstants.DEFAULT_PAY_CHANNEL);
        builder.set(BusinessFields.SIGN_SP_ID, QQWalletConstants.SIGN_SP_ID);
        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(QQWalletConfig.BSC_PAY, QQWalletConfig.SECRET, getSSLContext(), request);
        System.out.println(result);
    }


    public static void testOrderQuery() throws MpayException, MpayApiNetworkError {
        QQWalletClient client = new QQWalletClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.CHARSET, QQWalletConstants.CHARSET_UTF8_STRING);
        builder.set(BusinessFields.VER, QQWalletConstants.VER);
        builder.set(BusinessFields.PARTNER, "1320324001");
        builder.set(BusinessFields.OUT_TRADE_NO, "20160330170200");
        //builder.set(BusinessFields.TRANSACTION_ID, "2342432342356756234");
        builder.set(BusinessFields.SIGN_SP_ID, QQWalletConstants.SIGN_SP_ID);
        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(QQWalletConfig.QUERY, QQWalletConfig.SECRET, getSSLContext(), request);
        System.out.println(result);
    }


    public static void testCloseOrder() throws MpayException, MpayApiNetworkError {
        QQWalletClient client = new QQWalletClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.CHARSET, QQWalletConstants.CHARSET_UTF8_STRING);
        builder.set(ProtocolFields.SIGN_KEY_INDEX, QQWalletConstants.SIGN_KEY_INDEX);
        builder.set(ProtocolFields.SERVICE_VERSION, QQWalletConstants.SERVICE_VERSION_1_1);
        builder.set(BusinessFields.PARTNER, "1320324001");
        builder.set(BusinessFields.OUT_TRADE_NO, "34234567876543");
        builder.set(BusinessFields.TRANSACTION_ID, "345678");
        builder.set(BusinessFields.TOTAL_FEE, "1");
        builder.set(BusinessFields.SIGN_SP_ID, QQWalletConstants.SIGN_SP_ID);
        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(QQWalletConfig.CLOSE_ORDER, QQWalletConfig.SECRET, getSSLContext(), request);

    }


    public static void testRefund() throws MpayException, MpayApiNetworkError {
        QQWalletClient client = new QQWalletClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.INPUT_CHARSET, QQWalletConstants.CHARSET_UTF8_STRING);
        builder.set(ProtocolFields.SERVICE_VERSION, QQWalletConstants.SERVICE_VERSION_1_1);
        builder.set(BusinessFields.PARTNER, "1320324001");
        builder.set(BusinessFields.OUT_TRADE_NO, "2029029849332393");
        builder.set(BusinessFields.TRANSACTION_ID, "1320324001461603301903323420");
        builder.set(BusinessFields.OUT_REFUND_NO, "2221111111");
        builder.set(BusinessFields.TOTAL_FEE, "1");
        builder.set(BusinessFields.REFUND_FEE, "1");
        builder.set(BusinessFields.OP_USER_ID, "1320324001");
        builder.set(BusinessFields.SIGN_SP_ID, QQWalletConstants.SIGN_SP_ID);
        //builder.set(BusinessFields.OP_USER_PASSWD, "2239492jsdkfsd");
        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(QQWalletConfig.REFUND, QQWalletConfig.SECRET, getSSLContext(), request);
        System.out.println(result);
    }

    public static void testRefundQuery() throws MpayException, MpayApiNetworkError {
        QQWalletClient client = new QQWalletClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.INPUT_CHARSET, QQWalletConstants.CHARSET_UTF8_STRING);
        builder.set(ProtocolFields.SERVICE_VERSION, QQWalletConstants.SERVICE_VERSION_1_1);
        builder.set(BusinessFields.PARTNER, "1320324001");
        builder.set(BusinessFields.OUT_TRADE_NO, "2029029849332393");
        //builder.set(BusinessFields.TRANSACTION_ID, "1320324001461603301903323420");
        builder.set(BusinessFields.OUT_REFUND_NO, "2221111111");
        builder.set(BusinessFields.TOTAL_FEE, "1");
        builder.set(BusinessFields.REFUND_FEE, "1");
        builder.set(BusinessFields.SIGN_SP_ID, QQWalletConstants.SIGN_SP_ID);
        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(QQWalletConfig.REFUND_QUERY, QQWalletConfig.SECRET, getSSLContext(), request);
        System.out.println(result);
    }


    public static void testReverse() throws MpayException, MpayApiNetworkError {
        QQWalletClient client = new QQWalletClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.INPUT_CHARSET, QQWalletConstants.CHARSET_UTF8_STRING);
        builder.set(ProtocolFields.SERVICE_VERSION, QQWalletConstants.SERVICE_VERSION_1_1);
        builder.set(BusinessFields.PARTNER, "1320324001");
        builder.set(BusinessFields.OUT_TRADE_NO, "20160330170200");
        builder.set(BusinessFields.TOTAL_FEE, "1");
        builder.set(BusinessFields.OP_USER_ID, "1320324001");
        //builder.set(BusinessFields.OP_USER_PASSWD, "2239492jsdkfsd");
        builder.set(BusinessFields.SIGN_SP_ID, QQWalletConstants.SIGN_SP_ID);
        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(QQWalletConfig.CANCEL, QQWalletConfig.SECRET, getSSLContext(), request);

    }















}
