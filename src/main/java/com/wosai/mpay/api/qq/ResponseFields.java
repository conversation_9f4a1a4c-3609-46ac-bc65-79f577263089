package com.wosai.mpay.api.qq;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/3/2.
 */
public class ResponseFields {

    public static final String RETCODE = "retcode";//返回状态码，0表示成功，其他未定义；返回非0状态码时，请参考错误码类型。
    public static final String RETMSG = "retmsg";//返回信息，如非空，为错误原因
    public static final String VER = "ver";//版本号，ver默认值是1.0，目前版本ver取值应为2.0
    public static final String CHARSET = "charset";//字符编码，固定取值：UTF-8。
    public static final String TRANSACTION_ID = "transaction_id";
    public static final String SP_BILLNO = "sp_billno";//商户系统内部的定单号，32个字符内、可包含字母
    public static final String TOTAL_FEE = "total_fee";
    public static final String BANK_TYPE = "bank_type";//银行类型。值为CFT时，表示余额支付。

    public static final String BANK_BILLNO = "bank_billno";//银行订单号，若为财付通余额支付则为空
    public static final String COUPON_COUNT = "coupon_count";//本次交易中，使用的优惠券数量。该字段不存在或者，值为0时，表示没有使用优惠。


    public static final String COUPON_FEE = "coupon_fee";//本次交易中，为用户减免的金额。该金额应该小于等于total_fee。用户在本次交易中，实际支付的金额应该为total_fee- coupon_fee。

    public static final String TIME_END = "time_end";//

    public static final String TRADE_STATE = "trade_state";//支付结果状态码。0表示成功,其它为失败。


    public static final String TRADE_MODE = "trade_mode";//1-即时到账
    public static final String PARTNER = "partner";//商户号
    public static final String FEE_TYPE = "fee_type";//现金支付币种,目前只支持人民币,默认值是1-人民币
    public static final String IS_SPLIT = "is_split";//是否分账，0无分账，1分账
    public static final String IS_REFUND = "is_refund";//是否退款，0无退款，1退款
    public static final String TRANSPORT_FEE = "transport_fee";//物流费用，单位分，默认0。如果有值，必须保证transport_fee + product_fee = total_fee
    public static final String PRODUCT_FEE = "product_fee";//物品费用，单位分。如果有值，必须保证transport_fee + product_fee = total_fee
    public static final String DISCOUNT = "discount";//优惠掉的金额，单位分。用户交易中，实际支付的金额为 total_fee-discount.

    public static final String BUYER_ALIAS = "buyer_alias";//对应买家账号的一个加密串
    public static final String OUT_TRADE_NO = "out_trade_no";
    public static final String SIGN_SP_ID = "sign_sp_id";//受理商的商户号
    public static final String REFUND_ID = "refund_id";//财付通退款单号
    public static final String REFUND_FEE = "refund_fee";
    public static final String REFUND_STATUS = "refund_status";//退款状态：4，10：退款成功。3，5，6：退款失败。8，9，11：退款处理中。1，2：未确定，需要商户原退款单号重新发起。7：转入代发，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，资金回流到商户的现金帐号，需要商户人工干预，通过线下或者财付通转账的方式进行退款。
    public static final String REFUND_STATUS_0 = "refund_status_0";//退款查询接口返回的字段
    public static final String REFUND_COUNT = "refund_count";//退款记录数
    public static final String OUT_REFUND_NO = "out_refund_no";//商户退款单号
    public static final String REFUND_TIME_BEGIN = "refund_time_begin";//退款申请时间



}
