package com.wosai.mpay.api.qq;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/3/2.
 */
public class ProtocolFields {

    /**request**/
    public static final String INPUT_CHARSET = "input_charset";//字符编码，固定取值：UTF-8。
    public static final String SIGN = "sign";//MD5签名结果
    public static final String SERVICE_VERSION = "service_version";//版本号
    public static final String CHV = "chv";//输出格式,参数为空时，默认输出JSON。
    public static final String CHARSET = "charset";//1 UTF-8,固定为1


    /**response**/
    public static final String RETCODE = "retcode";//返回状态码0表示成功，其他未定义；返回非0状态码时，其他参数无效。

    public static final String RETMSG = "retmsg";//返回信息，如非空，为错误原因。

    public static final String SIGN_KEY_INDEX = "sign_key_index";//多密钥支持的密钥序号，默认1

}
