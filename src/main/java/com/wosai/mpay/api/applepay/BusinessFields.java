package com.wosai.mpay.api.applepay;

/**
 * Created by ma<PERSON>u
 */
public class BusinessFields {
    public static final String CLIENTSN = "clientSn"; //第三方订单号,不解析,只记录 Y
    public static final String AMOUNT = "amount";//金额,分 Y
    public static final String CARD = "card";//银行卡号 Y
    public static final String NFC = "nfc";//nfc内容,透传终端上送的内容即可 Y
    public static final String PIN = "pin";//银行卡密文,透传终端上送的内容即可  N
    public static final String CARDSN = "cardSn";//银行卡序列号,透传即可 Y
    public static final String TRACK2 = "track2";//银行卡的2磁道数据,透传即可 Y


    public static final String MERCHANT_IDENTIFIER = "merchantIdentifier";
    public static final String DISPLAY_NAME = "displayName";
    public static final String INITIATIVE = "initiative";
    public static final String INITIATIVE_CONTEXT = "initiativeContext";


}
