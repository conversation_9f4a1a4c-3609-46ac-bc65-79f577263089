package com.wosai.mpay.api.applepay;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.AlipayV2Exception;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.WebUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by maoyu
 */
public class ApplePayClient {
    public static final Logger logger = LoggerFactory.getLogger(ApplePayClient.class);
    public static final String CHARSET_UTF8 = "UTF-8";
    public static final String CONTENT_TYPE = "application/json";
    private static final ObjectMapper om = new ObjectMapper();
    private int connectTimeout = 3000;
    private int readTimeout = 15000;


    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }


    public Map<String, Object> call(String serviceUrl, Map<String, Object> request, String action) throws MpayException, MpayApiNetworkError {

        try {
            logger.debug("request {}", request);
            String response = "";
            if (ProtocalFields.ACTION_QUERY.equals(action)) {
                response = doGetApplePay(serviceUrl, request);
            } else {
                response = doPostApplePay(serviceUrl, request);
            }
            logger.debug("response {}", response);
            Map<String, Object> result = null;
            result = om.readValue(response, new TypeReference<Map<String, Object>>() {
            });
            return result;
        } catch (IOException e) {
            throw new AlipayV2Exception("99999", "IO error invoking ApplePay api", null, null, e);
        }


    }

    public String doGetApplePay(String serviceUrl, Map<String, Object> request) throws AlipayV2Exception {
        try {
            Map<String, String> params = new HashMap<String, String>();
            for (String para : request.keySet()) {
                if (request.get(para) != null) {
                    params.put(para, request.get(para).toString());
                }
            }
            return WebUtils.doGet(null, null, serviceUrl, params);
        } catch (IOException e) {
            throw new AlipayV2Exception("99999", "IO error invoking ApplePay api", null, null, e);
        }
    }

    public String doPostApplePay(String serviceUrl, Map<String, Object> request) throws MpayException, MpayApiNetworkError {
        try {
            Map<String, Object> params = new HashMap<String, Object>();
            for (String para : request.keySet()) {
                if (request.get(para) != null) {
                    params.put(para, request.get(para));
                }
            }
            String postParams = om.writeValueAsString(params);
            return WebUtils.doPost(null, null, serviceUrl, CONTENT_TYPE, postParams.getBytes(CHARSET_UTF8), connectTimeout, readTimeout);
        } catch (IOException e) {
            throw new AlipayV2Exception("99999", "IO error invoking ApplePay api", null, null, e);
        }

    }


    public static void main(String[] args) throws MpayException, MpayApiNetworkError {
//        testPay();
//        testRevoke();
        testQuery();
    }

    public static void testPay() throws MpayException, MpayApiNetworkError {
        String domain = "http://************:8804/pay";
        ApplePayClient client = new ApplePayClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.CLIENTSN, "11223344");
        builder.set(BusinessFields.AMOUNT, 1);
        builder.set(BusinessFields.CARD, "11223344");
        builder.set(BusinessFields.NFC, "ldkjfajfjwjfiojel");
        builder.set(BusinessFields.PIN, "ajfafjpfjpj");
        builder.set(BusinessFields.CARDSN, "ajfaflwlwjpfjpj");
        builder.set(BusinessFields.TRACK2, "track2");

        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(domain, request, ProtocalFields.ACTION_PAY);
        logger.debug("applepay pay response {}", result);
    }


    public static void testQuery() throws MpayException, MpayApiNetworkError {
        String domain = "http://************:8804/query";
        ApplePayClient client = new ApplePayClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ResponseFields.SN, "1458635836337584782");
        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(domain, request, ProtocalFields.ACTION_QUERY);
        logger.debug("applepay query response {}", result);
    }

    public static void testRevoke() throws MpayException, MpayApiNetworkError {
        String domain = "http://************:8804/revoke";
        ApplePayClient client = new ApplePayClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ResponseFields.SN, "1458635836337584782");
        builder.set(BusinessFields.CLIENTSN, "11223344");
        builder.set(BusinessFields.AMOUNT, 1);
        builder.set(BusinessFields.CARD, "11223344");
        builder.set(BusinessFields.NFC, "ldkjfajfjwjfiojel");
        builder.set(BusinessFields.PIN, "ajfafjpfjpj");
        builder.set(BusinessFields.CARDSN, "ajfaflwlwjpfjpj");
        builder.set(BusinessFields.TRACK2, "track2");

        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(domain, request, ProtocalFields.ACTION_REVOKE);
        logger.debug("applepay query response {}", result);

    }

}


