package com.wosai.mpay.api.applepay;

import java.util.Arrays;
import java.util.List;

/**
 * Created by maoyu
 */
public class ApplePayConstants {
    public static final String TYPE_PAY = "pay";//本次请求对应的订单操作类型: pay
    public static final String TYPE_REVOKE = "revoke";//本次请求对应的订单操作类型: pay

    public static final String ORDER_STATUS_CREATED = "0"; //创建
    public static final String ORDER_STATUS_INORPG = "1";//处理中
    public static final String ORDER_STATUS_SUCC = "2";//成功
    public static final String ORDER_STATUS_FAILED = "3"; //失败
    public static final String ORDER_STATUS_ERROR = "4";//错误


    public static final List<String> APPLE_PAY_INPROG_LIST = Arrays.asList(ORDER_STATUS_CREATED, ORDER_STATUS_INORPG);

    public static final List<String> APPLE_PAY_FAILED_LIST = Arrays.asList(ORDER_STATUS_ERROR, ORDER_STATUS_FAILED);


    public static final String INITIATIVE_WEB = "web";





}
