package com.wosai.mpay.api.bcs.enums;

public enum BCSOrderStatusEnum {
    // 订单状态
    UNPAY("UNPAY", "待支付"),
    PAYED("PAYED", "已支付"),
    FAIL("FAIL", "失败"),
    CLOSED("CLOSED", "已关单"),
    REVERSE("REVERSE", "已撤销"),
    REFUNDED("REFUNDED", "已退款"),

    // 交易状态（查单时）
    // 1. 预下单，但没有支付 （PROCESSING）
    // 2. 支付成功 （PAYED）、
    // 3. 退款成功 （SUCCESS）
    SUCCESS("SUCCESS", "成功"),
    PROCESSING("PROCESSING", "处理中");


    private final String status;
    private final String desc;

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    BCSOrderStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static BCSOrderStatusEnum of(String status) {
        if (null == status) {
            return UNPAY;
        }
        for (BCSOrderStatusEnum e : BCSOrderStatusEnum.values()) {
            if (e.status.equals(status)) {
                return e;
            }
        }
        return UNPAY;
    }
}
