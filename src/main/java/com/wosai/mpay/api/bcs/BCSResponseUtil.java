package com.wosai.mpay.api.bcs;

import java.util.Map;

public class BCSResponseUtil {

    /**
     * 检查响应是否成功
     * 
     * @param response 响应Map
     * @return true if response is successful, false otherwise
     */
    public static boolean isSuccess(Map<String, Object> response) {
        if (response == null) {
            return false;
        }
        
        String retCode = (String) response.get(BCSResponseFields.RET_CODE);
        return BCSResponseFields.RET_CODE_SUCCESS.equals(retCode);
    }
}
