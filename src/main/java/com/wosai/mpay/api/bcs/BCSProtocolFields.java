package com.wosai.mpay.api.bcs;

public class BC<PERSON>rotocolFields {
    /**
     * 请求方法
     */
    public static final String METHOD_PAY = "unified-order";
    public static final String METHOD_REFUND = "order-refund";
    public static final String METHOD_QUERY = "order-state/query";
    public static final String METHOD_REFUND_QUERY = "refund-order/query";
    public static final String METHOD_PRE_ORDER = "qrcode/pre-order";

    /**
     * 协议默认值
     */
    public static final String DEFAULT_SIGN_METHOD = "SM2"; // 默认签名方法
    public static final String DEFAULT_CONTENT_TYPE = "application/json";
    public static final String DEFAULT_VERSION = "1.0";
    public static final String DEFAULT_CHARSET = "UTF-8";
    public static final String DEFAULT_BANK_ID = "BCS";
    public static final String DEFAULT_NET_ACCESS = "0"; // 默认互联网接入
    public static final String DEFAULT_SCENE_ID = "00"; // 默认场景编号

    /**
     * 业务内容字段
     */
    public static final String BIZ_CONTENT = "bizContent"; // 业务内容
    public static final String DATA = "Data"; // 业务数据

    /**
     * 系统级请求参数 - 业务报文头
     */
    public static final String BUSINESS_SEQ_NO = "businessSeqNo"; // 商户交易流水号
    public static final String VERSION = "version"; // 版本号
    public static final String MCHT_NO = "mchtNo"; // 商户编号
    public static final String CLIENT_DATE = "clientDate"; // 请求日期, 格式: YYYYMMDD
    public static final String CLIENT_TIME = "clientTime"; // 请求时间, 格式: HHMMSS
    public static final String TRACE_ID = "traceId"; // 日志编号
    public static final String PLATFORM = "platform"; // 平台编号
    public static final String SCENE_ID = "sceneId"; // 场景编号
    public static final String TXN_TYPE = "txnType"; // 交易代码
    public static final String CHANNEL_NO = "channelNo"; // 收单渠道编号
    public static final String ACCEPT_TERMINAL = "acceptTerminal"; // 受理终端
    public static final String APP_ID = "appId"; // App编号
    public static final String NET_ACCESS = "netAccess"; // 网络接入方式
    public static final String REQUEST_IP = "requestIp"; // 请求IP
    public static final String CLIENT_TYPE = "clientType"; // 客户端类型
    public static final String MAC = "mac"; // 请求mac地址
    public static final String TRX_IP_CITY = "trxIpCity"; // 请求城市
    public static final String LONLAT = "lonlat"; // 经纬度
    public static final String RESERVED1 = "reserved1"; // 预留字段1
    public static final String RESERVED2 = "reserved2"; // 预留字段2
    public static final String RESERVED3 = "reserved3"; // 预留字段3

    /**
     * 系统级响应参数 - 业务报文头
     */
    public static final String RET_CODE = "RetCode"; // 返回码
    public static final String RET_MSG = "RetMsg"; // 返回信息
    public static final String SERVER_DATE = "serverDate"; // 服务器应答日期
    public static final String SERVER_TIME = "serverTime"; // 服务器应答时间
    public static final String RESERVED4 = "reserved4"; // 预留字段4
    public static final String RESERVED5 = "reserved5"; // 预留字段5

    /**
     * HTTP Header字段
     */
    public static final String HEADER_CONTENT_TYPE = "Content-Type"; // 内容类型
    public static final String HEADER_ACCEPT = "Accept"; // 接受的内容类型
    public static final String HEADER_AUTHORIZATION = "Authorization"; // 授权
    public static final String HEADER_APP_ID = "x-aob-appID"; // 应用ID
    public static final String HEADER_SIGNATURE = "x-aob-signature"; // 签名
    public static final String HEADER_BANK_ID = "x-aob-bankID"; // 银行标识
    public static final String HEADER_CUSTOMER_LAST_LOGGED_TIME = "x-aob-customer-last-logged-time"; // 用户上次登录时间
    public static final String HEADER_CUSTOMER_IP_ADDRESS = "x-aob-customer-ip-address"; // 用户IP地址
    public static final String HEADER_INTERACTION_ID = "x-aob-interaction-id"; // 关联ID
    public static final String HEADER_ACCESS_TOKEN = "x-aob-access-token"; // 用户访问令牌
    public static final String HEADER_CUSTOMER_USER_AGENT = "x-customer-user-agent"; // 用户代理
    public static final String HEADER_IDEMPOTENCY_KEY = "x-idempotency-key"; // 幂等标识

    /**
     * 需要加签的HTTP Header字段列表
     * 根据文档，以下字段有值且值长度大于0则加签
     */
    public static final String[] SIGN_HEADER_FIELDS = {
        HEADER_AUTHORIZATION,
        HEADER_APP_ID,
        HEADER_BANK_ID,
        HEADER_CUSTOMER_LAST_LOGGED_TIME,
        HEADER_CUSTOMER_IP_ADDRESS,
        HEADER_INTERACTION_ID,
        HEADER_ACCESS_TOKEN,
        HEADER_CUSTOMER_USER_AGENT,
        HEADER_IDEMPOTENCY_KEY
    };
}
