package com.wosai.mpay.api.bcs;

import com.wosai.mpay.api.pab.SmUtil;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import com.wosai.pantheon.util.MapUtil;
import okhttp3.Headers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.*;

import static com.wosai.mpay.api.bcs.BCSProtocolFields.*;
import static com.wosai.mpay.util.HttpClientUtils.BODY_RESULT_FIELD;
import static com.wosai.mpay.util.HttpClientUtils.HEADERS_RESULT_FIELD;

public class BCSClient {
    public static final Logger log = LoggerFactory.getLogger(BCSClient.class);

    private int connectTimeout = BCSConstants.DEFAULT_CONNECT_TIMEOUT;
    private int readTimeout = BCSConstants.DEFAULT_READ_TIMEOUT;

    /**
     * 信任所有证书的SSLContext（仅用于测试环境，生产环境不应使用）
     */
    private static final SSLContext TRUST_ALL_SSL_CONTEXT;
    static {
        try {
            SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, new TrustManager[]{new X509TrustManager() {
                @Override
                public void checkClientTrusted(X509Certificate[] chain, String authType) {}
                @Override
                public void checkServerTrusted(X509Certificate[] chain, String authType) {}
                @Override
                public X509Certificate[] getAcceptedIssuers() { return new X509Certificate[0]; }
            }}, new SecureRandom());
            TRUST_ALL_SSL_CONTEXT = sslContext;
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize trust-all SSL context", e);
        }
    }

    /**
     * 请求长沙银行接口
     *
     * @param requestUrl      请求地址
     * @param request         请求参数
     * @param appId          应用ID
     * @param privateKey     SM2私钥
     * @param publicKey      SM2公钥
     * @param sm4Key         SM4密钥
     * @param sm4Iv          SM4初始向量
     * @return 响应结果
     * @throws MpayException 业务异常
     * @throws MpayApiNetworkError 网络异常
     */
    public Map<String, Object> call(String requestUrl, Map<String, Object> request, String appId, String privateKey, String publicKey, String sm4Key, String sm4Iv)
            throws MpayException, MpayApiNetworkError {

        // 加密业务内容
        String encryptedBizContent = encryptBizContent(request, sm4Key, sm4Iv);

        // 构建HTTP Header（包含签名）
        Map<String, String> headers = buildHttpHeaders(appId, encryptedBizContent, privateKey);

        // 构建请求体
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put(BIZ_CONTENT, encryptedBizContent);
        // 发送请求
        Map<String, Object> response = sendRequest(requestUrl, headers, requestBody);

        // 处理响应，验证签名并解密
        return parseAndVerifyResponse(response, publicKey, sm4Key, sm4Iv);
    }

    /**
     * 加密业务内容
     */
    private String encryptBizContent(Map<String, Object> bizContent, String sm4Key, String sm4Iv) throws MpayException {
        try {
            log.info("长沙银行request: {}", bizContent);
            // 转换为JSON字符串
            String jsonContent = JsonUtil.objectToJsonString(bizContent);

            // 使用SM4加密
            String encryptedContent = Hex.encodeToString(SM4Util.encryptCBC(jsonContent.getBytes(StandardCharsets.UTF_8), Hex.decodeToBytes(sm4Key), Hex.decodeToBytes(sm4Iv)));

            return encryptedContent;
            
        } catch (Exception e) {
            log.error("BCS加密业务内容失败", e);
            throw new MpayException("BCS加密业务内容失败", e);
        }
    }

    /**
     * 构建HTTP Header（包含签名）
     */
    private Map<String, String> buildHttpHeaders(String appId, String encryptedBizContent, String privateKey) throws MpayException {
        try {
            Map<String, String> headers = new HashMap<>();

            headers.put(HEADER_CONTENT_TYPE, DEFAULT_CONTENT_TYPE);
            headers.put(HEADER_APP_ID, appId);
            headers.put(HEADER_BANK_ID, DEFAULT_BANK_ID);
            
            // 生成签名
            String signature = generateSignature(headers, encryptedBizContent, privateKey);
            headers.put(HEADER_SIGNATURE, signature);
            
            return headers;
            
        } catch (Exception e) {
            log.error("BCS构建HTTP Header失败", e);
            throw new MpayException("BCS构建HTTP Header失败", e);
        }
    }

    /**
     * 生成签名
     */
    private String generateSignature(Map<String, String> headers, String bizContent, String privateKey) throws MpayException {
        try {
            // 构建待签名内容, 得保持验签串构造的顺序，因此使用LinkedHashMap
            Map<String, String> signMap = new LinkedHashMap<>();
            
            // 添加需要加签的HTTP Header字段
            for (String field : SIGN_HEADER_FIELDS) {
                String value = headers.get(field);
                if (value != null && value.length() > 0) {
                    signMap.put(field, value);
                }
            }
            
            // 添加bizContent字段
            if (bizContent != null && bizContent.length() > 0) {
                signMap.put(BIZ_CONTENT, bizContent);
            }
            // 转换为JSON字符串
            String signContent = JsonUtil.objectToJsonString(signMap);
            // 使用SM2进行签名
            String signature = Hex.encodeToString(SM2Util.sign(Hex.decodeToBytes(privateKey), signContent.getBytes(StandardCharsets.UTF_8)));
            return signature;
            
        } catch (Exception e) {
            log.error("BCS生成签名失败", e);
            throw new MpayException("BCS生成签名失败", e);
        }
    }

    /**
     * 发送HTTP请求
     */
    private Map<String, Object> sendRequest(String requestUrl, Map<String, String> headers, Map<String, String> requestBody) throws MpayApiNetworkError {
        try {
            String requestJson = JsonUtil.objectToJsonString(requestBody);
            Map<String, Object> response = HttpClientUtils.doCommonMethod(BCSClient.class.getName(), TRUST_ALL_SSL_CONTEXT, null, requestUrl,null,DEFAULT_CONTENT_TYPE, requestJson, headers, DEFAULT_CHARSET, connectTimeout, readTimeout, "post");
            
            return response;
        } catch (Exception e) {
            log.error("BCS发送请求失败", e);
            throw new MpayApiNetworkError("BCS发送请求失败", e);
        }
    }

    /**
     * 解析并验证响应
     */
    private Map<String, Object> parseAndVerifyResponse(Map<String, Object> response, String publicKey, String sm4Key, String sm4Iv) throws MpayException {
        try {
            Headers headerMap = (Headers) (MapUtil.getObject(response, HEADERS_RESULT_FIELD));
            String bodyStr = MapUtil.getString(response, BODY_RESULT_FIELD);
            // 解析响应体JSON
            Map<String, Object> responseMap = JsonUtil.jsonStringToObject(bodyStr, Map.class);
            // 获取加密的业务内容。如果没有则表明响应异常，则不需要验证签名和解密（因为数据没有签名和加密），直接返回错误数据即可
            String encryptedBizContent = (String) responseMap.get(BIZ_CONTENT);
            if (encryptedBizContent == null || encryptedBizContent.trim().isEmpty()) {
                return (Map<String, Object>) responseMap.get(DATA);
            }
            // 验签
            verifyResponseSignature(headerMap, bodyStr, publicKey);
            
            // 解密业务内容
            String decryptedContent = new String(SM4Util.decryptCBC(Hex.decodeToBytes(encryptedBizContent), Hex.decodeToBytes(sm4Key), Hex.decodeToBytes(sm4Iv)), StandardCharsets.UTF_8);
            // 解析解密后的业务内容
            Map<String, Object> bizResponse = JsonUtil.jsonStringToObject(decryptedContent, Map.class);
            log.info("长沙银行response: {}", bizResponse);
            return (Map<String, Object>)bizResponse.get(DATA);
            
        } catch (Exception e) {
            log.error("BCS处理响应失败", e);
            throw new MpayException("BCS处理响应失败", e);
        }
    }

    private void verifyResponseSignature(Headers headerMap, String bodyStr, String publicKey) {
        String sign = headerMap.get(HEADER_SIGNATURE);
        try {
            bodyStr = SmUtil.generateSM3HASH(bodyStr);
        } catch (UnsupportedEncodingException e) {
            log.error("SM3摘要计算失败", e);
            throw new RuntimeException("SM3摘要计算失败");
        }
        if (!SM2Util.verifySign(Hex.decodeToBytes(publicKey), bodyStr.getBytes(StandardCharsets.UTF_8), Hex.decodeToBytes(sign))) {
            throw new RuntimeException("响应数据验签失败: " + bodyStr);
        }
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
}
