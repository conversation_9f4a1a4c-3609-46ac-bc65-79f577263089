package com.wosai.mpay.api.bcs;

public class BCSConstants {
    /**
     * 请求方法定义
     */
    public static final String METHOD_UNIFIED_ORDER = "BG0006"; // 统一下单
    public static final String METHOD_ORDER_QUERY = "BG0005"; // 订单查询
    public static final String METHOD_REFUND = "BG0003"; // 退款
    public static final String METHOD_REFUND_QUERY = "BT0011"; // 退款查询
    public static final String METHOD_PRE_ORDER= "BG0017";

    /**
     * 时间格式定义
     */
    public static final String DATE_SIMPLE_FORMAT = "yyyyMMdd"; // 日期格式
    public static final String TIME_SIMPLE_FORMAT = "HHmmss"; // 时间格式
    public static final String ORDER_TIME_FORMAT = "yyyyMMddHHmmss"; // 订单时间格式

    /**
     * 字符集编码
     */
    public static final String CHARSET_UTF8 = "UTF-8";

    /**
     * 版本号
     */
    public static final String VERSION = "1.0";

    /**
     * 银行标识
     */
    public static final String BANK_ID = "BCS";

    /**
     * 内容类型
     */
    public static final String CONTENT_TYPE = "application/json";
    public static final String JSON_FORMAT = "json";

    /**
     * 接受类型
     */
    public static final String ACCEPT = "json";

    /**
     * 网络接入方式
     */
    public static final String NET_ACCESS_INTERNET = "0"; // 互联网
    public static final String NET_ACCESS_PRIVATE = "1"; // 专线

    /**
     * 客户端类型
     */
    public static final String CLIENT_TYPE_PC = "pc";
    public static final String CLIENT_TYPE_IOS = "ios";
    public static final String CLIENT_TYPE_ANDROID = "android";
    public static final String CLIENT_TYPE_WAP = "wap";

    /**
     * 场景编号
     */
    public static final String SCENE_ID_DEFAULT = "00";

    /**
     * 业务类型 - bizType
     */
    public static final String BIZ_TYPE_GATEWAY_B2C = "GATEWAYB2C"; // B2C网关支付
    public static final String BIZ_TYPE_GATEWAY_B2B2 = "GATEWAYB2B2"; // B2B网关支付（无跳转）
    public static final String BIZ_TYPE_GATEWAY_B2B = "GATEWAYB2B"; // B2B网关支付（有跳转）
    public static final String BIZ_TYPE_QUICK = "QUICK"; // 快捷支付
    public static final String BIZ_TYPE_BANK_CARD_PAY = "BANK_CARD_PAY"; // 本行卡支付
    public static final String BIZ_TYPE_E_SCAN = "E_SCAN"; // e钱庄扫码支付
    public static final String BIZ_TYPE_UN_SCAN = "UN_SCAN"; // 银联条码付 云闪付被扫
    public static final String BIZ_TYPE_WX_SCAN = "WX_SCAN"; // 微信被扫
    public static final String BIZ_TYPE_ALI_SCAN = "ALI_SCAN"; // 支付宝被扫
    public static final String BIZ_TYPE_WX_BARCODE = "WX_SCAN"; // 微信条码支付（被扫）
    public static final String BIZ_TYPE_ALI_BARCODE = "ALI_SCAN"; // 支付宝条码支付（被扫）
    public static final String BIZ_TYPE_UN_QR = "UN_QR"; // 银联二维码主扫
    public static final String BIZ_TYPE_WX_QR = "WX_QR"; // 微信二维码主扫
    public static final String BIZ_TYPE_ALI_QR = "ALI_QR"; // 支付宝二维码主扫
    public static final String BIZ_TYPE_WX_APP = "WX_APP"; // 微信jsApi支付
    public static final String BIZ_TYPE_ALI_APP = "ALI_APP"; // 支付宝jsApi支付
    public static final String BIZ_TYPE_WX_NATIVE = "WX_NATIVE"; // 微信app支付
    public static final String BIZ_TYPE_DCEP_ICBC_SCAN = "DCEP_ICBC_SCAN"; // 数字人民币被扫
    public static final String BIZ_TYPE_DCEP_ICBC_QR = "DCEP_ICBC_QR"; // 数字人民币主扫

    /**
     * 币种
     */
    public static final String CURRENCY_CNY = "CNY"; // 人民币
    public static final String CURRENCY_HKD = "HKD"; // 人民币
    public static final String CURRENCY_USD = "USD"; // 人民币


    /**
     * 交易类型 - tradeType
     */
    public static final String TRADE_TYPE_SDK = "SDK";
    public static final String TRADE_TYPE_WEB = "WEB";

    /**
     * 设备号 - deviceInfo
     */
    public static final String DEVICE_INFO_WEB = "WEB";

    /**
     * 分账类型
     */
    public static final String ROYALTY_YES = "YES"; // 需要分账
    public static final String ROYALTY_NO = "NO"; // 不需要分账

    /**
     * 分账方式
     */
    public static final String SPLIT_METHOD_RATIO = "1"; // 按比例
    public static final String SPLIT_METHOD_AMOUNT = "2"; // 按固定金额

    /**
     * 组合类型
     */
    public static final String ORDER_MODE_TYPE_TRANS = "TRANS"; // 收单

    /**
     * 支付方式
     */
    public static final String PAY_METHOD_UNION_AT_PAY = "UnionAtPay"; // 银联AT微信支付宝
    public static final String PAY_METHOD_SCAN_CODE = "ScanCode";  // 银联微信支付宝被扫

    /**
     * 支付方式
     */
    public static final String TREAD_TYPE_SDK = "SDK"; // 交易类型

    /**
     * 限制信用卡交易
     */
    public static final String NO_CREDIT = "no_credit";

    /**
     * 卡支付方式
     */
    public static final String PAYMENT_PASSWORD = "A"; // 密码
    public static final String PAYMENT_NO_PASSWORD = "B"; // 无密(小额免密或指纹识别)
    public static final String PAYMENT_NO_PASSWORD_C = "C"; // 无密

    /**
     * 积分类型
     */
    public static final String POINT_TYPE_NO_COST = "1"; // 不计算成本
    public static final String POINT_TYPE_COST = "2"; // 计算成本

    /**
     * 网关类型
     */
    public static final String GATEWAY_TYPE_ICBC = "ICBC"; // 工商银行网关

    /**
     * 加密类型
     */
    public static final String ENC_TYPE_SM4 = "SM4"; // SM4加密

    /**
     * 返回码
     */
    public static final String RET_CODE_SUCCESS = "000000"; // 成功
    public static final String RET_CODE_ACCEPTED = "000001"; // 交易已受理


    /**
     * HTTP状态码
     */
    public static final int HTTP_STATUS_OK = 200; // API调用成功
    public static final int HTTP_STATUS_VALIDATION_ERROR = 412; // API验证异常
    public static final int HTTP_STATUS_INTERNAL_ERROR = 500; // API网关或内部服务错误
    public static final int HTTP_STATUS_SYSTEM_ERROR = 502; // 系统内部异常
    public static final int HTTP_STATUS_SERVICE_ERROR = 510; // 服务失败

    /**
     * 超时时间（毫秒）
     */
    public static final int DEFAULT_CONNECT_TIMEOUT = 30000; // 连接超时30秒
    public static final int DEFAULT_READ_TIMEOUT = 60000; // 读取超时60秒

    /**
     * 生产环境配置
     */
    public static class ProdEnv {
        public static final String PROD_URL = "https://open.bankofchangsha.com/prdGW/V1/uaps/unified-order";
    }

    /**
     * 业务内容字段
     */
    public static final String BIZ_CONTENT = "bizContent";
    public static final String DATA = "Data";

    /**
     * 特殊字符列表（需要转义）
     */
    public static final String[] SPECIAL_CHARS = {
        "(", "[", "{", "/", "^", "-", "$", "|", "}", "]", ")", "?", "*", "+", ".", 
        "=", ">", ">>", "<", "<<", "|", "&", "'", "`", "!", "#", "\\", "~", ":"
    };
}
