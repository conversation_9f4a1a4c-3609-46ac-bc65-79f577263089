package com.wosai.mpay.api.bcs;

public class BCSBusinessFields {
    /**
     * 统一下单请求字段
     */
    public static final String SIGN_NO = "signNo"; // 商户签约号
    public static final String BIZ_TYPE = "bizType"; // 业务类型
    public static final String CURRENCY = "currency"; // 币种
    public static final String CALLBACK_URL = "callbackUrl"; // 回调地址
    public static final String OUT_IDENTIFY_CODE = "outIdentifyCode"; // 客户标识号
    public static final String ORDER_AMOUNT = "orderAmount"; // 订单金额
    public static final String GOODS = "goods"; // 商品信息
    public static final String GOODS_DESC = "goodsDesc"; // 商品描述
    public static final String TRADE_NO = "tradeNo"; // 商户订单流水号
    public static final String TRADE_TYPE = "tradeType"; // 交易类型
    public static final String TRADE_TIME = "tradeTime"; // 商户交易时间
    public static final String REMARK = "remark"; // 备注
    public static final String FRONT_URL = "frontUrl"; // 前端回调地址
    public static final String TIME_EXPIRE = "timeExpire"; // 订单有效期
    public static final String SUB_ORDERS = "subOrders"; // 子单信息
    public static final String ROYALTIES = "royalties"; // 分账清单
    public static final String STORE_NO = "storeNo"; // 门店编号
    public static final String CASHIER_USER_NO = "cashierUserNo"; // 收银员编号
    public static final String ORDER_MODE = "orderMode"; // 订单组合方式


    /**
     * 统一下单响应字段
     */
    public static final String ORDER_NO = "orderNo"; // 订单编号
    public static final String ORDER_STATUS = "orderStatus"; // 订单状态
    public static final String AMOUNT = "amount"; // 订单金额
    public static final String REAL_AMOUNT = "realAmount"; // 实付金额
    public static final String ORDER_DATE = "orderDate"; // 订单日期
    public static final String ORDER_TIME = "orderTime"; // 订单时间
    public static final String FINISH_TIME = "finishTime"; // 支付完成时间
    public static final String CLEAR_DATE = "clearDate"; // 清算日期
    public static final String REQ_GATEWAY_URL = "reqGatewayUrl"; // 请求网关页面地址
    public static final String PREPAY_ID = "prepay_id"; // 预支付交易会话标识
    public static final String MWEB_URL = "mweb_url"; // 支付跳转页面
    public static final String WC_PAY_DATA = "wc_pay_data"; // 微信 APP 调用数据
    public static final String PAY_INFO = "payInfo"; // 支付调用数据

    /**
     * CSB二维码字段
     */
    // ========== 分账信息 ==========
    /** 分账描述(100) */
    public static final String SPLIT_REMARK = "remark";
    /** 有效期(20) - 单位分钟 */
    public static final String VALIDITY_PERIOD = "validityPeriod";
    /** 是否展示商品信息(1) - 空/0展示，1不展示 */
    public static final String SHOW_PRO_INFO = "showProInfo";

    /**
     * 子订单字段
     */
    public static final String SUB_MCHT_NO = "mchtNo"; // 二级商户编号
    public static final String SUB_TRADE_NO = "subTradeNo"; // 商户子订单流水号
    public static final String SUB_ORDER_AMOUNT = "subOrderAmount"; // 子订单金额
    public static final String SUB_GOODS = "goods"; // 子订单商品信息
    public static final String SUB_GOODS_DESC = "goodsDesc"; // 子订单商品描述
    public static final String IS_NEED_ROYALTY = "isNeedRoyalty"; // 是否分账

    /**
     * 分账清单字段
     */
    public static final String ROYALTY_ALLOCATION_NO = "royaltyAllocationNo"; // 分账资金标识号
    public static final String SPLIT_METHOD = "splitMethod"; // 分账方式
    public static final String SPLIT_ORDER_NO = "splitOrderNo"; // 分账订单号
    public static final String SPLIT_AMOUNT = "splitAmount"; // 分账金额/比例
    public static final String ROYALTY_REMARK = "remark"; // 分账描述

    /**
     * 订单组合方式字段
     */
    public static final String ORDER_MODE_TYPE = "type"; // 类型
    public static final String ORDER_MODE_AMOUNT = "amount"; // 金额
    public static final String PAY_METHOD = "payMethod"; // 支付方式
    public static final String ACCT_NO = "acctNo"; // 账号
    public static final String ACCT_NAME = "acctName"; // 账号名称
    public static final String ACCT_VALID_DATE = "acctValidDate"; // 账户有效期
    public static final String CVN2 = "cvn2"; // 账户安全码
    public static final String RESERVE_ACCT_PHONE = "reserveAcctPhone"; // 账户手机号
    public static final String BUSI_TYPE = "busiType"; // 对公/对私
    public static final String PASSWORD = "password"; // 密码
    public static final String PAYMENT = "payment"; // 卡支付方式
    public static final String CARD_MSG = "cardMsg"; // 卡背面末三位
    public static final String POINT_TYPE = "pointType"; // 积分类型
    public static final String POINT = "point"; // 积分
    public static final String SMS_CODE = "smsCode"; // 短信验证码
    public static final String OPEN_ID = "openId"; // 用户授权码
    public static final String SUB_OPEN_ID = "subOpenId"; // 子用户标识
    public static final String SUB_APP_ID = "subAppId"; // 子商户公众号ID
    public static final String LIMIT_PAY = "limitPay"; // 禁止客户支付方式
    public static final String DEVICE_INFO = "deviceInfo"; // 设备号
    public static final String LEGAL_PERSON_CODE = "legalPersonCode"; // 法人代码
    public static final String BIZ_CODE = "bizCode"; // 业务编号
    public static final String PURPOSE = "purpose"; // 用途
    public static final String ORDER_MODE_REMARK = "remark"; // 备注信息
    public static final String PROMO_NO = "promoNo"; // 优惠码
    public static final String AUTH_CODE = "authCode"; // 付款码
    public static final String USER_MAC = "userMac"; // 移动支付需要上传的mac地址
    public static final String GATE_WAY_TYPE = "gateWayType"; // 网关类型
    public static final String ISSR_CHNNL_ID = "issrChnnlId"; // 网关渠道标识
    public static final String PAYER_ACCT_ISSR_ID = "payerAcctIssrId"; // 付款方账户所属机构标识
    public static final String OPTRID_TP = "optridTp"; // 经办人证件类型
    public static final String ENC_TYPE = "encType"; // 加密类型
    public static final String LATITUDE = "latitude"; // 纬度
    public static final String LONGITUDE = "longitude"; // 经度
    public static final String SERIAL_NUM = "serialNum"; // 终端序列号

    /**
     * 微信支付数据字段
     */
    public static final String WX_APP_ID = "appId"; // 公众号id/小程序id
    public static final String WX_TIME_STAMP = "timeStamp"; // 时间戳
    public static final String WX_NONCE_STR = "nonceStr"; // 随机字符串
    public static final String WX_PACKAGE = "package"; // 订单详情扩展字符串
    public static final String WX_SIGN_TYPE = "signType"; // 签名方式
    public static final String WX_PAY_SIGN = "paySign"; // 签名
    public static final String WX_PARTNER_ID = "partnerId"; // 微信支付分配的从业机构号
    public static final String WX_PREPAY_ID = "prepayId"; // 微信返回的支付交易会话ID

    /**
     * 扩展字段
     */
    public static final String FIELD1 = "field1"; // 扩展字段1
    public static final String FIELD2 = "field2"; // 扩展字段2
    public static final String FIELD3 = "field3"; // 扩展字段3
    public static final String FIELD4 = "field4"; // 扩展字段4
    public static final String FIELD5 = "field5"; // 扩展字段5

    /**
     * 预留字段
     */
    public static final String RESERVED1 = "reserved1"; // 预留字段1
    public static final String RESERVED2 = "reserved2"; // 预留字段2
    public static final String RESERVED3 = "reserved3"; // 预留字段3
    public static final String RESERVED4 = "reserved4"; // 预留字段4
    public static final String RESERVED5 = "reserved5"; // 预留字段5

    /**
     * 退款相关字段
     */
    public static final String O_TRADE_NO = "oTradeNo"; // 原商户订单流水号
    public static final String O_ORDER_NO = "oOrderNo"; // 原订单编号
    public static final String REFUND_FUNDING_METHOD = "refundFundingMethod"; // 退款出资方式
    public static final String REFUND_FUNDING_LIST = "refundFundingList"; // 退款出资方清单
    public static final String REFUND_FUNDING_NO = "refundFundingNo"; // 退款出资标识号
    public static final String REFUND_FUNDING_AMOUNT = "refundFundingAmount"; // 退款出资金额/比例
    public static final String E_REFUND_SN = "eRefundSn"; // 退款流水号
    public static final String CANCEL_REASON = "cancelReason"; // 退款原因

    /**
     * 退款相关字段
     */
    public static final String REFUND_NO = "refundNo"; // 平台退款订单号
    public static final String OUT_REFUND_NO = "outRefundNo"; // 商户退款订单号
    public static final String REFUND_AMT = "refundAmt"; // 退款金额
    public static final String REFUND_AMOUNT = "refundAmount"; // 退款金额
    public static final String REFUND_REASON = "refundReason"; // 退款原因
    public static final String FEE = "fee"; // 退还手续费

    /**
     * 支付宝相关字段
     */
    public static final String ALI_AMOUNT = "amount"; // 交易总金额，以元为单位
    public static final String ALI_USER_ID = "userId"; // 买家的支付宝唯一用户号
    public static final String ALI_ORDER_DESC = "orderDesc"; // 商品名称
    public static final String ALI_TRADE_NO = "tradeNo"; // 支付宝预下单订单号

    /**
     * 设备相关字段
     */
    public static final String DEVICE_NO = "deviceNo"; // 终端设备编号
    public static final String TERMINAL_ID = "terminalId"; // 终端号
    public static final String MCH_IP = "mchIp"; // 终端ip
    public static final String SHOP_ID = "shopId"; // 门店id

    /**
     * 时间相关字段
     */
    public static final String TIME_START = "timeStart"; // 订单生成时间
    public static final String TIMESTAMP = "timestamp"; // 时间戳

    /**
     * 其他业务字段
     */
    public static final String ATTACH = "attach"; // 附加信息
    public static final String TOTAL_FEE = "totalFee"; // 总金额
    public static final String PRODUCT_INFO = "proInfo"; // 商品名称
    public static final String OUT_TRADE_NO = "outTradeNo"; // 商户订单号
    public static final String PAY_ID = "payId"; // 渠道流水号
    public static final String BACK_URL = "backUrl"; // 支付结果回调通知
    public static final String EXT_FIELD1 = "extfld1"; // 扩展字段1
    public static final String EXT_FIELD2 = "extfld2"; // 扩展字段2
    public static final String APPID = "appId"; // 应用id
    public static final String PACKAGE_FIELD = "packAge"; // 统一下单接口返回的prepay_id参数值
    public static final String NONCE_STR = "nonceStr"; // 随机字符串
    public static final String SIGN_TYPE = "signType"; // 签名方式
    public static final String PAY_SIGN = "paySign"; // 签名

    /**
     * 常用前缀和后缀
     */
    public static final String OPEN_ID_PREFIX = "0"; // 默认会给openId加上前缀:0


}
