package com.wosai.mpay.api.bcs;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

public class BCSRequestBuilder {
    private final Map<String, Object> businessRequest;   // 完成的请求体
    private final Map<String, Object> businessRequestHeader;  // 设置业务请求头
    private final Map<String, Object> businessRequestBody;  // 设置业务请求体
    private final List<Map<String, Object>> orderModeList;  // 业务请求体中的orderMode列表
    private final Map<String, Object> orderMode;  // 业务请求体中的orderMode

    public BCSRequestBuilder() {
        businessRequest = new LinkedHashMap<>();
        businessRequestHeader = new LinkedHashMap<>(32);
        businessRequestBody = new LinkedHashMap<>(32);
        orderModeList = new ArrayList<>();
        orderMode = new LinkedHashMap<>(32);
    }

    /**
     * 设置字段值
     *
     * @param field 字段名
     * @param value 字段值
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder set(String field, Object value) {
        businessRequestBody.put(field, value);
        return this;
    }


    /**
     * 设置orderMode字段值
     *
     * @param field 字段名
     * @param value 字段值
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setOrderMode(String field, Object value) {
        orderMode.put(field, value);
        return this;
    }


    /**
     * 设置商户签约号
     * 
     * @param signNo 商户签约号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setSignNo(String signNo) {
        return set(BCSBusinessFields.SIGN_NO, signNo);
    }

    /**
     * 设置业务类型
     * 
     * @param bizType 业务类型
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setBizType(String bizType) {
        return set(BCSBusinessFields.BIZ_TYPE, bizType);
    }

    /**
     * 设置币种
     * 
     * @param currency 币种
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setCurrency(String currency) {
        return set(BCSBusinessFields.CURRENCY, currency);
    }

    /**
     * 设置回调地址
     * 
     * @param callbackUrl 回调地址
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setCallbackUrl(String callbackUrl) {
        return set(BCSBusinessFields.CALLBACK_URL, callbackUrl);
    }

    /**
     * 设置客户标识号
     * 
     * @param outIdentifyCode 客户标识号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setOutIdentifyCode(String outIdentifyCode) {
        return set(BCSBusinessFields.OUT_IDENTIFY_CODE, outIdentifyCode);
    }

    /**
     * 设置订单金额
     * 
     * @param orderAmount 订单金额
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setOrderAmount(BigDecimal orderAmount) {
        return set(BCSBusinessFields.ORDER_AMOUNT, orderAmount);
    }

    /**
     * 设置商品信息
     * 
     * @param goods 商品信息
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setGoods(String goods) {
        return set(BCSBusinessFields.GOODS, goods);
    }

    /**
     * 设置商品描述
     * 
     * @param goodsDesc 商品描述
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setGoodsDesc(String goodsDesc) {
        return set(BCSBusinessFields.GOODS_DESC, goodsDesc);
    }

    /**
     * 设置商户订单流水号
     * 
     * @param tradeNo 商户订单流水号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setTradeNo(String tradeNo) {
        return set(BCSBusinessFields.TRADE_NO, tradeNo);
    }

    /**
     * 设置交易类型
     * 
     * @param tradeType 交易类型
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setTradeType(String tradeType) {
        return set(BCSBusinessFields.TRADE_TYPE, tradeType);
    }

    /**
     * 设置商户交易时间
     * 
     * @param tradeTime 商户交易时间
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setTradeTime(String tradeTime) {
        return set(BCSBusinessFields.TRADE_TIME, tradeTime);
    }

    /**
     * 设置前端回调地址
     * 
     * @param frontUrl 前端回调地址
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setFrontUrl(String frontUrl) {
        return set(BCSBusinessFields.FRONT_URL, frontUrl);
    }

    /**
     * 设置订单有效期
     * 
     * @param timeExpire 订单有效期
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setTimeExpire(String timeExpire) {
        return set(BCSBusinessFields.TIME_EXPIRE, timeExpire);
    }

    /**
     * 设置门店编号
     * 
     * @param storeNo 门店编号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setStoreNo(String storeNo) {
        return set(BCSBusinessFields.STORE_NO, storeNo);
    }

    /**
     * 设置收银员编号
     * 
     * @param cashierUserNo 收银员编号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setCashierUserNo(String cashierUserNo) {
        return set(BCSBusinessFields.CASHIER_USER_NO, cashierUserNo);
    }

    /**
     * 设置终端设备编号
     *
     * @param deviceNo 终端设备编号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setDeviceNo(String deviceNo) {
        return set(BCSBusinessFields.DEVICE_NO, deviceNo);
    }

    /**
     * 设置终端ip
     * 
     * @param mchIp 终端ip
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setMchIp(String mchIp) {
        return set(BCSBusinessFields.MCH_IP, mchIp);
    }

    /**
     * 设置门店id
     * 
     * @param shopId 门店id
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setShopId(String shopId) {
        return set(BCSBusinessFields.SHOP_ID, shopId);
    }

    /**
     * 设置订单生成时间
     * 
     * @param timeStart 订单生成时间
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setTimeStart(String timeStart) {
        return set(BCSBusinessFields.TIME_START, timeStart);
    }

    /**
     * 设置附加信息
     * 
     * @param attach 附加信息
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setAttach(String attach) {
        return set(BCSBusinessFields.ATTACH, attach);
    }


    // ==================== 退款相关字段设置方法 ====================

    /**
     * 设置原商户订单流水号
     *
     * @param oTradeNo 原商户订单流水号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setOTradeNo(String oTradeNo) {
        return set(BCSBusinessFields.O_TRADE_NO, oTradeNo);
    }

    /**
     * 设置原订单编号
     *
     * @param oOrderNo 原订单编号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setOOrderNo(String oOrderNo) {
        return set(BCSBusinessFields.O_ORDER_NO, oOrderNo);
    }

    /**
     * 设置退款金额
     *
     * @param amount 退款金额
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setRefundAmount(BigDecimal amount) {
        return set(BCSBusinessFields.AMOUNT, amount);
    }

    /**
     * 设置退款出资方式
     *
     * @param refundFundingMethod 退款出资方式，1-按比例 2-按金额
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setRefundFundingMethod(String refundFundingMethod) {
        return set(BCSBusinessFields.REFUND_FUNDING_METHOD, refundFundingMethod);
    }

    /**
     * 设置退款出资方清单
     *
     * @param refundFundingList 退款出资方清单
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setRefundFundingList(List<Map<String, Object>> refundFundingList) {
        return set(BCSBusinessFields.REFUND_FUNDING_LIST, refundFundingList);
    }

    /**
     * 设置退款出资标识号
     *
     * @param refundFundingNo 退款出资标识号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setRefundFundingNo(String refundFundingNo) {
        return set(BCSBusinessFields.REFUND_FUNDING_NO, refundFundingNo);
    }

    /**
     * 设置退款出资金额/比例
     *
     * @param refundFundingAmount 退款出资金额/比例
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setRefundFundingAmount(BigDecimal refundFundingAmount) {
        return set(BCSBusinessFields.REFUND_FUNDING_AMOUNT, refundFundingAmount);
    }

    /**
     * 设置退款流水号
     *
     * @param eRefundSn 退款流水号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setERefundSn(String eRefundSn) {
        return set(BCSBusinessFields.E_REFUND_SN, eRefundSn);
    }

    /**
     * 设置退款原因
     *
     * @param cancelReason 退款原因
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setCancelReason(String cancelReason) {
        return set(BCSBusinessFields.CANCEL_REASON, cancelReason);
    }

    /**
     * 设置退款原因
     *
     * @param orderNo 订单编号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setOrderNo(String orderNo) {
        return set(BCSBusinessFields.ORDER_NO, orderNo);
    }


    // ==================== OrderMode字段设置方法 ====================

    /**
     * 设置orderMode类型
     *
     * @param type 类型，示例值:TRANS 收单
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setType(String type) {
        return setOrderMode(BCSBusinessFields.ORDER_MODE_TYPE, type);
    }

    /**
     * 设置orderMode金额
     *
     * @param amount 金额
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setAmount(BigDecimal amount) {
        return setOrderMode(BCSBusinessFields.ORDER_MODE_AMOUNT, amount);
    }

    /**
     * 设置支付方式
     *
     * @param payMethod 支付方式，示例值：UnionAtPay 银联AT微信支付宝
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setPayMethod(String payMethod) {
        return setOrderMode(BCSBusinessFields.PAY_METHOD, payMethod);
    }

    /**
     * 设置账号
     *
     * @param acctNo 账号，银行卡和预付费支付时必输
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setAcctNo(String acctNo) {
        return setOrderMode(BCSBusinessFields.ACCT_NO, acctNo);
    }

    /**
     * 设置账号名称
     *
     * @param acctName 账号名称，银行卡支付时必输
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setAcctName(String acctName) {
        return setOrderMode(BCSBusinessFields.ACCT_NAME, acctName);
    }

    /**
     * 设置账户有效期
     *
     * @param acctValidDate 账户有效期，账户类型为贷记卡时必输，格式：yyyyMMdd
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setAcctValidDate(String acctValidDate) {
        return setOrderMode(BCSBusinessFields.ACCT_VALID_DATE, acctValidDate);
    }

    /**
     * 设置账户安全码
     *
     * @param cvn2 账户安全码，账户类型为贷记卡时必输
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setCvn2(String cvn2) {
        return setOrderMode(BCSBusinessFields.CVN2, cvn2);
    }

    /**
     * 设置账户手机号
     *
     * @param reserveAcctPhone 账户手机号，本行卡和预付费支付时必输、数字货币协议支付必输
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setReserveAcctPhone(String reserveAcctPhone) {
        return setOrderMode(BCSBusinessFields.RESERVE_ACCT_PHONE, reserveAcctPhone);
    }

    /**
     * 设置对公/对私
     *
     * @param busiType 对公/对私，本行卡支付时必输,0-对公/1-对私
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setBusiType(String busiType) {
        return setOrderMode(BCSBusinessFields.BUSI_TYPE, busiType);
    }

    /**
     * 设置密码
     *
     * @param password 密码，本行卡和预付费支付时必输
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setPassword(String password) {
        return setOrderMode(BCSBusinessFields.PASSWORD, password);
    }

    /**
     * 设置卡支付方式
     *
     * @param payment 卡支付方式，本行卡支付时必输,A 密码 B 无密(小额免密或指纹识别) C 无密
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setPayment(String payment) {
        return setOrderMode(BCSBusinessFields.PAYMENT, payment);
    }

    /**
     * 设置卡背面末三位
     *
     * @param cardMsg 卡背面末三位，本行卡支付时卡面支付必输
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setCardMsg(String cardMsg) {
        return setOrderMode(BCSBusinessFields.CARD_MSG, cardMsg);
    }

    /**
     * 设置积分类型
     *
     * @param pointType 积分类型，本行卡积分支付必传,1-不计算成本 2-计算成本
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setPointType(String pointType) {
        return setOrderMode(BCSBusinessFields.POINT_TYPE, pointType);
    }

    /**
     * 设置积分
     *
     * @param point 积分，本行卡积分支付必传
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setPoint(String point) {
        return setOrderMode(BCSBusinessFields.POINT, point);
    }

    /**
     * 设置短信验证码
     *
     * @param smsCode 短信验证码，需要发送短信时必输
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setSmsCode(String smsCode) {
        return setOrderMode(BCSBusinessFields.SMS_CODE, smsCode);
    }

    /**
     * 设置用户授权码
     *
     * @param openId 用户授权码，微信公众号/小程序支付时，OpenId和SubOpenId需要上送其中一个
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setOpenId(String openId) {
        return setOrderMode(BCSBusinessFields.OPEN_ID, openId);
    }

    /**
     * 设置子用户标识
     *
     * @param subOpenId 子用户标识，微信公众号/小程序支付时，OpenId和SubOpenId需要上送其中一个
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setSubOpenId(String subOpenId) {
        return setOrderMode(BCSBusinessFields.SUB_OPEN_ID, subOpenId);
    }

    /**
     * 设置子商户公众号ID
     *
     * @param subAppId 子商户公众号ID，微信公众号/小程序支付时，需要上送
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setSubAppId(String subAppId) {
        return setOrderMode(BCSBusinessFields.SUB_APP_ID, subAppId);
    }

    /**
     * 设置禁止客户支付方式
     *
     * @param limitPay 禁止客户支付方式，微信支付时no_credit--指定不能使用信用卡支付
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setLimitPay(String limitPay) {
        return setOrderMode(BCSBusinessFields.LIMIT_PAY, limitPay);
    }

    /**
     * 设置设备号
     *
     * @param deviceInfo 设备号，微信支付必输，终端设备号(门店号或收银设备ID)，注意：PC网页或公众号内支付请传"WEB"
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setDeviceInfo(String deviceInfo) {
        return setOrderMode(BCSBusinessFields.DEVICE_INFO, deviceInfo);
    }

    /**
     * 设置法人代码
     *
     * @param legalPersonCode 法人代码
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setLegalPersonCode(String legalPersonCode) {
        return setOrderMode(BCSBusinessFields.LEGAL_PERSON_CODE, legalPersonCode);
    }

    /**
     * 设置业务编号
     *
     * @param bizCode 业务编号，示例值：合同编号，落库
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setBizCode(String bizCode) {
        return setOrderMode(BCSBusinessFields.BIZ_CODE, bizCode);
    }

    /**
     * 设置用途
     *
     * @param purpose 用途
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setPurpose(String purpose) {
        return setOrderMode(BCSBusinessFields.PURPOSE, purpose);
    }

    /**
     * 设置备注信息
     *
     * @param remark 备注信息
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setRemark(String remark) {
        return setOrderMode(BCSBusinessFields.ORDER_MODE_REMARK, remark);
    }

    /**
     * 设置优惠码
     *
     * @param promoNo 优惠码
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setPromoNo(String promoNo) {
        return setOrderMode(BCSBusinessFields.PROMO_NO, promoNo);
    }

    /**
     * 设置付款码
     *
     * @param authCode 付款码，E钱庄扫码支付必传
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setAuthCode(String authCode) {
        return setOrderMode(BCSBusinessFields.AUTH_CODE, authCode);
    }

    /**
     * 设置移动支付需要上传的mac地址
     *
     * @param userMac 移动支付需要上传的mac地址，B2C网关支付必输
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setUserMac(String userMac) {
        return setOrderMode(BCSBusinessFields.USER_MAC, userMac);
    }

    /**
     * 设置网关类型
     *
     * @param gateWayType 网关类型，B2B网关支付必输
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setGateWayType(String gateWayType) {
        return setOrderMode(BCSBusinessFields.GATE_WAY_TYPE, gateWayType);
    }

    /**
     * 设置网关渠道标识
     *
     * @param issrChnnlId 网关渠道标识，B2B网关支付必输
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setIssrChnnlId(String issrChnnlId) {
        return setOrderMode(BCSBusinessFields.ISSR_CHNNL_ID, issrChnnlId);
    }

    /**
     * 设置付款方账户所属机构标识
     *
     * @param payerAcctIssrId 付款方账户所属机构标识，B2B网关支付（无跳转）必输
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setPayerAcctIssrId(String payerAcctIssrId) {
        return setOrderMode(BCSBusinessFields.PAYER_ACCT_ISSR_ID, payerAcctIssrId);
    }

    /**
     * 设置经办人证件类型
     *
     * @param optridTp 经办人证件类型，B2B网关支付（无跳转）必输
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setOptridTp(String optridTp) {
        return setOrderMode(BCSBusinessFields.OPTRID_TP, optridTp);
    }

    /**
     * 设置加密类型
     *
     * @param encType 加密类型，预付费密码支付必输
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setEncType(String encType) {
        return setOrderMode(BCSBusinessFields.ENC_TYPE, encType);
    }

    /**
     * 设置纬度
     *
     * @param latitude 纬度，终端设备扫码必填，终端实时纬度信息
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setLatitude(String latitude) {
        return setOrderMode(BCSBusinessFields.LATITUDE, latitude);
    }

    /**
     * 设置经度
     *
     * @param longitude 经度，终端设备扫码必填，终端实时经度信息
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setLongitude(String longitude) {
        return setOrderMode(BCSBusinessFields.LONGITUDE, longitude);
    }

    /**
     * 设置终端序列号
     *
     * @param serialNum 终端序列号，呼啦APP送手机序列号、POS终端、扫码盒子、刷脸设备送实际sn码，上述必填
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setSerialNum(String serialNum) {
        return setOrderMode(BCSBusinessFields.SERIAL_NUM, serialNum);
    }

    /**
     * 设置orderMode
     *
     * @param orderMode orderMode列表
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setOrderMode(List<Map<String, Object>> orderMode) {
        return set(BCSBusinessFields.ORDER_MODE, orderMode);
    }

    /**
     * 设置请求头字段值
     *
     * @param field 字段名
     * @param value 字段值
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setHeader(String field, Object value) {
        businessRequestHeader.put(field, value);
        return this;
    }

    /**
     * 设置商户交易流水号
     *
     * @param businessSeqNo 商户交易流水号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setBusinessSeqNo(String businessSeqNo) {
        return setHeader(BCSProtocolFields.BUSINESS_SEQ_NO, businessSeqNo);
    }

    /**
     * 设置版本号
     *
     * @param version 版本号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setVersion(String version) {
        return setHeader(BCSProtocolFields.VERSION, version);
    }

    /**
     * 设置商户编号
     *
     * @param mchtNo 商户编号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setMchtNo(String mchtNo) {
        return setHeader(BCSProtocolFields.MCHT_NO, mchtNo);
    }


    /**
     * 设置日志编号
     *
     * @param traceId 日志编号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setTraceId(String traceId) {
        return setHeader(BCSProtocolFields.TRACE_ID, traceId);
    }

    /**
     * 设置平台编号
     *
     * @param platform 平台编号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setPlatform(String platform) {
        return setHeader(BCSProtocolFields.PLATFORM, platform);
    }

    /**
     * 设置场景编号
     *
     * @param sceneId 场景编号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setSceneId(String sceneId) {
        return setHeader(BCSProtocolFields.SCENE_ID, sceneId);
    }

    /**
     * 设置交易代码
     *
     * @param txnType 交易代码
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setTxnType(String txnType) {
        return setHeader(BCSProtocolFields.TXN_TYPE, txnType);
    }

    /**
     * 设置收单渠道编号
     *
     * @param channelNo 收单渠道编号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setChannelNo(String channelNo) {
        return setHeader(BCSProtocolFields.CHANNEL_NO, channelNo);
    }

    /**
     * 设置受理终端
     *
     * @param acceptTerminal 受理终端
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setAcceptTerminal(String acceptTerminal) {
        return setHeader(BCSProtocolFields.ACCEPT_TERMINAL, acceptTerminal);
    }

    /**
     * 设置App编号
     *
     * @param appId App编号
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setAppId(String appId) {
        return setHeader(BCSProtocolFields.APP_ID, appId);
    }

    /**
     * 设置网络接入方式
     *
     * @param netAccess 网络接入方式，0-互联网 1-专线
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setNetAccess(String netAccess) {
        return setHeader(BCSProtocolFields.NET_ACCESS, netAccess);
    }

    /**
     * 设置请求IP
     *
     * @param requestIp 请求IP
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setRequestIp(String requestIp) {
        return setHeader(BCSProtocolFields.REQUEST_IP, requestIp);
    }

    /**
     * 设置客户端类型
     *
     * @param clientType 客户端类型:pc,ios,android,wap
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setClientType(String clientType) {
        return setHeader(BCSProtocolFields.CLIENT_TYPE, clientType);
    }

    /**
     * 设置请求mac地址
     *
     * @param mac 请求mac地址
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setMac(String mac) {
        return setHeader(BCSProtocolFields.MAC, mac);
    }

    /**
     * 设置请求城市
     *
     * @param trxIpCity 请求城市，如:"北京"
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setTrxIpCity(String trxIpCity) {
        return setHeader(BCSProtocolFields.TRX_IP_CITY, trxIpCity);
    }

    /**
     * 设置经纬度
     *
     * @param lonlat 经纬度
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setLonlat(String lonlat) {
        return setHeader(BCSProtocolFields.LONLAT, lonlat);
    }

    /**
     * 设置当前日期和时间到请求头
     *
     * @return BCSRequestBuilder
     */
    public BCSRequestBuilder setCurrentDateTime() {
        SimpleDateFormat dateFormat = new SimpleDateFormat(BCSConstants.DATE_SIMPLE_FORMAT);
        SimpleDateFormat timeFormat = new SimpleDateFormat(BCSConstants.TIME_SIMPLE_FORMAT);
        Date now = new Date();

        setHeader(BCSProtocolFields.CLIENT_DATE, dateFormat.format(now));
        setHeader(BCSProtocolFields.CLIENT_TIME, timeFormat.format(now));

        return this;
    }

    /**
     * 构建请求参数
     * @return 请求参数Map
     */
    public Map<String, Object> build() {

        if (!orderMode.isEmpty()){
            orderModeList.add(orderMode);
            businessRequestBody.put(BCSBusinessFields.ORDER_MODE, orderModeList);
        }

        businessRequestHeader.put(BCSProtocolFields.BIZ_CONTENT, businessRequestBody);
        businessRequest.put(BCSProtocolFields.DATA, businessRequestHeader);
        return businessRequest;
    }
}
