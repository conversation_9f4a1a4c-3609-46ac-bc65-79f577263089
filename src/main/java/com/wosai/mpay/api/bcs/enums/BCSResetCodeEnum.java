package com.wosai.mpay.api.bcs.enums;

public enum BCSResetCodeEnum {
    // BSC场景下，如果不直接支付成功（用户须输入密码支付时），则会返回交易失败的状态码，可根据以下状态码判断订单状态。
    FAIL("BCSOPEN-X00000001", "交易处理失败"),
    PROCESS("BCSOPEN-X00002001", "交易处理中"),
    UNKNOWN("BCSOPEN-X00000003", "交易处理结果未知"),
    ACCEPTED("BCSOPEN-X00000005", "交易受理成功");

    private final String status;
    private final String desc;

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    BCSResetCodeEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static BCSResetCodeEnum of(String status) {
        if (null == status) {
            return PROCESS;
        }
        for (BCSResetCodeEnum e : BCSResetCodeEnum.values()) {
            if (e.status.equals(status)) {
                return e;
            }
        }
        return FAIL;
    }
}
