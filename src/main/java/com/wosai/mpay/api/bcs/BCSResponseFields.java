package com.wosai.mpay.api.bcs;

public class BCSResponseFields {
    
    /**
     * 基础响应字段
     */
    public static final String RET_CODE = "RetCode"; // 返回码
    public static final String RET_MSG = "RetMsg"; // 返回信息
    public static final String BUSINESS_SEQ_NO = "businessSeqNo"; // 交易流水号
    public static final String SERVER_DATE = "serverDate"; // 服务器应答日期
    public static final String SERVER_TIME = "serverTime"; // 服务器应答时间

    /**
     * 统一下单响应字段
     */
    public static final String ORDER_NO = "orderNo"; // 订单编号
    public static final String TRADE_NO = "tradeNo"; // 商户订单流水号
    public static final String ORDER_STATUS = "orderStatus"; // 订单状态
    public static final String AMOUNT = "amount"; // 订单金额
    public static final String REAL_AMOUNT = "realAmount"; // 实付金额
    public static final String ORDER_DATE = "orderDate"; // 订单日期
    public static final String ORDER_TIME = "orderTime"; // 订单时间
    public static final String FINISH_TIME = "finishTime"; // 支付完成时间
    public static final String CLEAR_DATE = "clearDate"; // 清算日期
    public static final String REQ_GATEWAY_URL = "reqGatewayUrl"; // 请求网关页面地址
    public static final String PREPAY_ID = "prepay_id"; // 预支付交易会话标识
    public static final String MWEB_URL = "mweb_url"; // 支付跳转页面
    public static final String WC_PAY_DATA = "wc_pay_data"; // 微信 APP 调用数据
    public static final String PAY_INFO = "payInfo"; // 支付调用数据

    /**
     * 微信支付数据字段 - CSB响应字段
     */
    // ========== 二维码相关信息 ==========
    /** 二维码URL(100) - 必填，扫码后跳转地址 */
    public static final String PAY_DATA = "payData";
    /** 二维码编号(64) - 必填 */
    public static final String QRCODE_NO = "qrcodeNo";
    /** 二维码信息(500) - 必填，Base64编码的图片数据 */
    public static final String QRCODE_INFO = "qrcodeInfo";

    /**
     * 微信支付数据字段 - wc_pay_data
     */
    public static final String WX_APP_ID = "appId"; // 公众号id/小程序id
    public static final String WX_TIME_STAMP = "timeStamp"; // 时间戳
    public static final String WX_NONCE_STR = "nonceStr"; // 随机字符串
    public static final String WX_PACKAGE = "package"; // 订单详情扩展字符串
    public static final String WX_SIGN_TYPE = "signType"; // 签名方式
    public static final String WX_PAY_SIGN = "paySign"; // 签名

    /**
     * 微信APP支付数据字段
     */
    public static final String WX_PARTNER_ID = "partnerId"; // 微信支付分配的从业机构号
    public static final String WX_PREPAY_ID = "prepayId"; // 微信返回的支付交易会话ID

    /**
     * 预留字段
     */
    public static final String RESERVED1 = "reserved1"; // 预留字段1
    public static final String RESERVED2 = "reserved2"; // 预留字段2
    public static final String RESERVED3 = "reserved3"; // 预留字段3
    public static final String RESERVED4 = "reserved4"; // 预留字段4
    public static final String RESERVED5 = "reserved5"; // 预留字段5

    /**
     * 扩展字段
     */
    public static final String FIELD1 = "field1"; // 扩展字段1
    public static final String FIELD2 = "field2"; // 扩展字段2
    public static final String FIELD3 = "field3"; // 扩展字段3
    public static final String FIELD4 = "field4"; // 扩展字段4
    public static final String FIELD5 = "field5"; // 扩展字段5

    /**
     * 查询相关字段
     */
    public static final String EXTEND_MAP = "extendMap"; // 扩展参数
    public static final String RECORD_LIST = "list"; // 数据列表

    /**
     * 退款相关字段
     */
    public static final String REFUND_NO = "refundNo"; // 平台退款订单号
    public static final String OUT_REFUND_NO = "outRefundNo"; // 商户退款订单号
    public static final String REFUND_AMT = "refundAmt"; // 退款金额
    public static final String REFUND_AMOUNT = "refundAmount"; // 退款金额
    public static final String REFUND_REASON = "refundReason"; // 退款原因
    public static final String FEE = "fee"; // 退还手续费

    /**
     * 订单退款响应状态字段
     */
    public static final String E_REFUND_SN = "eRefundSn"; // 退款流水号
    public static final String REFUND_TXN_NO = "refundTxnNo"; // 收单退款流水号，退款成功时才返回
    public static final String O_TRADE_NO = "oTradeNo"; // 原商户订单流水号
    public static final String O_ORDER_NO = "oOrderNo"; // 原订单编号
    public static final String REFUND_TXN_DATE = "refundTxnDate"; // 退款日期
    public static final String REFUND_TXN_TIME = "refundTxnTime"; // 退款时间
    public static final String REPAY_DATE = "repayDate"; // 退款完成时间，格式：yyyyMMddHHmmss

    /**
     * 订单状态查询响应字段
     */
    public static final String MCHT_NO = "mchtNo"; // 商户号
    public static final String DEAL_CODE = "dealCode"; // 系统返回码
    public static final String DEAL_MSG = "dealMsg"; // 返回说明
    public static final String GOODS = "goods"; // 商品信息
    public static final String GOODS_DESC = "goods_desc"; // 商品描述
    public static final String PMC_USER_ID = "pmcUserId"; // 第三方支付平台用户标识，微信支付宝支付时返回
    public static final String PAY_METHOD = "payMethod"; // 支付方式，示例值：UnionAtPay 银联AT微信支付宝
    public static final String CASHIER_USER_NO = "cashierUserNo"; // 收银员编号

    /**
     * 订单退款状态查询响应字段
     */
    public static final String RETURN_CODE = "returnCode"; // 返回码
    public static final String RETURN_MSG = "returnMsg"; // 返回信息

    /**
     * 支付宝相关字段
     */
    public static final String ALI_AMOUNT = "amount"; // 交易总金额
    public static final String ALI_USER_ID = "userId"; // 买家的支付宝唯一用户号
    public static final String ALI_ORDER_DESC = "orderDesc"; // 商品名称
    public static final String ALI_TRADE_NO = "tradeNo"; // 支付宝预下单订单号

    /**
     * 其他业务字段
     */
    public static final String PAY_ID = "payId"; // 渠道流水号
    public static final String OUT_TRADE_NO = "outTradeNo"; // 商户订单号
    public static final String TOTAL_FEE = "totalFee"; // 总金额
    public static final String ATTACH = "attach"; // 附加信息
    public static final String DEVICE_NO = "deviceNo"; // 终端设备编号
    public static final String MCH_IP = "mchIp"; // 终端ip
    public static final String SHOP_ID = "shopId"; // 门店id
    public static final String TIME_START = "timeStart"; // 订单生成时间
    public static final String TIME_EXPIRE = "timeExpire"; // 订单超时时间

    /**
     * HTTP状态码常量
     */
    public static final int HTTP_STATUS_OK = 200; // API调用成功
    public static final int HTTP_STATUS_VALIDATION_ERROR = 412; // API验证异常
    public static final int HTTP_STATUS_INTERNAL_ERROR = 500; // API网关或内部服务错误
    public static final int HTTP_STATUS_SYSTEM_ERROR = 502; // 系统内部异常
    public static final int HTTP_STATUS_SERVICE_ERROR = 510; // 服务失败

    /**
     * 成功返回码的常量
     */
    public static final String RET_CODE_SUCCESS = "00000000"; // 成功

}
