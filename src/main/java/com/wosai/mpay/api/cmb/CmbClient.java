package com.wosai.mpay.api.cmb;

import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.cmb.CmbSM2Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Date: 2020/10/14 Time: 11:07 上午
 */
public class CmbClient {
    public static final Logger logger = LoggerFactory.getLogger(CmbClient.class);
    private static final String CONTENT_TYPE = "application/json;charset=UTF-8";

    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public CmbClient() {
    }

    public CmbClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }


    public Map<String,Object> call(String gateway, RequestBuilder requestBuilder, String appId, String secret
            , String privateKey) throws MpayException, MpayApiNetworkError {

        Map<String, String> request = requestBuilder.build();
        String timestamp = Long.toString(System.currentTimeMillis() / 1000);
        String signature = CmbSM2Util.sm2Sign(RsaSignature.getSignCheckContent(request)
                ,  privateKey);
        String headerSignature = sign(appId, secret, signature, timestamp);
        Map<String, String> headers = buildHttpHeader(appId, timestamp, headerSignature);

        request.put(ProtocolFields.SIGN, signature);
        String requestStr = JsonUtil.objectToJsonString(request);

        logger.debug("request {}", requestStr);
        String responseStr = HttpClientUtils.doPost(CmbClient.class.getName(), null, null, gateway
                , CONTENT_TYPE, requestStr, headers, CmbConstants.ENCODING, connectTimeout, readTimeout);
        logger.debug("response {}", responseStr);
        try{
            Map<String,Object> result = JsonUtil.jsonStringToObject(responseStr, Map.class);
            return result;
        }catch (Exception e){
            throw new MpayApiUnknownResponse("parse response error: " + e.getMessage(), e);
        }
    }

    private Map<String, String> buildHttpHeader(String appId, String timestamp, String headerSignature) {
        Map<String, String> headers = new HashMap<>();
        headers.put(ProtocolFields.APP_ID, appId);
        headers.put(ProtocolFields.TIMESTAMP, timestamp);
        headers.put(ProtocolFields.API_SIGN, headerSignature);
        return headers;
    }

    private String sign(String appId, String secret, String signature, String timestamp) {
        StringBuilder builder = new StringBuilder(256);
        builder.append(ProtocolFields.APP_ID).append("=").append(appId).append("&")
                .append(ProtocolFields.SECRET).append("=").append(secret).append("&")
                .append(ProtocolFields.SIGN).append("=").append(signature).append("&")
                .append(ProtocolFields.TIMESTAMP).append("=").append(timestamp);

        return Hashing.md5().hashString(builder.toString(), Charsets.UTF_8).toString().toLowerCase();
    }
}
