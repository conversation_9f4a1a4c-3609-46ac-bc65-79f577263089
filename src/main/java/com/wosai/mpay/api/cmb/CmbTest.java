package com.wosai.mpay.api.cmb;


import com.wosai.mpay.util.JsonUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR> Date: 2020/10/14 Time: 11:07 上午
 */
public class CmbTest {

    private static final String PAY_URL = "https://api.cmburl.cn:8065/polypay/v1.0/mchorders/pay";
//    private static final String ORDER_QUERY_URL = "https://api.cmburl.cn:8065/polypay/v1.0/mchorders/orderquery";
    private static final String ORDER_QUERY_URL = "https://api.cmbchina.com/polypay/v1.0/mchorders/orderquery";
//    private static final String REFUND_URL = "https://api.cmburl.cn:8065/polypay/v1.0/mchorders/refund";
    private static final String REFUND_URL = "https://api.cmbchina.com/polypay/v1.0/mchorders/refund";
    private static final String REFUND_QUERY_URL = "https://api.cmburl.cn:8065/polypay/v1.0/mchorders/refundquery";
    private static final String CANCEL_URL = "https://api.cmburl.cn:8065/polypay/v1.0/mchorders/cancel";
    private static final String CLOSE_URL = "https://api.cmburl.cn:8065/polypay/v1.0/mchorders/close";
    private static final String ALIPAY_URL = "https://api.cmburl.cn:8065/polypay/v1.0/mchorders/servpay";
    private static final String WX_URL = "https://api.cmburl.cn:8065/polypay/v1.0/mchorders/onlinepay";
    private static final String DCEP_PAY_URL = "https://api.cmburl.cn:8065/polypay/v1.0/mchorders/ecny/unifiedPayment";
//    private static final String DCEP_PAY_URL = "https://api.cmbchina.com/polypay/v1.0/mchorders/ecny/unifiedPayment";

//    private static final String ALIPAY_QUERY_URL = "https://api.cmburl.cn:8065/hou/apiuat/aliPay/orderStatusQuery";
//    private static final String ALIPAY_REFUND_URL = "https://api.cmburl.cn:8065/hou/apiuat/aliPay/refund";
//    private static final String ALIPAY_REFUND_QUERY_URL = "https://api.cmburl.cn:8065/hou/apiuat/aliPay/refundStatusQuery";
//    private static final String ALIPAY_CLOSE_URL = "https://api.cmburl.cn:8065/hou/apiuat/aliPay/orderClose";


    private static final String APP_ID = "8ab74856-8772-45c9-96db-54cb30ab9f74";
    private static final String SECRET = "5b96f20a-011f-4254-8be8-9a5ceb2f317f";
    private static final String PRIVATE_KEY = "D5F2AFA24E6BA9071B54A8C9AD735F9A1DE9C4657FA386C09B592694BC118B38";
    private static final String PUBLIC_KEY = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE6Q+fktsnY9OFP+LpSR5Udbxf5zHCFO0Pm OKlFNTxDIGl8jsPbbB/9ET23NV+acSz4FEkzD74sW2iiNVHRLiKHg==";

    private static final String MER_ID = "3089991727300AY";
    private static final String USER_ID = "V000001049";
    private static final String TERM_ID = "20201015";
    private static final String SUB_APP_ID = "wx7f9fd8b0426c4f7b";
    private static final String OPEN_ID = "oGFfks1us_Lloiyb5ULVItPac6ak";

//    private static final String MER_ID = "308999156510020";
//    private static final String USER_ID = "V000008624";
//    private static final String TERM_ID = "20201015";
//    private static final String SUB_APP_ID = "wx7f9fd8b0426c4f7b";
//    private static final String OPEN_ID = "oGFfks1us_Lloiyb5ULVItPac6ak";


    private static void payTest() throws Throwable {
        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.bizSet(BusinessFields.MER_ID, MER_ID);
//        requestBuilder.bizSet(BusinessFields.SUB_APP_ID, SUB_APP_ID);
        requestBuilder.bizSet(BusinessFields.ORDER_ID, System.currentTimeMillis() + "");
        requestBuilder.bizSet(BusinessFields.AUTH_CODE, "135577762479149292");
        requestBuilder.bizSet(BusinessFields.USER_ID, USER_ID);
        requestBuilder.bizSet(BusinessFields.TERM_ID, TERM_ID);
//        requestBuilder.bizSet(BusinessFields.NOTIFY_URL, "https://shouqinaba.com");
        requestBuilder.bizSet(BusinessFields.TXN_AMT, "1");
        requestBuilder.bizSet(BusinessFields.BODY, "test001");
        requestBuilder.bizSet(BusinessFields.TRADE_SCENE, CmbConstants.TRADE_SCENE_OFFLINE);


        CmbClient cmbClient = new CmbClient();
        Map<String, Object> result = cmbClient.call(PAY_URL, requestBuilder, APP_ID, SECRET, PRIVATE_KEY);
        System.out.println(JsonUtil.objectToJsonString(result));
    }

    private static void decpPayTest() throws Throwable {
        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.bizSet(BusinessFields.MER_ID, MER_ID);
//        requestBuilder.bizSet(BusinessFields.SUB_APP_ID, SUB_APP_ID);
        requestBuilder.bizSet(BusinessFields.ORDER_ID, System.currentTimeMillis() + "");
        requestBuilder.bizSet(BusinessFields.AUTH_CODE, "0100231003303922408");
        requestBuilder.bizSet(BusinessFields.USER_ID, USER_ID);
        requestBuilder.bizSet(BusinessFields.NOTIFY_URL, "https://e9b9af6332fa.ngrok.io/upay/v2/notify/cmbPay/f1b7107af761829d61f57ccc6365609d/d2d6b%267894259263047772%2623%26st-1580000000607817%26008da1da-d7e5-4b3a-9b4e-473e3b04812c%261%261668395864582%261%260%260");
        requestBuilder.bizSet(BusinessFields.TRANSACTION_TYPE, "TT01");
        requestBuilder.bizSet(BusinessFields.CURRENCY_CODE, "156");
        requestBuilder.bizSet(BusinessFields.TXN_AMT, "1");
        requestBuilder.bizSet(BusinessFields.TERMINAL_NO, "************");
        requestBuilder.bizSet(BusinessFields.TERMINAL_IP, "************");
        requestBuilder.bizSet(BusinessFields.GOODS_NAME, "香蕉");
        requestBuilder.bizSet(BusinessFields.TRADE_PLACE, "127.0.0.1");
        requestBuilder.bizSet(BusinessFields.ORDER_TIME_EXPIRE, "2022-11-16T10:10:10");

        CmbClient cmbClient = new CmbClient();
        Map<String, Object> result = cmbClient.call(DCEP_PAY_URL, requestBuilder, APP_ID, SECRET, PRIVATE_KEY);
        System.out.println(JsonUtil.objectToJsonString(result));
    }

    private static void queryOrderTest() throws Throwable {
        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.bizSet(BusinessFields.MER_ID, MER_ID);
        requestBuilder.bizSet(BusinessFields.ORDER_ID, "1667544148271");
        requestBuilder.bizSet(BusinessFields.USER_ID, USER_ID);

        CmbClient cmbClient = new CmbClient();
        Map<String, Object> result = cmbClient.call(ORDER_QUERY_URL, requestBuilder, APP_ID, SECRET, PRIVATE_KEY);
        System.out.println(JsonUtil.objectToJsonString(result));
    }

    private static void refundTest() throws Throwable {
        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.bizSet(BusinessFields.MER_ID, MER_ID);
        requestBuilder.bizSet(BusinessFields.ORDER_ID, System.currentTimeMillis());
        requestBuilder.bizSet(BusinessFields.USER_ID, USER_ID);
        requestBuilder.bizSet(BusinessFields.ORIG_ORDER_ID, "1667544148271");
        requestBuilder.bizSet(BusinessFields.TXN_AMT, "1");
        requestBuilder.bizSet(BusinessFields.REFUND_AMT, "1");

        CmbClient cmbClient = new CmbClient();
        Map<String, Object> result = cmbClient.call(REFUND_URL, requestBuilder, APP_ID, SECRET, PRIVATE_KEY);
        System.out.println(JsonUtil.objectToJsonString(result));
    }

    private static void queryRefundTest() throws Throwable {
        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.bizSet(BusinessFields.MER_ID, MER_ID);
        requestBuilder.bizSet(BusinessFields.ORDER_ID, "1647395492900");
        requestBuilder.bizSet(BusinessFields.USER_ID, USER_ID);

        CmbClient cmbClient = new CmbClient();
        Map<String, Object> result = cmbClient.call(REFUND_QUERY_URL, requestBuilder, APP_ID, SECRET, PRIVATE_KEY);
        System.out.println(JsonUtil.objectToJsonString(result));
    }

    private static void cancelTest() throws Throwable {
        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.bizSet(BusinessFields.MER_ID, MER_ID);
        requestBuilder.bizSet(BusinessFields.ORIG_ORDER_ID, "1603267277526");
        requestBuilder.bizSet(BusinessFields.USER_ID, USER_ID);

        CmbClient cmbClient = new CmbClient();
        Map<String, Object> result = cmbClient.call(CANCEL_URL, requestBuilder, APP_ID, SECRET, PRIVATE_KEY);
        System.out.println(JsonUtil.objectToJsonString(result));
    }





    private static void alipayWayTest() throws Throwable {
        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.bizSet(BusinessFields.MER_ID, MER_ID);
        requestBuilder.bizSet(BusinessFields.ORDER_ID, System.currentTimeMillis() + "");
        requestBuilder.bizSet(BusinessFields.TRADE_SCENE, CmbConstants.TRADE_SCENE_OFFLINE);
        requestBuilder.bizSet(BusinessFields.USER_ID, USER_ID);
        requestBuilder.bizSet(BusinessFields.NOTIFY_URL, "https://shouqianba.com");
        requestBuilder.bizSet(BusinessFields.BODY, "测试商品001");
        requestBuilder.bizSet(BusinessFields.TXN_AMT, "1");
        requestBuilder.bizSet(BusinessFields.BUYER_ID, "2088222035343046");

        CmbClient cmbClient = new CmbClient();
        Map<String, Object> result = cmbClient.call(ALIPAY_URL, requestBuilder, APP_ID, SECRET, PRIVATE_KEY);
        System.out.println(JsonUtil.objectToJsonString(result));
    }

    private static void wxWapTest() throws Throwable {
        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.bizSet(BusinessFields.MER_ID, MER_ID);
        requestBuilder.bizSet(BusinessFields.USER_ID, USER_ID);
        requestBuilder.bizSet(BusinessFields.SUB_APP_ID, SUB_APP_ID);
        requestBuilder.bizSet(BusinessFields.ORDER_ID, System.currentTimeMillis() + "");
        requestBuilder.bizSet(BusinessFields.TRADE_TYPE, CmbConstants.TRADE_TYPE_MINI);
        requestBuilder.bizSet(BusinessFields.TRADE_SCENE, CmbConstants.TRADE_SCENE_OFFLINE);
        requestBuilder.bizSet(BusinessFields.BODY, "测试商品001");
        requestBuilder.bizSet(BusinessFields.TXN_AMT, "1");
        requestBuilder.bizSet(BusinessFields.SPBILL_CREATE_IP, "127.0.0.1");
        requestBuilder.bizSet(BusinessFields.NOTIFY_URL, "https://shouqianba.com");
        requestBuilder.bizSet(BusinessFields.SUB_OPEN_ID, OPEN_ID);

        CmbClient cmbClient = new CmbClient();
        Map<String, Object> result = cmbClient.call(WX_URL, requestBuilder, APP_ID, SECRET, PRIVATE_KEY);
        System.out.println(JsonUtil.objectToJsonString(result));
    }

//    private static void alipayWayQueryTest() throws Throwable {
//        RequestBuilder requestBuilder = new RequestBuilder();
//        requestBuilder.bizSet(BusinessFields.MER_ID, MER_ID);
//        requestBuilder.bizSet(BusinessFields.ORDER_ID, "1602840864902");
//        requestBuilder.bizSet(BusinessFields.USER_ID, USER_ID);
//
//        CmbClient cmbClient = new CmbClient();
//        Map<String, Object> result = cmbClient.call(ALIPAY_QUERY_URL, requestBuilder, APP_ID, SECRET, PRIVATE_KEY);
//        System.out.println(JsonUtil.objectToJsonString(result));
//    }

//    private static void alipayWayRefundTest() throws Throwable {
//        RequestBuilder requestBuilder = new RequestBuilder();
//        requestBuilder.bizSet(BusinessFields.MER_ID, MER_ID);
//        requestBuilder.bizSet(BusinessFields.ORDER_ID, System.currentTimeMillis() + "");
//        requestBuilder.bizSet(BusinessFields.USER_ID, USER_ID);
//        requestBuilder.bizSet(BusinessFields.ORIG_ORDER_ID, "1602833181877");
//        requestBuilder.bizSet(BusinessFields.NOTIFY_URL, "https://shouqianba.com");
//        requestBuilder.bizSet(BusinessFields.TXN_AMT, "1");
//        requestBuilder.bizSet(BusinessFields.REFUND_AMT, "1");
//
//        CmbClient cmbClient = new CmbClient();
//        Map<String, Object> result = cmbClient.call(ALIPAY_REFUND_URL, requestBuilder, APP_ID, SECRET, PRIVATE_KEY);
//        System.out.println(JsonUtil.objectToJsonString(result));
//    }

//    private static void alipayWayRefundQueryTest() throws Throwable {
//        RequestBuilder requestBuilder = new RequestBuilder();
//        requestBuilder.bizSet(BusinessFields.MER_ID, MER_ID);
//        requestBuilder.bizSet(BusinessFields.ORDER_ID, "1602833439708");
//        requestBuilder.bizSet(BusinessFields.USER_ID, USER_ID);
//
//        CmbClient cmbClient = new CmbClient();
//        Map<String, Object> result = cmbClient.call(ALIPAY_REFUND_QUERY_URL, requestBuilder, APP_ID, SECRET, PRIVATE_KEY);
//        System.out.println(JsonUtil.objectToJsonString(result));
//    }


//    private static void alipayWayCancelTest() throws Throwable {
//        RequestBuilder requestBuilder = new RequestBuilder();
//        requestBuilder.bizSet(BusinessFields.MER_ID, MER_ID);
//        requestBuilder.bizSet(BusinessFields.ORIG_ORDER_ID, "1602833906018");
//        requestBuilder.bizSet(BusinessFields.USER_ID, USER_ID);
//
//        CmbClient cmbClient = new CmbClient();
//        Map<String, Object> result = cmbClient.call(ALIPAY_CLOSE_URL, requestBuilder, APP_ID, SECRET, PRIVATE_KEY);
//        System.out.println(JsonUtil.objectToJsonString(result));
//    }



//    private static void wxWapRefundTest() throws Throwable {
//
//    }
//
//    private static void wxWapCancelTest() throws Throwable {
//
//    }
//
//    private static void wxWapQuery() throws Throwable {
//
//    }
//
//    private static void wxWapRefundQueryTest() throws Throwable {
//
//    }


    public static void main(String[] args) throws Throwable {
//        decpPayTest();
//        payTest();
//        queryOrderTest();
//        refundTest();
//        queryRefundTest();
//        cancelTest();
//        alipayWayTest();
//        wxWapTest();
    }
}
