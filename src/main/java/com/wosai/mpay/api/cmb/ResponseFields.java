package com.wosai.mpay.api.cmb;

/**
 * <AUTHOR> Date: 2020/10/14 Time: 11:06 上午
 */
public class ResponseFields {

    public static final String RETURN_CODE = "returnCode"; //SUCCESS/FAIL,此字段是通信标 识，非交易标识，交易是否成功需要 查看respCode来判断。SUCCESS 表示商户上送的报文符合规范，FAIL 表示报文内的字段不符合规范，包括 ⻓度超⻓、非法字符、签名错误等
    public static final String RESP_CODE = "respCode"; //招行返回的业务错误码，成功为SUCCESS，失败为FAIL，若此时errCode为SYSTERM_ERROR则表示本次请求处理异常，并非交易结果是失败状态，需要商户再次发起查询确认
    public static final String ERR_DESCRIPTION = "errDescription"; //付款码收款交易在第三方处理失败时返回第三方的错误信息
    public static final String MER_ID = "merId"; //请求报文中的商户号
    public static final String ORDER_ID = "orderId"; //请求报文中的商户订单号
    public static final String CMB_ORDER_ID = "cmbOrderId"; //招行生成的订单号
    public static final String PAY_TYPE = "payType"; //支付方式, 目前支持:支付宝/微信/银联  ZF:支付宝 WX:微信 YL:银联
    public static final String OPEN_ID = "openId"; //用户标识, 支付宝支付:用户在支付宝系统的唯一标识  微信支付:商户上送sub_appid时返 回用户在sub_appid下的sub_openid
    public static final String PAY_BANK = "payBank"; //付款银行, 支付宝支付: ALIPAYACCOUNT 支付宝账户 PCREDIT 蚂蚁花呗 DEBIT_CARD 借记卡  CREDIT_CARD 信用卡  MIXED_CARD 借贷合一卡 等 微信支付:CMB_CREDIT 等
    public static final String THIRD_ORDER_ID = "thirdOrderId"; //第三方订单号, 支付宝、微信侧的订单号
    public static final String BUYER_LOGON_ID = "buyerLogonId"; //支付宝账户, 支付宝支付的交易返回
    public static final String TRADE_STATE = "tradeState"; //交易状态, P-支付状态未知(可能需要用户输入支付密码) F-支付失败 S-支付成功
    public static final String TXN_AMT = "txnAmt"; //交易金额, 单位为分
    public static final String DSC_AMT = "dscAmt"; //优惠金额, 单位为分
    public static final String PROMOTION_DETAIL = "promotionDetail"; //优惠券信息,微信、支付宝优惠券信息
    public static final String TXN_TIME = "txnTime"; //订单发送时间
    public static final String END_DATE = "endDate"; //订单完成日期
    public static final String END_TIME = "endTime"; //订单完成时间,第三方系统返回的交易完成时间，此时间仅在成功是返回，格式为HHmmss若交易是支付宝和微信通道，则成功时返回此字段若交易是银联通道，则不返回此字段
    public static final String ERR_CODE = "errCode"; //请求处理失败的错误码信息
    public static final String RESP_MSG = "respMsg"; //请求处理失败的详细描述信息
    public static final String MWEB_URL = "mwebUrl"; //trade_type为MWEB时返回， mweb_url为拉起微信支付收银台的 中间⻚面，可通过访问该url来拉起 微信客户，完成支付，mweb_url的 有效期为5分钟。
    public static final String PAY_DATA = "payData"; //JSON字符串，trade_type为JSAPI 或APP时返回，拉起支付时使用;trade_type为JSAPI时，用于微信浏 览器里面打开H5网⻚中执行JS调起 支付,或者用于小程序支付。 trade_type为APP时，用于APP端调 起支付。
    /**其中，当trade_type为JSAPI时，payData说明如下 start**/
    public static final String APP_ID = "appId"; //如果是公众号内H5支付，appId对应商户注册具有支付权限的公众号成功后,获取的appId;  如果是小程序支付，appId对应特约商户小程序对应的appId;
    public static final String TIME_STAMP = "timeStamp"; //Linux格式时间戳，如1414561699
    public static final String NONCE_STR = "nonceStr"; //随机字符串
    public static final String WX_PACKAGE = "package"; //统一下单接口返回的prepay_id参数值，格式如:prepay_id=123456789
    public static final String SIGN_TYPE = "signType"; //固定位RSA
    public static final String PAY_SIGN = "paySign"; //服务商秘钥加签的签名
    /**其中，当trade_type为JSAPI时，payData说明如下 end**/
    public static final String PAY_INFO = "payInfo"; //支付信息,唤起支付宝支付使用
    public static final String REFUND_AMT = "refundAmt"; //退款金额,单位为分
    public static final String REFUND_DSC_AMT = "refundDscAmt"; //优惠金额,单位为分
    public static final String COUPON_INFO = "couponInfo"; //银联通道且有优惠活动时出现
    public static final String REFUND_STATE = "refundState"; //P 退款正在处理(原交易为微信渠 道) S 退款成功(原交易为支付宝、银联 渠道) F 退款失败(原交易为微信、支付 宝、银联渠道)  仅当respCode为SUCCESS时出现
    public static final String CANCEL_STATE = "cancelState"; //撤销处理状态, D:撤销成功 F:被撤销交易为失败状态，撤销失败

    //微信优惠券信息
    public static final String PROMOTION_ID = "promotion_id";
    public static final String NAME = "name";
    public static final String SCOPE = "scope";
    public static final String TYPE = "type";
    public static final String AMOUNT = "amount";
    public static final String ACTIVITY_ID = "activity_id";
    public static final String WXPAY_CONTRIBUTE = "wxpay_contribute";
    public static final String MERCHANT_CONTRIBUTE = "merchant_contribute";
    public static final String OTHER_CONTRIBUTE = "other_contribute";
    public static final String GOODS_DETAIL = "goods_detail";
    //goods_detail
    public static final String GOODS_ID = "goods_id";
    public static final String GOODS_REMARK = "goods_remark";
    public static final String DISCOUNT_AMOUNT = "discount_amount";
    public static final String QUANTITY = "quantity";
    public static final String PRICE = "price";

    //支付宝优惠券信息
    public static final String ID = "id";
    public static final String VOUCHER_ID = "voucherId";
    public static final String MEMO = "memo";
    public static final String TEMPLATE_ID = "template_id";
    public static final String ZF_MERCHANT_CONTRIBUTE = "merchantContribute";
    public static final String ZF_OTHER_CONTRIBUTE = "otherContribute";
    public static final String OTHER_CONTRIBUTE_DETAIL = "other_contribute_detail";
    public static final String PURCHASE_BUYER_CONTRIBUTE = "purchase_buyer_contribute";
    public static final String PURCHASE_MERCHANT_CONTRIBUTE ="purchase_merchant_contribute";
    public static final String PURCHASE_ANT_CONTRIBUTE = "purchase_ant_contribute";
    //ContributeDetail字段说明
    public static final String CONTRIBUTE_TYPE = "contribute_type";
    public static final String CONTRIBUTE_AMOUNT = "contribute_amount";

}
