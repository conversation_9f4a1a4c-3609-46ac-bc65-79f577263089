package com.wosai.mpay.api.cmb;

import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Date: 2020/10/14 Time: 11:05 上午
 */
public class RequestBuilder {
    private Map<String, String> request;
    private Map<String, Object> bizContent;

    public RequestBuilder() {
        request = new HashMap<>();
        bizContent = new HashMap<>();
        request.put(ProtocolFields.ENCODING, CmbConstants.ENCODING);
        request.put(ProtocolFields.SIGN_METHOD, CmbConstants.SIGN_METHOD_SM2);
        request.put(ProtocolFields.VERSION, CmbConstants.VERSION);
    }

    public void baseSet(String field, String value) {
        request.put(field, value);
    }

    public void bizSet(String field, Object value) {
        bizContent.put(field, value);
    }

    public Map<String, String> build () {
        String bizContentStr;
        try {
            bizContentStr = JsonUtil.objectToJsonString(bizContent);
        } catch (MpayException e) {
            throw new RuntimeException("CMB请求参数构建异常", e);
        }
        request.put(ProtocolFields.BIZ_CONTENT, bizContentStr);

        return request;
    }

    public Map<String, String> getRequest() {
        return request;
    }

    public Map<String, Object> getBizContent() {
        return bizContent;
    }
}
