package com.wosai.mpay.api.cmb;

/**
 * <AUTHOR> Date: 2020/10/14 Time: 11:05 上午
 */
public class BusinessFields {

    public static final String MER_ID = "merId"; //商户在招行完成商户进件后分配的招行商户号
    public static final String SUB_APP_ID = "subAppId"; //商户微信公众号appid，非微信支付 模式可不填，若此字段有上送，则同步返回用户在此subAppId下的 sub_openid
    public static final String ORDER_ID = "orderId"; //商户端生成，要求此订单号在整个商 户下唯一
    public static final String AUTH_CODE = "authCode"; //支付授权码，设备读取用户APP的条 码或者二维码信息
    public static final String USER_ID = "userId"; //招行系统生成的收银员，每个收银员 均会对应到一个⻔店。若商户无⻔ 店、收银员概念则上送默认的虚拟收 银员即可。
    public static final String TERM_ID = "termId"; //原则是可以通过交易上送的终端编号 准确定位商户每一个⻔店内每一台收 银设备，建议按“⻔店编号+收银机编 号”或“设备编号”组成8位终端编号在 交易中上送
    public static final String NOTIFY_URL = "notifyUrl"; //支付结果将送到本通知。
    public static final String TXN_AMT = "txnAmt"; //交易金额, 单位为分
    public static final String CURRENCY_CODE = "currencyCode";//默认156，目前只支持人⺠币(156)
    public static final String BODY = "body"; //用于展示支付宝账单详情中的“商品说明”和微信账单详情中的“商品”字 段，若不上送则默认显示商户简称。
    public static final String ITEM_DISCOUNT = "itemDiscount"; //第三方单品优惠信息描述
    public static final String GOODS_TAG = "goodsTag"; //商品标记，代金券或立减优惠功能的参数，详⻅微信官网文档中的代金券或立减优惠说明。
    public static final String MCH_RESERVED = "mchReserved"; //用于传输商户自定义数据，在支付结 果查询时原样返回
    public static final String TRADE_SCENE = "tradeScene"; // 交易场景,OFFLINE:线下场景  ONLINE:线上场景(APP支付时填写) INSURANCE: 保险 CHARITY:公益
    public static final String SUB_ORDER_LIST = "subOrderList"; //子单信息
    public static final String SUB_MER_ID = "subMerId";
    public static final String SUB_ORDER_ID = "subOrderId";
    public static final String SUB_SERVICE_FEE = "subServiceFee";
    public static final String SUB_STORE_ID = "subStoreId";
    public static final String SUB_BODY = "subBody";
    public static final String SUB_MCH_RESERVED = "subMchReserved";


    public static final String IDENTITY = "identity"; //实名支付信息 {"need_check": "T", //是否校验身份，T是F否"name": "",//名字 "number": "",//证件号 "mobile": "",//手机号 "type": "IDCARD"//证件类型，只支持身份证  }
    public static final String POLICY_NO = "policyNo"; //保单单号
    public static final String REGION = "region"; //地区码
    public static final String DEVICE_INFO = "deviceInfo"; //设备号, PC网⻚或公众号内支付请传"WEB"
    public static final String TRADE_TYPE = "tradeType"; //交易类型, APP支付:APP  H5支付:MWEB  公众号支付:JSAPI  小程序支付:JSAPI
    public static final String GOODS_DETAIL = "goodsDetail"; //商品详细描述
    public static final String ATTACH = "attach"; //附加数据
    public static final String PAY_VALID_TIME = ""; //支付数据的有效时间，单位为秒，应不小于60
    public static final String ORDER_ORIG_AMT = "orderOrigAmt"; //订单原始金额,单位为分，和orderCouponAmt同时出现
    public static final String ORDER_COUPON_AMT = "orderCouponAmt"; //订单优惠金额,单位为分，和orderOrigAmt同时出现
    public static final String SPBILL_CREATE_IP = "spbillCreateIp"; //终端IP,必须传正确的用户端IP
    public static final String LIMIT_PAY = "limitPay"; //限定支付方式, no_credit--指定不能使用信用卡支付;为空表示无限制
    public static final String OPEN_ID = "openId"; //用户标识, 用户在Appid下的唯一标识。
    public static final String SUB_OPEN_ID = "subOpenId"; //用户子标识, trade_type=JSAPI，此参数必传， 用户在subAppId下的唯一标识。
    public static final String SCENE_INFO = "sceneInfo"; //场景信息
    public static final String TIMEOUT_EXPRESS = "timeoutExpress"; //支付有效时间,该笔订单允许的最晚付款时间，逾期 将关闭交易。取值范围:1m~15d。 m-分钟，h-小时，d-天，1c-当天 (1c-当天的情况下，无论交易何时 创建，都在0点关闭)。 该参数数值 不接受小数点， 如 1.5h，可转换为 90m。
    public static final String DISABLE_PAY_CHANNELS = "disablePayChannels"; //禁用渠道,用户不可用指定渠道支付，多个渠道 以逗号分割
    public static final String BUYER_LOGON_ID = "buyerLogonId"; //买家支付宝账号，和buerId不能同时为空
    public static final String BUYER_ID = "buyerId"; //买家支付宝用户ID，和buyerLogonId不能同时为空
    public static final String ORIG_ORDER_ID = "origOrderId"; //原交易的商户订单号，此字段和原交易平台订单号字段至少要上送一个， 若两个都上送，则以原交易平台订单号为准
    public static final String ORIG_CMB_ORDER_ID = "origCmbOrderId"; //原交易招行订单号，此字段和原交易商户订单号字段至少要上送一个，若两个都上送，则以此字段为准
    public static final String REFUND_AMT = "refundAmt"; //退款金额，单位为分，refundAmt=refundOrigAmt- refundCouponAmt;
    public static final String REFUND_ORIG_AMT = "refundOrigAmt"; //退单原始金额，单位为分，与refundCouponAmt同时出现
    public static final String REFUND_COUPON_AMT = "refundCouponAmt"; //退单优惠金额，单位为分，与refundOrigAmt同时出现
    public static final String REFUND_REASON = "refundReason"; //退款原因

    public static final String TRANSACTION_TYPE = "transactionType";
    public static final String TERMINAL_NO = "terminalNo";
    public static final String TERMINAL_IP = "terminalIp";
    public static final String GOODS_NAME = "goodsName";
    public static final String ORDER_DETAILS = "orderDetails";
    public static final String PLATFORM_NAME = "platformName";
    public static final String TRADE_PLACE = "tradePlace";
    public static final String ORDER_TIME_EXPIRE = "orderTimeExpire";







}
