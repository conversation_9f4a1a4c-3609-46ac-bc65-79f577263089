package com.wosai.mpay.api.abcbank;

/**
 * <AUTHOR>
 * @description 农业银行-业务字段
 * @date 2025-07-17
 */
public class ABCBankBusinessFields {
    public static final String APP_ID = "appid";//农行分配给收钱吧的应用id

    // 商户信息
    public static final String MERCHANT_ID = "merchant_id";
    public static final String MCH_TRADENO = "mch_tradeno";
    public static final String GOODS_NAME = "goods_name";
    public static final String TIME_EXPIRE = "time_expire";
    public static final String SUB_APPID = "sub_appid";
    public static final String SUB_OPENID = "sub_openid";
    public static final String AUTH_CODE = "auth_code";
    public static final String NAME = "name";
    public static final String CERT_TYPE = "cert_type";
    public static final String CERT_NO = "cert_no";
    public static final String TXT_REMARKS = "txt_remarks";
    
    // 终端信息
    public static final String POS_GA = "pos_ga";
    public static final String SERIAL_NUM = "serial_num";
    public static final String APP_VERSION = "app_version";
    public static final String NETWORK_LICENSE = "network_license";
    public static final String DEVICE_TYPE = "device_type";
    public static final String TERMINAL_ID = "terminal_id";
    public static final String TERMINAL_TRANSFORM_FLAG = "terminal_transform_flag";//终端改造标识，001表示终端已改造，000或空值表示终端未改造。固定传001
    public static final String ENCRYPT_RAND_NUM = "encrypt_rand_num";
    public static final String SECRET_TEXT = "secret_text";
    public static final String TERMINAL_IP = "terminal_ip";
    
    // 支付信息
    public static final String DETAIL = "detail";
    public static final String GOODS_TAG = "goods_tag";
    public static final String GOODS_DETAIL = "goods_detail";
    public static final String DELIVERY_PROMO_TAGS = "delivery_promo_tags";
    public static final String ID_SERVICE_PROVIDER = "id_service_provider"; // 服务商ID
    public static final String CALL_BAK_URL = "call_bak_url"; // 支付跳转地址
    public static final String NOTIFY_URL = "notify_url"; // 支付回调地址
    public static final String WX_API_TYPE = "wx_api_type"; // 交易类型
    public static final String SPBILL_CREATE_IP = "spbill_create_ip"; // 终端IP
    public static final String PAY_ACTV_TIME = "pay_actv_time"; // 支付有效时长(分钟)
    
    // 二维码平台返回结果
    public static final String COD_ANSW_COD = "cod_answ_cod";   // 应答码
    public static final String TXT_ANSW_INFO = "txt_answ_info"; // 应答信息
    public static final String TRADE_NO = "trade_no";           // 农行订单号
    public static final String OUT_TRADE_NO = "out_trade_no";   // 二维码平台订单号
    public static final String OUT_TRANS_ID = "out_trans_id";   // 第三方支付侧订单号，如微信、支付宝订单号
    public static final String REFERENCE_NO = "reference_no";   // 参考号
    public static final String TH_JRN_NO = "th_jrn_no";         // 太行流水号
    public static final String PREPAY_ID = "prepay_id";         // 预支付交易会话标识
    public static final String CODE_URL = "code_url";           // 二维码链接
    public static final String MWEB_URL = "mweb_url";           // 支付跳转链接
    public static final String WC_PAY_DATA = "wc_pay_data";     // 微信APP调用数据
    public static final String ORDER_STATUS = "order_status"; //订单状态
    public static final String BUYER_USER_ID = "buyer_user_id"; //用户标识
    public static final String OUT_REFUND_ID = "out_refund_id"; //第三方支付侧退款单号
    public static final String OUT_REFUND_NO = "out_refund_no"; //二维码平台退款单号
    public static final String ORDER_TIME = "order_time";       //订单完成时间
    public static final String TOTAL_AMOUNT = "total_amount";   //订单总金额
    public static final String RECIEPT_AMOUNT = "reciept_amount"; //现金支付金额。reciept_amount=total_amount-coupon_fee
    public static final String SETTLEMENT_TOTAL_FEE = "settlement_total_fee"; //应结订单金额。当订单使用了免充值型优惠券后返回该参数，应结订单金额=订单金额-免充值优惠券金额。
    public static final String COUPON_FEE = "coupon_fee"; //代金券金额

    // 微信签名参数
    public static final String WECHAT_APP_ID = "appId"; // 微信公众号、小程序id
    public static final String TIMESTAMP = "timeStamp"; // 时间戳
    public static final String NONCE_STR = "nonceStr"; // 随机字符串
    public static final String PACKAGE = "package"; // prepay_id参数值，格式如：prepay_id=xxx
    public static final String SIGN_TYPE = "signType"; // 签名方式
    public static final String PAY_SIGN = "paySign"; // 签名

    //支付宝参数
    public static final String ALIPAY_APP_ID = "app_id"; // 支付宝appid
    public static final String ALIPAY_BUYER_ID = "buyer_id"; // 支付宝用户标识

    //退款
    public static final String REFUND_AMOUNT = "refunt_amount";//退款金额
    public static final String MCH_REFUNDNO = "mch_refundno";//商户退款单号
}
