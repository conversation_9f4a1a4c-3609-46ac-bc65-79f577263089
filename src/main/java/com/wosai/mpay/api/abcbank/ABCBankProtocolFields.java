package com.wosai.mpay.api.abcbank;

/**
 * <AUTHOR>
 * @description
 * @date 2025-07-17
 */
public class ABCBankProtocolFields {
    /**
     * 协议默认值
     */
    public static final String DEFAULT_SIGN_ALGORITHM = "SHA1withRSA"; //默认的签名算法
    public static final String DEFAULT_CONTENT_TYPE = "application/json";
    public static final String DEFAULT_CHARSET = "utf-8";
    public static final String DEFAULT_ENCRYPT_TYPE = "AES";
    public static final String DEFAULT_SIGN_TYPE = "SHA256";
    public static final String DEFAULT_TERMINAL_TRANSFORM_FLAG = "001";
    public static final String DEFAULT_WECHAT_SIGN_METHOD = "RSA"; //微信默认签名方法

    /**
     * 系统级请求参数
     */
    public static final String CONTENT_TYPE = "Content-type";
    public static final String HEADER = "header";
    public static final String CHARSET = "charset"; //字符集, 默认"utf-8"
    public static String SIGN = "sign";//签名
    public static String SIGN_TYPE = "sign_type";//签名类型
    public static String ENCRYPT_TYPE = "encrypt_type";//加密类型
    public static String TIMESTAMP = "timestamp";
    public static String NONCE = "nonce";
    public static final String ENCRYPT_DATA = "encrypt_data";
    public static final String BIZ_DATA = "biz_data";
    public static final String DATA = "data";
}
