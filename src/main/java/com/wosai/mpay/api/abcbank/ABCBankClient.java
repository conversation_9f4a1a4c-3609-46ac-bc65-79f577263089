package com.wosai.mpay.api.abcbank;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

import static com.wosai.mpay.api.abcbank.ABCBankConstant.SEPARATOR;
import static com.wosai.mpay.api.abcbank.ABCBankProtocolFields.DEFAULT_CONTENT_TYPE;

/**
 * <AUTHOR>
 * @description 农业银行
 * @date 2025/07/15
 */
public class ABCBankClient {
    public static final Logger log = LoggerFactory.getLogger(ABCBankClient.class);

    private int connectTimeout = 5000;
    private int readTimeout = 10000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    /**
     * 请求
     *
     * @param url              请求地址
     * @param params           请求参数
     * @param appSecret        密钥
     * @param sqbPrivateKey    收钱吧私钥
     * @param abcBankPublicKey 农行公钥
     * @return
     * @throws MpayException
     * @throws MpayApiNetworkError
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> call(String url, Map<String, Object> params, String appSecret, String sqbPrivateKey, String abcBankPublicKey)
            throws MpayException, MpayApiNetworkError {

        String request = JsonUtil.objectToJsonString(params.remove(ABCBankProtocolFields.BIZ_DATA));
        log.info("request {}", request);

        //加密
        String encryptData = AesUtil.encrypt(request, appSecret);
        params.put(ABCBankProtocolFields.ENCRYPT_DATA, encryptData);

        String sign = getSign(params, sqbPrivateKey);
        params.put(ABCBankProtocolFields.SIGN, sign);

        String response = WebUtils.doPost(null, null, url, DEFAULT_CONTENT_TYPE,
                JsonUtil.objectToJsonString(params).getBytes(), connectTimeout, readTimeout);

        ABCBankResponse abcBankResponse;
        try {
            abcBankResponse = JsonUtil.jsonStringToObject(response, ABCBankResponse.class);
        } catch (Exception e) {
            throw new MpayException("响应报文解析失败, response: " + response);
        }

        if (!ABCBankConstant.SYSTEM_SUCCESS.equals(abcBankResponse.getCode())) {
            log.info("response {}", response);
            Map result = new HashMap<>();
            result.put(ABCBankResponseFields.CODE, abcBankResponse.getCode());
            result.put(ABCBankResponseFields.MSG, abcBankResponse.getMsg());
            return result;
        }

        String responseSignData = getResponseSignData(abcBankResponse);
        if (!RsaSignature.validateSign(responseSignData.getBytes(), abcBankResponse.getSign(), RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, abcBankPublicKey)) {
            log.warn("响应数据验签失败, response {}", response);
            throw new MpayException("响应数据验签失败: " + response);
        }
        //解密响应结果
        String decryptResponse = AesUtil.decrypt(abcBankResponse.getBiz_encrypt(), appSecret);
        log.info("response {}", decryptResponse);

        Map result = JsonUtil.jsonStringToObject(decryptResponse, Map.class);
        String resCode = MapUtils.getString(result, ABCBankResponseFields.RES_CODE);
        String resMessage = MapUtils.getString(result, ABCBankResponseFields.RES_MESSAGE);
        String errorDescription = MapUtils.getString(result, ABCBankResponseFields.ERROR_DESCRIPTION);
        result.put(ABCBankResponseFields.MSG, resMessage + ": " + errorDescription);
        result.put(ABCBankResponseFields.CODE, resCode);
        if (!ABCBankConstant.SYSTEM_SUCCESS.equals(resCode)) {
            return  result;
        }

        Map data = MapUtils.getMap(result, ABCBankProtocolFields.DATA);
        if (MapUtils.isNotEmpty(data)) {
            result.remove(ABCBankProtocolFields.DATA);
            result.putAll(data);
        }
        return result;
    }

    private String getSign(Map<String, Object> params, String sqbPrivateKey) throws MpayException {
        String sortedData = getSortedSignData(params);
        return RsaSignature.sign(sortedData, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, sqbPrivateKey);
    }

    private static String getResponseSignData(ABCBankResponse abcBankResponse) {
        return abcBankResponse.getBiz_encrypt() + SEPARATOR + abcBankResponse.getResponseid();
    }

    /**
     * 获取排序后的待签名数据
     *
     * @param params 待签名的参数
     * @return 返回排序后的待签名字符串
     */
    public static String getSortedSignData(Map<String, Object> params) {
        // 使用treeMap保证参数的顺序
        TreeMap<String, Object> sortMap = new TreeMap<>(params);
        StringBuilder sb = new StringBuilder(1024);
        for (Map.Entry<String, Object> entry : sortMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value == null
                    || key.equalsIgnoreCase(ABCBankProtocolFields.SIGN)
                    || key.equalsIgnoreCase(ABCBankResponseFields.CODE)
                    || key.equalsIgnoreCase(ABCBankResponseFields.MSG)) {
                continue;
            }
            if ((value instanceof String) && ((String) value).isEmpty()) {
                continue;
            }
            sb.append(value).append(SEPARATOR);
        }

        if (sb.length() - 1 >= 0) {
            sb.deleteCharAt(sb.length() - 1);
        }

        // 返回最终的参数字符串
        return sb.toString();
    }
}
