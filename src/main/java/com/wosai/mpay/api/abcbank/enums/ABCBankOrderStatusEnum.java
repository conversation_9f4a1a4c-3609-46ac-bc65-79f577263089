package com.wosai.mpay.api.abcbank.enums;


/**
 * <AUTHOR>
 * @description 农业银行-订单状态
 * @date 2025-07-21
 */
public enum ABCBankOrderStatusEnum {
    UNKNOWN("UNKNOWN", "支付结果未知"),
    SUCCESS("SUCCESS", "支付成功"),
    PROCESSING("PROCESSING", "处理中"),
    FAILED("FAIL","支付失败");

    private final String status;
    private final String desc;

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    ABCBankOrderStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static ABCBankOrderStatusEnum of(String status) {
        if (null == status) {
            return UNKNOWN;
        }
        for (ABCBankOrderStatusEnum e : ABCBankOrderStatusEnum.values()) {
            if (e.status.equals(status)) {
                return e;
            }
        }
        return UNKNOWN;
    }
}


