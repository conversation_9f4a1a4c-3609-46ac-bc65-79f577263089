package com.wosai.mpay.api.baifubao;

import java.lang.reflect.Array;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 15/11/17.
 */
public class BaifubaoConstants {
    /** 支付异步通知地址 **/
    public static final String NOTIFY_URL = "https://shouqianba.com";

    public static final String VERSION = "2";

    /** 字符集 **/
    public static final String CHARSET_GBK     = "1"; //百付宝规定的字符串编码取值范围,1是GBK

    public static SimpleDateFormat DATE_SDF = new SimpleDateFormat("yyyyMMddHHmmss");


    /** 默认时间格式 **/
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /** FEE_TYPE 货币类型**/
    public static final String FEE_TYPE_CNY = "1"; //百付宝规定的币种取值范围,1为人民币

    /**服务编号*/
    public static final String SERVICE_CODE_PAY = "1";//收款服务编号,表示即时到账
    public static final String SERVICE_CODE_REFUND = "2";//退款服务编号
    public static final String SERVICE_CODE_REFUNDQUERY = "12";//退款查询 的服务编号


    /**摘要算法*/
    public static final String SIGN_TYPE_MD5    = "1";//百付宝规定的摘要算法取值范围,1: MD5
    public static final String SIGN_TYPE_RSA    = "2";//百付宝规定的摘要算法取值范围,2: SHA-1


    /**退款,接受通知的请求方式. 1为GET，2为POST*/
    public static final String REFUND_RETURN_METHOD_GET = "1";
    public static final String REFUND_RETURN_METHOD_POST = "2";

    /**响应格式编码*/
    public static final String OUTPUPT_TYPE_XML = "1";//XML
    public static final String OUTPUT_TYPE_JSON = "2";//JSON

    public static final String QRCODE_OUTPUT_TYPE_IMAGE = "0";//二维码支付,输出格式: image
    public static final String QRCODE_OUTPUT_TYPE_JSON = "1";//二维码支付,输出格式,1是JSON -_-!!!

    public static final String CODE_TYPE_0 = "0";//百付宝C扫B必传的码类型,0

    public static final String PAY_TYPE_WALLET = "1";//余额支付（必须登录百度钱包）
    public static final String PAY_TYPE_EBANK = "2";//网银支付（在百度钱包页面上选择银行，可以不登录百度钱包）
    public static final String PAY_TYPE_PGSB = "3";//the Payment Gateway System of Bank; 银行网关支付（直接跳到银行的支付页面，无需登录百度钱包）


    /** 通信结果返回 **/
    public static final String RETURN_CODE_SUCCESS = "SUCCESS";
    public static final String RETURN_CODE_FAIL = "FAIL";

    /** 业务结果返回**/
    public static final String RESULT_CODE_SUCCESS = "0";//成功
    public static final String RESULT_CODE_INVALID_PARAMS = "5004";//参数错误
    public static final String RESULT_CODE_SERVER_SYS_ERROR = "1000";//服务器内部错误
    public static final String RESULT_CODE_SERVER_SYS_ERROR1 = "65201";//服务器内部错误
    public static final String RESULT_CODE__LACK_OF_PARAMS = "65202";//缺少参数
    public static final String RESULT_CODE_ILLEGAL_PARAMS = "65203";//参数非法
    public static final String RESULT_CODE_ILLEGAL_SIGN = "65204";//签名验证失败
    public static final String RESULT_CODE_ILLEGAL_SIGN_TYPE = "65205";//签名方式错误
    public static final String RESULT_CODE_TRADE_NOT_EXIST = "65213";//支付订单不存在

    public static final List<String> RESULT_SYSTEM_ERROR = Arrays.asList(RESULT_CODE_SERVER_SYS_ERROR,RESULT_CODE_SERVER_SYS_ERROR1);
    public static final List<String> RESULT_PROTOCAL_ERROR = Arrays.asList(RESULT_CODE_INVALID_PARAMS,RESULT_CODE__LACK_OF_PARAMS,RESULT_CODE_ILLEGAL_PARAMS,RESULT_CODE_ILLEGAL_SIGN,RESULT_CODE_ILLEGAL_SIGN_TYPE);

    /**付款返回码*/
    public static final String RESULT_CODE_PAY_PAID = "65215";//交易已付款
    public static final String RESULT_CODE_PAY_BARCODE_LOSEFFICACY = "65235";//付款码失效，请刷新重试。
    public static final String RESULT_CODE_PAY_CUSTOMER_PAY_ERROR = "65236";//用户端二次支付失败。
    public static final String RESULT_CODE_PAY_DEVICE_CHANGED = "69441";//支付时请不要更换设备
    public static final String RESULT_CODE_PAY_SELLER_HAS_NO_PRIVILEGE = "69506";//该商户暂不支持该服务
    public static final String RESULT_CODE_PAY_ORDER_EXPIRED = "69510";//订单已过期
    public static final String RESULT_CODE_PAY_BARCODE_EXPIRED = "69511";//付款码已过期
    public static final String RESULT_CODE_PAY_BUYER_WALLET_NOT_ENOUGH = "69515";//余额不足
    public static final String RESULT_CODE_PAY_NOT_YOURSELF = "69552";//用户不一致
    public static final String RESULT_CODE_PAY_WAIT_BUYER_PAY = "69556";//请用户输入密码确认支付
    public static final String RESULT_CODE_PAY_FAIL = "69557";//处理失败，交易可能存在风险，若是本人操作请联系客服400-8988-855。

    /**查单,支付结果列表*/
    public static final String RESULT_CODE_QUERY_PAY_IN_PROG = "1";//等待支付
    public static final String RESULT_CODE_QUERY_PAY_SUCCESS = "2";//支付成功
    public static final String RESULT_CODE_QUERY_PAY_FAIL = "10";//支付失败
    public static final String RESULT_CODE_QUERY_TRADE_SUCCESS = "3";//交易成功
    public static final String RESULT_CODE_QUERY_REFUND_SUCCESS = "4";//退款成功
    public static final String RESULT_CODE_QUERY_CANCEL_SUCCESS = "5";//交易取消
    public static final String RESULT_CODE_QUERY_TRADE_CLOSE = "6";//交易终止(失败)
    public static final String RESULT_CODE_QUERY_PARTLY_REFUND = "7";//部分退款
    public static final String RESULT_CODE_QUERY_TRADE_FAILED = "8";//交易失败




    /**退款返回码*/
    public static final String REFUND_CODE_SUCC = "1";//退款成功
    public static final String REFUND_CODE_ORDER_NOT_EXIST = "2";//交易不存在
    public static final String REFUND_CODE_SELLER_NOT_EXIST = "3";//商户不存在
    public static final String REFUND_CODE_INVALID_AMOUNT = "4";//退款金额不正确
    public static final String REFUND_CODE_INVALID_TRADE_STATUS = "5";//交易状态不正确
    public static final String REFUND_CODE_SYSTEM_ERROR = "6";//系统错误
    public static final String REFUND_CODE_ERROR_PASSWORD = "7";//支付密码错误
    public static final String REFUND_CODE_INVALID_REFUND_STATUS = "8";//退款状态不正确
    public static final String REFUND_CODE_ACCOUNT_FROZEN = "9";//买卖家账户被冻结
    public static final String REFUND_CODE_NOT_ENOUGH_MONEY = "10";//账户金额不足
    public static final String REFUND_CODE_REPEATD_REFUND_ACTION = "11";//退款重复调用
    public static final String REFUND_CODE_TIMEOUT = "12";//请求超时
    public static final String REFUND_CODE_INVALID_SP_REFUND_NO = "13";//sp_refund_no超出21位
    public static final String REFUND_CODE_DUPLICATED_SP_REFUND_NO = "14";//sp_refund_no重复
    public static final String REFUND_CODE_CANNOT_PARTLY_REFUND = "15";//此交易不允许部分退款
    public static final String REFUND_CODE_CANNOT_REFUND = "16";//此银行不支持原路退回
    public static final String REFUND_CODE_LACK_OF_PARAMS = "5801";//缺少参数
    public static final String REFUND_CODE_INVALID_PARAMS = "5802";//无效参数
    public static final String REFUND_CODE_ILLEGAL_SIGN_TYPE = "5803";//无效的签名方法
    public static final String REFUND_CODE_ILLEGAL_SIGN = "5804";//验签失败
    public static final String REFUND_CODE_TRADE_NOT_MATCH = "5805";//交易和商户不匹配
    public static final String REFUND_CODE_BFB_INTERNAL_ERROR = "5806";//内部错误，请重试
    public static final String REFUND_CODE_REFUND_NOT_EXIST = "5809";//退款记录不存在
    public static final String REFUND_CODE_ILLEGAL_TRADE = "5810";//交易状态不正确，不允许退款
    public static final String REFUND_CODE_TIMEOUT_AGAIN = "5009";//请求超时

    //接口返回错误列表 刷卡支付 支付结果失败
    public static final List<String> REFUND_NEED_QUERY = Arrays.asList(
             REFUND_CODE_SYSTEM_ERROR,REFUND_CODE_TIMEOUT,REFUND_CODE_TIMEOUT_AGAIN,REFUND_CODE_BFB_INTERNAL_ERROR
                );


    /**退款查询返回码*/
    public static final String REFUND_QUERY_CODE_SUCC = "1";//已退款至百度钱包余额
    public static final String REFUND_QUERY_CODE_IN_PROG = "2";//退回银行卡处理中   百度官方解释是退款成功了-1218
    public static final String REFUND_QUERY_CODE_SUCC_ROLLBACK = "3";//原路退款成功（如果是余额支付，表示已退至余额；如果是银行卡支付，表示已退至银行卡）
    public static final String REFUND_QUERY_CODE_FAIL = "4";//退款失败(商户未扣款)
    public static final String REFUND_QUERY_CODE_SUCC_1 = "5";//退银行卡失败，请用户联系百度钱包 .百度官方解释是退款成功了-1218.
    public static final String REFUND_QUERY_CODE_LACK_OF_PARAMS = "5801";//缺少参数
    public static final String REFUND_QUERY_CODE_INVALID_PARAMS = "5802";//无效参数
    public static final String REFUND_QUERY_CODE_INVALID_SIGH_TYPE = "5803";//无效的签名方法
    public static final String REFUND_QUERY_CODE_ILLEGAL_SIGN = "5804";//验签失败
    public static final String REFUND_QUERY_CODE_NOT_MATCH = "5805";//交易和商户不匹配
    public static final String REFUND_QUERY_CODE_SYS_ERROR = "5806";//内部错误，请稍后重新发起查询
    public static final String REFUND_QUERY_CODE_REFUND_RECORD_NOT_EXIST = "5809";//退款记录不存在


    public static final String REFUND_STATE_IN_WAIT_TO_PROCESS = "1";//单笔退款状态,待处理
    public static final String REFUND_STATE_ROLLBACK_IN_PROGE = "2";//原路退回处理中;
    public static final String REFUND_STATE_SUCC = "3";//已退到余额
    public static final String REFUND_STATE_FAIL = "4";//退回余额失败
    public static final String REFUND_STATE_SUCC_TOBANK = "5";//退回银行卡成功
    public static final String REFUND_STATE_FAIL_BANK = "6";//退回银行失败

    public static final List<String> REFUND_STATE_SUCCESS_LIST = Arrays.asList(REFUND_STATE_ROLLBACK_IN_PROGE,REFUND_STATE_SUCC,REFUND_STATE_SUCC_TOBANK,REFUND_STATE_IN_WAIT_TO_PROCESS);

    public static final List<String> REFUND_SUCC = Arrays.asList(REFUND_QUERY_CODE_SUCC,REFUND_QUERY_CODE_SUCC_ROLLBACK,REFUND_QUERY_CODE_IN_PROG,REFUND_QUERY_CODE_SUCC_1);
    public static final List<String> REFUND_SYS_ERROR = Arrays.asList(REFUND_QUERY_CODE_SYS_ERROR,REFUND_QUERY_CODE_FAIL);
    public static final List<String> REFUND_PROTOCAL_ERROR = Arrays.asList(REFUND_QUERY_CODE_LACK_OF_PARAMS,REFUND_QUERY_CODE_INVALID_PARAMS,REFUND_QUERY_CODE_INVALID_SIGH_TYPE,REFUND_QUERY_CODE_ILLEGAL_SIGN,REFUND_QUERY_CODE_NOT_MATCH);

    public static final String REVERSE_CODE_TRADE_NOT_EXIST = "101";//该笔交易不存在
    public static final String REVERSE_CODE_TRADE_CANCELED = "102";//该笔交易已经取消
    public static final String REVERSE_CODE_SYSTEMER = "103";//系统错误
    public static final String REVERSE_CODE_ORDER_HAS_PAID = "104";//该笔交易已经支付成功
    public static final String REVERSE_CODE_NO_PRIVILEGE = "105";//商户没有取消订单权限
    public static final String REVERSE_CODE_SELLER_NOT_EXIST = "106";//商户号对应商户不存在
    public static final String REVERSE_CODE_ACTION_NOT_ALLOWED = "107";//该类型交易不允许被关闭
    public static final String REVERSE_CODE_FAIL = "108";//只有处于创建状态的订单才能被关闭


    /** 异步通知结果 **/
    public static final String NOTIFY_PAY_RESULT_PAY_SUCCESS = "1";//支付成功
    public static final String NOTIFY_PAY_RESULT_PAY_WAIT = "2";//等待支付
    public static final String NOTIFY_PAY_RESULT_REFUND_SUCCESS = "3";//退款成功




}
