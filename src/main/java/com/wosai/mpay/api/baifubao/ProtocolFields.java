package com.wosai.mpay.api.baifubao;

/**
 * Created by <PERSON><PERSON><PERSON> on 15/11/17.
 */
public class ProtocolFields {
    public static final String INPUT_CHARSET = "input_charset";
    public static final String SERVICE_CODE = "service_code";
    public static final String SP_NO = "sp_no";

    public static final String SIGN = "sign";
    public static final String SIGN_METHOD = "sign_method";
    public static final String NOTIFY_URL = "notify_url";
    public static final String VERSION = "version";
    public static final String OUTPUT_TYPE = "output_type";//退款的时候需要传的  响应数据的格式.
    public static final String OUTPUT_CHARSET = "output_charset";
    public static final String REFUND_RETURN_URL = "return_url";//退款服务器异步地址
    public static final String REFUND_RETURN_METHOD = "return_method";//退款,后台接收通知的请求方式

    //二维码支付的时候,code_type,code_size,output_type不参与签名
    public static final String QRCODE_CODE_TYPE = "code_type";//整数,目前必须是0
    public static final String QRCODE_CODE_SIZE = "code_size";//取值范围: 1-10;默认为2;

    public static final String RESP_SUFFIX = "response";
}
