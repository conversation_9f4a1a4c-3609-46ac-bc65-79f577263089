package com.wosai.mpay.api.baifubao;

/**
 * Created by <PERSON><PERSON><PERSON> on 15/11/17.
 */
public class BaifubaoConfig {
    //微信3种支付  刷卡支付 公众号支付 扫码支付
    public static final String PAY = "https://www.baifubao.com/o2o/0/b2c/0/api/0/pay/0";//提交刷卡支付API
    public static final String QUERY = "https://www.baifubao.com/o2o/0/b2c/0/api/0/query_trans/0";//订单查询
    public static final String REVERSE = "https://www.baifubao.com/api/0/cancel";//撤销订单
    public static final String REFUND = "https://www.baifubao.com/api/0/refund";//申请退款
    public static final String REFUND_QUERY = "https://www.baifubao.com/api/0/refund/0/query";//退款查询接口
    public static final String PRE_CREATE = "https://www.baifubao.com/o2o/0/code/0/create/0";//C扫B预下单

    public static final String DEFAULT_SP_NO = "1000241705";//商户ID
    public static final String DEFAULT_SP_KEY = "";//合作密钥

}
