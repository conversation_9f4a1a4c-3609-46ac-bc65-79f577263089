package com.wosai.mpay.api.baifubao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.exception.AlipayV2Exception;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.BaifubaoSignature;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.XmlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.security.Security;
import java.util.Map;

/**
 * Created by maoyu on 15/11/17.
 */
public class BaifubaoClient {
    private static final Logger logger = LoggerFactory.getLogger(BaifubaoClient.class);
    private static final ObjectMapper om = new ObjectMapper();
    private String signType = BaifubaoConstants.SIGN_TYPE_MD5;
    private String charset = "GBK" ;//百付宝请求执行用GBK..


    static {
        //清除安全设置
        Security.setProperty("jdk.certpath.disabledAlgorithms", "");
    }


    public Map<String, Object> call(String url, String key, Map<String, String> request) throws MpayException, MpayApiNetworkError {
        request.put(ProtocolFields.SIGN_METHOD, signType);
        String sign = BaifubaoSignature.md5Sign(request, key, charset);
        request.put(ProtocolV2Fields.SIGN, sign);
        if(BaifubaoConfig.PRE_CREATE.equals(url)){
            //precreate 需要code_type,code_size,output_type,且不参与签名
            request.put(ProtocolFields.QRCODE_CODE_TYPE,BaifubaoConstants.CODE_TYPE_0);
            request.put(ProtocolFields.OUTPUT_TYPE,BaifubaoConstants.QRCODE_OUTPUT_TYPE_JSON);
        }
        logger.debug("request {}", request);
        try {
//            String resp = WebUtils.doGet(null, null, url, request, charset);
            String resp = HttpClientUtils.doGet(BaifubaoClient.class.getName(), null, null, url, request, charset, 30000, 30000);
            Map<String, Object> result=null;
            if(resp.startsWith("{") && resp.endsWith("}")){
                //json
                 result = om.readValue(resp, new TypeReference<Map<String, Object>>() {
                });

                for (String respKey: result.keySet()) {
                    if (respKey.endsWith(ProtocolFields.RESP_SUFFIX)){
                        result = (Map<String,Object>)result.get(respKey);
                    }
                }
            }else if(resp.startsWith("<") && resp.endsWith(">")){
                //xml
                result = XmlUtils.parse(resp);
            }
            logger.debug("response {}",resp);
            if (result == null || result.size()<1) {
                throw new AlipayV2Exception("99999", "invalid BAIFUBAO response", null, null);
            }
            formatResponseCodeMsg(result);
            return result;
        } catch (IOException e) {
            throw new AlipayV2Exception("99999", "IO error invoking BAIFUBAO api", null, null, e);
        }

    }

    private static void formatResponseCodeMsg(Map<String,Object> result){

        if(result.containsKey(BaifubaoBusinessFields.REFUND_RETURN_CODE)){
            result.put(BaifubaoBusinessFields.RETURN_CODE,result.get(BaifubaoBusinessFields.REFUND_RETURN_CODE));
            result.put(BaifubaoBusinessFields.RETURN_MSG,result.get(BaifubaoBusinessFields.REFUND_RETURN_MSG));
        }else if(result.containsKey(BaifubaoBusinessFields.REVERSE_RETURN_CODE)){
            result.put(BaifubaoBusinessFields.RETURN_CODE,result.get(BaifubaoBusinessFields.REVERSE_RETURN_CODE));
            result.put(BaifubaoBusinessFields.RETURN_MSG,result.get(BaifubaoBusinessFields.REVERSE_RETURN_MSG));
        }
    }

    public static void main(String[] args) throws MpayException, MpayApiNetworkError {

        /*String a = "{xlSds}";
        String b = "<xxlsdk>";
        if(a.startsWith("{") && a.endsWith("}")){
            System.out.println("JSON");
        }

        if(b.startsWith("<") &&  b.endsWith(">")){
            System.out.println("XML");
        }*/
        /*String nw = BaifubaoConstants.DATE_SDF.format(new Date());
        String n5 = BaifubaoConstants.DATE_SDF.format(new Date(System.currentTimeMillis() + 1000 * 60 * 5));
        System.out.println(nw+"----"+n5);
        */

       // testPay();
//        testQuery();
      testRefund();
//        testRefundQuery();
//        testCancel();
//        testPrecreate();


    }


    public static void testPay() throws MpayException, MpayApiNetworkError {
        BaifubaoClient client = new BaifubaoClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SERVICE_CODE,BaifubaoConstants.SERVICE_CODE_PAY);
        builder.set(ProtocolFields.SP_NO,BaifubaoConfig.DEFAULT_SP_NO);
        builder.set(ProtocolFields.INPUT_CHARSET,BaifubaoConstants.CHARSET_GBK);
        builder.set(ProtocolFields.VERSION,BaifubaoConstants.VERSION);
        builder.set(BaifubaoBusinessFields.ORDER_CREATE_TIME,"20151124163600");
        builder.set(BaifubaoBusinessFields.ORDER_NO,"000025923456789013");
        builder.set(BaifubaoBusinessFields.GOODS_NAME,"+甜甜圈+");
        builder.set(BaifubaoBusinessFields.GOODS_DESC,"ttq");
        builder.set(BaifubaoBusinessFields.TOTAL_AMOUNT,"1");
        builder.set(BaifubaoBusinessFields.CURRENCY,BaifubaoConstants.FEE_TYPE_CNY);
        //builder.set(BaifubaoBusinessFields.RETURN_URL,"https://shouqianba.com");
        builder.set(BaifubaoBusinessFields.EXPIRE_TIME,"20160101000001");
        builder.set(BaifubaoBusinessFields.PAY_CODE,"312656128602179172");
        builder.set(BaifubaoBusinessFields.MNAME,"");
        builder.set(BaifubaoBusinessFields.TNO,"");
        builder.set(BaifubaoBusinessFields.PROFIT_TYPE,"");
        builder.set(BaifubaoBusinessFields.PROFIT_SOLUTION,"");
        Map<String,String>request = builder.build();
        Map<String,Object> result = client.call(BaifubaoConfig.PAY, BaifubaoConfig.DEFAULT_SP_KEY,request);
        System.out.println(request);
        System.out.println(BaifubaoConfig.DEFAULT_SP_KEY);
        System.out.println(result);
    }

    public static void testQuery() throws MpayException, MpayApiNetworkError {
        BaifubaoClient client  = new BaifubaoClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SP_NO,BaifubaoConfig.DEFAULT_SP_NO);
        builder.set(BaifubaoBusinessFields.ORDER_NO,"0000458623590187");
        builder.set(ProtocolFields.INPUT_CHARSET,BaifubaoConstants.CHARSET_GBK);
        builder.set(ProtocolFields.VERSION,BaifubaoConstants.VERSION);
        builder.set(ProtocolFields.SIGN_METHOD,BaifubaoConstants.SIGN_TYPE_MD5);
        Map<String,String>request = builder.build();
        Map<String,Object> result = client.call(BaifubaoConfig.QUERY, BaifubaoConfig.DEFAULT_SP_KEY,request);
        System.out.println(request);
        System.out.println(BaifubaoConfig.DEFAULT_SP_KEY);
        System.out.println(result);
    }


    public static void testRefund() throws MpayException, MpayApiNetworkError {
        BaifubaoClient client  = new BaifubaoClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SERVICE_CODE,BaifubaoConstants.SERVICE_CODE_REFUND);
        builder.set(ProtocolFields.SP_NO,BaifubaoConfig.DEFAULT_SP_NO);
        builder.set(ProtocolFields.INPUT_CHARSET,BaifubaoConstants.CHARSET_GBK);
        builder.set(ProtocolFields.SIGN_METHOD,BaifubaoConstants.SIGN_TYPE_MD5);
        builder.set(ProtocolFields.OUTPUT_TYPE,BaifubaoConstants.OUTPUPT_TYPE_XML);
        builder.set(ProtocolFields.OUTPUT_CHARSET,BaifubaoConstants.CHARSET_GBK);
        builder.set(ProtocolFields.REFUND_RETURN_URL,"https://shouqianba.com");
        builder.set(ProtocolFields.VERSION,BaifubaoConstants.VERSION);
        builder.set(BaifubaoBusinessFields.CURRENCY,BaifubaoConstants.FEE_TYPE_CNY);
        builder.set(BaifubaoBusinessFields.ORDER_NO,"000025923456789013");
        builder.set(BaifubaoBusinessFields.CASHBACK_AMOUNT,"1");
        builder.set(BaifubaoBusinessFields.CASHBACK_TIME,"20151124111823");
        builder.set(BaifubaoBusinessFields.SP_REFUND_NO,"0023449920185930");
        Map<String,String>request = builder.build();
        Map<String,Object> result = client.call(BaifubaoConfig.REFUND, BaifubaoConfig.DEFAULT_SP_KEY,request);
        System.out.println(request);
        System.out.println(BaifubaoConfig.DEFAULT_SP_KEY);
        System.out.println(result);
    }


    public static void testRefundQuery() throws MpayException, MpayApiNetworkError {
        BaifubaoClient client  = new BaifubaoClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SERVICE_CODE,BaifubaoConstants.SERVICE_CODE_REFUNDQUERY);
        builder.set(ProtocolFields.SP_NO,BaifubaoConfig.DEFAULT_SP_NO);
        builder.set(ProtocolFields.OUTPUT_TYPE,BaifubaoConstants.OUTPUPT_TYPE_XML);
        builder.set(ProtocolFields.OUTPUT_CHARSET,BaifubaoConstants.CHARSET_GBK);
        builder.set(ProtocolFields.VERSION,BaifubaoConstants.VERSION);
        builder.set(ProtocolFields.SIGN_METHOD,BaifubaoConstants.SIGN_TYPE_MD5);
        builder.set(BaifubaoBusinessFields.ORDER_NO,"000025923456789013");
//        builder.set(BaifubaoBusinessFields.SP_REFUND_NO,"");
        Map<String,String>request = builder.build();
        Map<String,Object> result = client.call(BaifubaoConfig.REFUND_QUERY, BaifubaoConfig.DEFAULT_SP_KEY,request);
        System.out.println(request);
        System.out.println(BaifubaoConfig.DEFAULT_SP_KEY);
        System.out.println(result);
    }


    public static void testCancel() throws MpayException, MpayApiNetworkError {
        BaifubaoClient client  = new BaifubaoClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SP_NO,BaifubaoConfig.DEFAULT_SP_NO);
        builder.set(ProtocolFields.OUTPUT_TYPE,BaifubaoConstants.OUTPUT_TYPE_JSON);
        builder.set(ProtocolFields.OUTPUT_CHARSET,BaifubaoConstants.CHARSET_GBK);
        builder.set(ProtocolFields.SIGN_METHOD,BaifubaoConstants.SIGN_TYPE_MD5);
        builder.set(BaifubaoBusinessFields.ORDER_NO,"0000458623590187");
        Map<String,String>request = builder.build();
        Map<String,Object> result = client.call(BaifubaoConfig.REVERSE, BaifubaoConfig.DEFAULT_SP_KEY,request);
        System.out.println(request);
        System.out.println(BaifubaoConfig.DEFAULT_SP_KEY);
        System.out.println(result);
    }

    public static void testPrecreate() throws MpayException, MpayApiNetworkError {
        BaifubaoClient client  = new BaifubaoClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SERVICE_CODE,BaifubaoConstants.SERVICE_CODE_PAY);
        builder.set(ProtocolFields.INPUT_CHARSET,BaifubaoConstants.CHARSET_GBK);
        builder.set(ProtocolFields.VERSION,BaifubaoConstants.VERSION);
        builder.set(ProtocolFields.SIGN_METHOD,BaifubaoConstants.SIGN_TYPE_MD5);
        builder.set(ProtocolFields.SP_NO,BaifubaoConfig.DEFAULT_SP_NO);
        builder.set(BaifubaoBusinessFields.CURRENCY,BaifubaoConstants.FEE_TYPE_CNY);
        builder.set(BaifubaoBusinessFields.ORDER_CREATE_TIME,"20151124121212");
        builder.set(BaifubaoBusinessFields.ORDER_NO,"0000458623590187");
        builder.set(BaifubaoBusinessFields.GOODS_NAME,"+甜甜圈+");
        builder.set(BaifubaoBusinessFields.GOODS_DESC,"ttq");
        builder.set(BaifubaoBusinessFields.TOTAL_AMOUNT,"1");
        builder.set(BaifubaoBusinessFields.GOODS_URL,"");
        builder.set(BaifubaoBusinessFields.UNIT_AMOUNT,"1");
        builder.set(BaifubaoBusinessFields.UNIT_COUNT,"1");
        builder.set(BaifubaoBusinessFields.TRANSPORT_AMOUNT,"100");
        builder.set(BaifubaoBusinessFields.TOTAL_AMOUNT,"1");
        builder.set(BaifubaoBusinessFields.BUYER_SP_USERNAME,"Rain");
        builder.set(BaifubaoBusinessFields.RETURN_URL,"https://shouqianba.com");
        builder.set(BaifubaoBusinessFields.PAGE_URL,"");
        builder.set(BaifubaoBusinessFields.PAY_TYPE,BaifubaoConstants.PAY_TYPE_WALLET);
        builder.set(BaifubaoBusinessFields.BANK_NO,"");
        builder.set(BaifubaoBusinessFields.EXPIRE_TIME,"**************");
        builder.set(BaifubaoBusinessFields.SP_UNO,"");
        builder.set(BaifubaoBusinessFields.EXTRA,"");
        builder.set(BaifubaoBusinessFields.MNO,"");
        builder.set(BaifubaoBusinessFields.MNAME,"");
        builder.set(BaifubaoBusinessFields.TNO,"");
        Map<String,String>request = builder.build();
        Map<String,Object> result = client.call(BaifubaoConfig.PRE_CREATE, BaifubaoConfig.DEFAULT_SP_KEY,request);
        System.out.println(request);
        System.out.println(BaifubaoConfig.DEFAULT_SP_KEY);
        System.out.println(result);



    }



}