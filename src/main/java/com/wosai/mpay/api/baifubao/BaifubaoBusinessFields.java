package com.wosai.mpay.api.baifubao;

/**
 * Created by <PERSON><PERSON><PERSON> on 15/11/17.
 */
public class BaifubaoBusinessFields {

    public static final String RETURN_CODE = "ret" ;//返回码
    public static final String RETURN_MSG  = "msg" ;//返回信息描述
    public static final String RETURN_CONTENT = "content";//返回信息内容

    public static final String REFUND_RETURN_CODE = "ret_code";//退款返回码
    public static final String REFUND_RETURN_MSG = "ret_detail";//退款返回详情

    public static final String REVERSE_RETURN_CODE = "err_no";
    public static final String REVERSE_RETURN_MSG  = "err_msg";

    public static final String SERVICE_CODE = "service_code";//服务编号
    public static final String ORDER_CREATE_TIME = "order_create_time";//创建订单的时间
    public static final String GOODS_DESC = "goods_desc";//商品的描述信息
    public static final String GOODS_URL = "goods_url";//商品在商户网站上的URL。
    public static final String CURRENCY = "currency";//币种，默认人民币
    public static final String RETURN_URL = "return_url";//百度钱包主动通知商户支付结果的URL
    public static final String EXPIRE_TIME = "expire_time";//交易的超时时间
    public static final String INPUT_CHARSET = "input_charset";//请求参数的字符编码
    public static final String SIGN = "sign";//签名结果
    public static final String SIGN_METHOD = "sign_method";//签名方法
    public static final String EXTRA = "extra";//商户自定义数据
    public static final String PAY_CODE = "pay_code";//付款码
    public static final String PROFIT_TYPE = "profit_type";//分润类型
    public static final String PROFIT_SOLUTION = "profit_solution";//分润方案
    public static final String SP_NO = "sp_no";//商户号
    public static final String ORDER_NO = "order_no";// 商户订单号
    public static final String BFB_ORDER_NO = "bfb_order_no";//百度钱包交易号
    public static final String MNO = "mno";//实体商户号
    public static final String MNAME = "mname";//实体商户门店号
    public static final String TNO = "tno";//实体商户终端号
    public static final String GOODS_NAME = "goods_name";//商品名称
    public static final String CREATE_TIME = "create_time";//交易创建时间
    public static final String PAY_TIME = "pay_time";//交易支付时间
    public static final String TOTAL_AMOUNT = "total_amount";//总金额
    public static final String CASH_AMOUNT = "cash_amount";//交易现金金额
    public static final String MKT_AMOUNT = "mkt_amount";//营销优惠金额
    public static final String PAY_RESULT = "pay_result";//订单支付结果  1:等待支付; 2:支付成功; 10:支付失败
    public static final String VERSION = "version";//接口版本号
    public static final String PAY_ERR_CODE = "pay_err_code";//支付失败错误码
    public static final String PAY_ERR_INFO = "pay_err_info";//支付失败错误描述

    public static final String CASHBACK_AMOUNT = "cashback_amount";//退款金额
    public static final String CASHBACK_TIME = "cashback_time";//退款请求时间
    public static final String SP_REFUND_NO = "sp_refund_no";//商户退款流水号 String21
    public static final String STATE = "state";//退款的状态...

    public static final String UNIT_AMOUNT = "unit_amount";//商品单价
    public static final String UNIT_COUNT  = "unit_count";//商品数量
    public static final String TRANSPORT_AMOUNT = "transport_amount";//运费
    public static final String BUYER_SP_USERNAME = "buyer_sp_username";//买家在商户网站的用户名 不超过64个字符
    public static final String PAGE_URL = "page_url";//用户点击该URL可以返回到商户网站；该URL也可以起到通知支付结果的作用
    public static final String PAY_TYPE  = "pay_type";//默认支付方式
    public static final String BANK_NO = "bank_no";//银行网关支付（直接跳到银行的支付页面，无需登录百度钱包）
    public static final String SP_UNO = "sp_uno";//用户在商户端的用户id或者用户名(必须在商户端唯一，用来形成快捷支付合约)

    public static final String SP_PASS_THROUGH = "sp_pass_through";//参加百分之一折扣的 'sp_pass_through' => '{"offline_pay":"1"}'
    public static final String RET_DETAILS = "ret_details";//退款查询接口返回的信息
    public static final String REFUND_DETAIL = "refund_detail";//退款详情
	public static final String RESPONSE_KEY_REFUND_DETAIL = BaifubaoBusinessFields.RET_DETAILS + "." + BaifubaoBusinessFields.REFUND_DETAIL;





}
