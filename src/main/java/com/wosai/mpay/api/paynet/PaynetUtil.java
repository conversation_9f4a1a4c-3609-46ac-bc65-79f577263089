package com.wosai.mpay.api.paynet;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import com.wosai.mpay.api.paynet.bean.BizMessageIdr;

public class PaynetUtil {
    private static final Map<String, TransactionCodeInfo> SUPPORT_TRANSACTION_CODES = new HashMap<String, TransactionCodeInfo>();
    static {
        Stream.of(TransactionCodeInfo.values()).forEach(t -> SUPPORT_TRANSACTION_CODES.put(t.getChannelCode() + t.getCode(), t));
    }

    public static TransactionCodeInfo getTransactionByCode(String channelCode, String transactionCode) {
        return SUPPORT_TRANSACTION_CODES.get(channelCode + transactionCode);
    }

    public static final BizMessageIdr parseBizMsgIdr(String message) {
        if (message == null || message.length() < 22) {
            return null;
        }
        BizMessageIdr idr = new BizMessageIdr();
        idr.setCurrentDate(message.substring(0, 8));
        idr.setBicCode(message.substring(8, 16));
        idr.setTransactionCode(message.substring(16, 19));
        idr.setOriginator(message.substring(19, 20));
        idr.setChannelCode(message.substring(20, 22));
        idr.setSequenceNumber(message.substring(22));
        return idr;
    }

}
