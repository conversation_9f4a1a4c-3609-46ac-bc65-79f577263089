package com.wosai.mpay.api.paynet;

public enum ReasonCode {
    SUCCESS_TRANSACTION_ACCEPTED("00","Success/ Transaction Accepted","ACSP"),
    INVALID_TRANSACTION("12", "Invalid transaction", "RJCT"),
    INVALID_TRANSACTION_AMOUNT("13", "Unable to Credit, Invalid Amount", "RJCT"),
    INVALID_SIGNATURE("25", "Invalid signature", "RJCT"),
    INTERNAL_ERROR( "29", "Internal Error at Bank’s System – Technical Problem/Unknown Error", "RJCT"),
    MERCHANT_ACCOUNT_STATUS_ABNORMAL("45", "Merchant account is in either dormant/closed/blacklisted/hold/blocked status", "RJCT"),
    QR_VALIDATION_FAILED("46", "QR validation failed", "RJCT"),
    INVALID_SOURCE_OF_FUNDS("47", "Invalid source of funds", "RJCT"),
    QR_HAS_EXPIRED("48", "QR has expired", "RJCT"),
    QR_STATUS_VALIDATE_FAIL("49", "Merchant status is either Inactive/Suspended/Terminated Merchant", "RJCT"),
    QR_NOT_EXISTS("53", "Merchant account status is either Invalid/not exist", "RJCT"),
    TRANSACTION_NOT_ALLOWED("57", "Transaction not allowed", "RJCT"),

    PAYMENT_NOT_ACCEPTED("U110", "Payment Not Accepted", "RJCT")
    ;

    private String code;
    private String description;
    private String statusCode;

    ReasonCode(String code, String description, String statusCode) {
        this.code = code;
        this.description = description;
        this.statusCode = statusCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }
}
