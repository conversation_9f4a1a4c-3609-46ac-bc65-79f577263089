package com.wosai.mpay.api.paynet;

public enum TransactionCodeInfo {
    // QR
    QR_Payment("030", "QR Point of Sales payment via Credit Transfe", PaynetConstants.CHANNEL_QR,  MessageDefinition.PACS_008_001_06_01, MessageDefinition.PACS_002_001_08_01),
    QR_Enquiry("520", "Account resolution enquiry for Domestic QR", PaynetConstants.CHANNEL_QR, MessageDefinition.PACS_008_001_06_01, MessageDefinition.PACS_002_001_08_01),
    Transaction_Enquiry("630", "This API is used to retrieve the status of the QR payment using the Business Message Identifier", PaynetConstants.CHANNEL_QR, MessageDefinition.CAMT_005_001_08, MessageDefinition.CAMT_006_001_08),

    QR_CB_Payment("031", "QR Point of Sales payment via Credit Transfe for Cross border Inbound QR", PaynetConstants.CHANNEL_QR,  MessageDefinition.PACS_008_001_06_01, MessageDefinition.PACS_002_001_08_01),
    QR_CB_Enquiry("521", "Account resolution enquiry for Cross border Inbound QR", PaynetConstants.CHANNEL_QR, MessageDefinition.PACS_008_001_06_01, MessageDefinition.PACS_002_001_08_01),

    // Online Banking/Wallets
    Credit_Transfer("070", "Credit Transfer (RTP Redirect)", PaynetConstants.CHANNEL_OBW,  MessageDefinition.PACS_008_001_06, MessageDefinition.PACS_002_001_08),
    Credit_Transfer_Refund("012", "Request to Pay (Refund)", PaynetConstants.CHANNEL_OBW,  MessageDefinition.PACS_008_001_06, MessageDefinition.PACS_002_001_08),

    // AutoDebit
    Auto_Debit("080", "AutoDebit", PaynetConstants.CHANNEL_OBW,  MessageDefinition.PACS_008_001_06, MessageDefinition.PACS_002_001_08),
    Request_to_Pay_Refund("854", "Merchant Request to Pay Refund", PaynetConstants.CHANNEL_OBW,  MessageDefinition.PAIN_013_001_07, MessageDefinition.PAIN_014_001_07),

    ;

    /**
     * Transaction Code
     */
    private String code;

    /**
     * Channel Code
     */
    private String channelCode;

    /**
     * Description
     */
    private String description;

    /**
     * Request Message
     */
    private MessageDefinition requestMessage;

    /**
     * Response Message
     */
    private MessageDefinition responseMessage;

    TransactionCodeInfo(String code, String description, String channelCode, MessageDefinition requestMessage, MessageDefinition responseMessage) {
        this.code = code;
        this.description = description;
        this.channelCode = channelCode;
        this.requestMessage = requestMessage;
        this.responseMessage = responseMessage;
    }

    
    
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public MessageDefinition getRequestMessage() {
        return requestMessage;
    }

    public MessageDefinition getResponseMessage() {
        return responseMessage;
    }

    public String getChannelCode() {
        return channelCode;
    }

}
