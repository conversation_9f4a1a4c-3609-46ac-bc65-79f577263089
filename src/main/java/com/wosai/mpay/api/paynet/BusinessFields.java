package com.wosai.mpay.api.paynet;

public class BusinessFields {
    public static final String BUS_MSG = "BusMsg";
    public static final String APP_HDR = "AppHdr";
    public static final String FR = "Fr";
    public static final String To = "To";
    public static final String FIID = "FIId";
    public static final String FIN_INSTN_ID = "FinInstnId";
    public static final String OTHR = "Othr";
    public static final String ID = "Id";
    public static final String BIZ_MSG_IDR = "BizMsgIdr";
    public static final String MSG_DEF_IDR = "MsgDefIdr";
    public static final String BIZ_SVC = "BizSvc";
    public static final String CRE_DT = "CreDt";
    public static final String PSSBL_DPLCT = "PssblDplct";
    public static final String RLTD = "Rltd";
    public static final String RPP_SGNTR = "RPPSgntr";
    public static final String KEY_NBR = "KeyNbr";
    public static final String SIGNATURE = "Signature";
    public static final String DOCUMENT = "Document";
    public static final String FI_TO_FI_CSTMR_CDT_TRF_INF = "FIToFICstmrCdtTrfInf";
    public static final String FI_TO_FI_CSTMR_CDT_TRF = "FIToFICstmrCdtTrf";
    public static final String GRP_HDR = "GrpHdr";
    public static final String MSG_ID = "MsgId";
    public static final String CRE_DT_TM = "CreDtTm";
    public static final String NB_OF_TXS = "NbOfTxs";
    public static final String STTLM_INF = "SttlmInf";
    public static final String STTLM_MTD = "SttlmMtd";
    public static final String CDT_TRF_TX_INF = "CdtTrfTxInf";
    public static final String PMT_ID = "PmtId";
    public static final String END_TO_END_ID = "EndToEndId";
    public static final String TX_ID = "TxId";
    public static final String PMT_TP_INF = "PmtTpInf";
    public static final String CTGY_PURP = "CtgyPurp";
    public static final String PRTRY = "Prtry";
    public static final String CHRG_BR = "ChrgBr";
    public static final String DBTR = "Dbtr";
    public static final String NM = "Nm";
    public static final String TP = "Tp";
    public static final String DBTR_AGT = "DbtrAgt";
    public static final String CDTR_AGT = "CdtrAgt";
    public static final String CDTR = "Cdtr";
    public static final String CDTR_ACCT = "CdtrAcct";
    public static final String SPLMTRY_DATA = "SplmtryData";
    public static final String PLC_AND_NM = "PlcAndNm";
    public static final String ENVLP = "Envlp";
    public static final String INSTR_FOR_CDTR_AGT = "InstrForCdtrAgt";
    public static final String RECPT_REF = "RecptRef";
    public static final String PAYMNT_DESC = "PaymntDesc";
    public static final String SCND_VAL_IND = "ScndValInd";
    public static final String INSTR_FOR_DBTR_ACCT = "InstrForDbtrAcct";
    public static final String RSDNT_STS = "RsdntSts";
    public static final String PRD_TP = "PrdTp";
    public static final String SHARIA_CMPL = "ShariaCmpl";
    public static final String DTLS = "Dtls";
    public static final String IP_ADDR = "IPAddr";
    public static final String QR_TX_INFO = "QRTxInfo";
    public static final String QR_CD = "QRCd";
    public static final String QR_CATEGORY = "QRCategory";
    public static final String ACCEPTED_PYMT_TYPE = "AcceptedPymtType";
    public static final String PROMO_CD = "PromoCd";
    public static final String FIELD1 = "Field1";
    public static final String FIELD2 = "Field2";
    public static final String FIELD3 = "Field3";
    public static final String FIELD4 = "Field4";
    public static final String FIELD5 = "Field5";
    public static final String INTR_BK_STTLM_AMT = "IntrBkSttlmAmt";
    public static final String INTR_BK_STTLM_AMT_CCY = "IntrBkSttlmAmtCcy";
    public static final String FI_TO_FIPMT_STS_RPT_INF = "FIToFIPmtStsRptInf";
    public static final String FI_TO_FIPMT_STS_RPT = "FIToFIPmtStsRpt";
    public static final String ORGNL_GRP_INF_AND_STS = "OrgnlGrpInfAndSts";
    public static final String ORGNL_MSG_ID = "OrgnlMsgId";
    public static final String ORGNL_MSG_NM_ID = "OrgnlMsgNmId";
    public static final String TX_INF_AND_STS = "TxInfAndSts";
    public static final String ORGNL_END_TO_END_ID = "OrgnlEndToEndId";
    public static final String ORGNL_TX_ID = "OrgnlTxId";
    public static final String TX_STS = "TxSts";
    public static final String STS_RSN_INF = "StsRsnInf";
    public static final String RSN = "Rsn";
    public static final String CLR_SYS_REF = "ClrSysRef";
    public static final String ORGNL_TX_REF = "OrgnlTxRef";
    public static final String INTR_BK_STTLM_DT = "IntrBkSttlmDt";

}
