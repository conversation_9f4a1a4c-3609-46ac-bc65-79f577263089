package com.wosai.mpay.api.paynet;

import com.wosai.mpay.util.EmvQRParser;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/8/11.
 */
public class DuitNowQRParser {

    /**
     *
     * @param content
     * @param path 属性定义路径，嵌套的用.号拼接
     * @return
     */
    public static String getFieldContentByFieldPath(String content, String path){
       return EmvQRParser.getFieldContentByFieldPath(content, path);

    }


    public static Map<String, String> parseQRCodeContent(String content) {
        return EmvQRParser.parseQRCodeContent(content);
    }

}
