package com.wosai.mpay.api.paynet;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum MessageDefinition {
    PACS_008_001_06("pacs.008.001.06",
            Arrays.asList("/BusMsg/AppHdr/Fr/FIId/FinInstnId/Othr/Id",
                    "/BusMsg/AppHdr/To/FIId/FinInstnId/Othr/Id",
                    "/BusMsg/AppHdr/BizMsgIdr",
                    "/BusMsg/Document/FIToFICstmrCdtTrf/GrpHdr/MsgId", 
                    "/BusMsg/Document/FIToFICstmrCdtTrf/CdtTrfTxInf/0/PmtId/EndToEndId",
                    "/BusMsg/Document/FIToFICstmrCdtTrf/CdtTrfTxInf/0/IntrBkSttlmAmt",
                    "/BusMsg/Document/FIToFICstmrCdtTrf/CdtTrfTxInf/0/Dbtr/Nm",
                    "/BusMsg/Document/FIToFICstmrCdtTrf/CdtTrfTxInf/0/DbtrAcct/Id/Othr/Id",
                    "/BusMsg/Document/FIToFICstmrCdtTrf/CdtTrfTxInf/0/DbtrAgt/FinInstnId/Othr/Id",
                    "/BusMsg/Document/FIToFICstmrCdtTrf/CdtTrfTxInf/0/CdtrAgt/FinInstnId/Othr/Id",
                    "/BusMsg/Document/FIToFICstmrCdtTrf/CdtTrfTxInf/0/Cdtr/Nm",
                    "/BusMsg/Document/FIToFICstmrCdtTrf/CdtTrfTxInf/0/CdtrAcct/Id/Othr/Id")),
    PACS_002_001_08("pacs.002.001.08",
            Arrays.asList("/BusMsg/AppHdr/Fr/FIId/FinInstnId/Othr/Id",
                    "/BusMsg/AppHdr/To/FIId/FinInstnId/Othr/Id",
                    "/BusMsg/AppHdr/BizMsgIdr",
                    "/BusMsg/Document/FIToFIPmtStsRpt/GrpHdr/MsgId", 
                    "/BusMsg/Document/FIToFIPmtStsRpt/TxInfAndSts/0/OrgnlEndToEndId",
                    "/BusMsg/Document/FIToFIPmtStsRpt/TxInfAndSts/0/TxSts", 
                    "/BusMsg/Document/FIToFIPmtStsRpt/TxInfAndSts/0/StsRsnInf/0/Rsn/Prtry")),

    PACS_008_001_06_01("pacs.*************",
            Arrays.asList("/BusMsg/Document/FIToFICstmrCdtTrfInf/CdtTrfTxInf/0/PmtId/EndToEndId",
                    "/BusMsg/Document/FIToFICstmrCdtTrfInf/CdtTrfTxInf/0/IntrBkSttlmAmt",
                    "/BusMsg/Document/FIToFICstmrCdtTrfInf/CdtTrfTxInf/0/CdtrAgt/FinInstnId/Othr/Id",
                    "/BusMsg/Document/FIToFICstmrCdtTrfInf/CdtTrfTxInf/0/CdtrAcct/Id/Othr/Id")),
    PACS_002_001_08_01("pacs.*************",
            Arrays.asList("/BusMsg/Document/FIToFIPmtStsRptInf/GrpHdr/MsgId", 
                    "/BusMsg/Document/FIToFIPmtStsRptInf/TxInfAndSts/0/OrgnlEndToEndId",
                    "/BusMsg/Document/FIToFIPmtStsRptInf/TxInfAndSts/0/TxSts",
                    "/BusMsg/Document/FIToFIPmtStsRptInf/TxInfAndSts/0/StsRsnInf/0/Rsn/Prtry")),

    CAMT_005_001_08("camt.005.001.08",
            Arrays.asList("/BusMsg/AppHdr/Fr/FIId/FinInstnId/Othr/Id", 
                    "/BusMsg/AppHdr/To/FIId/FinInstnId/Othr/Id",
                    "/BusMsg/AppHdr/BizMsgIdr",
                    "/BusMsg/Document/GetTx/MsgHdr/MsgId",
                    "/BusMsg/Document/GetTx/MsgHdr/ReqTp/Prtry/Id",
                    "/BusMsg/Document/GetTx/TxQryDef/TxCrit/NewCrit/SchCrit/0/PmtSch/PmtId/0/TxId")),
    CAMT_006_001_08 ("camt.006.001.08",
            Arrays.asList("/BusMsg/AppHdr/Fr/FIId/FinInstnId/Othr/Id", 
                    "/BusMsg/AppHdr/To/FIId/FinInstnId/Othr/Id",
                    "/BusMsg/AppHdr/BizMsgIdr",
                    "/BusMsg/Document/RtrTx/MsgHdr/MsgId",
                    "/BusMsg/Document/RtrTx/MsgHdr/OrgnlBizQry/MsgId",
                    "/BusMsg/Document/RtrTx/RptOrErr/BizRpt/TxsSummry/EnqSts/Cd/Prtry",
                    "/BusMsg/Document/RtrTx/RptOrErr/BizRpt/TxsSummry/EnqSts/Rsn/Prtry")),

    PAIN_013_001_07("pain.013.001.07",
            Arrays.asList("/BusMsg/AppHdr/Fr/FIId/FinInstnId/Othr/Id", 
                    "/BusMsg/AppHdr/To/FIId/FinInstnId/Othr/Id",
                    "/BusMsg/AppHdr/BizMsgIdr",
                    "/BusMsg/Document/CdtrPmtActvtnReq/GrpHdr/MsgId",
                    "/BusMsg/Document/CdtrPmtActvtnReq/PmtInf/0/DbtrAgt/FinInstnId/Othr/Id",
                    "/BusMsg/Document/CdtrPmtActvtnReq/PmtInf/0/CdtTrfTx/0/PmtId/EndToEndId",
                    "/BusMsg/Document/CdtrPmtActvtnReq/PmtInf/0/CdtTrfTx/0/Amt/InstdAmt",
                    "/BusMsg/Document/CdtrPmtActvtnReq/PmtInf/0/CdtTrfTx/0/CdtrAgt/FinInstnId/Othr/Id")),
    PAIN_014_001_07("pain.014.001.07",
            Arrays.asList("/BusMsg/AppHdr/Fr/FIId/FinInstnId/Othr/Id", 
                    "/BusMsg/AppHdr/To/FIId/FinInstnId/Othr/Id",
                    "/BusMsg/AppHdr/BizMsgIdr",
                    "/BusMsg/Document/CdtrPmtActvtnReqStsRpt/GrpHdr/MsgId",
                    "/BusMsg/Document/CdtrPmtActvtnReqStsRpt/OrgnlPmtInfAndSts/TxInfAndSts/0/OrgnlEndToEndId",
                    "/BusMsg/Document/CdtrPmtActvtnReqStsRpt/OrgnlPmtInfAndSts/TxInfAndSts/0/TxSts",
                    "/BusMsg/Document/CdtrPmtActvtnReqStsRpt/OrgnlPmtInfAndSts/TxInfAndSts/0/StsRsnInf/0/Rsn/Prtry"))
    ;

    private String actionId;
    private List<String> signFields;

    MessageDefinition(String actionId, List<String> signFields) {
        this.actionId = actionId;
        this.signFields = signFields.stream().map(s -> s.replaceAll("/", ".")).map(s -> s.substring(1))
                .collect(Collectors.toList());
    }

    public String getActionId() {
        return actionId;
    }

    public void setActionId(String actionId) {
        this.actionId = actionId;
    }

    public List<String> getSignFields() {
        return signFields;
    }

    public void setSignFields(List<String> signFields) {
        this.signFields = signFields;
    }

}
