package com.wosai.mpay.api.unionpayopen;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayClientError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/3/4.
 */
public class UnionPayOpenClient {
    public static final Logger logger = LoggerFactory.getLogger(UnionPayOpenClient.class);
    public static final String CONTENT_TYPE = "application/json";

    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public UnionPayOpenClient() {
    }

    public UnionPayOpenClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public Map<String,Object> call(String gateway, String signKey, Map<String,Object> request) throws MpayException, MpayApiNetworkError {
        String sign;
        try {
            String signContent = RsaSignature.getSignCheckContent(request, ProtocolFields.SIGNATURE);
            sign = RsaSignature.sign(Digest.sha256(signContent.getBytes(UnionPayOpenConstants.CHARSET_UTF8)), RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, signKey);
        } catch (Exception e) {
            throw new MpayClientError("get sign error", e);
        }
        request.put(ProtocolFields.SIGNATURE, sign);
        String requestStr = JsonUtil.objectToJsonString(request);
        logger.debug("request {}", requestStr);
        String responseStr = HttpClientUtils.doPost(UnionPayOpenClient.class.getName(), null, null, gateway, CONTENT_TYPE, requestStr, UnionPayOpenConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        logger.debug("response {}", responseStr);
        try{
            Map<String,Object> result = JsonUtil.jsonStringToObject(responseStr, Map.class);
            processSomeJsonString(result);
            return result;
        }catch (Exception e){
            throw new MpayApiUnknownResponse("parse response error: " + e.getMessage(), e);
        }
    }

    /**
     * 银联开放平台返回的字段里面，有些值是json格式的字符串，需要把这些值转换为对象
     * @param result
     * @throws MpayException
     */
    public void processSomeJsonString(Map<String,Object> result) throws MpayException{
        if(result != null && result.containsKey(ResponseFields.PAYER_INFO) && result.get(ResponseFields.PAYER_INFO) instanceof String){
            String payerInfoStr = (String) result.get(ResponseFields.PAYER_INFO);
            if(StringUtils.empty(payerInfoStr)){
                result.put(ResponseFields.PAYER_INFO, null);
            }else{
                Map<String,Object> payInfo = JsonUtil.jsonStringToObject(result.get(ResponseFields.PAYER_INFO) + "", Map.class);
                result.put(ResponseFields.PAYER_INFO, payInfo);
            }

        }
        if(result != null && result.containsKey(ResponseFields.PAY_DATA) && result.get(ResponseFields.PAY_DATA) instanceof String){
            String payerInfoStr = (String) result.get(ResponseFields.PAY_DATA);
            if(StringUtils.empty(payerInfoStr)){
                result.put(ResponseFields.PAY_DATA, null);
            }else{
                Map<String,Object> payInfo = JsonUtil.jsonStringToObject(result.get(ResponseFields.PAY_DATA) + "", Map.class);
                result.put(ResponseFields.PAY_DATA, payInfo);
            }
        }
        if(result != null && result.containsKey(ResponseFields.COUPON_INFO) && result.get(ResponseFields.COUPON_INFO) instanceof String){
            String couponInfoStr = (String) result.get(ResponseFields.COUPON_INFO);
            if(StringUtils.empty(couponInfoStr)){
                result.put(ResponseFields.COUPON_INFO, null);
            }else{
                result.put(ResponseFields.COUPON_INFO, JsonUtil.jsonStringToObject(couponInfoStr, List.class));
            }
        }
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
}
