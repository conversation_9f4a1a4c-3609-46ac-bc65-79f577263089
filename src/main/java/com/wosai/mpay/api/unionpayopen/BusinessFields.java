package com.wosai.mpay.api.unionpayopen;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/3/4.
 */
public class BusinessFields {

    public static final String AREA_INFO = "areaInfo";// 地区信息
    public static final String AUTH_CODE = "authCode";// 付款码
    public static final String BACK_URL = "backUrl";// 交易通知地址
    public static final String CURRENCY_CODE = "currencyCode";// 交易币种
    public static final String CUSTOMER_IP = "customerIp";// 用户 IP
    public static final String DISCOUNTABLE_AMT = "discountableAmt";// 可打折金额
    public static final String FRONT_URL = "frontUrl";// 前台通知地址
    public static final String GOODS_INFO = "goodsInfo";// 商品详情
    public static final String GOODS_TAG = "goodsTag";// 商品标记
    public static final String LIMIT_PAY = "limitPay";// 限制支付
    public static final String OPERATOR_ID = "operatorId";// 商户操作员编号
    public static final String ORDER_INFO = "orderInfo";// 订单信息
    public static final String ORDER_NO = "orderNo";// 订单号
    public static final String ORDER_TIME = "orderTime";// 订单时间
    public static final String ORDER_TYPE = "orderType";// 订单类型
    public static final String ORIG_ORDER_NO = "origOrderNo";// 原订单号
    public static final String ORIG_TXN_AMT = "origTxnAmt";// 原交易金额
    public static final String PAYMENT_VALID_TIME = "paymentValidTime";// 支付有效时间
    public static final String ORDER_TIME_OUT = "orderTimeOut";//  订单超时时间
    public static final String PRODUCT_ID = "productId";// 产品 ID
    public static final String QR_CODE_ISSUER = "qrCodeIssuer";// 二维码发行方
    public static final String REQ_RESERVED = "reqReserved";// 服务商自定义域
    public static final String SCENE_INFO = "sceneInfo";// 场景信息
    public static final String STORE_ID = "storeId";// 商户门店编号
    public static final String SUB_APP_ID = "subAppId";// 子商户公众号 id
    public static final String TERM_INFO = "termInfo";// 终端信息
    public static final String TXN_AMT = "txnAmt";// 交易金额
    public static final String USER_ID = "userId";// 用户标识

    //终端ip信息
    public static final String TERM_INFO_IP = "ip";
    public static final String TERM_LATITUDE = "latitude";
    public static final String TERM_LONGITUDE = "longitude";
    public static final String TERM_DEVICE_TYPE = "deviceType";
    public static final String TERM_SERIAL_NUM = "serialNum";
}
