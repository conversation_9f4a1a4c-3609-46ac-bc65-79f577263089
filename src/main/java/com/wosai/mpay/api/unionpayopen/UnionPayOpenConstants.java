package com.wosai.mpay.api.unionpayopen;



import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/3/4.
 */
public class UnionPayOpenConstants {

    /** UTF-8字符集 **/
    public static final String CHARSET_UTF8 = "UTF-8";

    public static final String TERM_INFO_TERMINAL_TYPE = "11"; //条码支付辅助受理终端


    public static String ORDER_TIME_DATE_TIME_FORMAT = "yyyyMMddHHmmss"; //orderTime 20180702142900
    public static String PAY_TIME_DATE_TIME_FORMAT = "yyyyMMddHHmmss"; //payTime 20180629112830

    public static final String ORDER_TYPE_UP_JS = "upJs"; //银联 JS 支付
    public static final String ORDER_TYPE_WECHAT_JS = "wechatJs"; //微信公众号支付
    public static final String ORDER_TYPE_WECHAT_H5 = "wechatH5"; //微信 H5 支付
    public static final String ORDER_TYPE_WECHAT_APP = "wechatApp"; //微信 APP 支付
    public static final String ORDER_TYPE_WECHAT_MINI_PROGRAM = "wechatMiniProgram"; //微信小程序支付
    public static final String ORDER_TYPE_ALIPAY_JS = "alipayJs"; //支付宝服务窗支付
    public static final String ORDER_TYPE_ALIPAY_H5 = "alipayH5"; //支付宝 H5 支付
    public static final String ORDER_TYPE_ALIPAY_APP = "alipayApp"; //支付宝 APP 支付

    public static final String ORDER_TYPE_ALIPAY = "alipay";
    public static final String ORDER_TYPE_WECHAT = "wechat";
    public static final String ORDER_TYPE_UNIONPAY = "unionpay"; //银联二维码

    public static final String LIMIT_PAY_NO_CREDIT = "no_credit"; //指定不能使用信用卡支付

    public static final String CURRENCY_CODE_CNY = "156"; //人民币-156

    public static final String CARD_ATTR_DEBIT_CARD = "01"; // 借记卡
    public static final String CARD_ATTR_CREDIT_CARD = "02"; // 贷记卡

    public static final String COUPON_INFO_SPNSR_ID_UNIONPAY = "00010000"; //银联出资方
    public static final String COUPON_INFO_TYPE_DD01 = "DD01"; //随机立减
    public static final String COUPON_INFO_TYPE_CP01 = "CP01"; //抵金券 1:无需领取，交易时直接适配并承兑的优惠券
    public static final String COUPON_INFO_TYPE_CP02 = "CP02"; //抵金券 2:事前领取，交易时上送银联并承兑的优惠券


    /** 查单交易原交易应答码 **/
    public static final String QUERY_ORIG_RESP_CODE_SUCCESS = "00";//支付成功
    public static final String QUERY_ORIG_RESP_CODE_REFUND = "01";//转入退款
    public static final String QUERY_ORIG_RESP_CODE_NOTPAY = "02";//未支付
    public static final String QUERY_ORIG_RESP_CODE_CLOSED = "03";//已关闭
    public static final String QUERY_ORIG_RESP_CODE_REVOKED = "04";//已撤销（刷卡支付）
    public static final String QUERY_ORIG_RESP_CODE_USERPAYING = "05";//用户支付中
    public static final String QUERY_ORIG_RESP_CODE_PAYERROR = "06";//支付失败

    /** 退款查询交易原交易应答码 **/
    public static final String REFUND_QUERY_ORIG_RESP_CODE_SUCCESS = "00"; //退款成功
    public static final String REFUND_QUERY_ORIG_RESP_CODE_FAIL = "01"; //退款失败
    public static final String REFUND_QUERY_ORIG_RESP_CODE_IN_PROG = "02"; //退款处理中

    /** 应答码 **/
    public static final String RESP_CODE_SUCCESS = "00"; //成功
    public static final String RESP_CODE_SYSTEM_ERROR = "01"; //系统错误，交易失败
    public static final String RESP_CODE_IN_PROG = "02"; // 用户支付中，请稍后查询
    public static final String RESP_CODE_ORDER_REFUNDED  = "03"; // 交易已退款
    public static final String RESP_CODE_ORDER_UNKNOWN  = "04"; // 交易状态未明
    public static final String RESP_CODE_ORDER_PAID  = "05"; // 交易已被支付
    public static final String RESP_CODE_BUSY_RETRY_LATER  = "06"; // 系统繁忙，请稍后再试
    public static final String RESP_CODE_ORDER_CLOSED  = "07"; // 交易已经关闭
    public static final String RESP_CODE_INVALID_AMOUNT  = "08"; // 无效金额
    public static final String RESP_CODE_MERCHANT_REMARK_EXISTS  = "09"; // 商户备注已存在，请修改后重新提交
    public static final String RESP_CODE_INVALID_REQUEST_FORMAT  = "10"; // 报文格式错误
    public static final String RESP_CODE_INVALID_SIGN  = "11"; // 验证签名失败
    public static final String RESP_CODE_DUPLICATED_TRADE  = "12"; // 重复交易
    public static final String RESP_CODE_LACK_PARAMS  = "13"; // 报文交易要素缺失
    public static final String RESP_CODE_ORDER_REVOKED  = "14"; // 交易已撤销
    public static final String RESP_CODE_BALANCE_NOT_ENOUGH  = "15"; // 余额不足
    public static final String RESP_CODE_DUPLICATED_ORDER_SN  = "16"; // 订单号重复
    public static final String RESP_CODE_ORDER_NOT_EXISTS  = "17"; // 交易不存在
    public static final String RESP_CODE_AUTH_CODE_EXPIRE  = "20"; // 二维码已失效
    public static final String RESP_CODE_TRADE_COUNT_LIMITED  = "21"; // 支付次数超限
    public static final String RESP_CODE_AUTH_CODE_STATUS_INVALID  = "22"; // 二维码状态错误
    public static final String RESP_CODE_AUTH_CODE_NOT_EXISTS  = "23"; // 无此二维码
    public static final String RESP_CODE_MERCHANT_OR_AGENT_STATUS_INVALID  = "31"; // 商户或机构状态不正确
    public static final String RESP_CODE_AUTHORITY_DENY  = "32"; // 无此交易权限
    public static final String RESP_CODE_AMOUNT_LIMIT  = "33"; // 交易金额超限
    public static final String RESP_CODE_ORIG_NOT_EXISTS_OR_STATUS_INVALID  = "35"; // 原交易不存在或状态不正确
    public static final String RESP_CODE_ORIG_INFO_NOT_MATCH  = "36"; // 与原交易信息不符
    public static final String RESP_CODE_FREQUENCY_LIMITED  = "37"; // 已超过最大查询次数或操作过于频繁
    public static final String RESP_CODE_RISK_FAILED  = "38"; // 基于风险控制原因失败
    public static final String RESP_CODE_NOT_IN_BUSINESS_HOUR  = "39"; // 交易不在受理时间范围内


    public static final Set<String> PAY_RESP_CODE_FAIL_SET = new HashSet<String>(){{
        List<String> codes = Arrays.asList(
                RESP_CODE_INVALID_AMOUNT, RESP_CODE_INVALID_REQUEST_FORMAT, RESP_CODE_INVALID_SIGN, RESP_CODE_DUPLICATED_TRADE,
                RESP_CODE_LACK_PARAMS, RESP_CODE_DUPLICATED_ORDER_SN, RESP_CODE_ORDER_NOT_EXISTS, RESP_CODE_AUTH_CODE_EXPIRE,
                RESP_CODE_TRADE_COUNT_LIMITED, RESP_CODE_AUTH_CODE_STATUS_INVALID, RESP_CODE_AUTH_CODE_NOT_EXISTS,
                RESP_CODE_MERCHANT_OR_AGENT_STATUS_INVALID, RESP_CODE_AUTHORITY_DENY, RESP_CODE_AMOUNT_LIMIT, RESP_CODE_FREQUENCY_LIMITED,
                RESP_CODE_RISK_FAILED, RESP_CODE_NOT_IN_BUSINESS_HOUR
        );
        addAll(codes);
    }};

    public static final Set<String> TRADE_ERROR_FAIL_MESSAGE = new HashSet<String>(){{
        addAll(Arrays.asList(
                "收单机构返回错误：银行卡未开通认证支付","收单机构返回错误：输入的卡号无效，请确认后输入"
        ));
    }};


    /** 回调支付通知返回 **/
    public static final String NOTIFY_SUCCESS = "success";
    public static final String NOTIFY_FAIL = "fail";

}
