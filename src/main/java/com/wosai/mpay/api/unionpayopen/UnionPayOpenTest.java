package com.wosai.mpay.api.unionpayopen;


import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Digest;
import com.wosai.mpay.util.RsaSignature;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/3/4.
 */
public class UnionPayOpenTest {

    public static final String SER_PROV_ID = "";
    public static final String MER_ID = "";
    //我们的私钥
    public static final String PRIVATE_KEY = "";
    //平台的公钥
    public static final String PUBLIC_KEY = "";


    public static final String UNIFIED_ORDER = "http://ysf.bcbip.cn/gateway/api/pay/unifiedorder";
    public static final String PAY = "http://ysf.bcbip.cn/gateway/api/pay/refund";
    public static final String REFUND = "http://ysf.bcbip.cn/gateway/api/pay/refund";
    public static final String QUERY = "http://ysf.bcbip.cn/gateway/api/pay/queryOrder";
    public static final String CANCEL = "http://ysf.bcbip.cn/gateway/api/pay/cancelOrder";
    public static final String CLOSE = "http://ysf.bcbip.cn/gateway/api/pay/closeOrder";
    public static final String REFUND_QUERY = "http://ysf.bcbip.cn/gateway/api/pay/refundQuery";

    public static final UnionPayOpenClient client = new UnionPayOpenClient(3000, 5000);

    public static void main(String[] args) {
        testQuery();
    }


    public static void testPay(){
        RequestBuilder builder = getDefaultRequestBuilder();
        builder.set(BusinessFields.AUTH_CODE, "XX");
        builder.set(BusinessFields.ORDER_TYPE, UnionPayOpenConstants.ORDER_TYPE_UNIONPAY);
        builder.set(BusinessFields.ORDER_NO, getOrderNo());
        builder.set(BusinessFields.ORDER_TIME, getOrderTime());
        builder.set(BusinessFields.PAYMENT_VALID_TIME, "1");
        builder.set(BusinessFields.TXN_AMT, "1");
        builder.set(BusinessFields.CURRENCY_CODE, UnionPayOpenConstants.CURRENCY_CODE_CNY);
        builder.set(BusinessFields.ORDER_INFO, "星际飞船");
        try {
            Map<String,Object> response = client.call(PAY, PRIVATE_KEY, builder.build());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static void testPrecreate(){
        RequestBuilder builder = getDefaultRequestBuilder();
        builder.set(BusinessFields.ORDER_TYPE, UnionPayOpenConstants.ORDER_TYPE_UP_JS);
        builder.set(BusinessFields.ORDER_NO, getOrderNo());
        builder.set(BusinessFields.ORDER_TIME, getOrderTime());
        builder.set(BusinessFields.PAYMENT_VALID_TIME, "4");
        builder.set(BusinessFields.TXN_AMT, "1");
        builder.set(BusinessFields.CURRENCY_CODE, UnionPayOpenConstants.CURRENCY_CODE_CNY);
        builder.set(BusinessFields.ORDER_INFO, "星际飞船");
        builder.set(BusinessFields.BACK_URL, getNotifyUrl());
        builder.set(BusinessFields.CUSTOMER_IP, getUserIP());
        builder.set(BusinessFields.USER_ID, "xxx");
        try {
            Map<String,Object> response = client.call(UNIFIED_ORDER, PRIVATE_KEY, builder.build());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void testRefund(){
        RequestBuilder builder = getDefaultRequestBuilder();
        builder.set(BusinessFields.ORIG_ORDER_NO, "xxx");
        builder.set(BusinessFields.ORIG_TXN_AMT, "1"); // 为什么需要
        builder.set(BusinessFields.ORDER_NO, getOrderNo());
        builder.set(BusinessFields.TXN_AMT, "1");
        builder.set(BusinessFields.ORDER_TYPE, UnionPayOpenConstants.ORDER_TYPE_UNIONPAY); // 为什么需要
        try {
            Map<String,Object> response = client.call(REFUND, PRIVATE_KEY, builder.build());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    public static void testQuery(){
        RequestBuilder builder = getDefaultRequestBuilder();
        builder.set(BusinessFields.ORDER_NO, "xxx");
        builder.set(BusinessFields.ORDER_TYPE, UnionPayOpenConstants.ORDER_TYPE_UNIONPAY); //为什么需要
        try {
            Map<String,Object> response = client.call(QUERY, PRIVATE_KEY, builder.build());
            System.out.println(validateSign(response));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void testRefundQuery(){
        RequestBuilder builder = getDefaultRequestBuilder();
        builder.set(BusinessFields.ORDER_NO, "xxx");
        try {
            Map<String,Object> response = client.call(REFUND_QUERY, PRIVATE_KEY, builder.build());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void testCancel(){
        RequestBuilder builder = getDefaultRequestBuilder();
        builder.set(BusinessFields.ORIG_ORDER_NO, "xxx");
        builder.set(BusinessFields.ORDER_NO, getOrderNo());
        builder.set(BusinessFields.ORDER_TYPE, UnionPayOpenConstants.ORDER_TYPE_UNIONPAY);
        try {
            Map<String,Object> response = client.call(CANCEL, PRIVATE_KEY, builder.build());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void testClose(){
        RequestBuilder builder = getDefaultRequestBuilder();
        builder.set(BusinessFields.ORDER_NO, "XXX");
        builder.set(BusinessFields.ORDER_TYPE, UnionPayOpenConstants.ORDER_TYPE_UNIONPAY);
        try {
            Map<String,Object> response = client.call(CLOSE, PRIVATE_KEY, builder.build());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static boolean validateSign(Map<String,Object> response) throws MpayException {
        String sign = (String) response.get(ResponseFields.SIGNATURE);
        response.remove(ResponseFields.SIGNATURE);
        String signContent = RsaSignature.getSignCheckContent(response);
        try {
            return RsaSignature.validateSign(Digest.sha256(signContent.getBytes(UnionPayOpenConstants.CHARSET_UTF8)), sign, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, PUBLIC_KEY);
        } catch (Exception e) {
            throw new MpayException("validate sign error ", e);
        }
    }


    public static String getUserIP(){
        return "*********";
    }

    public static String getOrderTime(){
        return new SimpleDateFormat(UnionPayOpenConstants.ORDER_TIME_DATE_TIME_FORMAT).format(new Date());
    }

    public static String getNotifyUrl(){
        return "http://***************:8000/1nq7fhl1";
    }

    public static String getOrderNo(){
        return System.currentTimeMillis() + "";
    }


    public static RequestBuilder getDefaultRequestBuilder(){
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SER_PROV_ID, SER_PROV_ID);
        builder.set(ProtocolFields.MER_ID, MER_ID);
        return builder;
    }


}
