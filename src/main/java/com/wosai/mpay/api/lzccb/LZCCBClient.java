package com.wosai.mpay.api.lzccb;


import java.net.URI;
import com.wosai.mpay.api.lzccb.security.SMSaltSigner;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.pantheon.util.MapUtil;
import okhttp3.Headers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

import static com.wosai.mpay.util.HttpClientUtils.BODY_RESULT_FIELD;
import static com.wosai.mpay.util.HttpClientUtils.HEADERS_RESULT_FIELD;

public class LZCCBClient {
    public static final Logger logger = LoggerFactory.getLogger(LZCCBClient.class);
    private static SMSaltSigner signer;

    static {
        signer = new SMSaltSigner();
    }


    private int connectTimeout = 10000;
    private int readTimeout = 30000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map<String, Object> call(Map<String, Object> lzbRequest, String serviceUrl, String sm4Key
            , String signKey, String method) throws Exception {
        Map<String, Object> body = MapUtils.getMap(lzbRequest, LZCCBProtocolFields.BODY);
        String requestBody = "";
        try {
            requestBody = JsonUtil.objectToJsonString(body);
            logger.info("lzbank 请求报文:{}", requestBody);
        } catch (Exception e) {
            throw new MpayException("参数构建异常", e);
        }
        URI serviceUri = null;
        try {
            serviceUri = new URI(serviceUrl);
        } catch (Exception e) {
            throw new MpayException("参数构建异常sm4Url", e);
        }
        requestBody = signer.encryptData(sm4Key, requestBody);
        String merchantNo = MapUtils.getString(body, LZCCBRequestFields.MERCHANT_NO);
        Map<String, String> head = MapUtils.getMap(lzbRequest, LZCCBProtocolFields.HEAD);
        String signInfo = getSignBlock(serviceUri.getPath(), requestBody, merchantNo, head.get(LZCCBProtocolFields.TIME_STAMP));
        String sign = signer.sign(signInfo, signKey);
        head.put(LZCCBProtocolFields.SIGN, sign);
        Map<String, Object> result = HttpClientUtils.doCommonMethod(LZCCBTokenCache.class.getName(), null, null, serviceUrl, null, LZCCBConstant.CONTENT_TYPE, requestBody, head, LZCCBConstant.CHARSET, connectTimeout, readTimeout, method);
        logger.info("lzbank 返回结果:{}", result);
        String retStr = MapUtil.getString(result, BODY_RESULT_FIELD);
        Headers headerMap = (Headers) (MapUtil.getObject(result, HEADERS_RESULT_FIELD));
        String retSign = headerMap.get(LZCCBProtocolFields.SIGN);
        signInfo = getSignBlock(serviceUri.getPath(), retStr, merchantNo, head.get(LZCCBProtocolFields.TIME_STAMP));
        logger.info("lzbank 返回结果 retStr:{}, retSign:{}", head.get(LZCCBProtocolFields.TIME_STAMP), retSign);
        if (!signer.sign(signInfo, signKey).equals(retSign)) {
            throw new RuntimeException("返回报文验签失败");
        }
        retStr = signer.decryptData(sm4Key, retStr);
        Map<String, Object> retMap = new HashMap<>();
        try {
            retMap = JsonUtil.jsonStringToObject(retStr, Map.class);
            logger.info("lzbank 返回结果解密后:{}", retMap);
        } catch (Exception e) {
            logger.error("loadToken bodyString to map error", e);
            return null;
        }
        return retMap;
    }

    public String getSignBlock(String uri, String bodyString, String merId, String timeStamp) {
        StringBuffer sb = new StringBuffer();
        sb.append(uri).append("&");
        sb.append(bodyString).append("&");
        sb.append(merId).append("&");
        sb.append(timeStamp);
        return sb.toString();
    }

}
