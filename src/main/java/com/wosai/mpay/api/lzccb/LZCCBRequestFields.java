package com.wosai.mpay.api.lzccb;

public class LZCCBRequestFields {

    public static final String IS_REFRESH = "isRefresh";


    /**
     * 支付条码支付code
     */
    public static final String AUTH_CODE = "authCode";

    /**
     * 支付上送ip
     */
    public static final String REQ_IP = "reqIp";

    /**
     * 交易金额
     */
    public static final String TOTAL_AMOUNT = "totalAmount";

    public static final String METHOD = "method";

    /**
     * 终端信息
     */
    public static final String TERMINAL_INFO = "terminalInfo";

    /**
     * 备注信息
     */
    public static final String REMARK = "remark";

    /**
     * 序列号
     */
    public static final String SERIAL_NUM = "serialNum";


    public static final String OPEN_ID = "openId";

    public static final String SUB_APP_ID = "subAppId";

    /**
     * 业务参数
      */
    public static final String BUSINESS_PARAMS = "businessParams";

    /**
     * 可打折金额
     */
    public static final String DISCOUNTABLE_AMOUNT = "discountableAmount";

    /**
     * 商品扩展说明
     */
    public static final String GOODS_EXPAND_INSTRUCTION = "goodsExpandInstruction";

    /**
     * 商品说明
     */
    public static final String GOODS_INSTRUCTION = "goodsInstruction";


    /**
     * 商品名称
     */
    public static final String GOODS_NAME = "goodsName";

    /**
     * 截止日期
     */
    public static final String DEADLINE = "deadline";


    /**
     * 商户号，由平台分配
     */
    public static final String MERCHANT_NO = "merchantNo";

    /**
     * 商户门店编号, 在线支付时写一个固定值
     */
    public static final String STORE_ID = "storeId";

    /**
     * 终端号(POS机具号), 在线支付时写一个固定值
     */
    public static final String TERMINAL_ID = "terminalId";


    public static final String DEVICE_TYPE = "deviceType";

    public static final String TERMINAL_IP = "terminalIp";

    /**
     * 请求方ip
     */
    public static final String REQUEST_IP = "requestIp";

    /**
     * 原交易的用户客户端，如WeiXIN;AlipayClient;UnionPay;DigitalWallet
     */
    public static final String USER_AGENT = "userAgent";

    /**
     * 商户通知地址，用于平台主动通知商户地址
     */
    public static final String MERCHANT_NOTICE_URL = "merchantNoticeUrl";

    /**
     * 退款请求订单号，标识一次退款请求。同一笔交易多次退款需要保证唯一，此参数必传。
     */
    public static final String OUT_REQUEST_NO = "outRequestNo";

    /**
     * 订单支付时传入的商户订单号。不能和 tradeNo同时为空。
     */
    public static final String OUT_TRADE_NO = "outTradeNo";

    /**
     * 要退款的金额，该金额不能大于订单金额，单位为元，支持两位小数。
     */
    public static final String REFUND_AMOUNT = "refundAmount";

    /**
     * 退款的原因说明
     */
    public static final String REFUND_REASON = "refundReason";

    /**
     * 平台交易流水号，和商户订单号outTradeNo不能同时为空。
     */
    public static final String TRADE_NO = "tradeNo";

    /**
     * 原交易订单日期，格式为yyyyMMdd。建议商户侧该字段必传，若不传，则默认查询三天内订单。
     */
    public static final String ORDER_DATE = "orderDate";


    public static final String SM4_KEY = "sm4Key";
    public static final String SIGN_KEY = "signKey";
    public static final String REQ_METHOD = "method";


}
