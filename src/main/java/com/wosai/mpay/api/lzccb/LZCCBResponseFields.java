package com.wosai.mpay.api.lzccb;

public class LZCCBResponseFields {

    public static final String DATA = "data";
    public static final String SM4_KEY = "sm4Key";

    public static final String CODE = "code";

    public static final String MSG = "msg";


    // 支付返回值字段信息

    /**
     * 订单总金额
     */
    public static final String TOTAL_AMOUNT = "totalAmount";

    /**
     * 实收金额，在支付成功时返回
     */
    public static final String RECEIPT_AMOUNT = "receiptAmount";

    /**
     * 平台订单号，长度为32个字符
     */
    public static final String TRADE_NO = "tradeNo";

    /**
     * 商户号，由平台分配，格式为99XXXXXX，长度为32个字符
     */
    public static final String MERCHANT_NO = "merchantNo";

    /**
     * 商户订单号，32个字符以内，可包含字母、数字、下划线；需保证在商户端不重复
     */
    public static final String OUT_TRADE_NO = "outTradeNo";

    /**
     * 交易支付时间，格式为 yyyy-MM-dd HH:mm:ss
     */
    public static final String GMT_PAYMENT = "gmtPayment";

    /**
     * 用户主扫URL，非必须字段
     */
    public static final String CODE_URL = "codeUrl";

    /**
     * 状态值
     */
    public static final String STATUS = "status";

    /**
     * 状态描述
     */
    public static final String STATUS_MSG = "statusMsg";


    // 查询返回值字段信息

    /**
     * 订单状态
     */
    public static final String ORDER_STATUS = "orderStatus";

    /**
     * 退款状态
     */
    public static final String REFUND_STATUS = "refundStatus";

    /**
     * 清算状态
     */
    public static final String SETTLEMENT_STATUS = "settlementStatus";

    /**
     * 交易模式
     */
    public static final String TRX_MODE = "trxMode";

    /**
     * 业务类型
     */
    public static final String TRX_TYPE = "trxType";

    /**
     * 服务方流水
     */
    public static final String SERVICE_NO = "serviceNo";

    /**
     * 交易金额（如是退款订单是退款金额）
     */
    public static final String TRADE_AMOUNT = "tradeAmount";

    /**
     * 商户订单号
     */
    public static final String MERCHANT_ORDER_NO = "merchantOrderNo";


    /**
     * 平台订单号
     */
    public static final String TRX_NO = "trxNo";

    /**
     * 对账状态 未对账，对账成功，对账失败
     */
    public static final String RECONCILIATION_STATUS = "reconciliationStatus";

    /**
     * 渠道交易完成时间，交易完成时必填
     */
    public static final String TRANS_COMPLETE_TIME = "transCompleteTime";

    /**
     * 平台手续费，退款时为0
     */
    public static final String PLAT_INCOME = "platIncome";

    /**
     * 原始平台订单号，退款订单此项必填
     */
    public static final String OLD_TRX_NO = "oldTrxNo";

    /**
     * 支付渠道类型
     */
    public static final String USER_AGENT = "userAgent";

    /**
     * 终端号(POS机具号)
     */
    public static final String TERMINAL_ID = "terminalId";

    /**
     * 状态信息
     */
    public static final String STATU = "statu";

    /**
     * 状态描述信息
     */
    public static final String STATU_MSG = "statuMsg";


    /**
     * 退款返回时间字段
     */
    public static final String GMT_REFUND_PAY = "gmtRefundPay";


    // 微信预下单支付返回值字段信息
    public static final String TIME_STAMP = "timeStamp";

    public static final String PACKAGE = "package";

    public static final String PAY_SIGN = "paySign";

    public static final String APP_ID = "appId";

    public static final String SIGN_TYPE = "signType";

    //  支付宝微信返回数据信息
    public static final String WCPAY_DATA = "wcPayData";

    /**
     * 平台订单号，长度为32个字符
     */
    public static final String ALI_PAY_TRADE_NO = "tradeNO";




}
