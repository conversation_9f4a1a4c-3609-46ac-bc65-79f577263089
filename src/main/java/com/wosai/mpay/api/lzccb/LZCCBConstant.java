package com.wosai.mpay.api.lzccb;

public class LZCCBConstant {

    public static final String SUCCESS_CODE = "00000200";

    public static final String KEY_ALG_CONSTANT = "1";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYYMMDD = "yyyyMMdd";

    public static String YYYY_MM_DD_HHMMSS = "yyyy-MM-dd HH:mm:ss";

    // 用户代理常量
    public static final String USER_AGENT_WEIXIN = "WeiXIN";
    public static final String USER_AGENT_ALIPAY = "AlipayClient";
    public static final String USER_AGENT_UNIONPAY = "UnionPay";

    public static final String METHOD_WXMINAPP = "WXMINAPP";

    //商户主扫
    public static final String METHOD_MZS = "MZS";

    // 用户主扫
    public static final String METHOD_UZS = "UZS";

    // 支付时订单状态常量

    /**
     * 交易成功
     */
    public static final String SUCCESS = "SUCCESS";

    /**
     * 交易中
     */
    public static final String PAYING = "PAYING";

    /**
     * 待支付/待退款
     */
    public static final String UNPAID = "UNPAID";

    /**
     * 交易失败
     */
    public static final String FAIL = "FAIL";

    /**
     * 交易关闭
     */
    public static final String CLOSE = "CLOSE";

    /**
     * 微信sub_appid
     */
    public static final String SUB_APPID = "sub_appid";


    // 退款状态常量
    /**
     * 未退款
     */
    public static final String UNREFUND = "UNREFUND";

    /**
     * 退款中
     */
    public static final String REFUNDING = "REFUNDING";

    /**
     * 全部退款
     */
    public static final String ALLREFUND = "ALLREFUND";

    /**
     * 部分退款
     */
    public static final String PARTREFUND = "PARTREFUND";


    // 交易模式

    /**
     * 退款
     */
    public static final String REFUND = "REFUND";

    /**
     * 交易
     */
    public static final String TRADE = "TRADE";


    public static final String CHARSET = "UTF-8";

    public static final String ACCEPT_TYPE = "application/json";


    public static final String CONTENT_TYPE = "application/json";

    public static final String POST = "post";

    public static final String GET = "get";


}
