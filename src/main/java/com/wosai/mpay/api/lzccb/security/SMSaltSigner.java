package com.wosai.mpay.api.lzccb.security;

import org.bouncycastle.crypto.digests.SM3Digest;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 通过国米签名
 *
 * <AUTHOR>
 */
public class SMSaltSigner {

    /**
     * 密钥长度{@value}(按byte数组计算，转成base64后不一样)
     */
    private static final int KEY_LEN = 32;

    public String genKey() {
        return Base64.getEncoder().encodeToString(StringUtil.genRandomByte(KEY_LEN));
    }

    public String sign(String sign, String key) {
        String dataWithSalt = sign + key;
        System.out.println(dataWithSalt);
        byte[] srcByte = dataWithSalt.getBytes(StandardCharsets.UTF_8);
        byte[] md = new byte[32];
        SM3Digest sm3 = new SM3Digest();
        sm3.update(srcByte, 0, srcByte.length);
        sm3.doFinal(md, 0);
        String ret = Base64.getEncoder().encodeToString(md);
        return ret;
    }

    public static void main(String[] args) {
        String dataWithSalt = "/payGateway/payApi/merchant/getSm4Key&{\"isRefresh\":0}&M00014823&20230831163311FSuURpSNfUZhYz+T3woAwqkMqaHtWRotdtw4/Lj+yiI=";
        System.out.println(dataWithSalt);
        byte[] srcByte = dataWithSalt.getBytes(StandardCharsets.UTF_8);
        byte[] md = new byte[32];
        SM3Digest sm3 = new SM3Digest();
        sm3.update(srcByte, 0, srcByte.length);
        sm3.doFinal(md, 0);
        String ret = Base64.getEncoder().encodeToString(md);
        System.out.println(ret);
    }

    public boolean verify(String signBlock, String sign, String key) {
        String sign2 = sign(signBlock, key);
        if (StringUtil.stringIsNull(sign2)) {
            return false;
        } else {
            return sign2.equals(sign);
        }
    }

    /**
     * sm4加密
     *
     * @param secretKey 秘钥
     * @param plainText 待加密报文
     * @return 已加密报文
     */
    public String encryptData(String secretKey, String plainText) throws UnsupportedEncodingException {
        SM4Utils sm4 = new SM4Utils();
        sm4.secretKey = secretKey;
        sm4.hexString = true;
        return sm4.encryptData_ECB(plainText);
    }

    /**
     * sm4解密
     *
     * @param secretKey  秘钥
     * @param cipherText 待解密报文
     * @return 已解密报文
     */
    public String decryptData(String secretKey, String cipherText) {
        SM4Utils sm4 = new SM4Utils();
        sm4.secretKey = secretKey;
        sm4.hexString = true;
        return sm4.decryptData_ECB(cipherText);
    }

    /**
     * sm2解密
     *
     * @param secretKey  私钥
     * @param cipherText 待解密报文
     * @return 已解密报文
     */
    public String decryptSm2(String secretKey, String cipherText) {
        SM2Util sm2 = new SM2Util();
        byte[] aa = Utils.hexStringToBytes(cipherText);
        String deData = sm2.decrypt(aa, secretKey);
        return deData;
    }
}
