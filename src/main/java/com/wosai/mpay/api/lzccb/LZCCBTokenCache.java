package com.wosai.mpay.api.lzccb;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.wosai.mpay.api.lzccb.security.SMSaltSigner;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.wosai.mpay.util.HttpClientUtils.BODY_RESULT_FIELD;

public class LZCCBTokenCache {
    public static final Logger logger = LoggerFactory.getLogger(LZCCBTokenCache.class);
    private static SMSaltSigner signer;
    private static int connectTimeout = 10000;
    private static int readTimeout = 30000;

    static {
        signer = new SMSaltSigner();
    }

    private LoadingCache<LZCCBTokenCache.TokenKeyModel, String> TOKEN_CACHE = CacheBuilder.newBuilder()
            .maximumSize(1000) // 设置最大缓存数量为 1000
            .expireAfterWrite(6, TimeUnit.HOURS) // 设置过期时间为 6 小时
            .build(new CacheLoader<LZCCBTokenCache.TokenKeyModel, String>() {
                @Override
                public String load(LZCCBTokenCache.TokenKeyModel keyModel) {
                    return loadToken(keyModel); // 加载数据的方法
                }
            });


    private static class TokenKeyModel {


        private String sm2PriKey;

        private int isRefresh;


        private String sm4Url;


        private String signKey;

        private String merchantNo;


        private String date;


        public String getSm2PriKey() {
            return sm2PriKey;
        }

        public void setSm2PriKey(String sm2PriKey) {
            this.sm2PriKey = sm2PriKey;
        }

        public int getIsRefresh() {
            return isRefresh;
        }

        public void setIsRefresh(int isRefresh) {
            this.isRefresh = isRefresh;
        }

        public String getSm4Url() {
            return sm4Url;
        }

        public void setSm4Url(String sm4Url) {
            this.sm4Url = sm4Url;
        }

        public String getSignKey() {
            return signKey;
        }

        public void setSignKey(String signKey) {
            this.signKey = signKey;
        }

        public String getMerchantNo() {
            return merchantNo;
        }

        public void setMerchantNo(String merchantNo) {
            this.merchantNo = merchantNo;
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }


        /**
         * 商户号作为缓存的key
         *
         * @param o
         * @return
         */
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            TokenKeyModel that = (TokenKeyModel) o;
            return Objects.equals(merchantNo, that.merchantNo);
        }

        @Override
        public int hashCode() {
            return Objects.hash(merchantNo);
        }



    }

    public String getAccessToken(String sm2PriKey, int isRefresh, String getSm4Url, String signKey, String merchantNo, String date) {
        TokenKeyModel tokenKeyModel = new TokenKeyModel();
        tokenKeyModel.setSm2PriKey(sm2PriKey);
        tokenKeyModel.setIsRefresh(isRefresh);
        tokenKeyModel.setSm4Url(getSm4Url);
        tokenKeyModel.setSignKey(signKey);
        tokenKeyModel.setMerchantNo(merchantNo);
        tokenKeyModel.setDate(date);
        String sm4key = "";
        try {
            sm4key = TOKEN_CACHE.get(tokenKeyModel);
        } catch (Exception e) {
            throw new RuntimeException("getAccessToken error", e);
        }
        return sm4key;
    }

    private static String loadToken(TokenKeyModel tokenKeyModel) {
        try {
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put(LZCCBProtocolFields.MER_ID, tokenKeyModel.getMerchantNo());
            headerMap.put(LZCCBProtocolFields.KEY_SN, LZCCBConstant.KEY_ALG_CONSTANT);
            headerMap.put(LZCCBProtocolFields.ALG_SN, LZCCBConstant.KEY_ALG_CONSTANT);
            headerMap.put(LZCCBProtocolFields.TIME_STAMP, tokenKeyModel.getDate());
            headerMap.put(LZCCBProtocolFields.ACCEPT, LZCCBConstant.ACCEPT_TYPE);
            headerMap.put(LZCCBProtocolFields.CONTENT_TYPE_PROPERTY, LZCCBConstant.CONTENT_TYPE);
            Map<String, Object> reqMap = new HashMap<>();
            reqMap.put(LZCCBRequestFields.IS_REFRESH, tokenKeyModel.getIsRefresh());
            String reqJson = "";
            try {
                reqJson = JsonUtil.objectToJsonString(reqMap);
            } catch (Exception e) {
                logger.error("loadToken reqMap to json error", e);
                return null;
            }
            URI sm4Url = null;
            try {
                sm4Url = new URI(tokenKeyModel.getSm4Url());
            } catch (Exception e) {
                logger.error("getAccessToken sm4Url error", e);
                return null;
            }
            String signInfo = getSignBlock(sm4Url.getPath(), reqJson, tokenKeyModel.getMerchantNo(), headerMap.get(LZCCBProtocolFields.TIME_STAMP));
            logger.info("loadToken signInfo: " + signInfo);
            String sign = signer.sign(signInfo, tokenKeyModel.getSignKey());
            headerMap.put(LZCCBProtocolFields.SIGN, sign);
            Map<String, Object> result = HttpClientUtils.doCommonMethod(LZCCBTokenCache.class.getName(), null, null, tokenKeyModel.getSm4Url(), null, LZCCBConstant.CONTENT_TYPE, reqJson, headerMap, LZCCBConstant.CHARSET, connectTimeout, readTimeout, "post");
            String bodyString = MapUtil.getString(result, BODY_RESULT_FIELD);
            Map<String, Object> bodyMap = new HashMap<>();
            try {
                bodyMap = JsonUtil.jsonStringToObject(bodyString, Map.class);
            } catch (Exception e) {
                logger.error("loadToken bodyString to map error", e);
                return null;
            }
            String code = MapUtil.getString(bodyMap, LZCCBResponseFields.CODE);
            if (code.equals(LZCCBConstant.SUCCESS_CODE)) {
                Map<String, String> dataMap = MapUtil.getMap(bodyMap, LZCCBResponseFields.DATA, new HashMap());
                String sm4Key = MapUtil.getString(dataMap, LZCCBResponseFields.SM4_KEY);
                sm4Key = signer.decryptSm2(tokenKeyModel.getSm2PriKey(), sm4Key);
                return sm4Key;
            }
            return null;
        } catch (MpayApiNetworkError e) {
            throw new RuntimeException("loadToken network error", e);
        }
    }

    public static String getSignBlock(String uri, String bodyString, String merId, String timeStamp) {
        StringBuffer sb = new StringBuffer();
        sb.append(uri).append("&");
        sb.append(bodyString).append("&");
        sb.append(merId).append("&");
        sb.append(timeStamp);
        return sb.toString();
    }

    public static void main(String[] args) throws Exception {
        TokenKeyModel tokenKeyModel = new TokenKeyModel();
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        tokenKeyModel.setSm2PriKey("00985451BB1B015AF1AC5C14614A2DEB9CB39B6B392070E3DAFEADA417CC481FA9");
        tokenKeyModel.setIsRefresh(1);
        tokenKeyModel.setSignKey("FSuURpSNfUZhYz+T3woAwqkMqaHtWRotdtw4/Lj+yiI=");
        tokenKeyModel.setMerchantNo("M00019234");
        tokenKeyModel.setSm4Url("https://lzshhglpttet.lzccb.cn/payGateway/payApi/merchant/getSm4Key");
        tokenKeyModel.setDate(df.format(new Date()));

        String sm4Key = loadToken(tokenKeyModel);

//        D85C1EB11CB35FC87AFC3484E5F5036F
//          String sm4Key ="D85C1EB11CB35FC87AFC3484E5F5036F";
        b2c(tokenKeyModel, sm4Key);

        query(tokenKeyModel, sm4Key);

//        refund(tokenKeyModel,sm4Key);


    }

    private static void b2c(TokenKeyModel tokenKeyModel, String sm4Key) throws Exception {
        LZCCBClient lzccbClient = new LZCCBClient();
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        LZCCBRequestBuilder lzccbRequestBuilder = new LZCCBRequestBuilder();
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.MER_ID, tokenKeyModel.getMerchantNo());
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.KEY_SN, LZCCBConstant.KEY_ALG_CONSTANT);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.ALG_SN, LZCCBConstant.KEY_ALG_CONSTANT);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.ACCEPT, LZCCBConstant.ACCEPT_TYPE);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.CONTENT_TYPE_PROPERTY, LZCCBConstant.CONTENT_TYPE);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.TIME_STAMP, df.format(new Date()));
        lzccbRequestBuilder.setBody(LZCCBRequestFields.MERCHANT_NO, tokenKeyModel.getMerchantNo());
        lzccbRequestBuilder.setBody(LZCCBRequestFields.METHOD, LZCCBConstant.METHOD_MZS);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.MERCHANT_NOTICE_URL, LZCCBConstant.METHOD_MZS);
        lzccbRequestBuilder.setBody(LZCCBRequestFields.OUT_TRADE_NO, "78950342413259309");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.AUTH_CODE, "130239055890083060");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.TERMINAL_ID, "00021203");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.MERCHANT_NOTICE_URL, "http://**************:19006/preService/notify/payNotify");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.STORE_ID, "S00019644");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.REQ_IP, "**************");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.TOTAL_AMOUNT, "0.02");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.USER_AGENT, "WeiXIN");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.METHOD, "MZS");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.REMARK, "小美@#$1313123%^&S");
        HashMap<String, String> terminalInfo = new HashMap<>();
        terminalInfo.put(LZCCBRequestFields.SERIAL_NUM, "00010010");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.TERMINAL_INFO, JsonUtil.objectToJsonString(terminalInfo));
        String request = JsonUtil.objectToJsonString(lzccbRequestBuilder.build());
        Map<String, Object> result = lzccbClient.call(lzccbRequestBuilder.build(), "https://lzshhglpttet.lzccb.cn/payGateway/zlxt/trade/pay", sm4Key, "FSuURpSNfUZhYz+T3woAwqkMqaHtWRotdtw4/Lj+yiI=", "post");
        String result2 = JsonUtil.objectToJsonString(result);
        logger.info("result:{}", result);
    }


    private static void refund(TokenKeyModel tokenKeyModel, String sm4Key) throws Exception {
        LZCCBClient lzccbClient = new LZCCBClient();
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        LZCCBRequestBuilder lzccbRequestBuilder = new LZCCBRequestBuilder();
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.MER_ID, tokenKeyModel.getMerchantNo());
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.KEY_SN, LZCCBConstant.KEY_ALG_CONSTANT);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.ALG_SN, LZCCBConstant.KEY_ALG_CONSTANT);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.ACCEPT, LZCCBConstant.ACCEPT_TYPE);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.CONTENT_TYPE_PROPERTY, LZCCBConstant.CONTENT_TYPE);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.TIME_STAMP, df.format(new Date()));
        lzccbRequestBuilder.setBody(LZCCBRequestFields.MERCHANT_NO, tokenKeyModel.getMerchantNo());
        lzccbRequestBuilder.setBody(LZCCBRequestFields.TERMINAL_ID, "00021203");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.STORE_ID, "S00041093");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.REFUND_AMOUNT, "0.01");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.REQ_IP, "**************");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.USER_AGENT, "WeiXIN");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.OUT_REQUEST_NO, "653922");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.MERCHANT_NOTICE_URL, "http://**************:19006/preService/notify/payNotify");
//        lzccbRequestBuilder.setBody(LZCCBRequestFields.TRADE_NO, "O2025011715050746602872401315958");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.OUT_TRADE_NO, "78950342413259309");
        lzccbRequestBuilder.setBody(LZCCBRequestFields.OUT_REQUEST_NO, "7895034241325957");


        Map<String, Object> result = lzccbClient.call(lzccbRequestBuilder.build(), "https://lzshhglpttet.lzccb.cn/payGateway/zlxt/trade/refund", sm4Key, "FSuURpSNfUZhYz+T3woAwqkMqaHtWRotdtw4/Lj+yiI=", "post");
        String result2 = JsonUtil.objectToJsonString(result);
        logger.info("result:{}", result);
    }


    private static void query(TokenKeyModel tokenKeyModel, String sm4Key) throws Exception {
        LZCCBClient lzccbClient = new LZCCBClient();
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");

        LZCCBRequestBuilder lzccbRequestBuilder = new LZCCBRequestBuilder();
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.MER_ID, tokenKeyModel.getMerchantNo());
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.KEY_SN, LZCCBConstant.KEY_ALG_CONSTANT);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.ALG_SN, LZCCBConstant.KEY_ALG_CONSTANT);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.ACCEPT, LZCCBConstant.ACCEPT_TYPE);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.CONTENT_TYPE_PROPERTY, LZCCBConstant.CONTENT_TYPE);
        lzccbRequestBuilder.setHead(LZCCBProtocolFields.TIME_STAMP, df.format(new Date()));
        lzccbRequestBuilder.setBody(LZCCBRequestFields.MERCHANT_NO, tokenKeyModel.getMerchantNo());
        lzccbRequestBuilder.setBody(LZCCBRequestFields.OUT_TRADE_NO, "7895034241325957");

//        lzccbRequestBuilder.setBody(LZCCBRequestFields.TRADE_NO,"T202502111638221660013668364259309");
        Map<String, Object> result = lzccbClient.call(lzccbRequestBuilder.build(), "https://lzshhglpttet.lzccb.cn/payGateway/zlxt/trade/query", sm4Key, "FSuURpSNfUZhYz+T3woAwqkMqaHtWRotdtw4/Lj+yiI=", "post");
        String result2 = JsonUtil.objectToJsonString(result);
        logger.info("result:{}", result);
    }


}
