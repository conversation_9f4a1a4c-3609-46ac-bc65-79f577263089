package com.wosai.mpay.api.dcep.constant;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/10/7.
 */
public class BusinessField {

    /**
     * SM4 密钥密文，使用 SM2 公钥加密
     */
    public static final String ENCRYPT_KEY = "encryptKey";

    /**
     * 收款运营机构编号(金融机构代码)
     */
    public static final String INST_NO = "instNo";

    /**
     * 订单加密信息
     */
    public static final String ENCRYPT_INFO = "encryptInfo";

    /**
     * 商户号(收款运营机构提供)
     */
    public static final String MERCHANT_NO = "mrchntNo";

    /**
     * 商户机构编号 如果是运营机构的直连商户，传运营机构编 号;如果是商户通过受理机构接入，传受理机 构编号
     * 用于区分商户是直连还是间联
     */
    public static final String MERCHANT_INST_NO = "mrchntInstNo";

    /**
     * 接收方公钥证书序列号，用以区分不同的加密 证书
     */
    public static final String ENCRYPT_SN = "ncrptnSN";

}
