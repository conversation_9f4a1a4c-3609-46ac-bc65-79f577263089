package com.wosai.mpay.api.psbcbank;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @Description PSBCBankConstants
 * @Date 2021/4/9 10:15 AM
 */
public class PSBCBankConstants {


    public static final String SCENE_BAR_CODE = "bar_code";


    public static final String NEED_NOTIFY = "00";//需要回调通知
    public static final String DENY_NOTIFY = "01";//不需要回调通知

    public static final String NOT_SIGN = "0";//免签
    public static final String SIGN = "1";//验签

    public static final String APP_VERSION = "********";//默认应用程序版本号

    /**
     * 响应码 respCd
     */
    //支付结果码
    public static final String RESP_CODE_SUCCESS = "0000";//交易成功
    public static final String RESP_CODE_PAYING = "9030";//用户支付中
    public static final String RESP_CODE_PROCESSING = "9022";//交易正在处理中请稍后请求
    //业务码
    public static final String RESP_CODE_ERROR = "9999";//交易异常
    public static final String RESP_CODE_MERCHANT_PLATFORM_IS_EMPTY = "9701";//商户所属平台机构不能为空
    public static final String RESP_CODE_PLATFORM_PUBLIC_KEY_IS_EMPTY = "9702";//机构公钥不能为空
    public static final String RESP_CODE_PLATFORM_CERT_NOT_MATCH = "9703"; //CFCA证书序列号与PlatformID不匹配
    public static final String RESP_CODE_MERCHANT_LICENSE_CONTRAST_IS_EMPTY = "9704"; //商户证书对照信息为空
    public static final String RESP_CODE_OUTER_SYSTEM_PLATFORM_NOT_MATCH = "9705"; //外部系统代码与PlatformID不匹配
    public static final String RESP_CODE_MERCHANT_LICENSE_IS_EMPTY = "9706"; //商户证书信息为空
    public static final String RESP_CODE_PRECREATE_IS_NOT_WEIXIN = "9707"; //微信主扫-交易类型不是 微信主扫
    public static final String RESP_CODE_EMPTY_NOTIFY_URL = "9708"; //交易通知地址不能为空
    public static final String RESP_CODE_PLATFORM_ID_WRONG = "9709"; //平台机构号不正确
    public static final String RESP_CODE_SUB_MERCHANT_ID_IS_EMPTY = "9710"; //子商户号不能为空
    public static final String RESP_CODE_PRECREATE_IS_NOT_ALIPAY = "9711"; //交易类型不是主扫支付宝
    public static final String RESP_CODE_REFUND_QUERY_IS_NOT_WEIXIN = "9712"; //交易类型不是微信退款查询
    public static final String RESP_CODE_REFUND_QUERY_IS_NOT_ALIPAY = "9713"; //交易类型不是支付宝退款查询
    public static final String RESP_CODE_ORDER_STATUS_IS_CLEAR = "9714"; //订单状态已明确
    public static final String RESP_CODE_OUTER_INSTITUTE_IS_EMPTY = "9715"; //外部机构信息为空
    public static final String RESP_CODE_MERCHANT_FEE_IS_INVALID = "9716"; //商户成本费率不合法
    public static final String RESP_CODE_ORDER_SUCCESS = "9717"; //该订单交易已成功，不可关闭订单，请退款
    public static final String RESP_CODE_ORDER_FAIL = "9718"; //该订单交易已失败
    public static final String RESP_CODE_ORDER_PROCESSING = "9022"; //交易正在处理中请稍后请求
    public static final String RESP_CODE_ORIGINAL_ORDER_NOT_EXIST = "9015"; //原订单不存在
    public static final String RESP_CODE_ORIGINAL_TXN_NOT_EXIST = "9024"; //原支付流水不存在
    public static final String RESP_CODE_CLEARANCE_WRONG = "9043"; //商户结算账户类型不正确
    public static final String RESP_CODE_MERCHANT_IS_CLOSED = "9003"; //商户非启用状态
    public static final String RESP_CODE_MERCHANT_ORDER_NOT_MATCH = "9036"; //商户和原交易不一致
    public static final String RESP_CODE_MERCHANT_TRADE_NO_PERMISSION = "9040"; //该商户交易权限为空
    public static final String RESP_CODE_ORDER_PAYED = "9095"; //订单已支付
    public static final String RESP_CODE_TXN_CLOSED = "9997"; //交易已关闭
    public static final String RESP_CODE_NO_MERCHANT = "9002";//商户不存在
    public static final String RESP_CODE_INVALID_PARAMS = "9001"; //参数不合法
    public static final String RESP_CODE_NO_DATA = "9041";//查无数据
    public static final String RESP_CODE_FILE_PARSE_FAIL = "9402"; //文件解析失败
    public static final String RESP_CODE_TIME_ERROR = "9011"; //终端请求时间和统一系统时间偏差超过30分钟
    public static final String RESP_CODE_TRACE_ID_REPEAT = "9021"; //交易流水号重复
    public static final String RESP_CODE_RISK_BLOCK = "9222"; //风控检测阻断
    public static final String RESP_CODE_MERCHANT_FEE_EMPTY = "9033"; //该商户无此产品费率
    public static final String RESP_CODE_CLOSE_PAY_ERROR = "9014"; //原订单未成功
    public static final String RESP_CODE_ALL_REFUND = "9035"; //该订单已全部退货
    public static final String RESP_CODE_ONLY_CNY = "9042"; //目前只支持人民币交易
    public static final String RESP_CODE_OVER_LIMIT_REFUND = "9016"; //退货时间超过30日
    public static final String RESP_CODE_REFUND_AMT_LARGER = "9018"; //退货金额大于剩余可退货金额
    public static final String RESP_CODE_NO_REFUND_AUTH = "9012"; //此商户无退款权限
    public static final String RESP_CODE_UNKNOWN_CHANNEL = "9029"; //未知渠道
    public static final String RESP_CODE_INNER_MERCHANT_UNAVAILABLE = "9005"; //商户内部户不可用
    public static final String RESP_CODE_MERCHANT_NOT_SIGN_CONTRACT = "9059"; //协议号为空，商户可能未签约
    public static final String RESP_CODE_THIRD_PART_SYSTEM_ERROR = "9607"; //调用三方系统异常
    public static final String RESP_CODE_THIRD_PART_SYSTEM_ERROR_2 = "9604"; //调用三方系统异常
    public static final String RESP_CODE_TIMEOUT = "9998"; //交易超时
    public static final String RESP_CODE_ORDER_STATUS_NOT_FIT= "9032"; //订单状态不符合条件

    /**
     * 交易码 txnCode
     */
    public static final String TXN_CODE_WEIXIN_CREATE_ORDER = "8100";//统一下单（主扫微信）
    public static final String TXN_CODE_WEIXIN_QUERY_ORDER = "8101";//查询订单
    public static final String TXN_CODE_WEIXIN_REFUND_ORDER = "8102";//申请退款
    public static final String TXN_CODE_WEIXIN_REFUND_QUERY_ORDER = "8103";//查询单笔退款
    public static final String TXN_CODE_WEIXIN_CLOSE_ORDER = "8104";//关闭订单
    public static final String TXN_CODE_WEIXIN_NOTIFY = "8105"; //支付结果通知
    public static final String TXN_CODE_WEIXIN_ORDER_PAY = "8106"; //付款码支付（被扫微信)

    public static final String TXN_CODE_ALIPAY_CREATE_ORDER = "8111";//主扫支付宝
    public static final String TXN_CODE_ALIPAY_QUERY_ORDER = "8114";//交易查询
    public static final String TXN_CODE_ALIPAY_REFUND_ORDER = "8112";//退款
    public static final String TXN_CODE_ALIPAY_REFUND_QUERY_ORDER = "8113";//退款查询
    public static final String TXN_CODE_ALIPAY_CLOSE_ORDER = "8116";//交易关闭
    public static final String TXN_CODE_ALIPAY_ORDER_PAY = "8118"; //被扫支付宝

    public static final String TXN_CODE_UNION_PAY_ORDER_PAY = "8122"; //被扫云闪付
    public static final String TXN_CODE_UNION_PAY_ORDER_QUERY = "8123"; //被扫云闪付查询
    public static final String TXN_CODE_UNION_PAY_ORDER_REFUND = "8124"; //被扫云闪付退款
    public static final String TXN_CODE_UNION_PAY_ORDER_REFUND_QUERY = "8134"; //被扫云闪付退款查询
    public static final String TXN_CODE_UNION_PAY_ORDER_CLOSE = "8135"; //被扫云闪付关闭订单
    public static final String TXN_CODE_UNION_QUERY_USER_ID = "8125"; //主扫云闪付获取用户标识
    public static final String TXN_CODE_UNION_PAY_ORDER_PRECREATE = "8126"; //主扫云闪付
    public static final String TXN_CODE_UNION_PAY_ORDER_PRECREATE_QUERY = "8129"; //主扫云闪付查询
    public static final String TXN_CODE_UNION_PAY_ORDER_PRECREATE_REFUND = "8130"; //主扫云闪付退款
    public static final String TXN_CODE_UNION_PAY_ORDER_PRECREATE_REFUND_QUERY = "8133"; //主扫云闪付退款
    public static final String TXN_CODE_UNION_PAY_ORDER_PRECREATE_CLOSE = "8136"; //主扫云闪付关闭订单

    /**
     * alipay trade_status
     */
    public static final String ALIPAY_WAIT_BUYER_PAY = "WAIT_BUYER_PAY"; //交易创建，等待买家付款
    public static final String ALIPAY_TRADE_CLOSED = "TRADE_CLOSED"; //未付款交易超时关闭，或支付完成后全额退款
    public static final String ALIPAY_TRADE_SUCCESS = "TRADE_SUCCESS"; //交易支付成功
    public static final String ALIPAY_TRADE_FINISHED = "TRADE_FINISHED"; //交易结束，不可退款

    /**
     * 渠道终端
     */
    public static final String SOURCE_SMART = "01";//智能
    public static final String SOURCE_BOX = "02";//扫码盒子
    public static final String SOURCE_FACE_PAY = "03";//人脸支付终端
    public static final String SOURCE_MERCHANT_APP = "04";//商户APP
    public static final String SOURCE_GENERAL_APP = "05";//通用行业APP
    public static final String SOURCE_MOBILE_BANK = "06"; //手机银行
    public static final String SOURCE_WEIXIN_BANK = "07"; //微信银行
    public static final String SOURCE_INDUSTRY_ACCESS = "08"; //行业接入渠道
    public static final String SOURCE_PLATFORM_ACCESS = "09"; //平台接入渠道
    public static final String SOURCE_INSTITUTE_ACCESS = "10"; //机构接入渠道
    public static final String SOURCE_UNKNOWN = "99"; //其他系统

    /**
     * txnFlag 交易标识
     */
    public static final String TXN_FLAG_INITIAL = "00";//原生态
    public static final String TXN_FLAG_CLOSED = "01";//已关闭
    public static final String TXN_FLAG_CANCELED = "02";//已撤销
    public static final String TXN_FLAG_PARTIAL_REFUNDED = "03";//部分退货
    public static final String TXN_FLAG_ALL_REFUNDED = "04";//全部退货
    public static final String TXN_FLAG_ABORT = "05";//已冲正


    /**
     * txnSta 交易状态
     */
    public static final String TXN_STA_INITIAL = "01";//初始态
    public static final String TXN_STA_WAIT_FOR_PAYMENT = "02";//待支付
    public static final String TXN_STA_SUCCESS = "03";//成功
    public static final String TXN_STA_FAIL = "04";//失败
    public static final String TXN_STA_PROGRESSING = "99";//支付中或退款中

    /**
     * deviceType
     */
    public static final String DEVICE_TYPE_AUTO_TERMINAL = "01";//自动柜员机（含 ATM 和 CDM）和多媒 体自助终端
    public static final String DEVICE_TYPE_OLD_POS = "02";//传统 POS
    public static final String DEVICE_TYPE_MPOS = "03";//mPOS
    public static final String DEVICE_TYPE_SMART_POS = "04";//智能 POS
    public static final String DEVICE_TYPE_II_PHONE = "05";//II 型固定电话

    /** 默认时间格式 **/
    public static final String DATE_TIME_FORMAT = "yyyyMMddHHmmss";

    /** UTF-8字符集 **/
    public static final String CHARSET_UTF8     = "UTF-8";

    //请求失败状态列表
    public static final List<String> PROTOCOL_ERROR_LIST = Lists.newArrayList(RESP_CODE_MERCHANT_PLATFORM_IS_EMPTY, RESP_CODE_PLATFORM_PUBLIC_KEY_IS_EMPTY, RESP_CODE_PLATFORM_CERT_NOT_MATCH,
            RESP_CODE_MERCHANT_LICENSE_CONTRAST_IS_EMPTY, RESP_CODE_OUTER_SYSTEM_PLATFORM_NOT_MATCH, RESP_CODE_MERCHANT_LICENSE_IS_EMPTY, RESP_CODE_PRECREATE_IS_NOT_WEIXIN, RESP_CODE_EMPTY_NOTIFY_URL,
            RESP_CODE_PLATFORM_ID_WRONG, RESP_CODE_SUB_MERCHANT_ID_IS_EMPTY, RESP_CODE_PRECREATE_IS_NOT_ALIPAY, RESP_CODE_REFUND_QUERY_IS_NOT_WEIXIN, RESP_CODE_REFUND_QUERY_IS_NOT_ALIPAY,
            RESP_CODE_OUTER_INSTITUTE_IS_EMPTY, RESP_CODE_MERCHANT_FEE_IS_INVALID, RESP_CODE_CLEARANCE_WRONG, RESP_CODE_MERCHANT_IS_CLOSED, RESP_CODE_MERCHANT_TRADE_NO_PERMISSION,
            RESP_CODE_NO_MERCHANT, RESP_CODE_INVALID_PARAMS, RESP_CODE_FILE_PARSE_FAIL, RESP_CODE_TIME_ERROR, RESP_CODE_TRACE_ID_REPEAT, RESP_CODE_RISK_BLOCK, RESP_CODE_MERCHANT_FEE_EMPTY,
            RESP_CODE_NO_REFUND_AUTH, RESP_CODE_UNKNOWN_CHANNEL, RESP_CODE_INNER_MERCHANT_UNAVAILABLE, RESP_CODE_MERCHANT_NOT_SIGN_CONTRACT, RESP_CODE_ONLY_CNY);

    //查询失败状态列表
    public static final List<String> QUERY_ERROR_LIST = Lists.newArrayList(RESP_CODE_ERROR, RESP_CODE_TIMEOUT, RESP_CODE_THIRD_PART_SYSTEM_ERROR, RESP_CODE_THIRD_PART_SYSTEM_ERROR_2, RESP_CODE_ORDER_FAIL, RESP_CODE_NO_DATA, RESP_CODE_ORIGINAL_ORDER_NOT_EXIST, RESP_CODE_ORIGINAL_TXN_NOT_EXIST, RESP_CODE_MERCHANT_ORDER_NOT_MATCH, RESP_CODE_ORDER_STATUS_NOT_FIT);

    //退款失败状态列表
    public static final List<String> REFUND_ERROR_LIST = Lists.newArrayList(RESP_CODE_ERROR, RESP_CODE_TIMEOUT, RESP_CODE_THIRD_PART_SYSTEM_ERROR, RESP_CODE_THIRD_PART_SYSTEM_ERROR_2, RESP_CODE_TXN_CLOSED, RESP_CODE_OVER_LIMIT_REFUND, RESP_CODE_REFUND_AMT_LARGER, RESP_CODE_ALL_REFUND, RESP_CODE_CLOSE_PAY_ERROR, RESP_CODE_ORDER_STATUS_NOT_FIT);


}
