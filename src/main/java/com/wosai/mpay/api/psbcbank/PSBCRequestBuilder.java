package com.wosai.mpay.api.psbcbank;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.BuilderException;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Description RequestBuilder
 * @Date 2021/4/8 5:40 PM
 */
public class PSBCRequestBuilder {

    private static ObjectMapper om = new ObjectMapper();
    Map<String, Object> request;
    Map<String, Object> bizContent;

    public PSBCRequestBuilder() {
        request = new TreeMap<>();
        bizContent = new TreeMap<>();

        DateFormat df = new SimpleDateFormat(PSBCBankConstants.DATE_TIME_FORMAT);
        request.put(PSBCBusinessFields.REQ_DATE, df.format(new Date()));
        request.put(PSBCBusinessFields.VERSION, "1.0.0");  //统一收单系统交互使用的接口版本号

    }

    public Map<String, Object> build() throws BuilderException {
        try {
            request.put(PSBCBusinessFields.REQ_CONTENT, om.writeValueAsString(bizContent));
        } catch (JsonProcessingException e) {
            throw new BuilderException("unable to serialize bizContent in JSON format.", e);
        }
        return request;
    }

    public void set(String field, Object value) {
        request.put(field,  value);
    }

    public void bizSet(String field, Object value) {
        bizContent.put(field,  value);
    }

    public Map<String,Object> getRequest(){
        return request;
    }

}
