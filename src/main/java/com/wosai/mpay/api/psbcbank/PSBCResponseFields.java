package com.wosai.mpay.api.psbcbank;
/**
 * <AUTHOR>
 * @Description ResponseFields
 * @Date 2021/4/8 5:40 PM
 */
public class PSBCResponseFields {

    public static final String RESP_CODE = "respCd"; //邮储返回的响应码，此字段是请求方与收单后台之间的通信标识，非交易标识，交易是否成功需要根据异步通知中reqContent域中的trade_status进行判断
    public static final String RESP_DESC = "respDesc"; //响应描述
    public static final String MCHT_NO = "mchtNo"; //商户号	统一收单系统分配的商户号
    public static final String ORDER_NO = "orderNo"; //订单号  统一收单系统订单号
    public static final String REFUND_REMARKS = "refundRemarks"; //退款备注
    public static final String TXN_AMT = "txnAmt"; //退款金额
    public static final String TXN_STA = "txnSta"; //交易状态
    public static final String TXN_FLAG = "txnFlag"; //交易标识

    public static final String RESP_CONTENT = "respContent"; //统一收单线下交易查询接口响应报文

    public static final String TRADE_STATUS = "trade_status"; //交易状态：WAIT_BUYER_PAY（交易创建，等待买家付款）、TRADE_CLOSED（未付款交易超时关闭，或支付完成后全额退款）、TRADE_SUCCESS（交易支付成功）、TRADE_FINISHED（交易结束，不可退款）
}