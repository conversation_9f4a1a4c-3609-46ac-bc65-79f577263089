package com.wosai.mpay.api.psbcbank;

import com.wosai.mpay.api.unionqrcode.BusinessFields;
import com.wosai.mpay.api.unionqrcode.UnionPayQRCodeConstants;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.SafeSimpleDateFormat;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/***
 * @ClassName: PSBCBankTest
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/2/9 4:03 PM
 */
public class PSBCBankTest {

    private static final String BASE_URL = "https://**************:8055/trans/intermgr/online/api/platform/";
    private static final String PAY_URL = BASE_URL + "outAgency/unionOrderPay";
    private static final String QUERY_URL = BASE_URL + "ordQue/unionQueOrder";
    private static final String REFUND_URL = BASE_URL + "outAgency/unionRefOrder";
    private static final String REFUND_QUERY_URL = BASE_URL + "outAgency/unUnionQueRefOrder";
    private static final String UNION_PRECREATE_URL = BASE_URL + "outAgency/actUnionCreOrder";
    private static final String UNION_PRECREATE_QUERY_URL = BASE_URL + "ordQue/actUnionQueOrder";
    private static final String UNION_PRECREATE_REFUND_URL = BASE_URL + "outAgency/actUnionRefOrder";
    private static final String UNION_PRECREATE_REFUND_QUERY_URL = BASE_URL + "outAgency/unionQueRefOrder";


    private static final SafeSimpleDateFormat dateFormat = new SafeSimpleDateFormat("yyyyMMddHHmmss");
    //商户私钥
    private static final String secret_key = "MIIC1gIBATBHBgoqgRzPVQYBBAIBBgcqgRzPVQFoBDAH+Rt5Fq0iuWP4UXypKSllpAqeby7H6GE5wvD/L4JtSk9rAq3KYDaU6ynAv55pfZwwggKGBgoqgRzPVQYBBAIBBIICdjCCAnIwggIWoAMCAQICBRBEI3dDMAwGCCqBHM9VAYN1BQAwXDELMAkGA1UEBhMCQ04xMDAuBgNVBAoMJ0NoaW5hIEZpbmFuY2lhbCBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTEbMBkGA1UEAwwSQ0ZDQSBURVNUIFNNMiBPQ0ExMB4XDTIxMDQyODAyMjgzM1oXDTIxMDcyODAyMjgzM1owYzELMAkGA1UEBhMCQ04xDTALBgNVBAoMBE9DQTExDzANBgNVBAsMBlRQQy1TMzEZMBcGA1UECwwQT3JnYW5pemF0aW9uYWwtMjEZMBcGA1UEAwwQdGVzdDEwSDEwMDAwNDU4ODBZMBMGByqGSM49AgEGCCqBHM9VAYItA0IABASLaaXcRvhvp7x8ruag4xCMMv8RJvgFHXza2PNSrnhsvCW9PR4h6x9hauJt3iebx/nrKDavZQAOYi9UKRidj9ujgbswgbgwHwYDVR0jBBgwFoAUa/4Y2o9COqa4bbMuiIM6NKLBMOEwDAYDVR0TAQH/BAIwADA5BgNVHR8EMjAwMC6gLKAqhihodHRwOi8vdWNybC5jZmNhLmNvbS5jbi9TTTIvY3JsMjE3MTUuY3JsMA4GA1UdDwEB/wQEAwIGwDAdBgNVHQ4EFgQUSMPQT6hmNylWT4txN50uo6nbZXowHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMEMAwGCCqBHM9VAYN1BQADSAAwRQIgeAkHfn5S0aMHqLyzKr1RoP8RrCZcFcoD1zh8VAy1BQ8CIQDaqcvzXNrd6uWOXiUF2XASoz8JMpxw4of6M2LMNVHKnw==";

    private static void payTest() throws Exception {
        PSBCRequestBuilder requestBuilder = new PSBCRequestBuilder();
        getDefaultRequestBuilder(requestBuilder);

        long time = System.currentTimeMillis();
        //请求方交易流水号或者订单号
        requestBuilder.set(PSBCBusinessFields.REQ_TRACE_ID, "7891" + time);

        //交易码
        requestBuilder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_PAY);

        requestBuilder.bizSet(PSBCBusinessFields.VERSION, "1.0.0");
        //C2B 码
        requestBuilder.bizSet(BusinessFields.QR_NO, "6222050597221640752");
        //商户订单号
        requestBuilder.bizSet(BusinessFields.ORDER_NO, "7891" + time);
        requestBuilder.bizSet(BusinessFields.REQ_TYPE, "**********");
        //订单总金额，单位为分
        requestBuilder.bizSet(BusinessFields.TXN_AMT, 1);
        //订单时间
        requestBuilder.bizSet(BusinessFields.ORDER_TIME, dateFormat.format(new Date()));
        //交易币种, 156
        requestBuilder.bizSet(BusinessFields.CURRENCY_CODE, UnionPayQRCodeConstants.CURRENCY_CODE_CNY);
        //地区信息
//        requestBuilder.bizSet(BusinessFields.AREA_INFO, MapUtils.getString(configSnapshot, TransactionParam.AREA_INFO, UnionPayQRCodeConstants.AREA_INFO_SH));


        // 手续费费率
        requestBuilder.set(PSBCBusinessFields.ACTIV_RATE, "30");
        //termID
        requestBuilder.set(PSBCBusinessFields.TERM_ID, "********");
        String termIp = "127.0.0.1";
        Map<String, Object> termInfo = new HashMap<>();
        termInfo.put(PSBCBusinessFields.TERM_INFO_DEVICE_TYPE, PSBCBankConstants.DEVICE_TYPE_SMART_POS);
        termInfo.put(PSBCBusinessFields.TERM_INFO_SERIAL_NUM, "********");
        termInfo.put(PSBCBusinessFields.TERM_INFO_TERM_IP, termIp);
        termInfo.put(PSBCBusinessFields.TERM_INFO_APP_VERSION, PSBCBankConstants.APP_VERSION);
        Map<String, Object> poi = new HashMap<>();
        poi.put("longitude", "113.8683542");
        poi.put("latitude", "34.5553177");
        termInfo.putAll(poi);
        //259号文改造
        termInfo.put(PSBCBusinessFields.DEVICE_ID, "********");
        //termInfo
        requestBuilder.set(PSBCBusinessFields.TERM_INFO, JsonUtil.objectToJsonString(termInfo));
        //信用卡交易标识
        requestBuilder.set(PSBCBusinessFields.IS_CREDIT, 1);
        //回调通知,被扫不需要回调
        requestBuilder.set(PSBCBusinessFields.WHETHER_NOTIFY, PSBCBankConstants.DENY_NOTIFY);



        PSBCBankClient psbcBankClient = new PSBCBankClient();
        Map<String, Object> result = psbcBankClient.call(PAY_URL, requestBuilder.build(), secret_key, PSBCBusinessFields.sm2FilePass);


        System.out.println(result);
    }

    private static void queryTest() throws Exception {
        PSBCRequestBuilder requestBuilder = new PSBCRequestBuilder();

        //发起渠道终端, 默认"机构接入渠道"
        requestBuilder.set(PSBCBusinessFields.SOURCE_ID, PSBCBankConstants.SOURCE_INSTITUTE_ACCESS);
        //请求方系统代码
        requestBuilder.set(PSBCBusinessFields.REQ_SYS_ID, "SQB00000001");
        long time = System.currentTimeMillis();
        //请求方交易流水号或者订单号
        requestBuilder.set(PSBCBusinessFields.REQ_TRACE_ID, "7891" + time);
        //请求方自定义字段，响应时原样返回
        requestBuilder.set(PSBCBusinessFields.REQ_RESERVED, "reqReserved");
        //外包机构号，由统一收单系统分配
        requestBuilder.set(PSBCBusinessFields.PLATFORM_ID, "WB1101052");
        //CFCA证书序列号
        requestBuilder.set(PSBCBusinessFields.CERT_NUM, "**********");
        //渠道ID，标识交易发起渠道
        requestBuilder.set(PSBCBusinessFields.CHANNEL_ID, "shouqian");
        //邮储分配的商户号
        requestBuilder.set(PSBCBusinessFields.MCHT_NO, "1001201********");

        //交易码
//        requestBuilder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_QUERY);
        requestBuilder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_PRECREATE_QUERY);

//        requestBuilder.bizSet(BusinessFields.REQ_TYPE, "**********");
        requestBuilder.bizSet(BusinessFields.REQ_TYPE, "**********");
        requestBuilder.bizSet(PSBCBusinessFields.VERSION, "1.0.0");

        //订单号
        requestBuilder.bizSet(BusinessFields.ORDER_NO, "*****************");

        //订单时间
        requestBuilder.bizSet(BusinessFields.ORDER_TIME, "**************");

        //原请求方订单号或流水号
        requestBuilder.set(PSBCBusinessFields.ORG_REQ_TRACE_ID, "*****************");
        //原请求方交易时间,不需要精确时间
        requestBuilder.set(PSBCBusinessFields.ORG_REQ_DATE, "**************");

        PSBCBankClient psbcBankClient = new PSBCBankClient();

        Map<String, Object> result = psbcBankClient.call(UNION_PRECREATE_QUERY_URL, requestBuilder.build(), secret_key, PSBCBusinessFields.sm2FilePass);
//        Map<String, Object> result = psbcBankClient.call(UNION_PRECREATE_QUERY_URL, requestBuilder.build(), skey, hxkey);


        System.out.println(result);
    }


    private static void cancelTest() throws Exception {

    }

    private static void refundTest() throws Exception {
        PSBCRequestBuilder requestBuilder = new PSBCRequestBuilder();

        long time = System.currentTimeMillis();
        getDefaultRequestBuilder(requestBuilder);

        //请求方交易流水号或者订单号
        requestBuilder.set(PSBCBusinessFields.REQ_TRACE_ID, "7891" + time);

        //交易码
//        requestBuilder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_REFUND);
        requestBuilder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_PRECREATE_REFUND);
        requestBuilder.bizSet(PSBCBusinessFields.VERSION, "1.0.0");
        //原始订单号
        requestBuilder.bizSet(BusinessFields.ORIG_ORDER_NO, "*****************");
        //原始订单时间
        requestBuilder.bizSet(BusinessFields.ORIG_ORDER_TIME, "**************");
        //订单号
        requestBuilder.bizSet(BusinessFields.ORDER_NO,  "7891" + time);
        //订单时间
        requestBuilder.bizSet(BusinessFields.ORDER_TIME,  dateFormat.format(new Date()));
        //交易币种
        requestBuilder.bizSet(BusinessFields.CURRENCY_CODE, UnionPayQRCodeConstants.CURRENCY_CODE_CNY);
        //交易金额
        requestBuilder.bizSet(BusinessFields.TXN_AMT, "1");

        //termID
        requestBuilder.set(PSBCBusinessFields.TERM_ID, "********");
        String termIp = "127.0.0.1";
        Map<String, Object> termInfo = new HashMap<>();
        termInfo.put(PSBCBusinessFields.TERM_INFO_DEVICE_TYPE, PSBCBankConstants.DEVICE_TYPE_SMART_POS);
        termInfo.put(PSBCBusinessFields.TERM_INFO_SERIAL_NUM, "********");
        termInfo.put(PSBCBusinessFields.TERM_INFO_TERM_IP, termIp);
        termInfo.put(PSBCBusinessFields.TERM_INFO_APP_VERSION, PSBCBankConstants.APP_VERSION);
        Map<String, Object> poi = new HashMap<>();
        poi.put("longitude", "113.8683542");
        poi.put("latitude", "34.5553177");
        termInfo.putAll(poi);
        //259号文改造
        termInfo.put(PSBCBusinessFields.DEVICE_ID, "********");
        //termInfo
        requestBuilder.set(PSBCBusinessFields.TERM_INFO, JsonUtil.objectToJsonString(termInfo));

        //请求方交易流水号或者订单号
        requestBuilder.set(PSBCBusinessFields.REQ_TRACE_ID, "7891" + time);
        //原请求方订单号或流水号
        requestBuilder.set(PSBCBusinessFields.ORG_REQ_TRACE_ID, "*****************");
        //原请求方交易时间,不需要精确时间
        requestBuilder.set(PSBCBusinessFields.ORG_REQ_DATE, "**************");



        PSBCBankClient psbcBankClient = new PSBCBankClient();
//        Map<String, Object> result = psbcBankClient.call(REFUND_URL, requestBuilder.build(), secret_key, PSBCBusinessFields.sm2FilePass);
        Map<String, Object> result = psbcBankClient.call(UNION_PRECREATE_REFUND_URL, requestBuilder.build(), secret_key, PSBCBusinessFields.sm2FilePass);


        System.out.println(result);
    }

    private static void refundQueryTest() throws Exception {
        PSBCRequestBuilder requestBuilder = new PSBCRequestBuilder();

        long time = System.currentTimeMillis();
        getDefaultRequestBuilder(requestBuilder);

        //请求方交易流水号或者订单号
        requestBuilder.set(PSBCBusinessFields.REQ_TRACE_ID, "7891" + time);

        //交易码
//        requestBuilder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_REFUND_QUERY);
        requestBuilder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_PRECREATE_REFUND);


        //termID
        requestBuilder.set(PSBCBusinessFields.TERM_ID, "********");
        String termIp = "127.0.0.1";
        Map<String, Object> termInfo = new HashMap<>();
        termInfo.put(PSBCBusinessFields.TERM_INFO_DEVICE_TYPE, PSBCBankConstants.DEVICE_TYPE_SMART_POS);
        termInfo.put(PSBCBusinessFields.TERM_INFO_SERIAL_NUM, "********");
        termInfo.put(PSBCBusinessFields.TERM_INFO_TERM_IP, termIp);
        termInfo.put(PSBCBusinessFields.TERM_INFO_APP_VERSION, PSBCBankConstants.APP_VERSION);
        Map<String, Object> poi = new HashMap<>();
        poi.put("longitude", "113.8683542");
        poi.put("latitude", "34.5553177");
        termInfo.putAll(poi);
        //259号文改造
        termInfo.put(PSBCBusinessFields.DEVICE_ID, "********");
        //termInfo
        requestBuilder.set(PSBCBusinessFields.TERM_INFO, JsonUtil.objectToJsonString(termInfo));

        //请求方交易流水号或者订单号
        requestBuilder.set(PSBCBusinessFields.REQ_TRACE_ID, "7891" + time);
        //原请求方订单号或流水号
        requestBuilder.set(PSBCBusinessFields.ORG_REQ_TRACE_ID, "*****************");
        //原请求方交易时间,不需要精确时间
        requestBuilder.set(PSBCBusinessFields.ORG_REQ_DATE, "**************");


        Map<String, Object> builder = requestBuilder.build();
        builder.remove(PSBCBusinessFields.REQ_CONTENT);

        PSBCBankClient psbcBankClient = new PSBCBankClient();
//        Map<String, Object> result = psbcBankClient.call(REFUND_QUERY_URL, requestBuilder.build(), secret_key, PSBCBusinessFields.sm2FilePass);
        Map<String, Object> result = psbcBankClient.call(UNION_PRECREATE_REFUND_URL, requestBuilder.build(), secret_key, PSBCBusinessFields.sm2FilePass);


        System.out.println(result);
    }

    private static void preCreateTest() throws Exception {
        PSBCRequestBuilder requestBuilder = new PSBCRequestBuilder();

        long time = System.currentTimeMillis();
        getDefaultRequestBuilder(requestBuilder);
        //请求方交易流水号或者订单号
        requestBuilder.set(PSBCBusinessFields.REQ_TRACE_ID, "7891" + time);

        //交易码
        requestBuilder.set(PSBCBusinessFields.TXN_CODE, PSBCBankConstants.TXN_CODE_UNION_PAY_ORDER_PRECREATE);
        requestBuilder.bizSet(PSBCBusinessFields.VERSION, "1.0.0");
        //订单号
        requestBuilder.bizSet(BusinessFields.ORDER_NO, "7891" + time);
        //订单时间
        requestBuilder.bizSet(BusinessFields.ORDER_TIME, dateFormat.format(new Date()));
        requestBuilder.bizSet(BusinessFields.REQ_TYPE, "**********");
        //订单类型
        requestBuilder.bizSet(BusinessFields.ORDER_TYPE, "10");
        //支付有效时间
        requestBuilder.bizSet(BusinessFields.PAYMENT_VALID_TIME,  4 * 60 + "");
        //订单接收超时时间
        requestBuilder.bizSet(BusinessFields.ORDERTIMEOUT, 20230224180550L + 4 * 100 + "");
        //订单描述
        requestBuilder.bizSet(BusinessFields.ORDER_DESC, "test");
        //持卡人IP
        requestBuilder.bizSet(BusinessFields.CUSTOMER_IP, "127.0.0.1");
        //地区信息
//        requestBuilder.bizSet(BusinessFields.AREA_INFO, MapUtils.getString(configSnapshot, TransactionParam.AREA_INFO, UnionPayQRCodeConstants.AREA_INFO_SH));
        //交易金额
        requestBuilder.bizSet(BusinessFields.TXN_AMT, "1");
        //交易币种
        requestBuilder.bizSet(BusinessFields.CURRENCY_CODE, UnionPayQRCodeConstants.CURRENCY_CODE_CNY);
        //持卡人IP
        requestBuilder.bizSet("qrCodeType", "1");
        //用户开放标识
        requestBuilder.bizSet(BusinessFields.USER_ID, "KWt+DBy5y6oHGFW1kJzxVPyGPgbA/+CbFQCPe7siH6dNbAKvHGFz5uQB5s32RmUa");


        // 手续费费率
        requestBuilder.set(PSBCBusinessFields.ACTIV_RATE, "30");
        //termID
        requestBuilder.set(PSBCBusinessFields.TERM_ID, "********");
        String termIp = "127.0.0.1";
        Map<String, Object> termInfo = new HashMap<>();
        termInfo.put(PSBCBusinessFields.TERM_INFO_DEVICE_TYPE, PSBCBankConstants.DEVICE_TYPE_SMART_POS);
        termInfo.put(PSBCBusinessFields.TERM_INFO_SERIAL_NUM, "********");
        termInfo.put(PSBCBusinessFields.TERM_INFO_TERM_IP, termIp);
        termInfo.put(PSBCBusinessFields.TERM_INFO_APP_VERSION, PSBCBankConstants.APP_VERSION);
//        Map<String, Object> poi = new HashMap<>();
//        poi.put("longitude", "113.8683542");
//        poi.put("latitude", "34.5553177");
//        termInfo.putAll(poi);
        //259号文改造
        termInfo.put(PSBCBusinessFields.DEVICE_ID, "********");
        //termInfo
        requestBuilder.set(PSBCBusinessFields.TERM_INFO, JsonUtil.objectToJsonString(termInfo));
        //回调通知
        requestBuilder.set(PSBCBusinessFields.WHETHER_NOTIFY, PSBCBankConstants.NEED_NOTIFY);
        //回调地址
        requestBuilder.set(PSBCBusinessFields.BACK_URL, "https://shouqianba.com");
        //信用卡交易标识
        requestBuilder.set(PSBCBusinessFields.IS_CREDIT, 1);
        requestBuilder.set(PSBCBusinessFields.CODE_STA, "1");//码牌标识



        PSBCBankClient psbcBankClient = new PSBCBankClient();
        Map<String, Object> result = psbcBankClient.call(UNION_PRECREATE_URL, requestBuilder.build(), secret_key, PSBCBusinessFields.sm2FilePass);

        System.out.println(result);
    }


    public static void main(String[] args) throws Exception {
//        payTest();
//        refundTest();
//        refundQueryTest();
//        cancelTest();
        queryTest();
//        preCreateTest();

//        System.out.println(System.currentTimeMillis());
    }

    private static void getDefaultRequestBuilder(PSBCRequestBuilder requestBuilder){

        //发起渠道终端, 默认"机构接入渠道"
        requestBuilder.set(PSBCBusinessFields.SOURCE_ID, PSBCBankConstants.SOURCE_INSTITUTE_ACCESS);
        //请求方系统代码
        requestBuilder.set(PSBCBusinessFields.REQ_SYS_ID, "SQB00000001");

        //请求方自定义字段，响应时原样返回
        requestBuilder.set(PSBCBusinessFields.REQ_RESERVED, "reqReserved");
        //外包机构号，由统一收单系统分配
        requestBuilder.set(PSBCBusinessFields.PLATFORM_ID, "WB1101052");
        //CFCA证书序列号
        requestBuilder.set(PSBCBusinessFields.CERT_NUM, "**********");
        //渠道ID，标识交易发起渠道
        requestBuilder.set(PSBCBusinessFields.CHANNEL_ID, "shouqian");
        //邮储分配的商户号
        requestBuilder.set(PSBCBusinessFields.MCHT_NO, "1001201********");
    }

}
