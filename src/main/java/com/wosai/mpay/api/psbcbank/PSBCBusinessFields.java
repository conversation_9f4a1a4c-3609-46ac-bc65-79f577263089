package com.wosai.mpay.api.psbcbank;

/**
 * <AUTHOR>
 * @Description BusinessFields
 * @Date 2021/4/8 6:40 PM
 */
public class PSBCBusinessFields {

    public static final String sm2FilePass = "123456";

    public static final String VERSION = "version"; //接口版本号
    public static final String TXN_CODE = "txnCode"; //交易码，交易唯一标识
    public static final String SOURCE_ID = "sourceId"; //发起渠道终端
    public static final String REQ_SYS_ID = "reqSysId"; //请求方系统代码，由统一收单系统分配
    public static final String REQ_TRACE_ID = "reqTraceId"; //请求方交易流水号或者订单号，每笔交易必须唯一，不可重复，交易唯一值，响应时原样返回
    public static final String REQ_DATE = "reqDate"; //请求方交易时间，响应时原样返回，格式：YYYYMMDDHHmmss
    public static final String REQ_RESERVED = "reqReserved"; //请求方自定义字段，响应时原样返回
    public static final String PLATFORM_ID = "platformId"; //外包机构号，由统一收单系统分配
    public static final String CERT_NUM = "certNum";//CFCA证书序列号（厂商公钥证书的序列号），响应时原样返回
    public static final String CHANNEL_ID = "channelId"; //渠道ID，标识交易发起渠道
    public static final String IS_SIGN = "isSign"; //是否签名，0-免签，1-验签  值为0时，signature域可不需要上送
    public static final String SIGNATURE = "signature"; //签名域，MAC块数据元
    public static final String MCH_RESERVED = "mchReserved"; //用于传输商户自定义数据，在支付结果查询时原样返回

    /***/
    public static final String MCHT_NO = "mchtNo"; //商户号，统一收单系统生成的机构或者商户在邮储银行统一收单系统内的唯一标识
    public static final String TERM_ID = "termID"; //交易时，商户用户收款的终端设备编号
    public static final String TERM_INFO = "termInfo"; //终端信息，位置信息（经纬度信息）必须上送，本域采用JSON格式，全部内容用“{}”包含，内部可包含多个子域
    //terminal info
    public static final String TERM_INFO_DEVICE_TYPE = "deviceType"; //设备类型
    public static final String TERM_INFO_SERIAL_NUM = "serialNum"; //终端序列号
    public static final String TERM_INFO_TERM_IP = "termIP"; //终端IP
    public static final String TERM_INFO_BASE_STATION = "baseStation"; //基站信息
    public static final String TERM_INFO_APP_VERSION = "appVersion"; //终端应用程序的版本号。应用程序变更应 保证版本号不重复。当长度不足时，右补 空格。
    public static final String DEVICE_ID = "deviceId"; //终端设备号，商户终端报备时的终端设备号

    public static final String IS_CREDIT = "isCredit"; //信用卡交易标识，0-支持，1-不支持
    public static final String WHETHER_NOTIFY = "whetherNotify"; //回调通知，00-需要；01-不需要；（默认需要）
    public static final String BACK_URL = "backUrl"; //交易通知地址，whetherNotify取值00时，该域必传
    public static final String CODE_STA = "codeSta"; //码牌状态	0-静态码牌；1-动态码牌
    public static final String REQ_CONTENT = "reqContent"; //当单商户订单时，填写实际发生交易的商户的商户号
    public static final String ORG_REQ_TRACE_ID = "orgReqTraceID"; //支付时的请求方订单号
    public static final String ORG_REQ_DATE = "orgReqDate"; //原请求方交易时间
    public static final String ACTIV_RATE = "activRate"; // 交易手续费率

    public static final String USER_AUTH_CODE = "userAuthCode"; //授权码  付款方返回的临时授权码，一次有效
    public static final String APP_UP_IDENTIFIER = "appUpIdentifier"; //银联支付标识  收款方识别HTTP请求User Agent中包含银联支付标识，格式为“UnionPay/<版本号> <App标识>”，注意APP标识仅支持字母和数字。 示例：UnionPay/1.0 ICBCeLife
}
