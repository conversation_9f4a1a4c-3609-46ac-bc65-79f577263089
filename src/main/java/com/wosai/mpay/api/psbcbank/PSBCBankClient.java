package com.wosai.mpay.api.psbcbank;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.PSBCBankSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.security.cert.X509Certificate;
import java.util.*;

/**
 * <AUTHOR>
 * @Description PSBCBankAlipayClient
 * @Date 2021/4/9 3:06 PM
 */
public class PSBCBankClient {
    private static final Logger logger = LoggerFactory.getLogger(PSBCBankClient.class);
    private static final ObjectMapper om = new ObjectMapper();

    private static final String CONTENT_TYPE = "application/json;charset=utf-8";

    private int connectTimeout = 3000;
    private int readTimeout = 30000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
    private static SSLContext sslContext = null;
    private static HostnameVerifier hostnameVerifier = null;
    public static SSLContext getSSLContext() throws MpayException {
        if (null == sslContext) {
            try {
                sslContext = SSLContext.getInstance("SSL");
                sslContext.init(null, new TrustManager[]{new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] arg0, String arg1) {
                    }

                    @Override
                    public void checkServerTrusted(X509Certificate[] arg0, String arg1) {
                    }

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[]{};
                    }

                }}, new java.security.SecureRandom());
            } catch (Exception e) {
                throw new MpayException("获取证书失败", e);
            }
        }
        return sslContext;
    }

    public static HostnameVerifier getHostnameVerifier() {
        if (null == hostnameVerifier) {
            hostnameVerifier = (arg0, arg1) -> true;
        }
        return hostnameVerifier;
    }

    public Map<String, Object> call(String serviceUrl, Map<String, Object> request, String secretKey, String sm2Pass) throws MpayException, MpayApiNetworkError, JsonProcessingException {

        request.put(PSBCBusinessFields.IS_SIGN, PSBCBankConstants.SIGN);
        request.remove(PSBCBusinessFields.SIGNATURE);//防止重试时就会出现签名错误
        //macbuff 签名加密
        String signature = PSBCBankSignature.sign(request, secretKey, sm2Pass);
        request.put(PSBCBusinessFields.SIGNATURE, signature);
        logger.debug("request {}", request);
        String postParams = om.writeValueAsString(request);
        //使用 "application/json" contentType发起请求
        String response = HttpClientUtils.doPost(PSBCBankClient.class.getName(), getSSLContext(), getHostnameVerifier(), serviceUrl, CONTENT_TYPE, postParams, PSBCBankConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        logger.debug("response {}", response);
        Map<String, Object> result = JsonUtil.jsonStrToObject(response, Map.class);
        return result;
    }
}
