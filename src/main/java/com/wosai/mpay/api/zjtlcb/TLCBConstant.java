package com.wosai.mpay.api.zjtlcb;


import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/4/23、11:58
 **/

public class TLCBConstant {

    public static final String NAME = "provider.zjtlcb";


    public static long FOUR_MINUTES=  1000 * 60 * 4; //4分钟

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYYMMDDHHMMSSSSS = "yyyyMMddHHmmssSSS";

    public static String YYYYMMDD = "yyyyMMdd";

    public static String HHMMSS = "HH:mm:ss";

    public static String YYYYMMDDHHMMSSS = "yyyyMMddHHmmsss";


    //请求接口名
    public static String B2C_PAY = "scanPaymentCode";

    public static String C2B_ALIPAY = "alipayMerchantCodePayment"; //支付宝下单


    public static String C2B_WX = "wechatOrder"; //微信下单



    public static String APPROVE_DEV = "approveDev"; //获取token接口
    public static String REFUND_TRANSACTIONS = "refundTransactions"; //退款

    public static String TRANSACTION_DETAIL_QUERY = "transactionDetailQuery"; //交易明细查询

    public static String TRANSACTION_STATUS = "transactionStatus"; //交易状态查询

    public static String RESPONSE_SUCCESS= "000000";

   public static String  RMB = "156";


   public static String  ACCT_TYPE_WX = "02"; //账户类型 微信
   public static String  ACCT_TYPE_ALIPAY = "03"; //账户类型 支付宝


    //交易状态
    public static String SUCCESS = "S";

    public static String FAIL = "F";

    public static String PROCESSING = "P";

    public static String CONTENT_TYPE = "application/x-www-form-urlencoded";

    public static int CONNECT_TIME = 3000;

    public static int READ_TIME = 15000;

    public static String PRDCT_MSG = "PRDCT_MSG";


    public static String ADD_MSG =  "{\"payChannel\":\"01\"}";

    public static String SCAN_BOX =  "05"; //机具类型 扫码盒子


    //回调通知，业务处理成功
    public static String NOTIFY_SUCCESS = "00";

    //支付
    public static Set<String> NOTIFY_TYPE_PAY = new HashSet() {
        {
            add("1004"); //银联
            add("1006"); //微信
            add("1007"); //支付宝
        }
    };


    //退款
    public static String  NOTIFY_TYPE_REFUND = "1008";



    }
