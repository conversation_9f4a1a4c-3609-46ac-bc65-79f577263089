package com.wosai.mpay.api.zjtlcb;

/**
 * <AUTHOR>
 * @Date 2024/4/18、13:53
 * 泰隆
 **/

public class TLBRequestFields {


    public static final String MECH_NO = "mechNo"; //商户号
    public static final String MECH_NAME = "mechName"; //商户名称（商户名称地址）
    public static final String WECHAT_PUBLIC_NO = "wechatPublicNo"; //微信公众号
    public static final String BLG_MECH_NO = "blgMechNo"; //结算商户号
    public static final String CHANNEL_CODE = "channelCode"; //渠道号
    public static final String INET_NO = "inetNo"; //订单号
    public static final String SND_TM = "sndTm"; //发送时间戳
    public static final String START_TIME = "startTime"; //交易开始时间
    public static final String EXPIRE_TIME = "expireTime"; //交易结束时间
    public static final String ACCT_TYPE = "acctType"; //账户类型
    public static final String ACCT_NO = "acctNo"; //条形码
    public static final String GLOBAL_TYPE = "globalType"; //证件类型
    public static final String GLOBAL_ID = "globalId"; //证件号码
    public static final String CARD_TYPE = "cardType"; //卡类型
    public static final String CLIENT_NAME = "clientName"; //客户名称
    public static final String MOBILE = "mobile"; //手机号码
    public static final String SMS_VRFC_CD = "smsVrfcCd"; //短信验证码
    public static final String CRD_PSSWRD = "crdPsswrd"; //卡密码
    public static final String CVN2 = "cvn2"; //卡确认码CVN2
    public static final String VALID_DAYS = "validDays"; //有效期
    public static final String FLG_NO = "flgNo"; //标识号
    public static final String TO_USER_NO = "toUserNo"; //目标用户编号
    public static final String CLNT_ID = "clntId"; //客户标识
    public static final String CLNT_SBTP_ID = "clntSbtpId"; //用户子标识

    public static final String MERCH_CODE = "merchCode"; //商品编号
    public static final String PRDCT_MSG = "prdctMsg"; //产品描述
    public static final String PAY_AMOUNT = "payAmount"; //支付金额
    public static final String IP = "ip"; //IP地址
    public static final String PROVINCE_CODE = "provinceCode"; //省份代码（中文地址省代码）
    public static final String CITY_CODE = "cityCode"; //城市代码
    public static final String CCY = "ccy"; //币种
    public static final String EMPLOYEE_ID = "employeeId"; //员工号
    public static final String EMPLOYEE_NAME = "employeeName"; //员工姓名
    public static final String ADD_MSG = "addMsg"; //附加信息


    public static final String ORI_INET_NO = "oriInetNo"; //原订单号
    public static final String ORI_TRAN_DATE = "oriTranDate"; //原交易日期
    public static final String REFUND_AMT = "refundAmt"; //退款金额

    public static final String SEND_DATE = "sendDate"; //发送日期
    public static final String LOGIN_NO = "loginNo"; //登录号
    public static final String CLNT_NO = "clntNo"; //客户编号

    public static final String USER_NO = "userNo"; //用户编号

    public static final String TRAN_AMT = "tranAmt"; //交易金额

    public static final String PAY_TYPE = "payType"; //支付场景

    public static final String PREF_TP = "prefTp"; //优惠类型
    public static final String EQUIPMENT_CODE = "equipmentCode"; //设备号码(号码)
    public static final String CMMT_INFO_ARRAY = "cmmtInfoArray"; //商品信息数组
    public static final String WECHAT_CMMT_CD = "wechatCmmtCd"; //微信商品编号
    public static final String CMMT_NM = "cmmtNm"; //商品名称
    public static final String COUNT = "count"; //数量
    public static final String UNIT_PRICE = "unitPrice"; //单价

    public static final String TX_SNO = "txSno"; //time
    public static final String TX_TIME = "txTime"; //time


    public static final String APP_ID = "appID";

    public static final String SEQ_NO = "seqNO";

    public static final String RANDOM = "random";

    public static final String SM2_ENCRYPT_DATA = "sm2EncryptData";

    public static final String SM2_SIGN = "sm2Sign";


    public static final String SIGN = "sign";

    public static final String ERROR_CODE = "errorCode";



    public static final String SIGN_METHOD = "signMethod";

    public static final String RESPONSE_SUCCESS = "000000";



    public static final String ENCRYPT_METHOD = "encryptMethod";

    public static final String APP_ACCESS_TOKEN = "appAccessToken";

    public static final String REQ_DATA = "reqData";

    public static final String RSP_DATA = "rspData";

    public static final String HEAD = "head";

    public static final String BODY = "body";


    public static final String TRAN_TYPE = "tranType";

    public static final String ORDR_ST = "ordrSt";


    public static final String ZJTLCB_NOTIFY = "zjtlcb_notify";






    // 收钱吧系统内部唯一订单号
    public static final String PT_SEQ_NO = "ptSeqNo";

    // 商户流水号
    public static final String INET_SEQ_NO = "inetSeqNo";
    // 订单创建时间 yyyyMMddHHmmss
    public static final String INET_TMSTMP = "inetTmstmp";
    // 流水状态
    public static final String SEQ_ST = "seqSt";

    // 二级支付方式
    public static final String FDCR_PAY_DT = "fdcrPayDt";

    // 支付服务商订单号
    public static final String TR_PR_INET_NO = "trPrInetNo";

    // 实收金额
    public static final String ACT_PY_AMT = "actPyAmt";
    // 付款完成时间
    public static final String PAY_DATE = "payDate";
    // 服务商支付完成时间
    public static final String BUY_SRV_CMPLT_TM = "buySrvCmpltTm";
    // 交易概述
    public static final String TXN_CNTNT = "txnCntnt";
    // 收钱吧门店id
    public static final String ADR_ID = "adrId";
    // 收钱吧终端id
    public static final String TERM_NO = "termNo";
    // 操作员
    public static final String OPERATOR_NO = "operatorNo";
    // 反射参数
    public static final String PARM_VAL_DSC = "parmValDsc";
    // 商户编号
    public static final String OUT_SEC_MECH_NO = "outSecMechNo";
    // 门店编号
    public static final String MNGE_SHOP_NM = "mngeShopNm";
    // 终端编号
    public static final String TXN_TML_NO = "txnTmlNo";
    // 设备指纹
    public static final String ZW_MSG2 = "zwMsg2";
    // 硬件设备sn
    // 收款单表单内容
    public static final String CNTNT = "cntnt";
    // 业务订单号
    public static final String ASSCTN_BUSS_NO = "assctnBussNo";
    // 收钱吧会员id
    public static final String MEMBER_ID = "memberId";
    // 是否储值充值
    public static final String OFFER_FLAG = "offerFlag";
    // 收钱吧商家优惠金额
    public static final String MRCH_CMPN_AMT = "mrchCmpnAmt";
    // 收钱吧平台优惠金额
    public static final String PREF_AMT = "prefAmt";


    //机器mac地址
    public static final String MAC = "mac";


}
