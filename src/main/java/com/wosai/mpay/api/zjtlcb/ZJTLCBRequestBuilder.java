package com.wosai.mpay.api.zjtlcb;

import com.wosai.mpay.util.JsonUtil;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/4/18、14:23
 **/

public class ZJTLCBRequestBuilder {

    private Map<String,Object> request;

    private Map<String,Object> head;
    private Map<String,Object> body;



    public ZJTLCBRequestBuilder(){
        request = new LinkedHashMap<String,Object>();
        head = ZJTLUtil.buildHead();
        body = new LinkedHashMap<String,Object>();
    }

    public void set(String field, Object value) {
        body.put(field,  value);
    }


    public Map<String, Object> getRequest() {
        return this.request;
    }

    public Map<String, Object> buildRequest() {
        Map<String, Object> requestMap = new HashMap();

        request.put(TLBRequestFields.HEAD, head);
        request.put(TLBRequestFields.BODY, body);
        requestMap.put(TLBRequestFields.REQ_DATA, JsonUtil.toJsonStr(request));

        return requestMap;
    }
}
