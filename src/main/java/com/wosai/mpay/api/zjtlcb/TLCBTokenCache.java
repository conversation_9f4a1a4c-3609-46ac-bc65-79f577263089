package com.wosai.mpay.api.zjtlcb;

import com.google.common.base.Optional;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;

import com.wosai.mpay.api.tl.TlConstants;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2024/4/23、13:26
 **/

public class TLCBTokenCache {

    public static final Logger logger = LoggerFactory.getLogger(TLCBTokenCache.class);

    private LoadingCache<TLCBTokenCache.TokenKeyModel, Optional<String>> TOKEN_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)

            .build(new CacheLoader<TLCBTokenCache.TokenKeyModel, Optional<String>>() {
                @Override
                public Optional<String> load(TLCBTokenCache.TokenKeyModel keyModel) throws Exception {

                    return loadToken(keyModel.getAppID(), keyModel.getTlPublicKey(), keyModel.getSm2PrivateKey(), keyModel.getAppSecretKey(), keyModel.getTlHttpUrl());
                }
            });


    public String getAccessToken(String appID, String tlPublicKey, String sm2PrivateKey, String appSecretKey, String tlHttpUrl) {
        TLCBTokenCache.TokenKeyModel tokenKey = new TLCBTokenCache.TokenKeyModel(appID, tlPublicKey, sm2PrivateKey, appSecretKey, tlHttpUrl, TLCBConstant.APPROVE_DEV);

        Optional<String> tokenOptional;
        try {
            tokenOptional = TOKEN_CACHE.get(tokenKey);
        } catch (ExecutionException e) {
            throw new RuntimeException("获取token失败");
        }
        if (tokenOptional.isPresent()) {
            return tokenOptional.get();
        }
        throw new RuntimeException("获取token失败");
    }




    private Optional<String> loadToken(String appID, String tlPublicKey, String sm2PrivateKey, String appSecretKey, String tlHttpUrl) {
        String token = "";
        try {
            Map<String, String> reqMsg = new HashMap<>();
            String seqNO = (new SimpleDateFormat(TLCBConstant.YYYYMMDDHHMMSSS)).format(new Date());
            reqMsg.put(TLBRequestFields.APP_ID, appID);
            reqMsg.put(TLBRequestFields.SEQ_NO, seqNO);
            reqMsg.put(TLBRequestFields.RANDOM, MD5Util.md5_(seqNO));
            logger.debug("调用服务：" + TLCBConstant.APPROVE_DEV);
            String randomKey = MD5Util.md5_(UUID.randomUUID().toString());
            reqMsg.put(TLBRequestFields.SM2_ENCRYPT_DATA, SM2Util.encryptByPublicKey(randomKey, tlPublicKey));

            reqMsg.put(TLBRequestFields.SM2_SIGN, SM2Util.signByPrivateKey(randomKey, sm2PrivateKey, appID));
            reqMsg.put(TLBRequestFields.SIGN, SM3Util.sign(MapUtils.getString(reqMsg, TLBRequestFields.RANDOM) + MapUtils.getString(reqMsg, TLBRequestFields.SEQ_NO) + appSecretKey + randomKey));
            logger.debug("request {}" + JsonUtil.toJsonStr(reqMsg));
            String rspMsg = HttpClientUtils.doPost(TLCBTokenCache.class.getName(), null, null, tlHttpUrl + TLCBConstant.APPROVE_DEV, TLCBConstant.CONTENT_TYPE, JsonUtil.toJsonStr(reqMsg), TlConstants.CHARSET_UTF8, TLCBConstant.CONNECT_TIME, TLCBConstant.READ_TIME);
            logger.debug("response {}" + rspMsg);
            Map map = JsonUtil.jsonStringToObject(rspMsg, Map.class);

            if (!MapUtils.getString(map, TLBRequestFields.ERROR_CODE).equals(TLCBConstant.RESPONSE_SUCCESS)) {
                throw new RuntimeException("返回值异常");
            } else {
                token = SM2Util.decryptByPrivateKey(MapUtils.getString(map, TLBRequestFields.SM2_ENCRYPT_DATA), sm2PrivateKey);
                if (!SM2Util.verifyByPublicKey(MapUtils.getString(map, TLBRequestFields.SM2_SIGN), tlPublicKey, appID, token)) {
                    throw new RuntimeException("身份认证-SM2验签失败");
                } else if (!SM3Util.verify(MapUtils.getString(map, TLBRequestFields.RANDOM) + MapUtils.getString(map, TLBRequestFields.SEQ_NO) + token + appSecretKey, MapUtils.getString(map, TLBRequestFields.SIGN))) {
                    throw new RuntimeException("验签失败，报文一致性校验失败");
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return Optional.of(token);
    }


    private static class TokenKeyModel {

        private String appID;
        private String tlPublicKey;
        private String sm2PrivateKey;

        private String appSecretKey;
        private String tlHttpUrl;
        private String serviceId;

        public TokenKeyModel() {
        }

        public TokenKeyModel(String appID, String tlPublicKey, String sm2PrivateKey, String appSecretKey, String tlHttpUrl, String serviceId) {
            this.appID = appID;
            this.tlPublicKey = tlPublicKey;
            this.sm2PrivateKey = sm2PrivateKey;
            this.appSecretKey = appSecretKey;
            this.tlHttpUrl = tlHttpUrl;
            this.serviceId = serviceId;
        }

        public String getAppID() {
            return appID;
        }

        public void setAppID(String appID) {
            this.appID = appID;
        }

        public String getTlPublicKey() {
            return tlPublicKey;
        }

        public void setTlPublicKey(String tlPublicKey) {
            this.tlPublicKey = tlPublicKey;
        }

        public String getSm2PrivateKey() {
            return sm2PrivateKey;
        }

        public void setSm2PrivateKey(String sm2PrivateKey) {
            this.sm2PrivateKey = sm2PrivateKey;
        }

        public String getAppSecretKey() {
            return appSecretKey;
        }

        public void setAppSecretKey(String appSecretKey) {
            this.appSecretKey = appSecretKey;
        }

        public String getTlHttpUrl() {
            return tlHttpUrl;
        }

        public void setTlHttpUrl(String tlHttpUrl) {
            this.tlHttpUrl = tlHttpUrl;
        }

        public String getServiceId() {
            return serviceId;
        }

        public void setServiceId(String serviceId) {
            this.serviceId = serviceId;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            TokenKeyModel that = (TokenKeyModel) o;
            return Objects.equals(appID, that.appID) && Objects.equals(tlPublicKey, that.tlPublicKey) && Objects.equals(sm2PrivateKey, that.sm2PrivateKey) && Objects.equals(appSecretKey, that.appSecretKey) && Objects.equals(tlHttpUrl, that.tlHttpUrl);
        }

        @Override
        public int hashCode() {
            return Objects.hash(appID, tlPublicKey, sm2PrivateKey, appSecretKey, tlHttpUrl);
        }

        @Override
        public String toString() {
            return MD5Util.md5_("TokenKeyModel{" +
                    "appID='" + appID + '\'' +
                    ", tlPublicKey='" + tlPublicKey + '\'' +
                    ", sm2PrivateKey='" + sm2PrivateKey + '\'' +
                    ", appSecretKey='" + appSecretKey + '\'' +
                    ", tlHttpUrl='" + tlHttpUrl + '\'' +
                    '}');
        }


    }
}
