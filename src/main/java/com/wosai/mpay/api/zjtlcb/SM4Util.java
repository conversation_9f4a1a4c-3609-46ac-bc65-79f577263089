package com.wosai.mpay.api.zjtlcb;

/**
 * <AUTHOR>
 * @Date 2024/4/17、16:27
 **/


import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.Security;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import com.wosai.mpay.util.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

public class SM4Util {
    public SM4Util() {
    }

    public static String encrypt(String content, String password) {
        try {
            String passwordMD5 = MD5Util.md5_(password).toUpperCase().substring(8, 24);
            SecretKeySpec key = new SecretKeySpec(passwordMD5.getBytes(), "SM4");
            byte[] initParam = "abcdefghABCDEFGH".getBytes();
            IvParameterSpec ivParameterSpec = new IvParameterSpec(initParam);
            Cipher cipher = Cipher.getInstance("SM4/CBC/PKCS5Padding", "BC");
            byte[] byteContent = content.getBytes("utf-8");
            cipher.init(1, key, ivParameterSpec);
            byte[] result = cipher.doFinal(byteContent);
            return Base64.encode(result);
        } catch (NoSuchProviderException var9) {
            var9.printStackTrace();
        } catch (NoSuchPaddingException var10) {
            var10.printStackTrace();
        } catch (UnsupportedEncodingException var11) {
            var11.printStackTrace();
        } catch (InvalidKeyException var12) {
            var12.printStackTrace();
        } catch (InvalidAlgorithmParameterException var13) {
            var13.printStackTrace();
        } catch (IllegalBlockSizeException var14) {
            var14.printStackTrace();
        } catch (BadPaddingException var15) {
            var15.printStackTrace();
        } catch (NoSuchAlgorithmException var16) {
            var16.printStackTrace();
        }

        return null;
    }

    public static String decrypt(String content, String password) {
        try {
            String passwordMD5 = MD5Util.md5_(password).toUpperCase().substring(8, 24);
            SecretKeySpec key = new SecretKeySpec(passwordMD5.getBytes(), "SM4");
            Cipher cipher = Cipher.getInstance("SM4/CBC/PKCS5Padding", "BC");
            byte[] initParam = "abcdefghABCDEFGH".getBytes();
            IvParameterSpec ivParameterSpec = new IvParameterSpec(initParam);
            cipher.init(2, key, ivParameterSpec);
            byte[] result = cipher.doFinal(Base64.decode(content));
            return new String(result, "utf-8");
        } catch (NoSuchProviderException var8) {
            var8.printStackTrace();
        } catch (NoSuchPaddingException var9) {
            var9.printStackTrace();
        } catch (InvalidKeyException var10) {
            var10.printStackTrace();
        } catch (InvalidAlgorithmParameterException var11) {
            var11.printStackTrace();
        } catch (IllegalBlockSizeException var12) {
            var12.printStackTrace();
        } catch (BadPaddingException var13) {
            var13.printStackTrace();
        } catch (UnsupportedEncodingException var14) {
            var14.printStackTrace();
        } catch (NoSuchAlgorithmException var15) {
            var15.printStackTrace();
        }

        return null;
    }

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    public static void main(String[] args) {
        System.out.println("hello");
    }
}
