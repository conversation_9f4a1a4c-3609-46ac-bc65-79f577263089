package com.wosai.mpay.api.zjtlcb;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/4/30、10:27
 **/

public class ZJTLUtil {

    public static String getTime(String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date());
    }

    public static String getTime(String format,long timeStamp) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date(timeStamp));
    }


    //获取时间在xx毫秒之后
    public static String getTimeAfterMS(String format,long ms ) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        String timestamp = sdf.format(new Date(System.currentTimeMillis() + ms));
        return timestamp;
    }


    public static Map<String, Object> buildHead() {
        Map<String, Object> headmap = new HashMap<String, Object>();
        headmap.put(TLBRequestFields.TX_SNO, ZJTLUtil.getTime(TLCBConstant.YYYYMMDDHHMMSSSSS));
        headmap.put(TLBRequestFields.TX_TIME, ZJTLUtil.getTime(TLCBConstant.YYYYMMDD) + " " + ZJTLUtil.getTime(TLCBConstant.HHMMSS));
        return headmap;
    }


}
