package com.wosai.mpay.api.zjtlcb;


import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/4/18、14:08
 **/

public class TLBResponseFields {

    public static final String QUICK_RSP_STRING = "quickRspString"; //二维码字符串
    public static final String REMARK = "remark"; //备注
    public static final String SGN_TP = "sgnTp"; //签名类型
    public static final String SIGN_CERT = "signCert"; //签名证书
    public static final String TIMESTAMP = "timestamp"; //时间戳
    public static final String INET_NO = "inetNo"; //订单号
    public static final String ALIPAY_OR_WECHAT_NO = "alipayOrWechatNo"; //支付宝/微信/云闪付单号
    public static final String INET_SEQ_NO = "inetSeqNo"; //订单流水号


    public static final String SND_TM = "sndTm"; //发送时间戳
    public static final String ORI_TRAN_SEQ_NO = "oriTranSeqNo"; //原交易流水号
    public static final String ORI_TRAN_DATE = "oriTranDate"; //原交易日期
    public static final String REFUND_AMT = "refundAmt"; //退款金额
    public static final String CCY = "ccy"; //币种


    public static final String PAY_TYPE = "payType"; //支付类型
    public static final String TRAN_STATUS = "tranStatus"; //交易状态
    public static final String ACT_PY_AMT = "actPyAmt"; //实付金额
    public static final String TR_PR_INET_NO = "trPrInetNo"; //第三方订单号
    public static final String CLRG_AMT = "clrgAmt"; //清算金额
    public static final String CPON_AMT = "cponAmt"; //代金券金额
    public static final String PREF_TBL_ARRAY = "prefTblArray"; //优惠表数组
    public static final String CPON_TP = "cponTp"; //代金券类型


    public static final String SEQ_NO = "seqNo"; //流水号
    public static final String LOGIN_NO = "loginNo"; //登录号
    public static final String CLNT_NO = "clntNo"; //客户编号
    public static final String MRCH_CMPN_AMT = "mrchCmpnAmt"; //商户营销金额
    public static final String BNK_CMPN_AMT = "bnkCmpnAmt"; //银行营销金额
    public static final String TXN_NO = "txnNo"; //交易编码

    public static final String THIRD_SEQ_NO = "thirdSeqNo"; //第三方流水号
    public static final String THIRD_PARTY_DATE = "thirdPartyDate"; //第三方日期
    public static final String TRAN_AMT = "tranAmt"; //交易金额

    public static final String BUSS_TYPE = "bussType"; //业务类型

    public static final String BUSS_STATUS = "bussStatus"; //业务状态

    public static final String USER_NO = "userNo"; //用户编号

    public static final String ERROR_CODE = "errorCode";

    public static final String ERROR_MSG = "errorMsg";

    public static final String BNK_TOT_CMPN_AMT = "bnkTotCmpnAmt";

    public static final String MRCHTOTCMPNAMT = "mrchTotCmpnAmt";

}
