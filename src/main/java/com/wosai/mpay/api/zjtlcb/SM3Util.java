package com.wosai.mpay.api.zjtlcb;

/**
 * <AUTHOR>
 * @Date 2024/4/17、16:28
 **/


import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

public class SM3Util {
    public SM3Util() {
    }

    public static String sign(String data) {
        MessageDigest msgD = null;

        try {
            msgD = MessageDigest.getInstance("SM3", new BouncyCastleProvider());
            msgD.update(data.getBytes("utf-8"));
        } catch (NoSuchAlgorithmException var3) {
            var3.printStackTrace();
        } catch (UnsupportedEncodingException var4) {
            var4.printStackTrace();
        }

        return byte2hexString(msgD.digest()).toUpperCase();
    }

    public static boolean verify(String date, String signValue) {
        MessageDigest msgD = null;

        try {
            msgD = MessageDigest.getInstance("SM3", new BouncyCastleProvider());
            msgD.update(date.getBytes("utf-8"));
        } catch (NoSuchAlgorithmException var4) {
            var4.printStackTrace();
        } catch (UnsupportedEncodingException var5) {
            var5.printStackTrace();
        }

        return byte2hexString(msgD.digest()).toUpperCase().equals(signValue);
    }

    private static String byte2hexString(byte[] bytes) {
        StringBuffer bf = new StringBuffer(bytes.length * 2);

        for(int i = 0; i < bytes.length; ++i) {
            if ((bytes[i] & 255) < 16) {
                bf.append("0");
            }

            bf.append(Long.toString((long)(bytes[i] & 255), 16));
        }

        return bf.toString();
    }
}