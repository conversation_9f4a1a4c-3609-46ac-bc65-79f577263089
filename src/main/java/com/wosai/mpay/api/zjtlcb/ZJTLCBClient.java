package com.wosai.mpay.api.zjtlcb;


import com.wosai.mpay.api.tl.TlConstants;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Date 2024/4/18、14:23
 **/

public class ZJTLCBClient {

    public static final Logger logger = LoggerFactory.getLogger(ZJTLCBClient.class);

    private int connectTimeout = 3000;
    private int readTimeout = 15000;
    private String charset = "UTF-8";

    public ZJTLCBClient() {
    }

    public ZJTLCBClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public ZJTLCBClient(int connectTimeout, int readTimeout, String charset) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
        this.charset = charset;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }


    public Map<String, Object> call(Map<String, Object> requestMap, String appID, String appAccessToken, String serviceId, String tlPublicKey, String sm2PrivateKey, String tlHttpUrl, String appSecretKey) {
        try {
            String requestStr = JsonUtil.objectToJsonString(requestMap);
            logger.debug("request {}", requestStr);
            requestMap.put(TLBRequestFields.APP_ID, appID);
            requestMap.put(TLBRequestFields.SEQ_NO, (new SimpleDateFormat(TLCBConstant.YYYYMMDDHHMMSSS)).format(new Date()));
            requestMap.put(TLBRequestFields.SIGN_METHOD, "SM3");
            requestMap.put(TLBRequestFields.ENCRYPT_METHOD, "SM4");
            requestMap.put(TLBRequestFields.APP_ACCESS_TOKEN, appAccessToken);
            String randomKey = MD5Util.md5_(UUID.randomUUID().toString());
            requestMap.put(TLBRequestFields.SM2_ENCRYPT_DATA, SM2Util.encryptByPublicKey(randomKey, tlPublicKey));
            requestMap.put(TLBRequestFields.SM2_SIGN, SM2Util.signByPrivateKey(randomKey, sm2PrivateKey, appID));
            requestMap.put(TLBRequestFields.SIGN, SM3Util.sign(MapUtils.getString(requestMap, TLBRequestFields.REQ_DATA) + MapUtils.getString(requestMap, TLBRequestFields.SEQ_NO) + appSecretKey + randomKey));
            requestMap.put(TLBRequestFields.REQ_DATA, SM4Util.encrypt(MapUtils.getString(requestMap, TLBRequestFields.REQ_DATA), MapUtils.getString(requestMap, TLBRequestFields.SEQ_NO) + MapUtils.getString(requestMap, TLBRequestFields.APP_ACCESS_TOKEN) + appSecretKey + randomKey));
            logger.debug("TL加密后请求报文：" + JsonUtil.objectToJsonString(requestMap));
            String rspMsg = HttpClientUtils.doPost(ZJTLCBClient.class.getName(), null, null, tlHttpUrl + serviceId, TLCBConstant.CONTENT_TYPE, JsonUtil.toJsonStr(requestMap), TlConstants.CHARSET_UTF8, connectTimeout, readTimeout);
            logger.debug("TL返回原始报文：" +  rspMsg);


            Map encryptMap = JsonUtil.jsonStringToObject(rspMsg, Map.class);
            encryptMap.put(TLBRequestFields.APP_ACCESS_TOKEN, appAccessToken);

            return decrypt(encryptMap, sm2PrivateKey, tlPublicKey, appID,appSecretKey, TLBRequestFields.RSP_DATA);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    public  Map<String, Object> decrypt(Map<String, Object> encryptMap, String sm2PrivateKey, String tlPublicKey, String appID, String appSecretKey, String decryptKey) throws Exception {
        if (encryptMap.containsKey(decryptKey)) {
            String sm2Key = SM2Util.decryptByPrivateKey(MapUtils.getString(encryptMap, TLBRequestFields.SM2_ENCRYPT_DATA), sm2PrivateKey);
            encryptMap.put(TLBRequestFields.SM2_ENCRYPT_DATA, sm2Key);
            if (!SM2Util.verifyByPublicKey(MapUtils.getString(encryptMap, TLBRequestFields.SM2_SIGN), tlPublicKey, appID, sm2Key)) {
                throw new RuntimeException("身份认证-SM2验签失败");
            } else {
                String rspData = SM4Util.decrypt(MapUtils.getString(encryptMap, decryptKey), MapUtils.getString(encryptMap, TLBRequestFields.SEQ_NO) + MapUtils.getString(encryptMap, TLBRequestFields.APP_ACCESS_TOKEN) + appSecretKey + sm2Key);
                logger.debug("TL返回解密后原始报文：" +  rspData);
                if (SM3Util.verify(rspData + MapUtils.getString(encryptMap, TLBRequestFields.SEQ_NO) + appSecretKey + sm2Key, MapUtils.getString(encryptMap, TLBRequestFields.SIGN))) {
                    logger.debug("response {}", rspData);

                    Map responseMap = JsonUtil.jsonStringToObject(rspData, Map.class);
                    Map response =  new HashMap();
                    Map head = MapUtils.getMap(responseMap, TLBRequestFields.HEAD,new HashMap());
                    Map body = MapUtils.getMap(responseMap, TLBRequestFields.BODY, new HashMap());
                    response.putAll(body);
                    response.putAll(head);
                    return response;
                } else {
                    throw new RuntimeException("报文一致性校验失败");
                }
            }
        } else {
            return encryptMap;
        }
    }

}
