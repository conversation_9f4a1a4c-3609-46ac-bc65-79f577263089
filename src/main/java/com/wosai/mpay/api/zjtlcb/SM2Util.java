package com.wosai.mpay.api.zjtlcb;

/**
 * <AUTHOR>
 * @Date 2024/4/17、16:28
 **/


import java.io.UnsupportedEncodingException;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Security;
import java.security.Signature;
import java.security.spec.ECGenParameterSpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import com.wosai.mpay.util.Base64;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.crypto.params.ParametersWithRandom;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPrivateKey;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPublicKey;
import org.bouncycastle.jcajce.spec.SM2ParameterSpec;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.spec.ECParameterSpec;

public class SM2Util {
    public SM2Util() {
    }

    public static String decryptByPrivateKey(String data, String key) throws Exception {
        BCECPrivateKey privateKey = (BCECPrivateKey)generatePrivateKey(key);
        ECParameterSpec parameterSpec = privateKey.getParameters();
        ECDomainParameters domainParameters = new ECDomainParameters(parameterSpec.getCurve(), parameterSpec.getG(), parameterSpec.getN(), parameterSpec.getH());
        ECPrivateKeyParameters priKeyParameters = new ECPrivateKeyParameters(privateKey.getD(), domainParameters);
        SM2Engine engine = new SM2Engine();
        engine.init(false, priKeyParameters);
        return new String(engine.processBlock(Base64.decode(data), 0, Base64.decode(data).length), "utf-8");
    }

    public static String encryptByPublicKey(String data, String key) throws Exception {
        BCECPublicKey publicKey = (BCECPublicKey)generatePublicKey(key);
        ECParameterSpec parameterSpec = publicKey.getParameters();
        ECDomainParameters domainParameters = new ECDomainParameters(parameterSpec.getCurve(), parameterSpec.getG(), parameterSpec.getN(), parameterSpec.getH());
        ECPublicKeyParameters publicKeyParameters = new ECPublicKeyParameters(publicKey.getQ(), domainParameters);
        SM2Engine engine = new SM2Engine();
        engine.init(true, new ParametersWithRandom(publicKeyParameters));

        return Base64.encode(engine.processBlock(data.getBytes("utf-8"), 0, data.getBytes("utf-8").length));
    }

    public static String signByPrivateKey(String data, String key, String appID) throws Exception {
        PrivateKey privateKey = generatePrivateKey(key);
        Signature signature = Signature.getInstance("SM3withSM2", new BouncyCastleProvider());
        signature.setParameter(new SM2ParameterSpec(appID.getBytes()));
        signature.initSign(privateKey);
        signature.update(data.getBytes("utf-8"));
        return Base64.encode(signature.sign());
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 100; i++) {
                       sb.append(UUID.randomUUID().toString());
        }

    }

    public static boolean verifyByPublicKey(String data, String key, String appID, String originalText) throws Exception {
        PublicKey publicKey = generatePublicKey(key);
        Signature signature = Signature.getInstance("SM3withSM2", new BouncyCastleProvider());
        signature.setParameter(new SM2ParameterSpec(appID.getBytes()));
        signature.initVerify(publicKey);
        signature.update(originalText.getBytes("utf-8"));
        return signature.verify(Base64.decode(data));
    }

    public static Map<String, Object> generateKeys() throws Exception {
        ECGenParameterSpec sm2Spec = new ECGenParameterSpec("sm2p256v1");
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("EC", new BouncyCastleProvider());
        keyPairGen.initialize(256);
        keyPairGen.initialize(sm2Spec);
        KeyPair keyPair = keyPairGen.generateKeyPair();
        BCECPublicKey publicKey = (BCECPublicKey)keyPair.getPublic();
        BCECPrivateKey privateKey = (BCECPrivateKey)keyPair.getPrivate();
        HashMap keyMap = new HashMap(2);
        keyMap.put("sm2PublicKey", Base64.encode(publicKey.getEncoded()));
        keyMap.put("sm2PrivateKey", Base64.encode(privateKey.getEncoded()));
        return keyMap;
    }

    private static PrivateKey generatePrivateKey(String privateKey) throws Exception {
        byte[] keyBytes = Base64.decode(privateKey);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("EC", new BouncyCastleProvider());
        return keyFactory.generatePrivate(pkcs8KeySpec);
    }

    public static PublicKey generatePublicKey(String publicKey) throws Exception {
        byte[] keyBytes = Base64.decode(publicKey);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("EC", new BouncyCastleProvider());
        return keyFactory.generatePublic(x509KeySpec);
    }



    static {
        Security.addProvider(new BouncyCastleProvider());
    }
}
