package com.wosai.mpay.api.ccb;

import com.google.common.collect.Sets;

import java.util.Set;

/**
 * <AUTHOR> Date: 2021/8/12 Time: 1:37 下午
 */
public class CcbConstants {

    public static final String COPLFID = "B0004";
    public static final String CCB_IBS_VERSION = "V6";
    public static final String UNIONPAY_TX_CODE = "YLJS02";

    public static final String UNIONPAY_TX_CODE_V2 = "YLJS01";

    public static final String AHN_CD = "Ahn_CD";

    public static final String PY_INF_DSC = "Py_Inf_Dsc";
    public static final String USR_ID = "Usr_ID";

    public static final String WX_TX_CODE = "WXPT01";
    public static final String ALIPAY_TX_CODE = "ZFBPT1";
    public static final String TRADE_TYPE_JS = "JSAPI";
    public static final String TRADE_TYPE_MINI = "MINIPRO";
    public static final String REFUND_TX_CODE = "5W4004";
    public static final String QUERY_TX_CODE = "5W4003";
    public static final String CUR_CODE_RMB = "01";

    public static final String TDCD_IND_DYNAMIC = "0";
    public static final String TDCD_IND_STATIC = "1";

    public static final String TX_SPECIAL_EC_PAY = "0"; //支付，包括所有的 支付/消费类功能
    public static final String TX_SPECIAL_EC_REFUND = "1"; //退款，包括所有的 退款/退货/撤销类功 能
    public static final String TX_SPECIAL_EC_REFUND_ALL = "2"; //全部

    public static final String WAP_QUERY_RESULT_SUCCESS = "Y";
    public static final String WAP_QUERY_RESULT_FAILURE = "N";

    public static final String DEFAULT_PAGE = "1";

    public static final String RET_CODE_SUCCESS = "00";
    public static final String RET_CODE_FAILURE = "-1";
    public static final String RET_CODE_UNKNOWN = "-2";
    public static final String RET_CODE_TIMEOUT = "-10";
    public static final Set<String> B2C_RET_CODE_UNKNOWN_SET = Sets.newHashSet(RET_CODE_UNKNOWN
            , RET_CODE_TIMEOUT);

    public static final String STATUS_CODE_SUCCESS = "00";
    public static final String STATUS_CODE_PROCESSING = "02";
    public static final String STATUS_CODE_FAILURE = "01";

    public static final String WAP_QUERY_STATUS_SUCCESS = "00";
    public static final String WAP_QUERY_STATUS_FAILURE = "01";
    public static final String WAP_QUERY_STATUS_UNKNOWN_02 = "02";
    public static final String WAP_QUERY_STATUS_UNKNOWN_04 = "04";
    public static final String WAP_QUERY_STATUS_TIMEOUT = "TO";

    public static final String QRCODE_TYPE_DYNAMIC = "0"; //二维码类型 动态码
    public static final String QRCODE_TYPE_STATIC = "1"; //二维码类型 静态码

    public static final Set<String> WAP_QUERY_PROCESSING_SET = Sets
            .newHashSet(WAP_QUERY_STATUS_UNKNOWN_02, WAP_QUERY_STATUS_UNKNOWN_04
                    , WAP_QUERY_STATUS_TIMEOUT);

    public static final String WAP_CODE_SUCCESS = "000000";

}
