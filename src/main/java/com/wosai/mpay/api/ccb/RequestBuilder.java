package com.wosai.mpay.api.ccb;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> Date: 2021/8/13 Time: 2:16 下午
 */
public class RequestBuilder {
    private final Map<String, String> platformField;
    private final Map<String, String> request;
    public RequestBuilder() {
        platformField = new HashMap<>();
        request = new HashMap<String, String>();
    }

    public Map<String, String> getPlatformField() {
        return platformField;
    }

    public Map<String, String> getRequest() {
        return request;
    }

    public RequestBuilder pfSet(String field, String value) {
        platformField.put(field, value);
        return this;
    }

    public RequestBuilder set(String field, String value) {
        request.put(field, value);
        return this;
    }

    public String genPlatform() {
        if (platformField.size() != 1) {
            throw new RuntimeException("平台参数数量异常");
        }
        Set<Map.Entry<String, String>> entrySet = platformField.entrySet();
        for (Map.Entry<String, String> entry : entrySet) {
            return entry.getKey() + "=" + entry.getValue();
        }
        return null;
    }

    public String genParams() {
        StringBuilder temp = new StringBuilder();
        request.forEach((k, v) -> {
            if (temp.length() > 0) {
                temp.append("&");
            }
            temp.append(k).append("=").append(v);
        });
        return temp.toString();
    }
}
