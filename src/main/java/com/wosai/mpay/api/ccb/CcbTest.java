package com.wosai.mpay.api.ccb;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * <AUTHOR> Date: 2021/8/25 Time: 9:31 上午
 */
public class CcbTest {
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter
            .ofPattern("yyyyMMddHHmmss");
    private static final String GATEWAY = "http://juhetest.ccb.com/CCBIS/B2CMainPlat_00_PTJ";
    private static final String PUBLIC_KEY = "30819d300d06092a864886f70d010101050003818b00308187028181009a235efa57653b716b4163406caa49137bd3247edc27413d894af5fa217b11be83df194bba0e69e4abfa399f3aed662e8696c14fd061832b694bc81573a8c1047689ddf4dc97faced859b9551c09785d04553348606778171af02343943b05f648ac4984ce84139876e72005175c60c339909e7c0991cb0b080eb1128a443e1d020111";

    private static void alipayPrecreate() throws MpayException, MpayApiNetworkError {
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.COPLFID, CcbConstants.COPLFID);
        builder.set(BusinessFields.MERCHANT_ID, "105910100194086");
        builder.set(BusinessFields.POS_ID, "313352813");
        builder.set(BusinessFields.BRANCH_ID, "441000000");
        builder.set(BusinessFields.ORDER_ID, System.currentTimeMillis() + "");
        builder.set(BusinessFields.PAYMENT, "0.01");
        builder.set(BusinessFields.CUR_CODE, CcbConstants.CUR_CODE_RMB);
        builder.set(BusinessFields.TX_CODE, CcbConstants.ALIPAY_TX_CODE);
        builder.set(BusinessFields.TRADE_TYPE, CcbConstants.TRADE_TYPE_JS);
        builder.set(BusinessFields.USERID, "2088222035343046");

        Map<String, Object> result = new CcbClient().call(builder, GATEWAY, PUBLIC_KEY);
        System.out.println(result);
    }

    private static void unionpayPrecreate() throws MpayException, MpayApiNetworkError {
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.COPLFID, CcbConstants.COPLFID);
        builder.set(BusinessFields.CCB_IBS_VERSION, CcbConstants.CCB_IBS_VERSION);
        builder.set(BusinessFields.TX_CODE, CcbConstants.UNIONPAY_TX_CODE);
        builder.set(BusinessFields.MERCHANT_ID, "105910100194086");
        builder.set(BusinessFields.POS_ID, "313352813");
        builder.set(BusinessFields.BRANCH_ID, "441000000");
        builder.set(BusinessFields.AHN_TXN_AMT, "1");
        builder.set(BusinessFields.ORDR_NO, System.currentTimeMillis() + "");
        builder.set(BusinessFields.PY_VLD_TM, "150");
        builder.set(BusinessFields.DEAL_OVERTIME, LocalDateTime.now().plusMinutes(10)
                .format(DATE_TIME_FORMATTER));
        builder.set(BusinessFields.ORDR_DSC, "一笔订单");
        builder.set(BusinessFields.USR_ID, "admin");
        builder.set(BusinessFields.IP, "************");
        builder.set(BusinessFields.TXN_NTC_ADR, "https://shouqianba.com");
        builder.set(BusinessFields.TDCD_ADR, "https://shouqianba.com");
        builder.set(BusinessFields.VCHR_TDCD_IND, "1");

        Map<String, Object> result = new CcbClient().call(builder, GATEWAY, PUBLIC_KEY);
        System.out.println(result);
    }

    public static void main(String[] args) throws MpayException, MpayApiNetworkError {
        unionpayPrecreate();
//        alipayPrecreate();
    }
}
