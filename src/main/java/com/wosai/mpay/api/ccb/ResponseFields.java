package com.wosai.mpay.api.ccb;

/**
 * <AUTHOR> Date: 2021/8/12 Time: 1:38 下午
 */
public class ResponseFields {

    public static final String SUCCESS = "SUCCESS"; //请求返回结 果,此字段是通 信标识，表 示通信成功
    public static final String ERR_CODE = "ERRCODE"; //错误码,000000表示 交易成功， 非000000表 示交易失 败，错误信 息可以查看 ERRMSG字 段
    public static final String ERR_MSG = "ERRMSG"; //错误信息,错误信息描 述
    public static final String TX_CODE = "TXCODE"; //交易码
    public static final String APP_ID = "appId"; //微信分配的 APPID
    public static final String TIME_STAMP = "timeStamp"; //时间戳
    public static final String NONCE_STR = "nonceStr"; //随机串
    public static final String PACKAGE = "package"; //数据包
    public static final String SIGN_TYPE = "signType"; //签名方式
    public static final String PAY_SIGN = "paySign"; //签名数据
    public static final String PARTNER_ID = "partnerid"; //子商户的商户号
    public static final String PREPAY_ID = "prepayid"; //预支付交易会话 ID
    public static final String MWEB_URL = "mweb_url"; //微信 H5 支付中间页 面 URL
    public static final String JSAPI = "jsapi"; //Jsapi 响应参数
    public static final String OUT_TRADE_NO = "out_trade_no"; //商户订单号
    public static final String TRADE_NO = "trade_no"; //支付宝交易号
    public static final String YL_TRADE_NO = "yl_trade_no"; //银联交易号
    public static final String TRACE_ID = "TRACEID"; //全局事件跟 踪号,建行交易流 水号
    public static final String AGN_DIR_ADR = "Agn_Dir_Adr"; //银联js重定向地址

    public static final String RESULT = "RESULT"; //订单结果,Y:成功 N:失败
    public static final String ERROR_CODE = "ERRORCODE"; //错误码,报错时返回
    public static final String ERROR_MSG = "ERRORMSG"; //错误信息,报错时返回
    public static final String ORDER_NUM = "ORDER_NUM"; //订单号
    public static final String PAY_AMOUNT = "PAY_AMOUNT"; //支付金额
    public static final String AMOUNT = "AMOUNT"; //退款金额

    public static final String DATA_INFO = "dataInfo";
    public static final String LIST = "list";
    public static final String CUR_PAGE = "CUR_PAGE"; //当前⻚次,每⻚最多返回10条记录
    public static final String PAGE_COUNT = "PAGE_COUNT"; //总⻚次
    public static final String ED_CRD_PRTY_IDR_CD = "Ed_Crd_Prty_Idr_CD"; //商户号
    public static final String PY_AMT = "Py_Amt"; //支付金额,按时间查询才会返回。 单位:元
    public static final String MRCH_RFND_AMT = "Mrch_Rfnd_Amt"; //商户退款金额,按时间查询才会返回。 单位:元
    public static final String ONLN_PY_TXN_ORDR_ID = "OnLn_Py_Txn_Ordr_ID"; //订单编号
    public static final String CLRG_STM_DT_TM = "Clrg_Stm_Dt_Tm"; //交易时间,格式[yyyyMMddhhmiss]
    public static final String ACQ_FNDS_CLRG_DT = "Acq_Fnds_Clrg_Dt"; //记账日期,格式[yyyyMMdd]
    public static final String ORDR_TM = "Ordr_Tm"; //原支付订单时 间,格式[yyyyMMddhhmiss]
    public static final String AHN_TXNAMT = "Ahn_TxnAmt"; //交易金额,单位:元
    public static final String ORDR_PYRFD_AMT = "Ordr_PyRfd_Amt"; //退款总额,单位:元
    public static final String TXN_CLRGAMT = "Txn_ClrgAmt"; //结算金额,单位:元
    public static final String MRCHCMSN_AMT = "MrchCmsn_Amt"; //手续费金额,单位:元
    public static final String ORIG_AMT = "Orig_Amt"; //订单金额,单位:元
    public static final String RETGDS_ORIG_TXNAMT = "RetGds_Orig_TxnAmt"; //原支付金额,单位:元
    public static final String CST_ACCNO = "Cst_AccNo"; //支付卡号
    public static final String CCY_CD = "CcyCd"; //币种
    public static final String TXN_STATUS = "Txn_Status"; //交易状态,00-交易成功标志 01-交易失败 02-不确定 04-不确定 TO-交易超时
    public static final String ORIOVRLSSTNEV_TRCK_NO = "OriOvrlsstnEV_Trck_No"; //银行流水号
    public static final String MSGRP_JRNL_NO = "MsgRp_Jrnl_No"; //商户流水号,退款时商户上送的退款 流水号
    public static final String PAY_MODE = "PAY_MODE"; //支付方式,BHK:建行;THK:他行;ZFB: 支付宝;CFT:微信
    public static final String CLRG_TXN_CD = "Clrg_Txn_CD"; //清算交易码
    public static final String CNTRPRT_ID = "Cntrprt_ID"; //商户柜台代码
    public static final String BSNITM_DSC = "BsnItm_Dsc"; //支付备注1
    public static final String TXN_ITM_DSC = "Txn_Itm_Dsc"; //支付备注2
    public static final String CRD_ATTR_BMP_DEF_ID = "Crd_Attr_Bmp_Def_ID"; //卡属性位图定 义编号
    public static final String POS_ID = "POS_ID"; //POS终端编号
    public static final String CRCRD_INSTM_PRD_NUM = "CrCrd_Instm_Prd_Num"; //信用卡分期期 数
    public static final String RTRVL_REF_NO = "Rtrvl_Ref_No"; //POSPH系统参 考号
    public static final String CRD_AHN_CD = "Crd_Ahn_CD"; //卡授权码
    public static final String DSTCRD_ISSUBNK_INSNO = "DstCrd_IssuBnk_InsNo"; //内卡发卡行机 构号
    public static final String ACQ_TXN_VCHR_NO = "Acq_Txn_Vchr_No"; //交易凭证号
    public static final String BIGAMT_INSTM_HDCG = "BigAmt_Instm_HdCg"; //分期手续费
    public static final String PCS_AVY_ID = "Pcs_Avy_ID"; //促销活动编号
    public static final String TMNL_BTNO = "Tmnl_BtNo"; //批次号
    public static final String TX_SPECIAL_EC = "TX_SPECIAL_EC"; //流水类型
    public static final String CARDNO_INPT_MTDCD = "CardNo_Inpt_MtdCd"; //卡片受理方式
    public static final String CNTRPRT_ACC_NM = "Cntrprt_Acc_Nm"; //付款方户名
    public static final String EBNK_VCHR_NO = "EBNK_VCHR_NO"; //网银凭证号
    public static final String TXN_RMRK = "Txn_Rmrk"; //交易备注
    public static final String ONLNPCSGIND_1_BMP_ECD = "OnlnPcsgInd_1_Bmp_ECD"; //处理标志位图 编码
    public static final String TXN_AMT = "TxnAmt"; //真实扣卡金额
    public static final String CST_TP_PRS_DSC = "Cst_Tp_Prs_Dsc"; //客户类型优惠 描述
    public static final String JRNL_TPCD = "Jrnl_TpCd"; //流水类型代 码,流水类型，PT为普通收单流水(支付退货 交易时该商户为真实 商户)，XM为数字货币流水


    public static final String POSID = "POSID"; //
    public static final String BRANCHID = "BRANCHID"; //
    public static final String ORDERID = "ORDERID"; //
    public static final String PAYMENT = "PAYMENT"; //
    public static final String CURCODE = "CURCODE"; //
    public static final String REMARK1 = "REMARK1"; //
    public static final String REMARK2 = "REMARK2"; //
    public static final String ACC_TYPE = "ACC_TYPE"; //
    public static final String ERRMSG = "ERRMSG"; //
    public static final String OPENID = "OPENID"; //
    public static final String SUB_OPENID = "SUB_OPENID"; //
    public static final String COPLFID = "COPLFID"; //
    public static final String MERCHANTID = "MERCHANTID"; //
    public static final String SIGN = "SIGN"; //



    public static final String RET_CODE = "retCode";
    public static final String RET_MSG = "retMsg";
    public static final String VALID_DATE = "validDate";
    public static final String TRANS_DATA = "transData";
    public static final String MERCHANT_CODE = "merchantCode";
    public static final String TERM_NO = "termNo";
    public static final String ORDER_ID = "orderId";
    public static final String AMT = "amt";
    public static final String TRANS_TYPE = "transType";
    public static final String TIME = "time";
    public static final String DATE = "date";
    public static final String STATUS_CODE = "statusCode";
    public static final String SYSTEM_RET_CODE = "SystemRetCode";
    public static final String PID = "pid";
    public static final String CARD_NO = "cardNo";
    public static final String THIRD_TRADE_NO = "thirdTradeNo";


}
