package com.wosai.mpay.api.ccb;

/**
 * <AUTHOR> Date: 2021/8/12 Time: 1:37 下午
 */
public class BusinessFields {

    public static final String CCB_PARAM = "ccbParam"; //预下单交易参数

    public static final String PLATFORM_FIELD = "PLATFORM_FIELD";
    public static final String PLATFORM_FIELD_VALUE = "PLATFORM_FIELD_VALUE";

    public static final String MERCHANT_ID = "MERCHANTID"; //商户代码,由建行统一分配
    public static final String POS_ID = "POSID"; //商户柜台代码,由建行统一分配
    public static final String BRANCH_ID = "BRANCHID"; //分行代码,由建行统一指定
    public static final String ORDER_ID = "ORDERID"; //订单号,由商户提供，最长30位
    public static final String PAYMENT = "PAYMENT"; //付款金额,由商户提供，按实际金额给出支付完成后,请商户与收到的商户通知中的付款金额比 对，确认两者金额一致;
    public static final String CUR_CODE = "CURCODE"; //币种,缺省为 01-人民币 (只支持人民币支付)
    public static final String REMARK1 = "REMARK1"; //备注1,一般作为商户自定义备注信 息使用，可在对账单中显示。
    public static final String REMARK2 = "REMARK2"; //备注2,一般作为商户自定义备注信 息使用，可在对账单中显示。
    public static final String TX_CODE = "TXCODE"; //交易码,由建行统一分配为 WXPT01
    public static final String TYPE = "TYPE"; //接口类型,分行业务人员在 P2 员工 渠道后台设置防钓鱼的 开关。1- 防钓鱼接口
    public static final String GATEWAY = "GATEWAY"; //网关类型,默认送 0
    public static final String CLIENT_IP = "CLIENTIP"; //客户端 IP,客户在商户系统中的 IP，即 客户登陆(访问)商户系统 时使用的 ip)
    public static final String REG_INFO = "REGINFO"; //客户注册信息,客户在商户系统中注册的信 息，中文需使用 escape 编码
    public static final String PRO_INFO= "PROINFO"; //商品信息,客户购买的商品 中文需使用 escape 编码
    public static final String REFERER = "REFERER"; //商户 URL,商户送空值即可; 具体请看 REFERER 设置说 明
    public static final String TIMEOUT = "TIMEOUT"; //订单超时时 间,格式: YYYYMMDDHHMMSS 如: 20120214143005 银行系统时间> TIMEOUT 时拒绝交易，若送空值则不 判断超时。
    public static final String TRADE_TYPE = "TRADE_TYPE"; //交易类型,JSAPI-- 公 众 号 支 付 、MINIPRO--小程序
    public static final String SUB_APPID = "SUB_APPID"; //小程序/公众 号的 APPID,当前调起支付的小程序/公 众号 APPID
    public static final String SUB_OPENID = "SUB_OPENID"; //用户子标识,用户在小程序/公众号 appid 下的唯一标识
    public static final String WX_CHANNEL_ID = "WX_CHANNELID"; //渠道商号,对于商户自定义的渠道商号
    public static final String COPLFID = "COPLFID"; //合作平台编 号,建行生活大 B 端交易时上送,接入的平台编号 C0002-收钱吧
    public static final String SYSID = "SYSID";
    public static final String USERID = "USERID"; //支付宝的客 户 USERID,支付宝的客户 USERID

    public static final String CCB_IBS_VERSION = "CCB_IBSVersion"; //版本号,默认填V6
    public static final String AHN_TXN_AMT = "Ahn_TxnAmt"; //授权交易 金额
    public static final String ORDR_NO = "Ordr_No"; //订单号,应保证该订单号是唯一的
    public static final String PY_VLD_TM = "Py_Vld_Tm"; //支付有效 时间,允许对一个订单进行支付 的最⻓相对时间，单位为 秒。 支付有效时间可以小于二 维码有效时间 (qrValidTime)。支付有 效时间由银联统一管理
    public static final String DEAL_OVERTIME = "DEAL_OVERTIME"; //任务处理 超时时间,订单接收超时时间
    public static final String ORDR_DSC = "Ordr_Dsc"; //订单描述,描述性文字，，最⻓200字 节，用于向付款人展示。
    public static final String USR_ID = "Usr_ID"; //用户标识,用户开放标识，用户信息 获取应答码不等于34时必 送
    public static final String IP = "IP"; //持卡人IP
    public static final String TXN_NTC_ADR = "Txn_Ntc_Adr"; //前台通知 地址
    public static final String TDCD_ADR = "TDCD_Adr"; //失败交易 前台通知 地址
    public static final String VCHR_TDCD_IND= "Vchr_TDCD_Ind"; //凭证二维 码标志,二维码类型，主扫场景， 0:动态码，1:静态码

    public static final String REQUEST_SN = "REQUEST_SN"; //请求序列号,唯一标识当次退款请 求，不可重复使用
    public static final String Mrch_ID = "Mrch_ID"; //建行商户编号
    public static final String MONEY = "MONEY"; //退款金额
    public static final String ORDER = "ORDER"; //订单号
    public static final String StDt_Tm = "StDt_Tm"; //开始日期时间,根据支付时间往前加4小时，格式 [yyyyMMddhhmiss]
    public static final String EdDt_Tm = "EdDt_Tm"; //结束日期时间,根据支付时间往后加 4小时，但日期不能超过今日，格式[yyyyMMddhhmiss]
    public static final String REFUND_CODE = "REFUND_CODE"; //退款流水号,可不填，商户可根据 需要填写，退款流水 号由商户的系统生成

    public static final String TX_SPECIAL_EC = "TX_SPECIAL_EC"; //交易类型
    public static final String OnLn_Py_Txn_Ordr_ID = "OnLn_Py_Txn_Ordr_ID"; //订单编号
    public static final String Scn_Idr = "Scn_Idr"; //场景标识,BHK - 本行卡;THK - 他行卡;ZFB - 聚合支 付支付宝;CFT - 聚合 支付微信
    public static final String POS_CODE = "POS_CODE"; //柜台号
    public static final String Rtrvl_Ref_No = "Rtrvl_Ref_No"; //系统参考号
    public static final String Cst_AccNo = "Cst_AccNo"; //交易卡号
    public static final String Rng_Min_Amt = "Rng_Min_Amt"; //交易区间最 小金额
    public static final String Rng_Max_Amt = "Rng_Max_Amt"; //交易区间最 大金额
    public static final String Txn_Status = "Txn_Status"; //交易状态,00-交易成功标志 01-交易失败 02-不确定
    public static final String EBNK_VCHR_NO = "EBNK_VCHR_NO"; //网银凭证号
    public static final String OriOvrlsstnEV_Trck_No = "OriOvrlsstnEV_Trck_No"; //银行流水号
    public static final String MsgRp_Jrnl_No = "MsgRp_Jrnl_No"; //商户的流水号
    public static final String Ori_Txn_ECD = "Ori_Txn_ECD"; //原交易的清 算交易码
    public static final String Txn_Prd_TpCd = "Txn_Prd_TpCd"; //交易期间类 型代码,查询时间范围类型: 06 最近24小时内 99自定义(此时 StDt_Tm和EdDt_Tm 必填，时间跨度建议不 要超过3天)
    public static final String Jrnl_TpCd = "Jrnl_TpCd"; //流水类型代 码,流水类型，PT为普通收单流水(支付退货 交易时该商户为真实 商户)，XM为数字货 币流水
    public static final String PAGE = "PAGE"; //当前⻚次


    public static final String QRCODE_TYPE = "qrCodeType";//二维码类型

    public static final String QRCODE = "qrCode"; // 二维码

    public static final String USER_AUTH_CODE = "userAuthCode"; // 用户授权码


}
