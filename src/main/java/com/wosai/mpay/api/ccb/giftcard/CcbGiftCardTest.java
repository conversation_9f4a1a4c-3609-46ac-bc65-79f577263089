package com.wosai.mpay.api.ccb.giftcard;

import com.ccb.mis.common.utils.apache.EncoderException;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;

import java.util.Map;

public class CcbGiftCardTest {
    public static final String URL = "http://121.40.54.232:8090/CCBIS/B2CMainPlat_00_ZHST";
    public static final CcbGiftCardClient client = new CcbGiftCardClient();
    public static final String PAY_TYPE = "PAY003";
    public static final String PRIVATE_KEY = "DeY7IroYpmJ4IS1m3hbPd4zZSWa4DcG4";

    public static void main(String[] args) throws MpayApiNetworkError, MpayException, EncoderException {
        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.putRequest(BusinessFields.CAMPUS_ID, "310000004");
        requestBuilder.putRequest(BusinessFields.CORP_ID, "1101");
        requestBuilder.putRequest(BusinessFields.TXCODE, PAY_TYPE);
        requestBuilder.putCcbSafeParam(BusinessFields.BUSINESS_ID, "SJ2023070415200");
        requestBuilder.putCcbSafeParam(BusinessFields.VPOS_ID, "V00519777");
        requestBuilder.putCcbSafeParam(BusinessFields.PAYMENT, "0.01");
        requestBuilder.putCcbSafeParam(BusinessFields.ACTUAL_PAYMENT, "0.01");
        requestBuilder.putCcbSafeParam(BusinessFields.QR_CODE, "CT000103251825475935");
        requestBuilder.putCcbSafeParam(BusinessFields.ORDER_ID, "7890280727000001");
        requestBuilder.putCcbSafeParam(BusinessFields.OFFLINE, "0");

        Map<String, Object> call = client.call(URL, PRIVATE_KEY, requestBuilder);
        JsonUtil.toJsonStr(call);
    }
}
