package com.wosai.mpay.api.ccb.giftcard;

public class BusinessFields {
    public static final String CAMPUS_ID = "CAMPUS_ID"; //园区id
    public static final String CORP_ID = "CORP_ID";
    public static final String TXCODE = "TXCODE";
    public static final String CCB_SAFE_PARAM = "ccbSafeParam";
    public static final String BUSINESS_ID = "BUSINESS_ID";
    public static final String VPOS_ID = "VPOS_ID";
    public static final String PAYMENT = "PAYMENT";
    public static final String ACTUAL_PAYMENT = "ACTUAL_PAYMENT";
    public static final String COUPON_INFO = "COUPON_INFO";
    public static final String ACC_NOS = "ACC_NOS";
    public static final String QR_CODE = "QR_CODE";
    public static final String CUST_ID = "CUST_ID";
    public static final String ORDER_ID = "ORDER_ID";
    public static final String OFFLINE = "OFFLINE";
    public static final String SIGN_TIME = "SIGN_TIME";
    public static final String CCB_IBS_VERSION = "CCB_IBSVersion";
    public static final String PT_STYLE = "PT_STYLE";
    public static final String PT_LANGUAGE = "PT_LANGUAGE";
}
