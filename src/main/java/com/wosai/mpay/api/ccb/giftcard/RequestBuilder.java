package com.wosai.mpay.api.ccb.giftcard;

import com.ccb.mis.common.utils.apache.EncoderException;
import com.wosai.mpay.util.ccb.giftcard.MCipherEncryptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;


public class RequestBuilder {
    private Map<String, String> request;
    private Map<String, String> ccbSafeParam;
    public static final Logger logger = LoggerFactory.getLogger(RequestBuilder.class);

    public RequestBuilder() {
        request = new HashMap<>();
        ccbSafeParam = new HashMap<>();
        request.put(BusinessFields.CCB_IBS_VERSION, CcbGiftCardConstants.DEFAULT_VERSION);
        request.put(BusinessFields.PT_STYLE, CcbGiftCardConstants.DEFAULT_STYLE);
        request.put(BusinessFields.PT_LANGUAGE, CcbGiftCardConstants.DEFAULT_LANGUAGE);
    }

    public void putRequest(String key, String value){
        request.put(key,value);
    }

    public void putCcbSafeParam(String key, String value) {
        ccbSafeParam.put(key, value);
    }

    public Map<String, String> getRequest() {
        return request;
    }

    public Map<String, String> getCcbSafeParam() {
        return ccbSafeParam;
    }

    public void genCcbSafeParam(String strKey) throws EncoderException {
        MCipherEncryptor ccbEncryptor = new MCipherEncryptor(strKey);
        StringBuilder temp = new StringBuilder();
        for (Object mapKey : ccbSafeParam.keySet().toArray()) {
            String value = ccbSafeParam.get(mapKey);
            if (value == null) {
                ccbSafeParam.remove(mapKey);
            } else {
                if (temp.length() > 0) {
                    temp.append("&");
                }
                temp.append(mapKey).append("=").append(value);
            }
        }
        try {
            String encrypt = ccbEncryptor.doEncrypt(temp.toString());
            request.put(BusinessFields.CCB_SAFE_PARAM, encrypt);
            ccbSafeParam.clear();
        } catch (Exception e) {
            logger.error("建行智慧食堂参数加密失败{}", e.getMessage());
            throw new EncoderException("加密参数失败");
        }
    }
}
