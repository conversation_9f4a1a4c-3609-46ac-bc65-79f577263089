package com.wosai.mpay.api.ccb;

import ccb.pay.api.util.CCBPayUtil;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.util.Map;

/**
 * <AUTHOR> Date: 2021/8/12 Time: 1:37 下午
 */
public class CcbClient {
    public static final Logger logger = LoggerFactory.getLogger(CcbClient.class);

    private int connectTimeout = 3000;
    private int readTimeout = 15000;
    private String charset = "utf-8";

    public CcbClient() {
    }

    public CcbClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public CcbClient(int connectTimeout, int readTimeout, String charset) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
        this.charset = charset;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public Map<String, Object> call(RequestBuilder requestBuilder, String gateway
            , String publicKey) throws MpayException, MpayApiNetworkError {
        return call(requestBuilder, gateway, publicKey, null);
    }

    public Map<String, Object> call(RequestBuilder requestBuilder, String gateway
            , String publicKey, Map<String, String> headers)
            throws MpayException, MpayApiNetworkError {
        String merInfo = requestBuilder.genPlatform();
        CCBPayUtil ccbPayUtil = new CCBPayUtil();
        String params;
        String oriParams;
        try {
            oriParams = requestBuilder.genParams();
            params = ccbPayUtil.makeCCBParam(oriParams, publicKey);
        } catch (UnsupportedEncodingException e) {
            throw new MpayException("建行预下单参数构建异常", e);
        }

        String url = gateway + "?" + merInfo + "&" + BusinessFields.CCB_PARAM + "=" + params;
        logger.debug("request {}, oriParams: {}", url, oriParams);
        Map<String, Object> result = HttpClientUtils.doGet(CcbClient.class.getName()
                , null, null
                , url, null, headers, charset, connectTimeout, readTimeout);
        logger.debug("response {}", result);
        try{
            String body = MapUtil.getString(result, HttpClientUtils.BODY_RESULT_FIELD);
            return JsonUtil.jsonStringToObject(body, Map.class);
        }catch (Exception e){
            throw new MpayApiUnknownResponse("parse response error: " + e.getMessage(), e);
        }
    }

}
