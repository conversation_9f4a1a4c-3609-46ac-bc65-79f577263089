package com.wosai.mpay.api.ccb.giftcard;

import com.ccb.mis.common.utils.apache.EncoderException;
import com.wosai.mpay.api.ccb.CcbClient;
import com.wosai.mpay.api.tl.TlConstants;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class CcbGiftCardClient {
    public static final Logger logger = LoggerFactory.getLogger(CcbClient.class);

    private int connectTimeout = 3000;
    private int readTimeout = 5000;
    private String charset = "utf-8";

    public CcbGiftCardClient() {
    }

    public CcbGiftCardClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public CcbGiftCardClient(int connectTimeout, int readTimeout, String charset) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
        this.charset = charset;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public Map<String,Object> call(String serviceUrl, String privateKey, RequestBuilder request) throws MpayException, MpayApiNetworkError, EncoderException {
        logger.info("request: {}", JsonUtil.objectToJsonString(request));
        request.genCcbSafeParam(privateKey);
        String response = HttpClientUtils.doPost(CcbGiftCardClient.class.getName(), null, null, serviceUrl, "application/x-www-form-urlencoded", request.getRequest(), TlConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        return JsonUtil.jsonStringToObject(response, Map.class);
    }

}
