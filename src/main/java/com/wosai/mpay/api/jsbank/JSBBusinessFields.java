package com.wosai.mpay.api.jsbank;

/**
 * <AUTHOR>
 * @description 江苏银行-业务字段
 * @date 2024/11/3
 */
public class JSBBusinessFields {
    /**
     * 扩展字段1
     * 支付接口返回时: 表示付款人编号。微信: openid, 支付宝: buyerid
     * 查询接口返回时: 表示交易渠道。见:TradeTypeEnum.java
     */
    public static final String FIELD1 = "field1";
    /**
     * 扩展字段2
     * 支付接口返回时: 表示渠道流水号
     */
    public static final String FIELD2 = "field2";
    /**
     * 扩展字段3
     * 交易支付成功时间
     */
    public static final String FIELD3 = "field3";

    /**
     * 支付
     */
    public static final String OUT_TRADE_NO = "outTradeNo";//商户订单号。商户系统内部的订单号，5到32个字符，只能包含字母数字或者下划线，区分大小写，每次下单请求确保在商户系统唯一
    public static final String AUTH_CODE = "authCode";//授权码。扫码支付授权码，设备读取用户展示的条码或者二维码信息；一个授权码只能支付成功一次，请勿用同一个授权码重复发起支付请求
    public static final String TOTAL_FEE = "totalFee";//总金额，以元为单位，不允许包含任何字、符号
    public static final String TRADE_TYPE = "tradeType";//交易类型， 见:JSBTradeTypeEnum.java
    public static final String PRODUCT_INFO = "proInfo";//商品名称。购买商品描述，该字段会出现在用户支付完成后支付渠道推送的支付结果通知中，所以如果没有需要该字段不能随意给值
    public static final String ATTACH = "attach";//附加信息。商户附加信息，可做扩展参数
    public static final String DEVICE_NO = "deviceNo";//终端设备编号。商户自定义，要求该参数能够准确定位到一台设备，可使用机具编号或硬盘号等
    public static final String MCH_IP = "mchIp";//终端ip。要求为生成订单的实际终端IP
    public static final String SHOP_ID = "shopId";//门店id。特殊情况上送，收钱吧门店号对应江苏银行门店编号
    public static final String TIME_START = "timeStart";//订单生成时间。格式为yyyymmddhhmmss，如2009年12月25日9点10分10秒表示为20091225091010。时区为GMT+8 beijing。该时间取自商户服务器。注：订单生成时间与超时时间需要同时传入才会生效。
    public static final String TIME_EXPIRE = "timeExpire";//订单超时时间。格式为yyyymmddhhmmss
    public static final String ORDER_STATUS = "orderStatus";//订单状态，见:支付是JSBOrderStatusEnum.java, 退款是 JSBRefundOrderStatusEnum.java
    public static final String ORDER_NO = "orderNo";//平台订单号
    public static final String PAY_ID = "payId";//渠道流水号

    /**
     * 查询
     */
    public static final String EXTEND_MAP = "extendMap";//扩展参数
    public static final String RECORD_LIST = "list";//数据列表

    /**
     * 退款
     */
    public static final String REFUND_NO = "refundNo";//平台退款订单号
    public static final String OUT_REFUND_NO = "outRefundNo";//商户退款订单号
    public static final String REFUND_AMT = "refundAmt";//退款金额
    public static final String FEE = "fee";//退还手续费

    /**
     * 微信预下单
     */
    public static final String OPEN_ID_PREFIX = "0";//江苏银行默认会给openId加上前缀:0
    public static final String OPEN_ID = "openId";//付款人编号。小程序支付时填openId, app支付时填appId
    public static final String BACK_URL = "backUrl";//支付结果回调通知
    public static final String EXT_FIELD1 = "extfld1";//交易类型为JSAPI时填入appid
    public static final String EXT_FIELD2 = "extfld2";//可选送入商户外部订单号(outOrderNo)。默认上送收钱吧789商户编号
    public static final String APPID = "appId";//应用id
    public static final String PACKAGE = "packAge";//统一下单接口返回的prepay_id参数值,如wx201410272009395522657a690389285100
    public static final String TIMESTAMP = "timeStamp";//时间戳
    public static final String NONCE_STR = "nonceStr";//随机字符串
    public static final String SIGN_TYPE = "signType";//微信签名方式
    public static final String PAY_SIGN = "paySign";//签名
    public static final String PREPAY_ID = "prepay_id";

    /**
     * 支付宝预下单
     */
    public static final String AMOUNT = "amount";//交易总金额，以元为单位
    public static final String USER_ID = "userId";//买家的支付宝唯一用户号
    public static final String ORDER_DESC = "orderDesc";//商品名称
    public static final String ALIPAY_TRADE_NO = "tradeNo";//支付宝预下单订单号

    /**
     * 用户信息查询
     */
    public static final String PARTNER_BUYER_ID = "partnerBuyerId";//会员id，用户在商户端的唯一标志，可以是ID或用户号等
    public static final String EXT_FLD1 = "extFld1";//拓展字段1
    public static final String EXT_FLD2 = "extFld2";//拓展字段2
    public static final String EXT_FLD3 = "extFld3";//拓展字段3

    /**
     * 用户信息查询响应字段
     */
    public static final String RETURN_CODE = "returnCode";//返回码，00-成功 其他失败
    public static final String RETURN_MSG = "returnMsg";//返回信息
    public static final String USER_STATUS = "userStatus";//用户状态(0-正常 1-销户 2-开户待打款 4-打款成功待验证)
    public static final String USER_NO = "userNo";//银行用户号
    public static final String AVAILABLE_ACCT_NO = "avalaibleAcctNo";//可用户子账号
    public static final String FROZEN_ACCT_NO = "frozenAcctNo";//冻结户子账号

}
