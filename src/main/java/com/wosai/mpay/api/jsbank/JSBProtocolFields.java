package com.wosai.mpay.api.jsbank;

/**
 * <AUTHOR>
 * @description 江苏银行协议字段定义
 * @date 2024/11/3
 */
public class JSBProtocolFields {
    /**
     * 协议默认值
     */
    public static final String DEFAULT_SIGN_METHOD = "RSA"; //默认签名方法
    public static final String DEFAULT_SIGN_ALGORITHM = "SHA1withRSA"; //默认的签名算法
    public static final String DEFAULT_CONTENT_TYPE = "text/xml";
    public static final String DEFAULT_CHANNEL_NO = "m";
    public static final String DEFAULT_PUBLIC_KEY_CODE = "00";
    public static final String DEFAULT_VERSION = "v1.0.0";
    public static final String DEFAULT_CHARSET = "utf-8";

    /**
     * 系统级请求参数
     */
    public static final String METHOD = "service"; //请求方法
    public static final String CREATE_DATE = "createDate"; //报文发送日期, 格式: YYYYMMDD
    public static final String CREATE_TIME = "createTime"; //报文发送时间, 格式: HHMMSS
    public static final String BIZ_DATE = "bizDate"; //发送方业务日期, 格式: YYYYMMDD
    public static final String MSG_ID = "msgId"; //报文流水号, 保证唯一即可
    public static final String PARTNER_ID = "partnerId"; //合作商id, 即商户在江苏银行的用户id。注意生产和测试不同。申请对应环境的商户后生成，由银行业务人员提供
    public static final String CHANNEL_NO = "channelNo"; //渠道号，固定为"m"
    public static final String PUBLIC_KEY_CODE = "publicKeyCode"; //公私钥对编号。固定为"00"
    public static final String VERSION = "version"; //版本号, 固定为"v1.0.0"
    public static final String CHARSET = "charset"; //字符集, 默认"utf-8"
    public static String SIGN = "sign";//签名key
    public static String SIGN_TYPE = "signType";//签名类型, 目前只支持 RSA
}
