package com.wosai.mpay.api.jsbank.enums;


/**
 * <AUTHOR>
 * @description 江苏银行-订单状态
 * @date 2024/11/4
 */
public enum JSBOrderStatusEnum {
    SUCCESS("1", "支付成功"),
    PROCESSING("2", "处理中"),
    FAILED("3","支付失败");

    private final String status;
    private final String desc;

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    JSBOrderStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static JSBOrderStatusEnum of(String status) {
        if (null == status) {
            return PROCESSING;
        }
        for (JSBOrderStatusEnum e : JSBOrderStatusEnum.values()) {
            if (e.status.equals(status)) {
                return e;
            }
        }
        return PROCESSING;
    }
}


