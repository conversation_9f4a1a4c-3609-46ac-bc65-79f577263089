package com.wosai.mpay.api.jsbank;

import com.wosai.mpay.api.jsbank.enums.JSBRespCodeEnum;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @description 江苏银行响应工具类
 * @date 2024/9/2
 */
public class JSBResponseUtil {
    /**
     * 响应是否成功
     *
     * @param respCode
     * @return
     */
    public static boolean isResponseSuccess(String respCode) {
        return JSBRespCodeEnum.SUCCESS.getCode().equals(respCode);
    }

    /**
     * 业务是否成功
     *
     * @param bizErrCode
     * @return
     */
    public static boolean isBizSuccess(String bizErrCode) {
        return StringUtils.isBlank(bizErrCode);
    }
}
