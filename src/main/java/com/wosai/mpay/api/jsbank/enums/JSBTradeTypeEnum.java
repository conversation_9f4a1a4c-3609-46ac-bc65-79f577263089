package com.wosai.mpay.api.jsbank.enums;


/**
 * <AUTHOR>
 * @description 江苏银行-交易类型
 * @date 2024/11/3
 */
public enum JSBTradeTypeEnum {
    WECHAT("2", "微信"),
    ALIPAY("3","支付宝"),
    UNION_PAY("4","银联"),
    ;

    private final String type;
    private final String desc;

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    JSBTradeTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static JSBTradeTypeEnum of(String type) {
        if (null == type) {
            return null;
        }
        for (JSBTradeTypeEnum e : JSBTradeTypeEnum.values()) {
            if (type.equals(e.type)) {
                return e;
            }
        }
        return null;
    }
}


