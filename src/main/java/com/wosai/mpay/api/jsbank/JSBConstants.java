package com.wosai.mpay.api.jsbank;

/**
 * <AUTHOR>
 * @description 江苏银行-常量定义
 * @date 2024/11/3
 */
public class JSBConstants {
    /**
     * 请求方法定义
     */
    public static final String METHOD_PAY = "ePay"; //付款码支付(用户被扫)
    public static final String METHOD_QUERY = "payCheck"; //订单状态查询
    public static final String METHOD_REFUND = "payRefund"; //退款
    public static final String METHOD_WECHAT_PREPAY = "paymentWXPay"; //微信预下单(C扫B)
    public static final String METHOD_ALIPAY_PREPAY = "aliCreatePay";    //支付宝预下单(C扫B)
    public static final String METHOD_QUERY_LYB_USER_INFO = "queryLybUserInfo"; //用户信息查询

    public static final String DATE_SIMPLE_FORMAT = "yyyyMMdd";//日期格式
    public static final String TIME_SIMPLE_FORMAT = "HHmmss";//时间格式
    public static final String ORDER_TIME_FORMAT = "yyyyMMddHHmmss";//订单时间格式
    public static String CONNECTOR = "&";//参数连接字符
    public static String MUL_CONNECTOR = "&-&";//多参数连接字符
    public static String ASSIGNMENT = "=";//赋值字符

    public static String WECHAT_TRADE_TYPE_JSAPI = "JSAPI";//微信交易类型-小程序
}
