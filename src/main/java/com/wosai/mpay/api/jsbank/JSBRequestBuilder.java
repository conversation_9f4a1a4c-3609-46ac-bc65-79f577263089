package com.wosai.mpay.api.jsbank;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 江苏银行请求构建
 * @date 2024/11/3
 */
public class JSBRequestBuilder {
    private final Map<String,Object> request;

    public JSBRequestBuilder(){
        request = new LinkedHashMap<>(16);
    }

    public void set(String field, Object value) {
        request.put(field,  value);
    }

    public Map<String,Object> build(){
        return request;
    }
}
