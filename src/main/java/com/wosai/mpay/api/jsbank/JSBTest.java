package com.wosai.mpay.api.jsbank;

import cn.hutool.core.date.DateUtil;
import com.wosai.mpay.api.jsbank.enums.JSBTradeTypeEnum;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.Map;
import java.util.UUID;

import static com.wosai.mpay.api.jsbank.JSBConstants.*;
import static com.wosai.mpay.api.jsbank.JSBProtocolFields.DEFAULT_SIGN_ALGORITHM;


/**
 * <AUTHOR>
 * @description 江苏银行-测试类
 * @date 2024/9/2
 */
public class JSBTest {
    public static final Logger logger = LoggerFactory.getLogger(JSBTest.class);

    public JSBTest() {
    }

    private static final SafeSimpleDateFormat dateSimpleFormat = new SafeSimpleDateFormat(JSBConstants.DATE_SIMPLE_FORMAT);
    private static final SafeSimpleDateFormat timeSimpleFormat = new SafeSimpleDateFormat(JSBConstants.TIME_SIMPLE_FORMAT);
    private static final SafeSimpleDateFormat orderTimeSimpleFormat = new SafeSimpleDateFormat(JSBConstants.ORDER_TIME_FORMAT);

    //江苏银行公钥
    private static final String jsbPublicKey = "";
    //收钱吧私钥
    private static final String privateKey = "";
    //收钱吧公钥
    private static final String publicKey = "";
    //测试环境访问地址
    private static final String REQUEST_URL_BETA = "https://epaytest.jsbchina.cn:9999/eis/merchant/merchantServices.htm";
    //合作商id
    private static final String partnerId = "ab53d34b404b4f19afe1e2c559b3ee30";
    //回调通知地址
    private static final String notifyUrl = "http://upay-dispatcher.beta.iwosai.com/upay/v2/notify/trade/client";
    //终端号
    private static final String deviceNo = "24090338497000016134";
    //订单号
    private static final String outTradeNo = "7895222498254023";
    //微信appId
    private static final String wechatSubAppId = "wxccbcac9a3ece5112";
    //微信openId
    private static final String wechatOpenId = "";
    //支付宝appId
    private static final String alipayAppId = "2018011801955940";//测试环境,江苏银行提供
    //支付宝userId
    private static final String alipayUserId = "";


    /**
     * 测试商户：
     * partnerid:0544eea43a4745fa92fc999237f550ed
     * 母户名称：苏银凯基消费金融测试
     * 母账号：*****************
     * 行内商户号：20240711105832651001
     *
     * 定义这些常量
     */
    private static final String lybPartnerId = "0544eea43a4745fa92fc999237f550ed";
    private static final String lybPrivateKey = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDr75NsojcAnY1aY2fl4U5OwJotB98nOmhtI2Z9LVFEWXr5FJQkDgTwROdIQp49qhB1r2y/H81+4w/A2pG1YrG/b4K/+JPaNFCuRGx5AI/nYuiI/qk9RJ0XATLdfRvbOzS4TBZrcsXIgN6cw+i9vvIqsYsVLMgj8QwQkMOT7JrguFykDQC2GdhKo54+liQhB8UWAu99eHeTjL3Y/YjO+ZLNasRvQirWz7qPjzDAJTMInFWt9qE2qQ1TYQrdnuViTVUQjU1ogAiLWyjMhs3zMZWhIBa624/U3kqNKLDY3KkAUDFdD7OzFXtzQfIxK6wWmYTYTqh9m1/8cS2SnVjmL82nAgMBAAECggEALx5/KlxxMKnGf61HpDTtfzPe9qxSSFLVZSqDPu+sgcJ/GTyfY137ZYLVT+yiDr2moOJCjR8MWtF9JW5hWEDYL9vU2IMMADGfhVY79aSDmNn7h9SYNxi7aMs//dUNGakG2+GyPo5C6sq16sgDdNWsJpOjq7xwUiFDYBGRRbskqUk2WuxgMfI48bAV58mgMo8ihh53I1x5b+J1X4wIj1Fi7fqpyRvo3bi/WD8vKjodnk4a8e0QXbSUvvvTLCUi7+uOfW4De/nMpWawQZKdNdDflQsj1Al9vag1rFYAlCtWDA67NxMbzkR4QIFzeXhCKPjkeACc3g0Ta4HfoH2gFwmuaQKBgQD+HxLRWFP6nKlLgMqD4Jt4GNcmrBf5og/AeVZkIB54HcZDUJ+2+M3ECUK3sBjTjtm6rGlOBKl/D/9kfjL2BxPIpw+Xf9/JsYGQiXv1fqcSpAqqKPiix5LcBw1QgCr7N1afmO8E5hyoCAZSpWOdXpcdLEHGLRMkvERUDlbiSVZWmQKBgQDtrhYLcwuMLajPANByllIC+N5EOFCGntzVlxhb/81v9PVbU3hGFiFkrA6eaaIwd3yoNTemcY6IugkhwyNPa4cIqXCMiBEPcgiBG8engALIbNwjSbx6HpN9sOsip03QusT9MtX/nxwyvau3lfUcBjc0wHpv+nQTiMDj6kBygwkuPwKBgHLz/4EvoZ2GbJa5BeMuIWXsZrcZtia4hsOcn9XfKalQXrVorTcJyZraTZHmWPSqdBebkm82cK4HwJHm/IqKnQpxd/4iXEtSvrdxWd+tkUo/ZoueyQppmSdrETzPHaJSYB8GAWmH5oKkTqfoJO21MkqCEKbjEPvXJlmXuvnWDvZRAoGAOjUyLOel4lSkTN3ULtvlhdjRBkAMaIurZZu1WYKSG2MQW6dbgz6UOwVwJxeTou8AVmmCEX8p+w4kgmI52Y9p6JMmfyQrmW7B6myVPs444EDYwMOkJvwp/tQ/IaN7iSzUZXX3tryOuVZ+IV3WQ2C6UG77DUV2+/m305lg4yp4Kx0CgYAO2kw2roTVU3rxZctgtYbFmTB4cfCgowLftqv4Gm3btzbtOYbZYdstjcXzDjCGX//T8lsUohbrWi/BPP0U+lYWIBOW6AziSmwvnawtW5D+C54wiOmfgY9V1GJSRNAoPTJaSuNedOT/L7aEBGvzqdU4uAJrID9tvrsZ4pgcFuF3ow==";
    private static final String lybPrivateKeyPirPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA6++TbKI3AJ2NWmNn5eFO\n" +
            "TsCaLQffJzpobSNmfS1RRFl6+RSUJA4E8ETnSEKePaoQda9svx/NfuMPwNqRtWKx\n" +
            "v2+Cv/iT2jRQrkRseQCP52LoiP6pPUSdFwEy3X0b2zs0uEwWa3LFyIDenMPovb7y\n" +
            "KrGLFSzII/EMEJDDk+ya4LhcpA0AthnYSqOePpYkIQfFFgLvfXh3k4y92P2IzvmS\n" +
            "zWrEb0Iq1s+6j48wwCUzCJxVrfahNqkNU2EK3Z7lYk1VEI1NaIAIi1sozIbN8zGV\n" +
            "oSAWutuP1N5KjSiw2NypAFAxXQ+zsxV7c0HyMSusFpmE2E6ofZtf/HEtkp1Y5i/N\n" +
            "pwIDAQAB";

    private static final String lybPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCaSf1V3J2ARajoAS0NKlxn8UFgQ6/Fr5VH0oyWWvG5nBAk8akdOAHF85DTI8QVdnRsxIEASwUEQo3ME13p7pGELtsQqAK//Z12nxleX4bj1TCU1WFg4bzu3t12qn5S9Tu5iYdt+nzgUtbIGpP3dhJnDt0f0HTtXhdicJsW6p2FnQIDAQAB";
    // 母账号：*****************
    private static final String accountNo = "*****************";
    // 行内商户号：20240711105832651001
    private static final String merchantNo = "20240711105832651001";




    public static JSBClient jsbClient = new JSBClient();

    private static JSBRequestBuilder buildSystemParams() {
        JSBRequestBuilder requestBuilder = new JSBRequestBuilder();
        Date currentDate = new Date();
        requestBuilder.set(JSBProtocolFields.CREATE_DATE, dateSimpleFormat.format(currentDate));
        requestBuilder.set(JSBProtocolFields.CREATE_TIME, timeSimpleFormat.format(currentDate));
        requestBuilder.set(JSBProtocolFields.BIZ_DATE, dateSimpleFormat.format(currentDate));
        requestBuilder.set(JSBProtocolFields.MSG_ID, UUID.randomUUID().toString().replace("-", ""));
        requestBuilder.set(JSBProtocolFields.PARTNER_ID, partnerId);
        requestBuilder.set(JSBProtocolFields.CHANNEL_NO, JSBProtocolFields.DEFAULT_CHANNEL_NO);
        requestBuilder.set(JSBProtocolFields.PUBLIC_KEY_CODE, JSBProtocolFields.DEFAULT_PUBLIC_KEY_CODE);
        requestBuilder.set(JSBProtocolFields.VERSION, JSBProtocolFields.DEFAULT_VERSION);
        requestBuilder.set(JSBProtocolFields.CHARSET, JSBProtocolFields.DEFAULT_CHARSET);
        return requestBuilder;
    }

    private static JSBRequestBuilder buildLybSystemParams() {
        JSBRequestBuilder requestBuilder = new JSBRequestBuilder();
        Date currentDate = new Date();
        requestBuilder.set(JSBProtocolFields.CREATE_DATE, dateSimpleFormat.format(currentDate));
        requestBuilder.set(JSBProtocolFields.CREATE_TIME, timeSimpleFormat.format(currentDate));
        requestBuilder.set(JSBProtocolFields.BIZ_DATE, dateSimpleFormat.format(currentDate));
        requestBuilder.set(JSBProtocolFields.MSG_ID, UUID.randomUUID().toString().replace("-", ""));
        requestBuilder.set(JSBProtocolFields.PARTNER_ID, lybPartnerId);
        requestBuilder.set(JSBProtocolFields.CHANNEL_NO, JSBProtocolFields.DEFAULT_CHANNEL_NO);
        requestBuilder.set(JSBProtocolFields.PUBLIC_KEY_CODE, JSBProtocolFields.DEFAULT_PUBLIC_KEY_CODE);
        requestBuilder.set(JSBProtocolFields.VERSION, JSBProtocolFields.DEFAULT_VERSION);
        requestBuilder.set(JSBProtocolFields.CHARSET, JSBProtocolFields.DEFAULT_CHARSET);
        return requestBuilder;
    }

    /**
     * 支付
     *
     * @throws Exception
     */
    private static void pay() throws Exception {
        //构建系统级请求参数
        JSBRequestBuilder requestBuilder = buildSystemParams();

        //构建应用级参数
        requestBuilder.set(JSBBusinessFields.OUT_TRADE_NO, outTradeNo);
        requestBuilder.set(JSBBusinessFields.AUTH_CODE, "131720719612134507");
        requestBuilder.set(JSBBusinessFields.TOTAL_FEE, "0.01");
        requestBuilder.set(JSBBusinessFields.TRADE_TYPE, JSBTradeTypeEnum.WECHAT.getType());
        requestBuilder.set(JSBBusinessFields.PRODUCT_INFO, "测试商品名");
        requestBuilder.set(JSBBusinessFields.DEVICE_NO, deviceNo);
        requestBuilder.set(JSBBusinessFields.MCH_IP, "*************");

        Date orderStartTime = new Date();
        Date orderExpireTime = DateUtil.offsetMinute(orderStartTime, 5);
        requestBuilder.set(JSBBusinessFields.TIME_START, orderTimeSimpleFormat.format(orderStartTime));
        requestBuilder.set(JSBBusinessFields.TIME_EXPIRE, orderTimeSimpleFormat.format(orderExpireTime));

        Map<String, Object> result = jsbClient.call(REQUEST_URL_BETA, METHOD_PAY, requestBuilder.build(), privateKey, jsbPublicKey);
        System.out.println("result=" + JsonUtil.objectToJsonString(result));
    }

    /**
     * 查询订单状态
     *
     * @throws Exception
     */
    private static void query() throws Exception {
        //构建系统级请求参数
        JSBRequestBuilder requestBuilder = buildSystemParams();

        //构建应用级参数
        requestBuilder.set(JSBBusinessFields.OUT_TRADE_NO, outTradeNo);
        requestBuilder.set(JSBBusinessFields.TRADE_TYPE, JSBTradeTypeEnum.WECHAT.getType());
        requestBuilder.set(JSBBusinessFields.DEVICE_NO, deviceNo);

        Map<String, Object> result = jsbClient.call(REQUEST_URL_BETA, METHOD_QUERY, requestBuilder.build(), privateKey, jsbPublicKey);
        System.out.println("result=" + JsonUtil.objectToJsonString(result));
    }

    /**
     * 订单撤销
     *
     * @throws Exception
     */
    private static void refund() throws Exception {
        //构建系统级请求参数
        JSBRequestBuilder requestBuilder = buildSystemParams();

        //构建应用级参数
        requestBuilder.set(JSBBusinessFields.OUT_TRADE_NO, outTradeNo);
        requestBuilder.set(JSBBusinessFields.OUT_REFUND_NO, "rf" + outTradeNo);
        requestBuilder.set(JSBBusinessFields.TRADE_TYPE, JSBTradeTypeEnum.WECHAT.getType());
        requestBuilder.set(JSBBusinessFields.REFUND_AMT, "0.01");
        requestBuilder.set(JSBBusinessFields.DEVICE_NO, deviceNo);
        requestBuilder.set(JSBBusinessFields.MCH_IP, "*************");

        Map<String, Object> result = jsbClient.call(REQUEST_URL_BETA, METHOD_REFUND, requestBuilder.build(), privateKey, jsbPublicKey);
        System.out.println("result=" + JsonUtil.objectToJsonString(result));
    }

    /**
     * 微信预下单
     *
     * @throws Exception
     */
    private static void wechatPrepay() throws Exception {
        //构建系统级请求参数
        JSBRequestBuilder requestBuilder = buildSystemParams();

        //构建应用级参数
        requestBuilder.set(JSBBusinessFields.OPEN_ID, wechatOpenId);
        requestBuilder.set(JSBBusinessFields.TOTAL_FEE, "0.01");
        requestBuilder.set(JSBBusinessFields.TRADE_TYPE, WECHAT_TRADE_TYPE_JSAPI);
        requestBuilder.set(JSBBusinessFields.BACK_URL, notifyUrl);
        requestBuilder.set(JSBBusinessFields.PRODUCT_INFO, "默认商品");
        requestBuilder.set(JSBBusinessFields.MCH_IP, "*************");
        requestBuilder.set(JSBBusinessFields.EXT_FIELD1, wechatSubAppId);
        //放入收钱吧订单号
        requestBuilder.set(JSBBusinessFields.EXT_FIELD2, outTradeNo);

        Map<String, Object> result = jsbClient.call(REQUEST_URL_BETA, METHOD_WECHAT_PREPAY, requestBuilder.build(), privateKey, jsbPublicKey);
        System.out.println("result=" + JsonUtil.objectToJsonString(result));
    }

    /**
     * 支付宝预下单
     *
     * @throws Exception
     */
    private static void alipayPrepay() throws Exception {
        //构建系统级请求参数
        JSBRequestBuilder requestBuilder = buildSystemParams();

        //构建应用级参数
        requestBuilder.set(JSBBusinessFields.OUT_TRADE_NO, outTradeNo);
        requestBuilder.set(JSBBusinessFields.AMOUNT, "0.01");
        requestBuilder.set(JSBBusinessFields.USER_ID, alipayUserId);
        requestBuilder.set(JSBBusinessFields.APPID, alipayAppId);
        requestBuilder.set(JSBBusinessFields.BACK_URL, notifyUrl);
        requestBuilder.set(JSBBusinessFields.ORDER_DESC, "默认商品");

        Map<String, Object> result = jsbClient.call(REQUEST_URL_BETA, METHOD_ALIPAY_PREPAY, requestBuilder.build(), privateKey, jsbPublicKey);
        System.out.println("result=" + JsonUtil.objectToJsonString(result));
    }

    /**
     * 用户信息查询 - 使用新的 queryLybUserInfo 方法
     *
     * @throws Exception
     */
    private static void queryLybUserInfoWithNewMethod() throws Exception {
        try {
            //构建系统级请求参数
            JSBRequestBuilder requestBuilder = buildLybSystemParams();
            //构建应用级参数
            requestBuilder.set("partnerBuyerId", "test001");

            Map<String, Object> result = jsbClient.call(REQUEST_URL_BETA, "queryLybUserInfo", requestBuilder.build(), lybPrivateKey, lybPublicKey);
            System.out.println("result=" + JsonUtil.objectToJsonString(result));

        } catch (Exception e) {
            System.err.println("用户信息查询异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 用户信息查询 - 使用新的 queryLybUserInfo 方法
     *
     * @throws Exception
     */
    private static void lybOrderQuery() throws Exception {
        try {
            //构建系统级请求参数
            JSBRequestBuilder requestBuilder = buildLybSystemParams();
            //构建应用级参数
            requestBuilder.set("outOrderNo", "testorder001");

            Map<String, Object> result = jsbClient.call(REQUEST_URL_BETA, "lybOrderQuery", requestBuilder.build(), lybPrivateKey, lybPublicKey);
            System.out.println("result=" + JsonUtil.objectToJsonString(result));

        } catch (Exception e) {
            System.err.println("用户信息查询异常: " + e.getMessage());
            e.printStackTrace();
        }
    }



    public static void main(String[] args) throws Exception {
        try {
            lybOrderQuery();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
