package com.wosai.mpay.api.jsbank;


import com.google.common.annotations.VisibleForTesting;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.WebUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.wosai.mpay.api.jsbank.JSBBusinessFields.EXTEND_MAP;
import static com.wosai.mpay.api.jsbank.JSBBusinessFields.RECORD_LIST;
import static com.wosai.mpay.api.jsbank.JSBConstants.*;
import static com.wosai.mpay.api.jsbank.JSBProtocolFields.*;

/**
 * <AUTHOR>
 * @description 江苏银行
 * @date 2024/10/28
 */
public class JSBClient {
    public static final Logger log = LoggerFactory.getLogger(JSBClient.class);

    private int connectTimeout = 10000;
    private int readTimeout = 30000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    //响应结果解析表达式
    protected static final String RESPONSE_REGEX = "(.*)&signType=RSA&sign=([^&]+)(.*)";


    /**
     * 请求
     *
     * @param requestUrl      请求地址
     * @param method          请求方法名
     * @param params          请求参数
     * @param sqbPrivateKey   收钱吧私钥
     * @param jsbPublicKey    江苏银行公钥
     * @return
     * @throws MpayException
     * @throws MpayApiNetworkError
     */
    public Map<String, Object> call(String requestUrl, String method, Map<String, Object> params, String sqbPrivateKey, String jsbPublicKey)
            throws MpayException, MpayApiNetworkError {

        params.put(METHOD, method);
        System.out.println(JsonUtil.objectToJsonString(params));
        //转换为请求字符串
        String requestStr = convert2RequestStr(params);
        //生成签名
        String signValue = RsaSignature.sign(requestStr, DEFAULT_SIGN_ALGORITHM, sqbPrivateKey);
        requestStr = requestStr + "&sign=" + signValue + "&signType=" + DEFAULT_SIGN_METHOD;

        log.info("request {}", JsonUtil.objectToJsonString(params));

        String response = WebUtils.doPost(null, null, requestUrl, DEFAULT_CONTENT_TYPE, requestStr.getBytes(), connectTimeout, readTimeout);
        System.out.println(response);
        log.info("response {}", response);
        return parseAndVerifyResponse(response, jsbPublicKey);
    }

    /**
     * 转换为请求字符串
     *
     * @param params
     * @return
     */
    private String convert2RequestStr(Map<String, Object> params) {
        TreeMap<String, Object> requestMap = new TreeMap<>();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (entry.getValue() == null) {
                //移除值为null的数据
                continue;
            }
            requestMap.put(entry.getKey(), entry.getValue());
        }

        StringBuilder stringBuilder = new StringBuilder(1024);
        for (Map.Entry<String, Object> entry : requestMap.entrySet()) {
            stringBuilder.append(CONNECTOR).append(entry.getKey()).append(ASSIGNMENT).append(entry.getValue());
        }
        return stringBuilder.substring(1);
    }

    /**
     * 解析并验签响应结果
     *
     * @param response
     * @param publicKey
     * @return
     * @throws MpayException
     */
    private Map<String, Object> parseAndVerifyResponse(String response, String publicKey) throws MpayException {
        Pattern pattern = Pattern.compile(RESPONSE_REGEX);
        Matcher matcher = pattern.matcher(response);
        if (!matcher.find()) {
            log.error("响应数据验格式未包含签名字段, response={}", response);
            throw new MpayException("响应数据验签失败: " + response);
        }
        
        String content = matcher.group(1);
        String signValue = matcher.group(2);
        String listData = matcher.group(3);

        String fullContent = StringUtils.isEmpty(listData)
                ? content
                : content + listData;

        //验签
        checkSign(fullContent, signValue, publicKey);

        //解析响应结果
        return parseResponseContent(fullContent);
    }

    @VisibleForTesting
    protected Map<String, Object> parseResponseContent(String responseContent) {
        if (!responseContent.contains(MUL_CONNECTOR)) {
            //单条记录，直接解析并返回
            return convertString2Map(responseContent);
        }

        //多条记录, 需解析成list
        String [] recordsArray = responseContent.split(MUL_CONNECTOR);
        Map<String, Object> resultMap = convertString2Map(recordsArray[0]);
        if (recordsArray.length == 1) {
            return resultMap;
        }

        List<Map<String, Object>> list = new ArrayList<>();
        for (int i = 1; i < recordsArray.length; i++) {
            Map<String, Object> record = convertString2Map(recordsArray[i]);
            if (record.isEmpty()) {
                continue;
            }
            list.add(record);
        }
        resultMap.put(RECORD_LIST, list);
        return resultMap;
    }

    private Map<String, Object> convertString2Map(String response) {
        String[] arrayParams = response.split(CONNECTOR);
        if (arrayParams.length == 0) {
            return Collections.emptyMap();
        }

        Map<String, Object> resultMap = new HashMap<>(arrayParams.length);
        for(String param : arrayParams) {
            if (StringUtils.isEmpty(param)) {
                continue;
            }

            if (param.startsWith(EXTEND_MAP)) {
                //解析扩展参数
                Map<String, Object> value = parseExtendMap(param);
                if (null != value) {
                    resultMap.put(EXTEND_MAP, value);
                }
                continue;
            }

            int assignmentIndex = param.indexOf(ASSIGNMENT);
            if (assignmentIndex < 0) {
                continue;
            }

            String key = param.substring(0, assignmentIndex);
            String value = param.substring(assignmentIndex + 1);
            if (value.isEmpty()) {
                continue;
            }
            resultMap.put(key, value);
        }
        return resultMap;
    }

    private Map<String, Object> parseExtendMap(String param) {
        String[] value = param.split(ASSIGNMENT, 2);
        if (value.length != 2) {
            log.info("extendMap无效: {}", param);
            return null;
        }

        String extendMapStr = value[1];
        // 去掉字符串两端的花括号
        String input = extendMapStr.substring(1, extendMapStr.length() - 1);
        // 使用逗号分隔键值对
        String[] keyValuePairs = input.split(",");

        Map<String, Object> resultMap = new HashMap<>();
        for (String pair : keyValuePairs) {
            // 使用等号分隔键和值
            String[] entry = pair.split(ASSIGNMENT);
            if (entry.length == 2) {
                resultMap.put(entry[0].trim(), entry[1].trim());
            }
        }
        return resultMap;
    }

    /**
     * 检查签名
     *
     * @param content
     * @param publicKey
     * @throws MpayException
     */
    private void checkSign(String content, String signValue, String publicKey) throws MpayException {
        if (!RsaSignature.validateSign(content.getBytes(), signValue, DEFAULT_SIGN_ALGORITHM, publicKey)) {
            log.warn("响应数据验签失败, content={}", content);
            throw new MpayException("响应数据验签失败: " + content);
        }
    }

    /**
     * 用户信息查询
     * 根据会员id查询用户信息
     *
     * @param requestUrl      请求地址
     * @param partnerBuyerId  会员id，用户在商户端的唯一标志，可以是ID或用户号等
     * @param extFld1         拓展字段1（可选）
     * @param extFld2         拓展字段2（可选）
     * @param extFld3         拓展字段3（可选）
     * @param sqbPrivateKey   收钱吧私钥
     * @param jsbPublicKey    江苏银行公钥
     * @return                用户信息查询结果
     * @throws MpayException
     * @throws MpayApiNetworkError
     */
    public Map<String, Object> queryLybUserInfo(String requestUrl, String partnerBuyerId, String extFld1, String extFld2,
                                               String extFld3, String sqbPrivateKey, String jsbPublicKey)
            throws MpayException, MpayApiNetworkError {

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();

        // 添加必填参数
        params.put(JSBBusinessFields.PARTNER_BUYER_ID, partnerBuyerId);

        // 添加可选参数（如果有值）
        if (StringUtils.isNotEmpty(extFld1)) {
            params.put(JSBBusinessFields.EXT_FLD1, extFld1);
        }
        if (StringUtils.isNotEmpty(extFld2)) {
            params.put(JSBBusinessFields.EXT_FLD2, extFld2);
        }
        if (StringUtils.isNotEmpty(extFld3)) {
            params.put(JSBBusinessFields.EXT_FLD3, extFld3);
        }

        // 调用通用请求方法
        return call(requestUrl, JSBConstants.METHOD_QUERY_LYB_USER_INFO, params, sqbPrivateKey, jsbPublicKey);
    }
}
