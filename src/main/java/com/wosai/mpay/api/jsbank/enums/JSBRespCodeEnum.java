package com.wosai.mpay.api.jsbank.enums;


/**
 * <AUTHOR>
 * @description 江苏银行-公共返回报文-响应码
 * @date 2024/11/4
 */
public enum JSBRespCodeEnum {
    SUCCESS("000000", "成功"),
    INVALID_PARAM("027083", "入参无效"),
    INVALID_PARAM_WECHAT("027084", "微信号参数长度非法"),
    CLIENT_SN_CONFLICT("027089", "外部订单号已存在"),
    ORDER_NOT_EXIST("028007", "订单不存在"),
    ;

    private final String code;
    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    JSBRespCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static JSBRespCodeEnum of(String code) {
        if (null == code) {
            return null;
        }
        for (JSBRespCodeEnum e : JSBRespCodeEnum.values()) {
            if (code.equals(e.code)) {
                return e;
            }
        }
        return null;
    }
}


