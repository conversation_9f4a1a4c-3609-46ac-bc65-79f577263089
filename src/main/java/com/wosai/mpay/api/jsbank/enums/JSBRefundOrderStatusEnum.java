package com.wosai.mpay.api.jsbank.enums;


/**
 * <AUTHOR>
 * @description 江苏银行-退款订单状态
 * @date 2024/11/4
 */
public enum JSBRefundOrderStatusEnum {
    SUCCESS("1", "成功"),
    FAILED("2", "失败"),
    PROCESSING("3","退款处理中"),
    CLOSED("4","交易关闭");

    private final String status;
    private final String desc;

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    JSBRefundOrderStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static JSBRefundOrderStatusEnum of(String status) {
        if (null == status) {
            return PROCESSING;
        }
        for (JSBRefundOrderStatusEnum e : JSBRefundOrderStatusEnum.values()) {
            if (e.status.equals(status)) {
                return e;
            }
        }
        return PROCESSING;
    }
}


