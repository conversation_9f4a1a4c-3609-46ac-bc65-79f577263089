package com.wosai.mpay.api.uqpay;

import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.RsaSignature;
import org.apache.commons.lang.ArrayUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/12/25.
 */
public class UqpayUtil {

    /**
     * 构建表单数据，用于签名、请求
     * @param request
     * @param urlEncode
     * @param ignoreKeys
     * @return
     */
    public static String buildFormData(Map<String, Object> request, boolean urlEncode, String... ignoreKeys) {
        List<String> keys = new ArrayList<>(request.keySet()).parallelStream().filter(s -> ArrayUtils.indexOf(ignoreKeys, s) < 0).sorted().collect(Collectors.toList());
        StringBuilder queryString = new StringBuilder();
        try {
            for (int i = 0; i < keys.size(); i++) {
                String key = keys.get(i);
                Object v = request.get(key);
                if(v == null){
                    v = "";
                }
                String value = urlEncode ? URLEncoder.encode(v.toString(), "UTF-8") : v.toString();
                if (i == keys.size() - 1) {
                    queryString.append(key).append("=").append(value);
                } else {
                    queryString.append(key).append("=").append(value).append("&");
                }
            }
        } catch (UnsupportedEncodingException ignore) {

        }
        return queryString.toString();
    }

    /**
     * 验签
     * @param request
     * @param sign
     * @param publicKey
     * @return
     * @throws MpayException
     */
    public static boolean verifySign(Map<String, Object> request, String sign, String publicKey) throws MpayException {
        return RsaSignature.validateSign(UqpayUtil.buildFormData(request,false, ProtocolFields.SIGN_TYPE, ProtocolFields.SIGN), sign, RsaSignature.SIG_ALG_NAME_SHA1_With_RSA, publicKey);
    }
}
