package com.wosai.mpay.api.uqpay;

import com.wosai.mpay.exception.HttpFailException;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.RsaSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

public class UqpayClient {

    public static final Logger logger = LoggerFactory.getLogger(UqpayClient.class);
    public static final String FORM_CONTENT_TYPE = "application/x-www-form-urlencoded";

    private int connectTimeout = 500;
    private int readTimeout = 5000;

    public UqpayClient() {
    }

    public UqpayClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    /**
     * 调用接口, 返回原始字符串
     * 用于某些对方某些接口不是返回json格式的情况, 比如信用卡支付, 返回的是html报文.
     * @param url
     * @param key
     * @param request
     * @return
     * @throws Exception
     */
    public String callV0(String url, String key, Map<String,Object> request) throws Exception {
        String formData = UqpayUtil.buildFormData(request, false, ProtocolFields.SIGN, ProtocolFields.SIGN_TYPE);
        String sign = RsaSignature.sign(formData, RsaSignature.SIG_ALG_NAME_SHA1_With_RSA, key);
        request.put(ProtocolFields.SIGN, sign);
        request.put(ProtocolFields.SIGN_TYPE, UqpayConstant.SIGN_TYPE_RSA);
        formData = UqpayUtil.buildFormData(request, true);
        logger.info("request {}", formData);
//        printCurl(url, new HashMap<>(), formData);
        String responseString = null;
        try{
            responseString = HttpClientUtils.doPost(UqpayClient.class.getName(), null, null, url, FORM_CONTENT_TYPE, formData,  UqpayConstant.CHARSET_UTF8, connectTimeout, readTimeout);
        }catch (HttpFailException e){
            responseString = e.getBody();
        }
        logger.info("response {}",  responseString);
        System.out.println(responseString);
        return responseString;
    }

    /**
     * 调用接口, 返回 Map
     * @param url
     * @param key
     * @param request
     * @return
     * @throws Exception
     */
    public Map<String,Object> call(String url, String key, Map<String,Object> request) throws Exception {
        return JsonUtil.jsonStringToObject(callV0(url, key, request), Map.class);
    }


    public void printCurl(String url, Map<String,String> header, String request) {
        header = new HashMap<>(header);
        header.put("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
        StringBuilder curlCommand = new StringBuilder("curl -v -X POST ");

        // 添加URL
        curlCommand.append(url).append(" \\\n");

        // 添加Header
        if (header != null) {
            for (Map.Entry<String, String> entry : header.entrySet()) {
                curlCommand.append("  -H '").append(entry.getKey()).append(": ").append(entry.getValue()).append("' \\\n");
            }
        }

        // 添加请求体
        if (request != null) {
            curlCommand.append("  -d '").append(request).append("'");
        }

        System.out.println(curlCommand.toString());
    }


    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
}
