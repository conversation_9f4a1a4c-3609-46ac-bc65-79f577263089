package com.wosai.mpay.api.uqpay;

import com.google.zxing.qrcode.encoder.QRCode;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/12/26.
 */
public class ResponseFields {
    // Merchant id, the unique identification of merchant in the UQPAY system
    public static final String MERCHANT_ID = "merchantid";

    // Partner ID, the unique identification of partner in the UQPAY system. Presented if merchant belongs to a partner
    public static final String AGENT_ID = "agentid";

    // Transaction type, see Appendix 1 for details
    public static final String TRANSACTION_TYPE = "transtype";

    // The unique order id in merchant system
    public static final String ORDER_ID = "orderid";

    // The order id provide by UQPAY
    public static final String UQ_ORDER_ID = "uqorderid";

    // Payment method id, see Appendix 2 for details
    public static final String METHOD_ID = "methodid";

    // 0 for the merchant scans QR code, 1 for the acquisition of the QR code
    public static final String SCAN_TYPE = "scantype";

    // When scantype is 1, the QR code display address is stored
    public static final String QR_CODE_URL = "qrcodeurl";

    // When scantype is 1, the actual value of the QR code is stored
    public static final String QR_CODE = "qrcode";

    // Order amount
    public static final String AMOUNT = "amount";

    // Order currency
    public static final String CURRENCY = "currency";

    // Result code, see Appendix 4 for details
    public static final String CODE = "code";

    // Results description
    public static final String MESSAGE = "message";

    // Order status, see Appendix 3 for details
    public static final String STATE = "state";

    // JSON format info, additional information return from channel, see Appendix 5 for details
    public static final String CHANNEL_INFO = "channelinfo";


    /**
     * 5. Data in “channelinfo”
     *     NAME	Type	DESCRIPTION
     *     sub_openid	Request	Wechat open id
     */
    public static final String CHANNEL_INFO_SUB_OPENID = "sub_openid";

    /**
     * 5. Data in “channelinfo”
     *     NAME	Type	DESCRIPTION
     *     sub_appid	Request	Wechat mini-program/Official account APPID
     */
    public static final String CHANNEL_INFO_SUB_APPID = "sub_appid";

    /**
     * 5. Data in “channelinfo”
     *     NAME	Type	DESCRIPTION
     *     discount	Response	The amount of discount.
     */
    public static final String CHANNEL_INFO_DISCOUNT = "discount";

    /**
     * 5. Data in “channelinfo”
     *     NAME	Type	DESCRIPTION
     *     costAmount	Response	The cost amount after the discount, indicating the amount the Cardholder actually pays for the transaction when a promotion is involved.
     */
    public static final String CHANNEL_INFO_COST_AMOUNT = "costAmount";

    /**
     * 5. Data in “channelinfo”
     *     NAME	Type	DESCRIPTION
     *     QRCType	Response	QRCode Type, Static/Dynamic/Unknown. Presented If performance an EMVCo standard merchant-presented QRC transaction
     */
    public static final String CHANNEL_INFO_QRC_TYPE = "QRCType";

    /**
     * 5. Data in “channelinfo”
     *     NAME	Type	DESCRIPTION
     *     SGQRID	Response	The identity in SQQR Code. Presented if performance a SQQR type QRC transaction.
     */
    public static final String CHANNEL_INFO_SGQR_ID = "SGQRID";

    /**
     * 5. Data in “channelinfo”
     *     NAME	Type	DESCRIPTION
     *     pspName	Response	The name of the actual Alipay+ Mobile Payment Partner.
     */
    public static final String CHANNEL_INFO_PSP_NAME = "pspName";


    // JSON format info
    public static final String EXTEND_INFO = "extendinfo";

    // extendinfo terminalID
    public static final String EXTEND_INFO_TERMINAL_ID = "terminalID";

    // Store ID
    public static final String STORE_ID = "storeid";

    // Sales ID
    public static final String SELLER = "seller";

    // Time requested, use timestamp in milliseconds
    public static final String DATE = "date";

    // Signature Method, RSA
    public static final String SIGN_TYPE = "signtype";

    // Signature (See Signature for reference)
    public static final String SIGN = "sign";

    // Accept code from UnionPay for payment
    public static final String ACCEPT_CODE = "acceptcode";

    // Amount will be settled
    public static final String BILL_AMOUNT = "billamount";

    // Discount detail, JSON list, e.g. [{"amount":"5.0","currency":"SGD","note":"Instant Discount","settle":true}]
    public static final String DISCOUNT_INFO = "discountinfo";

    // Fields within discountinfo
    public static final String DISCOUNT_AMOUNT = "amount";
    public static final String DISCOUNT_CURRENCY = "currency";
    public static final String DISCOUNT_NOTE = "note";
    public static final String DISCOUNT_SETTLE = "settle";


    //finishtime	string	yes	订单完成时间，使用毫秒精度的时间戳
    public static final String FINISH_TIME = "finishtime";

    // Data in “channelinfo”
    // QRCode Type, Static/Dynamic/Unknown. Presented If performance an EMVCo standard merchant-presented QRC transaction
    public static final String QRC_TYPE = "QRCType";

    // The identity in SQQR Code. Presented if performance a SQQR type QRC transaction.
    public static final String SGQR_ID = "SGQRID";

    // The name of the actual Alipay+ Mobile Payment Partner.
    public static final String PSP_NAME = "pspName";
}
