package com.wosai.mpay.api.uqpay;

import com.wosai.mpay.util.JsonUtil;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Created by w<PERSON><PERSON><PERSON><PERSON>
 */
public class RequestBuilder {
    private Map<String, Object> request = new LinkedHashMap<>();

    public RequestBuilder() {

    }

    public void set(String field, Object value) {
        request.put(field, value);
    }

    public Map<String, Object> build() {
        Map<String, Object> result = new HashMap<>();
        for(String key: request.keySet()){
            Object value = request.get(key);
            if(value instanceof Map){
                result.put(key, JsonUtil.toJsonStr(value));
            }else{
                result.put(key, value);
            }
        }
        return result;
    }
}
