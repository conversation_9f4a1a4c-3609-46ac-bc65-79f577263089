package com.wosai.mpay.api.uqpay;

/**
 * Pay Method Supported By UQPAY
 * version 1.0.0
 * author <EMAIL>
 */
public final class MethodIdConstant {
  public static final int UnionPayOnlineQR = 1001;
  public static final int AlipayOfflineQR = 1002;
  public static final int WeChatOfflineQR = 1003;
  public static final int UnionPayOfflineQR = 1004;
  public static final int GradOfflineQR = 1005;
  public static final int GradOnlineQR = 1006;
  public static final int DIGICCY = 1008;
  public static final int AlipayStaticQR = 1009;
  public static final int AlipayOnlineQR = 1010;
  public static final int WeChatOnlineQR = 1011;
  public static final int PayNowOfflineQR = 1012;
  public static final int PayNowOnlineQR = 1013;
  public static final int RazerPay_OfflineQR = 1014;
  public static final int DASH_OfflineOR = 1016;
  public static final int DASH_OnlineOR = 1017;
  public static final int ShopeeOfflineQR = 1018;
  public static final int ShopeeOnlineQR = 1019;
  public static final int ShopeeStaticQR = 1020;
  public static final int XNAP = 1021;
  public static final int ThaiQR = 1022;
  public static final int TNGOfflineQR = 1023;
  public static final int KAKAOPAYOfflineQR = 1024;
  public static final int PayLahOfflineQR = 1025;
  public static final int UnionSecurePay = 1100;
  public static final int UnionPayMerchantHost = 1101;
  public static final int WechatOfficialAccount = 1102;
  public static final int GatewayPayment = 1105;
  public static final int DANAOnline = 1106;
  public static final int GCASHOnline = 1107;
  public static final int KAKAOPAYOnline = 1108;
  public static final int TNGOnline = 1109;
  public static final int TrueMoneyOnline = 1110;
  public static final int AlipayHKOnline = 1111;
  public static final int WechatMiniProgram = 1401;
  public static final int UnionPayServerHost = 1103;
  public static final int GrabOTC = 1104;
  public static final int VISA = 1200;
  public static final int VISA3D = 2500;
  public static final int Master = 1201;
  public static final int Master3D = 2501;
  public static final int UnionPayExpressPay = 1202;
  public static final int AMEX = 1203;
  public static final int JCB = 1204;
  public static final int PayPal = 1300;
  public static final int AlipayWebOnline = 1301;
  public static final int FreeCharge = 1302;
  public static final int ShopeeOnline = 1303;
  public static final int AlipayWap = 1501;
  public static final int WechatH5 = 1502;
  public static final int AuthQuickPay = 1600;
  public static final int Wechat_InAPP = 2000;
  public static final int UnionPay_InAPP = 2001;
  public static final int Alipay_InAPP = 2002;
  public static final int Shopee_InAPP = 2003;
  public static final int AlipayPlus_InAPP = 2004;
  public static final int ApplePay = 3000;
  public static final int QuickPay = 3001;
  public static final int BTC_Online = 5000;
  public static final int BTC_Offline = 5001;
  public static final int BCH_Online = 5002;
  public static final int BCH_Offline = 5003;
  public static final int ETH_Online = 5004;
  public static final int ETH_Offline = 5005;
  public static final int USDT_Online = 5006;
  public static final int USDT_Offline = 5007;
  public static final int USDC_Online = 5008;
  public static final int USDC_Offline = 5009;
  public static final int REAL_NAME_PAYMENT = 11000;//realname payment
}
