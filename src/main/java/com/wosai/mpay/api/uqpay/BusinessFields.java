package com.wosai.mpay.api.uqpay;

public class BusinessFields {
    // Merchant id, the unique identification of merchant in the UQPAY system
    public static final String MERCHANT_ID = "merchantid";

    // Partner ID, the unique identification of partner in the UQPAY system. Presented if merchant belongs to a partner
    public static final String AGENT_ID = "agentid";

    // Transaction type, see Appendix 1 for details
    public static final String TRANSACTION_TYPE = "transtype";

    // The merchant order id is unique at the merchant’s system
    public static final String ORDER_ID = "orderid";

    // Payment method id, see Appendix 2 for details
    public static final String METHOD_ID = "methodid";

    // 0 for the merchant scans QR code, 1 for the acquisition of the QR code
    public static final String SCAN_TYPE = "scantype";

    // Consumer identity obtained by scanning code, cannot be null when scanning QR code
    public static final String IDENTITY = "identity";

    // Order amount
    public static final String AMOUNT = "amount";

    // Order currency
    public static final String CURRENCY = "currency";

    // Product information
    public static final String TRANSACTION_NAME = "transname";

    // Number of purchases
    public static final String QUANTITY = "quantity";

    // Merchant City
    public static final String MERCHANT_CITY = "merchantcity";

    // Terminal Identity, less then 8
    public static final String TERMINAL_ID = "terminalid";

    // Store ID
    public static final String STORE_ID = "storeid";

    // Sales ID
    public static final String SELLER = "seller";

    // Interface address to receive payment result notification
    public static final String CALLBACK_URL = "callbackurl";

    // JSON format info, will returned when callback
    public static final String EXTEND_INFO = "extendinfo";

    // Time requested, use timestamp in milliseconds
    public static final String DATE = "date";

    // Client Type, 1 for web, 2 for iOS, 3 for Android
    public static final String CLIENT_TYPE = "clienttype";

    // Client IP
    public static final String CLIENT_IP = "clientip";

    // Signature Method, RSA
    public static final String SIGN_TYPE = "signtype";

    // Signature (See Signature for reference)
    public static final String SIGN = "sign";

    // Bank card number, encrypted by uqpay rsa public key.(valid for securepay only)
    public static final String CARD_NUM = "cardnum";

    // First Name of the cardholder
    public static final String FIRST_NAME = "firstname";

    // Last Name of the cardholder
    public static final String LAST_NAME = "lastname";

    // cvv
    public static final String CVV = "cvv";

    // Card expire year
    public static final String EXPIRE_YEAR = "expireyear";

    // Card expire month
    public static final String EXPIRE_MONTH = "expiremonth";

    // Billing address country,ISO 3166-1 two digital code
    public static final String ADDRESS_COUNTRY = "addresscountry";

    // Billing address state or province
    public static final String ADDRESS_STATE = "addressstate";

    // Billing address city
    public static final String ADDRESS_CITY = "addresscity";

    // Billing address, Split with ‘,’ for multi-lines
    public static final String ADDRESS = "address";

    // Billing address postcode
    public static final String ZIP = "zip";

    // Card Holder’s email
    public static final String EMAIL = "email";

    // address to receive payment redirection
    public static final String RETURN_URL = "returnurl";

    // Extent JSON format info for special paymethod see Appendix 5 for details
    public static final String CHANNEL_INFO = "channelinfo";

    // The original pay order id provide by UQPAY
    public static final String UQ_ORDER_ID = "uqorderid";

    //sub_openid	Request	Wechat open id
    public static final String SUB_OPENID = "sub_openid";

    //sub_appid	Request	Wechat mini-program/Official account APPID
    public static final String SUB_APPID = "sub_appid";

}
