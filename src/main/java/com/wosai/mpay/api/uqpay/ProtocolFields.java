package com.wosai.mpay.api.uqpay;

public class ProtocolFields {
    // Merchant ID, the unique identification of merchant in the UQPAY system
    public static final String MERCHANT_ID = "merchantid";

    // Partner ID, the unique identification of partner in the UQPAY system. Presented if merchant belongs to a partner
    public static final String AGENT_ID = "agentid";

    // Transaction type, see Appendix 1 for details
    public static final String TRANSACTION_TYPE = "transtype";

    // Time requested, use timestamp in milliseconds
    public static final String DATE = "date";

    // Client Type, 1 for web, 2 for iOS, 3 for Android
    public static final String CLIENT_TYPE = "clienttype";

    // Signature Method, RSA
    public static final String SIGN_TYPE = "signtype";

    // Signature (See Signature and Parameters for reference)
    public static final String SIGN = "sign";

    // Signature (See Signature and Parameters for reference) json请求使用
    public static final String SIGNATURE = "signature";
}
