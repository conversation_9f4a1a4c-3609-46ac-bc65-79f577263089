package com.wosai.mpay.api.uqpay;

import java.util.Arrays;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/12/24.
 */
public class UqpayConstant {

    public static final String CHARSET_UTF8 = "UTF-8";

    // currency sgd
    public static final String CURRENCY_SGD = "SGD";

    // sign type rsa
    public static final String SIGN_TYPE_RSA = "RSA";

    public static final String SIGNATURE_PLACEHOLDER = "000000";

    // Client Type, web 1, iOS 2, Android 3
    public static final int CLIENT_TYPE_WEB = 1;
    public static final int CLIENT_TYPE_IOS = 2;
    public static final int CLIENT_TYPE_ANDROID = 3;

    // Transaction Type
    public static final String TRANSACTION_TYPE_PAY = "pay";
    public static final String TRANSACTION_TYPE_CANCEL = "cancel";
    public static final String TRANSACTION_TYPE_REFUND = "refund";
    public static final String TRANSACTION_TYPE_PREAUTH = "preauth";
    public static final String TRANSACTION_TYPE_PREAUTH_COMPLETE = "preauthcomplete";
    public static final String TRANSACTION_TYPE_PREAUTH_CANCEL = "preauthcancel";
    public static final String TRANSACTION_TYPE_PREAUTH_CC = "preauthcc";
    public static final String TRANSACTION_TYPE_VERIFY_CODE = "verifycode";
    public static final String TRANSACTION_TYPE_ENROLL = "enroll";
    public static final String TRANSACTION_TYPE_QUERY = "query";

    // UQ_ORDER_ID 0 UQPAY 订单号,如果使用商户订单号查询，需要赋值为0
    public static final String UQ_ORDER_ID_ZERO = "0";

    // scan type merchant scan consumer
    public static final int SCAN_TYPE_MERCHANT = 0; //商户主扫
    public static final int SCAN_TYPE_CONSUMER = 1; //消费者主扫

    // 10000	TransSuccessed
    public static final String RESPONSE_CODE_SUCCESS = "10000";

    // Order Status
    public static final String ORDER_STATUS_READY = "Ready"; // 准备支付，订单创建完成,尚未支付或尚未得到支付结果
    public static final String ORDER_STATUS_PAYING = "Paying"; // 支付中，在多步订单中,比如 QRC, In-APP， 订单已经创建，并在等待下一步操作
    public static final String ORDER_STATUS_SUCCESS = "Success"; // 成功，支付成功
    public static final String ORDER_STATUS_FAILED = "Failed"; // 失败，支付失败
    public static final String ORDER_STATUS_SYNC_SUCCESS = "SyncSuccess"; // 同步成功，订单在交易成功，但是尚未收到最终状态通知
    public static final String ORDER_STATUS_SYNC_FAILED = "SyncFailed"; // 同步失败，订单在交易失败，但是尚未收到最终状态通知
    public static final String ORDER_STATUS_CLOSED = "Closed"; // 关闭，订单在创建后长时间没有后续操作

    // Response Codes
    public static final String RESPONSE_CODE_TRANS_PROCESSING = "10001"; // TransProcessing
    public static final String RESPONSE_CODE_TRANS_READY = "10002"; // TransReady

    public static final String RESPONSE_CODE_TRANS_MERCHANT_EMPTY = "11000"; // TransMerchantEmpty
    public static final String RESPONSE_CODE_TRANS_AGENT_EMPTY = "11002"; // TransAgentEmpty
    public static final String RESPONSE_CODE_TRANS_MERCHANT_ORDER_EMPTY = "11003"; // TransMerchantOrderEmpty
    public static final String RESPONSE_CODE_TRANS_VERIFY_CODE_EMPTY = "11004"; // TransVerifyCodeEmpty
    public static final String RESPONSE_CODE_TRANS_PHONE_NUMBER_EMPTY = "11005"; // TransPhoneNumberEmpty
    public static final String RESPONSE_CODE_TRANS_AMOUNT_EMPTY = "11006"; // TransAmountEmpty
    public static final String RESPONSE_CODE_TRANS_CURRENCY_EMPTY = "11007"; // TransCurrencyEmpty
    public static final String RESPONSE_CODE_TRANS_CARD_EMPTY = "11008"; // TransCardEmpty
    public static final String RESPONSE_CODE_TRANS_CVV2_EMPTY = "11009"; // TransCVV2Empty
    public static final String RESPONSE_CODE_TRANS_CARD_VALID_DATE_EMPTY = "11010"; // TransCardValidDateEmpty
    public static final String RESPONSE_CODE_TRANS_SIGNED_INFO_EMPTY = "11011"; // TransSignedInfoEmpty
    public static final String RESPONSE_CODE_TRANS_IDENTITY_EMPTY = "11012"; // TransIdentityEmpty
    public static final String RESPONSE_CODE_TRANS_BILL_ADDRESS_EMPTY = "11013"; // TransBillAddressEmpty
    public static final String RESPONSE_CODE_TRANS_TIME_FORMAT_ERROR = "11014"; // TransTimeFormatError
    public static final String RESPONSE_CODE_TRANS_CALL_BACK_URL_EMPTY = "11015"; // TransCallBackUrlEmpty
    public static final String RESPONSE_CODE_TRANS_RETURN_URL_EMPTY = "11016"; // TransReturnUrlEmpty
    public static final String RESPONSE_CODE_TRANS_SCAN_TYPE_EMPTY = "11017"; // TransScanTypeEmpty
    public static final String RESPONSE_CODE_TRANS_CLIENT_TYPE_EMPTY = "11018"; // TransClientTypeEmpty
    public static final String RESPONSE_CODE_TRANS_GOODS_INFO_EMPTY = "11019"; // TransGoodsInfoEmpty
    public static final String RESPONSE_CODE_TRANS_QUANTITY_ERROR = "11020"; // TransQuantityError
    public static final String RESPONSE_CODE_TRANS_STORE_ID_ERROR = "11021"; // TransStoreIdError
    public static final String RESPONSE_CODE_TRANS_SELLER_ERROR = "11022"; // TransSellerError
    public static final String RESPONSE_CODE_TRANS_CHANNEL_INFO_ERROR = "11023"; // TransChannelInfoError
    public static final String RESPONSE_CODE_TRANS_EXTEND_INFO_ERROR = "11024"; // TransExtendInfoError
    public static final String RESPONSE_CODE_TRANS_CARD_HOLDER_EMPTY = "11025"; // TransCardHolderEmpty
    public static final String RESPONSE_CODE_TRANS_CARD_BILLING_ADDRESS_EMPTY = "11026"; // TransCardBillingAddressEmpty
    public static final String RESPONSE_CODE_TRANS_CARD_EMAIL_EMPTY = "11027"; // TransCardEmailEmpty
    public static final String RESPONSE_CODE_TRANS_OPEN_ID_EMPTY = "11028"; // TransOpenIdEmpty
    public static final String RESPONSE_CODE_TRANS_CARD_TYPE_ERROR = "11029"; // TransCardTypeError
    public static final String RESPONSE_CODE_TRANS_UQPAY_ORDER_EMPTY = "11030"; // TransUqpayOrderEmpty
    public static final String RESPONSE_CODE_TRANS_CARD_TOKEN_EMPTY = "11031"; // TransCardTokenEmpty
    public static final String RESPONSE_CODE_TRANS_TERMINAL_ID_EMPTY = "11032"; // TransTerminalIDEmpty
    public static final String RESPONSE_CODE_TRANS_MERCHANT_CITY_EMPTY = "11034"; // TransMerchantCityEmpty
    public static final String RESPONSE_CODE_TRANS_CLIENT_IP_EMPTY = "11035"; // TransClientIpEmpty
    public static final String RESPONSE_CODE_TRANS_COUNTRY_EMPTY = "11036"; // TransCountryEmpty
    public static final String RESPONSE_CODE_TRANS_EXPIRE_DATE_EMPTY = "11037"; // TransExpireDateEmpty
    public static final String RESPONSE_CODE_TRANS_APPLE_PAY_DPAN_EMPTY = "11038"; // TransApplePayDpanEmpty
    public static final String RESPONSE_CODE_TRANS_APPLE_PAY_IC_CARD_DATA_EMPTY = "11039"; // TransApplePayICCardDataEmpty
    public static final String RESPONSE_CODE_TRANS_APPLE_PAY_CARDHOLDER_NAME_EMPTY = "11040"; // TransApplePayCardholderNameEmpty
    public static final String RESPONSE_CODE_TRANS_APPLE_PAY_PIN_EMPTY = "11041"; // TransApplePayPinEmpty
    public static final String RESPONSE_CODE_TRANS_THREE_D_PA_RES_EMPTY = "11042"; // TransThreeDPaResEmpty
    public static final String RESPONSE_CODE_TRANS_SIGN_TYPE_EMPTY = "11043"; // TransSignTypeEmpty
    public static final String RESPONSE_CODE_TRANS_QR_CODE_FORMAT_ERROR = "11044"; // TransQRCodeFormatError

    public static final String RESPONSE_CODE_TRANS_SIGNATURE_ERROR = "12000"; // TransSignatureError
    public static final String RESPONSE_CODE_TRANS_UNAUTHORIZED = "12001"; // TransUnauthorized
    public static final String RESPONSE_CODE_TRANS_ORDER_EXISTED = "12002"; // TransOrderExisted
    public static final String RESPONSE_CODE_TRANS_ORDER_NOT_EXIST = "12003"; // TransOrderNotExist
    public static final String RESPONSE_CODE_TRANS_ORDER_STATE_ERROR = "12004"; // TransOrderStateError
    public static final String RESPONSE_CODE_TRANS_AMOUNT_ERROR = "12005"; // TransAmountError
    public static final String RESPONSE_CODE_TRANS_CARD_INVALID = "12006"; // TransCardInvalid
    public static final String RESPONSE_CODE_TRANS_CURRENCY_NOT_SUPPORT = "12007"; // TransCurrencyNotSupport
    public static final String RESPONSE_CODE_TRANS_ORDER_MISS_MATCH = "12008"; // TransOrderMissMatch
    public static final String RESPONSE_CODE_TRANS_BALANCE_INSUFFICIENT = "12009"; // TransBalanceInsufficient
    public static final String RESPONSE_CODE_TRANS_CONFIG_ERROR = "12010"; // TransConfigError
    public static final String RESPONSE_CODE_TRANS_INVALID_PAY_IN = "12011"; // TransInvalidPayIn

    public static final String RESPONSE_CODE_TRANS_VERIFY_CODE_ERROR = "13045"; // TransVerifyCodeError
    public static final String RESPONSE_CODE_TRANS_VERIFY_CODE_EXPIRED = "13046"; // TransVerifyCodeExpired
    public static final String RESPONSE_CODE_TRANS_CHANNEL_TIME_OUT = "13047"; // TransChannelTimeOut
    public static final String RESPONSE_CODE_TRANS_CARD_BALANCE_INSUFFICIENT = "13048"; // TransCardBalanceInsufficient
    public static final String RESPONSE_CODE_TRANS_CARD_SAFETY_INFORMATION_ERROR = "13049"; // TransCardSafetyInformationError
    public static final String RESPONSE_CODE_TRANS_CARD_INFO_VERIFICATION_FAILURE = "13050"; // TransCardInfoVerificationFailure
    public static final String RESPONSE_CODE_TRANS_WRONG_TIME = "13051"; // TransWrongTime
    public static final String RESPONSE_CODE_TRANS_CARD_NOT_SUPPORT = "13052"; // TransCardNotSupport
    public static final String RESPONSE_CODE_TRANS_FORMAT_ERROR = "13033"; // TransFormatError
    public static final String RESPONSE_CODE_TRANS_CARD_BOUND = "13034"; // TransCardBound
    public static final String RESPONSE_CODE_TRANS_ISSUER_REFUSED = "13035"; // TransIssuerRefused
    public static final String RESPONSE_CODE_TRANS_CHANNEL_ERROR = "13036"; // TransChannelError
    public static final String RESPONSE_CODE_TRANS_MAY_FRAUD = "13037"; // TransMayFraud
    public static final String RESPONSE_CODE_TRANS_RESTRICTED_CARD = "13038"; // TransRestrictedCard

    public static final String RESPONSE_CODE_TRANS_PARAM_ERROR = "14000"; // TransParamError
    public static final String RESPONSE_CODE_TRANS_MCC_INVALID = "14001"; // TransMCCInvalid
    public static final String RESPONSE_CODE_TRANS_MERCHANT_NOT_EXIST = "14002"; // TransMerchantNotExist
    public static final String RESPONSE_CODE_TRANS_CUSTOMER_NOT_EXIST = "14003"; // TransCustomerNotExist
    public static final String RESPONSE_CODE_TRANS_TOKEN_EXPIRED = "14004"; // TransTokenExpired

    public static final String RESPONSE_CODE_TRANS_AMOUNT_EXCEED = "18001"; // TransAmountExceed
    public static final String RESPONSE_CODE_TRANS_CARD_AMOUNT_EXCEED = "18002"; // TransCardAmountExceed
    public static final String RESPONSE_CODE_TRANS_ILLEGAL_CARD = "18003"; // TransIllegalCard
    public static final String RESPONSE_CODE_TRANS_IP_BLOCKED = "18004"; // TransIPBlocked
    public static final String RESPONSE_CODE_TRANS_FREQUENCY_EXCEED = "18005"; // TransFrequencyExceed
    public static final String RESPONSE_CODE_TRANS_AMOUNT_MINI_LIMIT = "18006"; // TransAmountMiniLimit
    public static final String RESPONSE_CODE_TRANS_BLOCKED = "18007"; // TransBlocked

    public static final String RESPONSE_CODE_TRANS_NETWORK_ERROR = "19996"; // TransNetworkError
    public static final String RESPONSE_CODE_TRANS_RESULT_UNKNOW = "19997"; // TransResultUnknow
    public static final String RESPONSE_CODE_TRANS_RESULT_FAIL = "19998"; // TransResultFail
    public static final String RESPONSE_CODE_TRANS_INTERNAL_ERROR = "19999"; // TransInternalError



    // List of unknown status response codes
    public static final List<String> UNKNOWN_STATUS_RESPONSE_CODES = Arrays.asList(
            RESPONSE_CODE_TRANS_RESULT_UNKNOW,
            RESPONSE_CODE_TRANS_INTERNAL_ERROR
    );
}
