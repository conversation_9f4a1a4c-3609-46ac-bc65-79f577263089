package com.wosai.mpay.api.sodexo;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.pantheon.util.MapUtil;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Date: 2019-07-15 Time: 14:33
 */
public class SodexoTest {
    public static final ObjectMapper om = new ObjectMapper();
    private static final String token = "";
    private static final String DATA_TEMPLATE = "yyyyMMddHHmmss";

    public static void main(String[] args) throws Throwable {
//        pay();
      // query();
//        cancel();
//        refund();
// loadToken();
         //mobileopenidrequest();
        ExpenseTokenRequest();
       // ExpenseByToken();
    }

    public static void pay() throws Throwable {
        String gateway = "https://uat-mt.sdxpass.com/api/MealCardBarcodeExpense";

        Map<String, Object> request = buildBaseRequest();
        request.put("transType","MealExpense");


        SodexoClient client = new SodexoClient();
        Map<String, Object> result = client.call(gateway, token, request);
        System.out.println(om.writeValueAsString(result));
    }

    public static void query() throws Throwable {
        String gateway = "https://uat-mt.sdxpass.com/api/TransQuery";

        Map<String, Object> request = buildBaseRequest();
        request.put("transType","TransQuery");
        request.put("clientTraceNo", "1659350904196");

        SodexoClient client = new SodexoClient();
        Map<String, Object> result = client.call(gateway, token, request);
        System.out.println(om.writeValueAsString(result));
    }

    public static void cancel() throws Throwable {
        String gateway = "https://uat-mt.sdxpass.com/api/MealCardPaymentCancel";

        Map<String, Object> request = buildBaseRequest();
        request.put("transType","MealVoid");
        request.put("clientTraceOrgNo", "1563357545151");

        SodexoClient client = new SodexoClient();
        Map<String, Object> result = client.call(gateway, token, request);
        System.out.println(om.writeValueAsString(result));
    }

    public static void refund() throws Throwable {
        String gateway = "https://uat-mt.sdxpass.com/api/MealCardPaymentRefund";

        Map<String, Object> request = buildBaseRequest();
        request.put("transType","MealRefund");
        request.put("clientTraceOrgNo", "1563358557016");

        SodexoClient client = new SodexoClient();
        Map<String, Object> result = client.call(gateway, token, request);
        System.out.println(om.writeValueAsString(result));

    }

    public static void loadToken() throws Throwable {
        String gateway = "https://uat-mt.sdxpass.com:8001/OAuth/Token";
        Map<String, Object> request = new HashMap<String, Object>();
        request.put("client_id", "sdxtestuser111");
        request.put("client_secret", "240dc520c7c84bb4");
        request.put("grant_type", "client_credentials");

        SodexoClient client = new SodexoClient();
        Map<String, Object> result = client.queryTokenCall(gateway, request);
        System.out.println(om.writeValueAsString(result));

    }

    /**
     * 用户唯一标识申请
     * 
     * @throws Throwable
     */
    public static void mobileopenidrequest() throws Throwable {
        String gateway = "https://uat-mt.sdxpass.com/api/" + SodexoConstants.OPEN_ID_REQUEST_TRANS_TYPE;

        Map<String, Object> request = buildBaseRequest();
        request.put("transType", SodexoConstants.OPEN_ID_REQUEST_TRANS_TYPE);
        request.put("clientTraceOrgNo", "1563358557016");
        request.put(BusinessFields.MOBILE_NUMBER, "***********");

        SodexoClient client = new SodexoClient();
        Map<String, Object> result = client.call(gateway, token, request);
        System.out.println(om.writeValueAsString(result));
    }

    /**
     * 线上支付token申请
     * 
     * @throws Throwable
     */
    public static String ExpenseTokenRequest() throws Throwable {
        String gateway = "https://uat-mt.sdxpass.com/api/" + SodexoConstants.EXPENSE_TOKEN_REQUEST_TRANS_TYPE;

        Map<String, Object> request = buildBaseRequest();
        request.put("transType", SodexoConstants.EXPENSE_TOKEN_REQUEST_TRANS_TYPE);
        request.put("clientTraceOrgNo", "1563358557016");
        request.put(BusinessFields.OPEN_ID, "716c5c8560f57a84");

        SodexoClient client = new SodexoClient();
        Map<String, Object> result = client.call(gateway, token, request);
        System.out.println("线上支付token申请 result:" + om.writeValueAsString(result));

        return MapUtil.getString(result, BusinessFields.EXPENSE_TOKEN);
    }

    /**
     * 线上消费
     * 
     * @throws Throwable
     */
    public static void ExpenseByToken() throws Throwable {
        String gateway = "https://uat-mt.sdxpass.com/api/" + SodexoConstants.EXPENSE_BY_TOKEN_TRANS_TYPE;

        Date date = new Date();
        DateFormat format = new SimpleDateFormat(DATA_TEMPLATE);
        String currentDatetime = format.format(date);

        Map<String, Object> request = new HashMap<String, Object>();
        request.put("version","1.0");
        request.put("transType", SodexoConstants.EXPENSE_BY_TOKEN_TRANS_TYPE);
        request.put(BusinessFields.SUBMIT_TIME, currentDatetime);
        request.put("clientTraceNo",System.currentTimeMillis() + "");
        request.put(BusinessFields.EXPENSE_TOKEN, ExpenseTokenRequest());
        request.put("mid","000000000090253");
        request.put("tid","92530001");
        request.put("clientTraceOrgNo", "t20220801000001");
        request.put(BusinessFields.ACTUAL_AMOUNT, 0.01);

        SodexoClient client = new SodexoClient();
        Map<String, Object> result = client.call(gateway, token, request);
        System.out.println("线上消费, result:" + om.writeValueAsString(result));
    }

    /**
     * TBalanceQuery
     * 
     * @throws Throwable
     */
    public static void TBalanceQuery() throws Throwable {
        String gateway = "https://uat-mt.sdxpass.com/api/" + SodexoConstants.BALANCE_QUERY_TRANS_TYPE;

        Date date = new Date();
        DateFormat format = new SimpleDateFormat(DATA_TEMPLATE);
        String currentDatetime = format.format(date);

        Map<String, Object> request = new HashMap<String, Object>();
        request.put("version","1.0");
        request.put("transType", SodexoConstants.EXPENSE_BY_TOKEN_TRANS_TYPE);
        request.put(BusinessFields.SUBMIT_TIME, currentDatetime);
        request.put("clientTraceNo",System.currentTimeMillis() + "");
        request.put(BusinessFields.EXPENSE_TOKEN, ExpenseTokenRequest());
        request.put("mid","000000000090253");
        request.put("tid","92530001");
        request.put(BusinessFields.ACTUAL_AMOUNT, 0.01);


        SodexoClient client = new SodexoClient();
        Map<String, Object> result = client.call(gateway, token, request);
        System.out.println(om.writeValueAsString(result));
    }

    private static Map<String ,Object> buildBaseRequest() {
        Date date = new Date();
        DateFormat format = new SimpleDateFormat(DATA_TEMPLATE);
        String currentDatetime = format.format(date);

        Map<String, Object> request = new HashMap<String, Object>();
        request.put("version","1.0");
        request.put("clientTraceNo",System.currentTimeMillis() + "");
        request.put("transTime",currentDatetime);
        request.put("mid","000000000090253");
        request.put("tid","92530001");
        request.put("submitTime", currentDatetime);
        request.put("barcode","779104021037670632");
        request.put("actualAmount",1);

        return request;
    }


    private static Map<String ,Object> buildBaseExpenseTokenRequest() {
        Date date = new Date();
        DateFormat format = new SimpleDateFormat(DATA_TEMPLATE);
        String currentDatetime = format.format(date);

        Map<String, Object> request = new HashMap<String, Object>();
        request.put("version","1.0");
        request.put("clientTraceNo",System.currentTimeMillis() + "");
        request.put("mid","000000000090253");
        request.put("tid","92530001");
        return request;
    }
}
