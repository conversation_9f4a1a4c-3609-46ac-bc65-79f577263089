package com.wosai.mpay.api.sodexo;

/**
 * <AUTHOR> Date: 2019-07-15 Time: 14:27
 */
public class BusinessFields {

    public static final String CLIENT_ID = "client_id";
    public static final String CLIENT_SECRET = "client_secret";
    public static final String GRANT_TYPE = "grant_type";
    public static final String TRANS_TYPE = "transType"; //交易类型，固定MealExpense
    public static final String SUBMIT_TIME = "submitTime"; //交易提交时间，格式yyyyMMddHHmmss
    public static final String CLIENT_TRACE_NO = "clientTraceNo"; //客户端交易流水号
    public static final String BARCODE = "Barcode"; //条形码号
    public static final String DISCOUNT_AMOUNT = "DiscountAmount"; //折扣金额，单位元，默认0
    public static final String ACTUAL_AMOUNT = "actualAmount"; //消费金额，单位元
    public static final String TRANS_TIME = "transTime"; //交易时间，格式yyyyMMddHHmmss
    public static final String CLIENT_TRACE_ORG_NO = "clientTraceOrgNo"; //原交易流水号
    public static final String MOBILE_NUMBER = "mobilenumber"; //手机号
    public static final String OPEN_ID = "openid"; //用户唯一标识
    public static final String EXPENSE_TOKEN = "expensetoken"; //用户令牌
    

}
