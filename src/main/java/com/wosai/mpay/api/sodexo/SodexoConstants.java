package com.wosai.mpay.api.sodexo;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> Date: 2019-07-15 Time: 14:29
 */
public class SodexoConstants {

    public static final String VERSION = "1.0";
    public static final String PAY_TRANS_TYPE = "MealExpense";
    public static final String PAY_QUERY_TRANS_TYPE = "TransQuery";
    public static final String CANCEL_TRANS_TYPE = "MealVoid";
    public static final String REFUND_TRANS_TYPE = "MealRefund";

    /**
     * 用户唯一标识申请
     */
    public static final String OPEN_ID_REQUEST_TRANS_TYPE = "mobileopenidrequest";
    /**
     * 线上支付token申请
     */
    public static final String EXPENSE_TOKEN_REQUEST_TRANS_TYPE = "ExpenseTokenRequest";
    /**
     * 线上消费
     */
    public static final String EXPENSE_BY_TOKEN_TRANS_TYPE = "ExpenseByToken";
    /**
     * 余额查询
     */
    public static final String BALANCE_QUERY_TRANS_TYPE = "TBalanceQuery";

    /**
     * 余额查询 fix版本
     */
    public static final String BALANCE_QUERY_TRANS_TYPE_FIX = "BalanceQuery";

    public static final String GRANT_TYPE = "client_credentials";
    public static final String AUTH_PROFIX = "Bearer ";
    public static final String CHARSET_UTF8 = "UTF-8";

    /**返回码**/
    public static final String RESP_CODE_SUCCESS = "0000"; //扣费成功，撤销成功
    public static final String RESP_CODE_FAIL = "9988"; //扣费失败，撤销失败
    public static final String RESP_CODE_SYSTEM_ERR = "9999"; //返回系统错误信息
    public static final String RESP_CODE_CARD_STATUS_ERR = "9998"; //卡号状态异常，无法扣款
    public static final String RESP_CODE_BALANCE_NOT_ENOUGTH = "9997"; //卡内余额不足
    public static final String RESP_CODE_MID_OR_TID_ERR = "9996"; //
    public static final String RESP_CODE_DEDUCTION_FAIL = "9995"; //扣费失败
    public static final String RESP_CODE_INVALID_TML_OR_MCH = "9993"; //无效终端或商户
    public static final String RESP_CODE_TRADE_NO_DUPILCATE = "9992"; //重复流水号
    public static final String RESP_CODE_TRADE_NO_NOT_FOUND = "9989"; //流水号不存在
    public static final String RESP_CODE_CANCEL_FAIL_AMOUNT_NOT_MATCH = "9986"; //撤销失败(金额不符)
    public static final String RESP_CODE_CANCEL_FAIL_CARD_NO_NOT_MATCH = "9985"; //撤销失败(卡号不匹配)
    public static final String RESP_CODE_CANCEL_FAIL_TRADE_NO_NOT_FOUND = "9984"; //撤销失败(流水号不存在或非当日)
    public static final String RESP_CODE_CANCEL_FAIL_ORI_TRADE_CANCELED = "9983"; //撤销失败(原交易已撤销)
    public static final String RESP_CODE_REFUND_FAIL_TRADE_NO_NOT_FOUND = "9982"; //退货失败(流水号不存在)
    public static final String RESP_CODE_REFUND_FAIL_AMOUNT_OVER_ORI_AMOUNT = "9981"; //退货失败(原金额过大)
    public static final String RESP_CODE_REFUND_FAIL_ORI_TRADE_REFUNDED = "9980"; //退货失败(原交易已退货)
    public static final String RESP_CODE_REFUND_FAIL_ERR_UNKNOWN = "9978"; //退货失败(其他原因)
    public static final String RESP_CODE_CANCEL_FAIL_ERR_UNKNOWN = "9970"; //撤销失败(其他原因)

    /**交易状态码**/
    public static final String TRANS_STATUS_SUCCESS = "SUCCESS";
    public static final String TRANS_STATUS_FAILED = "FAILED";
    public static final String TRANS_STATUS_PENDING = "PENDING";

    /**鉴权失败返回message**/
    public static final String TOKEN_INVALID_MESSAGE = "Authorization has been denied for this request.";

    public static final Set<String> TRADE_CANCEL_CODE = new HashSet<String>(){{
        List<String> list = Arrays.asList(RESP_CODE_FAIL, RESP_CODE_CARD_STATUS_ERR, RESP_CODE_BALANCE_NOT_ENOUGTH
                , RESP_CODE_MID_OR_TID_ERR, RESP_CODE_DEDUCTION_FAIL, RESP_CODE_INVALID_TML_OR_MCH
                ,RESP_CODE_TRADE_NO_DUPILCATE);
        addAll(list);
    }};

}
