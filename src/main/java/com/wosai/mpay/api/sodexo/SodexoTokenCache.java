package com.wosai.mpay.api.sodexo;

import com.google.common.base.Objects;
import com.google.common.base.Optional;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Date: 2019-07-19 Time: 14:09
 */
public class SodexoTokenCache {
    public static final Logger logger = LoggerFactory.getLogger(SodexoTokenCache.class);

    private LoadingCache<TokenKeyModel, Optional<String>> TOKEN_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.HOURS)
            .build(new CacheLoader<TokenKeyModel, Optional<String>>() {
                @Override
                public Optional<String> load(TokenKeyModel keyModel) throws Exception {

                    return loadToken(keyModel.getClientId(), keyModel.getClientSecret(), keyModel.getUrl());
                }
            });

    private SodexoClient sodexoClient;

    public SodexoTokenCache(SodexoClient sodexoClient) {
        this.sodexoClient = sodexoClient;
    }

    public String getAccessToken(String clientId, String clientSecret, String url) {
        TokenKeyModel tokenKey = new TokenKeyModel(clientId, clientSecret, url);

        Optional<String> tokenOptional;
        try {
            tokenOptional = TOKEN_CACHE.get(tokenKey);
        } catch (ExecutionException e) {
            throw new RuntimeException("获取token失败");
        }
        if (tokenOptional.isPresent()) {
            return tokenOptional.get();
        }
        throw new RuntimeException("获取token失败");
    }

    public void refreshToken(String clientId, String clientSecret, String url) {
        TokenKeyModel tokenKey = new TokenKeyModel(clientId, clientSecret, url);

        TOKEN_CACHE.refresh(tokenKey);
    }



    private Optional<String> loadToken(String clientId, String clientSecret, String url) {
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.CLIENT_ID, clientId);
        builder.set(BusinessFields.CLIENT_SECRET, clientSecret);
        builder.set(BusinessFields.GRANT_TYPE, SodexoConstants.GRANT_TYPE);

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(builder.build(), url);
        } catch (Exception e) {
            throw new RuntimeException("获取token失败");
        }

        String accessToken = null;
        if (result != null && result.size() > 0) {
            accessToken = String.valueOf(result.get(ResponseFields.ACCESS_TOKEN));
        }
        if (StringUtils.isEmpty(accessToken) || "null".equalsIgnoreCase(accessToken)) {
            throw new RuntimeException("获取token失败");
        }

        return Optional.of(accessToken);
    }

    private Map<String, Object> retryIfNetworkException(Map<String,Object> request, String url) throws Exception {
        Exception exception = null;
        for (int i = 0; i < 3; ++i) {
            try {
                return sodexoClient.queryTokenCall(url, request);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in sodexo {}", ex);
            }
        }
        logger.error("loadToken still network i/o error after retrying 3 times.");
        throw exception;
    }



    private static class TokenKeyModel {
        private String clientId;
        private String clientSecret;
        private String url;

        public TokenKeyModel(String clientId, String clientSecret) {
            this.clientId = clientId;
            this.clientSecret = clientSecret;
        }

        public TokenKeyModel(String clientId, String clientSecret, String url) {
            this.clientId = clientId;
            this.clientSecret = clientSecret;
            this.url = url;
        }

        public String getClientId() {
            return clientId;
        }

        public void setClientId(String clientId) {
            this.clientId = clientId;
        }

        public String getClientSecret() {
            return clientSecret;
        }

        public void setClientSecret(String clientSecret) {
            this.clientSecret = clientSecret;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            TokenKeyModel that = (TokenKeyModel) o;
            return Objects.equal(clientId, that.clientId) &&
                    Objects.equal(clientSecret, that.clientSecret) &&
                    Objects.equal(url, that.url);
        }

        @Override
        public int hashCode() {
            return Objects.hashCode(clientId, clientSecret, url);
        }
    }
}
