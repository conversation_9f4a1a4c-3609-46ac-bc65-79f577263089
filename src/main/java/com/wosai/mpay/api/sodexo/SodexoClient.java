package com.wosai.mpay.api.sodexo;

import com.google.common.collect.Maps;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> Date: 2019-07-15 Time: 14:28
 */
public class SodexoClient {

    public static final Logger logger = LoggerFactory.getLogger(SodexoClient.class);
    private static final String CALL_CONTENT_TYPE = "application/json";
    private static final String QUERY_TOKEN_CALL_CONTENT_TYPE = "application/x-www-form-urlencoded";
    private static final String DEFAULT_CHARSET = "utf-8";

    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public SodexoClient() {
    }

    public SodexoClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map<String,Object> call(String gateway, String token, Map<String,Object> request) throws MpayException, MpayApiNetworkError {
        String requestStr = JsonUtil.objectToJsonString(request);
        Map<String, String> headers = buildHttpHeader(token);

        logger.debug("request {}", requestStr);
        String responseStr = HttpClientUtils.doPost(SodexoClient.class.getName(), null, null, gateway
                , CALL_CONTENT_TYPE, requestStr, headers, SodexoConstants.CHARSET_UTF8
                , connectTimeout, readTimeout);
        logger.debug("response {}", responseStr);

        try{
            Map<String,Object> result = JsonUtil.jsonStringToObject(responseStr, Map.class);
            return result;
        }catch (Exception e){
            throw new MpayApiUnknownResponse("parse response error: " + e.getMessage(), e);
        }
    }

    public Map<String, Object> queryTokenCall(String gateway, Map<String,Object> request) throws MpayException
            , MpayApiNetworkError {
        Map<String, String> req = genericsTransform(request);

        String requestStr;
        try {
            requestStr = WebUtils.buildQuery(req, DEFAULT_CHARSET);
        } catch (IOException e) {
            throw new MpayException(e.getMessage(), e);
        }

        logger.debug("request {}", JsonUtil.objectToJsonString(MapUtils.copyAndMaskSensitiveInfo(req, Arrays.asList(BusinessFields.EXPENSE_TOKEN, BusinessFields.CLIENT_SECRET))));
        String responseStr = HttpClientUtils.doPost(SodexoClient.class.getName(), null, null, gateway
                , QUERY_TOKEN_CALL_CONTENT_TYPE,requestStr, null, SodexoConstants.CHARSET_UTF8
                , connectTimeout, readTimeout);
        try{
            Map<String,Object> result = JsonUtil.jsonStringToObject(responseStr, Map.class);
            logger.debug("response {}", JsonUtil.objectToJsonString(MapUtils.copyAndMaskSensitiveInfo(result, Arrays.asList(ResponseFields.ACCESS_TOKEN, ResponseFields.REFRESH_TOKEN))));
            return result;
        }catch (Exception e){
            throw new MpayApiUnknownResponse("parse response error: " + e.getMessage(), e);
        }
    }

    private Map<String, String> genericsTransform(Map<String, Object> request) {
        if (request == null || request.isEmpty()) {
            return null;
        }
        Map<String, String> map = new HashMap<String, String>();

        Set<Map.Entry<String, Object>> entries = request.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            map.put(entry.getKey(), String.valueOf(entry.getValue()));
        }

        return map;
    }

    private Map<String, String> buildHttpHeader(String token) {
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        Map<String, String> headers = Maps.newHashMapWithExpectedSize(4);
        headers.put(ProtocolFields.AUTHORIZATION, SodexoConstants.AUTH_PROFIX + token);

        return headers;
    }
}
