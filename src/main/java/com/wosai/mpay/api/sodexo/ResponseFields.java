package com.wosai.mpay.api.sodexo;

/**
 * <AUTHOR> Date: 2019-07-15 Time: 14:28
 */
public class ResponseFields {

    public static final String RETURN_CODE = "returnCode"; //返回码

    public static final String RETURN_CODE_FIX = "returncode"; //返回码

    public static final String RETURN_MESSAGE = "returnMessage"; //返回码描述
    public static final String VERSION = "version"; //报文版本号
    public static final String TRANS_TYPE = "transType"; //交易类型
    public static final String SUBMIT_TIME = "submitTime"; //提交时间
    public static final String CLIENT_TRACE_NO = "clientTraceNo"; //客户端交易流水号
    public static final String HOST_TRACE_NO = "hostTraceNo"; //后台交易流水号
    public static final String HOST_TIME = "hostTime"; //后台处理时间
    public static final String TRANS_STATUS = "TransStatus"; //交易状态
    public static final String CLIENT_TRACE_ORG_NO = "clientTraceOrgNo"; //原交易流水号
    public static final String AVA_AMOUNT = "avaAmount"; //卡余额，单位元

    public static final String ACCESS_TOKEN = "access_token"; //访问令牌
    public static final String TOKEN_TYPE = "token_type"; //令牌类型
    public static final String EXPIRES_IN = "expires_in"; //有效期，单位s
    public static final String REFRESH_TOKEN = "refresh_token"; //

    public static final String MESSAGE = "Message";

}
