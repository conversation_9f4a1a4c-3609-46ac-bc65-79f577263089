package com.wosai.mpay.api.vnpt;

public class BusinessFields {
    // 请求字段常量
    public static final String PCODE = "pcode";
    public static final String MERCHANT_CODE = "merchant_code";
    public static final String DATA = "data";

    // 数据字段常量
    public static final String MAP_ID = "map_id";
    public static final String AMOUNT = "amount";
    public static final String START_DATE = "start_date";
    public static final String END_DATE = "end_date";
    public static final String CONDITION = "condition";
    public static final String CUSTOMER_NAME = "customer_name";
    public static final String REQUEST_ID = "request_id";
    public static final String BANK_CODE = "bank_code";
    public static final String EXTEND = "extend";
    public static final String ACCOUNT_NO = "account_no";
    public static final String PHONE = "phone";
    public static final String EMAIL = "email";
    public static final String ADDRESS = "address";
    public static final String ID = "id";
    public static final String CONTENT_QR = "contentQR";

    // 响应字段常量
    public static final String RESPONSE_CODE = "response_code";
    public static final String MESSAGE = "message";
    public static final String ACCOUNT_NAME = "account_name";
    public static final String BANK_NAME = "bank_name";
    public static final String QR_CODE = "qr_code";
}
