package com.wosai.mpay.api.vnpt;

import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Hex;
import com.wosai.mpay.util.RsaSignature;

import java.util.Map;

public class VnptSignature {

    /**
     * 校验签名
     *
     * @param request 请求参数，类型为Map
     * @param publicKey
     * @return 签名是否有效
     */
    public static boolean validateSign(Map<String, Object> request, String publicKey) throws MpayException {
        // 构建签名字符串
        String signatureString = request.get("RequestId") +
                "|" +
                request.get("ReferenceId") +
                "|" +
                request.get("RequestTime") +
                "|" +
                request.get("Amount") +
                "|" +
                request.get("Fee") +
                "|" +
                request.get("VaAcc") +
                "|" +
                request.get("MapId");

        // 获取请求中的签名
        String requestSignature = (String) request.get("Signature");

        // 使用公钥验证签名
        try {
            return RsaSignature.validateSign(signatureString.getBytes(), Hex.decodeToBytes(requestSignature), "SHA256withRSA", publicKey);
        } catch (Exception e) {
            throw new MpayException(e.getMessage(), e);
        }
    }

}
