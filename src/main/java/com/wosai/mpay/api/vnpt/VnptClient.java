package com.wosai.mpay.api.vnpt;

import com.wosai.mpay.util.DesUtil;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class VnptClient {

    public static final Logger logger = LoggerFactory.getLogger(VnptClient.class);
    public static final String CONTENT_TYPE = "application/json; utf-8";

    private int connectTimeout = 500;
    private int readTimeout = 5000;

    public VnptClient() {
    }

    public VnptClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }



    public Map<String,Object> call(String url, String key, Map<String,Object> request) throws Exception {
        logger.info("request {}", JsonUtil.objectToJsonString(request));
        // 加密请求数据
        Map<String, Object> clone = MapUtils.clone(request);
        // data 需要加密
        Object data = clone.get(BusinessFields.DATA);
        String plainText = null;
        if(data instanceof Map){
            plainText = JsonUtil.objectToJsonString(data);
        }else{
            plainText = data.toString();
        }
        String encryptedData = DesUtil.encryptToHex(plainText, key);
        clone.put(BusinessFields.DATA, encryptedData);
        String requestJson = JsonUtil.objectToJsonString(clone);
        logger.info("request {}", requestJson);
        String responseString = HttpClientUtils.doPost(VnptClient.class.getName(), null, null, url, CONTENT_TYPE, requestJson,null, VnptConstant.CHARSET_UTF8, connectTimeout, readTimeout);
        logger.info("response {}",  responseString);
        return JsonUtil.jsonStringToObject(responseString, Map.class);
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
}
