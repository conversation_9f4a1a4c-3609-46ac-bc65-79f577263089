package com.wosai.mpay.api.vnpt;

import com.wosai.pantheon.util.MapUtil;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/11/22.
 */
public class VnptUtil {

    /**
     * 构建mapId
     */

    public static String buildMapId(String qrcode, String orderSn) {
        if(orderSn == null){
            return qrcode;
        }
        return qrcode + ":" + orderSn;
    }

    /**
     * map id 规则为 terminalSn or qrcode:orderSn
     * @param mapId
     * @return
     */
    public static String getQrcodeFromMapId(String mapId) {
        return mapId.split(":")[0];
    }

    /**
     * map id 规则为 qrcode or qrcode:orderSn
     * @param mapId
     * @return
     */
    public static String getOrderSnFromMapId(String mapId) {
        if(mapId.indexOf(":") < 0){
            return null;
        }
        return mapId.split(":")[1];
    }

    public static String getQrcodeFromRequest(Map<String,Object> notification) {
        return getQrcodeFromMapId(MapUtil.getString(notification, BusinessFields.MAP_ID));
    }

}
