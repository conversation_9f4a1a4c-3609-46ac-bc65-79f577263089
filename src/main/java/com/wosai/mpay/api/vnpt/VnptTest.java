//package com.wosai.mpay.api.vnpt;
//
//import com.wosai.mpay.api.vnpt.BusinessFields;
//import com.wosai.mpay.exception.MpayException;
//import com.wosai.mpay.util.MapUtils;
//
//import java.time.ZoneId;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.UUID;
//
//import static com.wosai.mpay.util.QrcodeUtil.readQRCodeFromBase64;
//
///**
// * Created by wu<PERSON><PERSON><PERSON> on 2024/11/20.
// */
//public class VnptTest {
//    private static final String URL = "https://sandboxva.ecollect.vn:10003/ApiResf_VirtualAccount/services/registerVA";
//    private static final String MERCHANT_CODE = "VAP001";
//    private static final String KEY_3DES = "31feae316de0a42520ef5ec4";
//    public static final String PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQChj0YbDuPLiLhFGsmgTwhadeg5PceK1kfL+tJnMxb/55/YCa0JXeCtMPz4vfzvuZZjtqKLiLXN9JrLfCJVIsGC+YrW1D3sGCsh+fwGB2hMQgh0GpbQo5zU7AaZ9RTzbGQgwJiBNhGzfl3uze5KDyw5Rm618DRpU8rMzTDlD1cxIQIDAQAB";
//
//
//    public static void main(String[] args) throws Exception {
////        testSign();
////        testMap();
//        testQr();
//        System.out.println(ZoneId.of("UTC"));
//    }
//
//    public static void testSign() throws MpayException {
//        // 示例公钥（实际应用中应从安全的地方获取）
//        // 示例请求
//        Map<String, Object> request = MapUtils.hashMap(
//            "MerchantCode", "VAP001",
//    "RequestId", "VAP001D05E5453E0624CBE807B33A00BAA8551",
//    "RequestTime", "2024-11-18 17:00:57",
//    "VaAcc", "***************",
//    "VaName", "VAP001 NAME TEST",
//    "MapId", "VAP001-2024-WJW-008",
//    "ReferenceId", "VAP00157A2A81A260F4A4BA13BAE28BBFAC9FE",
//    "Amount", 100000,
//    "Fee", 4400,
//    "BankName", "Ngân hàng Kookmin",
//    "BankCode", "KBBANK",
//    "BankTranTime", "2024-11-18 16:59:57",
//    "Remark", "transfer to ***************",
//    "Signature", "12162ea039311e056d4c52042abad4c97b7e3e94aa9aa463c1fee8899285cf93f5504be137fe7bd5df0ce103c32d48d58be55e3d54e89cc1e6a7ece6d81dae73d8d66d9ab9d3dc7cf3c75ac1fd1d263d77e33d45dc6efd87af88361b1cbbff640a028f436cc147c65cc8682fe5b3e403dd1d7e90acb8134c2590f919dbb4477a"
//                );
//
//        VnptSignature validator = new VnptSignature();
//        boolean isValid = validator.validateSign(request, PUBLIC_KEY);
//        System.out.println("Signature is valid: " + isValid);
//    }
//
//
//    public static void testMap() throws Exception {
//        VnptClient client = new VnptClient();
//
//            // 构建请求数据
//             Map<String,Object> request = new HashMap<>();
//             Map<String,Object> requestData = new HashMap<>();
//
//            request.put(BusinessFields.PCODE, "9000");
//            request.put(BusinessFields.MERCHANT_CODE, MERCHANT_CODE);
//            request.put(BusinessFields.DATA, requestData);
//            requestData.put(BusinessFields.MAP_ID, "VAP001-2024-WJW-008");
//            requestData.put(BusinessFields.AMOUNT, 1000);
//            requestData.put(BusinessFields.START_DATE, "**************");
//            requestData.put(BusinessFields.END_DATE, "**************");
//            requestData.put(BusinessFields.CONDITION, "01");
//            requestData.put(BusinessFields.CUSTOMER_NAME, "NAME TEST"); //todo
//            requestData.put(BusinessFields.REQUEST_ID, "VAP001" + UUID.randomUUID().toString());
//            requestData.put(BusinessFields.BANK_CODE, "KBBANK"); // todo
////            requestData.put(BusinessFields.ACCOUNT_NO, "***************");
//
//            Map<String,Object> extend = new HashMap<>();
//            extend.put(BusinessFields.CONTENT_QR, "VAP001");
//            requestData.put(BusinessFields.EXTEND, extend);
//
//            // 调用API
//            Map<String,Object> response = client.call(URL, KEY_3DES, request);
//
//            // 打印响应
//            System.out.println("Response: " + response.toString());
//    }
//
//
//
//    public static void testQr() {
//        String base64String = "iVBORw0KGgoAAAANSUhEUgAAAfQAAAH0AQAAAADjreInAAADQ0lEQVR42u2cUW7jMAxEBfD+d+0BBGgbWxwOEcW77X6RGBdondhP/SEocjTSWP91fQ3x4sWLFy9evHjx4sWLF/8Tfo7rstedzfvn+rgffX9+PVnX1/tN8Y1427/jPbv/XgNeA79ur4Hwvvg+/H42rxfmvr1jCewOLowivh8/rgxhiCpE0dg34hvz19v7bl+4t/yfxLfi8/yB0EHFcCcTelN8Jz5i5W8/j/Wn+Jq8XzSDeLzsGsJLiOf+U3xV3mcOrxTGSNPGiiGmie/GY44grQATiI89zFtL8c14DGLUKCyWj+ag5gIjiG/D35MFzRsDM4dLCGggbYlvxe98ETWCD7G/n1FEcHCJb8Lv+PAWwQXkkZvHCQXxPX+Ir8yHMBg95EI+Wbuy9OQx3/OH+OL8ThwQDy2yitcPLizPT/qj+Kp8Lh2QIlA/IGfk6UN8E55KQuQSCh6eXgYLSeJ78ElEQK6gQBo0cVABIb4H79kBtUKICVw/UhUpvhNPDQRMZAiaQb2EV5HiW/G7JuAVY+MHaBzmOOsP4kvzpBRDMkL68G4yXKZH/Vl8Xd4jJoIInyz6C3wzj/4D8WX56A/wYswntHyA6lJ8Kx5tYqwjRzS5o8DNxGQhEN+Et0WG0WgT3E4QVaTbCMR34lfaL2TUSIzYUOKd5ql+FF+a99ZwskbkxeSbdeAQf+Jr84bWEduFIClBV0wdpfhmvE8QtpJ4PNlJPOyD/0R8aT5E41ggviMHDjLsLAw9QXwfnvcIUMVI3jEa6lg/ii/Mr2QT8gXkcBBmE9HJPyi+MA/lyBcPJjrJKCx5N7n4VnwWEA3tZEwgKZkc1o/Fl+djpZisojCUwFLGDaT4LjypxbF+mN0kyDRn/5j4wjyOCoFj3MguTisILDWK78Mn0xjOjaGaIZaOH/UD8RX5wL1qTMfGpC0Ew5b4Znw6RcD4lKB0dBie2RLfiQ+nGIlH6USp2GDywT8ivjJv2SzKkZROF8jygfg2PE6GmXSQbN47QKdJLPEt+cnlQ95CjpHP/jHxjXjeJDYtd5eP+UN8VT5bQyATJwshvnw8P1R8QT7Oj4/OgS3ElhsM8b34317ixYsXL168ePHixYsXL/4f+T8vLC4U/AppJgAAAABJRU5ErkJggg\u003d\u003d";
//        try {
//            String qrCodeContent = readQRCodeFromBase64(base64String);
//            System.out.println("QR Code Content: " + qrCodeContent);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//
//}
