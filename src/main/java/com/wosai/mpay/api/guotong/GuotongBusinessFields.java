package com.wosai.mpay.api.guotong;

/**
 * 国通业务字段类
 * 包含所有请求参数字段
 */
public class GuotongBusinessFields {
    // 通用字段
    public static final String AGET_ID = GuotongProtocolFields.AGET_ID; // 商户所在国通系统内机构号（或虚拟机构号）
    public static final String CUST_ID = GuotongProtocolFields.CUST_ID; // 商户所在国通系统内商户号
    public static final String ORDER_NO = GuotongProtocolFields.ORDER_NO; // 订单号，该订单号需要唯一，最大长度40，只能由数字，大小写字母构成
    public static final String TXAMT = GuotongProtocolFields.TXAMT; // 单位分，国通系统内记录至流水（原订单交易金额）
    public static final String TRADING_IP = GuotongProtocolFields.TRADING_IP; // 商户端终端设备 IP 地址，需传公网ip
    public static final String CUST_LOGIN = "custLogin"; // 客户登录信息
    public static final String DRIVE_NO = GuotongProtocolFields.DRIVE_NO; // 字母或数字，长度8位数字，由合作方自定义
    public static final String REMARK = GuotongProtocolFields.REMARK; // 备注信息
    public static final String OPERATOR = "operator"; // 可传店员标识
    public static final String TIME_STAMP = GuotongProtocolFields.TIME_STAMP; // 时间戳，格式yyyyMMddHHmmss
    public static final String VERSION = GuotongProtocolFields.VERSION; // 版本号，默认 1.0.0
    public static final String SIGN = GuotongProtocolFields.SIGN; // 签名
    public static final String TYPE = "type"; // 设备类型，P-智能POS A-app扫码 C-PC端 T-台牌扫码
    public static final String LATITUDE = "latitude"; // 纬度，例：119.411869
    public static final String LONGITUDE = "longitude"; // 经度，例：119.411869
    public static final String ACCESS = "access"; // 0:Android SDK 1:iOS SDK 2:windows SDK 3:直连（默认填3）

    // 支付相关字段
    public static final String CODE = "code"; // 设备读取用户微信或支付宝中的条码或者二维码信息（付款码）
    public static final String IS_POP = "isPop"; // 1-是，其他为否
    public static final String SP_NO = "spNo"; // 有使用国通音箱的时候传输可指定音箱播报
    public static final String SPEAKER_SWITCH = "speakerSwitch"; // 0不播报 1播报，默认1播报
    public static final String TITLE = "title"; // 最大长度64个字符
    public static final String SERVER_THREE_ORDER_NO = GuotongProtocolFields.SERVER_THREE_ORDER_NO; // 用于国通设备非接口交易时异步订单生成，或POS发起交易时传输
    public static final String FPID = "fpid"; // 用于银联交易上送服务商机构PID使用
    public static final String OUT_TIME = "outTime"; // 未传输时默认15，最小1分钟，最大值15分钟，单位（分）
    public static final String ASYNC_NOTIFY = "asyncNotify"; // 异步通知地址
    public static final String REAL_TIME_RATE = "realTimeRate"; // 单位%，支持小数点后两位，特殊项目需平台配置相关权限时生效
    public static final String REAL_TIME_D0_RATE = "realTimeD0Rate"; // 单位%，支持小数点后三位，特殊项目需平台配置相关权限时生效
    public static final String LIMIT_PAY = "limitPay"; // 0-无限制，1-不能使用信用卡
    public static final String SUB_APPID = "subAppid"; // 子应用ID
    public static final String ZFB_DISABLE_PAY_CHANNELS = "zfbDisablePayChannels"; // 支付宝禁用支付渠道
    public static final String WX_ATTACH = "wxAttach"; // 用于微信支付，该字段主要用于商户携带订单的自定义数据，限制127字符

    // 优惠相关字段
    public static final String DIS_INFO = GuotongProtocolFields.DIS_INFO; // 优惠券信息
    public static final String DIS_TXAMT = GuotongProtocolFields.DIS_TXAMT; // 单位分，实际优惠金额
    public static final String CODE_AMT = GuotongProtocolFields.CODE_AMT; // 单位分，优惠券面额
    public static final String REFUND_DIS_AMT = GuotongProtocolFields.REFUND_DIS_AMT; // 单位分，退款优惠金额

    // C扫B特有字段
    public static final String OPENID = "openid"; // 用户标识
    public static final String PAY_WAY = GuotongProtocolFields.PAY_WAY; // 支付方式
    public static final String IP = "ip"; // IP地址
    public static final String WX_APPID = "wxAppid"; // 微信支付必传
    public static final String TRA_TYPE = "traType"; // 微信支付必传，5公众号 8小程序
    public static final String ZFB_APPID = "zfbappid"; // 支付宝支付必传
    public static final String QR_CODE = "qrCode"; // 银联支付
    public static final String QR_CODE_TYPE = "qrCodeType"; // 银联支付，收款二维码类型
    public static final String JDBT_FQ_NUM = GuotongProtocolFields.JDBT_FQ_NUM; // 京东白条分期期数
    public static final String LIMIT_OPENID = "limitOpenid"; // 0：否1 是，不传默认0

    // 商品信息字段
    public static final String GOODS_DETAIL_LIST = "goodsDetailList"; // 商品详情列表
    public static final String ALIPAY_GOODS_DETAIL = "goodsDetail"; // 支付宝单品优惠活动，JSON数组
    public static final String GOODS_INFO = "goodsInfo"; // 商品信息
    public static final String BUSINESS_PARAMS = "businessParams"; // 商户传入业务信息

    // 支付宝业务参数
    public static final String ZFB_CUST_ID = "zfbCustId"; // 支付宝商户ID
    public static final String ZFB_BUYER_ACCOUNT = "zfbBuyerAccount"; // 支付宝买家账号
    public static final String ZFB_CONSUME_OUT_SCENE = "zfbConsumeOutScene"; // 目前固定传 "jiayou"
    public static final String ZFB_JIAYOU_SOURCE = "zfbJiayouSource"; // 目前固定传 "mayijiayou"
    public static final String ZFB_JIAYOU_PROMO_RULE = "zfbJiayouPromoRule"; // 目前固定传 "mayijiayou"
    public static final String ZFB_SHOP_NO = "zfbShopNo"; // 支付宝店铺编号

    // 支付宝业务扩展参数
    public static final String FOOD_ORDER_TYPE = "foodOrderType"; // 食品订单类型
    public static final String HB_FQ_NUM = GuotongProtocolFields.HB_FQ_NUM; // 花呗分期期数, 当前仅支持3、6、12
    public static final String CR_FQ_NUM = GuotongProtocolFields.CR_FQ_NUM; // 信用卡分期数，当前支持传入 3、6、12
    public static final String HB_FQ_SELLER_PERCENT = "hbFqSellerPercent"; // 使用花呗分期需要卖家承担的手续费比例的百分值

    // 退款相关字段
    public static final String RE_ORDER_NO = "reOrderNo"; // 对应支付接口返回的orderNo
    public static final String T_ORDER_NO = "tOrderNo"; // 对应支付接口或异步通知接口返回的T_ORDER_NO
    public static final String OLD_T_ORDER_NO = "oldTOrderNo"; // 对应支付接口请求参数中的orderNo
    public static final String REFUND_AMOUNT = "refundAmount"; // 单位分，支持部分退款
    public static final String TAG = "tag"; // 1支付宝 2微信 9银联（类型必须填写正确，与正向交易订单类型一致）12数币支付
    public static final String ASYNC_NOTIFY_URL = "asyncNotifyUrl"; // 异步通知URL

    // 查询相关字段
    public static final String GT_ORDER_NO = "gtOrderNo"; // 国通订单号
    public static final String ORDER_TYPE = GuotongProtocolFields.ORDER_TYPE; // 订单类型
    public static final String ORDER_TIME = GuotongProtocolFields.ORDER_TIME; // 订单时间，格式yyyyMMdd
    public static final String ORIGIN_TRADE_DATE = "originTradeDate"; // 原交易日期，格式yyyyMMdd

    // 终端信息参数
    public static final String ENCRYPT_RAND_NUM = "encryptRandNum"; // 加密随机数
    public static final String SECRET_TEXT = "secretText"; // 密文数据
    public static final String AP_VERSION_NO = "apVersionNo"; // AP版本号
    public static final String UGPS_ADDRESS = "ugpsAddress"; // 基站信息
    public static final String NETWORK_LICENSE = "networkLicense"; // 网络许可证

    // 数币硬钱包参数字段
    public static final String WAL_CHANNEL = "walChannel"; // 24-兴业银行数币通道，不传表示非钱包支付
    public static final String WAL_PWD = "walPwd"; // 硬件子钱包消费时，免密消费可不传，非免密消费必传
    public static final String ENCTRS_KEY = "enctrsKey"; // 硬件子钱包消费时，输入密码后必传
    public static final String APDU_RESPDATE = "apduRespdate"; // 硬件钱包单离线付款操作指令响应数

    // 商品详情字段
    public static final String GOODS_ID = "goods_id"; // 商品ID
    public static final String WXPAY_GOODS_ID = "wxpay_goods_id"; // 微信支付商品ID
    public static final String ALIPAY_GOODS_ID = "alipay_goods_id"; // 支付宝商品ID
    public static final String GOODS_NAME = "goods_name"; // 商品名称
    public static final String QUANTITY = "quantity"; // 数量
    public static final String PRICE = "price"; // 价格
    public static final String GOODS_CATEGORY = "goods_category"; // 商品类别
    public static final String BODY = "body"; // 商品描述
    public static final String SHOW_URL = "show_url"; // 商品展示链接
    public static final String DETAIL = "detail";

    // 商品信息字段
    public static final String COST_PRICE = "cost_price"; // 单位分，订单原价
    public static final String RECEIPT_ID = "receipt_id"; // 小票ID
    public static final String GOODS_DETAIL = "goods_detail"; // 商品详情

    // 收单附加数据字段
    public static final String ORDER_INFO = "orderInfo"; // 订单信息
    public static final String ID = "id"; // 商品ID
    public static final String NAME = "name"; // 商品名称
    public static final String CATEGORY = "category"; // 类别
    public static final String CARD_TYPE = "card_type"; // 卡类型
    public static final String DCT_AMOUNT = "dctAmount"; // 折扣金额
    public static final String ADDN_INFO = "addnInfo"; // 附加信息

    // 星POS项目专属字段
    public static final String TXAMT_ORDER = "txamtOrder"; // 星POS中转项目必传
    public static final String BATCH_NO = "batchNo"; // 批次号
    public static final String O_TXN_DT = "oTxnDt"; // 原交易日期
    public static final String CHARACTER_SET = "characterSet"; // 字符集，默认传00-GBK
    public static final String QRY_NO = "qryNo"; // 查询号
    public static final String QRY_DT = "qryDt"; // 查询日期

    // 获取银联用户标识
    public static final String PAY_CODE = "payCode"; // 收款方识别HTTP请求User Agent中包含银联支付标识， 格式为" UnionPay/<版本号><App标识>"， 其中<版本号>固定为1.0，<App标识>为付款方自行定义的内容（仅包含字母或数字）。示例：UnionPay/1.0 ICBCeLife
    public static final String AUTH_CODE = "authCode"; // 付款方返回的临时授权码，一次有效
}
