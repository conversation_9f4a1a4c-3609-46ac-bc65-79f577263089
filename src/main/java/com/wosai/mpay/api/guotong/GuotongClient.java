package com.wosai.mpay.api.guotong;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.GuotongSignature;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * @ClassName: GuotongClient
 * @Description: 国通支付客户端类，用于处理与国通支付网关的通信
 * @Auther:
 * @Date: 2025/4/22
 */
public class GuotongClient {
    // 日志记录器
    public static final Logger log = LoggerFactory.getLogger(GuotongClient.class);

    // 内容类型常量，用于HTTP请求头
    private static final String CONTENT_TYPE = "application/json;charset=utf-8";

    // 连接超时时间（毫秒）
    private int connectTimeout = 3000;

    // 读取超时时间（毫秒）
    private int readTimeout = 30000;

    /**
     * 获取连接超时时间
     *
     * @return 连接超时时间（毫秒）
     */
    public int getConnectTimeout() {
        return connectTimeout;
    }

    /**
     * 设置连接超时时间
     *
     * @param connectTimeout 连接超时时间（毫秒）
     */
    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    /**
     * 获取读取超时时间
     *
     * @return 读取超时时间（毫秒）
     */
    public int getReadTimeout() {
        return readTimeout;
    }

    /**
     * 设置读取超时时间
     *
     * @param readTimeout 读取超时时间（毫秒）
     */
    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    /**
     * 调用国通支付接口
     *
     * @param serviceUrl 服务URL地址
     * @param request    请求参数
     * @param publicKey  访问公钥
     * @return 响应结果
     * @throws MpayException       支付异常
     * @throws MpayApiNetworkError 网络错误异常
     */
    public Map<String, Object> call(String serviceUrl, Map<String, Object> request, String publicKey) throws MpayException, MpayApiNetworkError {
        // 记录原始请求日志
        log.info("url: {}", serviceUrl);
        log.info("原始请求参数: {}", JsonUtil.objectToJsonString(request));

        // 生成签名
        String retSigned = null;
        try {
            retSigned = GuotongSignature.getSign(publicKey, request);
        } catch (Exception e) {
            log.error("签名生成异常", e);
            throw  new MpayApiNetworkError("签名生成异常");
        }
        // 将签名添加到请求参数中
        request.put(GuotongProtocolFields.SIGN, retSigned);
        String requestStr = JsonUtil.objectToJsonString(request);
        log.info("request {}", requestStr);
        // 发送HTTP POST请求
        String responseStr = HttpClientUtils.doPost(GuotongClient.class.getName(), null, null, serviceUrl, CONTENT_TYPE, requestStr, "utf-8", connectTimeout, readTimeout);

        // 记录响应日志
        log.info("response {}", responseStr);

        // 解析响应结果
        try {
            return JsonUtil.jsonStringToObject(responseStr, new TypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            throw new MpayApiUnknownResponse("解析响应结果异常: " + e.getMessage(), e);
        }
    }
}
