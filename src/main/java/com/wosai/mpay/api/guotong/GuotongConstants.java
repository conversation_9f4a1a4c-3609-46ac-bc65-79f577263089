package com.wosai.mpay.api.guotong;

import java.util.Arrays;
import java.util.List;

/**
 * 国通常量类
 * 包含所有文档中的示例值和常量
 */
public class GuotongConstants {
    // 响应码常量
    public static final String CODE_SUCCESS = "000000"; // 表示成功
    public static final String CODE_PROCESSING = "222222"; // 表示支付中/等待授权/交易未知
    public static final String CODE_ORDER_CLOSED = "000010"; // 该订单已关闭，请重新下单
    public static final String CODE_ORDER_FAILED = "555555"; // 支付失败/长时间未支付自动关单成功/关单成功/
    public static final String CODE_REFUND_SUCCESS = CODE_SUCCESS; // 表示通讯成功，其他详见状态码，调用退款查询接口明确退款情况
    public static final String CODE_ORDER_NOT_EXIST = "121336"; // 第三方订单信息不存在
    public static final String CODE_ORDER_NOT_REFUND = "121338"; // 该订单还未退款，请稍后查询
    public static final String CODE_ORDER_CLOSED_REFUND = "121337"; // 该订单已关闭请重新发起新退款订单
    public static final String CODE_REFUND_FAILED = "333333"; // 退款失败
    public static final String CODE_FIND_REFUND_FAILED = "000002"; // 查找退款订单失败

    // 支付方式常量
    public static final String PAY_WAY_UNION = "3"; // 银联
    public static final String PAY_WAY_ALIPAY = "2"; // 支付宝
    public static final String PAY_WAY_WECHAT = "1"; // 微信


    // 订单状态常量
    public static final String ORDER_STATUS_PAY_SUCCESS = "1"; // 支付成功
    public static final String ORDER_STATUS_DIGITAL_SUCCESS = "e"; // 数币成功
    public static final String ORDER_STATUS_REFUND_FAILED = "3"; // 退款失败
    public static final String ORDER_STATUS_REFUND_SUCCESS = "4"; // 退款成功
    public static final String ORDER_STATUS_REFUNDING = "5"; // 退款中
    public static final String ORDER_STATUS_CARD_CANCEL_SUCCESS = "7"; // 刷卡撤销成功
    public static final String ORDER_STATUS_PRE_AUTH_SUCCESS = "14"; // 预授权成功
    public static final String ORDER_STATUS_PRE_AUTH_CANCEL_SUCCESS = "16"; // 预授权撤销成功
    public static final String ORDER_STATUS_CARD_CANCELING = "98"; // 刷卡撤销中
    public static final String ORDER_STATUS_GUARANTEE_SUCCESS = "a"; // 担保成功
    public static final String ORDER_STATUS_GUARANTEE_CANCEL_SUCCESS = "c"; // 担保撤销成功

    // 卡类型常量
    public static final String CARD_TYPE_DEBIT = "01"; // 借记卡
    public static final String CARD_TYPE_CREDIT = "02"; // 贷记卡
    public static final String CARD_TYPE_OTHER = "03"; // 其他
    public static final String CARD_TYPE_PREPAID = "04"; // 预付卡

    // 交易类型常量
    public static final String TRAN_TYPE_SER_SCAN_CONSUME = "01"; // 扫码消费
    public static final String TRAN_TYPE_SER_SCAN_REFUND = "02"; // 扫码退款
    public static final String TRAN_TYPE_SER_CARD_CONSUME = "P1"; // 刷卡消费
    public static final String TRAN_TYPE_SER_CARD_REFUND = "P2"; // 刷卡退货
    public static final String TRAN_TYPE_SER_CARD_CANCEL = "03"; // 刷卡撤销
    public static final String TRAN_TYPE_SER_CARD_NO_SIGN = "04"; // 刷卡免签免密（业务已暂停后续开启另行通知）
    public static final String TRAN_TYPE_SER_CARD_FLASH_STANDARD = "05"; // 刷卡闪付标准类（业务已暂停后续开启另行通知）
    public static final String TRAN_TYPE_SER_CARD_FLASH_DISCOUNT = "06"; // 刷卡闪付优惠类
    public static final String TRAN_TYPE_SER_CARD_PRE_AUTH = "07"; // 刷卡预授权
    public static final String TRAN_TYPE_SER_CARD_PRE_AUTH_COMPLETE = "08"; // 刷卡预授权完成
    public static final String TRAN_TYPE_SER_CARD_PRE_AUTH_CANCEL = "09"; // 刷卡预授权撤销
    public static final String TRAN_TYPE_SER_CARD_PRE_AUTH_COMPLETE_CANCEL = "10"; // 刷卡预授权完成撤销
    public static final String TRAN_TYPE_SER_GUARANTEE = "11"; // 担保（扫码预授权）
    public static final String TRAN_TYPE_SER_GUARANTEE_COMPLETE = "12"; // 担保完成（扫码预授权完成）
    public static final String TRAN_TYPE_SER_GUARANTEE_CANCEL = "13"; // 担保撤销（扫码预授权撤销）
    public static final String TRAN_TYPE_SER_GUARANTEE_COMPLETE_CANCEL = "14"; // 担保完成撤销（扫码预授权完成撤销）
    public static final String TRAN_TYPE_SER_CARD_NO_SIGN_NO_DISCOUNT = "21"; // 刷卡免密免签未优惠
    public static final String TRAN_TYPE_SER_DIGITAL_CONSUME = "31"; // 数币消费
    public static final String TRAN_TYPE_SER_DIGITAL_REFUND = "32"; // 数币退款

    // 设备类型常量
    public static final String TYPE_SMART_POS = "P"; // 智能POS
    public static final String TYPE_APP_SCAN = "A"; // app扫码
    public static final String TYPE_PC = "C"; // PC端
    public static final String TYPE_TABLE_SCAN = "T"; // 台牌扫码

    // 优惠标识常量
    public static final String DISCOUNT_FLAG_NONE = "0"; // 无
    public static final String DISCOUNT_FLAG_AGENCY_SUBSIDY = "1"; // 机构补贴
    public static final String DISCOUNT_FLAG_COMPANY_SUBSIDY = "2"; // 公司补贴
    public static final String DISCOUNT_FLAG_LANDING_AGENCY_SUBSIDY = "11"; // 落地机构补贴

    // 限制支付常量
    public static final String LIMIT_PAY_NO_LIMIT = "0"; // 无限制
    public static final String LIMIT_PAY_NO_CREDIT = "1"; // 不能使用信用卡

    // 是否常量
    public static final String YES = "1"; // 是
    public static final String NO = "0"; // 否

    // 音箱播报开关常量
    public static final String SPEAKER_SWITCH_OFF = "0"; // 不播报
    public static final String SPEAKER_SWITCH_ON = "1"; // 播报

    // 限制openid常量
    public static final String LIMIT_OPENID_NO = "0"; // 否
    public static final String LIMIT_OPENID_YES = "1"; // 是

    // 二维码类型常量
    public static final String QR_CODE_TYPE_DYNAMIC = "0"; // 动态码
    public static final String QR_CODE_TYPE_STATIC = "1"; // 静态码

    // 食品订单类型常量
    public static final String FOOD_ORDER_TYPE_QR_ORDER = "qr_order"; // 店内扫码点餐
    public static final String FOOD_ORDER_TYPE_PRE_ORDER = "pre_order"; // 预点到店自提
    public static final String FOOD_ORDER_TYPE_HOME_DELIVERY = "home_delivery"; // 外送到家
    public static final String FOOD_ORDER_TYPE_DIRECT_PAYMENT = "direct_payment"; // 直接付款
    public static final String FOOD_ORDER_TYPE_OTHER = "other"; // 其他

    // 分期期数常量
    public static final int FQ_NUM_3 = 3; // 3期
    public static final int FQ_NUM_6 = 6; // 6期
    public static final int FQ_NUM_12 = 12; // 12期

    // 支付宝业务参数常量
    public static final String ZFB_CONSUME_OUT_SCENE_JIAYOU = "jiayou"; // 加油场景
    public static final String ZFB_JIAYOU_SOURCE_MAYIJIAYOU = "mayijiayou"; // 蚂蚁加油

    // 钱包通道常量
    public static final String WAL_CHANNEL_INDUSTRIAL_BANK = "24"; // 兴业银行数币通道

    // 支付类型常量（用于退款接口TAG字段）
    public static final String TAG_ALIPAY = "1"; // 支付宝
    public static final String TAG_WECHAT = "2"; // 微信
    public static final String TAG_UNION = "9"; // 银联
    public static final String TAG_DIGITAL = "12"; // 数币支付

    //关单
    public static final String CLOSE_FLAG_SUCCESS = "1"; // 关单成功
    public static final String CLOSE_FLAG_FAIL = "0"; // 非成功

    // 银联用户标识常量
    public static final String UNION_USER_ID_ANDROID = "0"; // ANDROID sdk
    public static final String UNION_USER_ID_IOS = "1"; // IOS sdk
    public static final String UNION_USER_ID_WINDOWS = "2"; // windows sdk
    public static final String UNION_USER_ID_DIRECT = "3"; // 直连

    // 版本号
    public static final String DEFAULT_VERSION = "1.0.0";

    // 退款失败状态码列表
    private static final List<String> REFUND_FAILED_CODE_LIST = Arrays.asList(
            CODE_ORDER_NOT_EXIST,       // 121336 第三方订单信息不存在
            CODE_ORDER_CLOSED_REFUND,   // 121337 该订单已关闭请重新发起新退款订单
            CODE_REFUND_FAILED,         // 333333 退款失败
            CODE_FIND_REFUND_FAILED    // 000002 查找退款订单失败
    );

    /**
     * 是否退款失败
     *
     * @param code        响应码
     * @param orderStatus 订单状态
     * @return 是否为退款失败状态
     */
    public static boolean isRefundFailed(String code, String orderStatus) {
        return REFUND_FAILED_CODE_LIST.contains(code)
                || ORDER_STATUS_REFUND_FAILED.equals(orderStatus);
    }

    /**
     * 是否退款成功
     *
     * @param code        响应码
     * @param orderStatus 订单状态
     * @return　是否为退款成功状态
     */
    public static boolean isRefundSuccess(String code, String orderStatus) {
        return CODE_REFUND_SUCCESS.equals(code)
                || ORDER_STATUS_REFUND_SUCCESS.equals(orderStatus);
    }
}
