package com.wosai.mpay.api.guotong;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @ClassName: GuotongRequestBuilder
 * @Description: 国通支付请求构建器，用于构建请求参数
 * @Auther: 
 * @Date: 2025/4/22
 */
public class GuotongRequestBuilder {

    // 请求参数Map
    private Map<String, Object> request;

    /**
     * 构造函数，初始化请求参数Map
     */
    public GuotongRequestBuilder() {
        request = new LinkedHashMap<String, Object>();
    }

    /**
     * 设置请求参数
     * @param field 参数字段名
     * @param value 参数值
     */
    public void set(String field, Object value) {
        request.put(field, value);
    }

    /**
     * 构建请求参数Map
     * @return 请求参数Map
     */
    public Map<String, Object> build() {
        return request;
    }
}
