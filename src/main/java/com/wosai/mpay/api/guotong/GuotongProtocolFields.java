package com.wosai.mpay.api.guotong;

/**
 * 国通协议字段类
 * 包含请求和响应共用的字段
 */
public class GuotongProtocolFields {
    // 通用字段
    public static final String AGET_ID = "agetId"; // 商户所在国通系统内机构号（或虚拟机构号）
    public static final String CUST_ID = "custId"; // 商户所在国通系统内商户号
    public static final String ORDER_NO = "orderNo"; // 订单号，该订单号需要唯一，最大长度40，只能由数字，大小写字母构成
    public static final String TXAMT = "txamt"; // 单位分，国通系统内记录至流水（原订单交易金额）
    public static final String TRADING_IP = "tradingIp"; // 商户端终端设备 IP 地址，需传公网ip
    public static final String ORDER_TIME = "orderTime"; // 订单时间，格式yyyyMMddHHmmss
    public static final String REMARK = "remark"; // 备注信息
    public static final String TIME_STAMP = "timeStamp"; // 时间戳，格式yyyyMMddHHmmss
    public static final String VERSION = "version"; // 版本号，默认 1.0.0
    public static final String SIGN = "sign"; // 签名
    public static final String CODE = "code"; // 响应码，000000表示成功
    public static final String MSG = "msg"; // 响应消息
    public static final String CLOSE_FLAG = "closeFlag"; //	关单成功标识 1成功0非成功

    // 支付相关共用字段
    public static final String THREE_ORDER_NO = "threeOrderNo"; // 合作方订单号
    public static final String CANCEL_THREE_ORDER_NO = "threeOrderNO"; // 撤单时使用合作方订单号
    public static final String OPEN_ID = "openId"; // 用户标识，Appid对应的消费者openid
    public static final String SUB_OPEN_ID = "subOpenId"; // 子用户标识，subappid对应的subopenid
    public static final String SERVER_THREE_ORDER_NO = "serverThreeOrderNo"; // 用于国通设备非接口交易时异步订单生成
    public static final String CARD_TYPE = "cardType"; // 卡类型，01借记卡、02贷记卡、03其他
    public static final String BANK_CODE = "bankCode"; // 银行代码
    public static final String BANK_NAME = "bankName"; // 银行名称
    public static final String PAY_NO = "payNo"; // 支付编号
    public static final String ACTUAL_PAY_AMT = "actualPayAmt"; // 单位分，买家实付金额

    // 优惠相关共用字段
    public static final String DISCOUNT_FLAG = "discountFlag"; // 优惠标识，0无、1机构补贴、2公司补贴、11落地机构补贴
    public static final String DISCOUNT_FEE = "discountFee"; // 优惠手续费=应收手续费-实收手续费
    public static final String CUST_AMT = "custAmt"; // 单位分，商户优惠金额
    public static final String OTHER_AMT = "otherAmt"; // 单位分，其他出资方出资金额
    public static final String DIS_INFO = "disInfo"; // 优惠券信息
    public static final String DIS_TXAMT = "disTxamt"; // 单位分，实际优惠金额
    public static final String CODE_AMT = "codeAmt"; // 单位分，优惠券面额

    // 分期相关共用字段
    public static final String HB_FQ_NUM = "hbFqNum"; // 花呗分期期数, 当前仅支持3、6、12
    public static final String CR_FQ_NUM = "crFqNum"; // 信用卡分期数，当前支持传入 3、6、12
    public static final String JDBT_FQ_NUM = "jdbtFqNum"; // 京东白条分期数，当前仅支持3、6、12

    // 退款相关共用字段
    public static final String REFUND_DIS_AMT = "refundDisAmt"; // 单位分，退款优惠金额
    public static final String OLD_TORDER_NO = "oldTOrderNo"; // 原订单号

    // 设备相关共用字段
    public static final String DEVICE_NO = "deviceNo"; // 设备号
    public static final String DRIVE_NO = "driveNo"; // 字母或数字，长度8位数字，由合作方自定义
    public static final String TERNO = "terno"; // 终端号

    // 交易类型相关共用字段
    public static final String PAY_WAY = "payWay"; // 支付方式
    public static final String PAY_CHANNEL = "payChannel"; // 支付渠道
    public static final String TRAN_TYPE_SER = "tranTypeSer"; // 交易类型
    public static final String ORDER_TYPE = "orderType"; // 订单类型
    public static final String ORDER_STATUS = "orderStatus"; // 订单状态

    // 批次相关共用字段
    public static final String BAT_NO = "batNo"; // 批次号
    public static final String SREF_NO = "srefNo"; // 参考号

    // 异步通知响应字段
    public static final String NOTIFY_THREE_ORDER_NO = "THREE_ORDER_NO"; // 合作方生成交易的第三方订单号（大写）
}
