package com.wosai.mpay.api.guotong;

/**
 * 国通响应字段类
 * 包含所有响应参数字段
 */
public class GuotongResponseFields {
    // 通用响应字段
    public static final String CODE = GuotongProtocolFields.CODE; // 响应码，000000表示成功
    public static final String MSG = GuotongProtocolFields.MSG; // 响应消息
    public static final String DATA = "data"; // 响应数据

    // 数据字段
    public static final String AGET_ID = GuotongProtocolFields.AGET_ID; // 商户所在国通系统内机构号（或虚拟机构号）
    public static final String THREE_ORDER_NO = GuotongProtocolFields.THREE_ORDER_NO; // 合作方订单号
    public static final String NETR_AMT = "netrAmt"; // 单位分，原订单交易金额-官方商户出资金额-商户交易实收手续费
    public static final String TXAMT = GuotongProtocolFields.TXAMT; // 单位分，交易金额
    public static final String CUST_FEE = "custFee"; // 单位分，商户交易实收手续费
    public static final String OLD_FEE = "oldFee"; // 单位分，原手续费
    public static final String DISCOUNT_FEE = GuotongProtocolFields.DISCOUNT_FEE; // 单位分，优惠手续费=应收手续费-实收手续费
    public static final String CUST_AMT = GuotongProtocolFields.CUST_AMT; // 单位分，商户优惠金额
    public static final String WX_AGET_SUBSIDY = "wxAgetSubsidy"; // 单位分，微信代理商补贴
    public static final String OTHER_AMT = GuotongProtocolFields.OTHER_AMT; // 单位分，其他出资方出资金额
    public static final String ORDER_NO = GuotongProtocolFields.ORDER_NO; // 国通平台订单号
    public static final String TORDER_NO = "torderNo"; // 微信/支付宝（目的通道流水号），银联（官方订单号）
    public static final String OPEN_ID = GuotongProtocolFields.OPEN_ID; // Appid对应的消费者openid
    public static final String SUB_OPEN_ID = GuotongProtocolFields.SUB_OPEN_ID; // subappid对应的subopenid
    public static final String TRADING_IP = GuotongProtocolFields.TRADING_IP; // 交易IP
    public static final String ORDER_TIME = GuotongProtocolFields.ORDER_TIME; // 订单时间，格式yyyyMMddHHmmss
    public static final String CARD_TYPE = GuotongProtocolFields.CARD_TYPE; // 卡类型，01借记卡、02贷记卡、03其他
    public static final String DISCOUNT_FLAG = GuotongProtocolFields.DISCOUNT_FLAG; // 优惠标识，0无、1机构补贴、2公司补贴、11落地机构补贴
    public static final String SERVER_THREE_ORDER_NO = GuotongProtocolFields.SERVER_THREE_ORDER_NO; // 用于国通设备，非接口交易时，异步订单生成
    public static final String BANK_CODE = GuotongProtocolFields.BANK_CODE; // 银行代码
    public static final String BANK_NAME = GuotongProtocolFields.BANK_NAME; // 银行名称
    public static final String PAY_NO = GuotongProtocolFields.PAY_NO; // 支付编号
    public static final String PROMOTION_DETAIL = "promotionDetail"; // 优惠详情
    public static final String ACQ_ADDN_DATA = "acqAddnData"; // 收单附加数据
    public static final String ACTUAL_PAY_AMT = GuotongProtocolFields.ACTUAL_PAY_AMT; // 单位分，买家实付金额

    // 微信支付响应字段
    public static final String GET_PREPAY_ID = "getPrepayId"; // 为1时为可支付，其它表示交易未知
    public static final String PRE_PAY_ID = "prePayId"; // 根据返回ID调用官方接口唤起支付框
    public static final String JSAPI_APPID = "jsapiAppid"; // JSAPI应用ID
    public static final String JSAPI_TIMESTAMP = "jsapiTimestamp"; // JSAPI时间戳
    public static final String JSAPI_NONCESTR = "jsapiNoncestr"; // JSAPI随机字符串
    public static final String JSAPI_PACKAGE = "jsapiPackage"; // JSAPI包
    public static final String JSAPI_SIGN_TYPE = "jsapiSignType"; // JSAPI签名类型
    public static final String JSAPI_PAY_SIGN = "jsapiPaySign"; // JSAPI支付签名

    // 支付宝支付响应字段
    public static final String GETPREPAYID = "getprepayid"; // 为1时为可支付，其它表示交易未知
    public static final String PREPAYID = "prepayid"; // 根据返回ID调用官方接口唤起支付框

    // 银联支付响应字段
    public static final String REDIRECT_URL = "redirecturl"; // 重定向URL
    public static final String USER_ID = "userId"; // 用于基础支付板块-统一下单接口（C扫B）内）"openid"字段
    public static final String ATQ_TAG = "atqTag"; // 0走ATQ通道、1银联小微、2开放平台，用于基础支付板块-统一下单接口（C扫B）内）"atqTag"字段

    // 京东白条支付响应字段
    public static final String PAY_URL = "payUrl"; // 支付URL

    // 交易查询响应字段
    public static final String PAY_WAY = GuotongProtocolFields.PAY_WAY; // 支付方式
    public static final String LIMIT_FEE = "limitFee"; // 单位：分，限额手续费
    public static final String HB_FQ_NUM = GuotongProtocolFields.HB_FQ_NUM; // 花呗分期数，当前仅支持3、6、12
    public static final String CR_FQ_NUM = GuotongProtocolFields.CR_FQ_NUM; // 信用卡分期数，当前仅支持3、6、12
    public static final String JDBT_FQ_NUM = GuotongProtocolFields.JDBT_FQ_NUM; // 京东白条分期数，当前仅支持3、6、12
    public static final String PAY_CHANNEL = GuotongProtocolFields.PAY_CHANNEL; // 支付渠道
    public static final String MERC_ID = "mercId"; // 商户ID
    public static final String SREF_NO = GuotongProtocolFields.SREF_NO; // 参考号
    public static final String BAT_NO = GuotongProtocolFields.BAT_NO; // 批次号
    public static final String TXN_RSV1 = "txnRsv1"; // 交易保留字段1
    public static final String OLD_ORDER_NO = GuotongProtocolFields.OLD_TORDER_NO; // 原订单号
    public static final String TRAN_TYPE_SER = GuotongProtocolFields.TRAN_TYPE_SER; // 交易类型
    public static final String DEVICE_NO = GuotongProtocolFields.DEVICE_NO; // 设备号
    public static final String MEDIATYPE = "mediatype"; // 介质类型
    public static final String DIS_INFO = GuotongProtocolFields.DIS_INFO; // 优惠信息
    public static final String DIS_TXAMT = GuotongProtocolFields.DIS_TXAMT; // 优惠交易金额
    public static final String CODE_AMT = GuotongProtocolFields.CODE_AMT; // 优惠券金额
    public static final String TERNO = GuotongProtocolFields.TERNO; // 终端号

    // 退款响应字段
    public static final String ORDER_FLOW_NO = "orderFlowNo"; // 订单流水号
    public static final String REFUND_AMT = "refundAmt"; // 退款金额
    public static final String ORDER_STATUS = GuotongProtocolFields.ORDER_STATUS; // 订单状态
    public static final String REFUND_FEE = "refundFee"; // 退款手续费
    public static final String REFUND_DIS_AMT = GuotongProtocolFields.REFUND_DIS_AMT; // 退款优惠金额
    public static final String RE_AGET_SUBSIDY = "reAgetSubsidy"; // 代理商退款补贴
    public static final String RE_CUST_AMT = "reCustAmt"; // 商户退款金额
    public static final String RESERVE_FLAG = "reserveFlag"; // 保留标志
    public static final String OLD_THREE_ORDER_NO = "oldThreeOrderNo"; // 原第三方订单号


    //异步通知
    public static final String RSP_COD = "rspCod";
    public static final String RSP_MSG = "rspMsg";

    // 优惠详情字段
    public static final String PROMOTION_DETAIL_LIST = "promotion_detail"; // 优惠详情列表
    public static final String PROMOTION_ID = "promotion_id"; // 优惠ID
    public static final String NAME = "name"; // 优惠名称
    public static final String SCOPE = "scope"; // 优惠范围
    public static final String TYPE = "type"; // 优惠类型
    public static final String AMOUNT = "amount"; // 优惠金额
    public static final String ACTIVITY_ID = "activity_id"; // 活动ID
    public static final String WXPAY_CONTRIBU = "wxpay_contribu"; // 微信支付贡献
    public static final String MERCHANT_CONTRIBUTE = "merchant_contribute"; // 商户贡献
    public static final String OTHER_CONTRIBUTE = "other_contribute"; // 其他贡献
    public static final String GOODS_DETAIL = "goods_detail"; // 商品详情
    public static final String GOODS_ID = "goods_id"; // 商品ID
    public static final String GOODS_REMARK = "goods_remark"; // 商品备注
    public static final String DISCOUNT_AMOUNT = "discount_amount"; // 优惠金额
    public static final String QUANTITY = "quantity"; // 数量
    public static final String PRICE = "price"; // 价格

    // 收单附加数据字段
    public static final String GOODS_INFO = "goodsInfo"; // 商品信息
    public static final String ID = "id"; // 商品ID
    public static final String CATEGORY = "category"; // 类别

    // 星POS项目返回值（专属）
    public static final String TOTAL_AMOUNT = "total_amount"; // 总金额
    public static final String GOODS_TAG = "goodsTag"; // 商品标签
    public static final String SUBJECT = "subject"; // 主题
    public static final String REFUND_INNFO = "refundInnfo"; // 退款信息
    public static final String ATTACH = "attach"; // 附加信息
    public static final String TXN_CNL = "txn_cnl"; // 交易渠道
}
