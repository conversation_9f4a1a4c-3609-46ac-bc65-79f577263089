package com.wosai.mpay.api.macaupass.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className MacauPassDiscountTypeEnum
 * @description:
 * @create: 2025-04-27 10:18
 **/
public enum MacauPassDiscountTypeEnum {
    MINUS("Minus", "立减"),
    DISCOUNT("Discount","折扣"),
    GIFT("Gift","赠送"),
    RANDOM_MINUS("RandomMinus","随机立减");

    private final String type;
    private final String desc;

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    MacauPassDiscountTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static MacauPassDiscountTypeEnum of(String type) {
        if (null == type) {
            return null;
        }
        for (MacauPassDiscountTypeEnum e : MacauPassDiscountTypeEnum.values()) {
            if (e.type.equals(type)) {
                return e;
            }
        }
        return null;
    }
}


