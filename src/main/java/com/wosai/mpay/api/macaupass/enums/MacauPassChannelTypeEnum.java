package com.wosai.mpay.api.macaupass.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className MacauPassChannelTypeEnum
 * @description: 渠道类型
 * @create: 2025-04-27 10:23
 **/
public enum MacauPassChannelTypeEnum {

    SYSTEM("1", "系统"),
    SMART_DEVICE("2","智能设备"),
    OTHER("3","其他");

    private final String type;
    private final String desc;

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    MacauPassChannelTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static MacauPassChannelTypeEnum of(String type) {
        if (null == type) {
            return null;
        }
        for (MacauPassChannelTypeEnum e : MacauPassChannelTypeEnum.values()) {
            if (e.type.equals(type)) {
                return e;
            }
        }
        return null;
    }
}
