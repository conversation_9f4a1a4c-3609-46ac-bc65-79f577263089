package com.wosai.mpay.api.macaupass.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className MacauPassTradeStatusEnum
 * @description:
 * @create: 2025-04-28 18:14
 **/
public enum MacauPassTradeStatusEnum {
    UNKNOW("UNKNOW", "交易創建，等待買家付款"),
    SUCCESS("SUCCESS", "交易支付成功"),
    FAILED("FAILED", "交易支付失敗"),
    CLOSED("CLOSED", "未付款交易超時關閉,或支付完成後全額退款");

    private final String code;
    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    MacauPassTradeStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MacauPassTradeStatusEnum of(String code) {
        if (null == code) {
            return null;
        }
        for (MacauPassTradeStatusEnum e : MacauPassTradeStatusEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }

    /**
     * 交易是否完成
     *
     * @param status
     * @return
     */
    public static boolean isTradeCompleted(String status) {
        return MacauPassTradeStatusEnum.SUCCESS.getCode().equals(status) || MacauPassTradeStatusEnum.FAILED.getCode().equals(status) || MacauPassTradeStatusEnum.CLOSED.getCode().equals(status);
    }


    /**
     * 交易是否成功
     *
     * @param status
     * @return
     */
    public static boolean isTradeSuccess(String status) {
        return MacauPassTradeStatusEnum.SUCCESS.getCode().equals(status);
    }


    /**
     * 交易是否失败
     *
     * @param status
     * @return
     */
    public static boolean isTradeFailed(String status) {
        return MacauPassTradeStatusEnum.FAILED.getCode().equals(status);
    }


    /**
     * 交易是否关闭
     *
     * @param status
     * @return
     */
    public static boolean isTradeClosed(String status) {
        return MacauPassTradeStatusEnum.CLOSED.getCode().equals(status);
    }

    /**
     * 交易是否未知
     *
     * @param status
     * @return
     */
    public static boolean isTradeUnknow(String status) {
        return MacauPassTradeStatusEnum.UNKNOW.getCode().equals(status);
    }
}