package com.wosai.mpay.api.macaupass.constants;

import com.wosai.mpay.api.macaupass.enums.MacauPassErrorCodeEnum;
import com.wosai.mpay.api.macaupass.enums.MacauPassSystemErrorEnum;

import java.util.Arrays;
import java.util.List;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className MacauPassProtocolFieldsConstants
 * @description:
 * @create: 2025-04-27 10:47
 **/
public class MacauPassProtocolFieldsConstants {

    /**
     * service (String): 接口名称
     * 必填：是 (N)
     * 示例值：mpay.trade.xxx。使用不同的接口名称，对应请求业务字段有所区别，详见第七章
     */
    public static final String SERVICE = "service";
    /**
     * org_id (String(16)): 机构ID
     * 必填：是 (N)
     * 示例值：88808534816
     */
    public static final String ORG_ID = "org_id";
    /**
     * channel_type (String): 渠道类型 (1-系统 2-智能设备 3-其他)
     * 必填：是 (N)
     * 示例值：1
     */
    public static final String CHANNEL_TYPE = "channel_type";
    /**
     * sign_type (String): 签名方式
     * 必填：是 (N)
     * 示例值：RSA2
     */
    public static final String SIGN_TYPE = "sign_type";
    /**
     * sign (String): 签名
     * 必填：是 (N)
     * 请参阅签名规则说明
     */
    public static final String SIGN = "sign";
    /**
     * format (String): 请求数据格式，仅支持JSON
     * 必填：是 (N)
     * 示例值：JSON
     */
    public static final String FORMAT = "format";
    /**
     * timestamp (String): 商户服务器发送请求的时间戳记，精确到毫秒。Unix时间戳格式
     * 必填：是 (N)
     * 示例值：1532654894
     */
    public static final String TIMESTAMP = "timestamp";
    /**
     * nonce_str (String(128)): 随机字符串
     * 必填：是 (N)
     * 示例值：WAx4XSsF24
     */
    public static final String NONCE_STR = "nonce_str";
    /**
     * version (String): 版本号，最新版为1.1.2
     * 必填：是 (N)
     * 示例值：2025-01-07更新版本1.1.2，该版本增加异步通知、返回出参中coupon出资来源金额。2024-07-24更新版本1.1.1，该版本增加补充异步通知返回参数，并增加条码支付异步通知功能。1.0.7以上仍可继续使用；
     */
    public static final String VERSION = "version";


    /**
     * 应用级请求参数
     */
    public static final String PARAMETERS = "parameters";


    /**
     * 协议默认值
     */
    public static final String DEFAULT_APPLICATION_FORMAT = "JSON"; //默认的应用级参数格式
    public static final String DEFAULT_API_VERSION = "1.1.2"; //默认的接口版本
    public static final String DEFAULT_SIGN_TYPE = "RSA2"; //签名方法
    public static final String DEFAULT_SIGN_ALGORITHM = "SHA256WithRSA"; //默认的签名算法
    public static final String DEFAULT_ENCRYPT_ALGORITHM = "DESede"; //默认的加解密算法
    public static final String DEFAULT_CONTENT_TYPE = "application/json"; //默认的content type
    public static final String DEFAULT_CHARSET = "utf-8"; //默认的content type


    /**
     * 扫码类型
     */
    public static final String BARCODE = "barcode";

    /**
     * 日期格式
     */
    public static final String DATE_SIMPLE_REQUEST_FORMAT = "yyyyMMddHHmmss";
    public static final String DATE_SIMPLE_RESPONSE_FORMAT = "yyyy-MM-dd HH:mm:ss";


    public static final List<String> CODE_SUCCESS_LIST = Arrays.asList(MacauPassSystemErrorEnum.SUCCESS.getCode(), MacauPassErrorCodeEnum.SUCCESS.getCode());
}
