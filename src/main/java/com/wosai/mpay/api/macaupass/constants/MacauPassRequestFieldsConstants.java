package com.wosai.mpay.api.macaupass.constants;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className MacauPassRequestFieldsConstants
 * @description:
 * @create: 2025-04-27 10:42
 **/
public class MacauPassRequestFieldsConstants {


    /**
     * QUANTITY (String): 商品數量  注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：1
     */
    public static final String QUANTITY = "quantity";
    /**
     * TRANS_NAME (String(256)): 將在交易記錄清單中顯示的交易名稱。
     * 必填：否 (N)
     * 示例值：iphone
     */
    public static final String TRANS_NAME = "trans_name";
    /**
     * OUT_TRANS_ID (String(64)): 商戶訂單系統的訂單ID,商戶應保證其在系統中的唯一性,建議不小於16位
     * 必填：否 (N)
     * 示例值：20230117162501
     */
    public static final String OUT_TRANS_ID = "out_trans_id";
    /**
     * CURRENCY (String(8)): 交易貨幣,默認MOP
     * 必填：否 (N)
     * 示例值：MOP
     */
    public static final String CURRENCY = "currency";
    /**
     * TRANS_AMOUNT (String): 交易貨幣的交易金額(單位元); 範圍：0.1-100000000.00。小數點後兩位數。 注意：此处类型修改为String
     * 必填：否 (N)
     * 示例值：2000
     */
    public static final String TRANS_AMOUNT = "trans_amount";
    /**
     * BUYER_IDENTITY_CODE (String(32)): 用作第三方支付渠道的使用者的標識。這是個動態值,必須即時讀取用戶的第三方支付渠道錢包
     * 必填：否 (N)
     * 示例值：88011004150109004648
     */
    public static final String BUYER_IDENTITY_CODE = "buyer_identity_code";
    /**
     * IDENTITY_CODE_TYPE (String(16)): 掃碼類型
     * 必填：否 (N)
     * 示例值：barcode
     */
    public static final String IDENTITY_CODE_TYPE = "identity_code_type";
    /**
     * TRANS_CREATE_TIME (String(16)): 商戶系統創建交易的時間。格式YYYYMMDDHHMMSS
     * 可选：是 (Y)
     * 示例值：20230117162701
     */
    public static final String TRANS_CREATE_TIME = "trans_create_time";
    /**
     * NOTIFY_URL (String(190)): 澳門通系統服務器主動通知商戶系統指定的http/https路徑。
     * 可选：是 (Y)
     * 示例值：http://api.test.pay.net/atinterface/receive_notify.html
     */
    public static final String NOTIFY_URL = "notify_url";
    /**
     * MEMO (String(256)): 交易備註
     * 可选：是 (Y)
     * 示例值：iphone cellphone
     */
    public static final String MEMO = "memo";
    /**
     * OPERATOR_ID (String(28)): 操作員ID
     * 可选：是 (Y)
     * 示例值：Yx_001
     */
    public static final String OPERATOR_ID = "operator_id";
    /**
     * EXTEND_PARAMS (String): 該參數包含請求的擴展參數，包含商家的基本資訊。詳情見下描述 注意：此处类型修改为String
     * 必填：否 (N)
     * 示例值：{"sub_merchant_name":"apple零售商戶","sub_merchant_id":"888535065254305","sub_merchant_industry":"5065","store_id":"8885350652543050001","store_name":"apple零售門店"}
     */
    public static final String EXTEND_PARAMS = "extend_params";
    /**
     * SUB_MERCHANT_ID (String): 二級商戶ID 注意：此处类型修改为String
     * 必填：否 (N)
     * 示例值：888535065254305
     */
    public static final String SUB_MERCHANT_ID = "sub_merchant_id";
    /**
     * SUB_MERCHANT_NAME (String): 二級商户名称。只有在跨境代理机构获取模式下才能为空，并且验证名称。注意：此处类型修改为String
     * 必填：否 (N)
     * 示例值：apple零售商戶
     */
    public static final String SUB_MERCHANT_NAME = "sub_merchant_name";
    /**
     * SUB_MERCHANT_INDUSTRY (String): 商戶行業代碼,有关MCC代码定义，请参阅MCC列表 注意：此处类型修改为String
     * 必填：否 (N)
     * 示例值：5065
     */
    public static final String SUB_MERCHANT_INDUSTRY = "sub_merchant_industry";
    /**
     * STORE_NAME (String): 门店名称。仅当商店信息被验证时才可以为空。 注意：此处类型修改为String
     * 必填：否 (N)
     * 示例值：apple零售門店
     */
    public static final String STORE_NAME = "store_name";
    /**
     * STORE_ID (String): 门店ID 注意：此处类型修改为String
     * 必填：否 (N)
     * 示例值：8885350652543050001
     */
    public static final String STORE_ID = "store_id";
    /**
     * TERMINAL_ID (String): 终端ID 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：T80001
     */
    public static final String TERMINAL_ID = "terminal_id";


    /**
     * TRANS_ID (String(64)): 澳門通系統的交易流水號。至少16位，最多64位。
     * 可选：是 (Y)  （特殊：如果同时传输 out_trans_id 和 trans_id，则使用 trans_id）
     * 示例值：20230117150911
     */
    public static final String TRANS_ID = "trans_id";



    /**
     * OUT_REFUND_ID (String(64)): 商戶系統上的退款訂單ID，建議不小於16位。不能與out_trans_id相同，out_refund_id與org_id 一起確定退款交易
     * 必填：否 (N)
     * 示例值：R20230117162503001
     */
    public static final String OUT_REFUND_ID = "out_refund_id";
    /**
     * REFUND_AMOUNT (String): 不可超過原交易金額且≥0.10 (單位元)  注意：此处类型修改为String
     * 必填：否 (N)
     * 示例值：200.00
     */
    public static final String REFUND_AMOUNT = "refund_amount";
    /**
     * REFUND_REASON (String(128)): 退款的原因
     * 可选：是 (Y)
     * 示例值：退款
     */
    public static final String REFUND_REASON = "refund_reason";

    /**
     * SUBJECT (String(127)): 商品名稱/交易名稱/訂單主題/訂單關鍵字等。
     * 必填：是 (N)
     * 示例值：iphone
     */
    public static final String SUBJECT = "subject";
    /**
     * TOTAL_FEE (String): 幣種此訂單的總費用(單位元)。值的範圍是[0.10,5000.00]，該值可以在小數點後最多有兩位數。 注意：此处类型修改为String
     * 必填：是 (N)
     * 示例值：5000.00
     */
    public static final String TOTAL_FEE = "total_fee";
    /**
     * BODY (String(64)): 交易的具體描述。，不可包含特殊字符 ~ ! @ # $ % & * ( ) _+/ \ | )”等，注意，該值內中文字符長度超出36會引起微信交易失敗
     * 必填：是 (N)
     * 示例值：iphone cellphone
     */
    public static final String BODY = "body";
    /**
     * PRICE (String): 訂單中貨物的單價(單位元)。如果此參數是根據請求發送的,則必須滿足total_fee = price×quantity的條件。 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：1
     */
    public static final String PRICE = "price";
    /**
     * SHOW_URL (String(400)): 在结账柜台的网页上显示商品的超链接
     * 可选：是 (Y)
     * 示例值：http://www.taobao.com/product/113714.html
     */
    public static final String SHOW_URL = "show_url";
    /**
     * GOODS_DETAIL (String): 商品的詳細資訊，最大允許貨物數量為50。詳情見下描述 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：[{"goods_id":"apple-01","goods_name":" iphone","goods_category":"33","price":"5000.00","quantity":"1"}]
     */
    public static final String GOODS_DETAIL = "goods_detail";
    /**
     * IT_B_PAY (String(200)): 設置不付款的超時時間，交易時間到期後自動關閉。值範圍：正掃3m-2h。m分鐘，h小時。該參數的數值的小數點被拒絕，例如，1.5h可以轉化為90m。
     * 可选：是 (Y)
     * 示例值：3m
     */
    public static final String IT_B_PAY = "it_b_pay";
    /**
     * PASSBACK_PARAMETERS (String(256)): 如果商戶通過請求字段傳輸此參數，澳門通系統將通過非同步通知（參數名稱：extra_common_param）返回此參數。
     * 可选：是 (Y)
     * 示例值：test
     */
    public static final String PASSBACK_PARAMETERS = "passback_parameters";

    /**
     * RETURN_URL (String(400)): 支付完成后跳轉的頁面地址
     * 可选：是 (Y)
     * 示例值：http://www.taobao.com/product/113714.html
     */
    public static final String RETURN_URL = "return_url";
    // ------------------  嵌套的 GoodsDetail  参数  -----------------------
    /**
     * GOODS_DETAIL_GOODS_ID (String): 商品ID  多行时该参数不可重复
     * 必填：是 (N)  (GoodsDetail 数组中的元素)
     * 示例值：apple-01
     */
    public static final String GOODS_DETAIL_GOODS_ID = "goods_id";
    /**
     * GOODS_DETAIL_GOODS_NAME (String): 商品名稱
     * 必填：是 (N)  (GoodsDetail 数组中的元素)
     * 示例值：iphone
     */
    public static final String GOODS_DETAIL_GOODS_NAME = "goods_name";
    /**
     * GOODS_DETAIL_GOODS_CATEGORY (String): 商品類別
     * 可选：是 (Y)  (GoodsDetail 数组中的元素)
     * 示例值：33
     */
    public static final String GOODS_DETAIL_GOODS_CATEGORY = "goods_category";
    /**
     * GOODS_DETAIL_SHOW_URL (String): 在結帳櫃檯的網頁上顯示商品的超連結。
     * 可选： 是 (Y)  (GoodsDetail 数组中的元素)
     * 示例值：http://www.taobao.com
     */
    public static final String GOODS_DETAIL_SHOW_URL = "show_url";
    /**
     * GOODS_DETAIL_QUANTITY (String): 商品數量
     * 必填：是 (N)  (GoodsDetail 数组中的元素)
     * 示例值：1
     */
    public static final String GOODS_DETAIL_QUANTITY = "quantity";
    /**
     * GOODS_DETAIL_BODY (String): 商品簡介
     * 可选：是 (Y)  (GoodsDetail 数组中的元素)
     * 示例值：Cellphone at a sale
     */
    public static final String GOODS_DETAIL_BODY = "body";
    /**
     * GOODS_DETAIL_PRICE (String): 商品單價 注意：此处类型修改为String
     * 必填：是 (N)  (GoodsDetail 数组中的元素)
     * 示例值：5000.00
     */
    public static final String GOODS_DETAIL_PRICE = "price";
    // ------------------  嵌套的 ExtendParams  参数  -----------------------
    /**
     * EXTEND_PARAMS_SUB_MERCHANT_ID (String): 二級商戶ID
     * 必填：是 (N)  (ExtendParams 对象中的元素)
     * 示例值：888535065254305
     */
    public static final String EXTEND_PARAMS_SUB_MERCHANT_ID = "sub_merchant_id";
    /**
     * EXTEND_PARAMS_SUB_MERCHANT_NAME (String): 二級商户名称。只有在跨境代理机构获取模式下才能为空，并且验证名称。
     * 必填：是 (N)  (ExtendParams 对象中的元素)
     * 示例值：apple零售商戶
     */
    public static final String EXTEND_PARAMS_SUB_MERCHANT_NAME = "sub_merchant_name";
    /**
     * EXTEND_PARAMS_SUB_MERCHANT_INDUSTRY (String): 商戶行業代碼,有关MCC代码定义，请参阅MCC列表
     * 必填：是 (N)  (ExtendParams 对象中的元素)
     * 示例值：5065
     */
    public static final String EXTEND_PARAMS_SUB_MERCHANT_INDUSTRY = "sub_merchant_industry";
    /**
     * EXTEND_PARAMS_STORE_NAME (String): 门店名称。仅当商店信息被验证时才可以为空。
     * 必填：是 (N)  (ExtendParams 对象中的元素)
     * 示例值：apple零售門店
     */
    public static final String EXTEND_PARAMS_STORE_NAME = "store_name";
    /**
     * EXTEND_PARAMS_STORE_ID (String): 门店ID
     * 必填：是 (N)  (ExtendParams 对象中的元素)
     * 示例值：8885350652543050001
     */
    public static final String EXTEND_PARAMS_STORE_ID = "store_id";
    /**
     * EXTEND_PARAMS_TERMINAL_ID (String): 终端ID
     * 可选：是 (Y)  (ExtendParams 对象中的元素)
     * 示例值：T80001
     */
    public static final String EXTEND_PARAMS_TERMINAL_ID = "terminal_id";


    /**
     * PAY_CHANNEL (String(64)): 支付渠道
     * 必填：是 (N)
     * 枚舉值：mpay:澳門錢包,alipay:支付寶,wechat:微信
     */
    public static final String PAY_CHANNEL = "pay_channel";
    /**
     * PRODUCT_CODE (String(32)): 产品代码,仅MPay小程序內下單時傳MP_MINIAPP_PAY，其他情況傳MP_JSAPI_PAY
     * 必填：是 (N)
     * 枚舉值：MP_MINIAPP_PAY,MP_JSAPI_PAY
     */
    public static final String PRODUCT_CODE = "product_code";
    /**
     * SUB_APPID (String(128)): 商戶子appid,pay_channel=wechat時不可為空,pay_channel=mpay且product_code=MP_MINIAPP_PAY時不可為空
     * 可选：是 (Y)
     * 示例值：wx034235123235
     */
    public static final String SUB_APPID = "sub_appid";
    /**
     * SCENE (String(16)): 場景pay_channel=mpay且product_code=MP_MINIAPP_PAY時不可為空
     * 可选：是 (Y)
     * 枚舉值：ONLINE
     */
    public static final String SCENE = "scene";
    /**
     * USER_ID (String(256)): 支付渠道app對應的用戶標識，獲取方式見 JSAPI支付-流程描述
     * 必填：是 (N)
     * 示例值：208829412617312
     */
    public static final String USER_ID = "user_id";

    /**
     * BIZ_TYPE (String(16)): 業務類型 0005: 現金充值
     * 必填：是 (N)
     * 示例值：0005
     */
    public static final String BIZ_TYPE = "biz_type";

    /**
     * RISK_INFO (Object(2000)): 風控信息，詳見risk_info描述
     * 可选：是 (Y)
     * 示例值：{"session_id":"26dxxxxxxxxxxxxxxxxxxxxxxxxx03e9","serial_no":"f0xxxc9a","client_ip":"************","latitude_longitude":"39.9151190000,116.4039630000","idfa":"5D08BADB6-B7D1-46DE-BDAB-B66468A1EFCC","terminal_type":"APP","device_name":"Mike's iPhone","device_brand":"APPLE","device_model":"iPhone 7 Plus","imei":"863xxxxxxxx5012","os_name":"ios","os_version":"9.1.1","wireless_network":"china unicom","wireless_carrier":"china unicom","flight_mode":"enabled","finger_print_enabled":"enabled","device_boot_time":"20200617083001","last_unlock_time":"20200617083001","screen_resolution":"1024*768","is_jailbreaked":"true","mac_address":"8c:be:be:71:1f:34","system_language":"EN","time_zone":"UTC+08","signup_time":"20200617083001","last_login_time":"20200617083001","merchant_user_id":"20200617083001"}
     */
    public static final String RISK_INFO = "risk_info";


    /**
     * SESSION_ID (String(64)): 识别设备的会话ID
     * 可选：是 (Y)
     * 示例值：26dxxxxxxxxxxxxxxxxx7xxxxxxx03e9
     */
    public static final String SESSION_ID = "session_id";
    /**
     * SERIAL_NO (String(64)): 设备的序列号
     * 可选：是 (Y)
     * 示例值：f0cxec9a
     */
    public static final String SERIAL_NO = "serial_no";
    /**
     * CLIENT_IP (String(32)): 客户端IP地址
     * 可选：是 (Y)
     * 示例值：************
     */
    public static final String CLIENT_IP = "client_ip";
    /**
     * LATITUDE_LONGITUDE (String(128)): 用户请求的经纬度
     * 可选：是 (Y)
     * 示例值：39.9151190000,116.4039630000
     */
    public static final String LATITUDE_LONGITUDE = "latitude_longitude";
    /**
     * IDFA (String(128)): 广告标识符 (iOS)
     * 可选：是 (Y)
     * 示例值：5D08BADB6-B7D1-46DE-BDAB-B66468A1EFCC
     */
    public static final String IDFA = "idfa";
    /**
     * TERMINAL_TYPE (String(64)): 终端类型
     * 可选：是 (Y)
     * 示例值：APP
     */
    public static final String TERMINAL_TYPE = "terminal_type";
    /**
     * DEVICE_NAME (String(256)): 设备名称
     * 可选：是 (Y)
     * 示例值：Mike's iPhone
     */
    public static final String DEVICE_NAME = "device_name";
    /**
     * DEVICE_BRAND (String(256)): 设备品牌
     * 可选：是 (Y)
     * 示例值：APPLE
     */
    public static final String DEVICE_BRAND = "device_brand";
    /**
     * DEVICE_MODEL (String(64)): 设备型号
     * 可选：是 (Y)
     * 示例值：iPhone 7 Plus
     */
    public static final String DEVICE_MODEL = "device_model";
    /**
     * IMEI (String(64)): 国际移动设备标识
     * 可选：是 (Y)
     * 示例值：863xxxxxxxx5012
     */
    public static final String IMEI = "imei";
    /**
     * OS_NAME (String(64)): 操作系统名称
     * 可选：是 (Y)
     * 示例值：ios
     */
    public static final String OS_NAME = "os_name";
    /**
     * OS_VERSION (String(32)): 操作系统版本
     * 可选：是 (Y)
     * 示例值：9.1.1
     */
    public static final String OS_VERSION = "os_version";
    /**
     * WIRELESS_NETWORK (String(128)): 无线网络
     * 可选：是 (Y)
     * 示例值：china unicom
     */
    public static final String WIRELESS_NETWORK = "wireless_network";
    /**
     * WIRELESS_CARRIER (String(128)): 无线运营商名称
     * 可选：是 (Y)
     * 示例值：china unicom
     */
    public static final String WIRELESS_CARRIER = "wireless_carrier";
    /**
     * FLIGHT_MODE (String(32)): 是否启用飞行模式
     * 可选：是 (Y)
     * 示例值：enabled
     */
    public static final String FLIGHT_MODE = "flight_mode";
    /**
     * FINGER_PRINT_ENABLED (String(32)): 是否启用指纹
     * 可选：是 (Y)
     * 示例值：enabled
     */
    public static final String FINGER_PRINT_ENABLED = "finger_print_enabled";
    /**
     * DEVICE_BOOT_TIME (String(16)): 设备的启动时间, YYYYMMDDHHMMSS
     * 可选：是 (Y)
     * 示例值：20200617083001
     */
    public static final String DEVICE_BOOT_TIME = "device_boot_time";
    /**
     * LAST_UNLOCK_TIME (String(16)): 设备的最后解锁时间, YYYYMMDDHHMMSS
     * 可选：是 (Y)
     * 示例值：20200617083001
     */
    public static final String LAST_UNLOCK_TIME = "last_unlock_time";
    /**
     * SCREEN_RESOLUTION (String(32)): 设备屏幕的分辨率
     * 可选：是 (Y)
     * 示例值：1024*768
     */
    public static final String SCREEN_RESOLUTION = "screen_resolution";
    /**
     * IS_JAILBREAKED (Boolean(32)): 设备是否越狱
     * 可选：是 (Y)
     * 示例值：true
     */
    public static final String IS_JAILBREAKED = "is_jailbreaked";
    /**
     * MAC_ADDRESS (String(64)): Mac地址
     * 可选：是 (Y)
     * 示例值：8c:be:be:71:1f:34
     */
    public static final String MAC_ADDRESS = "mac_address";
    /**
     * SYSTEM_LANGUAGE (String(64)): 设备系统语言
     * 可选：是 (Y)
     * 示例值：EN
     */
    public static final String SYSTEM_LANGUAGE = "system_language";
    /**
     * TIME_ZONE (String(32)): 设备设置的时区
     * 可选：是 (Y)
     * 示例值：UTC+08
     */
    public static final String TIME_ZONE = "time_zone";
    /**
     * SIGNUP_TIME (String(16)): 用户注册时间, YYYYMMDDHHMMSS
     * 可选：是 (Y)
     * 示例值：20200617083001
     */
    public static final String SIGNUP_TIME = "signup_time";
    /**
     * LAST_LOGIN_TIME (String(16)): 商户用户上次登录时间, YYYYMMDDHHMMSS
     * 可选：是 (Y)
     * 示例值：20200617083001
     */
    public static final String LAST_LOGIN_TIME = "lastLogin_time"; // 注意：这里是 lastLogin_time，而不是 last_login_time
    /**
     * MERCHANT_USER_ID (String(64)): 商户系统中的用户ID
     * 可选：是 (Y)
     * 示例值：user0001
     */
    public static final String MERCHANT_USER_ID = "merchant_user_id";



}
