package com.wosai.mpay.api.macaupass.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wosai.mpay.api.macaupass.constants.MacauPassProtocolFieldsConstants;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className SignUtil
 * @description: 澳门通签名验签帮助类
 * @create: 2025-04-27 18:49
 **/
public class SignUtil {
    public static final Logger log = LoggerFactory.getLogger(SignUtil.class);


    public static final String SIGN_STR = "sign_str";
    public static final String PARAMS = "params";


    public static Map<String, Object> generateSign(Map<String, Object> params, String privateKey) throws MpayException {

        try {
            Map<String, Object> result = generateSignStr(params, true);

            String signStr = result.getOrDefault(SIGN_STR, "").toString();
            params = (Map<String, Object>) result.getOrDefault(PARAMS, new HashMap<>());

            String sign = RsaSignature.sign(signStr, MacauPassProtocolFieldsConstants.DEFAULT_SIGN_ALGORITHM, privateKey);

            params.put(MacauPassProtocolFieldsConstants.SIGN, sign);

            return params;

        } catch (MpayException e) {
            throw e;
        }
    }

    /**
     * 使用公钥验证 SHA256WithRSA 签名
     *
     * @param params      待验签数据
     * @param signature 签名字符串 (Base64 编码)
     * @param publicKey 公钥字符串 (Base64 编码)
     * @return true 如果验签成功，false 否则返回
     * @throws Exception 如果验签过程中发生错误
     */
    public static boolean verify(Map<String, Object> params, String signature, String publicKey) throws MpayException {

        try {
            Map<String, Object> result = generateSignStr(params, false);

            String signStr = result.getOrDefault(SIGN_STR, "").toString();

            Boolean type = RsaSignature.validateSign(signStr, signature, MacauPassProtocolFieldsConstants.DEFAULT_SIGN_ALGORITHM, publicKey);

            if (!type) {
                log.info("response sign error responseSign: {}; signStr: {}; ", signature, signStr);
            }

            return type;
        } catch (MpayException e) {
            throw e;
        }
    }

    /**
     * 将 Map<String, Object> 转换为按 Key 字母顺序排序的字符串。
     * Value 为字符串、数字或 Map<String, Object>，Map<String, Object> 则转换为 JSON 对象。
     *
     * @param params Map<String, Object> 对象
     * @return 排序后的字符串
     * @throws JsonProcessingException 如果 JSON 处理过程中发生错误
     */
    public static Map<String, Object> generateSignStr(Map<String, Object> params, boolean sortSubMap) throws MpayException {

        // 1. 使用 TreeMap 自动按 Key 的字母顺序排序
        TreeMap<String, Object> sortedParams = new TreeMap<>(params);
        // 2. 构建排序后的字符串
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (MacauPassProtocolFieldsConstants.SIGN.equals(key) || MacauPassProtocolFieldsConstants.SIGN_TYPE.equals(key)){
                continue;
            }

            if (value == null) {
                continue;
            } else if (value instanceof String && StringUtils.isEmpty((String) value)) {
                continue;
            }

            if (sb.length() > 0) {
                sb.append("&");  // 添加 & 分隔符
            }
            sb.append(key).append("=");
            if (value instanceof Map) {
                // 如果 Value 是 Map，则转换为 JSON 字符串
                if (sortSubMap) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> mapValue = convertToTreeMap((Map<String, Object>) value);
                    sb.append(JsonUtil.objectToJsonString(mapValue));

                    sortedParams.put(key, mapValue);
                } else {
                    sb.append(JsonUtil.objectToJsonString(value));
                }
            } else if (value instanceof List) {
                // 如果 Value 是 List，则转换为 JSON 字符串
                sb.append(JsonUtil.objectToJsonString(value));
            } else {
                // 否则，直接添加 Value 的字符串表示
                sb.append(value.toString());
            }
        }

        Map<String, Object> returnMap = new HashMap<>();
        returnMap.put(SIGN_STR, sb.toString());
        returnMap.put(PARAMS, sortedParams);
        return returnMap;
    }

    /**
     * 对子map 对key重新排序
     * @param subParams
     * @return
     */
    public static TreeMap<String, Object> convertToTreeMap(Map<String, Object> subParams) {
        TreeMap<String, Object> sortedParams = new TreeMap<>(subParams);

        for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
            if (entry.getValue() instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> mapValue = (Map<String, Object>) entry.getValue();
                sortedParams.put(entry.getKey(), convertToTreeMap(mapValue));
            }
        }

        return sortedParams;
    }
}
