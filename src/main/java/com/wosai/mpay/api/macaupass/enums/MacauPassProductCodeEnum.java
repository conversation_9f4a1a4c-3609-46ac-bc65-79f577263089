package com.wosai.mpay.api.macaupass.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className MacauPassProductCodeEnum
 * @description:
 * @create: 2025-04-28 17:06
 **/
public enum MacauPassProductCodeEnum {

    MINIAPP("MP_MINIAPP_PAY", "MPay小程序"),
    JSAPI("MP_JSAPI_PAY", "非MPay小程序");


    private final String code;
    private final String message;

    MacauPassProductCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public String getCode() {
        return code;
    }
    public String getMessage() {
        return message;
    }


    public static MacauPassProductCodeEnum of(String code) {
        if (null == code) {
            return JSAPI;
        }
        for (MacauPassProductCodeEnum e : MacauPassProductCodeEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return JSAPI;
    }

}
