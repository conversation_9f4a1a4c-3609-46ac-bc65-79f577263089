package com.wosai.mpay.api.macaupass.utils;

import com.wosai.mpay.api.macaupass.constants.MacauPassProtocolFieldsConstants;
import com.wosai.mpay.api.macaupass.constants.MacauPassResponseFieldsConstants;
import com.wosai.mpay.util.SafeSimpleDateFormat;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className StringCleanerUtil
 * @description:
 * @create: 2025-04-28 15:12
 **/
public class MacauPassUtil {

    // 特殊字符正则
    private static final String specialCharactersRegex = "[~!\\-`{}'\\[\\]?.,@\\^#\\$%&\\*\\(\\)_\\+\\/\\\\|)\"]"; // 定义特殊字符的正则表达式


    private static final SafeSimpleDateFormat dateSimpleFormat = new SafeSimpleDateFormat(MacauPassProtocolFieldsConstants.DATE_SIMPLE_REQUEST_FORMAT);


    private static final Map<String, Object> emptyMap = new HashMap<>();
    public static final String emptyStr = "";

    public static String cleanAndTruncate(String text, Integer size) {
        // 1. 去除特殊字符
        String cleanedText = text.replaceAll(specialCharactersRegex, "");
        // 2. 截取前64位 (非英文字符算2位)
        StringBuilder truncatedText = new StringBuilder();
        int length = 0;
        for (int i = 0; i < cleanedText.length(); i++) {
            char c = cleanedText.charAt(i);
            int charLength = 1; // 默认为英文字符
            if (isCJK(c)) {
                charLength = 2;
            }
            if (length + charLength <= size) {
                truncatedText.append(c);
                length += charLength;
            } else {
                break;
            }
        }
        return truncatedText.toString();
    }

    // 判断字符是否为中日韩字符
    private static boolean isCJK(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
                || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_FORMS
                || ub == Character.UnicodeBlock.HIRAGANA // 日文平假名
                || ub == Character.UnicodeBlock.KATAKANA  // 日文片假名
                || ub == Character.UnicodeBlock.HANGUL_SYLLABLES // 韩文音节
                || ub == Character.UnicodeBlock.HANGUL_JAMO // 韩文字母
                || ub == Character.UnicodeBlock.HANGUL_COMPATIBILITY_JAMO; // 韩文兼容字母
    }


    /**
     * 将字符串的金额分值转换为字符串的金额元，保留两位小数。
     *
     * @param amountInCents 字符串表示的金额分值。
     * @return 字符串表示的金额元，保留两位小数。 如果输入为空或无法解析为数字，则返回 null。
     * @throws IllegalArgumentException 如果输入字符串包含非数字字符。
     */
    public static String convertCentsToYuan(String amountInCents) {
        if (amountInCents == null || amountInCents.isEmpty()) {
            return "0.00";
        }
        try {
            // 使用 BigDecimal 避免浮点数精度问题
            BigDecimal cents = new BigDecimal(amountInCents);
            BigDecimal yuan = cents.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);  // 2位小数，四舍五入
            return yuan.toString();  // 返回字符串表示
        } catch (NumberFormatException e) {
            // 输入字符串无法解析为数字
            return "0.00";
        }
    }


    /**
     * 格式化时间
     * @param date
     * @return
     */
    public static String formatRequestDate(Date date) {
        return dateSimpleFormat.format(date);
    }

    /**
     * 获取结果状态map，仅有结果状态
     * @param result
     * @return
     */
    public static Map<String, String> getResultCodeMap(Map<String, Object> result) {
        String systemErrorCode = result.getOrDefault(MacauPassResponseFieldsConstants.IS_SUCCESS, emptyStr).toString();
        String error = result.getOrDefault(MacauPassResponseFieldsConstants.ERROR, emptyStr).toString();
        Map<String, Object> dataMap = (Map<String, Object>)result.getOrDefault(MacauPassResponseFieldsConstants.DATA, emptyMap);

        String resultCode = dataMap.getOrDefault(MacauPassResponseFieldsConstants.RESULT_CODE, emptyStr).toString();
        String resultMsg = dataMap.getOrDefault(MacauPassResponseFieldsConstants.RESULT_MSG, emptyStr).toString();

        Map<String, String> map = new HashMap<>();
        map.put(MacauPassResponseFieldsConstants.RESULT_CODE, resultCode);
        map.put(MacauPassResponseFieldsConstants.RESULT_MSG, resultMsg);
        map.put(MacauPassResponseFieldsConstants.IS_SUCCESS, systemErrorCode);
        map.put(MacauPassResponseFieldsConstants.ERROR, error);
        return map;
    }

}
