package com.wosai.mpay.api.macaupass.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className MacauPassPayChannelEnum
 * @description:
 * @create: 2025-04-28 17:04
 **/
public enum MacauPassPayChannelEnum {
    MPAY("mpay", "澳门钱包"),
    ALIPAY("alipay","支付宝"),
    WECHAT("wechat","微信");

    private final String type;
    private final String desc;

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    MacauPassPayChannelEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static MacauPassPayChannelEnum of(String type) {
        if (null == type) {
            return null;
        }
        for (MacauPassPayChannelEnum e : MacauPassPayChannelEnum.values()) {
            if (e.type.equals(type)) {
                return e;
            }
        }
        return null;
    }
}


