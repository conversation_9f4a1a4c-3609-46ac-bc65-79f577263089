package com.wosai.mpay.api.macaupass.constants;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className MacauPassNotifyFieldsConstants
 * @description:
 * @create: 2025-04-27 15:52
 **/
public class MacauPassNotifyFieldsConstants {


    /**
     * NOTIFY_TIME (String): 發送通知的時間。格式：YYYY-MM-DD HH:MM:SS  注意：此处类型修改为String
     * 必填：否 (N)
     * 示例值：2023-01-17 15:38:42
     */
    public static final String NOTIFY_TIME = "notify_time";
    /**
     * NOTIFY_TYPE (String): 通知類型
     * 必填：否 (N)
     * 示例值：trade_status_sync
     */
    public static final String NOTIFY_TYPE = "notify_type";
    /**
     * NOTIFY_ID (String): 通知驗證ID
     * 必填：否 (N)
     * 示例值：f5331b312b4b45a48b2c14f9d7afcc28
     */
    public static final String NOTIFY_ID = "notify_id";
    /**
     * SIGN_TYPE (String): 簽名方式
     * 必填：否 (N)
     * 示例值：RSA2
     */
    public static final String SIGN_TYPE = "sign_type";
    /**
     * SIGN (String): 請參考"數字簽名"
     * 必填：否 (N)
     * 示例值：RSUw55YRlvDRRj0jSCrHb6XqJHEk+i8N/U4SQ1YRNeARPBWDeYxoswi2CJEfX1LZC2Hg+TJC2OCjiGmbBN4QgWXvYe1ZwgI5NjEXFKcB64qa8PMseVuJjLSsdOrwLwhhFz4UvjRtMYcJ05qhOErEc8hcJWOF3a8k9isTjPaK53FGdgVzVxUDCpmZcxFici2/6fgf6BLFdoWS47Nl7NMAvEGwK03E9iKEGnsq+sTOTIaq0FEb/IARgP8
     */
    public static final String SIGN = "sign";
    /**
     * ORG_ID (String): 機構ID
     * 必填：否 (N)
     * 示例值：888535065254305
     */
    public static final String ORG_ID = "org_id";
    /**
     * OUT_TRANS_ID (String(64)): 在商戶的訂單系統中的唯一訂單ID。應保證商戶系統中此參數的唯一性。這是根據相應請求發送的參數，以其原始狀態返回。
     * 可选：是 (Y)
     * 示例值：20230117150911
     */
    public static final String OUT_TRANS_ID = "out_trans_id";
    /**
     * SUBJECT (String(127)): 商品名稱/交易名稱/訂單主題/訂單關鍵字等。這個參數在第三方支付渠道交易明細的第一列中,對於帳戶檢查很重要。這是根據相應請求發送的參數，以其原始狀態返回。
     * 可选：是 (Y)
     * 示例值：iphone
     */
    public static final String SUBJECT = "subject";
    /**
     * TRANS_ID (String(64)): 澳門通系統交易的交易編號,至少為16位,最多為64位
     * 可选：是 (Y)
     * 示例值：Q202301171538170000001
     */
    public static final String TRANS_ID = "trans_id";
    /**
     * TRANS_STATUS (String): 交易状态,詳情參見交易狀態
     * 可选：是 (Y)
     * 示例值：SUCCESS
     */
    public static final String TRANS_STATUS = "trans_status";
    /**
     * BUYER_LOGIN_ID (String(20)): 買方的登錄ID，id可能是電子郵件或手機號碼。為了隱私，id被部分掩蓋了。此參數僅在請求報文公共字段version=1.1.1時返回
     * 可选：是 (Y)
     * 示例值：68***029
     */
    public static final String BUYER_LOGIN_ID = "buyer_login_id";
    /**
     * BUYER_USER_ID (String(16)): 此ID代表每個帳號
     * 可选：是 (Y)
     * 示例值：2088102130896433
     */
    public static final String BUYER_USER_ID = "buyer_user_id";
    /**
     * GMT_CREATE (String): 創建交易的時間。格式：YYYY-MM-DD HH:MM:SS。 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：2023-01-17 15:38:17
     */
    public static final String GMT_CREATE = "gmt_create";
    /**
     * GMT_PAYMENT (String): 買方支付此交易的時間。格式：YYYY-MM-DD HH:MM:SS。  注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：2023-01-17 15:38:42
     */
    public static final String GMT_PAYMENT = "gmt_payment";
    /**
     * PRICE (String): 這是根據相應請求發送的價格參數，以其原始狀態返回 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：2000.00
     */
    public static final String PRICE = "price";
    /**
     * QUANTITY (String): 這是根據相應請求發送的數量參數，以其原始狀態返回 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：1
     */
    public static final String QUANTITY = "quantity";
    /**
     * CURRENCY (String(8)): 交易貨幣
     * 可选：是 (Y)
     * 示例值：MOP
     */
    public static final String CURRENCY = "currency";
    /**
     * TRANS_AMOUNT (String): 交易貨幣的訂單總金額 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：2000.00
     */
    public static final String TRANS_AMOUNT = "trans_amount";
    /**
     * RECEIPT_AMOUNT (String): 該金額為本筆交易，與商戶對賬的金額，其中將扣減商戶出資的discount及coupon優惠金額。單位為MOP，兩位小數。 注意：此处类型修改为String
     * 必填：否 (N)
     * 示例值：1998.00
     */
    public static final String RECEIPT_AMOUNT = "receipt_amount";
    /**
     * BUYER_PAY_AMOUNT (String): 買家實付金額，單位為元，兩位小數。該金額代表該筆交易買家實際支付的金額，=trans_amount-discount_amount-coupon_amount 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：1994.00
     */
    public static final String BUYER_PAY_AMOUNT = "buyer_pay_amount";
    /**
     * POINT_AMOUNT (String): 積分支付的金額，單位為元，兩位小數。該金額代表該筆交易中使用者使用積分支付的金額。 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：0.00
     */
    public static final String POINT_AMOUNT = "point_amount";
    /**
     * BODY (String(400)): 備註，描述和訂單的詳細資訊。這是根據相應請求發送的主體參數，以其原始狀態返回。
     * 必填：否 (N)
     * 示例值：iphone cellphone
     */
    public static final String BODY = "body";
    /**
     * PAYTOOLS_PAY_AMOUNT (String(512)): 每個付款渠道的付款金額。
     * 可选：是 (Y)
     * 示例值：[{"BANKCARD":"5000.00"}]
     */
    public static final String PAYTOOLS_PAY_AMOUNT = "paytools_pay_amount";
    /**
     * EXTRA_COMMON_PARAM (String(256)): 此參數在相應的請求參數上返回：passback_parameters
     * 可选：是 (Y)
     * 示例值：test
     */
    public static final String EXTRA_COMMON_PARAM = "extra_common_param";
    /**
     * DISCOUNT_AMOUNT (String): 該筆交易中使用的discount優惠金額，默認MOP，=merchant_discount_amt+platform_discount_amt 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：3.00
     */
    public static final String DISCOUNT_AMOUNT = "discount_amount";
    /**
     * MERCHANT_DISCOUNT_AMT (String(30)): 商戶出資的discount優惠金額（MOP），不結算給商戶
     * 可选：是 (Y)
     * 示例值：1.00
     */
    public static final String MERCHANT_DISCOUNT_AMT = "merchant_discount_amt";
    /**
     * PLATFORM_DISCOUNT_AMT (String(30)): 平臺出資的discount優惠金額（MOP），結算給商戶
     * 可选：是 (Y)
     * 示例值：2.00
     */
    public static final String PLATFORM_DISCOUNT_AMT = "platform_discount_amt";
    /**
     * DISCOUNT_TYPE (String(32)): 優惠類型,Minus：立減,Discount：折扣,Gift:贈送,RandomMinus：隨機立減
     * 可选：是 (Y)
     * 示例值：枚举值：Minus,Discount,Gift,RandomMinus
     */
    public static final String DISCOUNT_TYPE = "discount_type";
    /**
     * COUPON_AMOUNT (String): 本筆交易中使用的coupon優惠金額，默認MOP。其中由商戶出資的金額不結算（通過查詢訂單可獲取coupon_amount中商戶出資金額） 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：3.00
     */
    public static final String COUPON_AMOUNT = "coupon_amount";
    /**
     * COUPON_DETAIL (String): 本交易支付時所使用的所有優惠信息，詳見此處
     * 可选：是 (Y)
     * 示例值：[{"coupon_id":"00001","amount":"10.00" ,"name":"抵扣券","type":"DISCOUNT_VOUCHER","memo":" XX優惠券"}]
     */
    public static final String COUPON_DETAIL = "coupon_detail";
    /**
     * PAY_CHANNEL_TYPE (String(32)): 支付渠道
     * 必填：否 (N)
     * 示例值：mpay:澳門錢包,alipay:支付寶(含CN/MO/HK/A+),wechat:微信,tfpay:豐付寶,cgb:廣發錢包,boc:中銀錢包,ICBC:工銀錢包,uepay:極易付錢包,luso:國際錢包
     */
    public static final String PAY_CHANNEL_TYPE = "pay_channel_type";
    /**
     * USER_SITE (String(16)): 用戶所屬區域,當pay_channel_type為alipay時，不同user_site可用於區分A+錢包類型
     * 可选：是 (Y)
     * 示例值：CN-中國,MO-澳門 支付寶MO,HK-香港 支付寶HK,KP-韓國Kakao Pay,TH-泰国TrueMoney,MY-马来西亚TNG,PH-菲律賓GCash,MN-蒙古HiPay,DN-印尼DANA,IT-意大利Tinaba,KR1-韓國NAVER Pay,KR2-韓國Toss Payments,SG1-新加坡OCBC,SG2-新加坡ChangiPay,MY1-馬來西亞Public Bank,ID-印尼Mandiri,TH1-泰國KBank,MY2-馬來西亞Bigpay,SG3-新加坡Bigpay,TH2-泰國Bigpay,KZ-哈薩克斯坦Kaspi
     */
    public static final String USER_SITE = "user_site";
    /**
     * TRADE_NO (String(64)): 支付渠道交易流水號
     * 可选：是 (Y)
     * 示例值：(None Provided)
     */
    public static final String TRADE_NO = "trade_no";
    /**
     * USER_PAY_CCY (String(12)): 用户实际支付幣種,依據用戶錢包所屬地區返回，交易状态为成功才有值。枚舉值參考貨幣代碼表
     * 可选：是 (Y)
     * 示例值：CNY
     */
    public static final String USER_PAY_CCY = "user_pay_ccy";
    /**
     * USER_PAY_AMOUNT (String): 用户实际支付金额 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：200.00
     */
    public static final String USER_PAY_AMOUNT = "user_pay_amount";



}
