package com.wosai.mpay.api.macaupass.constants;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className MacauPassResponseFieldsConstants
 * @description:
 * @create: 2025-04-27 10:42
 **/
public class MacauPassResponseFieldsConstants {


    /// 系统级响应常量 ////////////////////

    /**
     * IS_SUCCESS (String): 请求成功与否。成功的请求并不意味着业务被成功接受和处理。T-成功 F-失败
     * 必填：否 (N)
     * 示例值：T
     */
    public static final String IS_SUCCESS = "is_success";
    /**
     * SIGN_TYPE (String): 签名方式
     * 可选：是 (Y)
     * 示例值：RSA2
     */
    public static final String SIGN_TYPE = "sign_type";
    /**
     * SIGN (String): 签名
     * 可选：是 (Y)
     * 请参阅签名规则说明
     */
    public static final String SIGN = "sign";
    /**
     * ERROR (String): 如果请求成功，则此参数不存在; 如果请求失败，则此参数为错误code
     * 可选：是 (Y)
     * 示例值：ILLEGAL_SIGN
     */
    public static final String ERROR = "error";
    /**
     * DATA (String): 响应数据,详情各接口响应信息
     * 可选：是 (Y)
     */
    public static final String DATA = "data";


    /**
     * RESULT_CODE (String(32)): 處理結果的響應代碼;
     * 必填：否 (N)
     * 示例值：0000
     */
    public static final String RESULT_CODE = "result_code";
    /**
     * RESULT_MSG (String(256)): 處理結果的響應信息
     * 必填：否 (N)
     * 示例值：交易成功
     */
    public static final String RESULT_MSG = "result_msg";
    /**
     * RESULT_MSG_EN (String(256)): 處理結果的英文響應信息，仅在result_code非0000时返回
     * 可选：是 (Y)
     * 示例值：Business failure
     */
    public static final String RESULT_MSG_EN = "result_msg_en";
    /**
     * OUT_TRANS_ID (String(64)): 請求中給出的out_trans_id
     * 必填：否 (N)
     * 示例值：20230117162501
     */
    public static final String OUT_TRANS_ID = "out_trans_id";
    /**
     * TRANS_ID (String(64)): 澳門通系統的交易流水ID
     * 必填：否 (N)
     * 示例值：Q202301171625520000001
     */
    public static final String TRANS_ID = "trans_id";
    /**
     * BUYER_LOGIN_ID (String(20)): 買方的登錄ID，id可能是電子郵件或手機號碼。為了隱私，id被部分掩蓋了。
     * 可选：是 (Y)
     * 示例值：186***22156
     */
    public static final String BUYER_LOGIN_ID = "buyer_login_id";
    /**
     * BUYER_USER_ID (String(16)): 此ID代表每個帳號
     * 可选：是 (Y)
     * 示例值：(None Provided)
     */
    public static final String BUYER_USER_ID = "buyer_user_id";
    /**
     * PAY_TIME (String(16)): 交易支付時間格式：YYYY-MM-DD HH:MM:SS
     * 可选：是 (Y)
     * 示例值：2023-01-17 16:30:23
     */
    public static final String PAY_TIME = "pay_time";
    /**
     * CURRENCY (String(8)): 交易貨幣,默認MOP
     * 可选：是 (Y)
     * 示例值：MOP
     */
    public static final String CURRENCY = "currency";
    /**
     * TRANS_AMOUNT (String): 交易金額;範圍：0.10-100000000.00。小數點後兩位數  注意：此处类型修改为String
     * 必填：否 (N)
     * 示例值：2000.00
     */
    public static final String TRANS_AMOUNT = "trans_amount";
    /**
     * TRANS_STATUS (String(32)): 交易状态,詳情參見交易狀態
     * 必填：否 (N)
     * 示例值：SUCCESS
     */
    public static final String TRANS_STATUS = "trans_status";
    /**
     * RECEIPT_AMOUNT (String): 該金額為本筆交易，與商戶對賬的金額，其中將扣減商戶出資的discount及coupon優惠金額。單位為MOP，兩位小數。 注意：此处类型修改为String
     * 必填：否 (N)
     * 示例值：1998.00
     */
    public static final String RECEIPT_AMOUNT = "receipt_amount";
    /**
     * BUYER_PAY_AMOUNT (String): 買家實付金額，單位為元，兩位小數。該金額代表該筆交易買家實際支付的金額，=trans_amount-discount_amount-coupon_amount 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：1994.00
     */
    public static final String BUYER_PAY_AMOUNT = "buyer_pay_amount";
    /**
     * POINT_AMOUNT (String): 積分支付的金額，單位為元，兩位小數。該金額代表該筆交易中使用者使用積分支付的金額。 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：0.00
     */
    public static final String POINT_AMOUNT = "point_amount";
    /**
     * DISCOUNT_AMOUNT (String): 該筆交易中使用的discount優惠金額，默認MOP，=merchant_discount_amt+platform_discount_amt 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：3.00
     */
    public static final String DISCOUNT_AMOUNT = "discount_amount";
    /**
     * MERCHANT_DISCOUNT_AMT (String(30)): 商戶出資的discount優惠金額（MOP），不結算給商戶
     * 可选：是 (Y)
     * 示例值：1.00
     */
    public static final String MERCHANT_DISCOUNT_AMT = "merchant_discount_amt";
    /**
     * PLATFORM_DISCOUNT_AMT (String(30)): 平臺出資的discount優惠金額（MOP），結算給商戶
     * 可选：是 (Y)
     * 示例值：2.00
     */
    public static final String PLATFORM_DISCOUNT_AMT = "platform_discount_amt";
    /**
     * DISCOUNT_TYPE (String(32)): 優惠類型, Minus：立減,Discount：折扣,Gift:贈送, RandomMinus：隨機立減
     * 可选：是 (Y)
     * 示例值：枚举值：Minus, Discount,Gift, RandomMinus
     */
    public static final String DISCOUNT_TYPE = "discount_type";
    /**
     * COUPON_AMOUNT (String): 本筆交易中使用的coupon優惠金額，默認MOP，包含商戶出資和平台出資金額，其中商戶出資金額不結算（通過查詢訂單可獲取coupon_amount中商戶出資金額）  注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：3.00
     */
    public static final String COUPON_AMOUNT = "coupon_amount";
    /**
     * COUPON_DETAIL (String): 本交易支付時所使用的所有優惠券資訊，詳見此處
     * 可选：是 (Y)
     */
    public static final String COUPON_DETAIL = "coupon_detail";
    /**
     * PAY_CHANNEL_TYPE (String(32)): 支付渠道
     * 必填：否 (N)
     * 示例值：mpay:澳門錢包,alipay:支付寶(含CN/MO/HK/A+),wechat:微信 ,tfpay:豐付寶,cgb:廣發錢包,boc:中銀錢包,ICBC:工銀錢包,uepay:極易付錢包,luso:國際錢包
     */
    public static final String PAY_CHANNEL_TYPE = "pay_channel_type";
    /**
     * USER_SITE (String(16)): 用戶所屬區域,當pay_channel_type為alipay時，不同user_site可用於區分A+錢包類型
     * 可选：是 (Y)
     * 示例值：CN-中國,MO-澳門 支付寶MO,HK-香港 支付寶HK,KP-韓國Kakao Pay,TH-泰国TrueMoney,MY-马来西亚TNG,PH-菲律賓GCash,MN-蒙古HiPay,DN-印尼DANA,IT-意大利Tinaba,KR1-韓國NAVER Pay,KR2-韓國Toss Payments,SG1-新加坡OCBC,SG2-新加坡ChangiPay,MY1-馬來西亞Public Bank,ID-印尼Mandiri,TH1-泰國KBank,MY2-馬來西亞Bigpay,SG3-新加坡Bigpay,TH2-泰國Bigpay,KZ-哈薩克斯坦Kaspi
     */
    public static final String USER_SITE = "user_site";
    /**
     * TRADE_NO (String(64)): 支付渠道交易流水號
     * 可选：是 (Y)
     * 示例值：(None Provided)
     */
    public static final String TRADE_NO = "trade_no";
    /**
     * USER_PAY_CCY (String(12)): 用户实际支付幣種,依據用戶錢包所屬地區返回，交易状态为成功才有值。枚舉值參考貨幣代碼表
     * 可选：是 (Y)
     * 示例值：CNY
     */
    public static final String USER_PAY_CCY = "user_pay_ccy";
    /**
     * USER_PAY_AMOUNT (String): 用户实际支付金额,交易状态为成功才有值 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：200.00
     */
    public static final String USER_PAY_AMOUNT = "user_pay_amount";
    /**
     * COUPON_ID (String(64)): 優惠券編號，同時使用多個coupon時，編碼以","分割返回
     * 必填：否 (N)
     * 示例值：0300141663309466,0300244842921745
     */
    public static final String COUPON_ID = "coupon_id";
    /**
     * NAME (String(64)): 優惠券名稱，同時使用多個coupon時，券名稱以空格分割返回
     * 必填：否 (N)
     * 示例值：平台滿減券滿30減20 商戶滿減券滿26減16
     */
    public static final String NAME = "name";
    /**
     * TYPE (String(32)): 優惠券類型
     * 必填：否 (N)
     * 示例值：0
     */
    public static final String TYPE = "type";
    /**
     * AMOUNT (String): 使用的優惠券優惠總金額，默認MOP，=merchantCouponAmt+platformCouponAmt  注意：此处类型修改为String
     * 必填：否 (N)
     * 示例值：36.00
     */
    public static final String AMOUNT = "amount";
    /**
     * MERCHANT_COUPON_AMT (String): 商家出資的優惠券金額，默認MOP，此金額不結算。該參數在version≥1.1.2時返回 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：16.00
     */
    public static final String MERCHANT_COUPON_AMT_HUMP = "merchantCouponAmt";
    /**
     * PLATFORM_COUPON_AMT (String): 平台出資的優惠券金額，默認MOP，此金額結算。該參數在version≥1.1.2時返回 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：20.00
     */
    public static final String PLATFORM_COUPON_AMT_HUMP = "platformCouponAmt";
    /**
     * MEMO (String(256)): 優惠券備註資訊
     * 可选：是 (Y)
     * 示例值：滿減券
     */
    public static final String MEMO = "memo";


    /**
     * RETRY_FLAG (String(1)): 是否可重試 , Y：由於可重複錯誤導致撤銷失敗, N：由於不可重複的錯誤，撤銷失敗
     * 可选：是 (Y)
     * 示例值：N
     */
    public static final String RETRY_FLAG = "retry_flag";
    /**
     * ACTION (String(10)): 撤銷的動作。close：僅關閉交易，但不予退款。refund：已退款。
     * 可选：是 (Y)
     * 示例值：refund
     */
    public static final String ACTION = "action";
    /**
     * CANCEL_TIME (String(14)): 撤銷時間.格式：YYYY-MM-DD HH:MM:SS
     * 可选：是 (Y)
     * 示例值：2023-01-17 15:38:42
     */
    public static final String CANCEL_TIME = "cancel_time";
    /**
     * OUT_REFUND_ID (String(64)): 商戶訂單系統中的退款ID 應該保證在商戶系統唯一性
     * 必填：否 (N)
     * 示例值：R20230117162503001
     */
    public static final String OUT_REFUND_ID = "out_refund_id";
    /**
     * REFUND_AMOUNT (String): 小於或等於原始交易金額和剩餘交易金額。注意：此处类型修改为String
     * 必填：否 (N)
     * 示例值：200.00
     */
    public static final String REFUND_AMOUNT = "refund_amount";
    /**
     * TOTAL_AMOUNT (String): 该笔退款所对应的交易的订单金额  注意：此处类型修改为String
     * 必填：否 (N)
     * 示例值：200.00
     */
    public static final String TOTAL_AMOUNT = "total_amount";
    /**
     * REFUND_REASON (String(128)): 发起退款时，传入的退款原因
     * 可选：是 (Y)
     * 示例值：退款
     */
    public static final String REFUND_REASON = "refund_reason";
    /**
     * STORE_ID (String(32)): 商户门店编号
     * 可选：是 (Y)
     * 示例值：8885350652543050001
     */
    public static final String STORE_ID = "store_id";
    /**
     * TERMINAL_ID (String(32)): 商户机具终端编号
     * 可选：是 (Y)
     * 示例值：(None Provided)
     */
    public static final String TERMINAL_ID = "terminal_id";
    /**
     * PAYTOOLS_PAY_AMOUNT (String(512)): 每個付款渠道的付款金額。
     * 可选：是 (Y)
     * 示例值：[{"BANKCARD":"5000.00"}]
     */
    public static final String PAYTOOLS_PAY_AMOUNT = "paytools_pay_amount";
    /**
     * MERCHANT_COUPON_AMT (String): 本筆交易中使用的商戶出資coupon優惠金額，默認MOP，=coupon _detail內層merchantCouponAmt，不結算給商戶。此參數在Version大於等於1.1.2時返回 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：1.00
     */
    public static final String MERCHANT_COUPON_AMT = "merchant_coupon_amt";
    /**
     * PLATFORM_COUPON_AMT (String): 本筆交易中使用的平臺出資coupon優惠金額，默認MOP，=coupon _detail內層platformCouponAmt，結算給商戶此參數在Version大於等於1.1.2時返回 注意：此处类型修改为String
     * 可选：是 (Y)
     * 示例值：2.00
     */
    public static final String PLATFORM_COUPON_AMT = "platform_coupon_amt";


    /**
     * QR_CODE (String(64)): qrcode地址
     * 必填：是 (N)
     * 示例值：http://qr.macaupay.com.mo/OcMNE5SB7Dh2rjI801577
     */
    public static final String QR_CODE = "qr_code";

    /**
     * PAY_INFO (String(2000)): 用於MPay/支付寶/微信APP內訪問的H5、或微信小程序拉起收銀台。JSAPI支付-如何拉起收銀台
     * 可选：是 (Y)
     * 示例值：{"appId":"wx4027b895e6a3f4c9","timeStamp":"1568189467","signtype":"RSA","package":"prepay_id=wx11161106959009d9ce0310361566607700","nonceStr":"r2A0NVnNneCHdNVGH40MhFRnzoZR4VPw","paySign":"971745B3FEFD423116F7E6510B79078B"}
     */
    public static final String PAY_INFO = "pay_info";


    /**
     * ACTUAL_RECHARGE_AMOUNT (Number(9,2)): 實際充值金額
     * 必填：是 (N)
     * 示例值：2000.00
     */
    public static final String ACTUAL_RECHARGE_AMOUNT = "actual_recharge _amount";
    /**
     * RECHARGE_TOTAL_AMOUNT (Number(9,2)): 充值后餘額。以元為單位，帶兩位小數點
     * 必填：是 (N)
     * 示例值：5000.00
     */
    public static final String RECHARGE_TOTAL_AMOUNT = "recharge_total_amount";


    /**
     * ACCOUNT_BALANCE (Number(9,2)): 撤銷后餘額,单位元,最大两位小数
     * 可选：是 (Y)
     * 示例值：2005.00
     */
    public static final String ACCOUNT_BALANCE = "account_balance";
    /**
     * CANCEL_BEFORE_BALANCE (Number(9,2)): 撤銷前餘額,单位元,最大两位小数
     * 可选：是 (Y)
     * 示例值：5.00
     */
    public static final String CANCEL_BEFORE_BALANCE = "cancel_before_balance";


    /**
     * 支付渠道常量
     */
    public static class PayChannelTypeConstants {

        /**
         * 澳门钱包
         */
        public static final String MPAY = "mpay";

        /**
         * 支付寶(含CN/MO/HK/A+)
         */
        public static final String ALIPAY = "alipay";

        /**
         * 微信
         */
        public static final String WECHAT = "wechat";

        /**
         * 豐付寶
         */
        public static final String TFPAY = "tfpay";

        /**
         * 廣發錢包
         */
        public static final String CGB = "cgb";

        /**
         * 中銀錢包
         */
        public static final String BOC = "boc";

        /**
         * 工銀錢包
         */
        public static final String ICBC = "ICBC";

        /**
         * 極易付錢包
         */
        public static final String UEPAY = "uepay";

        /**
         * 國際錢包
         */
        public static final String LUSO = "luso";

    }

    public static class UserSiteConstants {
        /**
         * 私有构造函数，防止实例化
         */
        private UserSiteConstants() {
        }
        /**
         * 中国
         */
        public static final String CN = "CN";
        /**
         * 澳门 支付寶MO
         */
        public static final String MO = "MO";
        /**
         * 香港 支付寶HK
         */
        public static final String HK = "HK";
        /**
         * 韓國Kakao Pay
         */
        public static final String KP = "KP";
        /**
         * 泰国TrueMoney
         */
        public static final String TH = "TH";
        /**
         * 马来西亚TNG
         */
        public static final String MY = "MY";
        /**
         * 菲律賓GCash
         */
        public static final String PH = "PH";
        /**
         * 蒙古HiPay
         */
        public static final String MN = "MN";
        /**
         * 印尼DANA
         */
        public static final String DN = "DN";
        /**
         * 意大利Tinaba
         */
        public static final String IT = "IT";
        /**
         * 韓國NAVER Pay
         */
        public static final String KR1 = "KR1";
        /**
         * 韓國Toss Payments
         */
        public static final String KR2 = "KR2";
        /**
         * 新加坡OCBC
         */
        public static final String SG1 = "SG1";
        /**
         * 新加坡ChangiPay
         */
        public static final String SG2 = "SG2";
        /**
         * 馬來西亞Public Bank
         */
        public static final String MY1 = "MY1";
        /**
         * 印尼Mandiri
         */
        public static final String ID = "ID";
        /**
         * 泰國KBank
         */
        public static final String TH1 = "TH1";
        /**
         * 馬來西亞Bigpay
         */
        public static final String MY2 = "MY2";
        /**
         * 新加坡Bigpay
         */
        public static final String SG3 = "SG3";
        /**
         * 泰國Bigpay
         */
        public static final String TH2 = "TH2";
        /**
         * 哈薩克斯坦Kaspi
         */
        public static final String KZ = "KZ";
        /**
         * 泰國PlanetX
         */
        public static final String TH3 = "TH3";
        /**
         * 歐洲Bluecode
         */
        public static final String EUR = "EUR";
    }

}
