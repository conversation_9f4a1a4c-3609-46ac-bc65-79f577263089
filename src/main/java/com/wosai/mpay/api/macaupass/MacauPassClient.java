package com.wosai.mpay.api.macaupass;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.mpay.api.macaupass.constants.MacauPassProtocolFieldsConstants;
import com.wosai.mpay.api.macaupass.constants.MacauPassRequestFieldsConstants;
import com.wosai.mpay.api.macaupass.constants.MacauPassResponseFieldsConstants;
import com.wosai.mpay.api.macaupass.enums.MacauPassErrorCodeEnum;
import com.wosai.mpay.api.macaupass.enums.MacauPassSystemErrorEnum;
import com.wosai.mpay.api.macaupass.utils.MacauPassUtil;
import com.wosai.mpay.api.macaupass.utils.SignUtil;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import com.wosai.pantheon.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;

import static com.wosai.mpay.util.HttpClientUtils.BODY_RESULT_FIELD;
import static com.wosai.mpay.util.MapEncryptUtil.resetValueAndReturnNewMap;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className MacauPassClient
 * @description: 澳门通支付
 * @create: 2025-04-27 10:16
 **/
public class MacauPassClient {

    public static final Logger log = LoggerFactory.getLogger(MacauPassClient.class);

    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    //打印日志时需要加密的字段
    private static final List<String> NEED_ENCRYPT_FIELDS_WHEN_LOG = Arrays.asList(
            MacauPassRequestFieldsConstants.TRANS_AMOUNT, MacauPassResponseFieldsConstants.REFUND_AMOUNT, MacauPassResponseFieldsConstants.AMOUNT);

    //默认加密后的值
    private static final String DEFAULT_ENCRYPT_VALUE = "*";

    /**
     * 请求接口
     *
     * @param requestUrl   请求地址
     * @param method       请求方法
     * @param params       请求参数
     * @param privateKey   私钥
     * @param publicKey    公钥
     * @return
     * @throws MpayException
     * @throws MpayApiNetworkError
     */
    public Map<String, Object> call(String requestUrl, String method, Map<String, Object> params, String privateKey, String publicKey) throws MpayException, MpayApiNetworkError {

        //将方法添加到参数中
        params.put(MacauPassProtocolFieldsConstants.SERVICE, method);

        //加签
        params = SignUtil.generateSign(params, privateKey);

        log.info("request {}", toJsonStringWithEncryptFields(params));

        String requestJson = JsonUtil.objectToJsonString(params);

        try {
            String response = HttpClientUtils.doPost(MacauPassClient.class.getName(), null, null,
                requestUrl, MacauPassProtocolFieldsConstants.DEFAULT_CONTENT_TYPE, requestJson, MacauPassProtocolFieldsConstants.DEFAULT_CHARSET, connectTimeout, readTimeout);


            log.info("response content {}", response);

            return verifyResponse(method, response, publicKey);
        } catch (Exception e) {
            log.error("澳门通请求异常: e.Msg={}", e.getMessage(), e);
            Map<String, Object> dataResult = new HashMap<>();
            dataResult.put(MacauPassResponseFieldsConstants.RESULT_CODE, MacauPassErrorCodeEnum.SYSTEM_EXCEPTION.getCode());
            dataResult.put(MacauPassResponseFieldsConstants.RESULT_MSG, MacauPassErrorCodeEnum.SYSTEM_EXCEPTION.getMessage());

            Map<String, Object> result = new HashMap<>();
            result.put(MacauPassResponseFieldsConstants.IS_SUCCESS, MacauPassSystemErrorEnum.FAILED.getCode());
            result.put(MacauPassResponseFieldsConstants.DATA, dataResult);
            result.put(MacauPassResponseFieldsConstants.ERROR, MacauPassSystemErrorEnum.FAILED.getMessage());
            return result;
        }
    }



    /**
     * 验签响应数据
     *
     * @param method
     * @param response
     * @param publicKey
     * @return
     * @throws MpayException
     */
    private Map<String, Object> verifyResponse(String method, String response, String publicKey) throws MpayException {
        Map<String, Object> result = JsonUtil.jsonStringToObject(response, new TypeReference<Map<String, Object>>() {});

        Map<String, String> resultCodeMap = MacauPassUtil.getResultCodeMap(result);
        String systemErrorCode = resultCodeMap.get(MacauPassResponseFieldsConstants.IS_SUCCESS);
        String error = resultCodeMap.get(MacauPassResponseFieldsConstants.ERROR);
        String resultCode = resultCodeMap.get(MacauPassResponseFieldsConstants.RESULT_CODE);

        if (!MacauPassSystemErrorEnum.isSuccess(systemErrorCode) || !MacauPassErrorCodeEnum.isSuccess(resultCode)) {
            log.info("response {}", response);

            String resultMsg = resultCodeMap.get(MacauPassResponseFieldsConstants.RESULT_MSG);

            Map<String, Object> dataResult = new HashMap<>();
            dataResult.put(MacauPassResponseFieldsConstants.RESULT_CODE, resultCode);
            dataResult.put(MacauPassResponseFieldsConstants.RESULT_MSG, resultMsg);

            result.put(MacauPassResponseFieldsConstants.IS_SUCCESS, systemErrorCode);
            result.put(MacauPassResponseFieldsConstants.DATA, dataResult);
            result.put(MacauPassResponseFieldsConstants.ERROR, error);
            return result;
        }

        String sign = result.getOrDefault(MacauPassProtocolFieldsConstants.SIGN, MacauPassUtil.emptyStr).toString();

        if (!SignUtil.verify(result, sign, publicKey)) {
            log.info("response sign error {}", response);

            Map<String, Object> dataResult = new HashMap<>();
            dataResult.put(MacauPassResponseFieldsConstants.RESULT_CODE, MacauPassErrorCodeEnum.SIGNATURE_VERIFICATION_FAILED.getCode());
            dataResult.put(MacauPassResponseFieldsConstants.RESULT_MSG, MacauPassErrorCodeEnum.SIGNATURE_VERIFICATION_FAILED.getMessage());

            result.put(MacauPassResponseFieldsConstants.IS_SUCCESS, MacauPassSystemErrorEnum.FAILED.getCode());
            result.put(MacauPassResponseFieldsConstants.DATA, dataResult);
            return result;
        }

        result.remove(MacauPassProtocolFieldsConstants.SIGN);
        result.remove(MacauPassProtocolFieldsConstants.SIGN_TYPE);

        log.info("response {}", toJsonStringWithEncryptFields(result));
        return result;
    }

    /**
     * 给指定需要脱敏的字段进行脱敏，并返回Json String
     *
     * @param data
     * @return
     * @throws MpayException
     */
    private static String toJsonStringWithEncryptFields(Map<String, Object> data) throws MpayException {
        Map<String, Object> newMap = resetValueAndReturnNewMap(data, NEED_ENCRYPT_FIELDS_WHEN_LOG, DEFAULT_ENCRYPT_VALUE);
        return JsonUtil.objectToJsonString(newMap);
    }

}
