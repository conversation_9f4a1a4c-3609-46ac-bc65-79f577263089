package com.wosai.mpay.api.macaupass.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className MacauPassSceneEnum
 * @description:
 * @create: 2025-04-29 10:29
 **/
public enum MacauPassSceneEnum {

    ONLINE("ONLINE", "ONLINE");

    private final String code;
    private final String message;

    MacauPassSceneEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public String getCode() {
        return code;
    }
    public String getMessage() {
        return message;
    }


    public static MacauPassSceneEnum of(String code) {
        if (null == code) {
            return null;
        }
        for (MacauPassSceneEnum e : MacauPassSceneEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
