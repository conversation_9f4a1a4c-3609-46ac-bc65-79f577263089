package com.wosai.mpay.api.macaupass.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className MacauPassSystemErrorEnum
 * @description:
 * @create: 2025-04-28 11:33
 **/
public enum MacauPassSystemErrorEnum {

    SUCCESS("T", "success"),
    FAILED("F", "failed");


    private final String code;
    private final String message;

    MacauPassSystemErrorEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public String getCode() {
        return code;
    }
    public String getMessage() {
        return message;
    }


    public static MacauPassSystemErrorEnum of(String code) {
        if (null == code) {
            return FAILED;
        }
        for (MacauPassSystemErrorEnum e : MacauPassSystemErrorEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return FAILED;
    }

    /**
     * 请求是否成功
     *
     * @param errorCode
     * @return
     */
    public static boolean isSuccess(String errorCode) {
        return MacauPassSystemErrorEnum.SUCCESS.getCode().equals(errorCode);
    }

}
