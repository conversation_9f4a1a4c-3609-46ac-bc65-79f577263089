package com.wosai.mpay.api.macaupass.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className MacauPassErrorCodeEnum
 * @description:
 * @create: 2025-04-27 11:36
 **/
public enum MacauPassErrorCodeEnum {


    // 成功
    SUCCESS("0000", "交易成功"),

    // 系统级别错误
    SYSTEM_EXCEPTION("9999", "系统异常"), // 例如，一个请求队列满了
    BUSINESS_TIMEOUT("9998", "业务超时"), // 例如，无法访问一个重要服务,请求发起查询请求 确认交易状态
    SIGNATURE_VERIFICATION_FAILED("9997", "验签失败"), // 请使用正确的密钥和签名方式重试,签名过程见"签名机制"
    CLIENT_IP_CHECK_FAILED("9996", "客户端ip校验失败"), // IP不在白名单内
    INTERFACE_NO_PERMISSION("9995", "接口无访问权限"), // 请确保service参数与API规范中的值具有相同的值，如果此错误仍然存在，请联系澳门通技术支持
    SYSTEM_BUSY("0009", "系统繁忙"), // 系统内部错误,请发起查询请求 确认交易状态
    INTERNAL_ERROR("0004", "内部错误"),
    // 通用错误
    BUSINESS_FAILED("0001", "业务失败/或者失败原因"), // 通用业务处理失败
    INVALID_REQUEST_PARAMETER("0002", "请求参数不合法"), // 消息中的字段缺失或者消息格式错误
    INVALID_INSTITUTION_KEY("0003", "机构密钥信息未配置或机构状态错误"), // 请联系澳门通确认密钥信息
    // 订单相关错误
    TRANSACTION_NOT_FOUND("0011", "交易不存在"),
    ORDER_STATUS_ERROR("0022", "订单状态异常"),
    UNSUPPORTED_ORDER_TYPE("0052", "不支持的订单类型"),
    ORDER_INSTITUTION_MISMATCH("0048", "订单所属机构不匹配"),
    INVALID_ORDER_AMOUNT_FORMAT("0024", "订单金额格式错误"),
    ORDER_AMOUNT_TOO_SMALL("0025", "订单金额过少"),
    DUPLICATE_ORDER_ID("0031", "订单号重复"),
    EMPTY_ORDER_AMOUNT("0062", "订单金额为空"),
    INVALID_PRICE_FORMAT("0028", "price 格式不正确"),
    INVALID_QUANTITY_FORMAT("0029", "quantity 格式不正确"),
    INVALID_PRICE_QUANTITY("0030", "交易总金额必须满足价格*数量"),
    GET_ORDER_TIME_FAILED("0021", "获取订单时间失败"),
    // 机构/商户相关错误
    INSTITUTION_NOT_FOUND("0012", "机构信息不存在"),
    INSTITUTION_STATUS_ERROR("0013", "机构状态异常"),
    SUB_MERCHANT_INSTITUTION_MISMATCH("0014", "二级商户所属机构不匹配"),
    SUB_MERCHANT_NOT_FOUND("0015", "二级商户信息不存在"),
    SUB_MERCHANT_STATUS_ERROR("0016", "二级商户状态异常"),
    SUB_MERCHANT_STORE_NOT_FOUND("0017", "二级商户门店信息不存在"),
    SUB_MERCHANT_INDUSTRY_CODE_EMPTY("0032", "二级商户行业编号不能为空"),
    SUB_MERCHANT_INDUSTRY_CODE_MISMATCH("0033", "二级商户行业编号不匹配"),
    SUB_MERCHANT_STORE_STATUS_ERROR("0035", "二级商户门店状态异常"),
    MERCHANT_STORE_MISMATCH("0070", "门店所属商户不匹配"),
    INVALID_SUB_MERCHANT_TYPE("0071", "二级商户类型错误,请联系澳门通"),
    INSTITUTION_ID_FORMAT_ERROR("0026", "机构号格式错误"),
    MERCHANT_NO_PERMISSION("0072", "商户无此权限,请联系澳门通"),
    // 支付/退款相关错误
    UNSUPPORTED_CURRENCY("0027", "不支持的币种"),
    UNSUPPORTED_PAYMENT_METHOD("0036", "暂不支持该支付方式"),
    EMPTY_PAYMENT_ID("0034", "支付单号为空"),
    GET_EXCHANGE_RATE_FAILED("0041", "获取汇率信息失败"),
    REFUND_NOT_ALLOWED("0053", "当前订单不允许退款"),
    REFUND_ALREADY_REVOKED("0054", "当前订单已撤销,不允许退款"),
    REFUND_ALREADY_COMPLETED("0055", "当前订单已全部退款"),
    REFUND_NOT_PAID("0056", "订单尚未支付成功,不允许退款"),
    REFUND_EXPIRED("0057", "退款有效期已过"),
    REFUND_AMOUNT_EXCEEDED("0058", "退款金额超过剩余可退金额"),
    EMPTY_BUSINESS_TYPE("0061", "业务类型为空"),
    EMPTY_REFUND_AMOUNT("0064", "退款金额为空"),
    EMPTY_REFUND_ID("0065", "退款流水号为空"),
    REFUND_ONLY_SAME_DAY("0073", "只能退当天交易"),
    REFUND_PROCESSING("0075", "退款处理中,请稍后重试"),
    ORDER_HAS_COUPON_PARTIAL_REFUND_NOT_ALLOWED("0087", "当前订单存在优惠,不允许部分退款"),
    REFUND_CURRENCY_NOT_SET("2029", "未设置退款币种"),
    TRANSACTION_TIME_MISMATCH("2041", "交易时间与原订单时间不匹配"),
    DUPLICATE_REFUND("2042", "重复退款"),
    REFUND_NOT_SUPPORTED("2043", "不支持退款"),
    PAYMENT_CHANNEL_CONFIG_ERROR("0037", "支付渠道配置错误"),
    ACTUAL_PAYMENT_AMOUNT_ZERO("0038", "实际付款金额不能为零"),
    INVALID_IT_B_PAY_FORMAT("0039", "it_b_pay无效的参数格式"),
    FUND_CHANNEL_PID_NOT_CONFIGURED("0042", "资金渠道PID未配置"),
    CREATE_QRCODE_FAILED("0043", "创建qrcode地址失败"),
    VERIFY_QRCODE_FAILED("0044", "验证qrcode码失败"),
    INVALID_BARCODE("0059", "不正确的条形码"),
    PAYMENT_CHANNEL_EMPTY("0046", "支付渠道不能为空"),
    // 用户相关错误
    USER_BALANCE_INSUFFICIENT("2001", "用户余额不足"),
    USER_STATUS_ERROR("2012", "用户状态异常"),
    ACCOUNT_STATUS_ERROR("2024", "账户状态异常"),
    ACCOUNT_NOT_SET("2027", "账号未设置"),
    PASSWORD_VERIFICATION_FAILED("2040", "密码验证失败"),
    USER_INFO_MISMATCH("2054", "用户信息不匹配"),
    USER_NOT_FOUND("2056", "用户不存在"),
    SELLER_ACCOUNT_UNVERIFIED("2059", "卖方账户未经认证"),
    // 交易金额限制错误
    TRANSACTION_AMOUNT_LIMIT_EXCEEDED("2018", "交易金额超限"),
    TRANSACTION_AMOUNT_DAILY_LIMIT_EXCEEDED("2019", "交易金额超出日限额"),
    TRANSACTION_AMOUNT_MONTHLY_LIMIT_EXCEEDED("2020", "交易金额超出月限额"),
    TRANSACTION_TIMES_MONTHLY_LIMIT_EXCEEDED("2032", "交易次数超出月限制"),
    TRANSACTION_AMOUNT_PRODUCT_RANGE_LIMIT_EXCEEDED("2017", "交易金额超出产品区间限额"),
    // 交易风险相关错误
    CONTAINS_SENSITIVE_WORDS("2050", "包含敏感词"),
    SELLER_ACCOUNT_BLOCKED("2060", "卖方的账户被封锁"),
    SELLER_RECEIVED_AMOUNT_MONTHLY_LIMIT_EXCEEDED("2061", "卖方收到的金额超出该月的上限"),
    CURRENCY_MISMATCH("2063", "币种不一致"),
    TRANSACTION_CONTAINS_RISK("2066", "当前交易包含风险"),
    PAYMENT_AMOUNT_EXCEEDS_INDUSTRY_LIMIT("2080", "付款金额不能超过行业中的特定金额"),
    // 商家余额相关错误
    MERCHANT_BALANCE_INSUFFICIENT("2082", "商家余额不足"),
    // 撤销相关错误
    ORDER_CANNOT_BE_REVOKED("0018", "当前订单不允许撤销"),
    ORDER_ALREADY_REFUNDED_CANNOT_BE_REVOKED("0019", "当前订单已经退过款,不允许撤销"),
    // 其他
    EMPTY_USER_IDENTIFIER("0063", "用户识别码为空"),
    TRANSACTION_FAILED("0078", "交易失败");


    private final String code;
    private final String message;

    MacauPassErrorCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public String getCode() {
        return code;
    }
    public String getMessage() {
        return message;
    }


    public static MacauPassErrorCodeEnum of(String code) {
        if (null == code) {
            return SYSTEM_EXCEPTION;
        }
        for (MacauPassErrorCodeEnum e : MacauPassErrorCodeEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return SYSTEM_EXCEPTION;
    }


    /**
     * 请求是否成功
     *
     * @param errorCode
     * @return
     */
    public static boolean isSuccess(String errorCode) {
        return MacauPassErrorCodeEnum.SUCCESS.getCode().equals(errorCode);
    }
}
