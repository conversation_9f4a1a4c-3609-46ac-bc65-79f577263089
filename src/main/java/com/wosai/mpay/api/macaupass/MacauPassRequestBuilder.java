package com.wosai.mpay.api.macaupass;

import com.wosai.mpay.api.macaupass.constants.MacauPassProtocolFieldsConstants;
import com.wosai.mpay.api.macaupass.constants.MacauPassServiceConstants;
import com.wosai.mpay.api.macaupass.enums.MacauPassChannelTypeEnum;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.UUID;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className MacauPassRequestBuilder
 * @description:
 * @create: 2025-04-28 12:00
 **/
public class MacauPassRequestBuilder {
    private final Map<String,Object> request;

    public MacauPassRequestBuilder(String orgId, String service, String channelType){
        request = new LinkedHashMap<>(16);

        request.put(MacauPassProtocolFieldsConstants.TIMESTAMP, System.currentTimeMillis());
        request.put(MacauPassProtocolFieldsConstants.FORMAT, MacauPassProtocolFieldsConstants.DEFAULT_APPLICATION_FORMAT);
        request.put(MacauPassProtocolFieldsConstants.ORG_ID, orgId);
        request.put(MacauPassProtocolFieldsConstants.SERVICE, service);
        request.put(MacauPassProtocolFieldsConstants.CHANNEL_TYPE, channelType);
        request.put(MacauPassProtocolFieldsConstants.SIGN_TYPE, MacauPassProtocolFieldsConstants.DEFAULT_SIGN_TYPE);
        request.put(MacauPassProtocolFieldsConstants.NONCE_STR, UUID.randomUUID().toString());
        request.put(MacauPassProtocolFieldsConstants.VERSION, MacauPassProtocolFieldsConstants.DEFAULT_API_VERSION);
    }

    public void set(String field, Object value) {
        request.put(field,  value);
    }

    public Map<String,Object> build(){
        return request;
    }
}
