package com.wosai.mpay.api.macaupass.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className MacauPassCurrencyEnum
 * @description: 币种枚举
 * @create: 2025-04-28 13:42
 **/
public enum MacauPassCurrencyEnum {

    /** 中國澳門 */
    MOP("MOP", "澳门元"),
    /** 中国 */
    CNY("CNY", "人民币元"),
    /** 中国香港 */
    HKD("HKD", "港元"),
    /** 韩国 */
    KRW("KRW", "韩元"),
    /** 菲律宾 */
    PHP("PHP", "菲律宾比索"),
    /** 泰国 */
    THB("THB", "泰铢"),
    /** 马来西亚 */
    MYR("MYR", "马来西亚林吉特"),
    /** 蒙古 */
    MNT("MNT", "蒙古图格里克"),
    /** 意大利 */
    EUR("EUR", "欧元"),
    /** 新加坡 */
    SGD("SGD", "新加坡元"),
    /** 印尼 */
    IDR("IDR", "印尼盾"),
    /** 哈萨克斯坦 */
    KZT("KZT", "哈萨克斯坦坚戈");


    private final String code;
    private final String name;

    MacauPassCurrencyEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }

    public static MacauPassCurrencyEnum of(String code) {
        if (null == code) {
            return MOP;
        }
        for (MacauPassCurrencyEnum e : MacauPassCurrencyEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return MOP;
    }
}
