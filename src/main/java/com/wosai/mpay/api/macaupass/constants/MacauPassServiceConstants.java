package com.wosai.mpay.api.macaupass.constants;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className MacauPassServiceConstants
 * @description: 澳门通请求方法常量
 * @create: 2025-04-27 10:28
 **/
public class MacauPassServiceConstants {

    /**
     * *********************** 基础交易接口 ***********************
     */
    /**
     * mpay.trade.query（交易查詢）
     * 此服务使商户能够通过这两个参数out_trans_id和trans_id查询交易的状态。
     * 适用于多种支付集成方式，如条码支付、二维码支付、JSAPI支付、网页支付、APP支付、预授权支付。
     */
    public static final String TRADE_QUERY = "mpay.trade.query";
    /**
     * mpay.trade.refund（退貨）
     * 此服务提供全部或部分退款的功能，前提是它在可退款期限内。
     * 适用于多种支付集成方式，如条码支付、二维码支付、JSAPI支付、网页支付、APP支付、预授权支付。
     */
    public static final String TRADE_REFUND = "mpay.trade.refund";
    /**
     * *********************** 条码支付接口 ***********************
     */
    /**
     * mpay.trade.spotpay（掃碼支付）
     * 此服务能够在客户的信息（例如客户的支付条码）存在时扫描客户条码信息实现支付。
     * 适用于条码支付集成。
     */
    public static final String TRADE_SPOTPAY = "mpay.trade.spotpay";
    /**
     * mpay.trade.cancel（交易撤銷）
     * 此服务用于在交易日（GMT + 8）内完全回滚交易,该交易可以成功完成或不成功完成。
     * 适用于条码支付、二维码支付、JSAPI支付。
     */
    public static final String TRADE_CANCEL = "mpay.trade.cancel";
    /**
     * *********************** 二维码支付接口 ***********************
     */
    /**
     * mpay.trade.precreate（預下單）
     * 此服务能够在客户的信息（例如客户的支付条码）不存在时生成二维码提供给客户扫描。
     * 适用于二维码支付和网页支付(本地收银台)的集成。
     */
    public static final String TRADE_PRECREATE = "mpay.trade.precreate";
    /**
     * *********************** JSAPI支付接口 ***********************
     */
    /**
     * mpay.trade.create（下單）
     * 此服务能够在客户的信息（例如客户在支付app中的user_id）存在时，于商户后端创建一笔订单,然后在商户前端使用js方法拉取收银台以完成支付。
     * 适用于JSAPI支付的集成。
     */
    public static final String TRADE_CREATE = "mpay.trade.create";
    /**
     * *********************** 网页支付（渠道收银台）接口 ***********************
     */
    /**
     * mpay.online.trade.precreate（下單）
     * 此服务能够在商家网站选择付款渠道后自动跳轉到对应錢包原生網站收銀台完成付款
     * 适用于網頁支付(渠道收銀台)的集成
     */
    public static final String ONLINE_TRADE_PRECREATE = "mpay.online.trade.precreate";
    /**
     * *********************** 扫码充值接口 ***********************
     */
    /**
     * mpay.trade.recharge（掃碼充值）
     * 此服务能够在客户的信息（例如客户MPay的支付条码或者手机号）存在时扫描客户条码信息或输入客户手机号实现充值。
     * 适用于掃碼充值的集成。
     */
    public static final String TRADE_RECHARGE = "mpay.trade.recharge";
    /**
     * mpay.trade.recharge.query（充值查詢）
     * 此服务使商户能够通过这三个参数out_trans_id和trans_id查询交易的状态。
     * 适用于掃碼充值的集成。
     */
    public static final String TRADE_RECHARGE_QUERY = "mpay.trade.recharge.query";
    /**
     * mpay.trade.recharge.cancel（充值沖正）
     * 此服务用于在交易日（GMT + 8）内完全回滚交易,该交易可以成功完成或不成功完成。
     * 适用于掃碼充值的集成。
     */
    public static final String TRADE_RECHARGE_CANCEL = "mpay.trade.recharge.cancel";
    /**
     * *********************** APP支付接口 ***********************
     */
    /**
     * mpay.trade.mobile.pay (下單)
     * 该服务需要将澳门通SDK引入到商户APP内部，在商户前端通过调用SDK的方法拉起收银台完成付款，注意，商户后端无需向澳门通发起下单命令。
     * 适用于APP支付的集成
     */
    public static final String TRADE_MOBILE_PAY = "mpay.trade.mobile.pay";
    /**
     * *********************** 预授权支付接口 ***********************
     */
    /**
     * mpay.trade.freeze（掃碼預授權凍結）
     * 此服务能够在客户的資訊（例如客户的支付條碼）存在時掃描客戶條碼資訊實現預授權資金凍結。
     * 适用于预授权接口的集成
     */
    public static final String TRADE_FREEZE = "mpay.trade.freeze";
    /**
     * mpay.trade.unfreeze（預授權解凍）
     * 此服务提供全部或部分解凍的功能，前提是它在可解凍期限內。
     * 适用于预授权接口的集成
     */
    public static final String TRADE_UNFREEZE = "mpay.trade.unfreeze";
    /**
     * mpay.trade.freeze.cancel（預授權凍結撤銷）
     * 此服务用于在交易日（GMT + 8）內完全回滾預授權凍結,無論凍結是否成功。
     * 适用于预授权接口的集成
     */
    public static final String TRADE_FREEZE_CANCEL = "mpay.trade.freeze.cancel";
    /**
     * mpay.trade.freeze.query（預授權凍結查詢）
     * 此服务使商户能够通过这三个参数out_order_no,out_trans_id和trans_id查询預授權凍結資金的狀態。
     * 适用于预授权接口的集成
     */
    public static final String TRADE_FREEZE_QUERY = "mpay.trade.freeze.query";
    /**
     * *********************** 独立券接口 ***********************
     */
    /**
     * mpay.trade.coupon.consumer（獨立券核销）
     * 此服务用于独立券核销
     */
    public static final String TRADE_COUPON_CONSUMER = "mpay.trade.coupon.consumer";
    /**
     * mpay.trade.coupon.query（查詢券狀態（voucher_status））
     * 此服务用于查询券状态
     */
    public static final String TRADE_COUPON_QUERY = "mpay.trade.coupon.query";

}
