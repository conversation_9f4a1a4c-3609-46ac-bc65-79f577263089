package com.wosai.mpay.api.haike;

import com.wosai.mpay.api.HttpResourceCallback;
import com.wosai.mpay.api.alipay.*;
import com.wosai.mpay.api.weixin.*;
import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import com.wosai.mpay.util.UUIDGenerator;
import com.wosai.pantheon.util.MapUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

import static com.wosai.mpay.api.alipay.AlipayV2NewClient.SIGN_TYPE_HAIKE;
import static com.wosai.mpay.api.alipay.AlipayV2NewClient.SIGN_TYPE_SM2;

/***
 * @ClassName: HaikeUpTest
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/7/12 11:46 AM
 */
public class HaikeUpTest {

    private static final String REFUND_QUERY_URL = "http://*************:8080/front-api/pay/refund-query";

    /**支付宝网关*/
    public static final String GATEWAY    = "https://sqb-front-test.icardpay.com";
    public static final String GATEWAY_PRO    = "https://sqb-front.icardpay.com";
    public static final String ALIPAY_APP_ID    = "1266000048330003";
    public static final String UPLOAD_IMAGE_URL = "/front-api/img/upload";
    public static final String OPEN_SHARING_URL = "/front-api/balanceSplit/apply";
    public static final String OPEN_SHARING_QUERY_URL = "/front-api/balanceSplit/query";
    public static final String DO_SHARING_URL = "/front-api/balanceSplit/doBalanceSplit";
    public static final String SHARING_QUERY_URL = "/front-api/balanceSplit/balanceSplitQuery";


    private static final String AGENT_NO = "ISV002073";
    private static final String MERCH_NO = "1234";
    //密钥
    private static final String accessKey = "5569e94bd92b4004b084b096ff133ff5";
    private static final SafeSimpleDateFormat format = new SafeSimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static void orderNotifyTest() throws Exception {
        HaikeRequestBuilder builder = new HaikeRequestBuilder();

        //海科服务商编号
        builder.set(HaikeProtocolFields.AGENT_NO, AGENT_NO);
        //请求流水号
        builder.set(HaikeProtocolFields.REQ_ID, UUID.randomUUID().toString());
        //海科商户编号
        builder.set(HaikeBusinessFields.MERCH_NO, "833F732354990001");
        //支付类型
        builder.set(HaikeBusinessFields.PAY_TYPE, "ALI");
        //支付方式
        builder.set(HaikeBusinessFields.PAY_MODE, "BARPAY");
        //子商户号;
        builder.set(HaikeBusinessFields.SUB_MCH_ID, "2088720376041321");
        //渠道商商户号
        builder.set(HaikeBusinessFields.CHANNEL_ID, "2088721382101609");
        //终端sn编号
        builder.set(HaikeBusinessFields.TERMINAL_ID, "TM000Bny");
        //收钱吧唯一订单号
        builder.set(HaikeBusinessFields.OUT_TRADE_NO, "7894259268780971");
        //交易金额, 上送银联的交易金额，单位为分，不支持小数
        builder.set(HaikeBusinessFields.TOTAL_AMOUNT, "1");
        //实收手续费
        builder.set(HaikeBusinessFields.REAL_FEE_AMOUNT, "0");

        long startTime = 1689835041564L;
        long finishTime = 1689835041568L;
        Date txnStartDt = new Date(startTime);
        Date txnFinishDt = new Date(finishTime);
        // 交易时间
        String orderTime = format.format(txnStartDt);
        String endTime = format.format(txnFinishDt);
        //交易在收钱吧的下单时间
        builder.set(HaikeBusinessFields.ORDER_TIME, orderTime);
        //交易在收钱吧的完成时间
        builder.set(HaikeBusinessFields.END_TIME, endTime);
        //经纬度同时出现
        builder.set(HaikeBusinessFields.LONGITUDE, "102.751907");
        builder.set(HaikeBusinessFields.LATITUDE, "25.116106");

        HaikeClient haikeClient = new HaikeClient();
        Map<String, Object> response =  haikeClient.call("http://*************:8080/front-api/pay/order-send", builder.build(), accessKey);

        //{"result_msg":"交易不存在","sign":"08E25D7E160BB5DEA361A50177E30B69","result_code":"600004","return_msg":"成功","return_code":"SUCCESS"}
        System.out.println(JsonUtil.objectToJsonString(response));
    }


    private static void refundQueryTest() throws Exception {
        HaikeRequestBuilder builder = new HaikeRequestBuilder();
        //海科服务商编号
        builder.set(HaikeProtocolFields.AGENT_NO, AGENT_NO);
        //请求流水号，每次请求唯一
        builder.set(HaikeProtocolFields.REQ_ID, UUIDGenerator.getUUID());
        //海科商户编号
        builder.set(HaikeBusinessFields.MERCH_NO, MERCH_NO);
        //收钱吧退款订单号,收钱吧系统内部唯一订单号
        builder.set(HaikeBusinessFields.OUT_REFUND_NO, "789420230711001");

        HaikeClient haikeClient = new HaikeClient();
        Map<String, Object> result = haikeClient.call(REFUND_QUERY_URL, builder.build(), accessKey);


        //{"result_msg":"交易不存在","sign":"08E25D7E160BB5DEA361A50177E30B69","result_code":"600004","return_msg":"成功","return_code":"SUCCESS"}
        System.out.println(JsonUtil.objectToJsonString(result));
    }

    private static void haikeAlipayPayTest() throws Exception {
        AlipayV2NewClient client = new AlipayV2NewClient();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, SIGN_TYPE_SM2);
        builder.set(ProtocolV2Fields.APP_ID, "2017012005294919");
//        builder.set(ProtocolV2Fields.APP_ID, "1266000048330003");
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_TRADE_TRADE);
        builder.set(ProtocolV2Fields.CERT_ID, "4039667322");
//        builder.set(ProtocolV2Fields.CERT_ID, "4550759066");
        builder.bizSet(BusinessV2Fields.ORG_PID, "2088721382101609");
//        builder.bizSet(BusinessV2Fields.ORG_PID, "2088531471794160");
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO,"78924567890970001");
        builder.bizSet(BusinessV2Fields.SCENE,AlipayConstants.SCENE_BAR_CODE);
        builder.bizSet(BusinessV2Fields.AUTH_CODE,"286539910361733424");
        builder.bizSet(BusinessV2Fields.TOTAL_AMOUNT,"0.01");
        builder.bizSet(BusinessV2Fields.SUBJECT,"甜甜圈");
        builder.bizSet(BusinessV2Fields.BODY,"弄啥嘞");
        builder.bizSet(BusinessV2Fields.OPERATOR_ID,"Rain");
        builder.bizSet(BusinessV2Fields.SUB_MERCHANT, MapUtil.hashMap("merchant_name", "安庆市个体户武富强2712", "merchant_id", "2088720342123002"));
//        builder.bizSet(BusinessV2Fields.SUB_MERCHANT, MapUtil.hashMap("merchant_name", "安庆市个体户武富强2712", "merchant_id", "2088610639407093"));
//        builder.bizSet(BusinessV2Fields.AREA_INFO, "340803");
//        builder.bizSet(BusinessV2Fields.TERMINAL_INFO, MapUtil.hashMap("terminal_type", "11",
//                "location", "+31.830641/+119.972182",
//                "serial_num", "",
//                "terminal_id","X00003LL"));
        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call(GATEWAY, SIGN_TYPE_HAIKE, AlipayV2Config.PRIVATE_KEY, request);
        System.out.println(result);
    }

    private static void haikeAlipayQueryTest() throws Exception {
        AlipayV2NewClient client = new AlipayV2NewClient();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, SIGN_TYPE_SM2);
        builder.set(ProtocolV2Fields.APP_ID, "2017012005294919");
//        builder.set(ProtocolV2Fields.APP_ID, "1266000048330003");
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_TRADE_QUERY);
        builder.set(ProtocolV2Fields.CERT_ID, "4039667322");
//        builder.set(ProtocolV2Fields.CERT_ID, "4550759066");
        builder.bizSet(BusinessV2Fields.ORG_PID, "2088721382101609");
//        builder.bizSet(BusinessV2Fields.ORG_PID, "2088531471794160");
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO,"78924567890970001");
//        builder.bizSet(BusinessV2Fields.SUB_MERCHANT, MapUtil.hashMap("merchant_name", "安庆市个体户武富强2712", "merchant_id", "2088720342123002"));
//        builder.bizSet(BusinessV2Fields.SUB_MERCHANT, MapUtil.hashMap("merchant_name", "安庆市个体户武富强2712", "merchant_id", "2088610639407093"));
//        builder.bizSet(BusinessV2Fields.AREA_INFO, "340803");
//        builder.bizSet(BusinessV2Fields.TERMINAL_INFO, MapUtil.hashMap("terminal_type", "11",
//                "location", "+31.830641/+119.972182",
//                "serial_num", "",
//                "terminal_id","X00003LL"));
        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call(GATEWAY, SIGN_TYPE_HAIKE, AlipayV2Config.PRIVATE_KEY, request);
        System.out.println(result);
    }

    public static void haikeAlipayCancelTest() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayV2NewClient client = new AlipayV2NewClient();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, SIGN_TYPE_SM2);
        builder.set(ProtocolV2Fields.APP_ID, "2017012005294919");
//        builder.set(ProtocolV2Fields.APP_ID, "1266000048330003");
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_TRADE_QUERY);
        builder.set(ProtocolV2Fields.CERT_ID, "4039667322");
//        builder.set(ProtocolV2Fields.CERT_ID, "4550759066");
        builder.bizSet(BusinessV2Fields.ORG_PID, "2088721382101609");
        builder.set(ProtocolV2Fields.METHOD, AlipayV2Methods.ALIPAY_TRADE_CANCEL);
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO,"78924567890970001");
        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call(GATEWAY, SIGN_TYPE_HAIKE, AlipayV2Config.PRIVATE_KEY, request);
        System.out.println(result);
    }

    public static void haikeAlipayPrecreateTest() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayV2NewClient client = new AlipayV2NewClient();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, SIGN_TYPE_SM2);
        builder.set(ProtocolV2Fields.APP_ID, "2017012005294919");
//        builder.set(ProtocolV2Fields.APP_ID, "1266000048330003");
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_TRADE_QUERY);
        builder.set(ProtocolV2Fields.CERT_ID, "4039667322");
//        builder.set(ProtocolV2Fields.CERT_ID, "4550759066");
        builder.bizSet(BusinessV2Fields.ORG_PID, "2088721382101609");
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_TRADE_PRECREATE);
        builder.set(ProtocolV2Fields.NOTIFY_URL,"https://shouqianba.com");
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO,"7892456789091001");
        builder.bizSet(BusinessV2Fields.TOTAL_AMOUNT,"0.01");
        builder.bizSet(BusinessV2Fields.UNDISCOUNTABLE_AMOUNT,"0.01");
        builder.bizSet(BusinessV2Fields.SUBJECT,"甜甜圈");
        builder.bizSet(BusinessV2Fields.BODY,"what r u 弄啥嘞");
        builder.bizSet(BusinessV2Fields.OPERATOR_ID,"Rain");
        builder.bizSet(BusinessV2Fields.SUB_MERCHANT, MapUtil.hashMap("merchant_name", "安庆市个体户武富强2712", "merchant_id", "2088720342123002"));
        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call(GATEWAY, SIGN_TYPE_HAIKE, AlipayV2Config.PRIVATE_KEY, request);
        System.out.println(result);
    }

    public static void haikeAlipayWapOrMiniTest() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayV2NewClient client = new AlipayV2NewClient();
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE, SIGN_TYPE_SM2);
        builder.set(ProtocolV2Fields.APP_ID, "2017012005294919");
//        builder.set(ProtocolV2Fields.APP_ID, "1266000048330003");
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_TRADE_QUERY);
        builder.set(ProtocolV2Fields.CERT_ID, "4039667322");
//        builder.set(ProtocolV2Fields.CERT_ID, "4550759066");
        builder.bizSet(BusinessV2Fields.ORG_PID, "2088721382101609");
        builder.set(ProtocolV2Fields.METHOD,AlipayV2Methods.ALIPAY_TRADE_CREATE);
        builder.set(ProtocolV2Fields.NOTIFY_URL,"https://shouqianba.com");
        builder.bizSet(BusinessV2Fields.OUT_TRADE_NO,"7892456789095001");
        builder.bizSet(BusinessV2Fields.TOTAL_AMOUNT,"0.01");
        builder.bizSet(BusinessV2Fields.UNDISCOUNTABLE_AMOUNT,"0.01");
        builder.bizSet(BusinessV2Fields.BUYER_ID, "2088112825196265");
        builder.bizSet(BusinessV2Fields.SUBJECT,"甜甜圈");
        builder.bizSet(BusinessV2Fields.BODY,"what r u 弄啥嘞");
        builder.bizSet(BusinessV2Fields.OPERATOR_ID,"Rain");
        builder.bizSet(BusinessV2Fields.SUB_MERCHANT, MapUtil.hashMap("merchant_name", "安庆市个体户武富强2712", "merchant_id", "2088720342123002"));
        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call(GATEWAY, SIGN_TYPE_HAIKE, AlipayV2Config.PRIVATE_KEY, request);
        System.out.println(result);
    }

    private static void haikeWeixinPayTest() throws Exception {
        WeixinClient client = new WeixinClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, "wx2421b1c4370ec43b");
//        builder.set(com.wosai.mpay.api.weixin.ProtocolFields.SUB_APP_ID, "wx2421b1c4370ec43b");
        builder.set(ProtocolFields.MCH_ID, "1900009211");
        builder.set(ProtocolFields.SUB_MCH_ID, "541738090");
        builder.set(ProtocolFields.CHANNEL_ID, "24006513");
        builder.set(ProtocolFields.CERT_ID, "4039667322");
        builder.set(ProtocolV2Fields.SIGN_TYPE, SIGN_TYPE_SM2);
        builder.set(BusinessFields.BODY, "ipad");
        builder.set(BusinessFields.DETAIL, "ipad <32G");
        builder.set(BusinessFields.ATTACH, "no");
        builder.set(BusinessFields.OUT_TRADE_NO, "78924567890972010");
        builder.set(BusinessFields.TOTAL_FEE, "1");
        builder.set(BusinessFields.FEE_TYPE, WeixinConstants.FEE_TYPE_CNY);
        builder.set(BusinessFields.SPBILL_CREATE_IP, "***************");
        builder.set(BusinessFields.GOODS_TAG, "huanan");
        builder.set(BusinessFields.AUTH_CODE, "132925669896002557");
        builder.set(BusinessFields.NOTIFY_URL, "ZX|http://*************:18180/upay/v2/notify/weixinUnionpayWap/23c03cac7b21fac3f6969d2f3ff1f14a/73f1c%267895032334055902%263%261580000005512818%2665c14c2d-c2e9-4db0-878d-12ddba61eefe%264%261689588112903%261800%260%260");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(GATEWAY + "/wx/v1/pay/micropay", SIGN_TYPE_HAIKE, "", null, request);
        System.out.println(result);
    }

    private static void haikeWeixinQueryTest() throws Exception {
        WeixinClient client = new WeixinClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, "wx2421b1c4370ec43b");
        builder.set(ProtocolFields.MCH_ID, "1900009211");
        builder.set(ProtocolFields.SUB_MCH_ID, "541738090");
        builder.set(ProtocolFields.CHANNEL_ID, "24006513");
        builder.set(ProtocolFields.CERT_ID, "4039667322");
        builder.set(ProtocolV2Fields.SIGN_TYPE, SIGN_TYPE_SM2);
        builder.set(BusinessFields.OUT_TRADE_NO, "78924567890972003");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(GATEWAY + "/wx/v1/pay/order/qry", SIGN_TYPE_HAIKE, "", null, request);
        System.out.println(result);
    }

    private static void haikeWeixinCancelTest() throws Exception {
        WeixinClient client = new WeixinClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, "wx2421b1c4370ec43b");
        builder.set(ProtocolFields.MCH_ID, "1900009211");
        builder.set(ProtocolFields.SUB_MCH_ID, "541738090");
        builder.set(ProtocolFields.CHANNEL_ID, "24006513");
        builder.set(ProtocolFields.CERT_ID, "4039667322");
        builder.set(ProtocolV2Fields.SIGN_TYPE, SIGN_TYPE_SM2);
        builder.set(BusinessFields.OUT_TRADE_NO, "78924567890972003");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(GATEWAY + "/wx/v1/pay/order/reverse", SIGN_TYPE_HAIKE, "", null, request);
        System.out.println(result);
    }

    private static void haikeWeixinPrecreateTest() throws Exception {
        WeixinClient client = new WeixinClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, "wx2421b1c4370ec43b");
        builder.set(ProtocolFields.MCH_ID, "1900009211");
        builder.set(ProtocolFields.SUB_MCH_ID, "541738090");
        builder.set(ProtocolFields.CHANNEL_ID, "24006513");
        builder.set(ProtocolFields.CERT_ID, "4039667322");
        builder.set(ProtocolV2Fields.SIGN_TYPE, SIGN_TYPE_SM2);
        builder.set(BusinessFields.BODY, "body");
        builder.set(BusinessFields.DETAIL, "detail");
        builder.set(BusinessFields.OUT_TRADE_NO, "78924567890973001");
        builder.set(BusinessFields.TOTAL_FEE, "1");
        builder.set(BusinessFields.TRADE_TYPE, WeixinConstants.TRADE_TYPE_NATIVE);
        builder.set(BusinessFields.SPBILL_CREATE_IP, "***************");
        builder.set(BusinessFields.GOODS_TAG, "goods");
        SafeSimpleDateFormat dateFormat = new SafeSimpleDateFormat("yyyyMMddHHmmss");
        long defaultTimeExpire = 4 * 60 * 1000;
        builder.set(BusinessFields.TIME_EXPIRE, dateFormat.format(new Date(System.currentTimeMillis() + defaultTimeExpire)));
        builder.set(BusinessFields.NOTIFY_URL, WeixinConfig.NOTIFY_URL);
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(GATEWAY + "/wx/v1/pay/prepay", SIGN_TYPE_HAIKE, "", null, request);
        System.out.println(result);
    }


    public static void haikeWeixinWapOrMiniTest() throws MpayApiNetworkError, MpayException {
        WeixinClient client = new WeixinClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, "wx2421b1c4370ec43b");
        builder.set(ProtocolFields.MCH_ID, "1900009211");
        builder.set(ProtocolFields.SUB_MCH_ID, "541738090");
        builder.set(ProtocolFields.CHANNEL_ID, "24006513");
        builder.set(ProtocolFields.CERT_ID, "4039667322");
        builder.set(ProtocolV2Fields.SIGN_TYPE, SIGN_TYPE_SM2);
        builder.set(ProtocolFields.DEVICE_INFO, "devicetest");
        builder.set(BusinessFields.BODY, "ipad");
        builder.set(BusinessFields.DETAIL, "ipad <32G");
        builder.set(BusinessFields.ATTACH, "no");
        builder.set(BusinessFields.OUT_TRADE_NO, "78924567890974001");
        builder.set(BusinessFields.TOTAL_FEE, "1");
        builder.set(BusinessFields.FEE_TYPE, WeixinConstants.FEE_TYPE_CNY);
        builder.set(BusinessFields.SPBILL_CREATE_IP, "***************");
        SafeSimpleDateFormat dateFormat = new SafeSimpleDateFormat("yyyyMMddHHmmss");
        long defaultTimeExpire = 4 * 60 * 1000;
        builder.set(BusinessFields.TIME_EXPIRE, dateFormat.format(new Date(System.currentTimeMillis() + defaultTimeExpire)));
        builder.set(BusinessFields.NOTIFY_URL, WeixinConfig.NOTIFY_URL);
        builder.set(BusinessFields.TRADE_TYPE, WeixinConstants.TRADE_TYPE_JSAPI);
        builder.set(BusinessFields.OPEN_ID, "oUpF8uJhLGuhtNbqXgZVXagSsNFs");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(GATEWAY + "/wx/v1/pay/prepay", SIGN_TYPE_HAIKE, "", null, request);
        System.out.println(result);
    }

    public static void haikeUploadImage() throws IOException, MpayException {
        HaikeRequestBuilder builder = new HaikeRequestBuilder();
        //海科服务商编号
//        builder.set(HaikeProtocolFields.AGENT_NO, "ISV002571");
        builder.set(HaikeProtocolFields.AGENT_NO, AGENT_NO);

        //请求流水号，每次请求唯一
        builder.set(HaikeProtocolFields.REQ_ID, UUIDGenerator.getUUID());
        File file = new File("/Users/<USER>/Downloads/商户授权服务商收钱吧向收单机构发送分账指令的.docx");
        FileInputStream fileInputStream = new FileInputStream(file);
        byte[] byt = new byte[fileInputStream.available()];
        fileInputStream.read(byt);

        String s1 = java.util.Base64.getEncoder().encodeToString(byt);
        builder.set(HaikeBusinessFields.IMAGE, s1);

        HaikeClient haikeClient = new HaikeClient();
//        Map<String, Object> result = haikeClient.call("https://saas-front.hkrt.cn/"+UPLOAD_IMAGE_URL, builder.build(), "18f059940b244fa88cb03bdd293f7c10");
        Map<String, Object> result = haikeClient.call("http://*************:8080/"+UPLOAD_IMAGE_URL, builder.build(), accessKey);


        System.out.println(result);
    }

    public static void haikeOpenSharing() throws MpayException, MpayApiNetworkError {
        //a08409fee0b0423a9a4ca218af840183
        String imageId = "a08409fee0b0423a9a4ca218af840183";
        HaikeRequestBuilder builder = new HaikeRequestBuilder();
        //海科服务商编号
        builder.set(HaikeProtocolFields.AGENT_NO, AGENT_NO);
        //请求流水号，每次请求唯一
        builder.set(HaikeProtocolFields.REQ_ID, UUIDGenerator.getUUID());
        builder.set(HaikeBusinessFields.AGENT_APPLY_NO,"7890000001");
        builder.set(HaikeBusinessFields.MERCH_NO, "833F732354990001");
        builder.set(HaikeBusinessFields.BUSINESS_PAGE,imageId);
        builder.set(HaikeBusinessFields.HOME_PAGE,imageId);
        builder.set(HaikeBusinessFields.AGREEMENT,imageId);
        HaikeClient haikeClient = new HaikeClient();

        Map<String, Object> result = haikeClient.call("http://*************:8080/"+OPEN_SHARING_URL, builder.build(), accessKey);
        System.out.println(result);
    }

    public static void haikeOpenSharingQuery() throws MpayException, MpayApiNetworkError {
        //a08409fee0b0423a9a4ca218af840183
        HaikeRequestBuilder builder = new HaikeRequestBuilder();
        //海科服务商编号
        builder.set(HaikeProtocolFields.AGENT_NO, AGENT_NO);
        //请求流水号，每次请求唯一
        builder.set(HaikeProtocolFields.REQ_ID, UUIDGenerator.getUUID());
        builder.set(HaikeBusinessFields.AGENT_APPLY_NO,"test_apply_1");
        builder.set(HaikeBusinessFields.MERCH_NO, "833F732354990001");
        HaikeClient haikeClient = new HaikeClient();

        Map<String, Object> result = haikeClient.call("http://*************:8080/"+OPEN_SHARING_QUERY_URL, builder.build(), accessKey);
        System.out.println(result);
    }

    public static void haikeSharingPay() throws MpayException, MpayApiNetworkError {
//        DO_SHARING_URL
        //BS230914165933240872950937
        HaikeRequestBuilder builder = new HaikeRequestBuilder();
        //海科服务商编号
        builder.set(HaikeProtocolFields.AGENT_NO, AGENT_NO);
        //请求流水号，每次请求唯一
        builder.set(HaikeProtocolFields.REQ_ID, UUIDGenerator.getUUID());
        builder.set(HaikeBusinessFields.AGENT_APPLY_NO,"78970000000003");
        builder.set(HaikeBusinessFields.MERCH_NO, "833F732354990001");
        builder.set(HaikeBusinessFields.LEDGER_AMOUNT,"0.01");
        builder.set(HaikeBusinessFields.RECEIVE_NO,"833F581258120001");
        builder.set(HaikeBusinessFields.SPLIT_TYPE,HaikeBusinessFields.SPLIT_TYPE_BALANCE_SPLIT);

        HaikeClient haikeClient = new HaikeClient();
        Map<String, Object> result = haikeClient.call("http://*************:8080/"+DO_SHARING_URL, builder.build(), accessKey);
        System.out.println(result);
    }

    public static void haikeSharingQuery() throws MpayException, MpayApiNetworkError {
        HaikeRequestBuilder builder = new HaikeRequestBuilder();
        //海科服务商编号
        builder.set(HaikeProtocolFields.AGENT_NO, AGENT_NO);
        //请求流水号，每次请求唯一
        builder.set(HaikeProtocolFields.REQ_ID, UUIDGenerator.getUUID());
        builder.set(HaikeBusinessFields.AGENT_APPLY_NO,"78970000000001");
//        builder.set(HaikeBusinessFields.MERCH_NO, "833F732354990001");

//        HaikeClient haikeClient = new HaikeClient();
//        Map<String, Object> result = haikeClient.call("http://*************:8080/"+SHARING_QUERY_URL, builder.build(), accessKey);
//        System.out.println(result);

        AsyncHaikeClient asyncHaikeClient = new AsyncHaikeClient();
        asyncHaikeClient.call("http://*************:8080/" + SHARING_QUERY_URL, builder.build(), accessKey, new HttpResourceCallback<Map<String, Object>>() {
            @Override
            public void onComplete(Map<String, Object> result) {
                System.out.println(JsonUtil.toJsonStr(result));
            }

            @Override
            public void onError(Throwable t) {

            }
        });
        //{"result_msg":"成功","flow_no":"BS230914165933240872950937","sign":"2DDA0C49F6C9078DEF710266B1DA6733","result_code":"10000","return_msg":"成功","return_code":"SUCCESS","status":"SUCCESS"}
    }

    public static void haikeSharingRefund() throws MpayException, MpayApiNetworkError {
        HaikeRequestBuilder builder = new HaikeRequestBuilder();
        //海科服务商编号
        builder.set(HaikeProtocolFields.AGENT_NO, AGENT_NO);
        //请求流水号，每次请求唯一
        builder.set(HaikeProtocolFields.REQ_ID, UUIDGenerator.getUUID());
        builder.set(HaikeBusinessFields.ORI_AGENT_APPLY_NO,"78970000000001");

        builder.set(HaikeBusinessFields.AGENT_APPLY_NO,"78970000000002");
//        builder.set(HaikeBusinessFields.RECEIVE_NO, "833F732354990001");
                builder.set(HaikeBusinessFields.MERCH_NO, "833F732354990001");

        builder.set(HaikeBusinessFields.LEDGER_AMOUNT,"0.01");
//        builder.set(HaikeBusinessFields.MERCH_NO,"833F581258120001");
                builder.set(HaikeBusinessFields.RECEIVE_NO,"833F581258120001");


        builder.set(HaikeBusinessFields.SPLIT_TYPE,HaikeBusinessFields.SPLIT_TYPE_BALANCE_SPLIT_BACK);

        HaikeClient haikeClient = new HaikeClient();
        Map<String, Object> result = haikeClient.call("http://*************:8080/"+DO_SHARING_URL, builder.build(), accessKey);
        System.out.println(JsonUtil.objectToJsonString(result));
    }

    public static void haikeActivateApply() throws MpayException, MpayApiNetworkError {

        HaikeRequestBuilder builder = new HaikeRequestBuilder();
        //海科服务商编号
        builder.set(HaikeProtocolFields.AGENT_NO, AGENT_NO);
        builder.set(HaikeProtocolFields.REQ_ID, UUIDGenerator.getUUID());
        //请求流水号，每次请求唯一
        builder.set(HaikeBusinessFields.MERCH_NO, "833F305758140048");
        builder.set(HaikeBusinessFields.AGENT_APPLY_NO,"sqb_test_833F732354990001_05");
        //833F305758140021
        builder.set(HaikeBusinessFields.AGREEMENT,"f89a34b3fe4c47d6b0b54930f673d616");
        builder.set(HaikeBusinessFields.NOTIFY_URL,"https://webhook.site/5fa23731-b6c1-459e-8322-0815e48a759b");
        builder.set(HaikeBusinessFields.SIGN_NOTIFY_URL,"https://webhook.site/5fa23731-b6c1-459e-8322-0815e48a759b");
//        builder.set(HaikeBusinessFields.SIGN_NOTIFY_URL,"https://webhook.site/5fa23731-b6c1-459e-8322-0815e48a759b");
        HaikeClient haikeClient = new HaikeClient();

        Map<String, Object> result = haikeClient.call("http://*************:8080/"+"front-api/balanceSplit/activateApply", builder.build(), accessKey);
        System.out.println(JsonUtil.objectToJsonString(result));
    }

    public static void haikeActivateApplyQuery() throws MpayException, MpayApiNetworkError {

        HaikeRequestBuilder builder = new HaikeRequestBuilder();
        //海科服务商编号
        builder.set(HaikeProtocolFields.AGENT_NO, AGENT_NO);
        builder.set(HaikeProtocolFields.REQ_ID, UUIDGenerator.getUUID());
        //请求流水号，每次请求唯一
        builder.set(HaikeBusinessFields.MERCH_NO, "833F732354990001");
        builder.set(HaikeBusinessFields.AGENT_APPLY_NO,"sqb_test_833F732354990001_05");
        //833F305758140021
        builder.set(HaikeBusinessFields.QUERY_SHARING_AUDIT_TYPE,"2");
//        builder.set(HaikeBusinessFields.NOTIFY_URL,"https://webhook.site/5fa23731-b6c1-459e-8322-0815e48a759b");
//        builder.set(HaikeBusinessFields.SIGN_NOTIFY_URL,"https://webhook.site/5fa23731-b6c1-459e-8322-0815e48a759b");
//        builder.set(HaikeBusinessFields.SIGN_NOTIFY_URL,"https://webhook.site/5fa23731-b6c1-459e-8322-0815e48a759b");
        HaikeClient haikeClient = new HaikeClient();

        Map<String, Object> result = haikeClient.call("http://*************:8080/"+"front-api/balanceSplit/applicationFormStateQry", builder.build(), accessKey);
        System.out.println(result);
    }


    public static void haikeSaveMerchantRelation() throws MpayException, MpayApiNetworkError {

        HaikeRequestBuilder builder = new HaikeRequestBuilder();
        //海科服务商编号
        builder.set(HaikeProtocolFields.AGENT_NO, AGENT_NO);
        builder.set(HaikeProtocolFields.REQ_ID, UUIDGenerator.getUUID());
        //请求流水号，每次请求唯一
        builder.set(HaikeBusinessFields.MERCH_NO, "833F732354990001");
        builder.set(HaikeBusinessFields.AGENT_APPLY_NO,"sqb_test_833F732354990001_833F305758140048_05");
        //833F305758140021
        builder.set(HaikeBusinessFields.AGREEMENT,"f89a34b3fe4c47d6b0b54930f673d616");
        builder.set(HaikeBusinessFields.RECEIVE_NO,"833F305758140048");
        builder.set(HaikeBusinessFields.NOTIFY_URL,"https://webhook.site/5fa23731-b6c1-459e-8322-0815e48a759b");
//        builder.set(HaikeBusinessFields.SIGN_NOTIFY_URL,"https://webhook.site/5fa23731-b6c1-459e-8322-0815e48a759b");
//        builder.set(HaikeBusinessFields.SIGN_NOTIFY_URL,"https://webhook.site/5fa23731-b6c1-459e-8322-0815e48a759b");
        HaikeClient haikeClient = new HaikeClient();

        Map<String, Object> result = haikeClient.call("http://*************:8080/"+"front-api/balanceSplit/saveMerchantRelation", builder.build(), accessKey);
        System.out.println(result);
    }

    public static void haikeSaveMerchantRelationQuery()throws MpayException, MpayApiNetworkError{

        HaikeRequestBuilder builder = new HaikeRequestBuilder();
        //海科服务商编号
        builder.set(HaikeProtocolFields.AGENT_NO, AGENT_NO);
        builder.set(HaikeProtocolFields.REQ_ID, UUIDGenerator.getUUID());
        //请求流水号，每次请求唯一
        builder.set(HaikeBusinessFields.MERCH_NO, "833F732354990001");
//        builder.set(HaikeBusinessFields.AGENT_APPLY_NO,"sqb_test_833F732354990001_833F305758140048_05");
        //833F305758140021
//        builder.set(HaikeBusinessFields.AGREEMENT,"f89a34b3fe4c47d6b0b54930f673d616");
        builder.set(HaikeBusinessFields.RECEIVE_NO,"833F305758140048");
//        builder.set(HaikeBusinessFields.NOTIFY_URL,"https://webhook.site/5fa23731-b6c1-459e-8322-0815e48a759b");
//        builder.set(HaikeBusinessFields.SIGN_NOTIFY_URL,"https://webhook.site/5fa23731-b6c1-459e-8322-0815e48a759b");
//        builder.set(HaikeBusinessFields.SIGN_NOTIFY_URL,"https://webhook.site/5fa23731-b6c1-459e-8322-0815e48a759b");
        HaikeClient haikeClient = new HaikeClient();

        Map<String, Object> result = haikeClient.call("http://*************:8080/"+"front-api/balanceSplit/merchantRelationQry", builder.build(), accessKey);
        System.out.println(result);
    }
    public static void main(String[] args) throws Exception {
//        haikeOpenSharingQuery();
//        haikeOpenSharing();
//        haikeSharingPay();
//        haikeUploadImage();
//        haikeSharingQuery();
//        haikeSharingRefund();
//        haikeUploadImage();
//        orderNotifyTest();
//        refundQueryTest();
//        haikeAlipayPayTest();
//        haikeAlipayQueryTest();
//        haikeAlipayCancelTest();
//        haikeAlipayPrecreateTest();
//        haikeAlipayWapOrMiniTest();
//        haikeWeixinPayTest();
//        haikeWeixinQueryTest();
//        haikeWeixinCancelTest();
//        haikeWeixinPrecreateTest();
//        haikeWeixinWapOrMiniTest();
//        haikeActivateApply();
//        haikeActivateApplyQuery();
//        haikeSaveMerchantRelation();
        haikeSaveMerchantRelationQuery();
    }


}
