package com.wosai.mpay.api.haike;

import com.wosai.mpay.api.HttpResourceCallback;
import com.wosai.mpay.api.lakala.LakalaConstants;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

public class AsyncHaikeClient {
    public static final Logger logger = LoggerFactory.getLogger(AsyncHaikeClient.class);
    protected int connectTimeout = 2000;
    protected int readTimeout = 5000;

    private RequestConfig requestConfig;
    private CloseableHttpAsyncClient client;

    public AsyncHaikeClient() {
        initClient();
    }

    public AsyncHaikeClient(int readTimeout, int connectTimeout){
        this.readTimeout = readTimeout;
        this.connectTimeout = connectTimeout;
        initClient();
    }

    public void initClient(){
        requestConfig = RequestConfig.custom().setConnectionRequestTimeout(connectTimeout).setConnectTimeout(connectTimeout).setSocketTimeout(readTimeout).build();
        client = AsyncClientUtil.getCloseableHttpAsyncClient(null, null);
    }

    public void call(String serviceUrl, Map<String, Object> request, String accessKey, HttpResourceCallback<Map<String, Object>> callback){
        try{
            doCall(serviceUrl, request, accessKey, callback);
        }catch (Throwable t){
            callback.onError(t);
        }
    }

    private void doCall(String serviceUrl, Map<String, Object> request, String accessKey, HttpResourceCallback<Map<String, Object>> callback) throws Exception {
        AsyncClientUtil.logRequest(logger, JsonUtil.objectMapper.writeValueAsString(request));

        String retSigned = null;
        try {
            retSigned = HaikeSignature.getSign(accessKey, request);
        } catch (Exception e) {
            logger.error("加签异常", e);
        }
        request.put(HaikeProtocolFields.SIGN, retSigned);
        logger.info("encrypt request is {}", JsonUtil.objectToJsonString(request));


        HttpPost httpPost = new HttpPost(serviceUrl);
        httpPost.setConfig(requestConfig);
        httpPost.setHeader("Content-Type", "application/json;charset=utf-8");
        httpPost.setEntity(new ByteArrayEntity(JsonUtil.objectMapper.writeValueAsBytes(request)));
        long start = System.currentTimeMillis();
        client.execute(httpPost, AsyncClientUtil.getFutureCallback(logger, LakalaConstants.CHARSET_UTF8, AsyncClientUtil.ResponseType.JSON, HttpResourceCallback.<Map<String,Object>>create(
                TracingUtil.getTraceCarrierItem(),
                (response, t) -> {
                    String responseStr = null;
                    try {
                        responseStr = JsonUtil.objectToJsonString(response);
                    } catch (MpayException e) {
                        responseStr = response.toString();
                    }

                    AsyncClientUtil.logResponse(logger, serviceUrl, System.currentTimeMillis() - start, response != null ? responseStr : null, t);
                    if(t != null){
                        callback.onError(t);
                        return;
                    }else{
                        callback.onComplete(response);
                    }
                }
        )));
    }

    private static String getNonceStr() {
        return ThreadLocalRandom.current().nextLong() + "";
    }
}
