package com.wosai.mpay.api.haike;

/***
 * @ClassName: HaikeConstants
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/7/10 5:12 PM
 */
public class HaikeConstants {

    //支付类型  支付宝：ALI  微信：WX  云闪付：UNIONQR
    public static final String PAY_TYPE_ALI = "ALI";
    public static final String PAY_TYPE_WX = "WX";
    public static final String PAY_TYPE_UNIONQR = "UNIONQR";

    //支付方式  主扫：NATIVE  被扫：BARPAY  公众号：JSAPI
    public static final String PAY_MODE_NATIVE = "NATIVE";
    public static final String PAY_MODE_BARPAY = "BARPAY";
    public static final String PAY_MODE_JSAPI = "JSAPI";

    //业务返回码
    public static final String RESULT_CODE_SUCCESS = "10000"; //成功
    public static final String RESULT_CODE_ALREADY_NOTIFY_HAIKE = "600001"; // 订单已推送成功
    public static final String RESULT_CODE_REPEAT_NO = "600003"; // 订单号重复
    public static final String RESULT_CODE_ORDER_NOT_EXIST = "600004"; // 交易不存在

    //业务返回信息
    public static final String RESULT_MSG_REPEAT_NO = "订单号重复"; // 订单号重复
    public static final String RESULT_MSG_ORDER_NOT_EXIST = "交易不存在"; // 订单号重复
    public static final String RESULT_MSG_ALREADY_NOTIFY_HAIKE = "该订单已推送成功，请勿重复推送"; // 该订单已推送成功，请勿重复推送

    //退款状态
    public static final String REFUND_STATUS_SUCCESS = "1"; //成功（退款申请接收成功，退款的到账时间以实际为准）
    public static final String REFUND_STATUS_FAIL = "2";    //失败（退款申请失败，错误原因参考error_msg）
    public static final String REFUND_STATUS_UNKNOWN = "3"; //结果未知（退款申请处理结果未知,请调用退款查询接口获取退款结果状态）

    public static final String SHARING_STATUS_SUCCESS = "SUCCESS";
    public static final String SHARING_STATUS_FAIL = "FAIL";
    public static final String SHARING_STATUS_DOING = "DOING";

    public static final String QRCODE_TYPE_DYNAMIC = "0"; //二维码类型 动态码
    public static final String QRCODE_TYPE_STATIC = "1"; //二维码类型 静态码


    public static final String QUERY_SHARING_AUDIT_TYPE_ACTIVATE = "1"; //分账申请单查询
    public static final String QUERY_SHARING_AUDIT_TYPE_SIGN = "2"; //商户签约结果查询

    public static final String QUERY_SHARING_AUDIT_STATUS_WAIT = "0";
    public static final String QUERY_SHARING_AUDIT_STATUS_SUCCESS = "1";
    public static final String QUERY_SHARING_AUDIT_STATUS_REJECT = "2";


    public static final String SIGN_STATUS_INIT = "0"; //草稿
    public static final String SIGN_STATUS_DOING = "1"; //签署中
    public static final String SIGN_STATUS_SUCCESS = "2"; //签署完成
    public static final String SIGN_STATUS_CANCEL = "3"; //撤销
    public static final String SIGN_STATUS_REJECT = "7"; //拒签

    public static final String AUTHORIZED_STATUS_BE_AUTHORIZED = "0"; //已授权
    public static final String AUTHORIZED_STATUS_UNAUTHORIZED = "1"; //未授权

    public static final String OPEN_SHARING_PROJECT_MARK_AUTO_SIGN = "01";//开通分账时上送标识可以触发免电签
}
