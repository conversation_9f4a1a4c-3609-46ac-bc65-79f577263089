package com.wosai.mpay.api.haike;

/***
 * @ClassName: HaikeBusinessFields
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/7/10 5:13 PM
 */
public class HaikeBusinessFields {

    public static final String MERCH_NO = "merch_no";   //海科商户编号
    public static final String PAY_TYPE = "pay_type";   //支付类型  支付宝：ALI  微信：WX  云闪付：UNIONQR
    public static final String PAY_MODE = "pay_mode";   //支付方式  主扫：NATIVE 被扫：BARPAY 公众号：JSAP
    public static final String SUB_MCH_ID = "sub_mch_id";   //通道商户号
    public static final String CHANNEL_ID = "channel_id";   //渠道商商户号
    public static final String TERMINAL_ID = "terminal_id";   //8位终端号,(交易通知被扫必传)
    public static final String QR_NO = "qr_no";   //云闪付C2B码，云闪付被扫必传
    public static final String OUT_TRADE_NO = "out_trade_no";   //收钱吧唯一订单号
    public static final String TOTAL_AMOUNT = "total_amount";   //交易金额
    //营销补贴金额 注意：服务商需开通营销补贴功能，且补贴金额需小于等于营销补贴账户余额，单位为分，不支持小数
    public static final String MARKET_SUBSIDY_AMOUNT = "market_subsidy_amount";
    public static final String REAL_FEE_AMOUNT = "real_fee_amount";     //实收手续费,单位为分，不支持小数
    public static final String ORDER_TIME = "order_time";     //交易在收钱吧的下单时间, 交易下单时间yyyy-MM-dd HH:mm:ss
    public static final String END_TIME = "end_time";     //交易在收钱吧的完成时间, 交易完成时间yyyy-MM-dd HH:mm:ss
    public static final String LONGITUDE = "longitude";     //经度 经纬度同时出现
    public static final String LATITUDE = "latitude";     //维度 经纬度同时出现

    public static final String REFUND_NO = "refund_no";   //海科退款订单号
    public static final String OUT_REFUND_NO = "out_refund_no";     //收钱吧退款订单号, 收钱吧系统内部唯一订单号
    public static final String REFUND_AMOUNT = "refund_amount";     //退款金额, 上送银联的退款请求金额，单位为分
    public static final String REAL_REFUND_FEE_AMOUNT = "real_refund_fee_amount";     //实退款手续费金额, 单位为分
    public static final String DISCOUNT_AMOUNT = "discount_amount";     //退无资金券金额, 不走结算资金的免充值型优惠券金额，单位为分
    public static final String NOTIFY_URL = "notify_url";     //异步通知地址

    public static final String PRIVATE_KEY = "private_key";
    public static final String PUBLIC_KEY = "public_key";

    public static final String IMAGE = "image"; //图片base64
    public static final String AGENT_APPLY_NO = "agent_apply_no"; //服务商申请编号
    public static final String BUSINESS_PAGE = "business_page"; //商户与服务商业务合作协议 上传图片到对方的接口 返回对应的id
    public static final String HOME_PAGE ="home_page";//分账方和分账接收方合作协议 上传图片到对方的接口 返回对应的id
    public static final String CORPORATE_AGREEMENT = "corporateAgreement"; //商户与收单机构分账协议及法人授权函协议
    public static final String AGREEMENT = "agreement"; //商户与海科分账协议
    public static final String SHARING_NOTIFY_URL = "notify_url"; //异步通知地址
    public static final String LEDGER_AMOUNT = "ledger_amount"; //分账金额 金额为元为单位
    public static final String RECEIVE_NO = "receive_no"; //分账接收方商户号
    public static final String REMARK = "remark";   //备注

    public static final String SPLIT_TYPE = "split_type"; //分账类型
    public static final String SPLIT_TYPE_BALANCE_SPLIT = "BALANCE_SPLIT"; //余额分账
    public static final String SPLIT_TYPE_BALANCE_SPLIT_BACK = "BALANCE_SPLIT_BACK"; //分账退回

    public static final String ORI_AGENT_APPLY_NO = "ori_agent_apply_no"; //原分账服务商申请单号
    public static final String SIGN_NOTIFY_URL = "sign_notify_url"; //商户分账签约状态回调地址
    public static final String SIGN_REDIRECT_URL = "sign_redirect_url"; //商户分账签约重定向定制

    public static final String QUERY_SHARING_AUDIT_TYPE = "type";
    public static final String OPEN_SHARING_PROJECT_MARK = "project_mark";

}
