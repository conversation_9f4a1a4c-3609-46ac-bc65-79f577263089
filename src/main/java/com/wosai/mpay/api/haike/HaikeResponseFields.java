package com.wosai.mpay.api.haike;

/***
 * @ClassName: HaikeResponseFields
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/7/10 5:14 PM
 */
public class HaikeResponseFields {

    public static final String RETURN_CODE = "return_code";   //返回状态码, SUCCESS/FAIL 此字段是通信标识，非业务响应码
    public static final String RETURN_MSG = "return_msg";   //返回信息, 当return_code为FAIL时返回信息为错误原因 ，例如：签名失败
    public static final String RESULT_CODE = "result_code";   //业务返回码, 10000：成功  其他：失败
    public static final String RESULT_MSG = "result_msg";   //业务返回信息
    public static final String TRADE_NO = "trade_no";   //海科订单号
    public static final String RECEIVABLE_FEE_AMOUNT = "receivable_fee_amount";   //应收手续费金额，单位为分
    public static final String REAL_FEE_AMOUNT = "real_fee_amount";   //实收手续费，单位为分
    public static final String FEE_SUBSIDY_AMOUNT = "fee_subsidy_amount";   //手续费补贴金额，单位为分
    public static final String ORDER_AMOUNT = "order_amount";   //海科侧订单总金额（上送银联交易金额+营销补贴总金额）单位为分
    public static final String TOTAL_AMOUNT = "total_amount";   //上送银联金额, 单位为分
    public static final String MARKET_SUBSIDY_AMOUNT = "market_subsidy_amount";   //营销补贴金额，单位为分
    public static final String SETTLE_TOTAL_AMOUNT = "settle_total_amount";   //商户结算金额=通道订单应结金额-实收手续费+营销补贴总金额，单位为分

    //退款状态
    //  1：成功（退款申请接收成功，退款的到账时间以实际为准）；
    //  2：失败（退款申请失败，错误原因参考error_msg）；
    //  3：结果未知（退款申请处理结果未知,请调用退款查询接口获取退款结果状态）
    public static final String REFUND_STATUS = "refund_status";
    public static final String OUT_TRADE_NO = "out_trade_no";   //原交易收钱吧订单号
    public static final String REFUND_NO = "refund_no";   //海科退款订单号
    public static final String OUT_REFUND_NO = "out_refund_no";   //收钱吧退款订单号
    public static final String TRADE_END_TIME = "trade_end_time";   //退款在海科的完成时间, 退款成功时返回，yyyy-MM-dd HH:mm:ss
    public static final String REFUND_AMOUNT = "refund_amount";   //本次申请退款金额，单位为分
    public static final String REMANENT_AMOUNT = "remanent_amount";   //通道剩余可退金额，单位为分
    public static final String REMANENT_MARKET_SUBSIDY_AMOUNT = "remanent_market_subsidy_amount";   //剩余可退补贴总金额，单位为分
    public static final String ATTACH = "attach";   //通道原生参数,参见对应文档

    public static final String STATUS = "status";
    public static final String SHARING_FLOW_NO = "flow_no";
    public static final String SHARING_OPEN_APPLY_NO = "apply_no";


    /**  分账商户签约 相关 **/
    public static final String AUDIT_MSG = "auditMsg"; //审核信息
    public static final String AUDIT_TIME = "auditTime"; //审核时间
    public static final String SIGN_URL = "signUrl";//电签信息
    public static final String SHORT_URL = "shortUrl";//电签信息 短链

    public static final String SIGN_STATUS = "signStatus"; //电签结果  0-草稿 1-签署中 2-完成 3-撤销  7-拒签
    public static final String MERCH_NO = "merchNo"; //分账方商户编号
    public static final String AGENT_APPLY_NO = "agentApplyNo"; //服务商申请编号
    public static final String AUTHORIZED_STATUS = "authorizedStatus"; //0：已授权 1：未授权

    public static final String IMAGE_ID = "image_id";
}
