package com.wosai.mpay.api.haike;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HaikeSignature;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/***
 * @ClassName: HaikeClient
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/7/10 6:16 PM
 */
public class HaikeClient {
    public static final Logger log = LoggerFactory.getLogger(HaikeClient.class);

    private static final String CONTENT_TYPE = "application/json;charset=utf-8";

    private int connectTimeout = 3000;
    private int readTimeout = 30000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map<String, Object> call(String serviceUrl, Map<String, Object> request, String accessKey) throws MpayException, MpayApiNetworkError {
        log.info("original request is {}" , JsonUtil.objectToJsonString(request));

        String retSigned = null;
        try {
            retSigned = HaikeSignature.getSign(accessKey, request);
        } catch (Exception e) {
            log.error("加签异常", e);
        }

        request.put(HaikeProtocolFields.SIGN, retSigned);
        log.info("encrypt request is {}", JsonUtil.objectToJsonString(request));
        String requestStr = JsonUtil.objectToJsonString(request);
        // 使用 "application/json" contentType发起请求
        String responseStr = HttpClientUtils.doPost(HaikeClient.class.getName(), null, null, serviceUrl, CONTENT_TYPE, requestStr, "utf-8", connectTimeout, readTimeout);

        log.info("response {}", responseStr);
        try{
            Map<String, Object> result = JsonUtil.jsonStringToObject(responseStr, Map.class);
            return result;
        }catch (Exception e){
            throw new MpayApiUnknownResponse("parse response error: " + e.getMessage(), e);
        }
    }
}
