package com.wosai.mpay.api.haike;

import java.util.LinkedHashMap;
import java.util.Map;

/***
 * @ClassName: HaikeRequestBuilder
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/7/10 11:05 PM
 */
public class HaikeRequestBuilder {

    private Map<String,Object> request;

    public HaikeRequestBuilder(){
        request = new LinkedHashMap<String,Object>();
    }

    public void set(String field, Object value) {
        request.put(field,  value);
    }

    public Map<String,Object> build(){
        return request;
    }
}
