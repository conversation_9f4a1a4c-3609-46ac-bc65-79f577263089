package com.wosai.mpay.api.cmbapp;

import com.google.common.collect.Maps;
import com.wosai.mpay.exception.MpayApiReadError;
import com.wosai.mpay.exception.MpayClientError;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.mpay.util.cmbapp.CmbHttpClientUtils;
import com.wosai.mpay.util.cmbapp.CmbLifeOkHttpClientBuilder;
import com.wosai.mpay.util.cmbapp.CmbLifeUtils;
import com.wosai.mpay.util.cmbapp.URLUtils;
import com.wosai.pantheon.util.MapUtil;
import kl.okhttp3.OkHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 招行生活APP请求客户端
 */
public class CmbAppClient {
    public static final Logger logger = LoggerFactory.getLogger(CmbAppClient.class);

    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public CmbAppClient() {
    }

    public CmbAppClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    /**
     * @param url            请求地址
     * @param privateKey     私钥
     * @param requestBuilder 请求参数
     * @return
     * @throws Exception
     */
    public Map<String, Object> call(String url, String privateKey, RequestBuilder requestBuilder) throws Exception {
        if (StringUtils.isEmpty(url)) {
            throw new MpayClientError("招行请求地址为空");
        }
        if (StringUtils.isEmpty(privateKey)) {
            throw new MpayClientError("私钥地址为空");
        }
        Map<String, String> requestMap = Maps.newHashMap(requestBuilder.getRequest());

        String sign = CmbLifeUtils.signForRequest(requestBuilder.getFuncName(), requestMap, privateKey);
        requestMap.put(BusinessFields.SIGN, sign);

        if (logger.isDebugEnabled()) {
            logger.debug("request {}", JsonUtil.objectToJsonString(requestMap));
        }
        String paramsUrlEncode = URLUtils.mapToQueryString(Maps.newHashMap(requestMap), false, true);
        String responseStr = HttpClientUtils.doPost(CmbAppClient.class.getName() + ".call", null, null,
                url, CmbAppConstants.CONTENT_TYPE, paramsUrlEncode,
                null, CmbAppConstants.CHARSET, connectTimeout, readTimeout);
        if (logger.isDebugEnabled()) {
            logger.debug("response {}", responseStr);
        }
        try {
            Map<String, Object> result = JsonUtil.jsonStringToObject(responseStr, Map.class);
            return result;
        } catch (Exception e) {
            throw new MpayApiReadError("parse response error.", e);
        }
    }

    public Map<String, Object> payCall(
            String url,
            String merPrivateKey,
            String cmbPublicKey,
            String cert,
            RequestBuilder requestBuilder) throws Exception {
        //构建请求入参
        Map<String, String> requestMap = buildPayRequestMap(cmbPublicKey, merPrivateKey, requestBuilder);
        String paramsUrlEncode = URLUtils.mapToQueryString(Maps.newHashMap(requestMap), false, true);
        if (logger.isDebugEnabled()) {
            logger.debug("payCall request {}", requestMap);
        }
        String responseStr = CmbHttpClientUtils.doPost(CmbAppClient.class.getName() + ".payCall", url, paramsUrlEncode, s -> {
            //构建请求
            String[] certChain = new String[]{cert};
            OkHttpClient.Builder builder = (new CmbLifeOkHttpClientBuilder(certChain))
                    .getOkHttpClientBuilder()
                    .connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
                    .readTimeout(readTimeout, TimeUnit.MILLISECONDS);
            return builder.build();
        });
        if (logger.isDebugEnabled()) {
            logger.debug("payCall response {}", responseStr);
        }
        try {
            Map<String, Object> result = JsonUtil.jsonStringToObject(responseStr, Map.class);
            return buildPayResult(result, merPrivateKey);
        } catch (Exception e) {
            throw new MpayApiReadError("parse response error.", e);
        }
    }

    /**
     * 构建下单请求入参
     *
     * @param cmbPublicKey   招行公钥
     * @param merPrivateKey  商户私钥
     * @param requestBuilder 请求入参
     * @return
     * @throws Exception
     */
    private Map<String, String> buildPayRequestMap(
            String cmbPublicKey, String merPrivateKey, RequestBuilder requestBuilder) throws Exception {
        String body = JsonUtil.objectToJsonString(requestBuilder.getBusinessMap());
        if (logger.isDebugEnabled()) {
            logger.debug("method[buildPayRequestMap] 明文body:{}", body);
        }
        String encryptBody = CmbLifeUtils.encrypt(body, cmbPublicKey);
        if (logger.isDebugEnabled()) {
            logger.debug("method[buildPayRequestMap] 密文body:{}", encryptBody);
        }
        Map<String, String> requestMap = Maps.newHashMap(requestBuilder.getCommonMap());
        requestMap.put(BusinessFields.KEY_ALIAS, "CO_PUB_KEY_SM2");
        requestMap.put(BusinessFields.CMB_KEY_ALIAS, "SM2_CMBLIFE_PLUS");
        requestMap.put(BusinessFields.ENCRYPT_BODY, encryptBody);

        Map<String, String> map = Maps.newHashMap(requestMap);
        String sign = CmbLifeUtils.signForRequest(requestBuilder.getFuncName(), map, merPrivateKey);

        requestMap.put(BusinessFields.SIGN, sign);
        if (logger.isDebugEnabled()) {
            logger.debug("method[buildPayRequestMap] request param:{}", JsonUtil.objectToJsonString(requestMap));
        }
        return requestMap;
    }

    /**
     * 构建下单返回结果
     *
     * @param result        响应结果
     * @param merPrivateKey 商户私钥
     * @return
     * @throws Exception
     */
    private Map<String, Object> buildPayResult(Map<String, Object> result, String merPrivateKey) throws Exception {
        if (MapUtil.isEmpty(result)) {
            throw new RuntimeException("响应结果为空");
        }
        String respCode = MapUtil.getString(result, ResponseFields.RESP_CODE);
        //接口调用失败则直接返回无需解密body
        if (!Objects.equals(CmbAppConstants.RESP_CODE_SUCCESS, respCode)) {
            return result;
        }
        //解密body
        String encryptBody = MapUtil.getString(result, ResponseFields.ENCRYPT_BODY);
        String decryptBody = CmbLifeUtils.decrypt(encryptBody, merPrivateKey);
        if (logger.isDebugEnabled()) {
            logger.debug("method[buildPayResult] 明文body:{}", decryptBody);
        }
        try {
            Map<String, Object> decryptBodyMap = JsonUtil.jsonStringToObject(decryptBody, Map.class);
            result.putAll(decryptBodyMap);
            return result;
        } catch (Exception e) {
            throw new MpayApiReadError("parse decryptBody error.", e);
        }
    }
}
