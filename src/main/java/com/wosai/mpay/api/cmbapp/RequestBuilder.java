package com.wosai.mpay.api.cmbapp;

import com.google.common.collect.Maps;
import com.wosai.pantheon.util.MapUtil;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

/**
 * <AUTHOR> Date: 2020/10/14 Time: 11:05 上午
 */
public class RequestBuilder {
    /**
     * 请求方法名称
     */
    private final String funcName;
    /**
     * 请求参数mapping
     */
    private final Map<String, String> request;

    public RequestBuilder(String funcName) {
        this.funcName = funcName;
        request = new TreeMap<>();
    }

    public RequestBuilder set(String key, String value) {
        request.put(key, value);
        return this;
    }

    public Map<String, String> getRequest() {
        return request;
    }

    public String getFuncName() {
        return funcName;
    }

    /**
     * 公共参数
     *
     * @return
     */
    public Map<String, String> getCommonMap() {
        if (MapUtil.isEmpty(request)) {
            return request;
        }
        Map<String, String> map = Maps.newHashMap();
        for (String fieldName : BusinessFields.COMMON_FIELDS) {
            String value = request.get(fieldName);
            if (Objects.nonNull(value)) {
                map.put(fieldName, value);
            }
        }
        return map;
    }

    /**
     * 业务参数
     *
     * @return
     */
    public Map<String, String> getBusinessMap() {
        if (MapUtil.isEmpty(request)) {
            return request;
        }
        Map<String, String> businessMap = Maps.newHashMap(request);
        BusinessFields.COMMON_FIELDS.forEach(businessMap::remove);
        return businessMap;
    }
}
