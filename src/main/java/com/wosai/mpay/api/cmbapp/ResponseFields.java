package com.wosai.mpay.api.cmbapp;

public interface ResponseFields {
    /**
     * 返回码
     */
    String RESP_CODE = "respCode";
    /**
     * 返回信息
     */
    String RESP_MSG = "respMsg";
    /**
     * 服务端时间 格式yyyyMMddHHmmss
     */
    String DATE = "date";
    /**
     * 商户密钥名称 国密返回：CO_PUB_KEY_SM2
     */
    String KEY_ALIAS = "keyAlias";
    /**
     * 掌上生活密钥名称 国密返回：SM2_CMBLIFE
     */
    String CMB_KEY_ALIAS = "cmbKeyAlias";
    /**
     * 签名值
     */
    String SIGN = "sign";
    /**
     * 消息体
     */
    String BODY = "body";

    /**
     * 密文body
     */
    String ENCRYPT_BODY = "encryptBody";
    
    /**
     * 支付结果
     * 
     * 0：未开始
     * 1：待支付
     * 2：支付成功
     * 3：撤销中
     * 4：已撤销
     * 6：退款中
     * 7：已退款
     */
    String ORDER_STATUS = "orderStatus";

    /**
     * 撤销结果 1成功 3失败
     */
    String REVERSE_RESULT = "reverseResult";

    /**
     * 撤销结果描述
     */
    String REVERSE_RESULT_DESC = "reverseResultDesc";
    /**
     * 订单生成时间
     */
    String ORDER_DATE = "orderDate";
    /**
     * 订单总金额
     */
    String TOTAL_AMT = "totalAmt";
    /**
     * 订单积分
     */
    String BONUS_POINT = "bonusPoint";
    /**
     * 商户应收金额
     */
    String RECEIVABLE_AMT = "receivableAmt";
    /**
     * 佣金
     */
    String COMMISSION_AMT = "commissionAmt";
    /**
     * 发票金额
     */
    String INVOICE_AMT = "invoiceAmt";
    /**
     * 代金券抵扣金额
     */
    String COUPON_AMT = "couponAmt";
    /**
     * 代金券招行补贴金额
     */
    String COUPON_CMB_SUBSIDY_AMT = "couponCmbSubsidyAmt";
    /**
     * 代金券商户补贴金额
     */
    String COUPON_MER_SUBSIDY_AMT = "couponMerSubsidyAmt";
    /**
     * 优惠券 多张券码逗号分隔
     */
    String COUPONS = "coupons";
    /**
     * 一招过余额客户实付金额
     */
    String ACTUAL_AMT = "actualAmt";
    /**
     * 随机立减金额
     */
    String RANDOM_DISCOUNT_AMT = "randomDiscountAmt";
    /**
     * 满减优惠金额
     */
    String FULL_DISCOUNT_AMT = "fullDiscountAmt";
    /**
     * 满减商户总补贴
     */
    String FULL_DISCOUNT_MER_SUBSIDY_AMT = "fullDiscountMerSubsidyAmt";
    /**
     * 满减招行总补贴
     */
    String FULL_DISCOUNT_CMB_SUBSIDY_AMT = "fullDiscountCmbSubsidyAmt";
    /**
     * 买单抵扣券优惠金额
     */
    String VOUCHER_AMT = "voucherAmt";
    /**
     * 买单抵扣券招行补贴
     */
    String VOUCHER_CMB_SUBSIDY_AMT = "voucherCmbSubsidyAmt";
    /**
     * 买单抵扣券商户补贴
     */
    String VOUCHER_MER_SUBSIDY_AMT = "voucherMerSubsidyAmt";

}
