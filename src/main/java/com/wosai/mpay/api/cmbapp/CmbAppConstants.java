package com.wosai.mpay.api.cmbapp;

import java.util.List;

import com.google.common.collect.ImmutableList;

public interface CmbAppConstants {

    String CHARSET = "UTF-8";

    String CONTENT_TYPE = "application/x-www-form-urlencoded;charset=UTF-8";

    //------------------------------ 调用响应状态码 ------------------------------
    /**
     * 接口调用成功 状态码
     */
    String RESP_CODE_SUCCESS = "1000";
    /**
     * 系统异常
     */
    String RESP_CODE_SYSTEM_ERROR = "1001";
    /**
     * 数据异常，请重试
     */
    String RESP_CODE_DATA_ERROR = "7002";
    /**
     * 服务异常[xxx]，请重试
     */
    String RESP_CODE_SERVER_ERROR = "7003";
    /**
     * 服务响应超时，请重试
     */
    String RESP_CODE_SERVER_TIMEOUT = "9997";
    /**
     * 服务发现异常
     */
    String RESP_CODE_SERVER_DISCOVERY_ERROR = "9998";
    /**
     * 操作失败
     */
    String RESP_CODE_OPERATE_ERROR = "9998";
    /**
     * 系统繁忙，请稍后再试！[AG36]（验签错误）
     */
    String RESP_CODE_SYSTEM_BUSY = "0999";

    //------------------------------ 调用接口名称 ------------------------------
    /**
     * 下单
     */
    String FUNC_NAME_ODDER_PAY = "yummyOrderPay";
    /**
     * 订单查询
     */
    String FUNC_NAME_ORDER_QUERY = "yummyOrderQuery";
    /**
     * 订单撤销
     */
    String FUNC_NAME_ORDER_CANCEL = "yummyOrderCancel";
    /**
     * 申请退款
     */
    String FUNC_NAME_ORDER_REFUND = "yummyOrderRefund";
    /**
     * 退款查询
     */
    String FUNC_NAME_REFUND_QUERY = "yummyRefundQuery";


    // ------------------------------ 订单查询,支付结果 ------------------------------
    /**
     * 未开始
     */
    String ORDER_STATUS_READY = "0";
    /**
     * 待支付
     */
    String ORDER_STATUS_PAY_WAIT = "1";
    /**
     * 支付成功
     */
    String ORDER_STATUS_PAY_FINISH = "2";
    /**
     * 撤销中
     */
    String ORDER_STATUS_CANCELING = "3";
    /**
     * 已撤销
     */
    String ORDER_STATUS_CANCEL_FINISH = "4";
    /**
     * 退款中
     */
    String ORDER_STATUS_REFUNDING = "6";
    /**
     * 已退款
     */
    String ORDER_STATUS_REFUND_FINISH = "7";

    //------------------------------ 撤销结果 ------------------------------
    /**
     * 撤销成功
     */
    String REVERSE_RESULT_SUCCESS = "1";

    /**
     * 撤销失败
     */
    String REVERSE_RESULT_FAILED = "3";

    // ------------------------------ 退款结果 ------------------------------
    /**
     * 成功
     */
    String REFUND_RESULT_SUCCESS = "1";
    /**
     * 退款中
     */
    String REFUND_RESULT_ING = "2";
    /**
     * 退款失败
     */
    String REFUND_RESULT_FAILED = "3";


    // ------------------------------ 常用列表集合体 ------------------------------
    /**
     * 未知状态列表
     */
    List<String> UNKNOWN_RESP_CODES = ImmutableList.of(
            CmbAppConstants.RESP_CODE_SYSTEM_ERROR, CmbAppConstants.RESP_CODE_DATA_ERROR,
            CmbAppConstants.RESP_CODE_SERVER_ERROR, CmbAppConstants.RESP_CODE_SERVER_TIMEOUT,
            CmbAppConstants.RESP_CODE_SERVER_DISCOVERY_ERROR, CmbAppConstants.RESP_CODE_OPERATE_ERROR,
            CmbAppConstants.RESP_CODE_SYSTEM_BUSY
    );

    /**
     * 订单完结状态列表
     */
    List<String> FINISH_ORDER_STATUS = ImmutableList.of(
            CmbAppConstants.ORDER_STATUS_PAY_FINISH,
            CmbAppConstants.ORDER_STATUS_CANCEL_FINISH,
            CmbAppConstants.ORDER_STATUS_REFUND_FINISH
    );

}
