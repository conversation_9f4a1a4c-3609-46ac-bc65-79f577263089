package com.wosai.mpay.api.cmbapp;

import com.google.common.collect.ImmutableList;

import java.util.List;

/**
 * 请求协议定义
 *
 * <AUTHOR>
 */
public interface BusinessFields {

    String KEY_ALIAS = "keyAlias";

    String CMB_KEY_ALIAS = "cmbKeyAlias";

    String MID = "mid";

    String AID = "aid";

    String SIGN = "sign";

    String RANDOM = "random";

    String DATE = "date";

    String MER_ORDER_NO = "merOrderNo";  //商户订单号

    String MER_NO = "merNo";    //商户号

    String STR_NO = "strNo";    //商户的门店号

    String AMOUNT = "amount";    //订单总金额

    String BONUS_POINT = "bonusPoint";    //订单积分

    String EXCLUDE_AMT = "excludeAmt";    //不参与优惠金额

    String TAG_CODE = "tagCode";    //账户二维码

    String NOTIFY_URL = "notifyUrl";    //支付回调地址地址 暂时不用

    String PRODUCT = "product";    //商品信息

    String CMB_ORDER_NO = "cmbOrderNo";    //银行订单号

    String MER_REFUND_ORDER_NO = "merRefundOrderNo";    //商户退款订单号

    String TOTAL_AMT = "totalAmt";    //订单总金额

    String REFUND_AMT = "refundAmt";    //退款金额

    String REFUND_DESC = "refundDesc";    //退款原因

    String OPERATOR = "operator";    //操作者 可以是终端号

    String ENCRYPT_BODY ="encryptBody"; //加密报文

    /**
     * 公共字段集合
     */
    List<String> COMMON_FIELDS = ImmutableList.of(DATE, RANDOM, AID, MID, KEY_ALIAS, CMB_KEY_ALIAS);
}
