package com.wosai.mpay.api.yop;

/**
 * 支付场景常量类
 * 根据不同支付渠道提供对应的场景值选项
 */
public class SceneConstants {

    /**
     * 微信支付场景
     */
    public static class WechatScene {
        public static final String ONLINE = "ONLINE";             // 线上
        public static final String OFFLINE = "OFFLINE";           // 线下
        public static final String BAOXIAN = "BAOXIAN";           // 保险
        public static final String GONGYI = "GONGYI";             // 公益
        public static final String DC_SEPARATION = "DC_SEPARATION"; // 借贷分离
        public static final String DIGITAL = "DIGITAL";           // 数娱
        public static final String REGISTRATION = "REGISTRATION"; // 报名(需要先优惠费率报名成功，否则会阻断交易)
        public static final String PRIVATE_EDUCATION = "PRIVATE_EDUCATION"; // 民办教育
        public static final String DIRECT = "DIRECT";             // 直连

    }

    /**
     * 支付宝支付场景
     */
    public static class AlipayScene {
        public static final String OFFLINE = "OFFLINE";           // 线下
        public static final String LARGE = "LARGE";               // 特殊
        public static final String REGISTRATION = "REGISTRATION"; // 报名

    }

}
