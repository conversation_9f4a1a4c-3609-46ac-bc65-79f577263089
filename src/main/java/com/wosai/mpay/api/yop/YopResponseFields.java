package com.wosai.mpay.api.yop;

/**
 * Response field constants for Yeepay API
 */
public class YopResponseFields {
    // Common response fields
    public static final String RESULT = "result";
    public static final String CODE = "code";
    public static final String MESSAGE = "message";
    public static final String PARENT_MERCHANT_NO = "parentMerchantNo";
    public static final String MERCHANT_NO = "merchantNo";
    public static final String ORDER_ID = "orderId";
    public static final String UNIQUE_ORDER_NO = "uniqueOrderNo";
    public static final String STATUS = "status";
    public static final String CREATE_TIME = "createTime";
    public static final String FINISH_TIME = "finishTime";
    public static final String LAST_UPDATE_TIME = "lastUpdateTime";
    public static final String BANK_ORDER_ID = "bankOrderId";
    public static final String PAY_SUCCESS_TIME = "paySuccessTime";
    public static final String CHANNEL_TRX_ID = "channelTrxId";

    // Pay and Pre-pay response fields
    public static final String TOKEN = "token";
    public static final String TOKEN_EXPIRE_TIME = "tokenExpireTime";
    public static final String CASHIER_URL = "cashierUrl";
    public static final String QR_CODE = "qrCode";
    public static final String SHORT_URL = "shortUrl";
    public static final String PAYMENT_TOOL_TYPE = "paymentToolType";
    public static final String PAYMENT_TYPE = "paymentType";
    public static final String ORDER_AMOUNT = "orderAmount";
    public static final String REAL_AMOUNT = "realAmount";
    public static final String FUND_PROCESS_TYPE = "fundProcessType";
    public static final String RESIDUAL_AMOUNT = "residualAmount";
    public static final String BANK_CARD_TYPE = "bankCardType";
    public static final String BANK_ID = "bankId";
    public static final String BANK_NAME = "bankName";
    public static final String CARD_TYPE = "cardType";
    public static final String CARD_NO = "cardNo";
    public static final String CHANNEL_ORDER_ID = "channelOrderId";
    public static final String CHANNEL_PROMOTION_INFO = "channelPromotionInfo";
    public static final String PRE_PAY_TN = "prePayTn";
    public static final String PAY_BANK = "payBank";

    // Tutelage pre-pay response fields
    public static final String TUTELAGE_MERCHANT_NO = "tutelageMerchantNo";
    
    // Pay-link response fields
    public static final String PAY_LINK = "payLink";
    public static final String EXPIRE_TIME = "expireTime";
    
    // Get user ID response fields
    public static final String USER_ID = "userId";
    public static final String USER_TYPE = "userType";
    
    // Query order response fields
    public static final String MEMO = "memo";
    public static final String GOODS_NAME = "goodsName";
    public static final String FUND_AMOUNT = "fundAmount";
    public static final String DISCOUNT_AMOUNT = "discountAmount";
    public static final String REFUND_AMOUNT = "refundAmount";
    public static final String RESIDUAL_REFUND_AMOUNT = "residualRefundAmount";
    public static final String COUPON_AMOUNT = "couponAmount";
    public static final String DIVIDE_DETAIL = "divideDetail";
    public static final String DIVIDE_STATUS = "divideStatus";
    public static final String PAY_SUCCESS_DATE = "paySuccessDate";
    /**
     * 用户实际支付金额
     */
    public static final String REAL_PAY_AMOUNT = "realPayAmount";

    /**
     * 未拆分金额
     */
    public static final String UN_SPLIT_AMOUNT = "unSplitAmount";
    /**
     * 支付者信息
     */
    public static final String PAYER_INFO = "payerInfo";

    // Refund response fields
    public static final String REFUND_REQUEST_ID = "refundRequestId";
    public static final String REFUND_ORDER_ID = "refundOrderId";
    public static final String REFUND_UNIQUE_ORDER_NO = "refundUniqueOrderNo";
    public static final String REFUND_STATUS = "refundStatus";
    public static final String REFUND_DESCRIPTION = "refundDescription";
    
    // Query refund response fields
    public static final String REFUND_TIME = "refundTime";
    public static final String REFUND_ACCOUNT_TYPE = "refundAccountType";
    public static final String REFUND_BANK_FAIL_REASON = "refundBankFailReason";
    
    // Error response fields
    public static final String ERROR_CODE = "errorCode";
    public static final String ERROR_MESSAGE = "errorMessage";
    public static final String SUB_CODE = "subCode";
    public static final String SUB_MESSAGE = "subMessage";


    /**
     * 支付者信息常量定义类
     * 包含支付者信息相关的字段名称、描述和示例值
     */
    public class PayerInfoConstants {

        /**
         * 银行编号
         * 如：ABC、CBC等
         * 示例值：ABC
         */
        public static final String BANK_ID = "bankId";

        /**
         * 账户名称
         * 账户名称.网银B2B支付会返回付款企业账户名称
         * 示例值：易宝支付有限公司
         */
        public static final String ACCOUNT_NAME = "accountName";

        /**
         * 银行卡号
         * 银行卡号（前6后4）
         * 示例值：622848****0000
         */
        public static final String BANK_CARD_NO = "bankCardNo";

        /**
         * 手机号
         * 手机号（前3后4)
         * 示例值：181****5651
         */
        public static final String MOBILE_PHONE_NO = "mobilePhoneNo";

        /**
         * 卡类型
         * 示例值：CREDIT
         */
        public static final String CARD_TYPE = "cardType";

        /**
         * 借记卡
         */
        public static final String CARD_TYPE_DEBIT = "DEBIT";

        /**
         * 贷记卡
         */
        public static final String CARD_TYPE_CREDIT = "CREDIT";

        /**
         * 微信零钱
         */
        public static final String CARD_TYPE_CFT = "CFT";

        /**
         * 准贷记卡
         */
        public static final String CARD_TYPE_QUASI_CREDIT = "QUASI_CREDIT";

        /**
         * 对公账户（网银B2B支付返回）
         */
        public static final String CARD_TYPE_PUBLIC_ACCOUNT = "PUBLIC_ACCOUNT";

        /**
         * 用户ID
         * 微信支付返回openID
         * 支付宝支付返回userID
         * 银联支付返回userID
         * 账户支付返回付款方商编
         * 示例值：oUpF8uMuAJO_M2pxb1Q9zNjWeS6o
         */
        public static final String USER_ID = "userID";

        /**
         * 支付宝买家登录账号
         * 支付宝支付返回buyerLogonId
         */
        public static final String BUYER_LOGON_ID = "buyerLogonId";

        /**
         * 记帐簿编号
         */
        public static final String YP_ACCOUNT_BOOK_NO = "ypAccountBookNo";

        /**
         * 微信appid
         */
        public static final String APP_ID = "appID";

        /**
         * 微信/支付宝订单号
         * 该笔订单在微信、支付宝侧系统生成的单号
         */
        public static final String CHANNEL_TRX_ID = "channelTrxId";
    }

}
