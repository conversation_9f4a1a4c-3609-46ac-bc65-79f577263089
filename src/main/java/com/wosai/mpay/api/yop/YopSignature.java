package com.wosai.mpay.api.yop;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.wosai.mpay.util.StringUtils;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.Hex;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.net.URLEncoder;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;


/**
 * Signature utility for Yeepay API
 */
public class YopSignature {

    private static final Set<String> DEFAULT_HEADERS_TO_SIGN = Sets.newHashSet();
    private static final Joiner HEADER_JOINER = Joiner.on('\n');
    private static final Joiner SIGNED_HEADER_STRING_JOINER = Joiner.on(';');
    private static final Joiner QUERY_STRING_JOINER = Joiner.on('&');


    private static final BitSet URI_UNRESERVED_CHARACTERS = new BitSet();
    private static final String[] PERCENT_ENCODED_STRINGS = new String[256];

    static {
        DEFAULT_HEADERS_TO_SIGN.add(YopConstant.HEADER_YOP_REQUEST_ID);
        DEFAULT_HEADERS_TO_SIGN.add(YopConstant.HEADER_YOP_APPKEY);
        DEFAULT_HEADERS_TO_SIGN.add(YopConstant.HEADER_YOP_CONTENT_SHA256);
        DEFAULT_HEADERS_TO_SIGN.add(YopConstant.HEADER_YOP_ENCRYPT);

        for (int i = 'a'; i <= 'z'; i++) {
            URI_UNRESERVED_CHARACTERS.set(i);
        }
        for (int i = 'A'; i <= 'Z'; i++) {
            URI_UNRESERVED_CHARACTERS.set(i);
        }
        for (int i = '0'; i <= '9'; i++) {
            URI_UNRESERVED_CHARACTERS.set(i);
        }
        URI_UNRESERVED_CHARACTERS.set('-');
        URI_UNRESERVED_CHARACTERS.set('.');
        URI_UNRESERVED_CHARACTERS.set('_');
        URI_UNRESERVED_CHARACTERS.set('~');

        for (int i = 0; i < PERCENT_ENCODED_STRINGS.length; ++i) {
            PERCENT_ENCODED_STRINGS[i] = String.format("%%%02X", i);
        }
    }

    /**
     * Generate signature for Yeepay API request
     *
     * @param params Request parameters
     * @param appKey Application key (secret)
     * @param signType Signature algorithm type (SHA256, MD5)
     * @return Generated signature
     */
    public static String sign(Map<String, String> params, String appKey, String signType) {
        if (params == null) {
            return null;
        }

        // Remove existing signature if present
        params.remove(YopRequestFields.SIGN);
        
        // Create sorted parameter string
        String paramStr = createLinkString(params);
        
        // Append app key
        String stringToSign = paramStr + "&key=" + appKey;
        
        // Generate signature based on sign type
        if (YopConstant.ALGORITHM_SHA256.equals(signType)) {
            return sha256(stringToSign);
        } else {
            return md5(stringToSign);
        }
    }
    
    /**
     * Verify signature from Yeepay API response
     *
     * @param params Response parameters
     * @param sign Signature to verify
     * @param appKey Application key (secret)
     * @param signType Signature algorithm type (SHA256, MD5)
     * @return True if signature is valid, false otherwise
     */
    public static boolean verify(Map<String, String> params, String sign, String appKey, String signType) {
        if (params == null || StringUtils.isEmpty(sign)) {
            return false;
        }
        
        // Remove existing signature if present
        params.remove(YopRequestFields.SIGN);
        
        // Generate signature and compare
        String generatedSign = sign(params, appKey, signType);
        return sign.equals(generatedSign);
    }
    
    /**
     * Create a sorted parameter string from a map
     *
     * @param params Parameters map
     * @return Sorted parameter string
     */
    private static String createLinkString(Map<String, String> params) {
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        
        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            String value = params.get(key);
            if (StringUtils.isEmpty(value)) {
                continue; // Skip empty values
            }
            
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(key).append("=").append(value);
        }
        
        return sb.toString();
    }
    
    /**
     * Generate SHA-256 hash
     *
     * @param data Input data
     * @return SHA-256 hash
     */
    private static String sha256(String data) {
        try {
            MessageDigest digest = MessageDigest.getInstance(YopConstant.ALGORITHM_SHA256);
            byte[] hash = digest.digest(data.getBytes(YopConstant.DEFAULT_ENCODING));
            return bytesToHex(hash);
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            throw new RuntimeException("SHA-256 hashing failed", e);
        }
    }
    
    /**
     * Generate MD5 hash
     *
     * @param data Input data
     * @return MD5 hash
     */
    private static String md5(String data) {
        try {
            MessageDigest digest = MessageDigest.getInstance(YopConstant.ALGORITHM_MD5);
            byte[] hash = digest.digest(data.getBytes(YopConstant.DEFAULT_ENCODING));
            return bytesToHex(hash);
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            throw new RuntimeException("MD5 hashing failed", e);
        }
    }
    
    /**
     * Generate HMAC-SHA256 signature
     *
     * @param data Input data
     * @param key Secret key
     * @return HMAC-SHA256 signature
     */
    public static String hmacSha256(String data, String key) {
        try {
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(key.getBytes(YopConstant.DEFAULT_ENCODING), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            byte[] hash = sha256_HMAC.doFinal(data.getBytes(YopConstant.DEFAULT_ENCODING));
            return java.util.Base64.getEncoder().encodeToString(hash);
        } catch (NoSuchAlgorithmException | InvalidKeyException | UnsupportedEncodingException e) {
            throw new RuntimeException("HMAC-SHA256 signing failed", e);
        }
    }
    
    /**
     * Convert bytes to hexadecimal string
     *
     * @param bytes Byte array
     * @return Hexadecimal string
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                sb.append('0');
            }
            sb.append(hex);
        }
        return sb.toString();
    }

    /**
     * Sign request for Yeepay API
     *
     * @param requestMethod HTTP method
     * @param requestUri API URI
     * @param headers Request headers
     * @param params Request parameters
     * @param privateKey Private key for signing
     * @return Authorization header value
     * @throws Exception if signing fails
     */
    public static String signRequest(YopConstant.YopRequestMethod requestMethod, String requestUri,
                                     Map<String, String> headers, Map<String, String> params,
                                     String privateKey) throws Exception {
        // A.构造认证字符串
        String authString = buildAuthString(headers.get(YopConstant.HEADER_YOP_APPKEY));

        // B.获取规范请求串
        SortedMap<String, String> headersToSign = getHeadersToSign(headers, DEFAULT_HEADERS_TO_SIGN);
        String canonicalRequest = buildCanonicalRequest(requestMethod, requestUri, params, authString, headersToSign);

        // C.计算签名
        String signature = encodeUrlSafeBase64(sign(canonicalRequest.getBytes(YopConstant.DEFAULT_ENCODING), privateKey)) + "$" + "SHA256";

        // D.添加认证头
        return buildAuthHeader(authString, headersToSign, signature);
    }

    /**
     * Build authorization header
     *
     * @param authString Authentication string
     * @param headersToSign Headers to sign
     * @param signature Signature
     * @return Authorization header value
     */
    public static String buildAuthHeader(String authString,
                                          SortedMap<String, String> headersToSign,
                                          String signature) {
        String signedHeaders = SIGNED_HEADER_STRING_JOINER.join(headersToSign.keySet());
        signedHeaders = signedHeaders.trim().toLowerCase();
        return YopConstant.DEFAULT_AUTH_PREFIX_RSA2048 + " " + authString + "/" + signedHeaders + "/" + signature;
    }

    /**
     * Sign data with private key
     *
     * @param data Data to sign
     * @param privateKeyStr Private key as string
     * @return Signature bytes
     */
    public static byte[] sign(byte[] data, String privateKeyStr) {
        try {
            Signature signature = Signature.getInstance(YopConstant.DEFAULT_SIGN_ALG);
            signature.initSign(string2PrivateKey(privateKeyStr));
            signature.update(data);
            return signature.sign();
        } catch (Exception e) {
            throw new RuntimeException("SystemError, Sign Fail, key:" + privateKeyStr + ", ex:", e);
        }
    }

    /**
     * Convert string to private key
     *
     * @param priKey Private key as string
     * @return PrivateKey object
     */
    public static PrivateKey string2PrivateKey(String priKey) {
        try {
            return KeyFactory.getInstance(YopConstant.RSA).generatePrivate(
                    new PKCS8EncodedKeySpec(decodeBase64(priKey)));
        } catch (Exception e) {
            throw new RuntimeException("ConfigProblem, IsvPrivateKey ParseFail, value:" + priKey + ", ex:", e);
        }
    }

    /**
     * Convert string to public key
     *
     * @param pubKey Public key as string
     * @return PublicKey object
     */
    public static PublicKey string2PublicKey(String pubKey) {
        try {
            return KeyFactory.getInstance(YopConstant.RSA).generatePublic(
                    new X509EncodedKeySpec(decodeBase64(pubKey)));
        } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
            throw new RuntimeException("ConfigProblem, YopPublicKey ParseFail, value:" + pubKey + ", ex:", e);
        }
    }

    /**
     * Decode Base64 string
     *
     * @param input Base64 string
     * @return Decoded bytes
     */
    public static byte[] decodeBase64(String input) {
        return Base64.decodeBase64(input);
    }

    /**
     * Encode bytes to URL-safe Base64 string
     *
     * @param input Bytes to encode
     * @return URL-safe Base64 string
     */
    public static String encodeUrlSafeBase64(byte[] input) {
        return Base64.encodeBase64URLSafeString(input);
    }

    /**
     * Build canonical request for signing
     *
     * @param httpMethod HTTP method
     * @param apiUri API URI
     * @param params Request parameters
     * @param authString Authentication string
     * @param headersToSign Headers to sign
     * @return Canonical request string
     */
    private static String buildCanonicalRequest(YopConstant.YopRequestMethod httpMethod, String apiUri, Map<String, String> params,
                                                String authString,
                                                SortedMap<String, String> headersToSign) {
        String canonicalQueryString;
        if (YopConstant.YopRequestMethod.GET.equals(httpMethod) && null != params) {
            canonicalQueryString = getCanonicalQueryString(params);
        } else {
            canonicalQueryString = "";// post from 与json时，均为空，此处先简单处理
        }
        String canonicalHeaders = getCanonicalHeaders(headersToSign);

        String canonicalURI = getCanonicalURIPath(apiUri);
        return authString + "\n"
                + httpMethod.name() + "\n"
                + canonicalURI + "\n"
                + canonicalQueryString + "\n"
                + canonicalHeaders;
    }

    /**
     * Get canonical URI path
     *
     * @param path URI path
     * @return Canonical URI path
     */
    private static String getCanonicalURIPath(String path) {
        if (path == null) {
            return "/";
        } else if (path.startsWith("/")) {
            return normalizePath(path);
        } else {
            return "/" + normalizePath(path);
        }
    }

    /**
     * Normalize path
     *
     * @param path Path to normalize
     * @return Normalized path
     */
    private static String normalizePath(String path) {
        return normalize(path).replace("%2F", "/");
    }

    /**
     * Get headers to sign
     *
     * @param headers All headers
     * @param headersToSign Headers to sign
     * @return Sorted map of headers to sign
     */
    private static SortedMap<String, String> getHeadersToSign(Map<String, String> headers, Set<String> headersToSign) {
        SortedMap<String, String> ret = Maps.newTreeMap();
        if (headersToSign != null) {
            Set<String> tempSet = Sets.newHashSet();
            for (String header : headersToSign) {
                tempSet.add(header.trim().toLowerCase());
            }
            headersToSign = tempSet;
        }
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            String key = entry.getKey();
            if (entry.getValue() != null && !entry.getValue().isEmpty()) {
                if ((headersToSign != null && headersToSign.contains(key.toLowerCase())
                        && !YopConstant.AUTHORIZATION.equalsIgnoreCase(key))) {
                    ret.put(key, entry.getValue());
                }
            }
        }
        return ret;
    }

    /**
     * Get canonical headers
     *
     * @param headers Headers to include
     * @return Canonical headers string
     */
    private static String getCanonicalHeaders(SortedMap<String, String> headers) {
        if (headers.isEmpty()) {
            return "";
        }

        List<String> headerStrings = Lists.newArrayList();
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            String key = entry.getKey();
            if (key == null) {
                continue;
            }
            String value = entry.getValue();
            if (value == null) {
                value = "";
            }
            headerStrings.add(normalize(key.trim().toLowerCase()) + ':' + normalize(value.trim()));
        }
        Collections.sort(headerStrings);

        return HEADER_JOINER.join(headerStrings);
    }

    /**
     * Build authentication string
     *
     * @param appKey Application key
     * @return Authentication string
     */
    private static String buildAuthString(String appKey) {
        Date timestamp = new Date();
        return YopConstant.DEFAULT_YOP_PROTOCOL_VERSION + "/"
                + appKey + "/"
                + YopUtil.formatDateToISO8601(timestamp) + "/"
                + "1800";
    }

    /**
     * Calculate content hash
     *
     * @param jsonContent JSON content
     * @return Content hash
     * @throws Exception if hashing fails
     */
    public static String calculateContentHash(String jsonContent) throws Exception {
        InputStream contentStream = getContentStream(jsonContent);
        return Hex.encodeHexString((digest(contentStream)));
    }

    /**
     * Calculate content hash
     *
     * @param requestMethod HTTP method
     * @param requestContentType Content type
     * @param params Request parameters
     * @param content Request content
     * @return Content hash
     * @throws Exception if hashing fails
     */
    public static String calculateContentHash(YopConstant.YopRequestMethod requestMethod, YopConstant.YopRequestContentType requestContentType,
                                              Map<String, String> params, String content) throws Exception {
        String digestSource;

        if (requestMethod.equals(YopConstant.YopRequestMethod.GET)) {
            digestSource = "";
        } else if (requestMethod.equals(YopConstant.YopRequestMethod.POST)
                && requestContentType.equals(YopConstant.YopRequestContentType.JSON)) {
            digestSource = content;
        } else {
            digestSource = getCanonicalQueryString(params);
        }
        InputStream contentStream = getContentStream(digestSource);
        return Hex.encodeHexString((digest(contentStream)));
    }

    /**
     * Get content stream from parameters
     *
     * @param params Parameters
     * @return Content stream
     * @throws Exception if conversion fails
     */
    private static ByteArrayInputStream getContentStream(Map<String, String> params) throws Exception {
        return getContentStream(getCanonicalQueryString(params));
    }

    /**
     * Get content stream from string
     *
     * @param paramStr Parameter string
     * @return Content stream
     * @throws Exception if conversion fails
     */
    private static ByteArrayInputStream getContentStream(String paramStr) throws Exception {
        byte[] bytes;
        if (StringUtils.isEmpty(paramStr)) {
            bytes = new byte[0];
        } else {
            bytes = paramStr.getBytes(YopConstant.DEFAULT_ENCODING);
        }
        return new ByteArrayInputStream(bytes);
    }

    /**
     * Get canonical query string
     *
     * @param params Parameters
     * @return Canonical query string
     */
    public static String getCanonicalQueryString(Map<String, String> params) {
        if (null == params || params.isEmpty()) {
            return "";
        }
        // 参数编码&排序
        return sortAndEncodeParams(params);
    }

    /**
     * Normalize string for signing
     *
     * @param value String to normalize
     * @return Normalized string
     */
    public static String normalize(String value) {
        try {
            StringBuilder builder = new StringBuilder();
            for (byte b : value.getBytes(YopConstant.DEFAULT_ENCODING)) {
                if (URI_UNRESERVED_CHARACTERS.get(b & 0xFF)) {
                    builder.append((char) b);
                } else {
                    builder.append(PERCENT_ENCODED_STRINGS[b & 0xFF]);
                }
            }
            return builder.toString();
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 参数编码&排序
     */
    private static String sortAndEncodeParams(Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }

        // 1. 参数排序（按键名ASCII升序）
        List<String> paramNames = new ArrayList<>(params.keySet());
        Collections.sort(paramNames);

        // 2. 构建规范查询字符串
        List<String> queryParts = new ArrayList<>();
        for (String name : paramNames) {
            String value = params.get(name).toString();
            // URL编码参数名和值
            String encodedName = urlEncodeForSign(name);
            String encodedValue = urlEncodeForSign(value);
            queryParts.add(encodedName + "=" + encodedValue);
        }

        // 3. 用&连接所有参数对
        return String.join("&", queryParts);
    }

    /**
     * URL编码(签名版)
     */
    private static String urlEncodeForSign(String value) {
        try {
            return URLEncoder.encode(value, "UTF-8")
                    .replace("+", "%20")
                    .replace("*", "%2A")
                    .replace("%7E", "~");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("URL编码失败", e);
        }
    }



    /**
     * Digest input stream
     *
     * @param input Input stream
     * @return Digest bytes
     */
    public static byte[] digest(InputStream input) {
        try {
            MessageDigest md = MessageDigest.getInstance(YopConstant.DEFAULT_DIGEST_ALG);
            DigestInputStream digestInputStream = new DigestInputStream(input, md);
            byte[] buffer = new byte[1024];
            while (digestInputStream.read(buffer) > -1) {
            }
            return digestInputStream.getMessageDigest().digest();
        } catch (Exception e) {
            throw new RuntimeException("SystemError, Digest Fail, alg:" + YopConstant.DEFAULT_DIGEST_ALG + ", ex:", e);
        }
    }

    /**
     * Build encrypt header
     *
     * @param encryptHeaders Headers to encrypt
     * @param encryptParams Parameters to encrypt
     * @param aesKey AES key
     * @param yopPublicKey YOP public key
     * @return Encrypt header value
     * @throws Exception if encryption fails
     */
    public static String buildEncryptHeader(Set<String> encryptHeaders, Set<String> encryptParams, String aesKey, String yopPublicKey) throws Exception {
        String aesAlgWithUnderscores = YopConstant.AES_ENCRYPT_ALG.replace("/", "_");
        return "yop-encrypt-v1" + YopConstant.SLASH +
                YopConstant.EMPTY + YopConstant.SLASH + //rsa 为空
                aesAlgWithUnderscores + YopConstant.SLASH +
                encodeUrlSafeBase64(encryptKey(decodeBase64(aesKey), string2PublicKey(yopPublicKey))) + YopConstant.SLASH +
                YopConstant.EMPTY + YopConstant.SLASH +
                YopConstant.STREAM + YopConstant.SLASH +
                String.join(YopConstant.SEMICOLON, encryptHeaders) + YopConstant.SLASH +
                encodeUrlSafeBase64(String.join(YopConstant.SEMICOLON, encryptParams).getBytes(YopConstant.DEFAULT_ENCODING));
    }

    /**
     * Encrypt key
     *
     * @param data Data to encrypt
     * @param key Key for encryption
     * @return Encrypted bytes
     */
    private static byte[] encryptKey(byte[] data, Key key) {
        Cipher cipher;
        try {
            cipher = Cipher.getInstance(YopConstant.RSA_ENCRYPT_ALG);
            cipher.init(Cipher.ENCRYPT_MODE, key);
            return cipher.doFinal(data);
        } catch (Exception e) {
            throw new RuntimeException("SystemError, Encrypt Fail, key:" + key + "ex:", e);
        }
    }
}
