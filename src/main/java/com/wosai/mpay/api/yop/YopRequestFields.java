package com.wosai.mpay.api.yop;

/**
 * Request field constants for Yeepay API
 */
public class YopRequestFields {
    // Common fields
    public static final String MERCHANT_NO = "merchantNo";
    public static final String PARENT_MERCHANT_NO = "parentMerchantNo";
    public static final String REQUEST_ID = "requestId";
    public static final String ORDER_ID = "orderId";
    public static final String ORDER_AMOUNT = "orderAmount";
    public static final String NOTIFY_URL = "notifyUrl";
    public static final String REDIRECT_URL = "redirectUrl";
    public static final String MEMO = "memo";
    public static final String GOODS_NAME = "goodsName";
    public static final String EXPIRE_TIME = "expireTime";
    public static final String TIMESTAMP = "timestamp";
    public static final String SIGN = "sign";
    public static final String APP_ID = "appId";
    
    // Pay and Pre-pay fields
    public static final String FUND_PROCESS_TYPE = "fundProcessType";
    public static final String PAYMENT_TOOL_TYPE = "paymentToolType";
    public static final String PAYMENT_TOOL = "paymentTool";
    public static final String PAYMENT_EXT = "paymentExt";
    public static final String TERMINAL_ID = "terminalId";
    public static final String TERMINAL_TYPE = "terminalType";
    public static final String TERMINAL_INFO = "terminalInfo";
    public static final String OPERATOR_ID = "operatorId";
    public static final String CHANNEL_PROMOTION_INFO = "channelPromotionInfo";
    public static final String IDENTITY_ID = "identityId";
    public static final String IDENTITY_TYPE = "identityType";
    public static final String USER_ID = "userId";
    public static final String USER_IP = "userIp";
    public static final String LIMIT_CREDIT_CARD = "limitCreditCard";
    public static final String LIMIT_CARD_TYPE = "limitCardType";
    public static final String TOKEN = "token";
    public static final String PAY_WAY = "payWay";
    public static final String SCENE = "scene";
    public static final String CHANNEL = "channel";

    // Tutelage pre-pay fields
    public static final String TUTELAGE_MERCHANT_NO = "tutelageMerchantNo";
    public static final String TUTELAGE_MERCHANT_NAME = "tutelageMerchantName";
    public static final String TUTELAGE_INDUSTRY_CODE = "tutelageIndustryCode";
    public static final String TUTELAGE_INDUSTRY_NAME = "tutelageIndustryName";
    public static final String TUTELAGE_MERCHANT_CITY_CODE = "tutelageMerchantCityCode";
    public static final String TUTELAGE_MERCHANT_PROVINCE_CODE = "tutelageMerchantProvinceCode";
    
    // Pay-link fields
    public static final String SCENE_TYPE = "sceneType";
    public static final String PRODUCT_CATALOG = "productCatalog";
    public static final String PRODUCT_NAME = "productName";
    public static final String PRODUCT_DESC = "productDesc";
    public static final String PRODUCT_URL = "productUrl";
    
    // Close order fields
    public static final String UNIQUE_ORDER_NO = "uniqueOrderNo";
    public static final String DESCRIPTION = "description";
    
    // Get user ID fields
    public static final String AUTH_CODE = "authCode";
    
    // Query order fields
    public static final String ORDER_DATE = "orderDate";
    
    // Refund fields
    public static final String REFUND_REQUEST_ID = "refundRequestId";
    public static final String REFUND_AMOUNT = "refundAmount";
    public static final String REFUND_ACCOUNT_TYPE = "refundAccountType";
    public static final String REFUND_DESCRIPTION = "refundDescription";
    
    // Query refund fields
    public static final String REFUND_ORDER_ID = "refundOrderId";

    // get-user-id
    public static final String USER_AUTH_CODE = "userAuthCode";

    // 公众号配置接口

    /**
     * Payment authorization directory list
     * 支付授权目录列表
     * Example value: ["http://www.yeepay.com/","http://www.yeepay.com/"]
     */
    public static final String TRADE_AUTH_DIR_LIST = "tradeAuthDirList";

    /**
     * Payment appId directory list
     * 支付appId目录列表
     * JSON string format:
     * - appId: Payment appId (required)
     * - appSecret: Payment appSecret (required for WeChat public accounts and user scanning,
     *   not required for WeChat mini programs or merchants using unified aggregated orders with WeChat public account payment)
     * - appIdType: AppId type (required) (OFFICIAL_ACCOUNT: public account; MINI_PROGRAM: mini program)
     *
     * Example value: [{"appId":"appId","appSecret":"appSecret","appIdType":"OFFICIAL_ACCOUNT"},
     *                {"appId":"appId","appSecret":"appSecret","appIdType":"MINI_PROGRAM"}]
     */
    public static final String APP_ID_LIST = "appIdList";

    /**
     * Reported merchant number
     * 报备商户号
     * If specified, the configuration will only apply to this reported merchant number.
     * Need to ensure this reported merchant number belongs to this merchant.
     */
    public static final String REPORT_MERCHANT_NO = "reportMerchantNo";

    /**
     * Constants for fields in AppIdInfo JSON objects within APP_ID_LIST
     */
    public static class AppId {
        /**
         * Payment appId (required)
         * 支付appId 必填
         */
        public static final String APP_ID = "appId";

        /**
         * Payment appSecret
         * 支付appSecret
         * Not required for WeChat mini programs.
         * Required for WeChat public accounts and WeChat user scanning.
         * (Not required for merchants only using aggregated unified orders with WeChat public account payment)
         */
        public static final String APP_SECRET = "appSecret";

        /**
         * AppId type (required)
         * appId类型 必填
         * Values: OFFICIAL_ACCOUNT (public account), MINI_PROGRAM (mini program)
         */
        public static final String APP_ID_TYPE = "appIdType";
    }

    /**
     * App ID type constants
     */
    public static class AppIdType {
        /**
         * WeChat Official Account
         * 微信公众号
         */
        public static final String OFFICIAL_ACCOUNT = "OFFICIAL_ACCOUNT";

        /**
         * WeChat Mini Program
         * 微信小程序
         */
        public static final String MINI_PROGRAM = "MINI_PROGRAM";
    }

}
