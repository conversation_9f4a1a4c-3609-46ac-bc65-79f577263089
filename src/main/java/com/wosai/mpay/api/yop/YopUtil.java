package com.wosai.mpay.api.yop;


import com.wosai.mpay.util.SafeSimpleDateFormat;

import java.util.Date;

/**
 * Utility functions for Yeepay API
 */
public class YopUtil {

    private static final SafeSimpleDateFormat ISO8601_DATE_FORMAT_PATTERN = new SafeSimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
    private static final SafeSimpleDateFormat DATE_FORMAT_PATTERN = new SafeSimpleDateFormat("yyyy-MM-dd HH:mm:ss");



    /**
     * Format date to string in ISO8601 standard format
     *
     * @param date Date to format
     * @return Formatted date string
     */
    public static String formatDateToISO8601(Date date) {
        if (date == null) {
            return null;
        }
        return ISO8601_DATE_FORMAT_PATTERN.format(date);
    }

    public static String formatDate(long timestamp) {
        return DATE_FORMAT_PATTERN.format(new Date(timestamp));
    }


}
