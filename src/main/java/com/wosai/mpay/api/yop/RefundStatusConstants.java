package com.wosai.mpay.api.yop;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/3/30.
 */
public class RefundStatusConstants {
    /**
     * 退款处理中
     */
    public static final String PROCESSING = "PROCESSING";

    /**
     * 退款成功
     */
    public static final String SUCCESS = "SUCCESS";

    /**
     * 退款失败
     */
    public static final String FAILED = "FAILED";

    /**
     * 退款关闭，商户通知易宝结束该笔退款后返回该状态
     */
    public static final String CANCEL = "CANCEL";

    /**
     * 退款中断，如需继续退款，请调用上送卡信息退款进行打款退款；如想结束退款，请调用结束退款来关闭退款订单
     */
    public static final String SUSPEND = "SUSPEND";


    public static boolean isValidStatus(String status) {
        return PROCESSING.equals(status) || SUCCESS.equals(status) || FAILED.equals(status) || CANCEL.equals(status) || SUSPEND.equals(status);
    }

    public static boolean isRefundCompleted(String status) {
        return SUCCESS.equals(status) || FAILED.equals(status) || CANCEL.equals(status);
    }

    public static boolean isRefundSuccessful(String status) {
        return SUCCESS.equals(status);
    }

    public static boolean isRefundInProgress(String status) {
        return PROCESSING.equals(status);
    }

}
