package com.wosai.mpay.api.yop;

import java.util.Arrays;
import java.util.List;

/**
 * Constants for Yeepay API integration
 */
public class YopConstant {
    // Common Constants
    public static final String SLASH = "/";
    public static final String SEMICOLON = ";";
    public static final String EMPTY = "";
    public static final String STREAM = "stream";
    public static final String DEFAULT_ENCODING = "UTF-8";
    // Character Encoding
    public static final String CHARSET_UTF8 = "UTF-8";
    
    // Encryption Constants
    public static final String AES_ENCRYPT_ALG = "AES/ECB/PKCS5Padding";
    public static final String RSA_ENCRYPT_ALG = "RSA/ECB/PKCS1Padding";
    public static final String RSA = "RSA";
    
    // Authentication Constants
    public static final String DEFAULT_YOP_PROTOCOL_VERSION = "yop-auth-v3";
    public static final String DEFAULT_AUTH_PREFIX_RSA2048 = "YOP-RSA2048-SHA256";
    public static final String DEFAULT_DIGEST_ALG = "SHA-256";
    public static final String DEFAULT_SIGN_ALG = "SHA256withRSA";
    
    // Header Constants
    public static final String HEADER_YOP_SDK_VERSION = "x-yop-sdk-version";
    public static final String HEADER_YOP_SDK_LANGS = "x-yop-sdk-langs";
    public static final String HEADER_YOP_REQUEST_ID = "x-yop-request-id";
    public static final String HEADER_YOP_APPKEY = "x-yop-appkey";
    public static final String HEADER_YOP_CONTENT_SHA256 = "x-yop-content-sha256";
    public static final String HEADER_YOP_ENCRYPT = "x-yop-encrypt";
    public static final String HEADER_AUTHORIZATION = "Authorization";
    public static final String HEADER_CONTENT_DISPOSITION = "Content-Disposition";
    public static final String HEADER_CONTENT_TYPE = "Content-Type";


    // HTTP Headers
    public static final String AUTHORIZATION = "Authorization";
    public static final String CONTENT_TYPE = "Content-Type";

    // Content Types
    public static final String YOP_HTTP_CONTENT_TYPE_JSON = "application/json";
    public static final String YOP_HTTP_CONTENT_TYPE_FORM = "application/x-www-form-urlencoded";
    public static final String YOP_HTTP_CONTENT_TYPE_MULTIPART_FORM = "multipart/form-data";
    public static final String YOP_HTTP_CONTENT_TYPE_STREAM = "application/octet-stream";
    public static final String YOP_HTTP_CONTENT_TYPE_TEXT = "text/plain;charset=UTF-8";

    // Response Codes
    public static final String CODE_SUCCESS = "00000";
    public static final String CODE_OPR_SUCCESS = "OPR00000";

    // Signature Constants
    public static final String ALGORITHM_SHA256 = "SHA-256";
    public static final String ALGORITHM_MD5 = "MD5";


    // CODE SUCCESS LIST
    public static final List<String> CODE_SUCCESS_LIST = Arrays.asList(CODE_SUCCESS, CODE_OPR_SUCCESS);


    // 渠道类型
    public static final String CHANNEL_WECHAT = "WECHAT";     // 微信
    public static final String CHANNEL_ALIPAY = "ALIPAY";     // 支付宝
    public static final String CHANNEL_UNIONPAY = "UNIONPAY"; // 银联
    public static final String CHANNEL_DCEP = "DCEP";         // 数字人民币


    public enum YopRequestMethod {
        GET,
        POST
    }

    public enum YopRequestType {
        WEB,
        FILE_DOWNLOAD,
        MULTI_FILE_UPLOAD,
    }

    public enum YopRequestContentType {
        FORM_URL_ENCODE(YOP_HTTP_CONTENT_TYPE_FORM),
        MULTIPART_FORM(YOP_HTTP_CONTENT_TYPE_MULTIPART_FORM),
        JSON(YOP_HTTP_CONTENT_TYPE_JSON),
        OCTET_STREAM(YOP_HTTP_CONTENT_TYPE_STREAM),
        TEXT_PLAIN(YOP_HTTP_CONTENT_TYPE_TEXT);

        private String value;

        YopRequestContentType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}
