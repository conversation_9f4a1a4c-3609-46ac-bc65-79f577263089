package com.wosai.mpay.api.yop;

import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static com.wosai.mpay.util.HttpClientUtils.BODY_RESULT_FIELD;

/**
 * 易宝支付
 */
public class YopClient {
    public static final Logger logger = LoggerFactory.getLogger(YopClient.class);


    private int connectTimeout = 1000;
    private int readTimeout = 15000;

    public YopClient() {

    }

    public YopClient(int readTimeout, int connectTimeout) {
        this.readTimeout = readTimeout;
        this.connectTimeout = connectTimeout;
    }


    public Map<String, Object> call(YopConstant.YopRequestMethod method, YopConstant.YopRequestContentType contentType, String gateway, Map<String, String> request, String appKey, String privateKey, String yopPublicKey) throws Exception {
        // 请求头
        String content;
        if(contentType == YopConstant.YopRequestContentType.FORM_URL_ENCODE){
            content = buildQuery(request, YopConstant.CHARSET_UTF8);
        }else if (contentType == YopConstant.YopRequestContentType.JSON){
            content = JsonUtil.objectToJsonString(request);
        }else {
            throw new MpayException("不支持的content-type类型");
        }
        Map<String, String> headers = buildHeaders(method, contentType, gateway, request,
                content, false, null, null, null,
                yopPublicKey, appKey, privateKey
        );
        logger.info("request: {}, {}", content, headers);
        Map<String, Object> response;
        if(method == YopConstant.YopRequestMethod.POST){
            response = HttpClientUtils.doCommonMethod(YopClient.class.getName(), null, null, gateway, request, contentType.getValue(), content, headers, YopConstant.CHARSET_UTF8, this.connectTimeout, this.readTimeout, "post");
        }else if(method == YopConstant.YopRequestMethod.GET){
            response = HttpClientUtils.doCommonMethod(YopClient.class.getName(), null, null, gateway, request, contentType.getValue(), content, headers, YopConstant.CHARSET_UTF8, this.connectTimeout, this.readTimeout, "get");
        }else {
            throw new MpayException("不支持的请求方法");
        }
        logger.info("response: {}", response);
        // 解析响应
        Map<String, Object> result = JsonUtil.jsonStringToObject(MapUtil.getString(response, BODY_RESULT_FIELD), Map.class);
        // 注意, 如果没有错误的话，响应结果中包含result, 则将其展开
        Map subResult = MapUtils.getMap(result, YopResponseFields.RESULT);
        if (subResult != null){
            result.putAll(subResult);
            result.remove(YopResponseFields.RESULT);
        }
        return result;
    }


    private static Map<String, String> buildHeaders(YopConstant.YopRequestMethod httpMethod, YopConstant.YopRequestContentType contentType,  String gatewayUrl,
                                                    Map<String, String> params, String content,
                                                    boolean encrypt, String encryptKey, Set<String> encryptHeaders, Set<String> encryptParams, String yopPublicKey,
                                                    String appKey, String privateKey) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put(YopConstant.HEADER_YOP_SDK_LANGS, "java");
        headers.put(YopConstant.HEADER_YOP_SDK_VERSION, "4.3.0");
        headers.put(YopConstant.HEADER_YOP_APPKEY, appKey);
        headers.put(YopConstant.HEADER_YOP_REQUEST_ID, UUID.randomUUID().toString());

        if (encrypt) {
            // 加密头：请求参数不加密的情况, 也需设置加密头，用于响应结果加密
            headers.put(YopConstant.HEADER_YOP_ENCRYPT, YopSignature.buildEncryptHeader(encryptHeaders, encryptParams, encryptKey, yopPublicKey));
        }

        // 摘要头
        headers.put(YopConstant.HEADER_YOP_CONTENT_SHA256, YopSignature.calculateContentHash(httpMethod, contentType, params, content));

        // 签名头
        headers.put(YopConstant.HEADER_AUTHORIZATION, YopSignature.signRequest(httpMethod, getRequestUri(gatewayUrl), headers, params, privateKey));
        return headers;
    }

    public static String getRequestUri(String gatewayUrl) throws MalformedURLException {
        // 注意 https://openapi.yeepay.com/yop-center/rest/v1.0/trade/order/query， requestUri 为 /rest/v1.0/trade/order/query
        String path = new URL(gatewayUrl).getPath();
        return path.substring(path.indexOf("/", 1));
    }

    public static String buildQuery(Map<String, String> params, String charset) throws IOException {
        if (params == null || params.isEmpty()) {
            return null;
        }

        StringBuilder query = new StringBuilder();
        Set<Map.Entry<String, String>> entries = params.entrySet();
        boolean hasParam = false;

        for (Map.Entry<String, String> entry : entries) {
            String name = entry.getKey();
            String value = entry.getValue();
            // 忽略参数名或参数值为空的参数
            if (StringUtils.areNotEmpty(name, value)) {
                if (hasParam) {
                    query.append("&");
                } else {
                    hasParam = true;
                }
                // 两次encode
                value = URLEncoder.encode(value, charset);
                value = URLEncoder.encode(value, charset);
                query.append(name).append("=").append(value);
            }
        }

        return query.toString();
    }



    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }



}
