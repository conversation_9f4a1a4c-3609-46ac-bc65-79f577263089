package com.wosai.mpay.api.yop;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/3/30.
 */
public class PaymentStatusConstants {

    /**
     * 处理中（内部系统处理超时会出现）
     */
    public static final String PROCESSING = "PROCESSING";

    /**
     * 等待用户输入密码
     */
    public static final String WAITPAY = "WAITPAY";

    /**
     * 支付成功
     */
    public static final String SUCCESS = "SUCCESS";

    /**
     * 支付失败
     */
    public static final String FAIL = "FAIL";

    /**
     *  订单关闭
     */
    public static final String CLOSE = "CLOSE";


    /**
     * 判断支付是否已完成（成功或失败）
     * @param status 支付状态
     * @return 如果支付已完成则返回true，否则返回false
     */
    public static boolean isPaymentCompleted(String status) {
        return SUCCESS.equals(status) || FAIL.equals(status) || CLOSE.equals(status);
    }

    /**
     * 判断支付是否成功
     * @param status 支付状态
     * @return 如果支付成功则返回true，否则返回false
     */
    public static boolean isPaymentSuccessful(String status) {
        return SUCCESS.equals(status);
    }

    /**
     * 判断支付是否处于进行中状态
     * @param status 支付状态
     * @return 如果支付正在进行中则返回true，否则返回false
     */
    public static boolean isPaymentInProgress(String status) {
        return PROCESSING.equals(status) || WAITPAY.equals(status);
    }

}
