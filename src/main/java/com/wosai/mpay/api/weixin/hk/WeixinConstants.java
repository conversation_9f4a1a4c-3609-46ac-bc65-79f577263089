package com.wosai.mpay.api.weixin.hk;

import java.util.Arrays;
import java.util.List;

/**
 * Created by jianfree on 12/11/15.
 */
public class WeixinConstants {
    /** 微信签名字段 **/
    public static final String APP_KEY = "app_key";



    /** UTF-8字符集 **/
    public static final String CHARSET_UTF8     = "UTF-8";


    public static String  DATE_TIME_FORMAT = "yyyyMMddHHmmss";

    /** sign type **/
    public static String SIGN_TYPE_MD5 = "MD5";

    /** FEE_TYPE 货币类型**/
    public static final String FEE_TYPE_CNY = "CNY"; //人民币

    /** 交易类型 **/
    public  static final String TRADE_TYPE_JSAPI = "JSAPI";//公众号支付
    public  static final String TRADE_TYPE_NATIVE = "NATIVE";//原生扫码支付
    public  static final String TRADE_TYPE_APP = "APP";//app支付
    public  static final String TRADE_TYPE_MICROPAY = "MICROPAY";//刷卡支付


    /** 交易状态 **/
    public static final String TRADE_STATE_SUCCESS = "SUCCESS";//支付成功
    public static final String TRADE_STATE_REFUND = "REFUND";//转入退款
    public static final String TRADE_STATE_NOTPAY = "NOTPAY";//未支付
    public static final String TRADE_STATE_CLOSED = "CLOSED";//已关闭
    public static final String TRADE_STATE_REVOKED = "REVOKED";//已撤销（刷卡支付）
    public static final String TRADE_STATE_USERPAYING = "USERPAYING";//用户支付中
    public static final String TRADE_STATE_PAYERROR = "PAYERROR";//支付失败


    /** 通信结果返回 **/
    public static final String RETURN_CODE_SUCCESS = "SUCCESS";
    public static final String RETURN_CODE_FAIL = "FAIL";

    /** 业务结果返回**/
    public static final String RESULT_CODE_SUCCESS = "SUCCESS";
    public static final String RESULT_CODE_FAIL = "FAIL";


    public static final String ERR_CODE_ORDER_IN_PROD = "0001";							// 订单支付中
    public static final String ERR_CODE_NOT_ENOUGH = "0011"; 							// 余额不足
    public static final String ERR_CODE_NOT_LIMIT_ENOUGH = "0012"; 						// 余额限额不足
    public static final String ERR_CODE_POINT_CONTROL_REJECTION = "0013";				// 分控拒绝
    public static final String ERR_CODE_BANK_REJECTION = "0014";						// 银行拒绝
    public static final String ERR_CODE_AUTH_CODE_EXPIRE = "0015"; 						// 二维码已过期，请用户在微信上刷新后再试
    public static final String ERR_CODE_UNKNOWN_ORDER_STATUS = "0100"; 					// 订单状态未知
    public static final String ERR_CODE_QUERY_ORDER_NOT_EXISTS = "0101"; 				// 查无此订单
    public static final String ERR_CODE_CANCEL_ORDER_NOT_EXISTS = "0201"; 				// 查无此订单
    public static final String ERR_CODE_ERROR_CERTIFICATE = "0250"; 					// 商户证书错误
    public static final String ERR_CODE_REFUND_FEE_FAIL = "0300"; 						// 于原订单金额不符
    public static final String ERR_CODE_REFUND_FEE_OVERSTEP = "0301"; 					// 超出可退总金额
    public static final String ERR_CODE_REFUND_ERROR_ORDERNO= "0302"; 					// 订单号不匹配
    public static final String ERR_CODE_REFUND_ERROR_CERTIFICATE = "0350"; 				// 商户证书错误
    public static final String ERR_CODE_QUERY_ERROR_ORDERNO = "0400"; 					// 订单号不匹配
    public static final String ERR_CODE_REFUND_QUERY_ORDER_NOT_EXISTS_1 = "0401"; 		// 查无此订单
    public static final String ERR_CODE_REFUND_QUERY_ORDER_NOT_EXISTS_2 = "0402"; 		// 查无此订单
    public static final String ERR_CODE_SIGN_ERROR = "9998"; 							// 签名错误
    public static final String ERR_CODE_SYSTEM_ERROR = "9999"; 							// 接口返回错误
    
    //接口返回错误列表 刷卡支付 支付结果失败
    public static final List<String> MICRO_PAY_RESULT_ERROR_CODE_FAIL_LIST = Arrays.asList(
    		ERR_CODE_NOT_ENOUGH, ERR_CODE_NOT_LIMIT_ENOUGH, ERR_CODE_POINT_CONTROL_REJECTION, ERR_CODE_BANK_REJECTION,
    		ERR_CODE_AUTH_CODE_EXPIRE
    );


    //接口返回错误列表，刷卡支付 支付结果未知
    public static final List<String> MICRO_PAY_RESULT_ERROR_CODE_UNKONW_LIST = Arrays.asList(
    		ERR_CODE_SYSTEM_ERROR
    );

    //接口返回错误列表  协议错误
    public static final List<String> RESULT_ERROR_CODE_PROTOCAL_FAIL_LIST = Arrays.asList(
    		ERR_CODE_SIGN_ERROR
    );

}
