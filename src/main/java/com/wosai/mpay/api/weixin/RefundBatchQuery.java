package com.wosai.mpay.api.weixin;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.util.Hex;
import com.wosai.mpay.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by jianfree on 6/7/16.
 */
public class RefundBatchQuery {
    public static  Logger logger = LoggerFactory.getLogger(RefundBatchQuery.class);
    public static  ObjectMapper objectMapper = new ObjectMapper();
    public static void main(String[] args) throws Exception {
        WeixinClient client = new WeixinClient();
        String file = "/Users/<USER>/Documents/wosai-work/refund.csv";
        Scanner scanner = new Scanner(new File(file));
        final List<Map> orders = new ArrayList<Map>();
        SimpleDateFormat smf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        while(scanner.hasNextLine()){
            String line = scanner.nextLine();
            if(StringUtils.isEmpty(line)){
                continue;
            }
            String arr [] = line.split(",");
            for (int i = 0; i < arr.length; i++) {
                arr[i] = arr[i].replaceAll("\"", "");
            }

            Map map = new ConcurrentHashMap();
            map.put("sn", arr[0]);
            map.put("ctime", arr[1]);
            map.put("ctimeString", smf.format(new Date(Long.parseLong(arr[1]))));
            map.put("effective_amount", Long.parseLong(arr[2])/100.0);
            map.put("original_amount",Long.parseLong(arr[3])/100.0);
            map.put("configSnapshot", arr[4]);
            orders.add(map);
        }
        orders.sort(new Comparator<Map>() {
            @Override
            public int compare(Map o1, Map o2) {
                Long o1Ctime = Long.parseLong(o1.get("ctime").toString());
                Long o2Ctime = Long.parseLong(o2.get("ctime").toString());
                if(o1Ctime > o2Ctime){
                    return 1;
                }else if(o1Ctime == o2Ctime){
                    return 0;
                }else{
                    return -1;
                }
            }
        });

        List<Map<String,Object>> weixinOrders = new ArrayList<Map<String, Object>>();

        for(Map order: orders){
            String configSnapshot = order.get("configSnapshot").toString();
            String orderSn =  order.get("sn").toString();
            RequestBuilder requestBuilder = getDefaultBuilder(orderSn, configSnapshot);
            if(requestBuilder != null){
                weixinOrders.add(order);
            }else{
                logger.debug("orderSn : {} is not weixin liquidation ", orderSn );
            }


        }

        for(Map order: weixinOrders){
            String configSnapshot = order.get("configSnapshot").toString();
            String orderSn =  order.get("sn").toString();
            RequestBuilder requestBuilder = getDefaultBuilder(orderSn, configSnapshot);
            if(requestBuilder != null){
                requestBuilder.set(BusinessFields.OUT_TRADE_NO, orderSn);

                Map<String,Object> result = client.call(WeixinConfig.REFUND_QUERY, WeixinConfig.SQB_APP_SECRET, null, requestBuilder.build());
                String status = getRefundStatsu(result);
                logger.debug("orderSn : {},  refundQuery result: {}", orderSn, objectMapper.writeValueAsString(result));
                boolean flag = false;
                if(status.equals(WeixinConstants.TRADE_STATE_SUCCESS)){
                    flag = true;
                }
                order.put("status", status);
                order.put("flag", flag);
                order.put("result", result);
                order.put("subMchId", requestBuilder.build().get("sub_mch_id"));
                order.remove("configSnapshot");

            }


        }

        PrintWriter out = new PrintWriter(new File("batch.out"));
        out.write(objectMapper.writeValueAsString(weixinOrders));
        out.append("\n");
        out.flush();
        out.close();
         out = new PrintWriter(new File("batch1.csv"));
        for(Map order: weixinOrders){
            out.append(String.format("%s\t%s\t%s\t%s\t%s\t%s\t%s\n", order.get("sn"), order.get("ctimeString"),
                    order.get("original_amount" ), order.get("status"), order.get("subMchId") ,order.get("flag"), order.get("result")));
        }


        out.flush();
    }


    public static String getRefundStatsu(Map<String,Object> result){
        String status = "";
        for(String key: result.keySet()){
            if(key.startsWith("refund_status")){
                 status =  (String) result.get(key) + " " + status;
            }
        }
        return status;
    }

    public static RequestBuilder getDefaultBuilder(String orderSn, String configSnapshot) throws IOException {
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
        builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
        builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
        builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
        String hexString = configSnapshot.substring(2);
        Map<String, Object> config = objectMapper.readValue(Hex.decode(hexString.toLowerCase()), Map.class);
//        System.out.println(config);
        if(config == null){
            return null;
        }

        for(String key: config.keySet()){
            Object value = config.get(key);
            if(value instanceof  Map){
                if (((Map) value).keySet().contains("active") && (Boolean)((Map) value).get("active") ==true){

                    if((Boolean)(((Map) value).get("liquidation_next_day")) && key.contains("weixin")){
                        builder.set(ProtocolFields.APP_ID, ((Map) value).get("weixin_appid"));
                        builder.set(ProtocolFields.SUB_APP_ID, ((Map) value).get("weixin_sub_appid"));
                        builder.set(ProtocolFields.MCH_ID, ((Map) value).get("weixin_mch_id"));
                        builder.set(ProtocolFields.SUB_MCH_ID, ((Map) value).get("weixin_sub_mch_id"));
                        builder.set(ProtocolFields.DEVICE_INFO, "device");
                        return builder;
                    }

                }
            }
        }
        return null;
    }
}
