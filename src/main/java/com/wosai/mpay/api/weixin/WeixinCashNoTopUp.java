package com.wosai.mpay.api.weixin;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Base64;
import com.wosai.mpay.util.WebUtils;
import com.wosai.mpay.util.WeixinSignature;
import com.wosai.mpay.util.XmlUtils;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import java.io.*;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.logging.Logger;

/**
 * Created by jianfree on 25/7/17.
 */
public class WeixinCashNoTopUp {
    private static Logger logger = Logger.getLogger(WeixinCashNoTopUp.class.toString());
    private static final WeixinClient client = new WeixinClient();
    private static final ObjectMapper objectMapper = new ObjectMapper();
    public static final String MICRO_PAY = "https://api.mch.weixin.qq.com/sandboxnew/pay/micropay";//提交刷卡支付API [刷卡支付]
    public static final String UNIFIED_ORDER = "https://api.mch.weixin.qq.com/sandboxnew/pay/unifiedorder";//统一下单 [公众号支付 扫码支付]
    public static final String QUERY = "https://api.mch.weixin.qq.com/sandboxnew/pay/orderquery";//订单查询 [刷卡支付 公众号支付 扫码支付]
    public static final String REFUND = "https://api.mch.weixin.qq.com/sandboxnew/secapi/pay/refund";//申请退款  [刷卡支付 公众号支付 扫码支付]
    public static final String REFUND_QUERY = "https://api.mch.weixin.qq.com/sandboxnew/pay/refundquery";//查询退款 [刷卡支付 公众号支付 扫码支付]
    public static final String DOWNLOAD_BILL = "https://api.mch.weixin.qq.com/sandboxnew/pay/downloadbill"; //下载对照单

    private static final String APP_ID = "?";
    private static  String APP_KEY = "?";
    private static final String MCH_ID = "?";
    private static final String SUB_MCH_ID = "?";
    private static final String CERT_DATA_BASE64 = "?";

    public static void main(String[] args) throws Exception {
        initSandboxSignKey();
        getSSLContext();
        case1();
        case2();
        case3();
        case4();
        case5();
    }

    public static void initSandboxSignKey() throws Exception {
        APP_KEY = getSandboxSignKey();
    }

    public static void case1() throws Exception {
        logger.info("start case1");
        String orderSn = generateTradeNo();
        RequestBuilder builder = getDefaultRequestBuild();
        builder.set(BusinessFields.BODY, "免充值测试");
        builder.set(BusinessFields.DETAIL, "免充值测试");
        builder.set(BusinessFields.ATTACH, "no");
        builder.set(BusinessFields.OUT_TRADE_NO, orderSn);
        builder.set(BusinessFields.TOTAL_FEE, "501");
        builder.set(BusinessFields.FEE_TYPE, WeixinConstants.FEE_TYPE_CNY);
        builder.set(BusinessFields.SPBILL_CREATE_IP, "***************");
        builder.set(BusinessFields.AUTH_CODE, "130834011446666609");
        Map<String,Object> result = client.call(MICRO_PAY, APP_KEY, getSSLContext(), builder.build());
        logger.info("case1, pay result: " + objectMapper.writeValueAsString(result));
        builder = getDefaultRequestBuild();
        builder.set(BusinessFields.OUT_TRADE_NO, orderSn);
        result = client.call(QUERY, APP_KEY, getSSLContext(), builder.build());
        logger.info("case1, query result: " + objectMapper.writeValueAsString(result));
        logger.info("end case1");
    }

    public static void case2() throws Exception {
        logger.info("start case2");
        String orderSn = generateTradeNo();
        RequestBuilder builder = getDefaultRequestBuild();
        builder.set(BusinessFields.BODY, "免充值测试");
        builder.set(BusinessFields.DETAIL, "免充值测试");
        builder.set(BusinessFields.ATTACH, "no");
        builder.set(BusinessFields.OUT_TRADE_NO, orderSn);
        builder.set(BusinessFields.TOTAL_FEE, "502");
        builder.set(BusinessFields.FEE_TYPE, WeixinConstants.FEE_TYPE_CNY);
        builder.set(BusinessFields.SPBILL_CREATE_IP, "***************");
        builder.set(BusinessFields.AUTH_CODE, "130834011446666609");
        Map<String,Object> result = client.call(MICRO_PAY, APP_KEY, getSSLContext(), builder.build());
        logger.info("case2, pay result: " + objectMapper.writeValueAsString(result));
        builder = getDefaultRequestBuild();
        builder.set(BusinessFields.OUT_TRADE_NO, orderSn);
        result = client.call(QUERY, APP_KEY, getSSLContext(), builder.build());
        logger.info("case2, query result: " + objectMapper.writeValueAsString(result));
        String refundNo = generateTradeNo();
        builder = getDefaultRequestBuild();
        builder.set(ProtocolFields.DEVICE_INFO, "test");
        builder.set(BusinessFields.OUT_TRADE_NO, orderSn);
        builder.set(BusinessFields.OUT_REFUND_NO, refundNo);
        builder.set(BusinessFields.TOTAL_FEE, "502");
        builder.set(BusinessFields.REFUND_FEE, "502");
        builder.set(BusinessFields.REFUND_FEE_TYPE, WeixinConstants.FEE_TYPE_CNY);
        builder.set(BusinessFields.OP_USER_ID, "testuser");
        result = client.call(REFUND, APP_KEY, getSSLContext(), builder.build());
        logger.info("case2, refund result: " + objectMapper.writeValueAsString(result));
        builder = getDefaultRequestBuild();
        builder.set(ProtocolFields.DEVICE_INFO, "test");
        builder.set(BusinessFields.OUT_TRADE_NO, orderSn);
        builder.set(BusinessFields.OUT_REFUND_NO, refundNo);
        result = client.call(REFUND_QUERY, APP_KEY, getSSLContext(), builder.build());
        logger.info("case2, refund query result: " + objectMapper.writeValueAsString(result));
        logger.info("end case2");
    }

    public static void case3() throws Exception {
        logger.info("start case3");
        String orderSn = generateTradeNo();
        RequestBuilder builder = getDefaultRequestBuild();
        builder.set(BusinessFields.BODY, "免充值测试");
        builder.set(BusinessFields.DETAIL, "免充值测试");
        builder.set(BusinessFields.ATTACH, "no");
        builder.set(BusinessFields.OUT_TRADE_NO, orderSn);
        builder.set(BusinessFields.TOTAL_FEE, "551");
        builder.set(BusinessFields.FEE_TYPE, WeixinConstants.FEE_TYPE_CNY);
        builder.set(BusinessFields.SPBILL_CREATE_IP, "***************");
        builder.set(BusinessFields.TRADE_TYPE, WeixinConstants.TRADE_TYPE_NATIVE);
        builder.set(BusinessFields.NOTIFY_URL, "http://www.baidu.com/");
        Map<String,Object> result = client.call(UNIFIED_ORDER, APP_KEY, getSSLContext(), builder.build());
        logger.info("case3, pay result: " + objectMapper.writeValueAsString(result));
        Thread.sleep(5000);
        builder = getDefaultRequestBuild();
        builder.set(BusinessFields.OUT_TRADE_NO, orderSn);
        result = client.call(QUERY, APP_KEY, getSSLContext(), builder.build());
        logger.info("case3, query result: " + objectMapper.writeValueAsString(result));
        logger.info("end case3");
    }

    public static void case4() throws Exception {
        logger.info("start case4");
        String orderSn = generateTradeNo();
        RequestBuilder builder = getDefaultRequestBuild();
        builder.set(BusinessFields.BODY, "免充值测试");
        builder.set(BusinessFields.DETAIL, "免充值测试");
        builder.set(BusinessFields.ATTACH, "no");
        builder.set(BusinessFields.OUT_TRADE_NO, orderSn);
        builder.set(BusinessFields.TOTAL_FEE, "552");
        builder.set(BusinessFields.FEE_TYPE, WeixinConstants.FEE_TYPE_CNY);
        builder.set(BusinessFields.SPBILL_CREATE_IP, "***************");
        builder.set(BusinessFields.TRADE_TYPE, WeixinConstants.TRADE_TYPE_NATIVE);
        builder.set(BusinessFields.NOTIFY_URL, "http://www.baidu.com/");
        Map<String,Object> result = client.call(UNIFIED_ORDER, APP_KEY, getSSLContext(), builder.build());
        logger.info("case4, pay result: " + objectMapper.writeValueAsString(result));
        Thread.sleep(5000);
        builder = getDefaultRequestBuild();
        builder.set(BusinessFields.OUT_TRADE_NO, orderSn);
        result = client.call(QUERY, APP_KEY, getSSLContext(), builder.build());
        logger.info("case4, query result: " + objectMapper.writeValueAsString(result));

        String refundNo = generateTradeNo();
        builder = getDefaultRequestBuild();
        builder.set(ProtocolFields.DEVICE_INFO, "test");
        builder.set(BusinessFields.OUT_TRADE_NO, orderSn);
        builder.set(BusinessFields.OUT_REFUND_NO, refundNo);
        builder.set(BusinessFields.TOTAL_FEE, "552");
        builder.set(BusinessFields.REFUND_FEE, "552");
        builder.set(BusinessFields.REFUND_FEE_TYPE, WeixinConstants.FEE_TYPE_CNY);
        builder.set(BusinessFields.OP_USER_ID, "testuser");
        result = client.call(REFUND, APP_KEY, getSSLContext(), builder.build());
        logger.info("case4, refund result: " + objectMapper.writeValueAsString(result));
        builder = getDefaultRequestBuild();
        builder.set(ProtocolFields.DEVICE_INFO, "test");
        builder.set(BusinessFields.OUT_TRADE_NO, orderSn);
        builder.set(BusinessFields.OUT_REFUND_NO, refundNo);
        result = client.call(REFUND_QUERY, APP_KEY, getSSLContext(), builder.build());
        logger.info("case4, refund query result: " + objectMapper.writeValueAsString(result));

        logger.info("end case4");
    }

    public static void case5() throws Exception {
        logger.info("case5 start");
        RequestBuilder builder = getDefaultRequestBuild();
        String day = new SimpleDateFormat("yyyyMMdd").format(new Date());
        builder.set(BusinessFields.BILL_DATE, day);
        builder.set(BusinessFields.BILL_TYPE, "ALL");
        Map<String,Object> request = builder.build();
        request.put(ProtocolFields.NONCE_STR, "XXX");
        for(Object mapKey: request.keySet().toArray()){
            if(request.get(mapKey) == null){
                request.remove(mapKey);
            }
        }
        request.remove(ProtocolFields.SIGN);
        request.put(ProtocolFields.SIGN, WeixinSignature.getSign(request, APP_KEY, WeixinConstants.CHARSET_UTF8));
        String requestXml = XmlUtils.map2XmlString(request, "xml");
        String response = WebUtils.doPost(null, null, DOWNLOAD_BILL, "text/xml", requestXml.getBytes(), 5000, 5000);
        logger.info("case5, download bill result: " + response);
        logger.info("case5 end");
    }


    public static SSLContext getSSLContext(byte [] certData, String password){
        InputStream inputStream = new ByteArrayInputStream(certData);
        SSLContext sslContext = null;
        try {
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(inputStream, password.toCharArray());//设置证书密码
            sslContext = SSLContext.getInstance("TLS");
            KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
            kmf.init(keyStore, password.toCharArray());
            sslContext.init(kmf.getKeyManagers(), null, new SecureRandom());
        } catch (Exception e) {
            logger.warning("getSSLContext error" + e.getMessage());
        }
        return sslContext;
    }


    public static  SSLContext  getSSLContext(){
        return getSSLContext(Base64.decode(CERT_DATA_BASE64), MCH_ID);
    }

    private byte[] getBytes(String string , String charset) throws MpayException {
        if(string == null){
            return null;
        }else {
            try {
                return string.getBytes(charset);
            } catch (UnsupportedEncodingException e) {
                throw new MpayException("UnsupportedEncodingException :", e);
            }
        }
    }

    private String getNonceStr(){
        return new Random().nextLong() + "";
    }
    private static String generateTradeNo() {
        Random random = new Random();
        return String.format("%d%d", System.currentTimeMillis(), random.nextInt(10000));
    }


    public static RequestBuilder getDefaultRequestBuild(){
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, APP_ID);
        builder.set(ProtocolFields.SUB_APP_ID, null);
        builder.set(ProtocolFields.MCH_ID, MCH_ID);
        builder.set(ProtocolFields.SUB_MCH_ID, SUB_MCH_ID);
        return  builder;
    }

    public static String  getSandboxSignKey() throws Exception{
        String url = "https://api.mch.weixin.qq.com/sandboxnew/pay/getsignkey";
        Map<String,Object> request = new HashMap<String, Object>();
        request.put("mch_id", MCH_ID);
        request.put("nonce_str", "werwe");
        Map<String,Object> result = new WeixinClient().call(url, APP_KEY, null, request);
        logger.info("sandbox sign key: " + objectMapper.writeValueAsString(result));
        return result.get("sandbox_signkey").toString();
    }
}
