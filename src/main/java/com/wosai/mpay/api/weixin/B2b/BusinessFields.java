package com.wosai.mpay.api.weixin.B2b;

/***
 * @ClassName: BusinessFields
 * @Description:
 * @Auther: dabuff
 * @Date: 2025/1/13 10:55
 */
public class BusinessFields {

    public static final String ACCESS_TOKEN = "access_token";   //token

    public static final String SIGN_DATA = "signData";          //该参数以string传递
    public static final String MODE = "mode";                   // 支付类型 不同mode的signData不同，B2b支付固定填retail_pay_goods。
    public static final String PAY_SIG = "paySig";              // 支付签名 支付签名，详见签名详解。
    public static final String MCHID = "mchid";                 // 微信商户号 由微信支付生成并下发的商户号。
    public static final String OUT_TRADE_NO = "out_trade_no";   // 商户订单号 原支付交易对应的商户订单号。商户订单号和B2b支付订单号必填其一。
    public static final String ORDER_ID = "order_id";           // B2b支付订单号 原支付交易对应的B2b支付订单号。商户订单号和B2b支付订单号必填其一。
    public static final String OUT_REFUND_NO = "out_refund_no"; // 商户退款单号 商户系统内部退款单号，商户系统内部唯一，只能是数字、大小写字母_-*，同一退款单号多次请求只退一笔。
    public static final String REFUND_AMOUNT = "refund_amount"; // 退款金额 退款金额，单位为分，只能为整数，不能超过原订单支付金额。
    public static final String REFUND_FROM = "refund_from";     // 退款来源 退款来源，枚举值 1：人工客服退款 2：用户自己退款 3：其他。
    public static final String REFUND_REASON = "refund_reason"; // 退款原因 退款原因，枚举值 0：暂无描述 1：产品问题 2：售后问题 3：意愿问题 4：价格问题 5：其他原因。
    public static final String REFUND_ID = "refund_id";         // B2b支付退款单号 B2b支付退款单号。商户退款单号和B2b支付退款单号必填其一。

    /*  signData start  */
    //mchid
    //out_trade_no
    public static final String DESCRIPTION = "description";     // 商品描述 商品描述，最多 127 字节。
    public static final String AMOUNT = "amount";               // 订单金额信息 订单金额信息，仅支持人民币，格式见Amount。
    public static final String ATTACH = "attach";               // 附加数据 附加数据，在查询API和支付通知中原样返回，可作为自定义参数使用。
    public static final String PRODUCT_INFO = "product_info";   // 商品信息 订单详细商品信息，格式见ProductInfo。
    public static final String DELIVERY_TYPE = "delivery_type"; // 配送方式 配送方式，具体取值为：1.同城配送 2.快递配送 3.门店自提 4.无需配送与提货。
    public static final String ENV = "env";                     // 下单环境 下单环境，具体取值为：0.生产环境 1.沙箱环境。
    /*  signData end    */

    /*  amount start    */
    public static final String PRODUCT_AMOUNT = "product_amount";   // 商品原总金额 订单所有商品的原价总和，单位为分。
    public static final String FREIGHT = "freight";                 // 运费 订单运费，单位为分。
    public static final String DISCOUNT = "discount";               // 总优惠金额 订单总计优惠金额，单位为分。
    public static final String OTHER_FEE = "other_fee";             // 其他费用 订单其他费用总金额，单位为分。
    public static final String ORDER_AMOUNT = "order_amount";       // 订单总金额 订单总需支付金额，也即是真正下单总金额，单位为分。
    public static final String CURRENCY = "currency";               // 货币类型 货币类型，仅支持"CNY"。
    /*  signData end    */

    /*  product_info start    */
    public static final String SPU_ID = "spu_id";       // 商品编号 商户系统内该商品的spuid。
    public static final String SKU_ID = "sku_id";       // 商品规格编号 商户系统内该商品的skuid。
    public static final String TITLE = "title";         // 商品标题 商品标题。
    public static final String PATH = "path";           // 商详页小程序路径 商户商品详请页小程序路径。
    public static final String HEAD_IMG = "head_img";   // 商品主图 商品主图的url，大小建议64*64。
    public static final String CATEGORY = "category";   // 类目 商户侧该商品所属的类目。
    public static final String SKU_ATTR = "sku_attr";   // sku属性 商户系统内该商品的sku属性。
    public static final String ORG_PRICE = "org_price"; // 商品原价 该商品原价，单位为分。
    public static final String SALE_PRICE = "sale_price";// 商品售价 该商品售价，单位为分。
    public static final String QUANTITY = "quantity";   // 商品数量 用户购买该商品的数量。
    /*  product_info end    */

}
