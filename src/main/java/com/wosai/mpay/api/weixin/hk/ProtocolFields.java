package com.wosai.mpay.api.weixin.hk;

/**
 * Created by jian<PERSON> on 12/11/15.
 */
public class ProtocolFields {
    public static final String APP_ID = "appid"; //微信分配的公众账号ID
    public static final String SUB_APP_ID = "sub_appid";//微信分配的子商户公众账号ID
    public static final String MCH_ID = "mch_id";//微信支付分配的商户号
    public static final String SUB_MCH_ID = "sub_mch_id";//微信分配的子商户公众账号ID
    public static final String DEVICE_INFO = "device_info";//调用接口提交的终端设备号
    public static final String NONCE_STR = "nonce_str";//随机字符串
    public static final String SIGN = "sign";//签名
    public static final String VERSION = "version";//版本号
    public static final String SIGN_TYPE = "sign_type";//签名方式
}
