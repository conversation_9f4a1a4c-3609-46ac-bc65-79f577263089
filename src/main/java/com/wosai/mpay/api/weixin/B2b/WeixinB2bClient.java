package com.wosai.mpay.api.weixin.B2b;


import java.net.URI;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.util.HashMap;
import java.util.Map;

/***
 * @ClassName: WeixinB2BClient
 * @Description:
 * @Auther: dabuff
 * @Date: 2025/1/13 11:57
 */
public class WeixinB2bClient {

    public static final Logger logger = LoggerFactory.getLogger(WeixinB2bClient.class);

    private int connectTimeout = 3000;
    private int readTimeout = 5000;
    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }
    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    /**
     * 调用支付接口服务
     *
     * 该方法负责组装请求数据，发送HTTPS请求到指定的服务URL，并解析返回的响应数据
     *
     * @param serviceUrl 服务URL模板，包含占位符用于插入实际的访问令牌和签名
     * @param signType 签名类型，指示使用哪种算法生成签名
     * @param signKey 签名密钥，用于生成签名
     * @param sslContext SSL上下文，用于HTTPS连接
     * @param request 请求数据，包含所有需要发送给服务的数据
     * @param accessToken 访问令牌，用于服务URL中的身份验证
     * @return 解析后的响应数据，以键值对的形式返回
     * @throws MpayException 如果在调用过程中发生任何错误，包括签名错误、网络错误等
     * @throws MpayApiNetworkError 如果网络请求失败
     */
    public Map<String, Object> call(String serviceUrl, String signType, String signKey, SSLContext sslContext, Map<String,Object> request, String accessToken) throws MpayException, MpayApiNetworkError {

        // 生成签名数据，用于验证请求的完整性和合法性
        Map<String, Object> signData = buildSignData(serviceUrl, signType, signKey, request);
        // 格式化服务URL，插入访问令牌和签名
        serviceUrl = serviceUrl + String.format("?access_token=%s&pay_sig=%s", accessToken, MapUtils.getString(signData, BusinessFields.PAY_SIG));
        // 记录请求数据的日志
        String requestStr = JsonUtil.objectToJsonString(request);
        logger.info("request {}", requestStr);

        // 发送HTTPS POST请求，并获取响应数据
        String responseStr = HttpClientUtils.doPost(WeixinB2bClient.class.getName(), sslContext, null, serviceUrl, WeixinConstants.CONTENT_TYPE, requestStr, WeixinConstants.CHARSET_UTF_8, connectTimeout, readTimeout);

        // 记录响应数据的日志
        logger.info("response {}", responseStr);
        // 尝试解析响应数据为Map对象
        try {
            return JsonUtil.jsonStringToObject(responseStr, Map.class);
        } catch (Exception e) {
            // 如果解析失败，抛出自定义异常
            throw new MpayApiUnknownResponse("parse response error: " + e.getMessage(), e);
        }
    }

    /**
     * 构建签名数据
     * 该方法用于根据给定的请求参数和签名密钥生成签名数据，主要用于支付过程中的安全验证
     * 它会移除请求中可能已存在的签名字段，以确保签名的准确性和一致性
     *
     * @param serviceUrl 请求的统一资源标识符，用于唯一标识请求的接口
     * @param signType 签名类型，决定了使用何种算法进行签名
     * @param signKey 签名密钥，用于签名算法中加密数据
     * @param request 原始请求参数，包含需要签名的数据
     * @return 返回包含签名数据的映射，主要包含签名内容和支付签名
     * @throws MpayException 如果签名过程中发生错误，抛出此异常
     */
    public Map<String, Object> buildSignData(String serviceUrl, String signType, String signKey, Map<String, Object> request) throws MpayException {

        String uriPath = serviceUrl;
        if (serviceUrl.startsWith("http")) {
            URI uri;
            try {
                uri = new URI(serviceUrl);
            } catch (Exception e) {
                throw new MpayException("uri path 获取失败！原service url is " + serviceUrl, e);
            }
            uriPath = uri.getPath();
        }

        // 移除请求中已存在的签名字段，避免重复或错误的签名验证
        request.remove(BusinessFields.PAY_SIG);
        request.remove(BusinessFields.SIGN_DATA);

        // 初始化结果映射，用于存储签名数据
        Map<String, Object> result = new HashMap<>();
        // 获取签名前的内容，这是通过对请求参数进行特定处理得到的
        String content =  JsonUtil.objectToJsonString(request);
        // 将处理后的签名内容添加到结果映射中
        result.put(BusinessFields.SIGN_DATA, content);

        String paySign = null;
        // 根据签名类型选择相应的签名算法
        if(WeixinConstants.SIGN_TYPE_HMAC_SHA256.equals(signType)){
            // 对于HMAC_SHA256签名类型，构造需要签名的消息
            String needSignMsg = uriPath + "&" + content;
            // 使用给定的签名密钥进行签名
            paySign = HmacSignature.sign(needSignMsg, WeixinConstants.SIGN_TYPE_HMAC_SHA256, signKey, false);
        } else {
            throw new MpayException("不支持该签名类型:" + signType);
        }
        // 将生成的支付签名添加到结果映射中
        result.put(BusinessFields.PAY_SIG, paySign);
        // 返回包含签名数据的结果映射
        return result;
    }

}
