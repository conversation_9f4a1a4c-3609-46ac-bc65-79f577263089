package com.wosai.mpay.api.weixin.B2b;

import java.util.*;

/***
 * @ClassName: RequestBuilder
 * @Description:
 * @Auther: dabuff
 * @Date: 2025/1/13 11:59
 */
public class RequestBuilder {

    private Map<String,Object> request;

    public RequestBuilder(){
        request = new LinkedHashMap<>();
    }

    public void set(String field, Object value) {
        request.put(field,  value);
    }

    public Map<String,Object> build(){
        return request;
    }

}
