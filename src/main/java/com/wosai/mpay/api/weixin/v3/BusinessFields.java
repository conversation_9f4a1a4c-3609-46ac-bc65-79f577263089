package com.wosai.mpay.api.weixin.v3;

/**
 * Created by jian<PERSON> on 12/11/15.
 */
public class BusinessFields {

    public static final String TRANSACTION_ID = "transaction_id";
    public static final String BODY = "body";
    public static final String DETAIL = "detail";
    public static final String ATTACH = "attach";
    public static final String ATTACH_BANK_MCH_NAME = "bank_mch_name";
    public static final String ATTACH_BANK_MCH_ID = "bank_mch_id";
    public static final String OUT_TRADE_NO = "out_trade_no";
    public static final String FEE_TYPE = "fee_type";
    public static final String TOTAL_FEE = "total_fee";
    public static final String SPBILL_CREATE_IP = "spbill_create_ip";
    public static final String TIME_START = "time_start";
    public static final String TIME_EXPIRE = "time_expire";
    public static final String GOODS_TAG = "goods_tag";
    public static final String GOODS_DETAIL = "goods_detail";
    public static final String NOTIFY_URL = "notify_url";
    public static final String TRADE_TYPE = "trade_type";
    public static final String PRODUCT_ID = "product_id";
    public static final String LIMIT_PAY = "limit_pay";
    public static final String LIMIT_PAYER = "limit_payer";
    public static final String OPEN_ID = "openid";
    public static final String SUB_OPEN_ID = "sub_openid";
    public static final String OUT_REFUND_NO = "out_refund_no";
    public static final String REFUND_FEE = "refund_fee";
    public static final String REFUND_FEE_TYPE = "refund_fee_type";
    public static final String OP_USER_ID = "op_user_id";
    public static final String AUTH_CODE = "auth_code";
    public static final String REFUND_ID = "refund_id";
    public static final String BILL_DATE = "bill_date";
    public static final String BILL_TYPE = "bill_type";
    public static final String SUB_APPID = "sub_appid";
    public static final String SCENE_INFO = "scene_info";
    public static final String SCEND_INFO_STORE_INFO = "store_info";
    public static final String SCENE_INFO_ID = "id";
    public static final String SCENE_INFO_NAME = "name";
    public static final String SCENE_INFO_AREA_CODE = "area_code";
    public static final String SCENE_INFO_ADDRESS = "address";
    public static final String DEPOSIT = "deposit";
    public static final String SIGN_TYPE = "sign_type";
    public static final String CONSUME_FEE = "consume_fee";
    public static final String WXPAY_GOODS_ID = "wxpay_goods_id";

    public static String ORGID = "orgid";//四方机构号	通联分配的四方机构号
    public static final String RECEIVER = "receiver";
    public static final String TYPE = "type";
    public static final String ACCOUNT = "account";
    public static final String NAME = "name";
    public static final String RELATION_TYPE = "relation_type";
    public static final String CUSTOM_RELATION = "custom_relation";
    public static final String AMOUNT = "amount";
    public static final String DESCRIPTION = "description";
    public static final String ORDER_ID = "order_id";
    public static final String OUT_ORDER_NO = "out_order_no";
    public static final String OUT_RETURN_NO = "out_return_no";
    public static final String RETURN_ACCOUNT_TYPE = "return_account_type";
    public static final String RETURN_ACCOUNT = "return_account";
    public static final String RETURN_AMOUNT = "return_amount";
    public static final String CREATE_TIME = "create_time";
    public static final String EVENT_TYPE = "event_type";
    public static final String SUMMARY = "summary";
    public static final String RESOURCE_TYPE = "resource_type";
    public static final String RESOURCE = "resource";
    public static final String ALGORITHM = "algorithm";
    public static final String ORIGINAL_TYPE = "original_type";
    public static final String CIPHERTEXT = "ciphertext";
    public static final String ASSOCIATED_DATA = "associated_data";
    public static final String PROFIT_SHARING = "profit_sharing";
    public static final String RECEIVERS = "receivers";

    public static final String STORE_ID = "store_id";
    public static final String STORE_NAME = "store_name";
    public static final String DEVICE_ID = "device_id";
    public static final String RAWDATA = "rawdata";
    public static final String NOW = "now";

    public static final String SERVICE_ID = "service_id";
    public static final String SERVICE_INTRODUCTION = "service_introduction";
    public static final String POST_PAYMENTS = "post_payments";
    public static final String COUNT = "count";
    public static final String POST_DISCOUNTS = "post_discounts";
    public static final String TIME_RANGE = "time_range";
    public static final String START_TIME = "start_time";
    public static final String START_TIME_REMARK = "start_time_remark";
    public static final String END_TIME = "end_time";
    public static final String END_TIME_REMARK = "end_time_remark";
    public static final String LOCATION = "location";
    public static final String START_LOCATION = "start_location";
    public static final String END_LOCATION = "end_location";
    public static final String RISK_FUND = "risk_fund";
    public static final String NEED_USER_CONFIRM = "need_user_confirm";
    public static final String TOTAL_AMOUNT = "total_amount";
    public static final String REASON = "reason";
    public static final String AUTHORIZATION_CODE = "authorization_code";

    public static final String CHANNEL_NAME = "channel_name"; //微信支付分配给收单服务商的名称
    public static final String MERCHANT_NAME = "merchant_name"; //该名称是公司主体全称，绑定公众号时会对主体一致性校验
    public static final String AREA_INFO = "area_info"; //地区信息
    public static final String TERMINAL_INFO = "terminal_info"; //商户侧受理终端信息
    public static final String TERM_INFO_LOCATION = "location"; //终端实时经纬度信息
    public static final String TERM_INFO_DEVICE_TYPE = "device_type"; //设备类型
    public static final String TERM_INFO_SERIAL_NUM = "serial_num"; // 终端设备的硬件序列号
    public static final String TERM_INFO_DEVICE_ID = "device_id"; //终端设备号
    public static final String TERM_INFO_DEVICE_IP = "device_ip"; //终端设备ip
    public static final String BRAND_MCH_ID = "brand_mch_id"; //品牌主商户号

    public static final String AMOUNT_TOTAL = "total"; //amount.total 发起交易总金额
    public static final String AMOUNT_CURRENCY = "currency";//amount.currency 【货币类型】符合ISO 4217标准的三位字母代码，固定传：CNY，代表人民币。
    public static final String PAYER = "payer"; //支付者信息
    public static final String PAYER_SP_OPENID = "sp_openid"; //支付者信息
    public static final String PAYER_SUB_OPENID = "sub_openid"; //支付者信息


    public static final String SETTLE_INFO = "settle_info";//结算信息
    public static final String SCENE_INFO_PAYER_CLIENT_IP = "payer_client_ip";
    public static final String SCENE_INFO_DEVICE_ID = "device_id"; //终端设备号

    public static final String AMOUNT_REFUND = "refund";
    public static final String AMOUNT_FROM = "from";
    // From数组内对象的字段
    public static final String FROM_ACCOUNT = "account";
    public static final String FROM_AMOUNT = "amount";

    //微信分付曝光接口 用户信息
    public static final String EXPOSURE_USER_ID = "user_id";
    public static final String EXPOSURE_USER_ID_TYPE = "user_id_type";
    public static final String EXPOSURE_USER_ID_SP_OPENID = "sp_openid";
    public static final String EXPOSURE_USER_ID_SUB_OPENID = "sub_openid";
    public static final String EXPOSURE_USER_ID_PHONE_NUMBER_HASH = "phone_number_hash";

}
