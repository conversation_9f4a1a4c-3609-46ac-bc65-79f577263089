package com.wosai.mpay.api.weixin.hk;

/**
 * Created by jian<PERSON> on 6/12/15.
 */
public class WapFields {
    public static final String APP_ID = "appId";//公众号id
    public static final String TIME_STAMP = "timeStamp";//时间戳
    public static final String NONCE_STR = "nonceStr";//随机字符串
    public static final String PACKAGE = "package";//订单详情扩展字符串
    public static final String PREPAY_ID = "prepay_id"; //package 统一下单接口返回的prepay_id参数值
    public static final String SIGN_TYPE = "signType";//签名方式
    public static final String PAY_SIGN = "paySign";//签名
}
