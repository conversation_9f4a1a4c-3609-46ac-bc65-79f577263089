package com.wosai.mpay.api.weixin;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by jian<PERSON> on 13/11/15.
 */
public class RequestBuilder {
    private Map<String,Object> request;

    public RequestBuilder(){
        request = new HashMap<String, Object>();
    }

    public void set(String field, Object value) {
        request.put(field,  value);
    }

    public void remove(String field) {
        request.remove(field);
    }

    public Map<String,Object> build(){
        return request;
    }
}
