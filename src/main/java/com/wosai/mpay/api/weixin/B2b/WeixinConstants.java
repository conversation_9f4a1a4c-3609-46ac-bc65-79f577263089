package com.wosai.mpay.api.weixin.B2b;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/***
 * @ClassName: WeixinConstants
 * @Description:
 * @Auther: dabuff
 * @Date: 2025/1/13 11:49
 */
public class WeixinConstants {

    public static final String CONTENT_TYPE = "application/json;charset=utf-8";
    public static final String CHARSET_UTF_8 = "UTF-8";
    public static final String SIGN_TYPE_HMAC_SHA256 = "HMAC-SHA256";
    public static final String COMMON_PAYMENT_URI = "requestCommonPayment";

    // 下单环境
    public static final int ENV_PRODUCTION = 0; // 生产环境
    public static final int ENV_SANDBOX = 1;    // 沙箱环境

    // 配送方式
    public static final int DELIVERY_TYPE_SAME_CITY = 1;    // 同城配送
    public static final int DELIVERY_TYPE_EXPRESS = 2;      // 快递配送
    public static final int DELIVERY_TYPE_STORE_PICKUP = 3; // 门店自提
    public static final int DELIVERY_TYPE_NONE = 4;         // 无需配送与提货

    // 订单状态
    public static final String PAY_STATUS_ORDER_INIT = "ORDER_INIT";            // 订单初始化
    public static final String PAY_STATUS_ORDER_PRE_PAY = "ORDER_PRE_PAY";      // 订单预下单成功，待支付
    public static final String PAY_STATUS_ORDER_PAY_SUCC = "ORDER_PAY_SUCC";    // 订单支付成功
    public static final String PAY_STATUS_ORDER_CLOSE = "ORDER_CLOSE";          // 订单已关闭
    public static final String PAY_STATUS_ORDER_REFUND_PROCESSING = "ORDER_REFUND_PROCESSING"; // 订单正在退款中
    public static final String PAY_STATUS_ORDER_REFUND = "ORDER_REFUND";        // 订单已有退款

    // 错误码
    public static final String ERR_CODE_SUCCESS = "0"; // 成功

    // 退款来源
    public static final int REFUND_FROM_CUSTOMER_SERVICE = 1; // 人工客服退款
    public static final int REFUND_FROM_USER_SELF = 2;        // 用户自己退款
    public static final int REFUND_FROM_OTHERS = 3;           // 其他

    // 退款原因
    public static final int REFUND_REASON_NONE = 0;             // 暂无描述
    public static final int REFUND_REASON_PRODUCT_ISSUE = 1;    // 产品问题
    public static final int REFUND_REASON_AFTER_SALE_ISSUE = 2; // 售后问题
    public static final int REFUND_REASON_INTENTIONAL = 3;      // 意愿问题
    public static final int REFUND_REASON_PRICE_ISSUE = 4;      // 价格问题
    public static final int REFUND_REASON_OTHER_REASON = 5;     // 其他原因

    // 退款状态
    public static final String REFUND_STATUS_REFUND_INIT = "REFUND_INIT";               // 退款单初始化
    public static final String REFUND_STATUS_REFUND_PROCESSING = "REFUND_PROCESSING";   // 退款处理中
    public static final String REFUND_STATUS_REFUND_SUCC = "REFUND_SUCC";               // 退款成功
    public static final String REFUND_STATUS_REFUND_FAIL = "REFUND_FAIL";               // 退款失败

    // 支付类型 mode
    public static final String MODE_RETAIL_PAY_GOODS = "retail_pay_goods"; // B2b支付固定填此值

    // 货币类型 currency
    public static final String CURRENCY_CNY = "CNY"; // 仅支持人民币

    // 消息类型 MsgType
    public static final String MSG_TYPE_EVENT = "event"; // 固定为event

    // 事件类型 Event
    public static final String EVENT_RETAIL_PAY_NOTIFY = "retail_pay_notify"; // 固定为此值


    /**
     * 微信B2b接口所允许的字段
     */
    public static final Set<String> PRE_CREATE_ALLOWED_FIELDS = new HashSet<String>(){{
        addAll(Arrays.asList(
                BusinessFields.ATTACH, BusinessFields.PRODUCT_INFO, BusinessFields.DELIVERY_TYPE
        ));
    }};

}
