package com.wosai.mpay.api.weixin;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wosai.mpay.api.SSLEnvFlag;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HmacSignature;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.SM2Util;
import com.wosai.mpay.util.SSLUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.mpay.util.WebUtils;
import com.wosai.mpay.util.WeixinSignature;
import com.wosai.mpay.util.XmlUtils;
import com.wosai.pantheon.util.MapUtil;

/**
 * Created by jianfree on 12/11/15.
 */
public class WeixinClient {
    public static final Logger logger = LoggerFactory.getLogger(WeixinClient.class);

    public static final String SIGN_TYPE_WEIXIN = "weixin";
    public static final String SIGN_TYPE_WEIXIN_ADD = "weixin_add";

    public static final String SIGN_TYPE_NUCC = "nucc";
    public static final String SIGN_TYPE_UNIONPAY = "unionpay";
    public static final String SIGN_TYPE_HAIKE = "haike";
    public static final String SIGN_TYPE_SM2 = "SM2";

    public static final String SIGN_TYPE_HMAC_SHA256 = "HMAC-SHA256";
    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public static SSLContext getSSLContext(byte [] certData, String password){
        InputStream inputStream = new ByteArrayInputStream(certData);
        SSLContext sslContext = null;
        try {
            char [] passwordChars = password != null ? password.toCharArray() : null;
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(inputStream, passwordChars);//设置证书密码
            sslContext = SSLContext.getInstance("TLS");
            KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
            kmf.init(keyStore, passwordChars);
            sslContext.init(kmf.getKeyManagers(), null, new SecureRandom());
        } catch (Exception e) {
            logger.error("getSSLContext error: {}", e.getMessage(), e);
        }
        return sslContext;
    }


    public static  SSLContext  getSSLContext(){
        InputStream instream = null;
        try {
            //加载本地的证书进行https加密传输
            String certPath = "/Users/<USER>/Documents/wosai-work/cert/apiclient_cert.p12";//微信证书路径
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            File file = new File(certPath);
            if(file.exists()){
                instream = new FileInputStream(file);
            }else{
                instream = WeixinClient.class.getResourceAsStream(certPath);
            }
            keyStore.load(instream, WeixinConfig.CERT_PASSWORD.toCharArray());//设置证书密码
            SSLContext sslContext = SSLContext.getInstance("TLS");
            KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
            kmf.init(keyStore, WeixinConfig.CERT_PASSWORD.toCharArray());
            sslContext.init(kmf.getKeyManagers(), null, new SecureRandom());
            return sslContext;
        } catch (Exception e) {
           logger.error("getSSLContext error: {}", e.getMessage(), e);
        } finally {
            if(instream != null){
                try {
                    instream.close();
                } catch (IOException e) {
                }
            }
        }
        return null;
    }

    public static  SSLContext  getNUCCSSLContext(){
        InputStream instream = null;
        try {
            //加载本地的证书进行https加密传输
            String certPath = "/data/work/wosai/documents/网联对接/证书/Z2016911000015_4003399532_RSA_ServerCert.p12";//微信证书路径
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            File file = new File(certPath);
            if(file.exists()){
                instream = new FileInputStream(file);
            }else{
                instream = WeixinClient.class.getResourceAsStream(certPath);
            }
            keyStore.load(instream, null);//设置证书密码
            SSLContext sslContext = SSLContext.getInstance("TLS");
            KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
            kmf.init(keyStore, null);
            sslContext.init(kmf.getKeyManagers(), null, new SecureRandom());
            return sslContext;
        } catch (Exception e) {
            logger.error("getSSLContext error: {}", e.getMessage(), e);
        } finally {
            if(instream != null){
                try {
                    instream.close();
                } catch (IOException e) {
                }
            }
        }
        return null;
    }

    public Map<String,Object> call(String serviceUrl,String signKey, SSLContext sslContext,  Map<String,Object> request) throws MpayException, MpayApiNetworkError {
        return call(serviceUrl, SIGN_TYPE_WEIXIN, signKey, sslContext, request);
    }

    /**
     *
     * @param serviceUrl
     * @param signType 根据此判断是直连微信，还是网联, 以此来根据不同的算法来算签名
     * @param signKey
     * @param sslContext
     * @param request
     * @return
     * @throws MpayException
     * @throws MpayApiNetworkError
     */
    public Map<String,Object> call(String serviceUrl, String signType, String signKey, SSLContext sslContext,  Map<String,Object> request) throws MpayException, MpayApiNetworkError {
        String requestXml = preProcess(signType, signKey, request);
        logger.info("request {}", requestXml);
        //由于某些支付通道的测试环境会出现证书不符等各种异常的情况，故此处做特殊处理
        sslContext = SSLEnvFlag.turnOffSSl() ? SSLUtil.getUnsafeSSLContext() : sslContext;
        HostnameVerifier hostnameVerifier = SSLEnvFlag.getNotVerifyHostNames().size() > 0 ? SSLUtil.getUnsafeHostnameVerifier(SSLEnvFlag.getNotVerifyHostNames()) : null;
//        String response = WebUtils.doPost(sslContext, hostnameVerifier, serviceUrl, "text/xml",getBytes(requestXml, WeixinConstants.CHARSET_UTF8), connectTimeout, readTimeout);
        String response = HttpClientUtils.doPost(WeixinClient.class.getName(), sslContext, hostnameVerifier, serviceUrl, "text/xml", requestXml, WeixinConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        logger.info("response {}",response.replaceAll("\\n", ""));//微信返回的xml报文，有换行，打印日志时，打印在一行上面。
        return postProcess(response, signType);
    }

    public static String preProcess(String signType, String signKey, Map<String,Object> request) throws MpayException {
        if (!SIGN_TYPE_WEIXIN_ADD.equals(signType)) {
            request.put(ProtocolFields.NONCE_STR, getNonceStr());
        }
        for(Object mapKey: request.keySet().toArray()){
            if(request.get(mapKey) == null){
                request.remove(mapKey);
            }
        }
        request.remove(ProtocolFields.SIGN);
        String sign = null;
        if(SIGN_TYPE_WEIXIN.equals(signType)){
            sign = WeixinSignature.getSign(request, signKey, WeixinConstants.CHARSET_UTF8);
        } else if(SIGN_TYPE_NUCC.equals(signType) || SIGN_TYPE_UNIONPAY.equals(signType)){
            sign = RsaSignature.sign(request, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, signKey);
        } else if (SIGN_TYPE_WEIXIN_ADD.equals(signType)) {
            sign = WeixinSignature.getSign(request, signKey, WeixinConstants.CHARSET_UTF8);
        } else if (SIGN_TYPE_HMAC_SHA256.equals(signType)) {
            sign = HmacSignature.sign(request, signKey);
        } else if(SIGN_TYPE_SM2.equals(signType)) {
            try {
                String certId = MapUtil.getString(request, ProtocolFields.CERT_ID);
                sign = SM2Util.unionpaySign(certId, signKey, RsaSignature.getSignCheckContent(request));
            } catch (Exception e) {
                throw new MpayException("签名失败", e);
            }
        } else if (SIGN_TYPE_HAIKE.equals(signType)) {
            //海科走前置机做加签, 故 sign 上送 空字符串， 上送null ,XmlUtils.map2XmlString 解析不了
            sign = StringUtils.EMPTY;
        }
        request.put(ProtocolFields.SIGN, sign);

        return XmlUtils.map2XmlString(request, "xml");
    }

    public static Map<String,Object> postProcess(String response, String signType){
        Map<String, Object> result = XmlUtils.parse(response);
        if(result != null && result.containsKey(ResponseFields.PROMOTION_DETAIL)){
            String promotion = (String) result.get(ResponseFields.PROMOTION_DETAIL);
            if(!StringUtils.isEmpty(promotion)){
                try {
                    Object value = JsonUtil.jsonStringToObject(promotion, Object.class);
                    //银联返回报文不存在嵌套的promotion_detail, 所以在此修改为直连微信一样的嵌套的promotion_detail
                    if(SIGN_TYPE_UNIONPAY.equals(signType) || SIGN_TYPE_SM2.equals(signType) || SIGN_TYPE_HAIKE.equals(signType)){
                        Map<String,Object> newValue = new HashMap();
                        newValue.put(ResponseFields.PROMOTION_DETAIL, value);
                        value = newValue;
                    }
                    result.put(ResponseFields.PROMOTION_DETAIL, value);
                } catch (Exception e) {
                    logger.warn("failed to transform promotion_detail {}", e.getMessage());
                }
            }
        }
        if(result != null && result.containsKey(ResponseFields.REFUND_DETAILS)){
            String refundDetail = (String) result.get(ResponseFields.REFUND_DETAILS);
            if(!StringUtils.isEmpty(refundDetail)){
                try {
                    Object value = JsonUtil.jsonStringToObject(refundDetail, Object.class);
                    result.put(ResponseFields.REFUND_DETAILS, value);
                } catch (Exception e) {
                    logger.warn("failed to transform refund_details {}", e.getMessage());
                }
            }
        }
        return result;
    }



    public static String getNonceStr(){
        return ThreadLocalRandom.current().nextLong() + "";
    }
    private static String generateTradeNo() {
        Random random = new Random();
        return String.format("%d%d", System.currentTimeMillis(), random.nextInt(10000));
    }

    public static void main(String[] args) throws Exception {
//        testNuccMicroPay();
//        testMicroPay();
//        testOrderQuery();
//        testReverse();
//        testRefund();
//        testRefundQuery();
//        testUnifiedOrder();
//        testCloseOrder();

//        testDepositQuery();
//        testDepositRefundQuery();
//        testUnifiedOrderAdult();
//        testLakalaQuery();
        testTlQuery();
    }
    
    private static void testTlQuery() throws MpayApiNetworkError, MpayException {
        
        WeixinClient client = new WeixinClient();
        Map<String, Object> request = XmlUtils.parse("<xml><channel_name>上海收钱吧互联网科技股份有限公司</channel_name><nonce_str>-1601859096044288701</nonce_str><out_trade_no>7895033093543622</out_trade_no><sign>eb3F7pJNlRyw5jQ9R0opk8JBijPGQHUYV4J9Ojet/JQCHU+ob0SZuyr12ICEaEH8gzCwcMYavMThljDk1QcEMNzTPSPoNroRQYEYOb6Uq1pI0gxGfNZkbE1766uDn5exJ0BhLaAv1hG6LEXRSO0IjTAdFRJrVvWVcekCL0RicuKua2jh7tsKzvInLeGPueuaOnYeNloyeVtprjEpag7FJsryTKI7dYMFHfFUkVYgTKwZc7HoQ0kvnPJXlqQx+4k4setj+MFzKrKkpaMXBld3K7RekYSokssr7Zr5Mll95LFpuTeIW/8uZS+8EOeGgD5l7A+lh9sAih4bsYu8/pSC0g==</sign><merchant_name>老爹炒饭杭州高教路店</merchant_name><terminal_info>{\"device_type\":\"11\",\"device_id\":\"ST00CujN\",\"device_ip\":\"**************\"}</terminal_info><area_info>330106</area_info><sub_appid>wx72534f3638c59073</sub_appid><sub_mch_id>540296461</sub_mch_id><channel_id>313848752</channel_id><version>1.0</version><orgid>7895</orgid></xml>");
        Map<String,Object> result = client.call("https://isv-api.allinpay.com/trxapi/wxtrx/wx_orderqry", SIGN_TYPE_UNIONPAY, "", null, request);
        System.out.println(result);


    }

    private static void testLakalaQuery() throws MpayApiNetworkError, MpayException {
        
        WeixinClient client = new WeixinClient();
        Map<String, Object> request = XmlUtils.parse("<xml><channel_name>上海收钱吧互联网科技股份有限公司</channel_name><time_expire>20230505185936</time_expire><nonce_str>505491425815441754</nonce_str><sign>QFRlYvFId/YGeazKNFNRFjLqJF75NFIxtY/Wwq31+nHuwbQ7XW7SkF/PbyqeuaknK29hGKu1eV3g0RLVRWBROA==</sign><cert_id>4528112827</cert_id><merchant_name>上海市浦东新区周浦镇予欣食品店</merchant_name><terminal_info>{\"device_type\":\"11\",\"device_id\":\"B6637724\",\"location\":\"+31.236115/+121.524028\",\"device_ip\":\"***************\"}</terminal_info><fee_type>CNY</fee_type><sub_appid>wx72534f3638c59073</sub_appid><mch_id>**********</mch_id><sub_mch_id>*********</sub_mch_id><body>家家乐乳山店</body><version>1.0</version><spbill_create_ip>127.0.0.1</spbill_create_ip><auth_code>132537342629051671</auth_code><out_trade_no>****************</out_trade_no><goods_tag>shanghai</goods_tag><appid>wxd23604aba7ed0487</appid><total_fee>320</total_fee><area_info>310115</area_info><attach>bank_mch_name=上海市浦东新区周浦镇予欣食品店&amp;bank_mch_id=*************</attach><channel_id>********</channel_id><sign_type>SM2</sign_type></xml>");
        Map<String,Object> result = client.call("https://tpay2.95516.com/wx/v1/pay/order/qry", SIGN_TYPE_SM2, "", null, request);
        System.out.println(result);


    }

    public static void testDepositQuery() throws MpayException, MpayApiNetworkError {
        WeixinClient client = new WeixinClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, "wx42f6886cbbb3fdbc");
        builder.set(ProtocolFields.SUB_APP_ID, "wx2d47ac8235990230");
        builder.set(ProtocolFields.MCH_ID, "**********");
        builder.set(ProtocolFields.SUB_MCH_ID, "**********");
        builder.set(BusinessFields.OUT_TRADE_NO, "****************");
        builder.set(BusinessFields.SIGN_TYPE, "HMAC-SHA256");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call("https://api.mch.weixin.qq.com/deposit/orderquery", SIGN_TYPE_HMAC_SHA256,"***", null, request);
        System.out.println(result);
    }
    
    public static void testDepositRefundQuery() throws MpayException, MpayApiNetworkError {
        WeixinClient client = new WeixinClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, "wx42f6886cbbb3fdbc");
        builder.set(ProtocolFields.SUB_APP_ID, "wx2d47ac8235990230");
        builder.set(ProtocolFields.MCH_ID, "**********");
        builder.set(ProtocolFields.SUB_MCH_ID, "**********");
        builder.set(BusinessFields.OUT_REFUND_NO, "7893259243055063");
        builder.set(BusinessFields.SIGN_TYPE, "HMAC-SHA256");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call("https://api.mch.weixin.qq.com/deposit/refundquery", SIGN_TYPE_HMAC_SHA256,"***", null, request);
        System.out.println(result);
    }

    public static void testMicroPay() throws MpayException, MpayApiNetworkError {
        WeixinClient client = new WeixinClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
        builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
        builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
        builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
        builder.set(BusinessFields.BODY, "ipad");
        builder.set(BusinessFields.DETAIL, "ipad <32G");
        builder.set(BusinessFields.ATTACH, "no");
        builder.set(BusinessFields.OUT_TRADE_NO, generateTradeNo());
        builder.set(BusinessFields.TOTAL_FEE, "5");
        builder.set(BusinessFields.FEE_TYPE, WeixinConstants.FEE_TYPE_CNY);
        builder.set(BusinessFields.SPBILL_CREATE_IP, "***************");
        builder.set(BusinessFields.GOODS_TAG, "huanan");
        builder.set(BusinessFields.AUTH_CODE, "130834011446666609");


        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(WeixinConfig.MICRO_PAY, WeixinConfig.SQB_APP_SECRET, getSSLContext(), request);
    }

    public static void testNuccMicroPay() throws MpayException, MpayApiNetworkError {
        String key = ("MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDNoT4bYV6XTVo+\n" +
                "ghU80kH6dPcGkOfjs+aRyfXIGUMghNcSvuWK8jkNqC7+mEs+fjFOeRGfAoB5R9OY\n" +
                "ryvPhxM0BHFXhJ0884yvitwVYVT0YrKiYq8Rb1tN5KHtRl5NTKonOtWPWryoBz51\n" +
                "qzTZFQU0v2mKCrB8fGClR429vVSxPs7OERCWV4BuiTFW2+o1R7IfFX0+MB9tKgWK\n" +
                "VOuXK3gpP2jOkqTyPqJl+NumK/S7QE7gtcUmh+4wuDKUbxAGfPSOrEQMknnRujzi\n" +
                "NX8w9tV9hGIrhqERaVw0lxYvdifqoWlHBfnZagVqoSMgfJVMr60XGSCWIdZvog7u\n" +
                "07K2jiApAgMBAAECggEAadtaJ6pGkclmbct1t2veP1s7WAv89IHGbsLzXmFy5yi7\n" +
                "t5DChncP2/H6z2IDSlaYd3doFN2q2cSaL33uJdW5kwu+dXl1oM0YXb40cmU64Nx2\n" +
                "bZYz3dvfuwe6PHX/UffnmG53RmdFu5KPGvBaMm7Z0T/AyymZlVxKPIvLciQnl157\n" +
                "tFHCz5ocGgoLb0khlp8j/3P2VOOk6pJbMmwx38PtQp25OBzxotl42mRoF8gHNBXH\n" +
                "lpYbcjZLmXE/7TX/YvBd0+VA0C/o1lYSto5vhr0cx7l37E1RN1W7ZIuSokmh/LIc\n" +
                "xvFxlBTXzws7LTUz/4X1/7oCME5LDcn3jGskw7iDoQKBgQDrm9bviWuBAey69W9T\n" +
                "gpP9eum+/hUsWGBE55/JtA4FTFq15T1EO9dMWrvX6M6PnLiAJpI8ZAFF+MdBqQUm\n" +
                "EyxKIILjg+dMRwf6aoddqHt3MOXVfyZxLy+LeVYHMlgHo5SqUfDZM/LPk0ARF8TJ\n" +
                "TdpTG/*******************************/K5DZg3mn71uwcnCB5nwdu2rhWC\n" +
                "/FcZ3+oZ7cLnWFAJrloirMo8kk9tAoIx6cM+ZWy1PccD5qYsHBQ+xX1uJpjtyVmj\n" +
                "78mk3tekAqp69wf1c7lRDj46Fsci8ivE6dXqn81JwLbAS0anAa/PbbNMBietHtCA\n" +
                "gomjHPksvQKBgC1re4HuAfV6w4I/MljtAU6KVWlmXfqQhu6BoIIn3dQTpiEOskLn\n" +
                "Llgf3bp/vOJemgrKZMKTnNM6ZF86EC4I4C7iGZl2oi5IOzeBNdtOUY7mtEf2HRkQ\n" +
                "uZ+vom/8uo+ub0huR3n308VEY8Nny53rDj2bVb1r7rSx8yIkidXMC8tZAoGBANoB\n" +
                "haHiLLlKPYuL0XPeUWVRc7GUI9nFVTIhHLAfnyI7r+Did/5qRILvDMo7jQp11yny\n" +
                "yVK5zm+uXuE8jkod/9ccBn6TZMcon7HpiFy6H7ll7IjdP8PNbjZb6nXtwdMkb3bN\n" +
                "H7C2yq27P9az3LWaXLzOcpOsscwndBTgyoIBBFUVAoGAWT2kZbf9N7Hk5+nttbsI\n" +
                "OREa8wgrL9ins9HO+eaFZ10o7VF4q9JBLRZxA4YQ8fDWv+hl8lhJs8tOB8qPMz/W\n" +
                "lQWA6EWrmi/ddc0tLgqbh0ovGtYdio55hxaUtjtkjPP2ubvRgqh9QtEy2JsEDgpw\n" +
                "aVnpxZtZdtxGfiTlkjj/XJ4=").replaceAll("\n", "");
        
        WeixinClient client = new WeixinClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, "wx2421b1c4370ec43b");
//        builder.set(ProtocolFields.SUB_APP_ID, "");
        builder.set(ProtocolFields.MCH_ID, "1900008751");
        builder.set(ProtocolFields.SUB_MCH_ID, "216358764");
        builder.set(BusinessFields.BODY, "ipad");
//        builder.set(BusinessFields.DETAIL, "ipad <32G");
        builder.set(BusinessFields.ATTACH, "no");
        builder.set(BusinessFields.OUT_TRADE_NO, generateTradeNo());
        builder.set(BusinessFields.TOTAL_FEE, "1");
        builder.set(BusinessFields.FEE_TYPE, WeixinConstants.FEE_TYPE_CNY);
        builder.set(BusinessFields.SPBILL_CREATE_IP, "***************");
        builder.set(BusinessFields.GOODS_TAG, "huanan");
        builder.set(BusinessFields.AUTH_CODE, "135258405348924098");
        builder.set("channel_id", "24006513");
        builder.set("idc_flag", "77");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call("https://**************:9443/gateway/wechat/micropay", SIGN_TYPE_NUCC, key, getNUCCSSLContext(), request);
    }

    public static void testOrderQuery() throws MpayApiNetworkError, MpayException {
        WeixinClient client = new WeixinClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
        builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
        builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
        builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
        builder.set(BusinessFields.OUT_TRADE_NO, "14488687438215665");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(WeixinConfig.QUERY, WeixinConfig.SQB_APP_SECRET, getSSLContext(), request);
    }

    public static void testReverse() throws MpayApiNetworkError, MpayException {
        WeixinClient client = new WeixinClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
        builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
        builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
        builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
        builder.set(BusinessFields.OUT_TRADE_NO, "14488687438215665");
//        builder.set(BusinessFields.TRANSACTION_ID, "1003261016201511161631043695");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(WeixinConfig.REVERSE, WeixinConfig.SQB_APP_SECRET, getSSLContext(), request);
    }

    public static void testRefund() throws MpayApiNetworkError, MpayException {
        WeixinClient client = new WeixinClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
        builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
        builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
        builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
        builder.set(ProtocolFields.DEVICE_INFO, "test");
//        builder.set(BusinessFields.TRANSACTION_ID, "1003261016201511131585982894111111");
        builder.set(BusinessFields.OUT_TRADE_NO, "7894259244096921");
        builder.set(BusinessFields.OUT_REFUND_NO, "7894259244096928");
        builder.set(BusinessFields.TOTAL_FEE, "1");
        builder.set(BusinessFields.REFUND_FEE, "1");
        builder.set(BusinessFields.REFUND_FEE_TYPE, WeixinConstants.FEE_TYPE_CNY);
        builder.set(BusinessFields.OP_USER_ID, "wjw");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(WeixinConfig.REFUND, WeixinConfig.SQB_APP_SECRET, getSSLContext(), request);
    }

    public static void testRefundQuery() throws MpayApiNetworkError, MpayException {
        WeixinClient client = new WeixinClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
        builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
        builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
        builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
        builder.set(ProtocolFields.DEVICE_INFO, "test");
//        builder.set(BusinessFields.TRANSACTION_ID, "10032610162015111616319655301");
//        builder.set(BusinessFields.OUT_TRADE_NO, "14476784649346631111");
        builder.set(BusinessFields.OUT_REFUND_NO, "14488687438215665");
//        builder.set(BusinessFields.REFUND_ID, "2003261016201511160078434341");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(WeixinConfig.REFUND_QUERY, WeixinConfig.SQB_APP_SECRET, getSSLContext(), request);
    }

    public static void testUnifiedOrder() throws MpayApiNetworkError, MpayException {
        WeixinClient client = new WeixinClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
        builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
        builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
        builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
        builder.set(ProtocolFields.DEVICE_INFO, "devicetest");
        builder.set(BusinessFields.BODY, "ipad");
        builder.set(BusinessFields.DETAIL, "ipad <32G");
        builder.set(BusinessFields.ATTACH, "no");
        builder.set(BusinessFields.OUT_TRADE_NO, generateTradeNo());
        builder.set(BusinessFields.TOTAL_FEE, "3");
        builder.set(BusinessFields.FEE_TYPE, WeixinConstants.FEE_TYPE_CNY);
        builder.set(BusinessFields.SPBILL_CREATE_IP, "***************");
//        builder.set(BusinessFields.TIME_START, WeixinConstants.DATE_SDF.format(new Date()));
//        builder.set(BusinessFields.TIME_EXPIRE, WeixinConstants.DATE_SDF.format(new Date(System.currentTimeMillis() + 1000 * 60 * 1)));
        builder.set(BusinessFields.NOTIFY_URL, WeixinConfig.NOTIFY_URL);
        builder.set(BusinessFields.TRADE_TYPE, WeixinConstants.TRADE_TYPE_NATIVE);
        builder.set(BusinessFields.PRODUCT_ID, "product1");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(WeixinConfig.UNIFIED_ORDER, WeixinConfig.SQB_APP_SECRET, getSSLContext(), request);
    }

    public static void testUnifiedOrderAdult() throws MpayApiNetworkError, MpayException {
        WeixinClient client = new WeixinClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, "wxccbcac9a3ece5112");
        builder.set(ProtocolFields.SUB_APP_ID, "");
        builder.set(ProtocolFields.MCH_ID, "**********");
        builder.set(ProtocolFields.SUB_MCH_ID, "1608493105");
        builder.set(ProtocolFields.DEVICE_INFO, "devicetest");
        builder.set(BusinessFields.BODY, "ipad");
        builder.set(BusinessFields.DETAIL, "ipad <32G");
        builder.set(BusinessFields.ATTACH, "no");
        builder.set(BusinessFields.OUT_TRADE_NO, generateTradeNo());
        builder.set(BusinessFields.TOTAL_FEE, "3");
        builder.set(BusinessFields.FEE_TYPE, WeixinConstants.FEE_TYPE_CNY);
        builder.set(BusinessFields.SPBILL_CREATE_IP, "***************");
//        builder.set(BusinessFields.TIME_START, WeixinConstants.DATE_SDF.format(new Date()));
        builder.set(BusinessFields.TIME_EXPIRE, "20230202183010");
        builder.set(BusinessFields.NOTIFY_URL, WeixinConfig.NOTIFY_URL);
        builder.set(BusinessFields.TRADE_TYPE, WeixinConstants.TRADE_TYPE_JSAPI);
        builder.set(BusinessFields.OPEN_ID, "ofDgL0YUiUrHcC4Xz9stSYHhhKGM");
        builder.set(BusinessFields.LIMIT_PAYER, WeixinConstants.ADULT);
        //<xml><time_expire>20220720114158</time_expire><nonce_str>2164831318286529979</nonce_str><openid>oyBevt8X3PYzcziFx0RZmsKumOBg</openid><sign>6D5097FE3BD0C8412C6D7D29A125E3AD</sign><fee_type>CNY</fee_type><sub_appid/><mch_id>**********</mch_id><sub_mch_id>1541791611</sub_mch_id><body>cgb-test</body><notify_url>https://e9b9af6332fa.ngrok.io/upay/v2/notify/weixinwap/c217783bc7b35a2a3dca3b072a80882a/60c47%267894355721339972%263%26st-1580000000263129%263b238fa1d2b2-d80a-5224-4f02-9abb71c2%263%261658288276492%261%260%260</notify_url><version>1.0</version><spbill_create_ip>127.0.0.1</spbill_create_ip><limit_payer>ADULT</limit_payer><out_trade_no>7894355721339972</out_trade_no><appid>wx42f6886cbbb3fdbc</appid><total_fee>1</total_fee><trade_type>JSAPI</trade_type><detail>candy</detail><scene_info>{\"store_info\":{\"id\":\"1232-13\"}}</scene_info></xml>
//        builder.set(BusinessFields.PRODUCT_ID, "product1");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(WeixinConfig.UNIFIED_ORDER, "20f64942ff81bbbb6506eb1a9b3d877e", getSSLContext(), request);
        System.out.println(result);
    }

    public static void testCloseOrder() throws MpayApiNetworkError, MpayException {
        WeixinClient client = new WeixinClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
        builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
        builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
        builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
        builder.set(BusinessFields.OUT_TRADE_NO, "14476815689034366");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(WeixinConfig.CLOSE_ORDER, WeixinConfig.SQB_APP_SECRET, getSSLContext(), request);
    }


    public static void testDownloadBill() throws Exception{
        WeixinClient client = new WeixinClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, "wx42f6886cbbb3fdbc");
        builder.set(ProtocolFields.MCH_ID, "**********");
        builder.set(BusinessFields.BILL_DATE, "20170315");
        builder.set(BusinessFields.BILL_TYPE, "ALL");
        Map<String,Object> request = builder.build();

        request.put(ProtocolFields.NONCE_STR, "XXX");
        for(Object mapKey: request.keySet().toArray()){
            if(request.get(mapKey) == null){
                request.remove(mapKey);
            }
        }
        request.remove(ProtocolFields.SIGN);
        request.put(ProtocolFields.SIGN, WeixinSignature.getSign(request, "7966737e09610cfef18b9372522eb79d", WeixinConstants.CHARSET_UTF8));
        String requestXml = XmlUtils.map2XmlString(request, "xml");
        logger.debug("request {}", requestXml);
        String response = WebUtils.doPost(null, null, "https://api.mch.weixin.qq.com/sandboxnew/pay/downloadbill", "text/xml", requestXml.getBytes(), 5000, 5000);
        System.out.println(response);
    }

    public static void getSandboxSignKey() throws Exception{
        String url = "https://api.mch.weixin.qq.com/sandboxnew/pay/getsignkey";
        Map<String,Object> request = new HashMap<String, Object>();
        request.put("mch_id", "**********");
        request.put("nonce_str", "werwe");
        Map<String,Object> result = new WeixinClient().call(url, WeixinConfig.SQB_APP_KEY, null, request);
        System.out.println(result);
    }

}
