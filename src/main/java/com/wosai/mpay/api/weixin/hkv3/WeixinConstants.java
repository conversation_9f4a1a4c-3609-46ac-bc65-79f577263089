package com.wosai.mpay.api.weixin.hkv3;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class WeixinConstants {
    /** 微信签名字段 **/
    public static final String APP_KEY = "app_key";



    /** UTF-8字符集 **/
    public static final String CHARSET_UTF8     = "UTF-8";


    public static String DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ssXXX";

    /** sign type **/
    public static String SIGN_TYPE_MD5 = "MD5";

    /** FEE_TYPE 货币类型**/
    public static final String FEE_TYPE_CNY = "CNY"; //人民币

    /** 交易类型 **/
    public  static final String TRADE_TYPE_JSAPI = "JSAPI";//公众号支付
    public  static final String TRADE_TYPE_NATIVE = "NATIVE";//原生扫码支付
    public  static final String TRADE_TYPE_APP = "APP";//app支付
    public  static final String TRADE_TYPE_MICROPAY = "MICROPAY";//刷卡支付


    /** 交易状态 **/
    public static final String TRADE_STATE_SUCCESS = "SUCCESS";//支付成功
    public static final String TRADE_STATE_REFUND = "REFUND";//转入退款
    public static final String TRADE_STATE_NOTPAY = "NOTPAY";//未支付
    public static final String TRADE_STATE_CLOSED = "CLOSED";//已关闭
    public static final String TRADE_STATE_REVOKED = "REVOKED";//已撤销（刷卡支付）
    public static final String TRADE_STATE_USERPAYING = "USERPAYING";//用户支付中
    public static final String TRADE_STATE_PAYERROR = "PAYERROR";//支付失败
    public static final String TRADE_STATE_PROCESSING = "PROCESSING";//退款处理中


    /** 通信结果返回 **/
    public static final String RETURN_CODE_SUCCESS = "SUCCESS";
    public static final String RETURN_CODE_FAIL = "FAIL";

    /** 业务结果返回**/
    public static final String RESULT_CODE_SUCCESS = "SUCCESS";
    public static final String RESULT_CODE_FAIL = "FAIL";


    public static final String CODE_SYSTEM_ERROR = "SYSTEMERROR";                           // 接口返回错误
    public static final String CODE_INVALID_REQUEST = "INVALID_REQUEST";                    // 无效请求
    public static final String CODE_REVERSE_EXPIRE = "REVERSE_EXPIRE";                      // 订单无法撤销
    public static final String CODE_FREQUENCY_LIMITED = "FREQUENCY_LIMITED";                // 频率限制
    public static final String CODE_ORDERPAID = "ORDERPAID";                                // 订单已支付
    public static final String CODE_AUTH_CODE_INVALID = "AUTH_CODE_INVALID";                // 付款码无效
    public static final String CODE_NOTENOUGH = "NOTENOUGH";                                // 余额不足
    public static final String CODE_RESOURCE_NOT_EXISTS = "RESOURCE_NOT_EXISTS";            // 单号不存在

    //接口返回错误列表 刷卡支付 支付结果失败
    public static final List<String> MICRO_PAY_RESULT_CODE_FAIL_LIST = Arrays.asList(
            CODE_NOTENOUGH, CODE_AUTH_CODE_INVALID, CODE_INVALID_REQUEST
    );


    //接口返回错误列表，刷卡支付 支付结果未知
    public static final List<String> MICRO_PAY_RESULT_ERROR_CODE_UNKONW_LIST = Arrays.asList(
            CODE_SYSTEM_ERROR
    );

    //接口返回错误列表  协议错误
    public static final List<String> RESULT_ERROR_CODE_PROTOCAL_FAIL_LIST = Arrays.asList(
            CODE_SYSTEM_ERROR
    );

    public static final int HTTP_CODE_CANCEL_SUCCESS = 204; // 退款成功http返回码



    public static final String NO_CREDIT = "no_credit";

    /**
     * 微信pay接口所允许的字段
     */
    public static final Set<String> PAY_ALLOWED_FIELDS = new HashSet<String>(Arrays.asList(
            "mchid", "appid","sub_mchid","sp_mchid","sp_appid","sub_appid","description","attach","out_trade_no","goods_tag","trade_type","limit_pay","merchant_category_code","payer","amount","scene_info","detail"
    ));

    /**
     * 微信precreate接口所允许的字段
     */
    public static final Set<String> PRECREATE_ALLOWED_FIELDS = new HashSet<String>(Arrays.asList(
            "mchid", "appid","sub_mchid","sp_mchid","sp_appid","sub_appid","description","attach","notify_url","out_trade_no","goods_tag","trade_type","limit_pay","time_start","time_expire","merchant_category_code","payer","amount","scene_info","detail"
    ));

}
