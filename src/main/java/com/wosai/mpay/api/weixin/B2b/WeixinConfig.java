package com.wosai.mpay.api.weixin.B2b;

/***
 * @ClassName: WeixinB2BConfig
 * @Description:
 * @Auther: dabuff
 * @Date: 2025/1/13 15:51
 */
public class WeixinConfig {

    public static final String QUERY = "https://api.weixin.qq.com/retail/B2b/getorder?access_token=%s&pay_sig=%s";
    public static final String REFUND = "https://api.weixin.qq.com/retail/B2b/refund?access_token=%s&pay_sig=%s";
    public static final String REFUND_QUERY = "https://api.weixin.qq.com/retail/B2b/getrefund?access_token=%s&pay_sig=%s";

    /** 支付异步通知地址 **/
    public static final String NOTIFY_URL = "https://shouqianba.com";

    public static final String CERT_PASSWORD = "";

    public static final String SQB_APP_ID = "wx42f6886cbbb3fdbc";
    public static final String SQB_SUB_APP_ID = "wx0fd179d3b11b7b34";
    public static final String SQB_MCH_ID = "1238313502";
    public static final String SQB_SUB_MCH_ID = "1247362501";
    public static final String SQB_APP_KEY = "";
    public static final String SQB_APP_SECRET = "";

}
