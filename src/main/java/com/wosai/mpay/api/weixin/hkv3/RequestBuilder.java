package com.wosai.mpay.api.weixin.hkv3;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by jian<PERSON> on 13/11/15.
 */
public class RequestBuilder {
    private Map<String,Object> request;

    public RequestBuilder(){
        request = new HashMap<String, Object>();
    }

    public void set(String field, Object value) {
        request.put(field,  value);
    }

    public Map<String,Object> build(){
        return request;
    }
}
