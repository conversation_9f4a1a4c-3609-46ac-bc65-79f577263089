package com.wosai.mpay.api.weixin.B2b;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HmacSignature;
import com.wosai.mpay.util.JsonUtil;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.net.MalformedURLException;
import java.security.Security;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/***
 * @ClassName: WeixinB2BTest
 * @Description:
 * @Auther: dabuff
 * @Date: 2025/1/13 13:59
 */
public class WeixinB2BTest {

    public static void main(String[] args) throws MpayException {

        Security.addProvider(new BouncyCastleProvider());

        Map<String, Object> request = new HashMap<>();
        Map<String, Object> signData = new HashMap<>();
        signData.put("mchid", "**********");
        signData.put("out_trade_no", "2025021200001");
        signData.put("description", "测试创建weixinB2b交易");
        signData.put("amount", new HashMap<String, Object>(){{
            put("order_amount", 1);
        }});
        signData.put("env", 0);
        String signDataStr = JsonUtil.objectToJsonString(signData);

        request.put("signData", signDataStr);
        request.put("mode", "retail_pay_goods");



        String data = HmacSignature.sign(WeixinConstants.COMMON_PAYMENT_URI + "&" + signDataStr, WeixinConstants.SIGN_TYPE_HMAC_SHA256, "", false);
        request.put(BusinessFields.PAY_SIG, data);

        request.put("signature", HmacSignature.sign(signDataStr, WeixinConstants.SIGN_TYPE_HMAC_SHA256, "", false));

        System.out.println(request);
    }

    public static void testB2BPay() throws MpayException {
        WeixinB2bClient client = new WeixinB2bClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.MCHID, WeixinConfig.SQB_MCH_ID);
        builder.set(BusinessFields.OUT_TRADE_NO, generateTradeNo());
        builder.set(BusinessFields.DESCRIPTION, "description");
        builder.set(BusinessFields.DESCRIPTION, "description");
        Map<String, Object> amount = new HashMap<>();
        amount.put(BusinessFields.ORDER_AMOUNT, 1);
        builder.set(BusinessFields.AMOUNT, amount);
        builder.set(BusinessFields.ENV, 1);
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.buildSignData(WeixinConstants.COMMON_PAYMENT_URI,
                WeixinConstants.SIGN_TYPE_HMAC_SHA256, WeixinConfig.SQB_APP_KEY, request);
    }

    public static void testQuery() throws MpayApiNetworkError, MpayException, MalformedURLException {
        WeixinB2bClient client = new WeixinB2bClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.MCHID, WeixinConfig.SQB_MCH_ID);
        builder.set(BusinessFields.OUT_TRADE_NO, "14488687438215665");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(WeixinConfig.QUERY,
                WeixinConstants.SIGN_TYPE_HMAC_SHA256, WeixinConfig.SQB_APP_KEY, null, request, "");
    }

    public static void testRefund() throws MpayApiNetworkError, MpayException, MalformedURLException {
        WeixinB2bClient client = new WeixinB2bClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.MCHID, WeixinConfig.SQB_MCH_ID);
        builder.set(BusinessFields.OUT_TRADE_NO, "14488687438215665");
        builder.set(BusinessFields.OUT_REFUND_NO, "7894259244096928");
        builder.set(BusinessFields.REFUND_AMOUNT, "1");
        builder.set(BusinessFields.REFUND_FROM, "3");
        builder.set(BusinessFields.REFUND_REASON, "3");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(WeixinConfig.REFUND,
                WeixinConstants.SIGN_TYPE_HMAC_SHA256, WeixinConfig.SQB_APP_KEY, null, request, "");
    }

    private static String generateTradeNo() {
        Random random = new Random();
        return String.format("%d%d", System.currentTimeMillis(), random.nextInt(10000));
    }
}
