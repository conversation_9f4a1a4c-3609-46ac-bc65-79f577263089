package com.wosai.mpay.api.weixin.hk;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.security.KeyStore;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.*;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;

/**
 * Created by jianfree on 12/11/15.
 */
public class WeixinHKClient {
    public  static final Logger logger = LoggerFactory.getLogger(WeixinHKClient.class);
    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    private ObjectMapper objectMapper = new ObjectMapper();



    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public SSLContext getSSLContext(byte [] certData, String password){
        InputStream inputStream = new ByteArrayInputStream(certData);
        SSLContext sslContext = null;
        try {
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(inputStream, password.toCharArray());//设置证书密码
            sslContext = SSLContext.getInstance("TLS");
            KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
            kmf.init(keyStore, password.toCharArray());
            sslContext.init(kmf.getKeyManagers(), null, new SecureRandom());
        } catch (Exception e) {
            logger.error("getSSLContext error: {}", e.getMessage(), e);
        }
        return sslContext;
    }


    public static  SSLContext  getSSLContext(){
        InputStream instream = null;
        try {
            //加载本地的证书进行https加密传输
            String certPath = "/Users/<USER>/Documents/wosai-work/cert/apiclient_cert.p12";//微信证书路径
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            File file = new File(certPath);
            if(file.exists()){
                instream = new FileInputStream(file);
            }else{
                instream = WeixinHKClient.class.getResourceAsStream(certPath);
            }
            keyStore.load(instream, WeixinConfig.CERT_PASSWORD.toCharArray());//设置证书密码
            SSLContext sslContext = SSLContext.getInstance("TLS");
            KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
            kmf.init(keyStore, WeixinConfig.CERT_PASSWORD.toCharArray());
            sslContext.init(kmf.getKeyManagers(), null, new SecureRandom());
            return sslContext;
        } catch (Exception e) {
           logger.error("getSSLContext error: {}", e.getMessage(), e);
        } finally {
            if(instream != null){
                try {
                    instream.close();
                } catch (IOException e) {
                }
            }
        }
        return null;
    }

    public Map<String,Object> call(String serviceUrl,String signKey, SSLContext sslContext,  Map<String,Object> request) throws MpayException, MpayApiNetworkError {
        request.put(ProtocolFields.NONCE_STR, getNonceStr());
        for(Object mapKey: request.keySet().toArray()){
            if(request.get(mapKey) == null){
                request.remove(mapKey);
            }
        }
        request.remove(ProtocolFields.SIGN);
        request.put(ProtocolFields.SIGN_TYPE, "SHA256");
        request.put(ProtocolFields.SIGN, WeixinSignature.getSHA256Sign(request, signKey, WeixinConstants.CHARSET_UTF8));
        String requestXml = XmlUtils.map2XmlString(request, "xml");
        logger.debug("request {}", requestXml);
//        String response = WebUtils.doPost(sslContext, null, serviceUrl, "text/xml",getBytes(requestXml, WeixinConstants.CHARSET_UTF8), connectTimeout, readTimeout);
        String response = HttpClientUtils.doPost(WeixinHKClient.class.getName(), sslContext, null, serviceUrl, "text/xml", requestXml, WeixinConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        logger.debug("response {}",response.replaceAll("\\n", ""));//微信返回的xml报文，有换行，打印日志时，打印在一行上面。
        Map<String, Object> result = XmlUtils.parse(response);
        if(result != null && result.containsKey(ResponseFields.PROMOTION_DETAIL)){
            String promotion = (String) result.get(ResponseFields.PROMOTION_DETAIL);
            if(!StringUtils.isEmpty(promotion)){
                try {
                    result.put(ResponseFields.PROMOTION_DETAIL, objectMapper.readValue(promotion, Object.class));
                } catch (IOException e) {
                    logger.warn("failed to transform promotion_detail {}", e.getMessage());
                }
            }
        }
        return result;
    }

    private byte[] getBytes(String string , String charset) throws MpayException {
        if(string == null){
            return null;
        }else {
            try {
                return string.getBytes(charset);
            } catch (UnsupportedEncodingException e) {
                throw new MpayException("UnsupportedEncodingException :", e);
            }
        }
    }

    private String getNonceStr(){
        return new Random().nextLong() + "";
    }
    private static String generateTradeNo() {
        Random random = new Random();
        return String.format("%d%d", System.currentTimeMillis(), random.nextInt(10000));
    }

    public static void main(String[] args) throws Exception {
//        testMicroPay();
//        testOrderQuery();
//        testReverse();
//        testRefund();
//        testRefundQuery();
//        testUnifiedOrder();
//        testCloseOrder();

        testDownloadBill();

    }

    public static void testMicroPay() throws MpayException, MpayApiNetworkError {
        WeixinHKClient client = new WeixinHKClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
        builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
        builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
        builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
        builder.set(BusinessFields.BODY, "ipad");
        builder.set(BusinessFields.DETAIL, "ipad <32G");
        builder.set(BusinessFields.ATTACH, "no");
        builder.set(BusinessFields.OUT_TRADE_NO, generateTradeNo());
        builder.set(BusinessFields.TOTAL_FEE, "5");
        builder.set(BusinessFields.FEE_TYPE, WeixinConstants.FEE_TYPE_CNY);
        builder.set(BusinessFields.SPBILL_CREATE_IP, "***************");
        builder.set(BusinessFields.GOODS_TAG, "huanan");
        builder.set(BusinessFields.AUTH_CODE, "130834011446666609");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(WeixinConfig.MICRO_PAY, WeixinConfig.SQB_APP_SECRET, getSSLContext(), request);
    }

    public static void testOrderQuery() throws MpayApiNetworkError, MpayException {
        WeixinHKClient client = new WeixinHKClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
        builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
        builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
        builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
        builder.set(BusinessFields.OUT_TRADE_NO, "14488687438215665");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(WeixinConfig.QUERY, WeixinConfig.SQB_APP_SECRET, getSSLContext(), request);
    }

    public static void testReverse() throws MpayApiNetworkError, MpayException {
        WeixinHKClient client = new WeixinHKClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
        builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
        builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
        builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
        builder.set(BusinessFields.OUT_TRADE_NO, "14488687438215665");
//        builder.set(BusinessFields.TRANSACTION_ID, "1003261016201511161631043695");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(WeixinConfig.REVERSE, WeixinConfig.SQB_APP_SECRET, getSSLContext(), request);
    }

    public static void testRefund() throws MpayApiNetworkError, MpayException {
        WeixinHKClient client = new WeixinHKClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
        builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
        builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
        builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
        builder.set(ProtocolFields.DEVICE_INFO, "test");
//        builder.set(BusinessFields.TRANSACTION_ID, "1003261016201511131585982894111111");
        builder.set(BusinessFields.OUT_TRADE_NO, "7894259244096921");
        builder.set(BusinessFields.OUT_REFUND_NO, "7894259244096928");
        builder.set(BusinessFields.TOTAL_FEE, "1");
        builder.set(BusinessFields.REFUND_FEE, "1");
        builder.set(BusinessFields.REFUND_FEE_TYPE, WeixinConstants.FEE_TYPE_CNY);
        builder.set(BusinessFields.OP_USER_ID, "wjw");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(WeixinConfig.REFUND, WeixinConfig.SQB_APP_SECRET, getSSLContext(), request);
    }

    public static void testRefundQuery() throws MpayApiNetworkError, MpayException {
        WeixinHKClient client = new WeixinHKClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
        builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
        builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
        builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
        builder.set(ProtocolFields.DEVICE_INFO, "test");
//        builder.set(BusinessFields.TRANSACTION_ID, "10032610162015111616319655301");
//        builder.set(BusinessFields.OUT_TRADE_NO, "14476784649346631111");
        builder.set(BusinessFields.OUT_REFUND_NO, "14488687438215665");
//        builder.set(BusinessFields.REFUND_ID, "2003261016201511160078434341");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(WeixinConfig.REFUND_QUERY, WeixinConfig.SQB_APP_SECRET, getSSLContext(), request);
    }

    public static void testUnifiedOrder() throws MpayApiNetworkError, MpayException {
        WeixinHKClient client = new WeixinHKClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
        builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
        builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
        builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
        builder.set(ProtocolFields.DEVICE_INFO, "devicetest");
        builder.set(BusinessFields.BODY, "ipad");
        builder.set(BusinessFields.DETAIL, "ipad <32G");
        builder.set(BusinessFields.ATTACH, "no");
        builder.set(BusinessFields.OUT_TRADE_NO, generateTradeNo());
        builder.set(BusinessFields.TOTAL_FEE, "3");
        builder.set(BusinessFields.FEE_TYPE, WeixinConstants.FEE_TYPE_CNY);
        builder.set(BusinessFields.SPBILL_CREATE_IP, "***************");
//        builder.set(BusinessFields.TIME_START, WeixinConstants.DATE_SDF.format(new Date()));
//        builder.set(BusinessFields.TIME_EXPIRE, WeixinConstants.DATE_SDF.format(new Date(System.currentTimeMillis() + 1000 * 60 * 1)));
        builder.set(BusinessFields.NOTIFY_URL, WeixinConfig.NOTIFY_URL);
        builder.set(BusinessFields.TRADE_TYPE, WeixinConstants.TRADE_TYPE_NATIVE);
        builder.set(BusinessFields.PRODUCT_ID, "product1");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(WeixinConfig.UNIFIED_ORDER, WeixinConfig.SQB_APP_SECRET, getSSLContext(), request);
    }

    public static void testCloseOrder() throws MpayApiNetworkError, MpayException {
        WeixinHKClient client = new WeixinHKClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, WeixinConfig.SQB_APP_ID);
        builder.set(ProtocolFields.SUB_APP_ID, WeixinConfig.SQB_SUB_APP_ID);
        builder.set(ProtocolFields.MCH_ID, WeixinConfig.SQB_MCH_ID);
        builder.set(ProtocolFields.SUB_MCH_ID, WeixinConfig.SQB_SUB_MCH_ID);
        builder.set(BusinessFields.OUT_TRADE_NO, "14476815689034366");
        Map<String,Object> request = builder.build();
        Map<String,Object> result = client.call(WeixinConfig.CLOSE_ORDER, WeixinConfig.SQB_APP_SECRET, getSSLContext(), request);
    }


    public static void testDownloadBill() throws Exception{
        WeixinHKClient client = new WeixinHKClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.APP_ID, "wx42f6886cbbb3fdbc");
        builder.set(ProtocolFields.MCH_ID, "1238313502");
        builder.set(BusinessFields.BILL_DATE, "20170315");
        builder.set(BusinessFields.BILL_TYPE, "ALL");
        Map<String,Object> request = builder.build();

        request.put(ProtocolFields.NONCE_STR, "XXX");
        for(Object mapKey: request.keySet().toArray()){
            if(request.get(mapKey) == null){
                request.remove(mapKey);
            }
        }
        request.remove(ProtocolFields.SIGN);
        request.put(ProtocolFields.SIGN, WeixinSignature.getSign(request, "7966737e09610cfef18b9372522eb79d", WeixinConstants.CHARSET_UTF8));
        String requestXml = XmlUtils.map2XmlString(request, "xml");
        logger.debug("request {}", requestXml);
        String response = WebUtils.doPost(null, null, "https://api.mch.weixin.qq.com/sandboxnew/pay/downloadbill", "text/xml", requestXml.getBytes(), 5000, 5000);
        System.out.println(response);
    }

    public static void getSandboxSignKey() throws Exception{
        String url = "https://api.mch.weixin.qq.com/sandboxnew/pay/getsignkey";
        Map<String,Object> request = new HashMap<String, Object>();
        request.put("mch_id", "1238313502");
        request.put("nonce_str", "werwe");
        Map<String,Object> result = new WeixinHKClient().call(url, WeixinConfig.SQB_APP_KEY, null, request);
        System.out.println(result);
    }

}
