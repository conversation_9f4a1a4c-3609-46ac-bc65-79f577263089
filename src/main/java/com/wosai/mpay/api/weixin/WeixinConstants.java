package com.wosai.mpay.api.weixin;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by jianfree on 12/11/15.
 */
public class WeixinConstants {
    /** 微信签名字段 **/
    public static final String APP_KEY = "app_key";

    /** 微信刷脸支付调用凭证接口默认版本号 **/
    public static final String DEFAULT_VERSION = "1";

    /** UTF-8字符集 **/
    public static final String CHARSET_UTF8     = "UTF-8";

    /** 排序后的content **/
    public static final String SORTED_CONTENT = "sorted_content";

    public static String  DATE_TIME_FORMAT = "yyyyMMddHHmmss";

    /** sign type **/
    public static final String SIGN_TYPE_MD5 = "MD5";
    public static final String SIGN_TYPE_HMAC_SHA256 = "HMAC-SHA256";

    public static final String ADULT = "ADULT"; //未成年人交易限制

    /** FEE_TYPE 货币类型**/
    public static final String FEE_TYPE_CNY = "CNY"; //人民币

    /** 交易类型 **/
    public  static final String TRADE_TYPE_JSAPI = "JSAPI";//公众号支付
    public  static final String TRADE_TYPE_NATIVE = "NATIVE";//原生扫码支付
    public  static final String TRADE_TYPE_APP = "APP";//app支付
    public  static final String TRADE_TYPE_MICROPAY = "MICROPAY";//刷卡支付
    public  static final String TRADE_TYPE_H5 = "MWEB";//公众号支付
    public  static final String TRADE_TYPE_PAP = "PAP";//微信委托代扣支付

    /** 刷掌支付取消原因 **/
    public static final String REASON_PAYMENT_RECEIVED = "PAYMENT_RECEIVED";
    public static final String REASON_FREE_CHARGE = "FREE_CHARGE";

    /** 分账标识 **/
    public static final String PROFIT_SHARING_YES = "Y";
    public static final String PROFIT_SHARING_NO = "N";


    /** 交易状态 **/
    public static final String TRADE_STATE_SUCCESS = "SUCCESS";//支付成功
    public static final String TRADE_STATE_REFUND = "REFUND";//转入退款
    public static final String TRADE_STATE_NOTPAY = "NOTPAY";//未支付
    public static final String TRADE_STATE_CLOSED = "CLOSED";//已关闭
    public static final String TRADE_STATE_REVOKED = "REVOKED";//已撤销（刷卡支付）
    public static final String TRADE_STATE_USERPAYING = "USERPAYING";//用户支付中
    public static final String TRADE_STATE_PAYERROR = "PAYERROR";//支付失败
    public static final String TRADE_STATE_CONSUMED = "CONSUMED";//押金消费成功
    public static final String TRADE_STATE_SETTLING = "SETTLING";//押金消费处理中

    public static final String TRADE_STATE_FAIL_DESC = "支付失败，请撤销订单";//支付失败

    /** 通信结果返回 **/
    public static final String RETURN_CODE_SUCCESS = "SUCCESS";
    public static final String RETURN_CODE_FAIL = "FAIL";

    public static final String RETURN_MSG_SYSTEM_ERROR = "SYSTEM ERROR";

    /** 业务结果返回**/
    public static final String RESULT_CODE_SUCCESS = "SUCCESS";
    public static final String RESULT_CODE_FAIL = "FAIL";

    public static final String NO_CREDIT = "no_credit";

    /** 分账回退结果 **/
    public static final String PROFIT_SHARING_RETURN_RESULT_PROCESSING = "PROCESSING";
    public static final String PROFIT_SHARING_RETURN_RESULT_SUCCESS = "SUCCESS";
    public static final String PROFIT_SHARING_RETURN_RESULT_FAIL = "FAIL";

    /** 分账状态 **/
    public static final String SHARING_STATUS_ACCEPTED = "ACCEPTED";
    public static final String SHARING_STATUS_PROCESSING = "PROCESSING";
    public static final String SHARING_STATUS_FINISHED = "FINISHED";
    public static final String SHARING_STATUS_CLOSED = "CLOSED";

    public static final String RESULT_ERROR_CODE_SYSTEM_ERROR = "SYSTEMERROR"; //接口返回错误
    public static final String RESULT_ERROR_CODE_PARAM_ERROR = "PARAM_ERROR"; //参数错误
    public static final String RESULT_ERROR_CODE_ORDER_PAID = "ORDERPAID"; //  订单已支付
    public static final String RESULT_ERROR_CODE_NOAUTH = "NOAUTH"; // 商户无权限
    public static final String RESULT_ERROR_CODE_AUTH_CODE_EXPIRE = "AUTHCODEEXPIRE"; // 二维码已过期，请用户在微信上刷新后再试
    public static final String RESULT_ERROR_CODE_NOT_ENOUGH = "NOTENOUGH"; //  余额不足
    public static final String RESULT_ERROR_CODE_NOT_SUPORTCARD = "NOTSUPORTCARD"; //  不支持卡类型
    public static final String RESULT_ERROR_CODE_ORDER_CLOSED = "ORDERCLOSED"; //订单已关闭
    public static final String RESULT_ERROR_CODE_ORDER_REVERSED = "ORDERREVERSED"; //  订单已撤销
    public static final String RESULT_ERROR_CODE_BANK_ERROR = "BANKERROR"; //  银行系统异常
    public static final String RESULT_ERROR_CODE_USER_PAYING = "USERPAYING"; // 用户支付中，需要输入密码
    public static final String RESULT_ERROR_CODE_AUTH_CODE_ERROR = "AUTH_CODE_ERROR"; //授权码参数错误
    public static final String RESULT_ERROR_CODE_AUTH_CODE_INVALID = "AUTH_CODE_INVALID"; //  授权码检验错误
    public static final String RESULT_ERROR_CODE_XML_FORMAT_ERROR = "XML_FORMAT_ERROR"; //   XML格式错误
    public static final String RESULT_ERROR_CODE_REQUIRE_POST_METHOD = "REQUIRE_POST_METHOD"; //请使用post方法
    public static final String RESULT_ERROR_CODE_SIGN_ERROR = "SIGNERROR"; //  签名错误
    public static final String RESULT_ERROR_CODE_LACK_PARAMS = "LACK_PARAMS"; //缺少参数
    public static final String RESULT_ERROR_CODE_POST_DATA_EMPTY = "POST_DATA_EMPTY"; //post数据为空
    public static final String RESULT_ERROR_CODE_NOT_UTF8 = "NOT_UTF8"; //   编码格式错误
    public static final String RESULT_ERROR_CODE_BUYER_MISMATCH = "BUYER_MISMATCH"; // 支付帐号错误
    public static final String RESULT_ERROR_CODE_APPID_NOT_EXIST = "APPID_NOT_EXIST"; //APPID不存在
    public static final String RESULT_ERROR_CODE_MCHID_NOT_EXIST = "MCHID_NOT_EXIST"; //MCHID不存在
    public static final String RESULT_ERROR_CODE_OUT_TRADE_NO_USED = "OUT_TRADE_NO_USED"; //  商户订单号重复
    public static final String RESULT_ERROR_CODE_APPID_MCHID_NOT_MATCH = "APPID_MCHID_NOT_MATCH"; //  appid和mch_id不匹配
    public static final String RESULT_ERROR_CODE_INVALID_REQUEST = "INVALID_REQUEST"; //无效请求，商户系统异常导致，商户权限异常、重复请求支付、证书错误、频率限制等
    public static final String RESULT_ERROR_CODE_TRADE_ERROR = "TRADE_ERROR"; //交易错误，业务错误导致交易失败、用户账号异常、风控、规则限制等
    public static final String RESULT_ERROR_CODE_ACCOUNTERROR = "ACCOUNTERROR"; //银行账户错误
    public static final String RESULT_ERROR_CODE_RULELIMIT = "RULELIMIT"; //交易额度超限
    public static final String RESULT_ERROR_CODE_REQUEST_BLOCKED = "REQUEST_BLOCKED"; //商户资料不全
    public static final String RESULT_ERROR_CODE_USER_ACCOUNT_ABNORMAL = "USER_ACCOUNT_ABNORMAL"; //买家身份信息不完善
    public static final String RESULT_ERROR_CODE_RESOURCE_ALREADY_EXISTS = "RESOURCE_ALREADY_EXISTS"; //资源已经存在(已经成功发送通知，无需重复调用)


    public static final String RESULT_ERROR_ORDER_NOT_EXIST = "ORDERNOTEXIST"; //  此交易订单号不存在
    public static final String RESULT_ERROR_TRANSACTION_ID_INVALID = "TRANSACTION_ID_INVALID"; //  订单号非法
    public static final String RESULT_ERROR_REFUND_FEE_INVALID = "REFUND_FEE_INVALID"; //  退款金额大于支付金额
    public static final String RESULT_ERROR_ORDE_RREFUND = "ORDERREFUND"; //  订单撤销失败（已经退款的订单不允许撤销）
    public static final String RESULT_ERROR_TRADE_STATE_ERROR = "TRADE_STATE_ERROR"; //  订单退款失败（订单已经退完款后，不能再退款）
    public static final String RESULT_ERROR_REFUND_NOT_EXIST = "REFUNDNOTEXIST"; //  订单退款查询（退款不存在）
    public static final String RESULT_ERROR_REVERSE_EXPIRE = "REVERSE_EXPIRE"; //  订单无法撤销 订单有7天的撤销有效期，过期将不能撤销
    public static final String RESULT_ERROR_ORDER_STATUS_ERROR = "ORDERSTATUSERROR"; //  订单状态错误
    public static final String RESULT_ERROR_BIZERR_NEED_RETRY = "BIZERR_NEED_RETRY"; //  退款业务流程错误，需要商户触发重试来解决
    public static final String RESULT_ERROR_FREQUENCY_LIMITED = "FREQUENCY_LIMITED"; //  频率限制
    public static final String RESULT_ERROR_ACCOUNT_ERROR = "ACCOUNTERROR"; //  分账接收方账户不存在
    public static final String RESULT_ERROR_AMOUNT_OVERDUE = "AMOUNT_OVERDUE"; //  分账金额超限
    public static final String RESULT_ERROR_INVALID_REQUEST = "INVALID_REQUEST"; //  分账次数过多
    public static final String RESULT_ERROR_INVALID_TRANSACTIONID = "INVALID_TRANSACTIONID"; //  无效的微信支付订单号
    public static final String RESULT_ERROR_NOAUTH = "NOAUTH"; //  无分账权限
    public static final String RESULT_ERROR_NOT_SHARE_ORDER = "NOT_SHARE_ORDER"; //  非分账订单
    public static final String RESULT_ERROR_OPENID_MISMATCH = "OPENID_MISMATCH"; //  Openid错误
    public static final String RESULT_ERROR_ORDER_NOT_READY = "ORDER_NOT_READY"; //  订单处理中
    public static final String RESULT_ERROR_PARAM_ERROR = "PARAM_ERROR"; //  参数错误
    public static final String RESULT_ERROR_RECEIVER_INVALID = "RECEIVER_INVALID"; //  分账接收方非法
    public static final String RESULT_ERROR_USER_NOT_EXIST = "USER_NOT_EXIST"; //  分账接收方不存在
    public static final String RESULT_ERROR_TRADE_ERROR= "TRADE_ERROR"; // 支付失败
    public static final String RESULT_ERROR_SETTLEMENTERROR = "SETTLEMENTERROR"; // 手续费账户余额不足、商户账户没有结算权限等原因导致资金解冻失败


    public static final String DEPOSIT_STATE_CREATE = "CREATED";
    public static final String DEPOSIT_STATE_DOING = "DOING";
    public static final String DEPOSIT_STATE_DONE="DONE";
    public static final String DEPOSIT_STATE_REVOKED="REVOKED";
    public static final String DEPOSIT_STATE_EXPIRED = "EXPIRED";
    public static final String DEPOSIT_STATE_COMPLETED = "COMPLETED";  //微信刷掌支付 完成状态
    public static final String DEPOSIT_STATE_CLOSED = "CLOSED";        //微信刷掌支付 订单关闭状态

    public static final String DEPOSIT_STATE_DESCRIPTION_USER_CONFIRM = "USER_CONFIRM";
    public static final String DEPOSIT_STATE_DESCRIPTION_MCH_COMPLETE = "MCH_COMPLETE";

    public static final String DEPOSIT_CODE_SYSTEM_ERROR="SYSTEM_ERROR";
    public static final String DEPOSIT_CODE_PARAM_ERROR="PARAM_ERROR";
    public static final String DEPOSIT_CODE_NO_AUTH="NO_AUTH";
    public static final String DEPOSIT_CODE_FREQUENCY_LIMITED="FREQUENCY_LIMITED";
    public static final String DEPOSIT_CODE_INVALID_REQUEST="INVALID_REQUEST";
    public static final String DEPOSIT_CODE_ORDER_NOT_EXIST="ORDER_NOT_EXIST";
    public static final String DEPOSIT_CODE_INVALID_ORDER_STATE="INVALID_ORDER_STATE";
    public static final String DEPOSIT_CODE_ORDER_CANCELED="ORDER_CANCELED";
    public static final String DEPOSIT_CODE_ORDER_DONE="ORDER_DONE";
    public static final String DEPOSIT_USER_PAYING = "USER_PAYING";
    public static final String DEPOSIT_USER_PAID = "USER_PAID";
    public static final String DEPOSIT_PAID_TYPE_NEWTON="NEWTON";
    public static final String DEPOSIT_PAID_TYPE_MCH="MCH";

    public static final String WECHATPAY_NONCE="Wechatpay-Nonce";
    public static final String WECHATPAY_TIMESTAMP="Wechatpay-Timestamp";
    public static final String WECHATPAY_SIGNATURE="Wechatpay-Signature";

    public static final String RESOURCE = "resource";
    public static final String CIPHER_TEXT = "ciphertext";
    public static final String ASSOCIATED_DATA = "associated_data";
    public static final String NONCE = "nonce";

    //接口返回错误列表 刷卡支付 支付结果失败
    public static final Set<String> MICRO_PAY_RESULT_ERROR_CODE_FAIL_LIST = Arrays.asList(
            RESULT_ERROR_CODE_PARAM_ERROR, RESULT_ERROR_CODE_NOAUTH, RESULT_ERROR_CODE_AUTH_CODE_EXPIRE,
            RESULT_ERROR_CODE_NOT_ENOUGH, RESULT_ERROR_CODE_NOT_SUPORTCARD, RESULT_ERROR_CODE_ORDER_CLOSED, RESULT_ERROR_CODE_ORDER_REVERSED,
            RESULT_ERROR_CODE_AUTH_CODE_ERROR, RESULT_ERROR_CODE_AUTH_CODE_INVALID, RESULT_ERROR_CODE_XML_FORMAT_ERROR,
            RESULT_ERROR_CODE_REQUIRE_POST_METHOD, RESULT_ERROR_CODE_SIGN_ERROR, RESULT_ERROR_CODE_LACK_PARAMS, RESULT_ERROR_CODE_NOT_UTF8,
            RESULT_ERROR_CODE_BUYER_MISMATCH, RESULT_ERROR_CODE_APPID_NOT_EXIST, RESULT_ERROR_CODE_MCHID_NOT_EXIST,
            RESULT_ERROR_CODE_APPID_MCHID_NOT_MATCH, RESULT_ERROR_CODE_INVALID_REQUEST, RESULT_ERROR_CODE_TRADE_ERROR,
            RESULT_ERROR_CODE_ACCOUNTERROR, RESULT_ERROR_CODE_RULELIMIT, RESULT_ERROR_CODE_REQUEST_BLOCKED,
            RESULT_ERROR_CODE_USER_ACCOUNT_ABNORMAL
    ).stream().collect(Collectors.toSet());


    //接口返回错误列表，刷卡支付 支付结果未知
    public static final Set<String> MICRO_PAY_RESULT_ERROR_CODE_UNKONW_LIST = Arrays.asList(
            RESULT_ERROR_CODE_SYSTEM_ERROR, //系统超时
            RESULT_ERROR_CODE_BANK_ERROR,//银行端超时
            RESULT_ERROR_CODE_USER_PAYING//该笔交易因为业务规则要求，需要用户输入支付密码
    ).stream().collect(Collectors.toSet());

    //接口返回错误列表  协议错误
    public static final Set<String> RESULT_ERROR_CODE_PROTOCAL_FAIL_LIST = Arrays.asList(
            RESULT_ERROR_CODE_NOAUTH,RESULT_ERROR_CODE_APPID_NOT_EXIST,RESULT_ERROR_CODE_MCHID_NOT_EXIST,
            RESULT_ERROR_CODE_APPID_MCHID_NOT_MATCH, RESULT_ERROR_CODE_LACK_PARAMS,
            RESULT_ERROR_CODE_SIGN_ERROR,RESULT_ERROR_CODE_XML_FORMAT_ERROR, RESULT_ERROR_CODE_REQUIRE_POST_METHOD,
            RESULT_ERROR_CODE_NOT_UTF8,RESULT_ERROR_CODE_POST_DATA_EMPTY
    ).stream().collect(Collectors.toSet());
    
    // 微信押金支付预授权完成失败返回码
    public static final Set<String> DEPOSIT_CONSUMER_FAIL_LIST = Arrays.asList(
            DEPOSIT_CODE_PARAM_ERROR, RESULT_ERROR_SETTLEMENTERROR
    ).stream().collect(Collectors.toSet());
    
    // 微信押金支付预授权完成失败返回码
    public static final Set<String> DEPOSIT_CONSUMER_V3_FAIL_LIST = Arrays.asList(
            RESULT_ERROR_NOAUTH, RESULT_ERROR_INVALID_REQUEST
    ).stream().collect(Collectors.toSet());
    
    /**
     * 微信pay接口所允许的字段
     */
    public static final Set<String> PAY_ALLOWED_FIELDS = new HashSet<String>(){{
        addAll(Arrays.asList(
                "version", "appid","sub_appid","mch_id","sub_mch_id","device_info","nonce_str","sign","body","detail","attach","out_trade_no","total_fee","fee_type","spbill_create_ip","goods_tag","limit_pay","auth_code","scene_info"
        ));

    }};

    /**
     * 微信precreate接口所允许的字段
     */
    public static final Set<String> PRECREATE_ALLOWED_FIELDS = new HashSet<String>(){{
        addAll(Arrays.asList(
                "version", "appid","mch_id","sub_appid","sub_mch_id","device_info","nonce_str","sign","sign_type","body","detail","attach","out_trade_no","fee_type","total_fee","spbill_create_ip","time_start","time_expire","goods_tag","notify_url","trade_type","product_id","limit_pay","openid","sub_openid","scene_info","appid","mch_id","sub_appid","sub_mch_id","device_info","nonce_str","sign","sign_type","body","detail","attach","out_trade_no","fee_type","total_fee","spbill_create_ip","time_start","time_expire","goods_tag","notify_url","trade_type","product_id","limit_pay","openid","sub_openid","scene_info","appid","mch_id","sub_appid","sub_mch_id","device_info","nonce_str","sign","body","detail","attach","out_trade_no","total_fee","spbill_create_ip","time_start","time_expire","goods_tag","notify_url","trade_type","limit_pay","appid","mch_id","sub_appid","sub_mch_id","device_info","nonce_str","sign","sign_type","body","detail","attach","out_trade_no","fee_type","total_fee","spbill_create_ip","time_start","time_expire","goods_tag","notify_url","trade_type","product_id","limit_pay","openid","sub_openid","scene_info","appid","mch_id","sub_appid","sub_mch_id","device_info","nonce_str","sign","sign_type","body","detail","attach","out_trade_no","fee_type","total_fee","spbill_create_ip","time_start","time_expire","goods_tag","notify_url","trade_type","limit_pay","openid","sub_openid"

        ));

    }};

    /**
     * 微信refund接口所允许的字段
     */
    public static final Set<String> REFUND_ALLOWED_FIELDS = new HashSet<String>(){{
        addAll(Arrays.asList(
                "version", "appid","mch_id","sub_appid","sub_mch_id","nonce_str","sign","transaction_id","out_trade_no","out_refund_no","total_fee","refund_fee","refund_fee_type","refund_desc","refund_account","appid","mch_id","sub_appid","sub_mch_id","nonce_str","sign","transaction_id","out_trade_no","out_refund_no","total_fee","refund_fee","refund_fee_type","refund_desc","refund_account","appid","mch_id","sub_appid","sub_mch_id","nonce_str","sign","transaction_id","out_trade_no","out_refund_no","total_fee","refund_fee","refund_fee_type","refund_desc","refund_account","appid","mch_id","sub_appid","sub_mch_id","nonce_str","sign","transaction_id","out_trade_no","out_refund_no","total_fee","refund_fee","refund_fee_type","refund_desc","refund_account","appid","mch_id","sub_appid","sub_mch_id","nonce_str","sign","transaction_id","out_trade_no","out_refund_no","total_fee","refund_fee","refund_fee_type","refund_desc","refund_account","appid","mch_id","sub_appid","sub_mch_id","device_info","nonce_str","sign","sign_type","out_refund_no","total_fee","refund_fee","refund_fee_type","op_user_id","refund_account","detail"

        ));

    }};

    /**
     * 微信预授权接口所允许的字段
     */
    public static final Set<String> DEPOSIT_PRE_CREATE_ALLOWED_FIELDS = new HashSet<String>(){{
        addAll(Arrays.asList(
                "post_payments", "post_discounts","time_range","location","risk_fund","attach","openid","need_user_confirm", "service_id", "sub_appid", "sub_openid", "deduction_credential"
        ));

    }};

    /**
     * 微信预授权完成接口所允许的字段
     */
    public static final Set<String> DEPOSIT_DEPOSIT_CANCEL_ALLOWED_FIELDS = new HashSet<String>(){{
        addAll(Arrays.asList(
                "reason"
        ));
    }};

    /**
     * 微信预授权订单同步接口所允许的字段
     */
    public static final Set<String> DEPOSIT_SYNC_ALLOWED_FIELDS = new HashSet<String>(){{
        addAll(Arrays.asList(
                "type", "detail"
        ));
    }};

    /**
     * 微信支付分服务商商户预授权接口所允许的字段
     */
    public static final Set<String> DEPOSIT_AUTH_APPLY_ALLOWED_FIELDS = new HashSet<String>(){{
        addAll(Arrays.asList(
                "service_id", "sub_appid", "sub_openid", "authorization_code"
        ));
    }};

    /**
     * 微信支付分服务商商户预授权查询接口所允许的字段
     */
    public static final Set<String> DEPOSIT_AUTH_QUERY_ALLOWED_FIELDS = new HashSet<String>(){{
        addAll(Arrays.asList(
                "service_id", "sub_appid", "sub_openid", "authorization_code"
        ));
    }};

    /**
     * 微信支付分服务商商户预授权查询接口所允许的字段
     */
    public static final Set<String> DEPOSIT_AUTH_TERMINATE_ALLOWED_FIELDS = new HashSet<String>(){{
        addAll(Arrays.asList(
                "service_id", "sub_appid", "sub_openid", "authorization_code", "reason"
        ));
    }};

    public static final String DEPOSIT_YES = "Y";

    /**
     * 微信返回trade_error对应失败错误文案
     */
    public static final Set<String> TRADE_ERROR_FAIL_MESSAGE = new HashSet<String>(){{
        addAll(Arrays.asList(
                "暂无可用的支付方式，请绑定其它银行卡完成支付", "本次交易存在风险，为保障资金安全，暂时不能付款。", "该商家未在微信支付平台完成开户意愿确认，今日暂无法继续收款",
                "银行拒绝该交易，请联系银行客服", "103 您的银行卡已被冻结，请尝试其他卡", "当前用户账户存在风险，暂时无法完成交易",
                "该银行卡已注销，请尝试其他卡，如有疑问请联系银行客服", "暂无可用的支付方式,请绑定其它银行卡完成支付", "该卡已被银行系统锁定，无法继续使用，请携带相关证件前往银行柜台解锁。如有疑问，可联系银行客服确认",
                "你的银行卡号有误或者卡已失效，请核对后再试", "当前银行卡已过期，请更换新卡后再试", "当前支付方式暂不可用",
                "当前银行卡已过期，请更换新卡后再试", "该交易存在风险被拒绝，请联系客服处理", "与赠卡方好友关系异常，亲属卡交易失败，请确认后再试", 
                "订单金额已超出该银行单月累计支付限额，请尝试更换其他支付方式", "104 银行拒绝该交易，请联系银行",
                "103 该银行卡不支持开通微信支付，请更换其他银行卡，可点击下方查看解决方法了解详情", "104 银行系统暂停交易，请稍后再试",
                "交易失败，超出单日对此用户收款最大金额", "103 银行卡预留手机号不符，请核对后再试，若银行卡预留手机号已变更，请更新手机号",
                "你已在银行关闭该卡片的快捷支付功能，请登录银行网银或者到银行柜台开通后再试", "你的账户存在异常，已开启保护模式。",
                "你的银行卡卡号填写错误或者卡号不存在", "当前银行系统异常，请稍后再试，或使用其它银行卡",
                "银行正在维护中，请选择其它银行卡"

        ));
    }};

    /**
     * 微信返回return msg对应失败错误文案
     */
    public static final Set<String> RETURN_FAIL_MESSAGE = new HashSet<String>(){{
        addAll(TRADE_ERROR_FAIL_MESSAGE);
        addAll(Arrays.asList(
                "此商家的收款功能已被限制，暂无法支付。商家可以登录微信商户平台/微信支付商家助手小程序查看原因和解决方案。",
                "101 付款码无效，请重新扫码", "sub_mch_id与sub_appid不匹配", "特约子商户商户号未授权服务商的产品权限",
                "商户状态不正常", "AppID不存在，请检查后再试", "输入源“/body/xml/auth_code”映射到值字段“付款码”字符串规则校验失败，字符串必须匹配正则表达式“^1[0-6][0-9]{16}$”",
                "输入源“/body/xml/total_fee”映射到数值字段“标价金额”规则校验失败，值高于最大值 100000000"

        ));
    }};

    public static final String MESSAGE_INVALID_ORDER_STATUS = "当前订单状态不合法";        // 订单状态可能是成功，需要做查单

    public static final String EVENT_TYPE_SCORE_USER_PAID = "PAYSCORE.USER_PAID";       // 预授权完成成功
    public static final String EVENT_TYPE_SCORE_USER_CONFIRM = "PAYSCORE.USER_CONFIRM"; // 预授权成功

    public static final String DEPOSIT_CANCEL_TRADE_EXISTS_MSG = "此out_order_no已存在";
    public static final String SIGN_TYPE_RSA2   = "RSA2";

    public static final String TYPE_ORDER_PAID = "Order_Paid";  // 场景类型为“Order_Paid”，字符串表示“订单收款成功” 。

    public static final String TERM_INFO_TERMINAL_TYPE = "11"; //条码支付辅助受理终端

}
