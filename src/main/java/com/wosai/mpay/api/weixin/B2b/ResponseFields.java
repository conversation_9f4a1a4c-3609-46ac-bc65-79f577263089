package com.wosai.mpay.api.weixin.B2b;

/***
 * @ClassName: ResponseFields
 * @Description:
 * @Auther: dabuff
 * @Date: 2025/1/13 11:14
 */
public class ResponseFields {

    public static final String SIGNATURE = "signature";
    public static final String TIMESTAMP = "timestamp";
    public static final String ECHOSTR = "echostr";
    public static final String NONCE = "nonce";

    public static final String TO_USER_NAME = "ToUserName";     // 小程序原始ID 小程序原始ID。
    public static final String FROM_USER_NAME = "FromUserName"; // 事件消息openid 微信官方的openid。
    public static final String CREATE_TIME = "CreateTime";      // 消息发送时间 消息发送时间戳。
    public static final String MSG_TYPE = "MsgType";            // 消息类型 消息类型，固定为event。
    public static final String EVENT = "Event";                 // 事件类型 事件类型，固定为retail_pay_notify。
    public static final String APPID = "appid";                 // 小程序ID 商户申请的小程序对应的appid。
    public static final String MCHID = "mchid";                 // 微信商户号 由微信支付生成并下发的商户号。
    public static final String OUT_TRADE_NO = "out_trade_no";   // 商户订单号 商户系统内部订单号，只能是数字、大小写字母_-*且在同一个商户号下唯一。
    public static final String ORDER_ID = "order_id";           // B2b支付订单号 B2b支付生成的订单号。
    public static final String PAY_STATUS = "pay_status";       // 订单状态 订单状态，枚举值。
    public static final String PAY_TIME = "pay_time";           // 支付完成时间 支付完成时间，标准北京时间，时区为东八区，格式为yyyy-MM-dd HH:mm:ss。
    public static final String ATTACH = "attach";               // 附加数据 附加数据，在查询API和支付通知中原样返回，可作为自定义参数使用。
    public static final String PAYER_OPENID = "payer_openid";   // 支付者 用户在直连商户appid下的唯一标识。
    public static final String AMOUNT = "amount";               // 订单金额信息 订单金额信息，仅支持人民币，格式见Amount。
    public static final String WXPAY_TRANSACTION_ID = "wxpay_transaction_id";   // 微信支付订单号 微信支付生成的订单号。
    public static final String ENV = "env";                     // 订单环境 订单环境 0：正式环境 1：沙箱环境。
    public static final String ERR_CODE = "errcode";            // 错误码 接口返回的错误码，0表示成功，非0表示失败。
    public static final String ERR_MSG = "errmsg";              // 错误信息 错误信息描述。
    public static final String REFUND_ID = "refund_id";         // B2b支付退款单号 B2b支付退款单号。
    public static final String OUT_REFUND_NO = "out_refund_no"; // 商户退款单号 商户系统内部退款单号，商户系统内部唯一，只能是数字、大小写字母_-*，同一退款单号多次请求只退一笔。
    public static final String REFUND_TIME = "refund_time";     // 退款成功时间 退款成功时间，当退款状态为退款成功时有返回，标准北京时间，时区为东八区，格式为yyyy-MM-dd HH:mm:ss。
    public static final String REFUND_STATUS = "refund_status"; // 退款状态 退款单状态，枚举值：REFUND_INIT、REFUND_PROCESSING、REFUND_SUCC、REFUND_FAIL。
    public static final String REFUND_DESC = "refund_desc";     // 退款说明 退款说明。
    public static final String WXPAY_REFUND_ID = "wxpay_refund_id"; // 微信支付退款单号 微信支付退款单号。

    /*  amount start    */
    public static final String ORDER_AMOUNT = "order_amount";       // 订单总金额 订单总需支付金额，也即是真正下单总金额，单位为分。
    public static final String PAYER_AMOUNT = "payer_amount";       // 用户支付金额 用户支付金额，单位为分（指使用优惠券的情况下，这里等于总金额-优惠券金额，目前暂不支持优惠券）。
    public static final String REFUND_AMOUNT = "refund_amount";     // 退款金额 退款金额，单位为分，可以做部分退款。
    public static final String CURRENCY = "currency";               // 货币类型 货币类型，仅支持"CNY"。
    /*  signData end    */

}
