package com.wosai.mpay.api.weixin;

import com.wosai.mpay.api.SSLEnvFlag;
import com.wosai.mpay.api.common.HttpConstant;
import com.wosai.mpay.exception.*;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.SSLUtil;
import com.wosai.mpay.util.WebUtils;
import okhttp3.*;
import okhttp3.internal.Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.HostnameVerifier;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

public class WeixinV3Client {

    public static final Logger logger = LoggerFactory.getLogger(WeixinV3Client.class);

    public static final String METHOD_POST = "POST";
    public static final String METHOD_GET = "GET";
    public static final String SIGN_TYPE_HMAC_SHA256 = "HMAC-SHA256";
    public static final String WECHATPAY2_SHA256_RSA2048 = "WECHATPAY2-SHA256-RSA2048";
    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    /**
     * @param serviceUrl 请求地址
     * @param signKey    请求秘钥
     * @param request    请求
     * @return 结果
     * @throws MpayException       业务异常
     * @throws MpayApiNetworkError 网络异常
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> call(String serviceUrl, String method, String mchId,String serialNo, String signKey, Map request) throws MpayException, MpayApiNetworkError {
        preProcess(request);
        String requestBody = JsonUtil.toJsonStr(request);
        logger.info("request {}", requestBody);
        //由于某些支付通道的测试环境会出现证书不符等各种异常的情况，故此处做特殊处理
        String requestUrl = serviceUrl;
        try {
            if(METHOD_GET.equals(method)){
                requestUrl = serviceUrl + "?" + WebUtils.buildQuery(request, WeixinConstants.CHARSET_UTF8);
            }
        }catch (Exception ex){
            throw new MpayApiSendError("failed to buildQuery request",ex);
        }
        String authorization = buildAuthorization(requestUrl, method, signKey, mchId, serialNo, requestBody);
        HostnameVerifier hostnameVerifier = SSLEnvFlag.getNotVerifyHostNames().size() > 0 ? SSLUtil.getUnsafeHostnameVerifier(SSLEnvFlag.getNotVerifyHostNames()) : null;
        SupplierWithEx<Request.Builder> requestBuildSupplier;
        String finalRequestUrl = requestUrl;
        if (METHOD_GET.equals(method)) {
            requestBuildSupplier = () -> new Request.Builder().url(finalRequestUrl).get();
        } else {
            requestBuildSupplier = ()->new Request.Builder().url(finalRequestUrl).post(RequestBody.create(MediaType.parse("application/json"), requestBody));
        }
        Map<String,Object> result = doRequest(WeixinV3Client.class.getName(), hostnameVerifier, serviceUrl,requestBuildSupplier, authorization, connectTimeout, readTimeout);
        logger.info("response {}", JsonUtil.toJsonStr(result));
        return result;
    }

    public static void preProcess(Map<String, Object> request) {
        for (Object mapKey : request.keySet().toArray()) {
            if (request.get(mapKey) == null) {
                request.remove(mapKey);
            }
        }
    }

    public static String buildAuthorization(String requestUrl, String method, String signKey, String mchId, String serialNo, String requestBody) throws MpayException {
        HttpUrl httpUrl = HttpUrl.parse(requestUrl);
        if (httpUrl == null) {
            throw new MpayClientError("serviceUrl parse error:" + requestUrl);
        }
        String canonicalUrl = httpUrl.encodedPath();
        if (httpUrl.encodedQuery() != null) {
            canonicalUrl += "?" + httpUrl.encodedQuery();
        }
        String nonceStr = getNonceStr();
        String timestamp = System.currentTimeMillis() / 1000 + "";
        String signBody = method + "\n" + canonicalUrl + "\n" + timestamp + "\n" + nonceStr + "\n";
        if(METHOD_GET.equals(method)){
            signBody = signBody +"\n";
        }else{
            signBody += requestBody + "\n";
        }
        String sign = RsaSignature.sign(signBody, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, signKey);
        return WECHATPAY2_SHA256_RSA2048 + " " +
                ProtocolFields.MCHID + "=\"" + mchId + "\"," +
                ProtocolFields.NONCE_STR + "=\"" + nonceStr + "\"," +
                ProtocolFields.SIGNATURE + "=\"" + sign + "\"," +
                ProtocolFields.TIMESTAMP + "=\"" + timestamp + "\"," +
                ProtocolFields.SERIAL_NO + "=\"" + serialNo+"\"";
    }

    public static String getNonceStr() {
        return ThreadLocalRandom.current().nextLong() + "";
    }

    public static Map<String, Object> doRequest(String clientName, HostnameVerifier verifier, String url, SupplierWithEx<Request.Builder> builderSupplier, String authorization, int connectTimeout, int readTimeout) throws MpayApiNetworkError {
        OkHttpClient client = HttpClientUtils.getHttpClient(clientName, null, verifier, connectTimeout, readTimeout);
        long start = System.currentTimeMillis();
        Response response = null;
        try {
            Request req;
            try {
                Request.Builder builder = builderSupplier.get();
                builder.header("Content-Type", "application/json");
                builder.header("Accept", "application/json");
                builder.header("Authorization", authorization);
                req = builder.build();
            } catch (Exception e) {
                throw new MpayApiSendError("failed to buildQuery request", e);
            }
            try {
                response = client.newCall(req).execute();
            } catch (IOException e) {
                throw new MpayApiSendError("failed to send request", e);
            }
            int code = response.code();
            Map<String, Object> result = new HashMap<>();
            try {
                if (response.body() != null) {
                    String responseBodyStr = response.body().string();
                    try {
                        if(responseBodyStr != null && !responseBodyStr.isEmpty()){
                            result = JsonUtil.jsonStringToObject(responseBodyStr, Map.class);
                        }
                    } catch (Exception ex) {
                        logger.error("JSON.parseObject error");
                    }
                } else {
                    throw new MpayApiSendError("failed to send request", new Exception(response.toString()));
                }
                result.put(HttpConstant.HTTP_CODE, code);
                return result;
            } catch (IOException e) {
                throw new MpayApiReadError("failed to read response", e);
            }
        } finally {
            Util.closeQuietly(response);
            long end = System.currentTimeMillis();
            if (logger.isDebugEnabled()) {
                logger.debug("cost: {} ms, url: {}", end - start, url);
            }
        }
    }

    @FunctionalInterface
    public interface SupplierWithEx<T> {

        /**
         * Gets a result.
         *
         * @return a result
         */
        T get() throws Exception;
    }

}
