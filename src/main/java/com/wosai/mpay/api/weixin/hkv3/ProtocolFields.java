package com.wosai.mpay.api.weixin.hkv3;

public class ProtocolFields {
    public static final String APP_ID = "appid"; //微信分配的公众账号ID
    public static final String SP_APP_ID = "sp_appid"; // 机构在微信公众平台申请服务号对应的APPID
    public static final String SP_MCH_ID = "sp_mchid"; // 微信支付分配的机构商户号
    public static final String SUB_MCH_ID = "sub_mchid";//微信分配的子商户公众账号ID
    public static final String SUB_APP_ID = "sub_appid";//微信分配的子商户公众账号ID
    public static final String DEVICE_INFO = "device_info";//调用接口提交的终端设备号
    public static final String NONCE_STR = "nonce_str";//随机字符串
    public static final String SIGN = "sign";//签名
    public static final String VERSION = "version";//版本号
    public static final String SIGN_TYPE = "sign_type";//签名方式
    public static final String MCH_ID = "mch_id";//微信支付分配的商户号
    public static final String MCHID = "mchid";//微信支付分配的商户号
    public static final String SUB_MCHID = "sub_mchid";//微信分配的子商户公众账号ID
    public static final String CHANNEL_ID = "channel_id"; //微信支付分配给收单服务商 的 ID。
    public static final String SIGNATURE = "signature";//签名
    public static final String TIMESTAMP = "timestamp";//签名
    public static final String SERIAL_NO = "serial_no";//证书序列号
    public static final String CERT_ID = "cert_id"; // 证书序列号

}
