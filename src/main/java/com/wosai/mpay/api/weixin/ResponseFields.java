package com.wosai.mpay.api.weixin;

/**
 * Created by jian<PERSON> on 13/11/15.
 */
public class ResponseFields {
    public static final String RETURN_CODE = "return_code";
    public static final String RETURN_MSG = "return_msg";

    public static final String CODE = "code";
    public static final String MESSAGE = "message";

    public static final String APP_ID = "appid"; //调用接口提交的公众账号ID
    public static final String SUB_APP_ID = "sub_appid";//微信分配的子商户公众账号ID
    public static final String MCH_ID = "mch_id";//调用接口提交的商户号
    public static final String SUB_MCH_ID = "sub_mch_id";//微信分配的子商户公众账号ID
    public static final String DEVICE_INFO = "device_info";//调用接口提交的终端设备号
    public static final String NONCE_STR = "nonce_str";//随机字符串
    public static final String SIGN = "sign";//签名
    public static final String VERSION = "version";//版本号
    public static final String RESULT_CODE = "result_code";//业务结果
    public static final String ERR_CODE = "err_code";//错误代码
    public static final String ERR_CODE_DES = "err_code_des";//错误代码描述
    public static final String AUTHINFO = "authinfo";//微信人脸支付调用凭证
    public static final String EXPIRES_IN = "expires_in";//authinfo的有效时间, 单位秒


    public static final String OPEN_ID = "openid"; //用户在商户appid 下的唯一标识
    public static final String IS_SUBSCRIBE = "is_subscribe"; //用户是否关注公众账号，仅在公众账号类型支付有效，取值范围：Y或N;Y-关注;N-未关注
    public static final String SUB_OPEN_ID = "sub_openid"; //子商户appid下用户唯一标识，如需返回则请求时需要传sub_appid
    public static final String SUB_IS_SUBSCRIBE = "sub_is_subscribe"; //用户是否关注子公众账号，仅在公众账号类型支付有效，取值范围：Y或N;Y-关注;N-未关注
    public static final String TRADE_TYPE = "trade_type"; //支付类型为MICROPAY(即扫码支付)
    public static final String BANK_TYPE = "bank_type"; //银行类型，采用字符串类型的银行标识
    public static final String FEE_TYPE = "fee_type"; //符合ISO 4217标准的三位字母代码，默认人民币：CNY，
    public static final String TOTAL_FEE = "total_fee"; //订单总金额，单位为分，只能为整数
    public static final String CASH_FEE_TYPE = "cash_fee_type"; //符合ISO 4217标准的三位字母代码，默认人民币：CNY
    public static final String CASH_FEE = "cash_fee"; //订单现金支付金额
    public static final String COUPON_FEE = "coupon_fee"; //代金券或立减优惠金额<=订单总金额，订单总金额-代金券或立减优惠金额=现金支付金额
    public static final String TRANSACTION_ID = "transaction_id"; //微信支付订单号
    public static final String OUT_TRADE_NO = "out_trade_no"; //商户系统的订单号，与请求一致
    public static final String ATTACH = "attach"; //商家数据包，原样返回
    public static final String TIME_END = "time_end"; //订单完成时间，格式为yyyyMMddHHmmss
    public static final String REFUND_ID = "refund_id"; //微信退款单号
    public static final String CODE_URL = "code_url"; //二维码链接
    public static final String PREPAY_ID = "prepay_id";//预支付交易会话标识

    public static final String CASH_REFUND_FEE = "cash_refund_fee";//现金退款金额
    public static final String COUPON_REFUND_FEE = "coupon_refund_fee";//代金券或立减优惠退款金额
    public static final String REFUND_FEE = "refund_fee";//申请退款金额
    public static final String COUPON_REFUND_COUNT = "coupon_refund_count";//退款代金券使用数量
    public static final String COUPON_REFUND_ID_PREFIX = "coupon_refund_id";//退款代金券ID, $n为下标，从0开始编号
    public static final String COUPON_TYPE_PREFIX = "coupon_type";//代金券类型
    public static final String COUPON_REFUND_FEE_PREFIX = "coupon_refund_fee";//代金券或立减优惠退款金额

    public static final String REFUND_DETAILS = "refund_detail";
    public static final String REFUND_DETAILS_PROMOTION_ID = "promotion_id";
    public static final String REFUND_DETAILS_SCOPE = "scope";
    public static final String REFUND_DETAILS_TYPE = "type";
    public static final String REFUND_DETAILS_AMOUNT = "amount";
    public static final String REFUND_DETAILS_REFUND_AMOUNT = "refund_amount";


    public static final String PROMOTION_DETAIL = "promotion_detail";//营销详情
    public static final String PROMOTION_DETAIL_TYPE = "type";//营销详情 可选 32 优惠类型(COUPON- 代金券，需要走结算资金的充值型代金券, (境外商户券币种不支付币种一致) DISCOUNT- 优惠券，丌走结算资金的免充值型优惠 券，(境外商户券币种不标价币种一致)
    public static final String PROMOTION_DETAIL_PROMOTION_ID = "promotion_id";// 必填 32 优惠 ID，券或者立减优惠 id
    public static final String PROMOTION_DETAIL_AMOUNT = "amount";//营销详情 必填 32 优惠券面额，优惠券面额=微 信出资金额+商家出资金额+其他出资方金额
    public static final String PROMOTION_DETAIL_WXPAY_CONTRIBUTE = "wxpay_contribute";//营销详情 必填 32 微信出资金额，单 位为分
    public static final String PROMOTION_DETAIL_MERCHANT_CONTRIBUTE = "merchant_contribute";//营销详情  32 商家出资金额(指交 易发起方)，单位为分
    public static final String PROMOTION_DETAIL_OTHER_CONTRIBUTE = "other_contribute";//营销详情  必填 32 其他出资方出资金 额，单位为分
    public static final String SETTLEMENT_REFUND_FEE = "settlement_refund_fee";//应结退款 金额
    public static final String SETTLEMENT_TOTAL_FEE = "settlement_total_fee";//应结订单金 额
    public static final String COUPON_TYPE = "coupon_type";//代金券类型
    public static final String MWEB_URL = "mweb_url";

    public static final String ORDER_ID = "order_id";
    public static final String ERROR_MSG = "error_msg";
    public static final String ERROR_CODE = "error_code";
    public static final String RESULT = "result";
    public static final String FAIL_REASON = "fail_reason";
    public static final String FINISH_TIME = "finish_time";
    public static final String STATUS = "status";
    public static final String CLOSE_REASON = "close_reason";
    public static final String FINISH_TRANSACTION_ID = "finish_transaction_id";

    public static final String STATE = "state";
    public static final String STATE_DESCRIPTION = "state_description";
    public static final String PACKAGE = "package";
    public static final String NEED_COLLECTION = "need_collection";
    public static final String COLLECTION = "collection";
    public static final String COLLECTION_STATE = "state";
    public static final String COLLECTION_PAYING_AMOUNT = "paying_amount";
    public static final String COLLECTION_PAID_AMOUNT = "paid_amount";
    public static final String COLLECTION_DETAILS = "details";
    public static final String COLLECTION_DETAIL_SEQ = "seq";
    public static final String COLLECTION_DETAIL_AMOUNT = "amount";
    public static final String COLLECTION_DETAIL_PAID_TYPE = "paid_type";
    public static final String COLLECTION_DETAIL_PAID_TIME = "paid_time";
    public static final String COLLECTION_DETAIL_TRANSACTION_ID = "transaction_id";
    public static final String APPLY_PERMISSIONS_TOKEN = "apply_permissions_token";

    public static final String RISK_INFO = "risk_info"; //风险控制信息
    public static final String USER_ID = "user_id"; //微信用户唯一标识码


    public static final String AUTH_STATUS = "auth_status"; //授权状态

    /**
     * 交易状态
     * SUCCESS—支付成功 REFUND—转入退款 NOTPAY—未支付 CLOSED—已关闭 REVOKED—已撤销(刷卡支付) USERPAYING--用户支付中 PAYERROR--支付失败(其他原因，如银行返回失败)
     */
    public static final String TRADE_STATE = "trade_state";
    public static final String TRADE_STATE_DESC = "trade_state_desc";
    
    public static final String RESPONSE_KEY_PROMOTION_DETAIL = PROMOTION_DETAIL + "." + PROMOTION_DETAIL;

}
