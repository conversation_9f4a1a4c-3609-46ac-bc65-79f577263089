package com.wosai.mpay.api.weixin;

import com.wosai.mpay.api.HttpResourceCallback;
import com.wosai.mpay.api.SSLEnvFlag;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import java.util.Map;

/**
 * Created by jianfree on 12/11/15.
 */
public class AsyncWeixinClient {
    public static final Logger logger = LoggerFactory.getLogger(AsyncWeixinClient.class);
    protected int connectTimeout = 2000;
    protected int readTimeout = 5000;

    private RequestConfig requestConfig;

    public AsyncWeixinClient() {
        initClient();
    }

    public AsyncWeixinClient(int readTimeout, int connectTimeout){
        this.readTimeout = readTimeout;
        this.connectTimeout = connectTimeout;
        initClient();
    }

    public void initClient(){
        requestConfig = RequestConfig.custom().setConnectionRequestTimeout(connectTimeout).setConnectTimeout(connectTimeout).setSocketTimeout(readTimeout).build();
    }


    public void call(String serviceUrl, String signType, String signKey, SSLContext sslContext, Map<String, Object> request, HttpResourceCallback<Map<String, Object>> callback){
        try{
            doCall(serviceUrl, signType, signKey, sslContext, request, callback);
        }catch (Throwable t){
            callback.onError(t);
        }
    }

    private void doCall(String serviceUrl, String signType, String signKey, SSLContext sslContext, Map<String, Object> request, HttpResourceCallback<Map<String, Object>> callback) throws MpayException {
        CloseableHttpAsyncClient client = getClient(sslContext);
        String requestXml = WeixinClient.preProcess(signType, signKey, request);
        AsyncClientUtil.logRequest(logger, requestXml);
        HttpPost httpPost = new HttpPost(serviceUrl);
        httpPost.setHeader("Content-Type", "text/xml");
        httpPost.setEntity(new ByteArrayEntity(requestXml.getBytes()));
        httpPost.setConfig(requestConfig);
        long start = System.currentTimeMillis();
        client.execute(httpPost, AsyncClientUtil.getFutureCallback(logger, WeixinConstants.CHARSET_UTF8, AsyncClientUtil.ResponseType.STRING, HttpResourceCallback.<String>create(
                TracingUtil.getTraceCarrierItem(),
                (response, t) -> {
                    AsyncClientUtil.logResponse(logger, serviceUrl, System.currentTimeMillis() - start, response != null ? response.replaceAll("\\n", "") : null, t);
                    if(t != null){
                        callback.onError(t);
                        return;
                    }else{
                        Map<String,Object> result = WeixinClient.postProcess(response, signType);
                        callback.onComplete(result);
                    }
                }
        )));
    }


    private CloseableHttpAsyncClient getClient(SSLContext sslContext) {
        sslContext = SSLEnvFlag.turnOffSSl() ? SSLUtil.getUnsafeSSLContext() : sslContext;
        HostnameVerifier hostnameVerifier = SSLEnvFlag.getNotVerifyHostNames().size() > 0 ? SSLUtil.getUnsafeHostnameVerifier(SSLEnvFlag.getNotVerifyHostNames()) : null;
        return AsyncClientUtil.getCloseableHttpAsyncClient(sslContext, hostnameVerifier);
    }

}