package com.wosai.mpay.api.foxconn;

public class ResponseFields {
    public static final String CODE = "code";// 响应编码
    public static final String MSG = "msg";// 响应消息
    public static final String ERROR = "error";// 错误信息
    public static final String TIMESTAMP = "timestamp";// 时间戳，格式yyyy-MM-dd HH:mm:ss
    public static final String DATA = "data";// 响应参数合集（同步应答失败时，此参数为空）
    public static final String STATUS_CODE = "statusCode";// 响应编码
    public static final String STATUS_DESC = "statusDesc";// 响应描述
    public static final String MERCHANT_NO ="merchantNo"; //商户编号
    public static final String ORDER_NO ="orderNo"; //平台订单编号
    public static final String MER_ORDER_NO ="merOrderNo"; //商户订单编号
    public static final String ORDER_AMT ="orderAmt"; //订单金额
    public static final String CURRENCY ="currency"; //币种，默认CNY
    public static final String PAY_STYLE ="payStyle"; //支付渠道
    public static final String TRADE_STATUS ="tradeStatus"; //订单状态，参见“订单状态
    public static final String TRADE_MSG ="tradeMsg"; //订单状态描述
    public static final String EXCEPTION_MSG ="exceptionMsg"; //异常信息
    public static final String REMARK ="remark"; //商户保留域（异步通知会原封返回给商户）
    public static final String SUB_ORDERLIST ="subOrderList"; //分账订单明细列表
    public static final String SUB_ORDNO ="subOrdNo"; //支付平台分账订单编号
    public static final String SUB_MCHCODE ="subMchCode"; //商户见证子账号
    public static final String SUB_AMOUNT ="subAmount"; //分账订单金额单位为分
    public static final String SEQ_NUM ="seqNum"; //分账订单序号
    public static final String SUB_SUBJECT ="subSubject"; //分账订单标题
    public static final String WARRANT_RESULT ="warrantResult"; //担保结果
    public static final String REFUND_RESULT ="refundResult"; //是否退款标识：01-未退款、02-部分已退款、03-全额已退款
    public static final String ORDER_ID ="orderId"; //平台订单编号

}