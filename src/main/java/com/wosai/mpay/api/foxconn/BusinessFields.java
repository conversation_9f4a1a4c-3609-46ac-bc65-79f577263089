package com.wosai.mpay.api.foxconn;

public class BusinessFields {
    public static final String TYPE = "type";// 报文类型
    public static final String USER_ID = "userId";
    public static final String EQUIPMENT_SN = "equipmentSn";// 机具序列号
    public static final String BAR_CODE = "barCode";// 条码
    public static final String AMOUNT = "amount";// 交易金额
    public static final String MER_ORDER_NO = "merOrderNo"; // 商户订单编号
    public static final String ORDER_NO = "orderNo"; // 平台订单号
    public static final String ORDER_TYPE = "orderType"; // 订单类型
    public static final String OLD_ORDER_NO = "oldOrderNo"; // 原平台支付订单编号
    public static final String REFUND_AMT = "refundAmt"; // 退款金额(分)
    public static final String BGRET_URL = "bgRetUrl"; // 后台回调地址
    public static final String REMARK = "remark"; // 保留域
    public static final String SUB_ORDER_INFO = "subOrderInfo"; // 退款分账订单
    public static final String OLD_SUB_ORDERNO = "oldSubOrderNo"; // 原平台分账订单号,支付成功异步通知/查询时，会返回该平台分账订单号
    public static final String SUB_REFUND_AMT = "subRefundAmt"; // 子订单退款金额单位为分，与退款金额需一致
    public static final String SUB_SUBJECT = "subSubject"; // 子订单标题
    public static final String FEE_AMOUNT = "feeAmount"; // 分账订单手续费(单位为分)
}
