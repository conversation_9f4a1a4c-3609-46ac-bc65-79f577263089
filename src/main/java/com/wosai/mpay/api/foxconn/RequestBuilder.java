package com.wosai.mpay.api.foxconn;

import java.util.HashMap;
import java.util.Map;

import com.wosai.mpay.util.JsonUtil;

public class RequestBuilder {
    private Map<String, String> request;
    private Map<String, Object> data;

    public RequestBuilder() {
        request = new HashMap<String, String>();
        data = new HashMap<String, Object>();
    }

    public void set(String field, String value) {
        request.put(field, value);
    }

    public void dataSet(String field, Object value) {
        data.put(field, value);
    }
    
    public Map<String, String> build() {
        request.put(ProtocolFields.DATA, JsonUtil.toJsonStr(data));
        return request;
    }
}