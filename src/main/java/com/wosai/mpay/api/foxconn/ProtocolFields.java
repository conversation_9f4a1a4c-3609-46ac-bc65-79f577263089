package com.wosai.mpay.api.foxconn;

public class ProtocolFields {
    public static final String ACCESS_ID = "access_id"; // 平台分配给接入方编号(机构/商户/appCode)
    public static final String VERSION = "version";// 接口版本号默认1.0
    public static final String USER_ID = "userId";// 商户或用户唯一标识；
    public static final String TIMESTAMP = "timestamp";// 时间戳，格式yyyy-MM-dd HH:mm:ss
    public static final String FORMAT = "format";// 请求参数集合格式，暂时只支持JSON
    public static final String TYPE = "type";// 报文类型
    public static final String DATA = "data";// 请求参数合集
    public static final String SIGN = "sign";// 商户请求参数签名


}