package com.wosai.mpay.api.foxconn;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class FoxconnConstants {

    public static final String CHARSET_UTF8 = "UTF-8";

    public static final String VERSION_1_0 = "1.0";
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String FORMAT_JSON = "JSON";

    public static final String TYPE_FOXCONN_PAY = "7735"; // 富士康条码支付
    public static final String TYPE_FOXCONN_PAY_QUERY = "7737"; // 富士康条码支付结果查询
    public static final String TYPE_QUERY = "7501"; // 交易查询
    public static final String USER_ID_BARCODE_PAY = "P0000000";

    public static final String CODE_SUCCESS = "200"; // 成功

    // code
    public static final String CODE_R001 = "R001"; // 账号不存在
    public static final String CODE_R002 = "R002"; // 余额不足
    public static final String CODE_R003 = "R003"; // 验证码错误
    public static final String CODE_R004 = "R004"; // 接入门店不存在
    public static final String CODE_R005 = "R005"; // 报文格式错误
    public static final String CODE_R006 = "R006"; // 金额必须大于0
    public static final String CODE_R007 = "R007"; // 订单号重复
    public static final String CODE_R008 = "R008"; // 金额格式错误
    public static final String CODE_R009 = "R009"; // 交易编号错误
    public static final String CODE_R010 = "R010"; // 绑定流水号不存在
    public static final String CODE_R011 = "R011"; // 此账号未授权
    public static final String CODE_R012 = "R012"; // 查无此交易
    public static final String CODE_R014 = "R014"; // 签名失败
    public static final String CODE_R015 = "R015"; // 参数错误
    public static final String CODE_R016 = "R016"; // 业务处理出现异常
    public static final String CODE_R017 = "R017"; // 未查询到相对应数据信息
    public static final String CODE_R018 = "R018"; // 业务种类错误
    public static final String CODE_R019 = "R019"; // 账户已存在
    public static final String CODE_R020 = "R020"; // 不支持该银行交易
    public static final String CODE_R021 = "R021"; // 银行卡号有误
    public static final String CODE_R022 = "R022"; // 银行卡验证未通过
    public static final String CODE_R023 = "R023"; // 终端号错误
    public static final String CODE_R024 = "R024"; // 交易处理中
    public static final String CODE_R025 = "R025"; // 交易已受理
    public static final String CODE_R026 = "R026"; // 证件信息有误
    public static final String CODE_R027 = "R027"; // 付款账户异常
    public static final String CODE_R028 = "R028"; // 收付款账户相同
    public static final String CODE_R029 = "R029"; // 企业账户不可转账给个人账户
    public static final String CODE_R030 = "R030"; // 付款账户不可发起转账业务
    public static final String CODE_R031 = "R031"; // 没有对应渠道或对应多个渠道
    public static final String CODE_R032 = "R032"; // 调用支付系统出错
    public static final String CODE_R033 = "R033"; // 支付发生错误
    public static final String CODE_R034 = "R034"; // 余额转账发生异常
    public static final String CODE_R035 = "R035"; // 账号状态冻结或注销
    public static final String CODE_R036 = "R036"; // 四要素验证未通过
    public static final String CODE_R037 = "R037"; // 银行卡未绑定
    public static final String CODE_R038 = "R038"; // 用户不存在
    public static final String CODE_R039 = "R039"; // 用户已被冻结
    public static final String CODE_R040 = "R040"; // 账户风险控制异常
    public static final String CODE_R041 = "R041"; // 业务编号重复
    public static final String CODE_R042 = "R042"; // 报文总笔数或者总金额与列表不符
    public static final String CODE_R043 = "R043"; // 合同编号重复
    public static final String CODE_R044 = "R044"; // 合同编号不存在
    public static final String CODE_R050 = "R050"; // 快捷支付充值失败
    public static final String CODE_R051 = "R051"; // 风控检查不通过
    public static final String CODE_R052 = "R052"; // 商户流水已存在
    public static final String CODE_R053 = "R053"; // 用户状态异常
    public static final String CODE_R054 = "R054"; // 用户状态正常
    public static final String CODE_R055 = "R055"; // 用户已被注销
    public static final String CODE_R056 = "R056"; // 银行卡类型有误
    public static final String CODE_R057 = "R057"; // 网络异常
    public static final String CODE_R058 = "R058"; // 明细订单号重复
    public static final String CODE_R059 = "R059"; // 账务日期错误
    public static final String CODE_R060 = "R060"; // 原交易信息不存在
    public static final String CODE_R061 = "R061"; // 支付通道异常
    public static final String CODE_R062 = "R062"; // 退款金额大于可退金额
    public static final String CODE_R063 = "R063"; // 原交易订单未成功
    public static final String CODE_R064 = "R064"; // 公钥信息未配置
    public static final String CODE_R065 = "R065"; // 暂无数据
    public static final String CODE_R066 = "R066"; // 商户入驻信息异常
    public static final String CODE_R067 = "R067"; // 交易撤销失败
    public static final String CODE_R068 = "R068"; // 待支付
    public static final String CODE_R069 = "R069"; // 数据异常
    public static final String CODE_R070 = "R070"; // 文件格式错误
    public static final String CODE_R071 = "R071"; // 授权码无效
    public static final String CODE_R072 = "R072"; // 结算类型为T0的订单不能发起退款
    public static final String CODE_R073 = "R073"; // 退款金额大于未结算净额
    public static final String CODE_R074 = "R074"; // 商户禁止退款
    public static final String CODE_R075 = "R075"; // 当前交易订单有正在处理中的退款
    public static final String CODE_R076 = "R076"; // 渠道不允许已结算订单退款或超出最长退款时间
    public static final String CODE_R077 = "R077"; // 退款商户与原订单所属商户不一致
    public static final String CODE_R078 = "R078"; // 进件数量超出限制
    public static final String CODE_R079 = "R079"; // 当日进件数量超出限制
    public static final String CODE_R080 = "R080"; // 历史进件数量超出限制
    public static final String CODE_R081 = "R081"; // 不支持部分退款
    public static final String CODE_R082 = "R082"; // 订单已成功，请勿重复支付
    public static final String CODE_R083 = "R083"; // 超出最大提现金额，请重新查询可提现金额
    public static final String CODE_R084 = "R084"; // 手机号码格式不正确
    public static final String CODE_R167 = "R167"; // 订单已成功，不能发起撤销
    public static final String CODE_R168 = "R168"; // 流水冲正已成功，不能重复冲正
    public static final String CODE_R200 = "R200"; // 已受理
    public static final String CODE_R201 = "R201"; // 机构号不存在
    public static final String CODE_R202 = "R202"; // 接入商户号不存在
    public static final String CODE_R203 = "R203"; // 商户注销失败
    public static final String CODE_R204 = "R204"; // 机构商户号不存在
    public static final String CODE_R205 = "R205"; // 机构商户号已经存在
    public static final String CODE_R206 = "R206"; // 商户不存在
    public static final String CODE_R207 = "R207"; // 商户查询失败
    public static final String CODE_R208 = "R208"; // 商户待审核
    public static final String CODE_R209 = "R209"; // 商户审核拒绝
    public static final String CODE_R210 = "R210"; // 商户被冻结
    public static final String CODE_R211 = "R211"; // 商户已注销
    public static final String CODE_R212 = "R212"; // 机构费率不存在
    public static final String CODE_R213 = "R213"; // 商户费率不在机构费率范围内
    public static final String CODE_R214 = "R214"; // 省份/直辖市编号不存在
    public static final String CODE_R215 = "R215"; // 城市编号不存在
    public static final String CODE_R216 = "R216"; // 区县编号不存在
    public static final String CODE_R217 = "R217"; // 银行类别不存在
    public static final String CODE_R218 = "R218"; // 联行号不存在
    public static final String CODE_R219 = "R219"; // 支付宝经营类目不存在
    public static final String CODE_R220 = "R220"; // 微信经营类目不存在
    public static final String CODE_R221 = "R221"; // 支付类型不支持
    public static final String CODE_R222 = "R222"; // 商户入驻失败
    public static final String CODE_R223 = "R223"; // 商户证件类型不正确
    public static final String CODE_R224 = "R224"; // 选择了商户证件类型，商户证件号码为空
    public static final String CODE_R225 = "R225"; // 商户证件有效期不正确
    public static final String CODE_R226 = "R226"; // 清算类型不正确
    public static final String CODE_R227 = "R227"; // 账户类型不正确
    public static final String CODE_R228 = "R228"; // 支付类型不正确
    public static final String CODE_R229 = "R229"; // 时效类型不正确
    public static final String CODE_R230 = "R230"; // 费用类型不正确
    public static final String CODE_R231 = "R231"; // 费用参数不正确
    public static final String CODE_R232 = "R232"; // 商户修改失败
    public static final String CODE_R233 = "R233"; // 机构状态不为有效
    public static final String CODE_R234 = "R234"; // 机构不支持的清算类型
    public static final String CODE_R241 = "R241"; // 渠道类型不正确
    public static final String CODE_R242 = "R242"; // 国家编号不存在
    public static final String CODE_R243 = "R243"; // 用户已存在
    public static final String CODE_R244 = "R244"; // 用户类型错误
    public static final String CODE_R245 = "R245"; // 门店不存在
    public static final String CODE_R246 = "R246"; // 入账商户不存在
    public static final String CODE_R247 = "R247"; // 子订单号重复
    public static final String CODE_R248 = "R248"; // 原支付子订单号不存在
    public static final String CODE_R249 = "R249"; // 分页参数格式不正确
    public static final String CODE_R250 = "R250"; // 不存在即将过期金额
    public static final String CODE_R251 = "R251"; // 订单信息不存在
    public static final String CODE_R252 = "R252"; // 订单信息已存在
    public static final String CODE_R253 = "R253"; // 用户未开通免密支付
    public static final String CODE_R254 = "R254"; // 该机构用户在平台存在多个
    public static final String CODE_R255 = "R255"; // 该商户不是线上商户，不能进行免密支付
    public static final String CODE_R256 = "R256"; // 该订单冻结失败
    public static final String CODE_R257 = "R257"; // 商户未开通免密支付
    public static final String CODE_R258 = "R258"; // 商户未配置子账户信息
    public static final String CODE_R259 = "R259"; // 渠道调用失败
    public static final String CODE_R260 = "R260"; // 支付密码错误
    public static final String CODE_R261 = "R261"; // 余额支付失败
    public static final String CODE_R262 = "R262"; // 订单存在分账信息,暂不支持余额支付
    public static final String CODE_R263 = "R263"; // 支付密码已锁定
    public static final String CODE_R264 = "R264"; // 交易金额超限
    public static final String CODE_R265 = "R265"; // 验证码发送失败
    public static final String CODE_R266 = "R266"; // 邮件发送失败
    public static final String CODE_R267 = "R267"; // 暂不支持的操作类型
    public static final String CODE_R268 = "R268"; // 插入卡包相关信息缺失，该发票不能插入卡包
    public static final String CODE_R269 = "R269"; // 该手机号已注册
    public static final String CODE_R270 = "R270"; // 未获取到APP名称，商户APP参数未配置
    public static final String CODE_R271 = "R271"; // 暂不支持该银行
    public static final String CODE_R272 = "R272"; // 该银行卡已绑定
    public static final String CODE_R273 = "R273"; // 订单已终态
    public static final String CODE_R274 = "R274"; // 短信发送太频繁
    public static final String CODE_R275 = "R275"; // 提现绑卡失败
    public static final String CODE_R276 = "R276"; // 支付密码未设置
    public static final String CODE_R277 = "R277"; // 原手机号码为空
    public static final String CODE_R278 = "R278"; // 用户信息已失效
    public static final String CODE_R279 = "R279"; // 短信发送超限
    public static final String CODE_R280 = "R280"; // 姓名证件号码验证失败
    public static final String CODE_R281 = "R281"; // 短信验证码校验失败
    public static final String CODE_R282 = "R282"; // 没有通过验证或验证失效
    public static final String CODE_R283 = "R283"; // 验证码已超时，请重新获取
    public static final String CODE_R284 = "R284"; // 支付密码验证失败
    public static final String CODE_R285 = "R285"; // 暂不支持该类型银行卡的查询
    public static final String CODE_R286 = "R286"; // 银行卡信息不存在
    public static final String CODE_R287 = "R287"; // 提现失败
    public static final String CODE_R288 = "R288"; // 门店状态非正常
    public static final String CODE_R289 = "R289"; // 账户还未实名认证
    public static final String CODE_R290 = "R290"; // 注册成功，开户失败
    public static final String CODE_R291 = "R291"; // 支付分账指令中子不能存在同商户同门店
    public static final String CODE_R292 = "R292"; // 原手机号码不同
    public static final String CODE_R235 = "R235"; // 未审核
    public static final String CODE_R236 = "R236"; // 已审核
    public static final String CODE_R237 = "R237"; // 审核未通过
    public static final String CODE_R238 = "R238"; // 待审核
    public static final String CODE_R239 = "R239"; // 已删除
    public static final String CODE_R240 = "R240"; // 初审通过
    public static final String CODE_R301 = "R301"; // 优惠劵核销失败
    public static final String CODE_R310 = "R310"; // 渠道流水不存在
    public static final String CODE_R311 = "R311"; // 验证码发送未成功
    public static final String CODE_R314 = "R314"; // 流水状态不允许冲正
    public static final String CODE_R320 = "R320"; // 门店状态非正常
    public static final String CODE_R332 = "R332"; // 操作太频繁
    public static final String CODE_R309 = "R309"; // 子订单不存在
    public static final String CODE_R302 = "R302"; // 核销设备号未配置
    public static final String CODE_R303 = "R303"; // 银行卡用户信息不匹配
    public static final String CODE_R304 = "R304"; // 银行卡状态异常
    public static final String CODE_R308 = "R308"; // 平台系统参数未配置
    public static final String CODE_R312 = "R312"; // 调用核销异常
    public static final String CODE_R313 = "R313"; // appCode信息未配置
    public static final String CODE_R315 = "R315"; // 原交易存在分账信息，退款没有分账
    public static final String CODE_R316 = "R316"; // 分账信息数量超限
    public static final String CODE_R317 = "R317"; // 订单金额和分账金额不一致
    public static final String CODE_R318 = "R318"; // 余额冻结失败
    public static final String CODE_R319 = "R319"; // 优惠信息已存在
    public static final String CODE_R321 = "R321"; // 对账未完成
    public static final String CODE_R322 = "R322"; // 开户失败
    public static final String CODE_R323 = "R323"; // 设备不存在
    public static final String CODE_R324 = "R324"; // 无效的类型
    public static final String CODE_R325 = "R325"; // 通讯异常
    public static final String CODE_R326 = "R326"; // 小程序支付参数未配置或者状态异常
    public static final String CODE_R327 = "R327"; // 异步回调响应非成功
    public static final String CODE_R328 = "R328"; // 用户名或密码错误
    public static final String CODE_R329 = "R329"; // 订单支付与下单业务类型不一致
    public static final String CODE_R330 = "R330"; // 产品未开通
    public static final String CODE_R331 = "R331"; // 途径不正确
    public static final String CODE_R333 = "R333"; // 该渠道不支持该产品
    public static final String CODE_R334 = "R334"; // 该渠道暂不支持产品开通
    public static final String CODE_R336 = "R336"; // 交易订单不存在
    public static final String CODE_R100 = "R100"; // 新建交易
    public static final String CODE_R000 = "R000"; // 处理成功
    public static final String CODE_R101 = "R101"; // 交易关闭
    public static final String CODE_R102 = "R102"; // 已处理
    public static final String CODE_R992 = "R992"; // 已领完
    public static final String CODE_R993 = "R993"; // 已过期
    public static final String CODE_R994 = "R994"; // 订单金额小于订单手续费
    public static final String CODE_R995 = "R995"; // 数字签名错误
    public static final String CODE_R996 = "R996"; // 交易超时
    public static final String CODE_R997 = "R997"; // 状态未决
    public static final String CODE_R998 = "R998"; // 系统异常
    public static final String CODE_R999 = "R999"; // 交易失败

    // statusCode
    public static final String STATUS_CODE_INVALID_TOKEN = "R400"; // 无效token 生效
    public static final String STATUS_CODE_INVALID_BARCODE = "R469"; // 无效的条码
    public static final String STATUS_CODE_SUCCESS = "R000"; // 处理成功
    public static final String STATUS_CODE_FAIL = "R999"; // 处理失败
    
    
    // 订单状态
    public static final String TRADE_STATUS_CREATED = "P01"; // 待支付
    public static final String TRADE_STATUS_IN_PROG = "P02"; // 支付中
    public static final String TRADE_STATUS_PAID = "P03"; // 支付成功
    public static final String TRADE_STATUS_PAY_FAIL = "P04"; // 支付失败
    public static final String TRADE_STATUS_PAY_CLOSED = "P05"; // 已关闭

    // 支付中返回
    public static final Set<String> CODE_IN_PROG = new HashSet<>(
            Arrays.asList(CODE_R025, CODE_R024, CODE_R068,
                        CODE_R200, CODE_R100
                    )
            );

    public static final Set<String> CODE_PAY_CANCELED = new HashSet<>(
            Arrays.asList(CODE_R001, CODE_R002, CODE_R004, CODE_R005, CODE_R006, CODE_R007, CODE_R008,
                    CODE_R009, CODE_R012, CODE_R014, CODE_R015, CODE_R023, CODE_R027, CODE_R028, CODE_R029,
                    CODE_R030, CODE_R035, CODE_R039, CODE_R040, CODE_R055, CODE_R053, CODE_R201, CODE_R204,
                    CODE_R206, CODE_R210, CODE_R211, CODE_R245, CODE_R247, CODE_R251, CODE_R252, CODE_R320,
                    CODE_R995, CODE_R999, CODE_R336
                    )
            );
    public static final String ORDER_TYPE_TRADE = "01"; // 支付交易
    
}
