package com.wosai.mpay.api.foxconn;

import java.util.Map;

import com.wosai.mpay.util.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Digest;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.RsaSignature;

public class FoxconnClient {
    public static final Logger logger = LoggerFactory.getLogger(FoxconnClient.class);

    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public FoxconnClient() {
    }

    public FoxconnClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public Map<String, Object> call(String gateway, String signKey, Map<String, String> request) throws MpayException, MpayApiNetworkError {
        request.remove(ProtocolFields.SIGN);
        for(Object mapKey: request.keySet().toArray()){
            if(request.get(mapKey) == null){
                request.remove(mapKey);
            }
        }
        String data = MapUtils.getString(request, ProtocolFields.DATA);
        request.put(ProtocolFields.DATA, getMd5Sign(data));
        String sign = RsaSignature.sign(request, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, signKey);
        request.put(ProtocolFields.SIGN, sign);
        request.put(ProtocolFields.DATA, data);
        logger.debug("request {}", RsaSignature.getSignCheckContent(request));
        String responseStr = HttpClientUtils.doPost(FoxconnClient.class.getName(), null, null, gateway, request, FoxconnConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        logger.debug("response {}", responseStr);
        try{
            Map<String,Object> result = JsonUtil.jsonStrToObject(responseStr, Map.class);
            if(result.containsKey(ResponseFields.DATA)) {
                result.put(ResponseFields.DATA, JsonUtil.jsonStrToObject((String)result.get(ResponseFields.DATA), Map.class));
            }
            return result;
        }catch (Exception e){
            throw new MpayApiUnknownResponse("parse response error: " + e.getMessage(), e);
        }
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
    
    /**
     * 获取md5签名串
     * @param content
     * @param key
     * @param charset
     * @return
     * @throws MpayException
     */
    public static String getMd5Sign(String content) throws MpayException{
        String md5 = "";
        try {
            md5 = Digest.md5((content).getBytes(FoxconnConstants.CHARSET_UTF8));
        } catch (Exception e) {
            throw new MpayException("failed to generate md5 signature.", e);
        }
        return md5;
    }
}