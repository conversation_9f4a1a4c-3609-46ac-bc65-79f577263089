package com.wosai.mpay.api.foxconn;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import com.wosai.mpay.util.SafeSimpleDateFormat;

public class FoxconnTest {
    public static final String ACCESS_ID = "********";
    public static final String USER_ID = "********";
    public static final String PRIVATE_KEY = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQDx1qtUGhAKx5rxCu8Vun65BhX2LnXZKEgJsm63B1iKFmTIDOamt1N5a4BRicsms7ArJP8ePEAD5XJmRONedirg44weReTQ3GXtDlCikFX2leasj6bwO2zX43L9QF2g753rDKIP4Xzcn+kTvG4UTxWn2v5alS4JoRgJmxY6SF6fZi8Yy9OwqdrTTDC7jf8CdCDD+33T6ER4qGSVcrUvo+F3iiu3tUpTvC/oBEmoVtJUf+D2wj2/+4nKxLsXVOT9U4T0jCsHIock7nKkhLAwGFHOW8Y9YGLggymxHzF3fPOpUaC0/mwu26M24AmxQZ4PAqiVCW//c2mx1HF+yruPdS1tAgMBAAECggEBAJge+FZGrqCZPhUxU4LPdLbd5SKdxvN6KxYubwYiUMdLnslXlB56Yr+Vp3miu270yHPbKb7TF1FcGsHJw1u/6OjdOV4xiOT4PgUHZCFduTUKxbb2BYRHXQfPwp0ax8pb73HS2H400F3XGL2ZYYTU2kJjtms0O2lDM+OW8jxnGV4Zp68VAzo1rUPqb8rdMSNSqrxCriFJ6FRdUo1VJrO8gU3+C8VTRJOg+WOjZQ5LQjJIkbadfhxQj/jSd9fg1iwl06Am6cvHUuRzvZPoF+evD2oCCv1cuzUyxCc6WTPcD0S6LBIfO9L9Wv6LomNdokDfL+NjpnmjirD/qRqswO5MwQECgYEA+WIdpLKLfYP4XUH2k5sL2HjP33nrhHH+M80u2uiwsNdViPesGfFNkB34pVyny2piqdKjvrM4yWXF2vIJf01EM5RnQi+Ebw7K3OqFs5wKprDFVsqHeUvVBySofFzlT2Hchg5kGE9h4GXEga3BjeTqQXzQRLIAOTbvqA9b/eTabYkCgYEA+EFOu/0ekQRDW+mWpkVz0faINPS9WT/Pp+XgRO8lVdIJ2bmIp9JThHyD6YnuN3eJC6BsQ/gm4R/5p7HiVigcb0ucU+KwbybXBASUcxDnt/nb206h6dEz6gzlMVCDDgrjiY8iKjo7uTRTnS74TZP0Oylj+0dtGf8gcHG7LPraC8UCgYEAj7dDbg6W5JaRIu1ItHe9s9qrsRYJ7CHlZOTyA6XlbYQq/uv8elbdZBhhmqpy6H8+rK9/Pf760tWb2Dlzz7EvhKoZd8L6hyX3MqjUtFVQrg9hKks9F2HkOmGd102RuNWkyHyyom/c7/y16iXATY2GkobjXzNVfaNzKV7wnLiuSEECgYEA6fKhgAbJB2A9GiDHF2nb+8GYGk+JINJBnrfC4TN8u6N0JPnzDjPnALNWnH6fPyYmwA4t+N+hxAWd/6pi4U5rfXaDm4nFOvofJmoqe0UzGO0dg05Wx0Ge6rG8SvzUhCc+xiNczu6R2ba2FSpuVK1ZO2eGEQb+icEjkNOc0IaVdQECgYAFUHbCKQppfOFjpboQlEzSqGd4KzH8hLWOdHEmt7TUkwbC/PbhpObEetZJnPU4nRJetZ7h7Vw6aKLw/E986Whbu2v2nuj4ZvH0AuxT/YnEVMC4uvvts1fLvW8/plWTdfpjwV9LCXfHIuL6p2sIF+dMV7I2mm/3/56sAW814iVi0w==";
    public static final String GATEWAY = "http://**************:31001/epp-gateway/api/entry.do";
    private static final SafeSimpleDateFormat dateFormat = new SafeSimpleDateFormat(FoxconnConstants.DATE_TIME_FORMAT);

    public static void main(String[] args) throws Exception{
        query();
        foxconnQuery();
//        pay();
    }

    public static void foxconnQuery(){
        RequestBuilder builder = getDefaultRequestBuilder();
        builder.set(BusinessFields.TYPE, FoxconnConstants.TYPE_FOXCONN_PAY_QUERY);
        builder.set(BusinessFields.USER_ID, "********");
        builder.dataSet(BusinessFields.MER_ORDER_NO, "7894259278260572");

        
        FoxconnClient client = new FoxconnClient(1000, 5000);
        try {
            Map<String,Object> response = client.call(GATEWAY, PRIVATE_KEY, builder.build());
            System.out.println(response);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public static void query(){
        RequestBuilder builder = getDefaultRequestBuilder();
        builder.set(BusinessFields.TYPE, FoxconnConstants.TYPE_QUERY);
        builder.set(BusinessFields.USER_ID, "100060001");
        builder.dataSet(BusinessFields.ORDER_NO, "579926161662255104");
        builder.dataSet(BusinessFields.ORDER_TYPE, FoxconnConstants.ORDER_TYPE_TRADE);
        builder.dataSet(BusinessFields.MER_ORDER_NO, null);

        
        FoxconnClient client = new FoxconnClient(1000, 5000);
        try {
            Map<String,Object> response = client.call(GATEWAY, PRIVATE_KEY, builder.build());
            System.out.println(response);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public static void pay(){
        RequestBuilder builder = getDefaultRequestBuilder();
        builder.set(BusinessFields.TYPE, FoxconnConstants.TYPE_FOXCONN_PAY);
        builder.set(ProtocolFields.ACCESS_ID, "********");
        builder.set(ProtocolFields.VERSION, "1.0");
        builder.set(ProtocolFields.TIMESTAMP, dateFormat.format(new Date()));
        builder.set(ProtocolFields.FORMAT, "JSON");
        builder.set(BusinessFields.TYPE, "7735");
        builder.set(ProtocolFields.USER_ID, "M00020024");
        builder.dataSet(BusinessFields.EQUIPMENT_SN, "***********");
        builder.dataSet(BusinessFields.BAR_CODE, "883423432432423");
        builder.dataSet(BusinessFields.AMOUNT, "700");
        

        FoxconnClient client = new FoxconnClient(1000, 5000);
        try {
            Map<String,Object> response = client.call(GATEWAY, PRIVATE_KEY, builder.build());
            System.out.println(response);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public static String getTxTime(){
        return new SimpleDateFormat(FoxconnConstants.DATE_TIME_FORMAT).format(new Date());
    }

    public static String getOrderId(){
        return "test" + System.currentTimeMillis();
    }



    public static RequestBuilder getDefaultRequestBuilder(){
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.ACCESS_ID, ACCESS_ID);
        builder.set(ProtocolFields.VERSION, FoxconnConstants.VERSION_1_0);
        builder.set(ProtocolFields.TIMESTAMP, dateFormat.format(new Date()));
        builder.set(ProtocolFields.FORMAT, FoxconnConstants.FORMAT_JSON);
        return builder;
    }
}
