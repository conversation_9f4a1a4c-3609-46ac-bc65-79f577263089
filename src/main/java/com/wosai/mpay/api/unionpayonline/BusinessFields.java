package com.wosai.mpay.api.unionpayonline;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/6/6.
 */
public class BusinessFields {
    public static final String TXN_TYPE = "txnType";// 交易类型 交易类型：00
    public static final String TXN_SUB_TYPE = "txnSubType";// 交易子类 01：自助消费，通过地址的方式区分前台消费和后台消费（含无跳转支付） 03：分期付款
    public static final String ACCOUNT_PAY_CHANNEL = "accountPayChannel";// 预付卡通道 预付卡的专享支付页面
    public static final String ACC_NO = "accNo";// 账号 1、 后台类消费交易时上送全卡号或卡号后4位 2、 跨行收单且收单机构收集银行卡信息时上送、 3、前台类交易可通过配置后返回，卡号可选上送
    public static final String ACC_SPLIT_DATA = "accSplitData";// 分账域 查看详情
    public static final String ACC_TYPE = "accType";// 账号类型(卡介质) 后台类交易且卡号上送； 跨行收单且收单机构收集银行卡信息时上送 01：银行卡 02：存折 03：IC卡 默认取值：01 取值“03”表示以IC终端发起的IC卡交易，IC作为普通银行卡进行支付时，此域填写为“01”
    public static final String ACQ_INS_CODE = "acqInsCode";// 收单机构代码 接入类型为收单机构接入时需上送
    public static final String BACK_URL = "backUrl";// 后台通知地址 后台返回商户结果时使用，如上送，则发送商户后台交易结果通知，不支持换行符等不可见字符，如需通过专线通知，需要在通知地址前面加上前缀：专线的首字母加竖线ZX| 如果不需要发后台通知，可以固定上送http://www.specialUrl.com
    public static final String CARD_TRANS_DATA = "cardTransData";// 有卡交易信息域 查看详情
    public static final String CERT_TYPE = "certType";// 证书类型 01：敏感信息加密公钥
    public static final String CTRL_RULE = "ctrlRule";// 控制规则 32位01字符串控制位，从左至右第二位取值为1时表示小微商户
    public static final String CHANNEL_TYPE = "channelType";// 渠道类型，这个字段区分B2C网关支付和手机wap支付；07：PC,平板 08：手机
    public static final String CURRENCY_CODE = "currencyCode";// 交易币种 默认为156
    public static final String CUSTOMER_INFO = "customerInfo";// 银行卡验证信息及身份信息 查看详情
    public static final String CUSTOMER_IP = "customerIp";// 持卡人IP 前台交易，有IP防钓鱼要求的商户上送
    public static final String DEFAULT_PAY_TYPE = "defaultPayType";// 默认支付方式 C 取值参考数据元说明
    public static final String ENCRYPT_CERT_ID = "encryptCertId";// 加密证书ID
    public static final String FRONT_FAIL_URL = "frontFailUrl";// 失败交易前台跳转地址 前台消费交易若商户上送此字段，则在支付失败时，页面跳转至商户该URL（不带交易信息，仅跳转）
    public static final String FRONT_URL = "frontUrl";// 前台通知地址 前台返回商户结果时使用，前台类交易需上送 不支持换行符等不可见字符
    public static final String INSTAL_TRANS_INFO = "instalTransInfo";// 分期付款信息域 查看详情
    public static final String ISS_INS_CODE = "issInsCode";// 发卡机构代码 1、当账号类型为02-存折时需填写 2、在前台类交易时填写默认银行代码，支持直接跳转到网银。银行简码列表参考附录：C.1、C.2， 其中C.2银行列表仅支持借记卡
    public static final String MER_ABBR = "merAbbr";// 商户简称 接入类型为收单机构接入时需上送
    public static final String MER_CAT_CODE = "merCatCode";// 商户类别 接入类型为收单机构接入时需上送
    public static final String MER_NAME = "merName";// 商户名称 接入类型为收单机构接入时需上送
    public static final String ORDER_DESC = "orderDesc";// 订单描述 移动支付上送
    public static final String ORDER_ID = "orderId";// 商户订单号 商户订单号，不能含“-”或“_”; 商户自定义，同一交易日期内不可重复; 商户代码merId、商户订单号orderId、订单发送时间txnTime三要素唯一确定一笔交易。
    public static final String ORDER_TIMEOUT = "orderTimeout";// 订单接收超时时间 1、前台类消费交易时上送 2、认证支付2.0，后台交易时可选
    public static final String ORIG_QRY_ID = "origQryId";// 原交易查询流水号 原始消费交易的queryId
    public static final String PAY_TIMEOUT = "payTimeout";// 支付超时时间 超过此时间用户支付成功的交易，不通知商户，系统自动退款，大约5个工作日金额返还到用户账户
    public static final String REQ_RESERVED = "reqReserved";// 请求方保留域 商户自定义保留域，交易应答时会原样返回
    public static final String RESERVED = "reserved";// 保留域 查看详情
    public static final String RISK_RATE_INFO = "riskRateInfo";// 风控信息域 查看详情
    public static final String SUB_MER_ABBR = "subMerAbbr";// 二级商户简称 商户类型为平台类商户接入时必须上送
    public static final String SUB_MER_ID = "subMerId";// 二级商户代码 商户类型为平台类商户接入时必须上送
    public static final String SUB_MER_NAME = "subMerName";// 二级商户名称 商户类型为平台类商户接入时必须上送
    public static final String SUP_PAY_TYPE = "supPayType";// 支持支付方式 仅仅pc使用，使用哪种支付方式 由收单机构填写，取值为以下内容的一种或多种，通过逗号（，）分割。取值参考数据字典
    public static final String TERM_ID = "termId";// 终端号
    public static final String TXN_AMT = "txnAmt";// 交易金额
    public static final String TXN_TIME = "txnTime";// 订单发送时间
    public static final String USER_MAC = "userMac";// 终端信息域 移动支付业务需要上送
}
