package com.wosai.mpay.api.unionpayonline;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/6/6.
 */
public class UnionPayOnlineConstants {

    public static final String CHARSET_UTF8 = "UTF-8";

    public static final String VERSION_1_0_0 = "1.0.0";
    public static final String VERSION_5_0_0 = "5.0.0";
    public static final String VERSION_5_0_1 = "5.0.1";
    public static final String VERSION_5_1_0 = "5.1.0";

    public static final String SIGNMETHOD_RSA = "01";
    public static final String SIGNMETHOD_SHA256 = "11";
    public static final String SIGNMETHOD_SM3 = "12";

    public static final String DATE_TIME_FORMAT = "yyyyMMddHHmmss"; // 20180702142900
    public static final String CURRENCY_CODE_CNY = "156"; //人民币-156

    public static final String BIZ_TYPE_B2B = "000202"; // 企业网银支付（B2B支付）
    public static final String BIZ_TYPE_B2C = "000201"; // 在线网关支付 B2C网关支付

    public static final String TXN_TYPE_PRECREATE = "01";
    public static final String TXN_TYPE_CANCEL = "31";
    public static final String TXN_TYPE_REFUND = "04";
    public static final String TXN_TYPE_QUERY = "00";
    public static final String TXN_TYPE_CERT_UPDATE_QUERY = "95";

    public static final String TXN_SUB_TYPE_00 = "00";
    public static final String TXN_SUB_TYPE_01 = "01";  //自助消费

    public static final String ACCESS_TYPE_MERCHANT = "0"; //0：商户直连接入
    public static final String ACCESS_TYPE_CHANNEL = "1"; //1：收单机构接入
    public static final String ACCESS_TYPE_BIG_MERCHANT = "2"; //2：平台商户接入

    public static final String CHANNEL_TYPE_PC = "07";
    public static final String CHANNEL_TYPE_PHONE = "08";

    public static final String PAY_CARD_TYPE_DEBIT_CARD = "00"; //借记卡
    public static final String PAY_CARD_TYPE_CREDIT_CARD = "01"; //贷记卡

    public static final String RESP_CODE_SUCCESS = "00"; //成功
    public static final String RESP_CODE_PARTIAL_SUCCESS = "A6"; //有缺陷的成功
    public static final String RESP_CODE_TIMEOUT = "03"; //交易通讯超时，请发起查询交易
    public static final String RESP_CODE_REQUEST_ACCEPTED = "05"; //交易已受理，请稍后查询交易结果
    public static final String RESP_CODE_SYSTEM_BUSY = "06"; //系统繁忙，请稍后再试
    public static final String RESP_CODE_DUPLICATE = "12"; //重复交易
    public static final String RESP_CODE_ORDER_NOT_EXISTS = "34"; //查无此交易

}
