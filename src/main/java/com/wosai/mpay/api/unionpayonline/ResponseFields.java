package com.wosai.mpay.api.unionpayonline;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/6/6.
 */
public class ResponseFields {
    public static final String ACCESS_TYPE = "accessType";// 接入类型 0：商户直连接入 1：收单机构接入 2：平台商户接入
    public static final String ACC_NO = "accNo";// 账号 根据商户配置返回
    public static final String ACC_SPLIT_DATA = "accSplitData";// 分账域 查看详情
    public static final String ACQ_INS_CODE = "acqInsCode";// 收单机构代码
    public static final String BIND_ID = "bindId";// 绑定标识号 绑定支付时，根据商户配置返回
    public static final String BIZ_TYPE = "bizType";// 产品类型
    public static final String CARD_TRANS_DATA = "cardTransData";// 有卡交易信息域 查看详情
    public static final String CERT_TYPE = "certType";// 证书类型
    public static final String CURRENCY_CODE = "currencyCode";// 交易币种 默认为156
    public static final String CUSTOMER_INFO = "customerInfo";// 银行卡验证信息及身份信息 查看详情
    public static final String ENCODING = "encoding";// 编码方式
    public static final String ENCRYPT_PUB_KEY_CERT = "encryptPubKeyCert";// 加密公钥证书 加密公钥证书
    public static final String EXCHANGE_DATE = "exchangeDate";// 兑换日期 交易成功，交易币种和清算币种不一致的时候返回
    public static final String EXCHANGE_RATE = "exchangeRate";// 清算汇率 交易成功，交易币种和清算币种不一致的时候返回
    public static final String ISSUER_IDENTIFY_MODE = "issuerIdentifyMode";// 发卡机构识别模式 消费、预授权交易返回
    public static final String MER_ID = "merId";// 商户代码
    public static final String ORDER_ID = "orderId";// 商户订单号 商户订单号，不能含“-”或“_”; 商户自定义，同一交易日期内不可重复; 商户代码merId、商户订单号orderId、订单发送时间txnTime三要素唯一确定一笔交易。
    public static final String ORIG_QRY_ID = "origQryId";// 原交易查询流水号 原始消费交易的queryId
    public static final String ORIG_RESP_CODE = "origRespCode";// 原交易应答码 查询交易成功时返回
    public static final String ORIG_RESP_MSG = "origRespMsg";// 原交易应答信息
    public static final String PAY_CARD_ISSUE_NAME = "payCardIssueName";// 支付卡名称 移动支付交易时，根据商户配置返回
    public static final String PAY_CARD_NO = "payCardNo";// 支付卡标识 移动支付交易时，根据商户配置返回
    public static final String PAY_CARD_TYPE = "payCardType";// 支付卡类型 根据商户配置返回
    public static final String PAY_TYPE = "payType";// 支付方式 根据商户配置返回
    public static final String QR_CODE = "qrCode";// 二维码数据
    public static final String QUERY_ID = "queryId";// 查询流水号 供后续查询用
    public static final String REQ_RESERVED = "reqReserved";// 请求方保留域
    public static final String RESERVED = "reserved";// 保留域 查看详情
    public static final String RESP_CODE = "respCode";// 应答码
    public static final String RESP_MSG = "respMsg";// 应答信息
    public static final String SETTLE_AMT = "settleAmt";// 清算金额
    public static final String SETTLE_CURRENCY_CODE = "settleCurrencyCode";// 清算币种
    public static final String SETTLE_DATE = "settleDate";// 清算日期
    public static final String SIGNATURE = "signature";// 签名
    public static final String SIGN_METHOD = "signMethod";// 签名方法
    public static final String SIGN_PUB_KEY_CERT = "signPubKeyCert";// 签名公钥证书 使用RSA签名方式时必选，此域填写银联签名公钥证书。
    public static final String TN = "tn";// 银联受理订单号 商户推送订单后银联移动支付系统返回该流水号，商户调用支付控件时使用
    public static final String TRACE_NO = "traceNo";// 系统跟踪号
    public static final String TRACE_TIME = "traceTime";// 交易传输时间
    public static final String TXN_AMT = "txnAmt";// 交易金额
    public static final String TXN_SUB_TYPE = "txnSubType";// 交易子类
    public static final String TXN_TIME = "txnTime";// 订单发送时间
    public static final String TXN_TYPE = "txnType";// 交易类型
    public static final String VERSION = "version";// 版本号
}
