package com.wosai.mpay.api.unionpayonline;

import com.wosai.mpay.api.unionpayopen.UnionPayOpenClient;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayClientError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

/**
 * Created by w<PERSON><PERSON>an<PERSON> on 2019/6/6.
 */
public class UnionPayOnlineClient {
    public static final Logger logger = LoggerFactory.getLogger(UnionPayOnlineClient.class);

    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public UnionPayOnlineClient() {
    }

    public UnionPayOnlineClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public Map<String, String> call(String gateway, String signKey, Map<String, String> request) throws MpayException, MpayApiNetworkError {
        request.remove(ProtocolFields.SIGNATURE);
        for(Object mapKey: request.keySet().toArray()){
            if(request.get(mapKey) == null){
                request.remove(mapKey);
            }
        }

        try {
            addSignInfo(signKey, request);
        } catch (Exception e) {
            throw new MpayClientError("get sign error", e);
        }
        String requestStr;
        try{
            requestStr = WebUtils.buildQuery(request, UnionPayOnlineConstants.CHARSET_UTF8);
        }catch (Exception e){
            throw new MpayClientError("build query error", e);
        }
        logger.debug("request {}", requestStr);
        String responseStr = HttpClientUtils.doPost(UnionPayOpenClient.class.getName(), null, null, gateway, request, UnionPayOnlineConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        logger.debug("response {}", responseStr);
        try{
            Map<String,String> result = convertResultStringToMap(responseStr);
            return result;
        }catch (Exception e){
            throw new MpayApiUnknownResponse("parse response error: " + e.getMessage(), e);
        }
    }

    public static void addSignInfo(String signKey, Map<String,String> request) throws Exception{
        //由于version, encoding对 签名有影响，故这些值统一在这里设置
        request.put(ProtocolFields.VERSION, UnionPayOnlineConstants.VERSION_5_1_0);
        request.put(ProtocolFields.ENCODING, UnionPayOnlineConstants.CHARSET_UTF8);
        request.put(ProtocolFields.SIGN_METHOD, UnionPayOnlineConstants.SIGNMETHOD_RSA);
        String signContent = Digest.sha256(RsaSignature.getSignCheckContent(request, ProtocolFields.SIGNATURE).getBytes(UnionPayOnlineConstants.CHARSET_UTF8));;
        String sign = RsaSignature.sign(signContent, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, signKey);
        request.put(ProtocolFields.SIGNATURE, sign);
    }


    /**
     * 将形如key=value&key=value的字符串转换为相应的Map对象
     *
     * @param result
     * @return
     */
    public static Map<String, String> convertResultStringToMap(String result) {
        Map<String, String> map = null;

        if (result != null && !"".equals(result.trim())) {
            if (result.startsWith("{") && result.endsWith("}")) {
                result = result.substring(1, result.length() - 1);
            }
            map = parseQString(result);
        }
        return map;
    }


    /**
     * 解析应答字符串，生成应答要素
     *
     * @param str
     *            需要解析的字符串
     * @return 解析的结果map
     * @throws UnsupportedEncodingException
     */
    public static Map<String, String> parseQString(String str) {
        Map<String, String> map = new HashMap<String, String>();
        int len = str.length();
        StringBuilder temp = new StringBuilder();
        char curChar;
        String key = null;
        boolean isKey = true;
        boolean isOpen = false;//值里有嵌套
        char openName = 0;
        if(len>0){
            for (int i = 0; i < len; i++) {// 遍历整个带解析的字符串
                curChar = str.charAt(i);// 取当前字符
                if (isKey) {// 如果当前生成的是key

                    if (curChar == '=') {// 如果读取到=分隔符
                        key = temp.toString();
                        temp.setLength(0);
                        isKey = false;
                    } else {
                        temp.append(curChar);
                    }
                } else  {// 如果当前生成的是value
                    if(isOpen){
                        if(curChar == openName){
                            isOpen = false;
                        }

                    }else{//如果没开启嵌套
                        if(curChar == '{'){//如果碰到，就开启嵌套
                            isOpen = true;
                            openName ='}';
                        }
                        if(curChar == '['){
                            isOpen = true;
                            openName =']';
                        }
                    }

                    if (curChar == '&' && !isOpen) {// 如果读取到&分割符,同时这个分割符不是值域，这时将map里添加
                        putKeyValueToMap(temp, isKey, key, map);
                        temp.setLength(0);
                        isKey = true;
                    } else {
                        temp.append(curChar);
                    }
                }

            }
            putKeyValueToMap(temp, isKey, key, map);
        }
        return map;
    }

    private static void putKeyValueToMap(StringBuilder temp, boolean isKey, String key, Map<String, String> map) {
        if (isKey) {
            key = temp.toString();
            if (key.length() == 0) {
                throw new RuntimeException("QString format illegal");
            }
            map.put(key, "");
        } else {
            if (key.length() == 0) {
                throw new RuntimeException("QString format illegal");
            }
            map.put(key, temp.toString());
        }
    }

    /**
     * 功能：前台交易构造HTTP POST自动提交表单<br>
     * @param reqUrl 表单提交地址<br>
     * @param request 以MAP形式存储的表单键值<br>
     * @param encoding 上送请求报文域encoding字段的值<br>
     * @return 构造好的HTTP POST交易表单<br>
     */
    public static String createAutoFormHtml(String reqUrl, Map<String, String> request,String encoding) {
        StringBuffer sf = new StringBuffer();
        sf.append("<html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset="+encoding+"\"/></head><body>");
        sf.append("<form id = \"pay_form\" action=\"" + reqUrl
                + "\" method=\"post\">");
        if (null != request && 0 != request.size()) {
            Set<Map.Entry<String, String>> set = request.entrySet();
            Iterator<Map.Entry<String, String>> it = set.iterator();
            while (it.hasNext()) {
                Map.Entry<String, String> ey = it.next();
                String key = ey.getKey();
                String value = ey.getValue();
                sf.append("<input type=\"hidden\" name=\"" + key + "\" id=\""
                        + key + "\" value=\"" + value + "\"/>");
            }
        }
        sf.append("</form>");
        sf.append("</body>");
        sf.append("<script type=\"text/javascript\">");
        sf.append("document.all.pay_form.submit();");
        sf.append("</script>");
        sf.append("</html>");
        return sf.toString();
    }



    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
}
