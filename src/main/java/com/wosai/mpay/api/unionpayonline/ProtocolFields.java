package com.wosai.mpay.api.unionpayonline;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/6/6.
 */
public class ProtocolFields {
    public static final String VERSION = "version";// 版本号 固定填写5.1.0
    public static final String ENCODING = "encoding";// 编码方式 默认取值：UTF-8
    public static final String SIGNATURE = "signature";// 签名 填写对报文摘要的签名
    public static final String SIGN_METHOD = "signMethod";// 签名方法 取值： 非对称签名： 01（表示采用RSA签名） HASH表示散列算法 11：支持散列方式验证SHA-256 12：支持散列方式验证SM3
    public static final String ACCESS_TYPE = "accessType";// 接入类型 0：商户直连接入 1：收单机构接入 2：平台商户接入
    public static final String MER_ID = "merId";// 商户代码
    public static final String BIZ_TYPE = "bizType";// 产品类型
    public static final String CERT_ID = "certId";// 证书ID


}
