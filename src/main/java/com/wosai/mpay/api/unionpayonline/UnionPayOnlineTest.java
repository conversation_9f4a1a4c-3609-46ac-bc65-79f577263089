package com.wosai.mpay.api.unionpayonline;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/6/6.
 */
public class UnionPayOnlineTest {
    public static final String MER_ID = "";
    public static final String CERT_ID = "";
    public static final String PRIVATE_KEY = "";
    public static final String QUERY = "https://gateway.95516.com/gateway/api/queryTrans.do";
    public static final String PRECREATE = "https://gateway.95516.com/gateway/api/frontTransReq.do";

    public static void main(String[] args) throws Exception{
//        query();
//        precreate();
    }

    public static void query(){
        RequestBuilder builder = getDefaultRequestBuilder();
        builder.set(BusinessFields.TXN_TIME, "20190610104543");
        builder.set(BusinessFields.TXN_TYPE, UnionPayOnlineConstants.TXN_TYPE_QUERY);
        builder.set(BusinessFields.TXN_SUB_TYPE, UnionPayOnlineConstants.TXN_SUB_TYPE_00);
        builder.set(BusinessFields.ORDER_ID, "test1560134743459");
        UnionPayOnlineClient client = new UnionPayOnlineClient(1000, 5000);
        try {
            Map<String,String> response = client.call(QUERY, PRIVATE_KEY, builder.build());
            System.out.println(response);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public static void precreate(){
        RequestBuilder builder = getDefaultRequestBuilder();
        builder.set(BusinessFields.TXN_TIME, getTxTime());
        builder.set(BusinessFields.TXN_AMT, "3");
        builder.set(BusinessFields.CURRENCY_CODE, UnionPayOnlineConstants.CURRENCY_CODE_CNY);
        builder.set(BusinessFields.TXN_TYPE, UnionPayOnlineConstants.TXN_TYPE_PRECREATE);
        builder.set(BusinessFields.TXN_SUB_TYPE, UnionPayOnlineConstants.TXN_SUB_TYPE_01);
        builder.set(BusinessFields.CHANNEL_TYPE, UnionPayOnlineConstants.CHANNEL_TYPE_PC); //默认pc, 允许上送支持 手机网银支付
        builder.set(BusinessFields.ORDER_ID, getOrderId());
        builder.set(BusinessFields.ORDER_DESC, "矿泉水");
        builder.set(BusinessFields.FRONT_URL, "http://requestbin.net/r/vyxbyrvy");
        builder.set(BusinessFields.BACK_URL, "http://requestbin.net/r/vyxbyrvy");
        builder.set(BusinessFields.ORDER_TIMEOUT, "1"); //5m 上送会报错
        builder.set(BusinessFields.PAY_TIMEOUT, new SimpleDateFormat("yyyyMMddHHmmss").format(new Date().getTime() + 10 * 60 * 1000)); //10m
        Map<String,String> request = builder.build();
        try {
            UnionPayOnlineClient.addSignInfo(PRIVATE_KEY, request);
            String formString = UnionPayOnlineClient.createAutoFormHtml(PRECREATE, request, UnionPayOnlineConstants.CHARSET_UTF8);
            System.out.println(formString);
        } catch (Exception e) {
            e.printStackTrace();
        }



    }

    public static String getTxTime(){
        return new SimpleDateFormat(UnionPayOnlineConstants.DATE_TIME_FORMAT).format(new Date());
    }

    public static String getOrderId(){
        return "test" + System.currentTimeMillis();
    }



    public static RequestBuilder getDefaultRequestBuilder(){
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.ACCESS_TYPE, UnionPayOnlineConstants.ACCESS_TYPE_MERCHANT);
        builder.set(ProtocolFields.MER_ID, MER_ID);
        builder.set(ProtocolFields.CERT_ID, CERT_ID);
        builder.set(ProtocolFields.BIZ_TYPE, UnionPayOnlineConstants.BIZ_TYPE_B2C);
        return builder;
    }
}
