package com.wosai.mpay.api.icbc;

import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.RsaSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.util.Map;

/***
 * @ClassName: ICBCClient
 * @Description:
 * @Auther: dabuff
 * @Date: 2022/2/28 12:37 PM
 */
public class ICBCClient {
    public static final Logger log = LoggerFactory.getLogger(ICBCClient.class);

    private int connectTimeout = 1000;
    private int readTimeout = 8000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }


    public Map<String, Object> call(String serviceUrl, Map<String, Object> request, String privateKey, String publicKey) throws Exception {

        log.info("original request is {}" , JsonUtil.objectToJsonString(request));

        URL url = new URL(serviceUrl);
        String retSigned = null;
        try {
            retSigned = RsaSignature.icbcSign(url.getPath(), request, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, privateKey);
        } catch (Exception e) {
            log.error("加签异常", e);
        }

        request.put(ICBCProtocolFields.SIGN, retSigned);
        String responseStr = HttpClientUtils.doPost(ICBCClient.class.getName(), null, null, serviceUrl, request, ICBCConstants.CHARSET_UTF_8, connectTimeout, readTimeout);
        log.info("original response is {}" , responseStr);

        Map<String, Object> response = JsonUtil.jsonStrToObject(responseStr, Map.class);

        return MapUtils.getMap(response, ICBCProtocolFields.RESPONSE_BIZ_CONTENT);
    }
}
