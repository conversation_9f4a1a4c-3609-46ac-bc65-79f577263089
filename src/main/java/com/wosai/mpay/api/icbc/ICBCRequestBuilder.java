package com.wosai.mpay.api.icbc;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.exception.BuilderException;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;
import java.util.UUID;

/***
 * @ClassName: ICBCRequestBuilder
 * @Description:
 * @Auther: dabuff
 * @Date: 2022/2/28 12:38 PM
 */
public class ICBCRequestBuilder {
    private static ObjectMapper om = new ObjectMapper();
    Map<String, Object> request;
    Map<String, Object> bizContent;

    public ICBCRequestBuilder() {
        request = new TreeMap<>();
        bizContent = new TreeMap<>();
        DateFormat dateFormat = new SimpleDateFormat(ICBCConstants.DATE_TIME_FORMAT);

        request.put(ICBCProtocolFields.FORMAT, ICBCConstants.FORMAT_JSON);  //请求参数格式，仅支持json
        request.put(ICBCProtocolFields.CHARSET, ICBCConstants.CHARSET_UTF_8);  //字符集 ,缺省为UTF-8
        request.put(ICBCProtocolFields.MSG_ID, UUID.randomUUID().toString().replaceAll("-", "")); //消息通讯唯一编号
        request.put(ICBCProtocolFields.SIGN_TYPE, ICBCConstants.SIGN_TYPE_RSA2); //签名类型
        request.put(ICBCProtocolFields.TIMESTAMP, dateFormat.format(new Date())); //交易发生时间戳
    }

    public Map<String, Object> build() throws BuilderException {

        if (!bizContent.isEmpty()) {
            try {
                request.put(ICBCProtocolFields.BIZ_CONTENT, om.writeValueAsString(bizContent));
            }
            catch (JsonProcessingException e) {
                throw new BuilderException("unable to serialize bizContent in JSON format.", e);
            }
        }
        return request;
    }

    public void bizSet(String field, Object value) {
        request.put(field,  value);
    }

    public void bizContentSet(String field, Object value) {
        bizContent.put(field,  value);
    }
}
