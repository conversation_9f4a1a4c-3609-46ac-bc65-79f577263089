package com.wosai.mpay.api.icbc;

/***
 * @ClassName: ICBCProtocolFields
 * @Description:
 * @Auther: dabuff
 * @Date: 2022/2/28 12:37 PM
 */
public class ICBCProtocolFields {

    public static final String APP_ID = "app_id";           //APP的编号,应用在API开放平台注册时生成
    public static final String MSG_ID = "msg_id";           //消息通讯唯一编号，每次调用独立生成，APP级唯一
    public static final String FORMAT = "format";           //请求参数格式，仅支持json
    public static final String CHARSET = "charset";         //字符集 ,缺省为UTF-8
    public static final String ENCRYPT_TYPE = "encrypt_type";     //聚合支付使用，现在仅支持AES，部分接口支持加密，如接口无需加密，参数中此字段无需上送
    public static final String SIGN_TYPE = "sign_type";     //签名类型，为RSA2-RSAWithSha256认证方式
    public static final String SIGN = "sign";               //报文签名
    public static final String TIMESTAMP = "timestamp";     //交易发生时间戳，yyyy-MM-dd HH:mm:ss格式
    public static final String CA = "ca";                   //交易发生时间戳，yyyy-MM-dd HH:mm:ss格式
    public static final String BIZ_CONTENT = "biz_content"; //请求参数的集合
    public static final String RESPONSE_BIZ_CONTENT = "response_biz_content"; //响应参数集合,包含公共和业务参数

}
