package com.wosai.mpay.api.icbc;

import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import com.wosai.pantheon.util.MapUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/***
 * @ClassName: ICBCTest
 * @Description:
 * @Auther: dabuff
 * @Date: 2022/2/28 12:38 PM
 */
public class ICBCTest {
//    private static final String BASE_URL = "https://apipcs3.dccnet.com.cn/";
    private static final String BASE_URL = "https://gw.open.icbc.com.cn/";
//    private static final String PAY_URL = BASE_URL + "api/mybank/pay/qrcode/scanned/pay/V1";
    private static final String PAY_URL = BASE_URL + "api/mybank/pay/qrcode/scanned/pay/V6";
    private static final String QUERY_URL = BASE_URL + "api/mybank/pay/qrcode/scanned/paystatus/V1";
    private static final String REFUND_URL = BASE_URL + "api/mybank/pay/qrcode/scanned/return/V1";
    private static final String REFUND_QUERY_URL = BASE_URL + "api/mybank/pay/qrcode/scanned/returnstatus/V1";
    private static final String PRECREATE_URL = BASE_URL + "api/cardbusiness/aggregatepay/b2c/online/consumepurchase/V1";
    private static final String PRECREATEQUERY_URL = BASE_URL + "api/cardbusiness/aggregatepay/b2c/online/orderqry/V1";
    private static final String PRECREATREFUND_URL = BASE_URL + "api/cardbusiness/aggregatepay/b2c/online/merrefund/V1";
    private static final String PRECREATREFUNDQUERY_URL = BASE_URL + "api/cardbusiness/aggregatepay/b2c/online/refundqry/V1";

//    private static final String APP_ID = "10000000000000226086";
    private static final String APP_ID = "10000000000003219534";
    private static final String APP_ID_test = "10000000000000184513";
//    private static final String MERCHANT_NO = "************";
    private static final String MERCHANT_NO = "************";
//    private static final String MERCHANT_NO = "************";

    private static final String MERCHANT_NO_V6 = "************";
    private static final String MERCHANT_NO_V6_test = "100159933034";

    private static SafeSimpleDateFormat df = new SafeSimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static SafeSimpleDateFormat df2 = new SafeSimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");

    //商户私钥
    private static final String private_key = "";
    private static final String private_key_v6 = "";
//    private static final String private_key_v6_test = "";
    //银行公钥证书
    private static final String public_key  = "";
    private static final String public_key_v6  = "";
    private static final String public_key_v6_test  = "";

    private static void payTest() throws Exception {
        ICBCRequestBuilder requestBuilder = new ICBCRequestBuilder();
        requestBuilder.bizSet(ICBCProtocolFields.APP_ID, APP_ID);
        requestBuilder.bizSet(ICBCProtocolFields.MSG_ID, UUID.randomUUID().toString().replaceAll("-", ""));
        requestBuilder.bizSet(ICBCProtocolFields.SIGN_TYPE, ICBCConstants.SIGN_TYPE_RSA2);
        requestBuilder.bizSet(ICBCProtocolFields.TIMESTAMP, df.format(new Date()));

        requestBuilder.bizContentSet(ICBCBusinessFields.MER_ID, MERCHANT_NO_V6);
        requestBuilder.bizContentSet(ICBCBusinessFields.QR_CODE, "132551252759887133");
        requestBuilder.bizContentSet(ICBCBusinessFields.OUT_TRADE_NO, "icbc20260613000005");
        requestBuilder.bizContentSet(ICBCBusinessFields.TRADE_DATE, "20250613");
        requestBuilder.bizContentSet(ICBCBusinessFields.TRADE_TIME, "171300");
        requestBuilder.bizContentSet(ICBCBusinessFields.ORDER_AMT, "1");


        //sub_appid
        requestBuilder.bizContentSet(ICBCBusinessFields.SUB_APP_ID, "wx72534f3638c59073");
        requestBuilder.bizContentSet("subject", "测试v6");

        Map<String, Object> terminalInfo = new HashMap<>();
        terminalInfo.put("device_type", "11");
        terminalInfo.put("device_id", MERCHANT_NO_V6 + "001");
        requestBuilder.bizContentSet("terminal_info", terminalInfo);

        ICBCClient icbcClient = new ICBCClient();
        Map<String, Object> result = icbcClient.call(PAY_URL, requestBuilder.build(), private_key_v6, public_key_v6);

        System.out.println(result);
    }

    private static void queryTest() throws Exception {

        ICBCRequestBuilder requestBuilder = new ICBCRequestBuilder();
        requestBuilder.bizSet(ICBCProtocolFields.APP_ID, APP_ID);
        requestBuilder.bizSet(ICBCProtocolFields.MSG_ID, UUID.randomUUID().toString().replaceAll("-", ""));
        requestBuilder.bizSet(ICBCProtocolFields.SIGN_TYPE, ICBCConstants.SIGN_TYPE_RSA2);
        requestBuilder.bizSet(ICBCProtocolFields.TIMESTAMP, df.format(new Date()));

        requestBuilder.bizContentSet(ICBCBusinessFields.MER_ID, MERCHANT_NO);
        requestBuilder.bizContentSet(ICBCBusinessFields.OUT_TRADE_NO, "7894259283742272");
        requestBuilder.bizContentSet(ICBCBusinessFields.TRADE_DATE, "20220308");

        ICBCClient icbcClient = new ICBCClient();
        Map<String, Object> result = icbcClient.call(QUERY_URL, requestBuilder.build(), private_key, public_key);

        System.out.println(result);
    }

    private static void refundTest() throws Exception {

        ICBCRequestBuilder requestBuilder = new ICBCRequestBuilder();
        requestBuilder.bizSet(ICBCProtocolFields.APP_ID, APP_ID);
        requestBuilder.bizSet(ICBCProtocolFields.MSG_ID, UUID.randomUUID().toString().replaceAll("-", ""));
        requestBuilder.bizSet(ICBCProtocolFields.SIGN_TYPE, ICBCConstants.SIGN_TYPE_RSA2);
        requestBuilder.bizSet(ICBCProtocolFields.TIMESTAMP, df.format(new Date()));

        requestBuilder.bizContentSet(ICBCBusinessFields.MER_ID, MERCHANT_NO);
        requestBuilder.bizContentSet(ICBCBusinessFields.OUT_TRADE_NO, "icbc20220304000003");
        requestBuilder.bizContentSet(ICBCBusinessFields.REJECT_NO, "icbcrefund202203030006");
        requestBuilder.bizContentSet(ICBCBusinessFields.REJECT_AMT, "1");
//        requestBuilder.bizContentSet(ICBCBusinessFields.OPER_ID, "sqb0001");

        ICBCClient icbcClient = new ICBCClient();
        Map<String, Object> result = icbcClient.call(REFUND_URL, requestBuilder.build(), private_key, public_key);

        System.out.println(result);

    }

    private static void refundQueryTest() throws Exception {

        ICBCRequestBuilder requestBuilder = new ICBCRequestBuilder();
        requestBuilder.bizSet(ICBCProtocolFields.APP_ID, APP_ID);
        requestBuilder.bizSet(ICBCProtocolFields.MSG_ID, UUID.randomUUID().toString().replaceAll("-", ""));
        requestBuilder.bizSet(ICBCProtocolFields.SIGN_TYPE, ICBCConstants.SIGN_TYPE_RSA2);
        requestBuilder.bizSet(ICBCProtocolFields.TIMESTAMP, df.format(new Date()));

        requestBuilder.bizContentSet(ICBCBusinessFields.MER_ID, MERCHANT_NO);
        requestBuilder.bizContentSet(ICBCBusinessFields.OUT_TRADE_NO, "icbc2022030400001");
        requestBuilder.bizContentSet(ICBCBusinessFields.REJECT_NO, "icbcrefund2022030400001");

        ICBCClient icbcClient = new ICBCClient();
        Map<String, Object> result = icbcClient.call(REFUND_QUERY_URL, requestBuilder.build(), private_key, public_key);

        System.out.println(result);

    }

    private static void preCreateTest() throws Exception {
        ICBCRequestBuilder requestBuilder = new ICBCRequestBuilder();
        requestBuilder.bizSet(ICBCProtocolFields.APP_ID, APP_ID);
        requestBuilder.bizSet(ICBCProtocolFields.MSG_ID, UUID.randomUUID().toString().replaceAll("-", ""));
        requestBuilder.bizSet(ICBCProtocolFields.SIGN_TYPE, ICBCConstants.SIGN_TYPE_RSA2);
        requestBuilder.bizSet(ICBCProtocolFields.TIMESTAMP, df.format(new Date()));

        requestBuilder.bizContentSet(ICBCBusinessFields.MER_ID, MERCHANT_NO);
        requestBuilder.bizContentSet(ICBCBusinessFields.OUT_TRADE_NO, "icbcpre202203040001");
        requestBuilder.bizContentSet(ICBCBusinessFields.PAY_MODE, ICBCConstants.PAY_MODE_WX);
        requestBuilder.bizContentSet(ICBCBusinessFields.ACCESS_TYPE, ICBCConstants.ACCESS_TYPE_WX_SUB_APP);
        requestBuilder.bizContentSet(ICBCBusinessFields.MER_PRTCL_NO, MERCHANT_NO);
        requestBuilder.bizContentSet(ICBCBusinessFields.ORIG_DATE_TIME, df2.format(new Date()));
        requestBuilder.bizContentSet(ICBCBusinessFields.DEVICE_INFO, "sqb0001");
        requestBuilder.bizContentSet(ICBCBusinessFields.BODY, "sqb0001-test");
        requestBuilder.bizContentSet(ICBCBusinessFields.FEE_TYPE, ICBCConstants.FEE_TYPE_CNY);
        requestBuilder.bizContentSet(ICBCBusinessFields.SPBILL_CREATE_IP, "127.0.0.1");
        requestBuilder.bizContentSet(ICBCBusinessFields.TOTAL_FEE, "1");
        requestBuilder.bizContentSet(ICBCBusinessFields.ICBC_APPID, APP_ID);
        requestBuilder.bizContentSet(ICBCBusinessFields.UNION_ID, "2088112825196265");
        requestBuilder.bizContentSet(ICBCBusinessFields.MER_URL, "https://gateway.shouqianba.com/notify");
        requestBuilder.bizContentSet(ICBCBusinessFields.NOTIFY_TYPE, ICBCConstants.NOTIFY_TYPE_HS);

        ICBCClient icbcClient = new ICBCClient();
        Map<String, Object> result = icbcClient.call(PRECREATE_URL, requestBuilder.build(), private_key, public_key);

        System.out.println(result);

    }

    private static void preCreateQueryTest() throws Exception {
        ICBCRequestBuilder requestBuilder = new ICBCRequestBuilder();
        requestBuilder.bizSet(ICBCProtocolFields.APP_ID, APP_ID);
        requestBuilder.bizSet(ICBCProtocolFields.MSG_ID, UUID.randomUUID().toString().replaceAll("-", ""));
        requestBuilder.bizSet(ICBCProtocolFields.SIGN_TYPE, ICBCConstants.SIGN_TYPE_RSA2);
        requestBuilder.bizSet(ICBCProtocolFields.TIMESTAMP, df.format(new Date()));

        requestBuilder.bizContentSet(ICBCBusinessFields.MER_ID, MERCHANT_NO);
        requestBuilder.bizContentSet(ICBCBusinessFields.OUT_TRADE_NO, "7894259283563072");
        requestBuilder.bizContentSet(ICBCBusinessFields.ICBC_APPID, APP_ID);
        requestBuilder.bizContentSet(ICBCBusinessFields.DEAL_FLAG, ICBCConstants.DEAL_FLAG_QUERY);

        ICBCClient icbcClient = new ICBCClient();
        Map<String, Object> result = icbcClient.call(PRECREATEQUERY_URL, requestBuilder.build(), private_key, public_key);

        System.out.println(result);

    }

    private static void preCreateRefundTest() throws Exception {
        ICBCRequestBuilder requestBuilder = new ICBCRequestBuilder();
        requestBuilder.bizSet(ICBCProtocolFields.APP_ID, APP_ID);
        requestBuilder.bizSet(ICBCProtocolFields.MSG_ID, UUID.randomUUID().toString().replaceAll("-", ""));
        requestBuilder.bizSet(ICBCProtocolFields.SIGN_TYPE, ICBCConstants.SIGN_TYPE_RSA2);
        requestBuilder.bizSet(ICBCProtocolFields.TIMESTAMP, df.format(new Date()));

        requestBuilder.bizContentSet(ICBCBusinessFields.MER_ID, MERCHANT_NO);
        requestBuilder.bizContentSet(ICBCBusinessFields.OUT_TRADE_NO, "7894259283563072");
        requestBuilder.bizContentSet(ICBCBusinessFields.ICBC_APPID, APP_ID);
        requestBuilder.bizContentSet(ICBCBusinessFields.OUTTRX_SERIAL_NO, "icbcrefund20220308000001");
        requestBuilder.bizContentSet(ICBCBusinessFields.RET_TOTAL_AMT, "1");
        requestBuilder.bizContentSet(ICBCBusinessFields.TRNSC_CCY, ICBCConstants.FEE_TYPE_CNY);

        ICBCClient icbcClient = new ICBCClient();
        Map<String, Object> result = icbcClient.call(PRECREATREFUND_URL, requestBuilder.build(), private_key, public_key);

        System.out.println(result);

    }

    private static void preCreateRefundQueryTest() throws Exception {
        ICBCRequestBuilder requestBuilder = new ICBCRequestBuilder();
        requestBuilder.bizSet(ICBCProtocolFields.APP_ID, APP_ID);
        requestBuilder.bizSet(ICBCProtocolFields.MSG_ID, UUID.randomUUID().toString().replaceAll("-", ""));
        requestBuilder.bizSet(ICBCProtocolFields.SIGN_TYPE, ICBCConstants.SIGN_TYPE_RSA2);
        requestBuilder.bizSet(ICBCProtocolFields.TIMESTAMP, df.format(new Date()));

        requestBuilder.bizContentSet(ICBCBusinessFields.MER_ID, MERCHANT_NO);
        requestBuilder.bizContentSet(ICBCBusinessFields.OUT_TRADE_NO, "7894259283563072");
        requestBuilder.bizContentSet(ICBCBusinessFields.OUTTRX_SERIAL_NO, "icbcrefund20220308000001");

        ICBCClient icbcClient = new ICBCClient();
        Map<String, Object> result = icbcClient.call(PRECREATREFUNDQUERY_URL, requestBuilder.build(), private_key, public_key);

        System.out.println(result);

    }

    public static void main(String[] args) throws Exception {
        payTest();
//        queryTest();
//        refundTest();
//        refundQueryTest();
//        preCreateTest();
//        preCreateQueryTest();
//        preCreateRefundTest();
//        preCreateRefundQueryTest();
    }
}
