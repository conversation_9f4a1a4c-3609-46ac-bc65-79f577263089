package com.wosai.mpay.api.icbc;

import com.google.common.collect.Lists;

import java.util.List;

/***
 * @ClassName: ICBCConstants
 * @Description:
 * @Auther: dabuff
 * @Date: 2022/2/28 12:36 PM
 */
public class ICBCConstants {

    /** 默认时间格式 **/
    public static final String DATE_SIMPLE_FORMAT = "yyyyMMdd";
    public static final String DATE_TIME_SIMPLE_FORMAT = "HHmmss";
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_TIME_FORMAT_WITH_T = "yyyy-MM-dd'T'HH:mm:ss";

    public static final String FORMAT_JSON = "json";
    public static final String CHARSET_UTF_8 = "UTF-8";
    public static final String SIGN_TYPE_RSA2   = "RSA2";
    public static final String FEE_TYPE_CNY = "001"; //交易币种，目前工行只支持使用人民币（001）支付
    public static final String NOTIFY_SUCCESS = "success"; //结果通知返回成功

    /* 证件类型 cust_cert_type */
    public static final String CUST_CERT_TYPE_ID = "0"; //0-身份证
    public static final String CUST_CERT_TYPE_PASSPORT = "1"; //1-护照
    public static final String CUST_CERT_TYPE_OFFICER = "2"; //2-军官证
    public static final String CUST_CERT_TYPE_SOLDIER = "3"; //3-士兵证
    public static final String CUST_CERT_TYPE_HOME_RETURN = "4"; //4-回乡证
    public static final String CUST_CERT_TYPE_TEMP_ID = "5"; //5-临时身份证
    public static final String CUST_CERT_TYPE_FAMILY_REGISTER = "6"; //6-户口本
    public static final String CUST_CERT_TYPE_OTHER = "7"; //7-其他
    public static final String CUST_CERT_TYPE_POLICE = "9"; //9-警官证
    public static final String CUST_CERT_TYPE_IDENTITY = "10"; //10-识别证
    public static final String CUST_CERT_TYPE_DRIVER = "11"; //11-驾驶执照

    /* 被扫交易结果标志 pay_status */
    public static final String PAY_STATUS_CREATE_FAIL = "-1"; //-1:下单失败
    public static final String PAY_STATUS_PROCESSING = "0"; //0：支付中
    public static final String PAY_STATUS_SUCCESS = "1"; //1：支付成功
    public static final String PAY_STATUS_FAIL = "2"; //2：支付失败

    /* 退款查询显示无此交易 */
    public static final String REFUND_RETURN_MSG_NOT_EXIST = "无此交易";

    /* 主扫交易结果标志 pay_status */
    public static final String PRECREATE_PAY_STATUS_SUCCESS = "0"; //0：支付成功
    public static final String PRECREATE_PAY_STATUS_FAIL = "1"; //1：支付失败
    public static final String PRECREATE_PAY_STATUS_UNKNOWN = "2"; //2：未知

    /* 主扫退款查询结果标志 pay_status */
    public static final String PRECREATE_REFUND_STATUS_SUCCESS = "0"; //0-退货成功
    public static final String PRECREATE_REFUND_STATUS_FAIL = "1"; //1-退货失败
    public static final String PRECREATE_REFUND_STATUS_UNKNOWN = "2"; //2-退货状态未知

    /* 渠道标识 channel */
    public static final String CHANNEL_WX = "91"; //91-微信支付
    public static final String CHANNEL_ZFB = "92"; //92-支付宝
    public static final String CHANNEL_UNION = "93"; //93-银联二维码
    public static final String CHANNEL_DCEP = "94"; //94-数字人民币
    public static final String CHANNEL_ICBC = "99"; //99-工银二维码

    /* 支付方式 pay_mode */
    public static final String PAY_MODE_WX = "9"; //9-微信
    public static final String PAY_MODE_ZFB = "10"; //10-支付宝
    public static final String PAY_MODE_UNION = "13"; //13-云闪付

    /* 收单接入方式 access_type */
    public static final String ACCESS_TYPE_APP = "5"; //5-APP
    public static final String ACCESS_TYPE_WX_SUB_APP = "7"; //7-微信公众号
    public static final String ACCESS_TYPE_ZFB = "8"; //8-支付宝生活号
    public static final String ACCESS_TYPE_WX_MINI = "9"; //9-微信小程序

    /* 通知类型 notify_type */
    public static final String NOTIFY_TYPE_HS = "HS"; //取值“HS”：在交易完成后将通知信息，主动发送给商户，发送地址为mer_url指定地址
    public static final String NOTIFY_TYPE_AG = "AG"; //取值“AG”：在交易完成后不通知商户

    /* 结果发送类型 result_type */
    //通知方式为HS时有效
    public static final String RESULT_TYPE_ALL_NOTIFY = "0"; //取值“0”：无论支付成功或者失败，银行都向商户发送交易通知信息,默认是”0”
    public static final String RESULT_TYPE_SUCCESS_NOTIFY = "1"; //取值“1”，银行只向商户发送交易成功的通知信息

    /* 支付方式限定 pay_limit */
    //不上送或上送空表示无限制
    public static final String PAY_LIMIT_NO_CREDIT = "no_credit"; //上送”no_credit“表示不支持信用卡支付
    public static final String PAY_LIMIT_NO_BALANCE = "no_balance"; //上送“no_balance”表示仅支持银行卡支付；

    /* 交易类型 trade_type */
    //支付方式为微信时返回
    public static final String TRADE_TYPE_JSAPI = "JSAPI"; //JSAPI ：公众号支付、小程序支付
    public static final String TRADE_TYPE_APP = "APP"; //APP：APP支付；

    /* 发卡行标志 card_flag */
    public static final String CARD_FLAG_UNION = "1"; //1：银联卡
    public static final String CARD_FLAG_INNER = "2"; //2：本行卡
    public static final String CARD_FLAG_OUTER = "3"; //3：外卡；

    /* 卡借贷标志 decr_flag */
    public static final String DECR_FLAG_DEBIT = "1"; //1-借
    public static final String DECR_FLAG_CREDIT = "2"; //2-贷

    /* 操作标志 deal_flag */
    public static final String DEAL_FLAG_QUERY = "0"; //0-查询
    public static final String DEAL_FLAG_CLOSE = "1"; //1-关单

    /* 设备类型: 终端设备类型 device_type */
    public static final String DEVICE_TYPE_11 = "11"; //条码支付辅助受理终端

    /* 返回码 return_code */
    public static final String RETURN_CODE_SUCCESS = "0"; //成功
    public static final String RETURN_CODE_PARAM_ILLEGAL = "400011"; //参数非法，原因可能为app id为空、app id非法、签名为空、应用系统时间与API平台系统时间不在限定差值以内、时间戳非法
    //参数检查失败，原因可能为：
    // 1、被扫支付场景qrCode、tradeDate、tradeTime、orderAmt缺少或为空,以及mer_id缺少
    // 2、查询场景mer_id缺少
    // 3、退款场景mer_id缺少， out_trade_no 和 order_id 均为空或缺少， reject_no 为空， rejectAmt 缺少
    // 4、退款查询场景 reject_no 为空， out_trade_no 缺少
    public static final String RETURN_CODE_PARAM_CHECK_FAILED = "92200001";
    // 1、退款 商户系统流水重复 重复的 reject_no
    // 2、云闪付 被扫 消费下单调用商户信息查询异常：该受理卡/账户类型不在协议记录中的参数属性代码#L0918的协议条件取值范围内
    // 3、云闪付 被扫 验证签名失败[9110009]
    // 4、云闪付 被扫 银联二维码被扫支付调用协议最大交易金额判断服务失败：单笔交易金额超过协议允许最大交易金额（49988）
    // 5、云闪付 被扫 txnAmt 为0 交易失败，详情请咨询您的发卡行[1013101]
    // 6、云闪付 被扫 txnAmt 为负 报文格式错误txnAmt[9199010]
    // 7、云闪付 被扫 out_trade_no 重复使用 调用检查订单是否可以继续支付服务异常:订单已支付
    // 8、云闪付 被扫 消费下单调用商户信息查询异常：商户不能受理信用卡，请联系收单行
    public static final String RETURN_CODE_CHECK_FAILED = "00100018"; //
    public static final String RETURN_CODE_MER_ID_INVALID = "10250002"; //查询商户与APPID对照信息验证不通过：检查该商户编号, 错误的mer_id
    public static final String RETURN_CODE_QR_CODE_INVALID = "92200004"; //无效的付款码类型, qr_code类型不属于支付宝、微信、银联云闪付
    public static final String RETURN_CODE_ORDER_AMT_LT_ZERO = "00099992"; //被扫支付 orderamt 为负
    public static final String RETURN_CODE_ORDER_AMT_GT_MERCHANT_SIGNAL_LIMIT = "00095011"; //被扫支付 交易金额超过商户单笔消费限额
    public static final String RETURN_CODE_REPEAT_OUT_TRADE_NO = "92200002"; //被扫支付 重复交易,使用过的out_trade_no
    public static final String RETURN_CODE_OUT_TRADE_NO_AND_ORDER_ID_BOTH_EMPTY = "92200018"; //查询 内部订单号和外部订单号必须输入其中一个, out_trade_no 和 order_id 均为空
    public static final String RETURN_CODE_REFUND_ERROR = "08001374"; //退款 错误的 out_trade_no 或者 out_trade_no 跟 mer_id 不匹配 或者 已全额退款，继续退
    public static final String RETURN_CODE_REFUND_AMT_LT_ZERO = "99992"; //退款 refundamt 为负
    public static final String RETURN_CODE_CAN_NOT_REFUND = "95068"; //退款 refundamt 为 0 或 ACQ.SELLER_BALANCE_NOT_ENOUGH 卖家余额不足
    public static final String RETURN_CODE_REFUND_AMT_OVER_AVAILABLE_AMT = "95026"; //退款 退货金额总和不允许大于原交易金额
    public static final String RETURN_CODE_REFUND_QUERY_ERROR_OUT_TRADE_NO = "00059994"; //退款查询 错误的 OUT_TRADE_NO
    public static final String RETURN_CODE_REFUND_QUERY_ERROR_REJECT_NO = "00060028"; //退款查询 错误的 REJECT_NO
    public static final String RETURN_CODE_REFUND_SERVICE_ERROR = "-500200"; //退款 服务调用失败：null
    // 1、支付查询 无此交易
    // 2、退款查询 错误的 OUT_TRADE_NO 和 REJECT_NO (都不对) 或者 OUT_TRADE_NO，REJECT_NO 均存在，但不对应
    public static final String RETURN_CODE_ORDER_NOT_EXIST = "00095024";
    public static final String RETURN_CODE_ORDER_PAY_IN_PROGRESS = "20000002"; //调SAES_AC通知查询关单ACS服务未知失败通知查询关单接口处理失败:第三方交易状态异常：0，第三方订单待支付
    public static final String RETURN_CODE_ORDER_PAY_IN_PROGRESS_2 = "20000011"; //调SAES_AC通知查询关单ACS服务未知失败通知查询关单接口处理失败:当前交易正在处理中

    /* 支付宝侧异常 */
    public static final String RETURN_CODE_ALIPAY_PROCESS_FAILED = "00095203"; //支付宝侧处理失败

    /* 微信侧异常 */
    public static final String RETURN_CODE_WX_BARCODE_EXPIRED = "00095046"; //二维码已过期
    public static final String RETURN_CODE_WX_PROCESS_FAILED = "00095202"; //微信侧处理失败

    /* 云闪付侧异常 */
    public static final String RETURN_CODE_UNION_REFUND_INVALID = "11000921"; //重复退款 或者 refundAmt = 0，调银联未知失败交易失败，详情请咨询您的发卡行[1013000]
    public static final String RETURN_CODE_UNION_REFUND_AMT_OVER_AVAILABLE_AMT = "00070037"; //剩余可退订单金额不足，不可退货
    public static final String RETURN_CODE_UNION_ALREADY_REFUND = "00070038"; //重复使用 reject_no 调用检查该笔退货是否可以继续服务异常:订单已退款

    public static final String RETURN_CODE_API_NOT_EXIST = "500018"; //访问的API不存在
    public static final String RETURN_CODE_CALL_ILLEGAL = "500020"; //非法调用
    public static final String RETURN_CODE_PUBLIC_KEY_ERROR = "400016"; //app公钥未维护
    public static final String RETURN_CODE_VERIFY_SIGNATURE_ERROR = "400017"; //签名验证失败
    public static final String RETURN_CODE_VERIFY_AUTH_ERROR = "400019"; //授权验证失败
    public static final String RETURN_CODE_TOO_FAST = "500031"; //速率超限
    public static final String RETURN_CODE_OVER_CONCURRENCE = "500032"; //并发超限
    public static final String RETURN_CODE_PROXY_ERROR = "-500041"; //代理异常
    public static final String RETURN_CODE_PROXY_TIMEOUT = "-500042"; //代理超时
    public static final String RETURN_CODE_SIGN_ERROR = "-500044"; //网关签名失败
    public static final String RETURN_CODE_PROPERTY_ERROR = "500043"; //网关配置文件错误，无法从配置文件中读取配置
    public static final String RETURN_CODE_INNER_ERROR = "-500099"; //网关内部异常
    public static final String RETURN_CODE_MERCHANT_NOT_SUPPORT = "93012288"; //商户不支持该交易
    public static final String RETURN_CODE_SIGNAL_LIMIT_NOT_EXIST = "98001706"; //单笔限额不存在
    public static final String RETURN_CODE_DAILY_LIMIT_NOT_EXIST = "98001707"; //日累计限额不存在
    public static final String RETURN_CODE_OVER_SIGNAL_LIMIT = "98000259"; //单笔金额超限
    public static final String RETURN_CODE_TX_AMT_OVER_SIGNAL_LIMIT = "93008725"; //交易金额超过单笔消费限额
    public static final String RETURN_CODE_TX_AMT_OVER_DAILY_LIMIT = "93008726"; //超日累计消费限额
    public static final String RETURN_CODE_OVER_DAILY_LIMIT = "98000515"; //日累计限额超限
    public static final String RETURN_CODE_QRCODE_PAY_OVER_DAILY_LIMIT = "98001849"; //二维码支付日累计金额超限
    public static final String RETURN_CODE_OVER_TRANSACTION_LIMIT = "93008727"; //超商户交易限额
    public static final String RETURN_CODE_OVER_MONTHLY_LIMIT = "98000261"; //月累计金额超限
    public static final String RETURN_CODE_REPEAT_RECORD = "1207"; //此记录已存在
    public static final String RETURN_CODE_REPEAT_ORDER = "98000031"; //订单不能重复提交
    public static final String RETURN_CODE_RETRY_ORDER = "98000807"; //请重新下单支付
    public static final String RETURN_CODE_REFUND_OVERTIME = "98000824"; //该交易超退货期限
    public static final String RETURN_CODE_REFUND_AMT_LARGER = "98001401"; //金额大于可退金额
    public static final String RETURN_CODE_REFUND_IN_PROGRESSING = "5920006"; //正在退款中
    public static final String RETURN_CODE_EMPTY_REFUND_AMT = "5920007"; //退款金额不能为空
    public static final String RETURN_CODE_REFUND_PARTIAL_IS_NOT_ALLOWED = "5920011"; //当日只能全款退款
    public static final String RETURN_CODE_QUERY_ORDER_NOT_EXIST = "5930001"; //未查询到该笔订单
    public static final String RETURN_CODE_CREATE_ORDER_OVERTIME = "81200001"; //下单时间超过商户上送的订单有效期范围
    public static final String RETURN_CODE_REPEAT_ORDER_NO = "81200003"; //商户订单号重复
    public static final String RETURN_CODE_ORDER_ALREADY_EXIST = "81200004"; //该订单已存在
    public static final String RETURN_CODE_CALL_APPLICATION_FAILED = "3203"; //调用主机应用程序失败

    public static final List<String> PROTOCOL_ERROR_CODE_LIST = Lists.newArrayList(RETURN_CODE_PARAM_ILLEGAL, RETURN_CODE_PARAM_CHECK_FAILED,
            RETURN_CODE_MER_ID_INVALID, RETURN_CODE_API_NOT_EXIST, RETURN_CODE_CALL_ILLEGAL, RETURN_CODE_PUBLIC_KEY_ERROR, RETURN_CODE_VERIFY_SIGNATURE_ERROR,
            RETURN_CODE_OUT_TRADE_NO_AND_ORDER_ID_BOTH_EMPTY, RETURN_CODE_VERIFY_AUTH_ERROR, RETURN_CODE_SIGN_ERROR, RETURN_CODE_PROPERTY_ERROR,
            RETURN_CODE_MERCHANT_NOT_SUPPORT, RETURN_CODE_SIGNAL_LIMIT_NOT_EXIST, RETURN_CODE_DAILY_LIMIT_NOT_EXIST,
            RETURN_CODE_EMPTY_REFUND_AMT, RETURN_CODE_REFUND_PARTIAL_IS_NOT_ALLOWED );

    public static final List<String> UNKNOWN_ERROR_CODE_LIST = Lists.newArrayList(RETURN_CODE_TOO_FAST, RETURN_CODE_OVER_CONCURRENCE,
            RETURN_CODE_PROXY_ERROR, RETURN_CODE_PROXY_TIMEOUT, RETURN_CODE_INNER_ERROR, RETURN_CODE_CALL_APPLICATION_FAILED, RETURN_CODE_REFUND_SERVICE_ERROR,
            RETURN_CODE_ORDER_PAY_IN_PROGRESS, RETURN_CODE_ORDER_PAY_IN_PROGRESS_2);

    public static final List<String> FAIL_ERROR_CODE_LIST = Lists.newArrayList(RETURN_CODE_QR_CODE_INVALID, RETURN_CODE_ORDER_AMT_LT_ZERO, RETURN_CODE_ORDER_AMT_GT_MERCHANT_SIGNAL_LIMIT,
            RETURN_CODE_REPEAT_OUT_TRADE_NO, RETURN_CODE_REFUND_ERROR, RETURN_CODE_REFUND_AMT_LT_ZERO, RETURN_CODE_CAN_NOT_REFUND, RETURN_CODE_REFUND_AMT_OVER_AVAILABLE_AMT,
            RETURN_CODE_REFUND_QUERY_ERROR_OUT_TRADE_NO, RETURN_CODE_REFUND_QUERY_ERROR_REJECT_NO, RETURN_CODE_ORDER_NOT_EXIST, RETURN_CODE_ALIPAY_PROCESS_FAILED,
            RETURN_CODE_WX_BARCODE_EXPIRED, RETURN_CODE_WX_PROCESS_FAILED, RETURN_CODE_UNION_REFUND_INVALID, RETURN_CODE_UNION_REFUND_AMT_OVER_AVAILABLE_AMT,
            RETURN_CODE_UNION_ALREADY_REFUND, RETURN_CODE_OVER_SIGNAL_LIMIT, RETURN_CODE_TX_AMT_OVER_SIGNAL_LIMIT, RETURN_CODE_TX_AMT_OVER_DAILY_LIMIT,
            RETURN_CODE_OVER_DAILY_LIMIT, RETURN_CODE_QRCODE_PAY_OVER_DAILY_LIMIT, RETURN_CODE_OVER_TRANSACTION_LIMIT, RETURN_CODE_OVER_MONTHLY_LIMIT,
            RETURN_CODE_REPEAT_RECORD, RETURN_CODE_REPEAT_ORDER, RETURN_CODE_RETRY_ORDER, RETURN_CODE_REFUND_OVERTIME, RETURN_CODE_REFUND_AMT_LARGER,
            RETURN_CODE_QUERY_ORDER_NOT_EXIST, RETURN_CODE_CREATE_ORDER_OVERTIME, RETURN_CODE_REPEAT_ORDER_NO, RETURN_CODE_ORDER_ALREADY_EXIST);

}
