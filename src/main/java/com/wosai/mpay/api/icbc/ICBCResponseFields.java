package com.wosai.mpay.api.icbc;

/***
 * @ClassName: ICBCResponseFields
 * @Description:
 * @Auther: dabuff
 * @Date: 2022/2/28 12:37 PM
 */
public class ICBCResponseFields {

    /* 公共响应参数 */
    public static final String RETURN_CODE = "return_code"; //返回码，交易成功返回0，其他表示业务报错
    public static final String RETURN_MSG = "return_msg"; //返回信息
    public static final String OUT_TRADE_NO = "out_trade_no"; //商户系统订单号
    public static final String CARD_NO = "card_no"; //屏蔽后的银行卡号
    public static final String ORDER_ID = "order_id"; //行内系统订单号,15位商户+0+3位渠道类型+4位太阳日+7位顺序号

    /* 被扫支付、被扫支付查询及聚合支付共有响应参数 */
    public static final String TOTAL_AMT = "total_amt"; //订单总金额,单位：分
    public static final String PAY_TIME = "pay_time"; //支付完成时间,yyyyMMdd格式

    /* 被扫支付及被扫支付查询响应参数 */
    public static final String PAY_STATUS = "pay_status"; //交易结果标志,-1:下单失败，0：支付中，1：支付成功，2：支付失败
    public static final String POINT_AMT = "point_amt"; //积分抵扣金额  单位：分
    public static final String ECOUPON_AMT = "ecoupon_amt "; //电子券抵扣金额  单位：分
    public static final String MER_DISC_AMT = "mer_disc_amt"; //优惠立减金额（商户部分） 单位：分
    public static final String COUPON_AMT = "coupon_amt"; //优惠券金额  单位：分
    public static final String BANK_DISC_AMT = "bank_disc_amt"; //银行补贴金额  单位：分
    public static final String PAYMENT_AMT = "payment_amt"; //用户实际扣减金额  单位：分
    public static final String TOTAL_DISC_AMT = "total_disc_amt"; //总优惠金额
    public static final String BANK_NAME = "bank_name"; //发卡行名称
    public static final String CHANNEL = "channel"; //渠道标识,91-微信支付、92-支付宝、93-银联二维码、99-工银二维码
    public static final String ATTACH = "attach"; //商户附加数据
    public static final String TP_CUST_ID = "tp_cust_id"; //第三方支付机构的客户编号
    public static final String TRX_SER_NO = "trx_ser_no"; //工行交易检索号
    public static final String TP_ORDER_ID = "tp_order_id"; //第三方订单号
    public static final String BUYER_LOGON_ID = "buyer_logon_id"; //支付宝账号
    public static final String SUB_OPEN_ID = "sub_open_id"; //微信子用户标识
    public static final String BANK_TYPE = "bank_type"; //付款机构编码

    /* 退货及退货查询响应参数 */
    public static final String REJECT_NO = "reject_no"; //退款编号
    public static final String REAL_REJECT_AMT = "real_reject_amt"; //实退金额(现金部分), 单位：分
    public static final String REJECT_AMT = "reject_amt"; //退款总金额, 单位：分
    public static final String REJECT_POINT = "reject_point"; //积分退款金额, 单位：分
    public static final String REJECT_ECOUPON = "reject_ecoupon"; //电子券退款金额, 单位：分
    public static final String REJECT_MER_DISC_AMT = "reject_mer_disc_amt"; //本次所退优惠立减金额（商户部分）, 单位：分
    public static final String REJECT_BANK_DISC_AMT = "reject_bank_disc_amt"; //本次退款银行补贴金额, 单位：分
    public static final String ORDER_CHANNEL = "order_channel"; //渠道标识,91-微信支付、92-支付宝、93-银联二维码、99-工银二维码、94-数字人民币
    public static final String TRADE_NO = "trace_no"; //工行交易检索号

    /* 聚合支付响应参数 */
    public static final String MER_ID = "mer_id"; //商户编号
    public static final String PAY_MODE = "pay_mode"; //支付方式，1-刷卡支付；2-工行快捷支付；3-网银支付；4-新型无卡；5-简单无卡；6-银联快捷支付；7-3D支付；8-ApplePay；9-微信；10-支付宝；11-非3D支付；12-银联系扫码付；13-云闪付
    public static final String ACCESS_TYPE = "access_type"; //收单接入方式，5-APP，7-微信公众号，8-支付宝生活号，9-微信小程序
    public static final String CARD_KIND = "card_kind"; //卡种，90-VISA外卡、91-MASTER外卡、92-运通外卡、93-大来外卡、94-JCB外卡
    public static final String TRADE_TYPE = "trade_type"; //支付方式为微信时返回，交易类型，JSAPI ：公众号支付、小程序支付；APP：APP支付；
    public static final String WX_DATA_PACKAGE = "wx_data_package"; //支付方式为微信时返回，微信数据包，用于之后唤起微信支付。详细内容请参考微信支付开放平台接口
    public static final String ZFB_DATA_PACKAGE = "zfb_data_package"; //支付方式为支付宝时返回，支付宝数据包，用于之后唤起支付宝支付。详细内容请参考支付宝开放平台接口
    public static final String UNION_DATA_PACKAGE = "union_data_package"; //支付方式为云闪付时返回，云闪付受理订单号，用于之后进行银联云闪付支付。详细内容请参考银联开放平台的手机支付控件接口
    public static final String THIRD_PARTY_RETURN_CODE = "third_party_return_code"; //第三方报错时返回的报错码
    public static final String THIRD_PARTY_RETURN_MSG = "third_party_return_msg"; //第三方报错时返回的报错信息

    /* WX_DATA_PACKAGE */
    public static final String APPID = "appid";
    public static final String PACKAGE = "package";
    public static final String SIGN = "sign";
    public static final String SIGNTYPE = "signType";
    public static final String NONCESTR = "noncestr";
    public static final String TIMESTAMP = "timestamp";

    public static final String THIRD_TRADE_NO = "third_trade_no"; //第三方订单号
    public static final String OPEN_ID = "open_id"; //openID

    /* 聚合支付退款响应参数 */
    public static final String INTRX_SERIAL_NO = "intrx_serial_no"; //退货工行流水号

    /* 聚合支付退款查询响应参数 */
    public static final String OUTTRX_SERIAL_NO = "outtrx_serial_no"; //商户系统退货时生成的退款编号

}
