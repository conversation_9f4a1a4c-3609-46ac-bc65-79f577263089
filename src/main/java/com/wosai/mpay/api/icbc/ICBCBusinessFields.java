package com.wosai.mpay.api.icbc;

/***
 * @ClassName: ICBCBusinessFields
 * @Description:
 * @Auther: dabuff
 * @Date: 2022/2/28 12:36 PM
 */
public class ICBCBusinessFields {

    /* 公共请求参数 */
    public static final String MER_ID = "mer_id"; //商户、部门编号
    public static final String OUT_TRADE_NO = "out_trade_no"; //商户订单号

    /* 被扫支付及聚合支付共有 */
    public static final String ATTACH = "attach"; //商户附加信息,目前生产仅支持100位，聚合可以支持200位

    /* 被扫支付 */
    public static final String QR_CODE = "qr_code"; //被扫付款码
    public static final String ORDER_AMT = "order_amt"; //订单金额,单位：分
    public static final String SUB_APP_ID = "sub_app_id"; //子商户应用号
    public static final String TRADE_DATE = "trade_date"; //交易日期, 格式:YYYYMMDD
    public static final String TRADE_TIME = "trade_time"; //交易时间,格式:HHMMSS，暂不使用
    public static final String CUST_NAME = "cust_name"; //客户姓名 (仅支持支付宝上送,其他支付方式请勿上送)
    public static final String CUST_CERT_TYPE = "cust_cert_type"; //证件类型,0-身份证;1-护照;2-军官证;3-士兵证;4-回乡证;5-临时身份证;6-户口本;7-其他;9-警官证;10-识别证;11-驾驶执照; (仅支持支付宝上送,其他支付方式请勿上送)
    public static final String CUST_CERT_NO = "cust_cert_no"; //证件号码 (仅支持支付宝上送,其他支付方式请勿上送)
    public static final String SUBJECT = "subject"; //订单标题，128字节长度
    public static final String TERMINAL_INFO = "terminal_info"; //商户侧受理终端信息
    //设备类型: 终端设备类型，受理方可参考终端注册时的设备类型填写，取值如下：
    // 01：自动柜员机（含 ATM 和 CDM）和多媒体自助终端
    // 02：传统 POS
    // 03：mPOS
    // 04：智能 POS
    // 05：II 型固定电话
    // 06：云闪付终端；
    // 07：保留使用；
    // 08：手机 POS；
    // 09：刷脸付终端；
    // 10：条码支付受理终端；
    // 11：条码支付辅助受理终端；
    // 12：行业终端（公交、地铁用于指定行业的终端）；
    // 13：MIS 终端；
    public static final String TERMINAL_INFO_DEVICE_TYPE = "device_type";
    // 终端设备号: 收单机构为商户终端分配的唯一编号。
    // 工行商户编号+001：工行商户编号20000001+001=2000001001
    public static final String TERMINAL_INFO_DEVICE_ID = "device_id";

    /* 被扫支付查询 */
    public static final String ORDER_ID = "order_id"; //工行订单号（工行订单号和商户订单号必输其中一个）

    /* 退货及退货查询 */
    public static final String CUST_ID = "cust_id"; //用户标识
    public static final String REJECT_NO = "reject_no"; //商户系统生成的退款编号，每次部分退款需生成不同的退款编号
    public static final String OPER_ID = "oper_id";
    public static final String REJECT_AMT = "reject_amt"; //单位：分 （仅退货）

    /* 聚合支付B2C(WAP) */
    public static final String PAY_MODE = "pay_mode"; //支付方式，9-微信；10-支付宝；13-云闪付
    public static final String ACCESS_TYPE = "access_type"; //收单接入方式，5-APP，7-微信公众号，8-支付宝生活号，9-微信小程序
    public static final String MER_PRTCL_NO = "mer_prtcl_no"; //收单产品协议编号
    public static final String ORIG_DATE_TIME = "orig_date_time"; //交易日期时间，格式为yyyy-MM-dd’T’HH:mm:ss
    public static final String DEVICE_INFO = "decive_info"; //设备号
    //商品描述，商品描述交易字段格式根据不同的应用场景按照以下格式：
    // 1）PC网站：传入浏览器打开的网站主页title名-实际商品名称 ；
    // 2）公众号：传入公众号名称-实际商品名称；
    // 3）H5：传入浏览器打开的移动网页的主页title名-实际商品名称；
    // 4）线下门店：门店品牌名-城市分店名-实际商品名称；
    // 5）APP：传入应用市场上的APP名字-实际商品名称
    public static final String BODY = "body";
    public static final String FEE_TYPE = "fee_type"; //交易币种，目前工行只支持使用人民币（001）支付
    public static final String SPBILL_CREATE_IP = "spbill_create_ip"; //用户端IP
    public static final String TOTAL_FEE = "total_fee"; //订单金额，单位为分
    public static final String MER_URL = "mer_url"; //异步通知商户URL，端口必须为443或80
    public static final String SHOP_APPID = "shop_appid"; //商户在微信开放平台注册的APPID，支付方式为微信时不能为空
    public static final String ICBC_APPID = "icbc_appid"; //商户在工行API平台的APPID
    public static final String OPEN_ID = "open_id"; //第三方用户标识，商户在微信公众号内或微信小程序内接入时必送，即access_type为7或9时，上送用户在商户APPID下的唯一标识；商户通过支付宝生活号接入时不送
    public static final String UNION_ID = "union_id"; //第三方用户标识，商户在支付宝生活号接入时必送，即access_type为8时，上送用户的唯一标识；商户通过微信公众号内或微信小程序接入时不送
    public static final String EXPIRE_TIME = "expire_time"; //订单失效时间，单位为秒，建议大于60秒
    public static final String NOTIFY_TYPE = "notify_type"; //通知类型，表示在交易处理完成后把交易结果通知商户的处理模式。 取值“HS”：在交易完成后将通知信息，主动发送给商户，发送地址为mer_url指定地址； 取值“AG”：在交易完成后不通知商户
    public static final String RESULT_TYPE = "result_type"; //结果发送类型，通知方式为HS时有效。取值“0”：无论支付成功或者失败，银行都向商户发送交易通知信息；取值“1”，银行只向商户发送交易成功的通知信息。默认是”0”
    public static final String PAY_LIMIT = "pay_limit"; //支付方式限定，上送”no_credit“表示不支持信用卡支付；上送“no_balance”表示仅支持银行卡支付；不上送或上送空表示无限制
    public static final String ORDER_APD_INF = "order_apd_inf"; //订单附加信息
    public static final String DETAIL = "detail"; //商品详细描述，对于使用单品优惠的商户，该字段必须按照规范上传。微信与支付宝的规范不同，请根据支付方式对应相应的规范上送，详细内容参考文末说明
    public static final String RETURN_URL = "return_url"; //支付成功回显页面，支付成功后，跳转至该页面显示。当access_type=5且pay_mode=10才有效
    public static final String QUIT_URL = "quit_url"; //用户付款中途退出返回商户网站的地址（仅对浏览器内支付时有效）当access_type=5且pay_mode=10才有效

    /* 聚合支付B2C查询 */
    public static final String DEAL_FLAG = "deal_flag"; //操作标志，0-查询；1-关单

    /* 聚合支付B2C退款和退款查询 */
    public static final String OUTTRX_SERIAL_NO = "outtrx_serial_no"; //退货流水号，商户系统生成的退款编号，每次部分退款需生成不同的退款编号
    public static final String RET_TOTAL_AMT = "ret_total_amt"; //退款金额，单位：分
    public static final String TRNSC_CCY = "trnsc_ccy"; //交易币种，目前只支持人民币，送001

}