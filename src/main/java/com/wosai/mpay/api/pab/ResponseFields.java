package com.wosai.mpay.api.pab;

/**
 * <AUTHOR>
 * @Date 2023/12/14、15:05
 **/


public class ResponseFields {

    public static final String S_TRANS_CLASS = "sTransClass";
    // 服务区别码
    public static final String S_SERVICE_DISTINCT_CODE = "sServiceDistinctCode";
    // 交易时间
    public static final String S_TRANSMISSION_DATE_TIME = "sTransmsnDateTime";
    // 商户流水号
    public static final String S_TERM_SSN = "sTermSSN";
    // 应答码
    public static final String S_RESP_CODE = "sRespCode";
    // 交易状态描述
    public static final String S_TRANS_STATE_MSG = "sTransStateMsg";
    // 商户号
    public static final String S_CARD_ACCPTR_ID = "sCardAccptrId";
    // 终端号
    public static final String S_CARD_ACCPTR_TERMNL_ID = "sCardAccptrTermnlId";
    // 交易金额
    public static final String S_AMT_TRANS = "sAmtTrans";
    // 交易币种
    public static final String S_CURRCY_CODE_TRANS = "sCurrcyCodeTrans";
    // 优惠金额
    public static final String S_AMT_COUPON = "sAmtCoupon";
    // 检索参考号
    public static final String S_RETRIVL_REF_NUM = "sRetrivlRefNum";
    // 受理理机构标识码
    public static final String S_ACQ_INST_ID_CODE = "sAcqInstIdCode";
    // 交易介质
    public static final String S_TRANS_MEDIA = "sTransMedia";
    // 交易日志跟踪号
    public static final String S_TRAN_LOG_TRACE_NO = "sTranLogTraceNo";
    // 商户订单号/外部订单号
    public static final String S_OUT_ORDER_NO = "sOutOrderNo";
    // 用户子标识
    public static final String S_SUB_OPENID = "sSubOpenid";
    // 银联订单号
    public static final String S_TRADE_NO = "sTradeNo";
    // 付款凭证号
    public static final String S_VOUCHER_NUM = "sVoucherNum";
    // 微信优惠信息
    public static final String S_PROMOTION_DETAIL = "sPromotionDetail";
    // 加解密方式
    public static final String S_ENCRYPT_FLAG = "sEncryptFlag";
    // 证书序列号
    public static final String S_SIGNCERT_ID = "sSigncertID";
    // 签名
    public static final String S_SIGNATURE = "sSignature";


    public static final String S_FWD_INST_ID_CODE = "sFwdInstIdCode"; // 发送机构标识码
    // 交易金额

    public static final String S_ORIG_DATA_ELEMTS = "sOrigDataElemts"; // 原交易信息

    public static final String S_INQUIRY_MOD = "sInquiryMod"; // 退款方式
    // 原交易渠道订单号
    public static final String S_CHN_ORDER_NO = "sChnOrderNo"; // 原交易渠道订单号

    public static final String S_ORG_TERM_SSN = "sOrgTermSSN";
    // 原交易状态
    public static final String S_ORG_TRANS_STATE = "sOrgTransState";
    // 原交易状态描述
    public static final String S_ORG_TRANS_STATE_MSG = "sOrgTransStateMsg";
    // 原交易检索参考号
    public static final String S_ORG_RETRIVL_REF_NUM = "sOrgRetrivlRefNum";
    // 原交易授权码
    public static final String S_ORG_AUTHR_ID_RESP = "sOrgAuthrIdResp";
    // 原交易订单号
    public static final String S_ORG_ORDER_NO = "sOrgOrderNo";

    // 原交易渠道订单号
    public static final String S_ORIG_CHN_ORDER_NO = "sOrigChnOrderNo";
    // 试算手续费
    public static final String S_PRE_AMT_FEE = "sPreAmtFee";

    //微信预下单返回

    public static final String SWC_PAY_DATA = "sWcPayData";

    public static final String PAB_APP_ID = "appId";
    public static final String PAB_TIME_STAMP = "timeStamp";
    public static final String PAB_NONCE_STR = "nonceStr";
    public static final String PAB_PACKAGE_NAME = "package";
    public static final String PAB_SIGN_TYPE = "signType";
    public static final String PAB_PAY_SIGN = "paySign";


}
