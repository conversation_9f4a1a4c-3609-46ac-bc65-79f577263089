package com.wosai.mpay.api.pab;

import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;


/**
 * <AUTHOR>
 * @Date 2024/2/28、13:55
 **/

public class PabUtil {
    public static  String allChar = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";

    public static String generateString(int length) {
        StringBuffer sb = new StringBuffer();
        Random random = new SecureRandom();
        for (int i = 0; i < length; i++) {
            sb.append(allChar.charAt(random.nextInt(allChar.length())));
        }
        return sb.toString();
    }


    /**
     * 日期转换
     * @param timestamp
     * @return
     */
    public static String convertTimestampToDateTime(long timestamp) {
        // 创建SimpleDateFormat对象，并指定日期和时间的格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");

        // 使用format方法将时间戳转换为指定格式的日期和时间
        String formattedDateTime = sdf.format(new Date(timestamp));

        // 返回格式化后的日期和时间
        return formattedDateTime;
    }




}
