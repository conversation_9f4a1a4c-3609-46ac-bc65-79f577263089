package com.wosai.mpay.api.pab;

/**
 * <AUTHOR>
 * @Date 2023/12/14、15:05
 **/

public class RequestFields {

    public static final String S_TRANS_CLASS = "sTransClass";
    // 服务区别码
    public static final String S_SERVICE_DISTINCT_CODE = "sServiceDistinctCode";
    // 交易时间
    public static final String S_TRANSMISSION_DATE_TIME = "sTransmsnDateTime";
    // 商户流水号
    public static final String S_TERM_SSN = "sTermSSN";
    // 商户号
    public static final String S_CARD_ACCPTR_ID = "sCardAccptrId";
    // 终端号
    public static final String S_CARD_ACCPTR_TERMNL_ID = "sCardAccptrTermnlId";
    // 发送机构标识码
    public static final String S_FWD_INST_ID_CODE = "sFwdInstIdCode";
    // 交易金额
    public static final String S_AMT_TRANS = "sAmtTrans";
    // 交易币种
    public static final String S_CURRCY_CODE_TRANS = "sCurrcyCodeTrans";
    // 商户终端机器IP
    public static final String S_MCHT_IP = "sMchtIp";
    // 二维码
    public static final String S_AUTH_CODE = "sAuthCode";
    // 商户清算类型
    public static final String S_MCHT_SETTLE_TYPE = "sMchtSettleType";
    // 交易日志跟踪号
    public static final String S_TRAN_LOG_TRACE_NO = "sTranLogTraceNo";
    // 商户订单号/外部订单号
    public static final String S_OUT_ORDER_NO = "sOutOrderNo";
    // 订单优惠标记
    public static final String S_GOODS_TAG = "sGoodsTag";
    // 微信单品优惠
    public static final String S_DETAIL = "sDetail";
    // 地区码
    public static final String S_AREA_CODE = "sAreaCode";
    // 加解密方式
    public static final String S_ENCRYPT_FLAG = "sEncryptFlag";
    // 证书序列号
    public static final String S_SIGNCERT_ID = "sSigncertID";
    // 签名
    public static final String S_SIGNATURE = "sSignature";


    // 原交易信息
    public static final String S_ORIG_DATA_ELEMTS = "sOrigDataElemts";
    // 检索参考号
    public static final String S_RETRIVL_REF_NUM = "sRetrivlRefNum";
    // 退款方式
    public static final String S_INQUIRY_MOD = "sInquiryMod";
    // 原交易渠道订单号
    public static final String S_CHN_ORDER_NO = "sChnOrderNo";

    // 原交易商户订单号
    public static final String S_ORG_OUT_ORDER_NO = "sOrgOutOrderNo";
    // 手续费应答标识
    public static final String S_AMT_FEE_FLAG = "sAmtFeeFlag";


    public static final String S_TRANSMNS_DATE_TIME = "sTransmsnDateTime";
    public static final String S_TRANS_SSN = "sTransSSN";

    public static final String S_ORDER_NO = "sOrderNo";

    public static final String S_TRANS_MEDIA = "sTransMedia";

    public static final String S_ENCRYPT_KEY = "sEncryptKey";
    public static final String S_IDENTITY = "sIdentity";

    public static final String S_TRADE_TYPE = "sTradeType";
    public static final String S_SUB_APPID = "sSubAppid";
    public static final String S_SUB_OPENID = "sSubOpenid";
    public static final String S_BUYER_ID = "sBuyerId";
    public static final String S_VOICE_DEV_NO = "sVoiceDevNo";
    public static final String S_AMT_ORDER = "sAmtOrder";

    public static final String B2C_TERMINAL_ID = "b2c_terminal_id";


    public static final String OTHER_TERMINAL_ID = "other_terminal_id";

}
