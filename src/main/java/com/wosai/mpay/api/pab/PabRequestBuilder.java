package com.wosai.mpay.api.pab;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/12/14、15:03
 **/

public class PabRequestBuilder {
    private Map<String,Object> request;

    public PabRequestBuilder(){
        request = new LinkedHashMap<String,Object>();
    }

    public void set(String field, Object value) {
        request.put(field,  value);
    }

    public Map<String,Object> build(){
        return request;
    }

    public Map<String, Object> getRequest() {
        return this.request;
    }
}
