package com.wosai.mpay.api.pab;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2023/12/14、17:20
 * 平安常量
 **/

public class PabConstant {

    public static final String RET_CODE_SUCCESS = "00000000";
    public static final String RET_CODE_REPEAT = "PAS29402";

    //后六位
    //无原始交易
    public static final String RET_CODE_NOT_FOUNT_TRANSACTION_LAST_SIX = "000025";

    //系统错误
    public static final String RET_CODE_NOT_SYSTEM_ERROR_LAST_SIX = "000096";

    //后端超时
    public static final String RET_CODE_NOT_TIME_OUT_LAST_SIX = "000098";

    //支付宝处理中
    public static final String RET_CODE_ALIPAY_IN_PROG = "AL000004";

    //微信处理中
    public static final String RET_CODE_WEIXIN_IN_PROG = "TX000004";

    //云闪付处理中
    public static final String RET_CODE_UNION_IN_PROG = "YE000004";


    public static Set<String> IN_PROG = new HashSet(){
        {
            add(RET_CODE_ALIPAY_IN_PROG);
            add(RET_CODE_WEIXIN_IN_PROG);
            add(RET_CODE_UNION_IN_PROG);
        }
    };


    public static final String  PAY = "9901"; //b2c 支付

    public static final String  REFUND = "9911"; //退款


    public static final String  B2C_QUERY = "9912"; // 交易查询
    public static final String  C2B_QUERY = "9603"; //交易查询

    public static final String  C2B_PAY = "9602"; //c2b 支付

    //交易场景
    public static final String  C2B = "03"; //支付
    public static final String  B2C = "01"; //支付

    //交易方式
    public static final String  WEIXIN = "01"; //支付
    public static final String  ALIPAY = "02"; //支付

    public static final String SCAN_CODE = "01"; //扫码
    public static final String RETAIL = "63070000"; //发送机构标识码

    public static final String RMB = "156"; //币种：人民币
    public static final String D1 = "01"; // 商户清算类型 t+1
    public static final String ASYMMETRIC_ENCRYPTION = "1"; //加密方式： 非对称加密


    public static final String NAME = "provider.pab";


    //原交易状态
    public static final String IN_PROCESS = "0";

    public static final String SUCCESS = "1";

    //支付机构拒绝
    public static final String PAYMENT_CHANNEL_REFUSE = "2";

    //受理方拒绝
    public static final String ACCEPTOR_REFUSE = "4";

    //已撤销
    public static final String REVOKE = "5";

    //已关闭
    public static final String CLOSE = "6";

    //已转入退款
    public static final String FORWARD_REFUND = "7";

    public static Set<String> FAIL = new HashSet(){
        {
            add(PAYMENT_CHANNEL_REFUSE);
            add(ACCEPTOR_REFUSE);
            add(REVOKE);
            add(CLOSE);
        }
    };

    public static String FORMAT = "YYYYMMdd";

    public static String JSAPI = "JSAPI";

    public static String DEFAULT_IP = "127.0.0.1";




}
