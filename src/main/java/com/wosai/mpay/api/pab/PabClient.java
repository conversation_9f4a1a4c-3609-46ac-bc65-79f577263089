package com.wosai.mpay.api.pab;


import com.wosai.mpay.api.tl.TlConstants;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/12/14、13:47
 * 平安银行client
 **/

public class PabClient {

    public static final Logger logger = LoggerFactory.getLogger(PabClient.class);

    private int connectTimeout = 3000;
    private int readTimeout = 15000;
    private String charset = "UTF-8";

    public PabClient() {
    }

    public PabClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public PabClient(int connectTimeout, int readTimeout, String charset) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
        this.charset = charset;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public Map<String, Object> call(PabRequestBuilder requestBuilder, String gateway
            , String privateKey, String userId) throws MpayException, MpayApiNetworkError {
        return call(requestBuilder, gateway, privateKey, null, userId);
    }

    public Map<String, Object> call(PabRequestBuilder requestBuilder, String gateway
            , String privateKey, Map<String, String> headers, String userId)
            throws MpayException, MpayApiNetworkError {
        Map<String, Object> request = requestBuilder.build();
        //移除签名
        request.remove(RequestFields.S_SIGNATURE);

        try {
            String jsonStr = JsonUtil.toJsonStr(request);

            String s = SmUtil.generateSM3HASH(jsonStr);
            //加密
            String sSignature = SmUtil.signSm3WithSm2(s, userId, privateKey);
            request.put(RequestFields.S_SIGNATURE, sSignature);
        } catch (Exception e) {
            throw new MpayException("平安银行参数构建异常", e);
        }
        String requestStr = JsonUtil.objectToJsonString(request);

        logger.info("request is {}", requestStr);

        String response = HttpClientUtils.doPost(PabClient.class.getName(), null, null, gateway, "application/json", requestStr, TlConstants.CHARSET_UTF8, connectTimeout, readTimeout);

        logger.info("response is {}", response);
        try {
            return JsonUtil.jsonStringToObject(response, Map.class);
        } catch (Exception e) {
            throw new MpayApiUnknownResponse("parse response error: " + e.getMessage(), e);
        }
    }


}
