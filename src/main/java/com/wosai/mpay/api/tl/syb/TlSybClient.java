package com.wosai.mpay.api.tl.syb;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wosai.mpay.api.HttpResourceCallback;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.api.tl.AsyncTlClient;
import com.wosai.mpay.api.tl.ProtocolFields;
import com.wosai.mpay.api.tl.ResponseFields;
import com.wosai.mpay.api.tl.TlConstants;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.message.BasicNameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

public class TlSybClient {
    public static final Logger logger = LoggerFactory.getLogger(TlSybClient.class);
    protected int connectTimeout = 2000;
    protected int readTimeout = 5000;

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map<String,Object> call(String serviceUrl, String appId, String privateKey, Map<String, String> request) throws MpayException, MpayApiNetworkError {
        preProcess(appId, privateKey, request);
        logger.info("request: {}", JsonUtil.objectToJsonString(request));
        long start = System.currentTimeMillis();
        String response = HttpClientUtils.doPost(AsyncTlClient.class.getName(), null, null, serviceUrl, "application/x-www-form-urlencoded", request, TlConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        logger.info("url: {}, cost: {} ms, get response normal:  {}", serviceUrl, System.currentTimeMillis() - start, response);
        return JsonUtil.jsonStringToObject(response, Map.class);
    }

    public static void preProcess(String appId, String privateKey, Map<String, String> request) throws MpayException {
        request.put(ProtocolFields.RANDOM_STR, getNonceStr());
        request.remove(ProtocolV2Fields.SIGN);
        StringBuilder temp = new StringBuilder();
        for (Object mapKey : request.keySet().toArray()) {
            String value = request.get(mapKey);
            if (value == null || "".equals(value)) {
                request.remove(mapKey);
            } else {
                if (temp.length() > 0) {
                    temp.append("&");
                }
                temp.append(mapKey).append("=").append(value);
            }
        }
        String paramsString = temp.toString();
        try {
            request.put(ProtocolV2Fields.SIGN, SM2Util.unionpaySign(appId, privateKey, paramsString));
        } catch (Exception e) {
            throw new MpayException("签名失败", e);
        }
    }

    private static String getNonceStr() {
        return ThreadLocalRandom.current().nextLong() + "";
    }
}
