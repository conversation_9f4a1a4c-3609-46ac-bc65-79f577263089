package com.wosai.mpay.api.tl.s2p;

import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.RsaSignature;


import java.util.*;

/**
 * S2P 通联相机扫码接口签名工具类
 */
public class S2PSignature {

    /**
     * 生成签名
     *
     * @param params    参数Map
     * @param privateKey 私钥
     * @return 签名字符串
     * @throws MpayException 签名异常
     */
    public static String sign(Map<String, String> params, String privateKey) throws MpayException {
        try {
            String content = buildSignContent(params);
            return RsaSignature.sign(content, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, privateKey, "UTF-8");
        } catch (Exception e) {
            throw new MpayException("S2P签名异常", e);
        }
    }

    /**
     * 验证签名
     *
     * @param params    参数Map
     * @param sign      签名
     * @param publicKey 公钥
     * @return 验证结果
     * @throws MpayException 验证异常
     */
    public static boolean verify(Map<String, String> params, String sign, String publicKey) throws MpayException {
        try {
            String content = buildSignContent(params);
            return RsaSignature.validateSign(content, sign, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, publicKey, "UTF-8");
        } catch (Exception e) {
            throw new MpayException("S2P验签异常", e);
        }
    }

    /**
     * 构建签名内容
     *
     * @param params 参数Map
     * @return 待签名字符串
     */
    private static String buildSignContent(Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }

        // 移除sign字段
        Map<String, String> signParams = new HashMap<>(params);
        signParams.remove(S2PRequestFields.Common.SIGN);

        // 按字段名的ASCII码从小到大排序
        List<String> keys = new ArrayList<>(signParams.keySet());
        Collections.sort(keys);

        // 使用URL的键值对格式拼接成字符串
        StringBuilder content = new StringBuilder();
        int index = 0;
        for (String key : keys) {
            Object value = signParams.get(key);
            if (value != null ) {
                if (index > 0) {
                    content.append("&");
                }
                content.append(key).append("=").append(value);
                index++;
            }
        }

        return content.toString();
    }
}
