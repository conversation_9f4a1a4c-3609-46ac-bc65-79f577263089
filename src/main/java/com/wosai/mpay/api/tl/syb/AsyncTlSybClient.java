package com.wosai.mpay.api.tl.syb;

import com.wosai.mpay.api.HttpResourceCallback;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.api.tl.ProtocolFields;
import com.wosai.mpay.api.tl.ResponseFields;
import com.wosai.mpay.api.tl.TlConstants;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.message.BasicNameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 通联收银宝异步客户端
 */
public class AsyncTlSybClient {
    public static final Logger logger = LoggerFactory.getLogger(AsyncTlSybClient.class);
    protected int connectTimeout = 2000;
    protected int readTimeout = 5000;

    private RequestConfig requestConfig;
    private CloseableHttpAsyncClient client;

    public AsyncTlSybClient() {
        initClient();
    }

    public AsyncTlSybClient(int readTimeout, int connectTimeout){
        this.readTimeout = readTimeout;
        this.connectTimeout = connectTimeout;
        initClient();
    }

    public void initClient(){
        requestConfig = RequestConfig.custom().setConnectionRequestTimeout(connectTimeout).setConnectTimeout(connectTimeout).setSocketTimeout(readTimeout).build();
        client = AsyncClientUtil.getCloseableHttpAsyncClient(null, null);
    }

    public void call(String serviceUrl, String appId, String privateKey, Map<String, Object> request, HttpResourceCallback<Map<String, Object>> callback){
        try{
            doCall(serviceUrl,appId, privateKey, request, callback);
        }catch (Throwable t){
            callback.onError(t);
        }
    }

    private void doCall(String serviceUrl, String appId, String privateKey, Map<String, Object> request, HttpResourceCallback<Map<String, Object>> callback) throws Exception {
        preProcess(appId,privateKey, request);
        AsyncClientUtil.logRequest(logger, JsonUtil.objectMapper.writeValueAsString(request));
        HttpPost httpPost = new HttpPost(serviceUrl);
        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
        List<NameValuePair> pairs = new ArrayList<NameValuePair>();
        request.forEach((k, v) -> {
            if(v == null) {
                return;
            }
            pairs.add(new BasicNameValuePair(k, v.toString()));
        });
        httpPost.setEntity(new UrlEncodedFormEntity(pairs, TlConstants.CHARSET_UTF8));
        httpPost.setConfig(requestConfig);
        long start = System.currentTimeMillis();
        client.execute(httpPost, AsyncClientUtil.getFutureCallback(logger, TlConstants.CHARSET_UTF8, AsyncClientUtil.ResponseType.STRING, HttpResourceCallback.<String>create(
                TracingUtil.getTraceCarrierItem(),
                (response, t) -> {
                    Map<String, Object> responseMap = new HashMap<>();
                    try {
                        responseMap = JsonUtil.jsonStringToObject(response, Map.class);
                    } catch (MpayException e) {
                        responseMap.put(ResponseFields.RET_CODE, response);
                    }

                    AsyncClientUtil.logResponse(logger, serviceUrl, System.currentTimeMillis() - start, response != null ? response : null, t);
                    if(t != null){
                        callback.onError(t);
                        return;
                    }else{
                        callback.onComplete(responseMap);
                    }
                }
        )));
    }

    public static void preProcess(String appId, String privateKey, Map<String, Object> request) throws MpayException {
        request.put(ProtocolFields.RANDOM_STR, getNonceStr());
        request.remove(ProtocolV2Fields.SIGN);
        StringBuilder temp = new StringBuilder();
        for (Object mapKey : request.keySet().toArray()) {
            String value = (String) request.get(mapKey);
            if (value == null) {
                request.remove(mapKey);
            } else {
                if (temp.length() > 0) {
                    temp.append("&");
                }
                temp.append(mapKey).append("=").append(value);
            }
        }
        String paramsString = temp.toString();
        try {
            request.put(ProtocolV2Fields.SIGN, SM2Util.unionpaySign(appId, privateKey, paramsString));
        } catch (Exception e) {
            throw new MpayException("签名失败", e);
        }
    }

    private static String getNonceStr() {
        return ThreadLocalRandom.current().nextLong() + "";
    }
}
