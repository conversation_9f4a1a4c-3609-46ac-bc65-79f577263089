package com.wosai.mpay.api.tl.s2p;

/**
 * S2P 通联相机扫码接口常量类
 */
public class S2PConstants {
    
    /**
     * API版本号
     */
    public static final String VERSION = "V2.0.0";
    
    /**
     * 签名类型
     */
    public static final String SIGN_TYPE_RSA2 = "RSA2";

    /**
     * 交易类型
     */
    public static class TransType {
        /**
         * 交易结果查询
         */
        public static final String QUERY = "Query";
        
        /**
         * 对账文件下载
         */
        public static final String DOWN_FILE = "DownFile";
        
        /**
         * 拒付文件下载
         */
        public static final String DOWN_DISP_FILE = "DownDispFile";
        
        /**
         * 退货
         */
        public static final String TOKEN_REFUND = "TokenRefund";
    }
    
    /**
     * 交易币种
     */
    public static class Currency {
        /**
         * 人民币
         */
        public static final String CNY = "CNY";
        
        /**
         * 港币
         */
        public static final String HKD = "HKD";
        
        /**
         * 美元
         */
        public static final String USD = "USD";
        
        /**
         * 欧元
         */
        public static final String EUR = "EUR";
        
        /**
         * 英镑
         */
        public static final String GBP = "GBP";
        
        /**
         * 日元
         */
        public static final String JPY = "JPY";
        
        /**
         * 台币
         */
        public static final String TWD = "TWD";
        
        /**
         * 澳元
         */
        public static final String AUD = "AUD";
    }
    
    /**
     * 应答码
     */
    public static class ResultCode {
        /**
         * 成功
         */
        public static final String SUCCESS = "0000";
        
        /**
         * 交易支付中
         */
        public static final String PAYING = "P000";
        
        /**
         * 订单不存在
         */
        public static final String ORDER_NOT_EXIST = "0007";
        
        /**
         * 处理超时
         */
        public static final String TIMEOUT = "0006";
        
        /**
         * 系统异常
         */
        public static final String SYSTEM_ERROR = "9999";
        
        /**
         * 参数错误
         */
        public static final String PARAM_ERROR = "0001";
        
        /**
         * 密钥校验失败
         */
        public static final String KEY_VERIFY_FAIL = "0002";
        
        /**
         * 密钥不存在或已失效
         */
        public static final String KEY_NOT_EXIST = "0003";
        
        /**
         * 请求的功能尚不支持
         */
        public static final String FUNCTION_NOT_SUPPORT = "0004";
        
        /**
         * 不支持的币种
         */
        public static final String CURRENCY_NOT_SUPPORT = "0005";
        
        /**
         * 风险交易
         */
        public static final String RISK_TRANSACTION = "0008";
        
        /**
         * 报文格式错误
         */
        public static final String FORMAT_ERROR = "0009";
        
        /**
         * 商户不存在
         */
        public static final String MERCHANT_NOT_EXIST = "0010";
        
        /**
         * 不支持的交易
         */
        public static final String TRANSACTION_NOT_SUPPORT = "0011";
        
        /**
         * 超出超限
         */
        public static final String EXCEED_LIMIT = "0013";
        
        /**
         * 原交易状态异常
         */
        public static final String ORIGINAL_TRANSACTION_STATUS_ERROR = "0014";
        
        /**
         * 无可用路由
         */
        public static final String NO_AVAILABLE_ROUTE = "0015";
        
        /**
         * 无此发卡方
         */
        public static final String NO_ISSUER = "0016";
        
        /**
         * 金额错误
         */
        public static final String AMOUNT_ERROR = "0017";
        
        /**
         * 订单重复
         */
        public static final String ORDER_DUPLICATE = "0022";
        
        /**
         * 对账单不存在
         */
        public static final String BILL_NOT_EXIST = "0099";
    }
    
    /**
     * 订单状态
     */
    public static class OrderStatus {
        /**
         * 待支付
         */
        public static final String READY = "READY";
        
        /**
         * 支付中
         */
        public static final String PAYING = "PAYING";
        
        /**
         * 已支付
         */
        public static final String PAIED = "PAIED";
        
        /**
         * 已撤销
         */
        public static final String REVOKED = "REVOKED";
        
        /**
         * 已关闭
         */
        public static final String CLOSED = "CLOSED";
        
        /**
         * 已退款
         */
        public static final String REFUND = "REFUND";
        
        /**
         * 交易失败
         */
        public static final String FAILED = "FAILED";
    }
    
    /**
     * 卡组织列表
     */
    public static class CardOrganization {
        /**
         * VISA
         */
        public static final String VISA = "VISA";
        
        /**
         * MASTERCARD
         */
        public static final String MASTERCARD = "MASTERCARD";
        
        /**
         * JCB
         */
        public static final String JCB = "JCB";
        
        /**
         * AMERICAEXPRESS
         */
        public static final String AMERICAEXPRESS = "AMERICAEXPRESS";
        
        /**
         * DINERSCLUB
         */
        public static final String DINERSCLUB = "DINERSCLUB";
        
        /**
         * UNIONPAY
         */
        public static final String UNIONPAY = "UNIONPAY";
    }
}
