package com.wosai.mpay.api.tl.syb;

import java.util.Map;
import java.util.TreeMap;

public class RequestBuilder {

    private Map<String, String> request;

    public RequestBuilder() {
        request = new TreeMap<>();
        request.put(BusinessFields.RANDOMSTR, System.currentTimeMillis() + "");
        request.put(BusinessFields.VERSION, SybConstants.DEFAULT_VERSION);
    }

    public void set(String field, String value) {
        request.put(field, value);
    }

    public Map<String, String> build() {
        return request;
    }

}
