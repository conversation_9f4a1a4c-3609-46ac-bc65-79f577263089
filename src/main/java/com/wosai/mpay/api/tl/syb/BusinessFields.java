package com.wosai.mpay.api.tl.syb;

public class BusinessFields {

    public static final String ORGID = "orgid";                 //集团/代理商商户号
    public static final String CUSID = "cusid";                 //实际交易的商户号
    public static final String APPID = "appid";                 //平台分配的APPID
    public static final String VERSION = "version";             //接口版本号
    public static final String TRXAMT = "trxamt";               //交易金额 单位为分
    public static final String REQSN = "reqsn";                 //商户的交易订单号
    public static final String PAYTYPE = "paytype";             //交易方式
    public static final String RANDOMSTR = "randomstr";         //商户自行生成的随机字符串
    public static final String BODY = "body";                   //订单商品名称，为空则以商户名作为商品名称
    public static final String REMARK = "remark";               //备注信息
    public static final String VALIDTIME = "validtime";         //有效时间 订单有效时间，以分为单位，不填默认为5分钟
    public static final String ACCT = "acct";                   //支付平台用户标识JS支付时使用 微信支付-用户的微信openid 支付宝支付-用户user_id 微信小程序-用户小程序的openid云闪付JS-用户userId
    public static final String NOTIFY_URL = "notify_url";       //交易结果通知地址
    public static final String LIMIT_PAY = "limit_pay";         //支付限制 暂时只对微信支付和支付宝有效,仅支持no_credit
    public static final String SUB_APPID = "sub_appid";         //微信小程序/微信公众号/APP的appid
    public static final String GOODS_TAG = "goods_tag";         //订单优惠标记，用于区分订单是否可以享受优惠，字段内容在微信后台配置券时进行设置，说明详见代金券或立减优惠 只对微信支付有效W01交易方式不支持
    public static final String BENEFITDEATIL = "benefitdetail"; //优惠信息
    public static final String CHNLSTOREID = "chnlstoreid";     //渠道门店编号
    public static final String SUBBRANCH = "subbranch";         //门店号
    public static final String EXTENDPARAMS = "extendparams";   //拓展参数 json字符串，注意是String
    public static final String CUSIP = "cusip";                 //终端ip payType=U02 云闪付JS支付不为空
    public static final String FRONT_URL = "front_url";         //支付完成跳转 只支持payType=U02云闪付JS支付 payType=W02微信JS支付
    public static final String IDNO = "idno";                   //证件号 实名交易必填.填了此字段就会验证证件号和姓名 暂只支持支付宝支付,微信支付(微信支付的刷卡支付除外)
    public static final String TRUENAME = "truename";           //实名交易必填.填了此字段就会验证证件号和姓名
    public static final String ASINFO = "asinfo";               //分账信息
    public static final String FQNUM = "fqnum";                 //3  花呗分期3期 6  花呗分期6期 12  花呗分期12期 3-cc 支付宝信用卡分期3期 6-cc 支付宝信用卡分期6期 12-cc 支付宝信用卡分期12期
    public static final String SIGNTYPE = "signtype";           //签名方式
    public static final String UNPID = "unpid";                 //银联pid
    public static final String OPERATORID = "operatorid";       //收银员号
    public static final String SIGN = "sign";
    public static final String AUTHCODE = "authcode";           //支付授权码
    public static final String TRXID = "trxid";
    public static final String OLDTRXID = "oldtrxid";           //原支付流水
    public static final String OLDREQSN = "oldreqsn";           //原交易订单号
    public static final String AUTH_TYPE = "authtype";          //授权码类型
    public static final String IDENTIFY = "identify";
    public static final String ISV_CUSID = "isvcusid";
    public static final String SUB_MCHID = "sub_mchid";
    public static final String TOTAL_AMOUNT = "total_amount";
    public static final String REFUND_AMOUNT = "refund_amount";
    public static final String REFUND_CUS_AMOUNT = "refund_cus_amount";
    public static final String REFUND_FEE = "refund_fee";
    public static final String REFUND_TRADE_NO = "refund_trade_no";
    public static final String OUT_TRADE_NO = "out_trade_no";
    public static final String CHNLTYPE = "chnltype";
    public static final String CHANNEL_ID = "channel_id";
    public static final String ORIG_ORDER_TIME = "orig_order_time";

    public static final String TERMIINFO = "terminfo";

    //terminfo字段信息
    public static final String TERMNO = "termno"; //终端号 8位数字  商户下唯一
    /**
     * 01：自动柜员机（含 ATM 和 CDM）和多媒体自助终端
     * 02：传统 POS
     * 03：mPOS
     * 04：智能 POS
     * 05：II 型固定电话
     * 06：云闪付终端；
     * 07：保留使用；
     * 08：手机 POS；
     * 09：刷脸付终端；
     * 10：条码支付受理终端；
     * 11：条码支付辅助受理终端；
     * 12：行业终端（公交、地铁用于指定行业的终端）；
     * 13：MIS 终端；
     */
    public static final String DEVICETYPE = "devicetype"; //设备类型
    public static final String TERMSN = "termsn"; //// 终端类型（device_type）填写为 02、03、04、05、06、08、09 或 10时，必须填写终端序列号。
    public static final String LONGITUDE = "longitude";
    public static final String LATITUDE = "latitude";

    public static final String PAYEECUSID = "payeecusid";
    public static final String SHARETYPE = "sharetype";
    public static final String AMTMAX = "amtmax";  //最高金额 单位元
    public static final String RATEMAX = "ratemax"; //最高四位小数 例如:0.2513代表25.13%


    public static final String LIMITTYPE = "limittype"; //0-比例超限或者金额超限都不通过  1-比例不超限或者金额不超限都通过 默认0
    public static final String REASON = "reason";

    public static final String RKTRXID = "rktrxid"; //分账回退需要上送的退款流水号
    public static final String PAYERTRXID = "payertrxid";

    public static final String CHNLTYPE_WEIXIN = "WEIXIN";
    public static final String CHNLTYPE_ALIPAY = "ALIPAY";
    public static final String CHNLTYPE_UNIONPAY = "UNIONPAY";
    public static final String VERSION_REFUND_V2 = "12";
    public static final String ORDER_ID = "orderid"; //POS订单支付的输入的商户订单号
    public static final String TRX_DATE = "trxdate";
    public static final String RESEND_NOTIFY = "resendnotify";//是否重发通知
    public static final String RESEND_NOTIFY_NO = "0";//不重发
    public static final String RESEND_NOTIFY_YES = "1";//重发

}
