package com.wosai.mpay.api.tl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.message.BasicNameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wosai.mpay.api.HttpResourceCallback;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.AsyncClientUtil;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.TracingUtil;

/**
 * 通联异步客户端
 */
public class AsyncTlClient {
    public static final Logger logger = LoggerFactory.getLogger(AsyncTlClient.class);
    protected int connectTimeout = 2000;
    protected int readTimeout = 5000;

    private RequestConfig requestConfig;
    private CloseableHttpAsyncClient client;

    public AsyncTlClient() {
        initClient();
    }

    public AsyncTlClient(int readTimeout, int connectTimeout){
        this.readTimeout = readTimeout;
        this.connectTimeout = connectTimeout;
        initClient();
    }

    public void initClient(){
        requestConfig = RequestConfig.custom().setConnectionRequestTimeout(connectTimeout).setConnectTimeout(connectTimeout).setSocketTimeout(readTimeout).build();
        client = AsyncClientUtil.getCloseableHttpAsyncClient(null, null);
    }

    public void call(String serviceUrl, String privateKey, Map<String, Object> request, HttpResourceCallback<Map<String, Object>> callback){
        try{
            doCall(serviceUrl, privateKey, request, callback);
        }catch (Throwable t){
            callback.onError(t);
        }
    }

    private void doCall(String serviceUrl, String privateKey, Map<String, Object> request, HttpResourceCallback<Map<String, Object>> callback) throws Exception {
        preProcess(privateKey, request);
        AsyncClientUtil.logRequest(logger, JsonUtil.objectMapper.writeValueAsString(request));
        HttpPost httpPost = new HttpPost(serviceUrl);
        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
        List<NameValuePair> pairs = new ArrayList<NameValuePair>();
        request.forEach((k, v) -> {
            if(v == null) {
                return;
            }
            pairs.add(new BasicNameValuePair(k, v.toString()));
        });
        httpPost.setEntity(new UrlEncodedFormEntity(pairs, TlConstants.CHARSET_UTF8));
        httpPost.setConfig(requestConfig);
        long start = System.currentTimeMillis();
        client.execute(httpPost, AsyncClientUtil.getFutureCallback(logger, TlConstants.CHARSET_UTF8, AsyncClientUtil.ResponseType.STRING, HttpResourceCallback.<String>create(
                TracingUtil.getTraceCarrierItem(),
                (response, t) -> {
                    Map<String, Object> responseMap = new HashMap<>();
                    try {
                        responseMap = JsonUtil.jsonStringToObject(response, Map.class);
                    } catch (MpayException e) {
                        responseMap.put(ResponseFields.RET_CODE, response);
                    }

                    AsyncClientUtil.logResponse(logger, serviceUrl, System.currentTimeMillis() - start, response != null ? response : null, t);
                    if(t != null){
                        callback.onError(t);
                        return;
                    }else{
                        callback.onComplete(responseMap);
                    }
                }
        )));
    }

    public static void preProcess(String privateKey, Map<String, Object> request) throws MpayException {
        request.put(ProtocolFields.RANDOM_STR, getNonceStr());
        request.remove(ProtocolV2Fields.SIGN);
        for (Object mapKey : request.keySet().toArray()) {
            if (request.get(mapKey) == null) {
                request.remove(mapKey);
            }
        }
        request.put(ProtocolV2Fields.SIGN, RsaSignature.sign(request, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, privateKey));
    }

    private static String getNonceStr(){
        return ThreadLocalRandom.current().nextLong() + "";
    }
}
