package com.wosai.mpay.api.tl.s2p;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 *
 */
public class S2PClient {
    public static final Logger logger = LoggerFactory.getLogger(S2PClient.class);


    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public S2PClient() {
    }

    public S2PClient(int readTimeout, int connectTimeout) {
        this.readTimeout = readTimeout;
        this.connectTimeout = connectTimeout;
    }


    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map<String, Object> call(String gateway, Map<String, String> request, String privateKey) throws MpayException, MpayApiNetworkError {
        logger.info("request: {}", request);
        // 添加签名
        addSign(request, privateKey);
        String response = WebUtils.doPost(null, null, gateway, request, connectTimeout, readTimeout);
        logger.info("response: {}", response);
        // 解析响应
        Map<String, Object> result = JsonUtil.jsonStringToObject(response, Map.class);
        return result;
    }

    /**
     * 生成RSA2签名并添加到参数Map中
     *
     * @param params     参数Map
     * @param privateKey 私钥
     * @throws MpayException 签名异常
     */
    public static void addSign(Map<String, String> params, String privateKey) throws MpayException {
        // 添加签名类型
        params.put(S2PRequestFields.Common.SIGN_TYPE, S2PConstants.SIGN_TYPE_RSA2);
        params.remove(S2PRequestFields.Common.SIGN);
        // 生成签名
        String sign = S2PSignature.sign(params, privateKey);

        // 添加签名到参数Map
        params.put(S2PRequestFields.Common.SIGN, sign);
    }


}
