package com.wosai.mpay.api.tl.s2p;

/**
 * S2P 通联相机扫码接口响应字段定义
 */
public class S2PResponseFields {

    /**
     * 通用响应字段
     */
    public static class Common {
        /**
         * 应答码
         */
        public static final String RESULT_CODE = "resultCode";
        
        /**
         * 应答描述
         */
        public static final String RESULT_DESC = "resultDesc";
        
        /**
         * 商户ID
         */
        public static final String MCHT_ID = "mchtId";
        
        /**
         * 商户订单号
         */
        public static final String ACCESS_ORDER_ID = "accessOrderId";
        
        /**
         * 版本号
         */
        public static final String VERSION = "version";
        
        /**
         * 签名
         */
        public static final String SIGN = "sign";
        
        /**
         * 签名类型
         */
        public static final String SIGN_TYPE = "signType";
        
        /**
         * 卡组织应答码
         */
        public static final String CA_RSP_CODE = "caRspCode";
        
        /**
         * 卡组织检索参考号
         */
        public static final String CA_RRN = "caRrn";
        
        /**
         * 卡组织授权应答码
         */
        public static final String CA_AUTH_CODE = "caAuthCode";
    }
    
    /**
     * 预处理响应字段
     */
    public static class PreProcess {
        /**
         * ApplePay返回的支付session
         */
        public static final String APPLE_PAY_SESSION = "applePaySession";
        
        /**
         * 支付币种
         */
        public static final String CURRENCY = "currency";
        
        /**
         * 订单金额
         */
        public static final String AMOUNT = "amount";
    }
    
    /**
     * 授权响应字段
     */
    public static class Authorized {
        /**
         * 支付系统订单号
         */
        public static final String ORDER_ID = "orderId";
        
        /**
         * 支付币种
         */
        public static final String CURRENCY = "currency";
        
        /**
         * 订单金额
         */
        public static final String AMOUNT = "amount";
    }
    
    /**
     * 交易结果异步通知字段
     */
    public static class Notify {
        /**
         * 系统订单号
         */
        public static final String ORDER_ID = "orderId";
        
        /**
         * 卡号
         */
        public static final String CARD_NO = "cardNo";
        
        /**
         * 卡组织或钱包类型
         */
        public static final String CARD_ORGN = "cardOrgn";
        
        /**
         * 支付币种
         */
        public static final String CURRENCY = "currency";
        
        /**
         * 订单金额
         */
        public static final String AMOUNT = "amount";
        
        /**
         * 本地币种
         */
        public static final String LOCAL_CURRENCY = "LocalCurrency";
        
        /**
         * 本地金额
         */
        public static final String LOCAL_AMOUNT = "LocalAmount";
        
        /**
         * 交易时间
         */
        public static final String TRANS_TIME = "transTime";
    }
    
    /**
     * 交易结果查询响应字段
     */
    public static class Query {
        /**
         * 原商户订单号
         */
        public static final String ORI_ACCESS_ORDER_ID = "oriAccessOrderId";
        
        /**
         * 系统订单号
         */
        public static final String ORDER_ID = "orderId";
        
        /**
         * 支付币种
         */
        public static final String CURRENCY = "currency";
        
        /**
         * 订单金额
         */
        public static final String AMOUNT = "amount";
        
        /**
         * 本地币种
         */
        public static final String LOCAL_CURRENCY = "LocalCurrency";
        
        /**
         * 本地金额
         */
        public static final String LOCAL_AMOUNT = "LocalAmount";
        
        /**
         * 订单状态
         */
        public static final String STATUS = "status";
        
        /**
         * 订单Desc
         */
        public static final String STATUS_DESC = "statusDesc";
        
        /**
         * 交易时间
         */
        public static final String TRANS_TIME = "transTime";
        
        /**
         * 卡组织
         */
        public static final String CARD_ORGN = "cardOrgn";
    }
    
    /**
     * 退货响应字段
     */
    public static class Refund {
        /**
         * 原商户订单号
         */
        public static final String ORI_ACCESS_ORDER_ID = "oriAccessOrderId";
        
        /**
         * 系统订单号
         */
        public static final String ORDER_ID = "orderId";
        
        /**
         * 支付币种
         */
        public static final String REFUND_CURRENCY = "refundCurrency";
        
        /**
         * 退款金额
         */
        public static final String REFUND_AMOUNT = "refundAmount";
        
        /**
         * 本地币种
         */
        public static final String LOCAL_CURRENCY = "LocalCurrency";
        
        /**
         * 本地金额
         */
        public static final String LOCAL_AMOUNT = "LocalAmount";
        
        /**
         * 交易时间
         */
        public static final String TRANS_TIME = "transTime";
    }
    
    /**
     * 对账文件下载响应字段
     */
    public static class DownFile {
        /**
         * 对账文件内容
         */
        public static final String BILL_DATA = "billData";
    }
    
    /**
     * 拒付数据下载响应字段
     */
    public static class DownDispFile {
        /**
         * 请求时间
         */
        public static final String ACCESS_DATETIME = "accessDatetime";
        
        /**
         * 拒付明细内容
         */
        public static final String DISPUTE_DATA = "disputeData";
    }
}
