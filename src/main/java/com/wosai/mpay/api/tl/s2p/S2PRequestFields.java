package com.wosai.mpay.api.tl.s2p;

/**
 * S2P 通联相机扫码接口请求字段定义
 */
public class S2PRequestFields {

    /**
     * 通用请求字段
     */
    public static class Common {
        /**
         * 版本号
         */
        public static final String VERSION = "version";
        
        /**
         * 接入号
         */
        public static final String INST_NO = "instNo";
        
        /**
         * 商户号
         */
        public static final String MCHT_ID = "mchtId";
        
        /**
         * 商户订单号
         */
        public static final String ACCESS_ORDER_ID = "accessOrderId";
        
        /**
         * 签名
         */
        public static final String SIGN = "sign";
        
        /**
         * 签名类型
         */
        public static final String SIGN_TYPE = "signType";
        
        /**
         * 交易类型
         */
        public static final String TRANS_TYPE = "transType";
    }
    
    /**
     * 预处理请求字段
     */
    public static class PreProcess {
        /**
         * 支付币种
         */
        public static final String CURRENCY = "currency";
        
        /**
         * 订单金额
         */
        public static final String AMOUNT = "amount";
        
        /**
         * 订单过期时间
         */
        public static final String EXPIRE_MINS = "expireMins";
        
        /**
         * Applpay请求地址
         */
        public static final String VALIDATION_URL = "validationUrl";
        
        /**
         * 子商户号
         */
        public static final String SUB_MCHT_ID = "subMchtId";
        
        /**
         * 子商户名称
         */
        public static final String SUB_MCHT_NAME = "subMchtName";
        
        /**
         * 子商户的mcc
         */
        public static final String SUB_MCHT_MCC = "subMchtMcc";
        
        /**
         * 子商户所在国家
         */
        public static final String SUB_MCHT_COUNTRY = "subMchtCountry";
        
        /**
         * 子商户所在州/省
         */
        public static final String SUB_MCHT_STATE = "subMchtState";
        
        /**
         * 子商户所在城市
         */
        public static final String SUB_MCHT_CITY = "subMchtCity";
        
        /**
         * 子商户地址
         */
        public static final String SUB_MCHT_ADDR = "subMchtAddr";
        
        /**
         * 子商户邮编
         */
        public static final String SUB_MCHT_ZIP = "subMchtZip";
        
        /**
         * 子商户URL
         */
        public static final String SUB_MCHT_URL = "subMchtUrl";
        
        /**
         * 付款名称
         */
        public static final String DISPLAY_NAME = "displayName";

        /**
         * 支付域名
         */
        public static final String DOMAIN_NAME = "domainName";
        
        /**
         * 商品信息
         */
        public static final String PRODUCT_INFO = "productInfo";
        
        /**
         * 异步通知地址
         */
        public static final String NOTIFY_URL = "notifyUrl";
    }
    
    /**
     * 商品信息字段
     */
    public static class ProductInfo {
        /**
         * 商品编号
         */
        public static final String SKU = "sku";
        
        /**
         * 商品名称
         */
        public static final String PRODUCT_NAME = "productName";
        
        /**
         * 商品单价
         */
        public static final String PRICE = "price";
        
        /**
         * 商品数量
         */
        public static final String QUANTITY = "quantity";
        
        /**
         * 商品图片URL
         */
        public static final String PRODUCT_IMAGE = "productImage";
        
        /**
         * 商品链接URL
         */
        public static final String PRODUCT_URL = "productUrl";
    }
    
    /**
     * 授权请求字段
     */
    public static class Authorized {
        /**
         * 原预处理请求accessOrderId
         */
        public static final String ORI_ACCESS_ORDER_ID = "oriAccessOrderId";
        
        /**
         * 支付币种
         */
        public static final String CURRENCY = "currency";
        
        /**
         * 订单金额
         */
        public static final String AMOUNT = "amount";
        
        /**
         * 负载
         */
        public static final String PAYLOAD = "payload";
        
        /**
         * 持卡人姓名
         */
        public static final String CARD_HOLDER = "cardHolder";
        
        /**
         * 卡号
         */
        public static final String ACCT_NO = "acctNo";
    }
    
    /**
     * 交易结果查询字段
     */
    public static class Query {
        /**
         * 原商户订单号
         */
        public static final String ORI_ACCESS_ORDER_ID = "oriAccessOrderId";
    }
    
    /**
     * 退货字段
     */
    public static class Refund {
        /**
         * 退货金额
         */
        public static final String REFUND_AMOUNT = "refundAmount";
        
        /**
         * 原商户订单号
         */
        public static final String ORI_ACCESS_ORDER_ID = "oriAccessOrderId";
        
        /**
         * 时区
         */
        public static final String TIME_ZONE = "timeZone";
    }
    
    /**
     * 对账文件下载字段
     */
    public static class DownFile {
        /**
         * 交易日期
         */
        public static final String BILL_DATE = "billDate";
    }
    
    /**
     * 拒付数据下载字段
     */
    public static class DownDispFile {
        /**
         * 查询拒付时间起
         */
        public static final String DISPUTE_START_DATE = "disputeStartDate";
        
        /**
         * 查询拒付时间止
         */
        public static final String DISPUTE_END_DATE = "disputeEndDate";
        
        /**
         * 创建时间
         */
        public static final String CREATE_DATE = "createDate";
    }
}
