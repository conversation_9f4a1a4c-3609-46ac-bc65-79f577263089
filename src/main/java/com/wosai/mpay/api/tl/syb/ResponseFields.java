package com.wosai.mpay.api.tl.syb;

public class ResponseFields {

    public static final String RET_CODE = "retcode";
    public static final String RET_MSG = "retmsg";

    /**
     * 0000：交易成功
     * 1001：交易不存在
     * 2008或者2000 : 交易处理中,请查询交易,如果是实时交易(例如刷卡支付,交易撤销,退货),建议每隔一段时间(10秒)查询交易
     * 3开头的错误码代表交易失败
     * 3888-流水号重复
     * 3889-交易控制失败，具体原因看errmsg
     * 3099-渠道商户错误
     * 3014-交易金额小于应收手续费
     * 3031-校验实名信息失败
     * 3088-交易未支付(在查询时间区间内未成功支付,如已影响资金24小时内会做差错退款处理)
     * 3089-撤销异常,如已影响资金24小时内会做差错退款处理
     * 3045-其他错误，具体原因看errmsg
     * 3050-交易已被撤销
     * 3999-其他错误，具体原因看errmsg
     * 其他3开头的错误码代表交易失败,具体原因请读取errmsg
     */
    public static final String TRX_STATUS = "trxstatus";

    public static final String ACCT = "acct";
    public static final String ACCT_NO = "acctno";
    public static final String FIN_TIME = "fintime";         //yyyyMMddHHmmss
    public static final String FEE = "fee";
    public static final String CHANNEL_DATA = "chnldata";   //渠道信息  目前返回云闪付/微信/支付宝的活动参数
    public static final String ACCT_TYPE = "accttype";      //00-借记卡 02-信用卡 99-其他（花呗/余额等）
    public static final String TRX_AMT = "trxamt";           //交易金额

    /**
     * VSP501	微信支付
     * VSP502	微信支付撤销
     * VSP503	微信支付退款
     * VSP505	手机QQ 支付
     * VSP506	手机QQ支付撤销
     * VSP507	手机QQ支付退款
     * VSP511	支付宝支付
     * VSP512	支付宝支付撤销
     * VSP513	支付宝支付退款
     * VSP541	扫码支付
     * VSP542	扫码撤销
     * VSP543	扫码退货
     * VSP551	银联扫码支付
     * VSP552	银联扫码撤销
     * VSP553	银联扫码退货
     * VSP907	差错借记调整
     * VSP908	差错贷记调整
     * VSP611	数字货币支付
     * VSP612	数字货币撤销
     * VSP613	数字货币退货
     * VSP621	分期支付
     * VSP622	分期撤销
     * VSP623	分期退货
     * 300002	充值
     */
    public static final String TRX_CODE = "trxcode";         //交易类型
    public static final String INIT_AMT = "initamt";         //原交易金额
    public static final String CM_ID = "cmid";               //渠道子商户号
    public static final String CHANNEL_ID = "chnlid";       //渠道号
    public static final String CHANNEL_TRX_ID = "chnltrxid";
    public static final String TRX_ID = "trxid";
    public static final String PAY_INFO ="payinfo";         //wap支付链接

    public static final String STATUS = "status";
    public static final String VERIFYTIME = "verifytime";
    public static final String ERRMSG = "errmsg";

    public static final String TRX_ERR_MSG = "trxerrmsg";  //交易错误信息
    public static final String TRX_RESERVE = "trxreserve";//订单支付的业务关联内容
    public static final String AMOUNT = "amount"; //单位分

    public static final String TERM_REF_NUM = "termrefnum";//参考号
    public static final String TERM_BATCH_ID = "termbatchid";//终端批次号
    public static final String TERM_AUTH_NO = "termauthno";//授权码
    public static final String TERM_TRACE_NO = "traceno";//终端流水

    public static final String BIZ_SEQ = "bizseq";//业务流水号 如订单号，保单号，缴费编号等
}
