package com.wosai.mpay.api.tl.syb;

public class SybConstants {
    public static final String DEFAULT_VERSION = "11";
    public static final String DEFAULT_SIGN_TYPE = "SM2";
    public static final String RET_CODE_SUCCESS = "SUCCESS";
    public static final String RET_CODE_FAIL = "FAIL";
    public static final String TRX_STATUS_SUCCESS = "0000";  //交易成功
    public static final String TRX_STATUS_NOT_FOUND = "1001";//交易不存在
    public static final String TRX_STATUS_PROCESSING_1 = "2000";//交易处理中,请查询交易,如果是实时交易(例如刷卡支付,交易撤销,退货),建议每隔一段时间(10秒)查询交易
    public static final String TRX_STATUS_PROCESSING_2 = "2008";//交易处理中,请查询交易,如果是实时交易(例如刷卡支付,交易撤销,退货),建议每隔一段时间(10秒)查询交易
    public static final String DATA_FORMAT = "yyyyMMddHHmmss"; //时间格式
    public static final String ACCT_TYPE_DEBIT_VALUE = "00"; //00-借记卡
    public static final String ACCT_TYPE_CREDIT_VALUE = "02"; //02-信用卡
    public static final String ACCT_TYPE_OTHER_VALUE = "99";//99-其他（花呗/余额等）
    public static final String DEFAULT_DEVICE_TYPE = "11";

    public static final String PAY_TYPE_W01 ="W01";
    public static final String PAY_TYPE_W02 ="W02";
    public static final String PAY_TYPE_W06 ="W06";
    public static final String PAY_TYPE_A01 ="A01";
    public static final String PAY_TYPE_A02 ="A02";
    public static final String PAY_TYPE_A03 ="A03";
    public static final String PAY_TYPE_Q01 ="Q01";
    public static final String PAY_TYPE_Q02 ="Q02";
    public static final String PAY_TYPE_U01 ="U01";
    public static final String PAY_TYPE_U02 ="U02";
    public static final String PAY_TYPE_S03 ="S03";

    public static final String EXTEND_PARAMS_KEY_ALIPAY_FOOD_ORDER_TYPE = "food_order_type";
    public static final String EXTEND_PARAMS_KEY_ALIPAY_EXT_USER_INFO = "ext_user_info";
    public static final String EXTEND_PARAMS_KEY_ALIPAY_INDUSTRY_REFLUX_INFO = "industry_reflux_info";
    public static final String EXTEND_PARAMS_KEY_ALIPAY_SYS_SERVICE_PROVIDER_ID = "sys_service_provider_id";
    public static final String EXTEND_PARAMS_KEY_ALIPAY_CARD_TYPE = "card_type";
    public static final String EXTEND_PARAMS_KEY_SPECIFIED_SELLER_NAME = "specified_seller_name";
    public static final String EXTEND_PARAMS_KEY_DYNAMIC_TOKEN_OUT_BIZ_NO = "DYNAMIC_TOKEN_OUT_BIZ_NO";

    public static final String EXTEND_PARAMS_KEY_WEIXIN_DEVICE_INFO = "device_info";
    public static final String EXTEND_PARAMS_KEY_WEIXIN_ATTACH = "attach";
    public static final String EXTEND_PARAMS_KEY_WEIXIN_STORE_INFO = "store_info";

    public static final String EXTEND_PARAMS_KEY_UNIONPAY_UPTERMNO = "uptermno";
    public static final String AUTH_TYPE_WX = "01";
    public static final String AUTH_TYPE_UNION = "02";

    public static final String DUPLICATE_TRANSACTION_ERROR_MSG = "交易流水号重复";



    // 标准交易类型
    /** 消费 */
    public static final String CONSUME = "VSP001";
    /** 消费撤销 */
    public static final String CONSUME_CANCEL = "VSP002";
    /** 退货 */
    public static final String REFUND = "VSP003";
    /** 预授权 */
    public static final String PRE_AUTH = "VSP004";
    /** 预授权撤销 */
    public static final String PRE_AUTH_CANCEL = "VSP005";
    /** 预授权完成 */
    public static final String PRE_AUTH_COMPLETE = "VSP006";
    /** 预授权完成撤销 */
    public static final String PRE_AUTH_COMPLETE_CANCEL = "VSP007";
    /** 手工退货登记 */
    public static final String MANUAL_REFUND_REGISTRATION = "VSP008";

    // 扫码相关
    /** 扫码预消费 */
    public static final String SCAN_PRE_CONSUME = "VSP011";
    /** 扫码预消费回退 */
    public static final String SCAN_PRE_CONSUME_ROLLBACK = "VSP012";
    /** 扫码预消费完成 */
    public static final String SCAN_PRE_CONSUME_COMPLETE = "VSP013";
    /** 扫码预消费完成退货 */
    public static final String SCAN_PRE_CONSUME_COMPLETE_REFUND = "VSP014";

    // 冲正相关
    /** 消费冲正 */
    public static final String CONSUME_REVERSAL = "CMN001";
    /** 预授权冲正 */
    public static final String PRE_AUTH_REVERSAL = "CMN002";
    /** 预授权完成冲正 */
    public static final String PRE_AUTH_COMPLETE_REVERSAL = "CMN003";

    // 微信支付
    /** 微信支付 */
    public static final String WEIXIN_PAY = "VSP501";
    /** 微信支付撤销 */
    public static final String WEIXIN_PAY_CANCEL = "VSP502";
    /** 微信支付退款 */
    public static final String WEIXIN_PAY_REFUND = "VSP503";

    // 支付宝
    /** 支付宝支付 */
    public static final String ALIPAY_PAY = "VSP511";
    /** 支付宝支付撤销 */
    public static final String ALIPAY_PAY_CANCEL = "VSP512";
    /** 支付宝支付退货 */
    public static final String ALIPAY_PAY_REFUND = "VSP513";

    // 通联钱包
    /** 通联钱包消费 */
    public static final String TONGLIAN_WALLET_CONSUME = "VSP521";
    /** 通联钱包消费撤销 */
    public static final String TONGLIAN_WALLET_CANCEL = "VSP522";
    /** 通联钱包消费退货 */
    public static final String TONGLIAN_WALLET_REFUND = "VSP523";

    // 手机QQ
    /** 手机QQ支付 */
    public static final String MOBILE_QQ_PAY = "VSP505";
    /** 手机QQ支付撤销 */
    public static final String MOBILE_QQ_PAY_CANCEL = "VSP506";
    /** 手机QQ支付退款 */
    public static final String MOBILE_QQ_PAY_REFUND = "VSP507";

    // 扫码支付
    /** 扫码支付 */
    public static final String SCAN_PAY = "VSP541";
    /** 扫码支付撤销 */
    public static final String SCAN_PAY_CANCEL = "VSP542";
    /** 扫码支付退货 */
    public static final String SCAN_PAY_REFUND = "VSP543";

    // 银联扫码
    /** 银联扫码支付 */
    public static final String UNIONPAY_SCAN_PAY = "VSP551";
    /** 银联扫码撤销 */
    public static final String UNIONPAY_SCAN_CANCEL = "VSP552";
    /** 银联扫码退货 */
    public static final String UNIONPAY_SCAN_REFUND = "VSP553";

    // 数字货币
    /** 数字货币支付 */
    public static final String DIGITAL_CURRENCY_PAY = "VSP611";
    /** 数字货币撤销 */
    public static final String DIGITAL_CURRENCY_CANCEL = "VSP612";
    /** 数字货币退货 */
    public static final String DIGITAL_CURRENCY_REFUND = "VSP613";

    /**
     * 借记卡
     **/
    public static final String DEBIT_CARD = "00";

    /**
     * 存折
     **/
    public static final String PASSBOOK = "01";

    /**
     * 信用卡（扫码支付场景专用）
     **/
    public static final String CREDIT_CARD = "02";

    /**
     * 准贷记卡
     **/
    public static final String SEMI_CREDIT_CARD = "03";

    /**
     * 预付卡
     **/
    public static final String PREPAID_CARD = "04";

    /**
     * 境外卡
     **/
    public static final String FOREIGN_CARD = "05";

    /**
     * 其他
     **/
    public static final String OTHER = "99";

}
