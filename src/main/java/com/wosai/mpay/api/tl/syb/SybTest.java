package com.wosai.mpay.api.tl.syb;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;

import java.util.Map;

public class SybTest {

    private static final String BASE_URL = "https://syb-test.allinpay.com/";
    private static final String PAY_URL = BASE_URL + "apiweb/unitorder/pay";
    private static final String SCAN_PAY_URL = BASE_URL + "apiweb/unitorder/scanqrpay";
    private static final String QUERY_URL = BASE_URL + "apiweb/tranx/query";
    private static final String REFUND_URL = BASE_URL + "apiweb/tranx/refund";
    private static final String CANCEL_URL = BASE_URL + "apiweb/tranx/cancel";
    private static final String CLOSE_URL = BASE_URL + "apiweb/tranx/close";
    private static final String REFUND_V2_URL = BASE_URL + "apiweb/unitorder/isvrefund";
    private static final String TEST_NOTIFY_URL = "http://127.0.0.1:9966/upay-gateway/upay/v2/notify/tlSyb/31a28ac3677e497c4d042d3925ee8214/83937%267894259268372772%262%2621590000001178645%2647c6fafa-3366-4e92-9160-d31f5495afe5%264%261686557512248%26100%260%260";

    private static final String APP_ID = "00000051";
    private static final String CUS_ID = "990581007426001";
    private static final String PRIVATE_KEY = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgNqz1EieIP8QVzV7vEmx5e8f7XN7/MIzoeXgEinxcG0agCgYIKoEcz1UBgi2hRANCAAQNfkEgaCQ4cdZ4aD2LWMcnkk5LALQfL05oY8x8XQDIyUM44N15YcTwtFNvHYgyeNRa93vlEUutp935n6rp4yuf";
    private static final String NOTIFY_PRIVATE_KEY = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgjj4Rk+b0YjwO+UwXofnHf4bK+kaaY5Btkd8nMP2VimmgCgYIKoEcz1UBgi2hRANCAAQqlALW4qGC3bP1x3wo5QsKxaCMEZJ2ODTTwOQ+d8UGU7GoK/y/WMBQWf5upMnFU06p5FxGooXYYoBtldgm03hq";
    private static final String prikey = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQg2vB4g3/SYQuv57mJAM+JeLC+vhsdtKRso3opi3LMtrGgCgYIKoEcz1UBgi2hRANCAARvoxxSJjXnjicwN7GbtFiGSJExLTJBiy+9a/MmTB1KYp+PT/gFTv9nbtYJ0xdDPDWnXB1T/bTSoyFMHjG7DspR";

    private static final String DEFAULT_AMT_MAX = "200";//最高金额为200元
    private static final String DEFAULT_RATE_MAX = "0.3"; //最高比例
    private static final String DEFAULT_SHARE_TYPE = "03";//动态分账
    private static final String DEFAULT_LIMIT_TYPE = "1";//0-比例超限或者金额超限都不通过 1-比例不超限或者金额不超限都通过
    private static final String DEFAULT_FORMAT_REASON = "分给%s";

    public static void main(String[] args) throws Exception {
//        testPay();
//        testScanPay();
//        testQuery();
//        testRefund();
//        testCancel();
//        testClose();
//        testNotify();
//        testBindProfit();
//        testSharingPay();
//        testSharingRefund();
//        testSharingPayQuery();
//        testSharingRefundQuery();
//        testPay();
        testRefundV2();
    }

    

    private static void testPay() throws Exception {
        TlSybClient tlSybClient = new TlSybClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.APPID, APP_ID);
        builder.set(BusinessFields.CUSID, "56261105812YTTQ");
        builder.set(BusinessFields.ORGID,"660290000000J8Y");
        builder.set(BusinessFields.TRXAMT, "1");
        builder.set(BusinessFields.REQSN, "78941000000000");
        builder.set(BusinessFields.PAYTYPE, "U02");
        builder.set(BusinessFields.ACCT, "99061105812A6NJ");
        builder.set(BusinessFields.CUSIP,"*************");
        builder.set(BusinessFields.CUSIP,"*************");
        builder.set(BusinessFields.REMARK,"");

//        builder.set(BusinessFields.SUB_APPID,"wx72534f3638c59073");

//        builder.set(BusinessFields.ACCT,"2088000092984543");
        builder.set(BusinessFields.SIGNTYPE, SybConstants.DEFAULT_SIGN_TYPE);

        System.out.println(tlSybClient.call(PAY_URL, APP_ID, PRIVATE_KEY, builder.build()));
    }

    private static void testScanPay() throws Exception {
        TlSybClient tlSybClient = new TlSybClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.APPID, APP_ID);
        builder.set(BusinessFields.CUSID, CUS_ID);
        builder.set(BusinessFields.TRXAMT, "10");
        builder.set(BusinessFields.REQSN, "78940000000318");
        builder.set(BusinessFields.SIGNTYPE, SybConstants.DEFAULT_SIGN_TYPE);
        builder.set(BusinessFields.AUTHCODE, "132184580114718547");
        Map<String, String> map = MapUtil.hashMap(BusinessFields.TERMSN, "dfjskljioe13238023", BusinessFields.LONGITUDE, "+121.48352", BusinessFields.LATITUDE, "-03.561345",
                BusinessFields.TERMNO, "00000001", BusinessFields.DEVICETYPE, "04");
        builder.set(BusinessFields.TERMIINFO, JsonUtil.toJsonStr(map));


        System.out.println(tlSybClient.call(SCAN_PAY_URL, APP_ID, PRIVATE_KEY, builder.build()));
    }


    private static void testQuery() throws Exception {
        TlSybClient tlSybClient = new TlSybClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.APPID, APP_ID);
        builder.set(BusinessFields.CUSID, CUS_ID);
        builder.set(BusinessFields.TRXID, "230606118000028846");
        builder.set(BusinessFields.SIGNTYPE, SybConstants.DEFAULT_SIGN_TYPE);
        System.out.println(tlSybClient.call(QUERY_URL, APP_ID, PRIVATE_KEY, builder.build()));
    }

    private static void testRefund() throws Exception {
        TlSybClient tlSybClient = new TlSybClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.APPID, APP_ID);
        builder.set(BusinessFields.CUSID, CUS_ID);
        builder.set(BusinessFields.TRXAMT, "1");
        builder.set(BusinessFields.REQSN, "78930000000010");
        builder.set(BusinessFields.OLDTRXID, "230605111600025064");
        builder.set(BusinessFields.SIGNTYPE, SybConstants.DEFAULT_SIGN_TYPE);

        System.out.println(tlSybClient.call(REFUND_URL, APP_ID, PRIVATE_KEY, builder.build()));
    }

    private static void testRefundV2() throws Exception {
        TlSybClient tlSybClient = new TlSybClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.ORGID,"");
        builder.set(BusinessFields.APPID, "");
        builder.set(BusinessFields.CUSID, "");
        builder.set(BusinessFields.ISV_CUSID, "");
        builder.set(BusinessFields.SUB_MCHID, "");
        builder.set(BusinessFields.TOTAL_AMOUNT, "1");
        builder.set(BusinessFields.REFUND_AMOUNT, "1");
        builder.set(BusinessFields.REFUND_CUS_AMOUNT, "1");
        builder.set(BusinessFields.REFUND_FEE, "0");
        builder.set(BusinessFields.REFUND_TRADE_NO, "");
        builder.set(BusinessFields.OUT_TRADE_NO, "");
        builder.set(BusinessFields.CHNLTYPE, "WEIXIN");
        builder.set(BusinessFields.CHANNEL_ID, "");
        builder.set(BusinessFields.SIGNTYPE, SybConstants.DEFAULT_SIGN_TYPE);
        builder.set(BusinessFields.VERSION, "12");
        System.out.println(tlSybClient.call(REFUND_V2_URL, "", "", builder.build()));
    }


    private static void testCancel() throws Exception {
        TlSybClient tlSybClient = new TlSybClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.APPID, APP_ID);
        builder.set(BusinessFields.CUSID, CUS_ID);
        builder.set(BusinessFields.TRXAMT, "1");
        builder.set(BusinessFields.OLDTRXID,"230605117000029425");
        builder.set(BusinessFields.REQSN, "78920000000012");
        builder.set(BusinessFields.SIGNTYPE, SybConstants.DEFAULT_SIGN_TYPE);
        System.out.println(tlSybClient.call(CANCEL_URL, APP_ID, PRIVATE_KEY, builder.build()));
    }

    private static void testClose() throws Exception {
        TlSybClient tlSybClient = new TlSybClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.APPID, APP_ID);
        builder.set(BusinessFields.CUSID, CUS_ID);
        builder.set(BusinessFields.OLDTRXID, "230605117000029426");
        builder.set(BusinessFields.SIGNTYPE, SybConstants.DEFAULT_SIGN_TYPE);
        System.out.println(tlSybClient.call(CLOSE_URL, APP_ID, PRIVATE_KEY, builder.build()));
    }

    private static void testNotify() throws Exception{
        TlSybClient tlSybClient = new TlSybClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.APPID, APP_ID);
        builder.set(BusinessFields.CUSID, CUS_ID);
        builder.set(BusinessFields.TRXAMT, "1");
        builder.set(BusinessFields.REQSN, "7894259268372772");
        builder.set(BusinessFields.SIGNTYPE, SybConstants.DEFAULT_SIGN_TYPE);
        builder.set(BusinessFields.AUTHCODE, "280173021511976232");
        System.out.println(tlSybClient.call(TEST_NOTIFY_URL, APP_ID, PRIVATE_KEY, builder.build()));

    }

    private static void testBindProfit() throws MpayException, MpayApiNetworkError {

        String prikey = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQg2vB4g3/SYQuv57mJAM+JeLC+vhsdtKRso3opi3LMtrGgCgYIKoEcz1UBgi2hRANCAARvoxxSJjXnjicwN7GbtFiGSJExLTJBiy+9a/MmTB1KYp+PT/gFTv9nbtYJ0xdDPDWnXB1T/bTSoyFMHjG7DspR";
        TlSybClient tlSybClient = new TlSybClient();
        String url = "https://syb-test.allinpay.com/vsppcusapi/sybserviceapi/cusasagreeConfig";
        RequestBuilder builder = new RequestBuilder();
        //55058100780004S
        builder.set(BusinessFields.APPID, "00000005");
        builder.set(BusinessFields.CUSID, "990581007426001");
        builder.set(BusinessFields.ORGID, "55058100780004S");

        builder.set(BusinessFields.PAYEECUSID, "100581058141619");
        builder.set(BusinessFields.SHARETYPE, DEFAULT_SHARE_TYPE);
        builder.set(BusinessFields.LIMITTYPE, DEFAULT_LIMIT_TYPE);
        builder.set(BusinessFields.AMTMAX, DEFAULT_AMT_MAX);
        builder.set(BusinessFields.RATEMAX, DEFAULT_RATE_MAX);
        builder.set(BusinessFields.REASON, String.format(DEFAULT_FORMAT_REASON, "测试"));
        builder.set("signtype","SM2");
        System.out.println(JsonUtil.toJsonStr(tlSybClient.call(url, "00000005", prikey, builder.build())));

    }

    private static void testSharingPay() throws MpayException, MpayApiNetworkError {
        //230628117100028178
        TlSybClient tlSybClient = new TlSybClient();
        String url = "https://syb-test.allinpay.com/apiweb/trxshare/share";
        RequestBuilder builder = new RequestBuilder();
        //55058100780004S
        builder.set(BusinessFields.APPID, "00000005");
        builder.set(BusinessFields.CUSID, "990581007426001");
        builder.set(BusinessFields.ORGID, "55058100780004S");
        builder.set("signtype","SM2");

        builder.set(BusinessFields.OLDTRXID, "230628117100028178");
        builder.set(BusinessFields.REQSN, "78970000000318");
        builder.set(BusinessFields.PAYEECUSID, "100581058141619");

        builder.set(BusinessFields.TRXAMT, "1");

        System.out.println(JsonUtil.toJsonStr(tlSybClient.call(url, "00000005", prikey, builder.build())));
        //{"appid":"00000005","cusid":"990581007426001","oldtrxid":"230628117100028178","orgid":"55058100780004S","payeecusid":"100581058141619","randomstr":"-8973147579246927899","reqsn":"78970000000318","sign":"z59vckl3NB3Y+/oNcBZgm7t9wK/rLS3vplPChKoifPRqzzk4UWbGXuLHOtjlfz1VEEt8/gnM9K2htMaBS8w3IQ==","signtype":"SM2","trxamt":"1","version":"11"}
        //{"appid":"00000005","cusid":"990581007426001","errmsg":"分账成功","fintime":"20230629094404","payeetrxid":"230629115800025460","payertrxid":"230629117400025087","randomstr":"098686403106","reqsn":"78970000000318","retcode":"SUCCESS","sign":"7Nnkh+EaINUAMhNcwCUzFLgGPKYBDqQzE2v/1PAKiTMK4wXg9Vu9YrHyn4RlYWtUStYJvjoy9+6Iu+XHtzjkCQ==","trxcode":"100103","trxstatus":"0000"}
    }
    private static void testSharingRefund() throws MpayException, MpayApiNetworkError {
        TlSybClient tlSybClient = new TlSybClient();
        String url = "https://syb-test.allinpay.com/apiweb/trxshare/revoke";
        RequestBuilder builder = new RequestBuilder();
        //55058100780004S
        builder.set(BusinessFields.APPID, "00000005");
        builder.set(BusinessFields.CUSID, "990581007426001");
        builder.set(BusinessFields.ORGID, "55058100780004S");
        builder.set("signtype","SM2");

        builder.set(BusinessFields.PAYERTRXID, "230629117400025087");
        builder.set(BusinessFields.REQSN, "78970000000319");
//        builder.set(BusinessFields.PAYEECUSID, "100581058141619");

        builder.set(BusinessFields.TRXAMT, "1");

        System.out.println(JsonUtil.toJsonStr(tlSybClient.call(url, "00000005", prikey, builder.build())));
        //{"appid":"00000005","cusid":"990581007426001","orgid":"55058100780004S","payertrxid":"230629117400025087","randomstr":"943586304132648040","reqsn":"78970000000319","sign":"narcbl7L6WYjIx3Vr7Mc0ywEI9ypCHBSCXaWpQcI04vCuqM9UcwaTpxSIt2GbmWUrzx3/5pZzPaT929RnwAigQ==","signtype":"SM2","trxamt":"1","version":"11"}
        //{"appid":"00000005","cusid":"990581007426001","errmsg":"分账回退成功","fintime":"20230629095327","payertrxid":"230629115900026669","randomstr":"879207106091","reqsn":"78970000000319","retcode":"SUCCESS","rktrxid":"230629112900024856","sign":"v92Xhkm1H0m0ULj366Ayz5zk7oJGcFq+6p+XDn0bUxif/xfQiueMVqbHH9yNHsekRMFMC6pxQrtgiOtQ3XkTKA==","trxcode":"100102","trxstatus":"0000"}
    }

    private static void testSharingPayQuery() throws MpayException, MpayApiNetworkError {
        //230628117100028178
        TlSybClient tlSybClient = new TlSybClient();
        String url = "https://syb-test.allinpay.com/apiweb/trxshare/query";
        RequestBuilder builder = new RequestBuilder();
        //55058100780004S
        builder.set(BusinessFields.APPID, "00000005");
        builder.set(BusinessFields.CUSID, "990581007426001");
        builder.set(BusinessFields.ORGID, "55058100780004S");
        builder.set("signtype","SM2");
        builder.set(BusinessFields.PAYERTRXID, "230629117400025087");
//        builder.set(BusinessFields.PAYERTRXID, "230629117400025087");
        System.out.println(JsonUtil.toJsonStr(tlSybClient.call(url, "00000005", prikey, builder.build())));
        //{"appid":"00000005","cusid":"990581007426001","orgid":"55058100780004S","payertrxid":"230629117400025087","randomstr":"4284142937456438015","sign":"+DFV81qgU64AATiBtbxyqqDNihJzZUV+ufEVN/ml8bohC5sS9DTG3gFCC1RX4Um9pWNn/oHPmmT18fJEmcL/bg==","signtype":"SM2","version":"11"}
        //{"appid":"00000005","cusid":"990581007426001","fee":"0","fintime":"20230629094404","payeecusid":"100581058141619","payertrxid":"230629117400025087","randomstr":"205712736000","reqsn":"78970000000318","retcode":"SUCCESS","sign":"ZjIr3M9n5WKhuhPLt0ZsJyda/e8CTL0ADv7NmsHgScq2+4SOA3uPnl7KH/CnRs2e0Es9SczmJ6JV3tRJ5RPp4w==","trxamt":"1","trxcode":"100103","trxstatus":"0000"}
    }

    private static void testSharingRefundQuery() throws MpayException, MpayApiNetworkError {
        //230628117100028178
        TlSybClient tlSybClient = new TlSybClient();
        String url = "https://syb-test.allinpay.com/apiweb/trxshare/revokequery";
        RequestBuilder builder = new RequestBuilder();
        //55058100780004S
        builder.set(BusinessFields.APPID, "00000005");
        builder.set(BusinessFields.CUSID, "990581007426001");
        builder.set(BusinessFields.ORGID, "55058100780004S");
        builder.set("signtype","SM2");
        builder.set(BusinessFields.RKTRXID, "230629112900024856");
//        builder.set(BusinessFields.PAYERTRXID, "230629117400025087");
        System.out.println(JsonUtil.toJsonStr(tlSybClient.call(url, "00000005", prikey, builder.build())));
        //{"appid":"00000005","cusid":"990581007426001","orgid":"55058100780004S","randomstr":"5621739860272412307","rktrxid":"230629112900024856","sign":"lZyTRqlJxySpS4ggrL3cnV6zxw3Z1mSbRFzVIFLuPfAw1qDvN4V2Et4sbijs8nmBix+swsgUyRJt0zPUMYfdFw==","signtype":"SM2","version":"11"}
        //{"appid":"00000005","cusid":"990581007426001","fintime":"20230629095327","payercusid":"100581058141619","randomstr":"366529809943","reqsn":"78970000000319","retcode":"SUCCESS","rktrxid":"230629112900024856","sign":"CJD/DO2LgyGsIb61CaicMzCLAVpwE5gyjltjOEFrnG6bIryuHqBqF7u9DGgPOljvHtSDAGqcQDqni1DYvGNJpA==","trxamt":"1","trxcode":"100102","trxstatus":"0000"}
    }



}
