package com.wosai.mpay.api.cmbcbank;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.mpay.util.WebUtils;
import com.wosai.pantheon.util.MapUtil;

public class CMBCBankClient {
    public static final Logger logger = LoggerFactory.getLogger(CMBCBankClient.class);
    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map<String, Object> call(String serviceUrl, Map<String, Object> request, String publicKey, String privateKey,
            String privateKeyPwd) throws MpayApiNetworkError, MpayException {
        String jsonbody = JsonUtil.toJsonStr(request);
        Map<String,Object> map = new HashMap<String,Object>();
        String sign = CmbcBankSignUtil.getSign(privateKey, privateKeyPwd, jsonbody);
        map.put(ProtocolFields.SIGN, sign);
        map.put(ResponseFields.BODY, jsonbody);
        String jsonRequest = JsonUtil.toJsonStr(map);
        logger.debug("orig request {}", jsonRequest);
        String context = String.format("{\"businessContext\":\"%s\",\"merchantNo\":\"\",\"merchantSeq\":\"\",\"reserve1\":\"\",\"reserve2\""
                  + ":\"\",\"reserve3\":\"\",\"reserve4\":\"\",\"reserve5\":\"\",\"reserveJson\":\"\",\"securityType\":\"\",\"sessionId\":\"\",\"source\":\"\","
                  + "\"transCode\":\"\",\"transDate\":\"\",\"transTime\":\"\",\"version\":\"\"}", CmbcBankSignUtil.encrypt(publicKey, jsonRequest));

       logger.debug("encrypt request {}", context);
       String response = WebUtils.doPost(null, null, serviceUrl, "application/json;charset=UTF-8", context.getBytes(), connectTimeout, readTimeout);
       logger.debug("encrypt response {}", response);
       Map<String,Object> jsonResp = JsonUtil.jsonStrToObject(response, Map.class);
       String businessContext = MapUtil.getString(jsonResp, ResponseFields.BUSINESS_CONTEXT);
       String origResponse = response;
       if (null != businessContext && !"".equals(businessContext)){
           String dncryptBusinessContext = CmbcBankSignUtil.dncrypt(privateKey, privateKeyPwd, businessContext);
           jsonResp.put(ResponseFields.BUSINESS_CONTEXT, dncryptBusinessContext);
           origResponse = JsonUtil.toJsonStr(jsonResp);

           Map<String, Object> businessContextMap = JsonUtil.jsonStrToObject(dncryptBusinessContext, Map.class);
           jsonResp.put(ResponseFields.BUSINESS_CONTEXT, businessContextMap);
           if(null != businessContextMap.get(ResponseFields.BODY)){
               Map<String, Object> bodyMap = JsonUtil.jsonStrToObject((String)businessContextMap.get(ResponseFields.BODY), Map.class);
               Map<String, Object> centerInfo = parseCenterInfo(MapUtil.getString(bodyMap, ResponseFields.CENTER_INFO));
               bodyMap.put(ResponseFields.CENTER_INFO, centerInfo);
               businessContextMap.put(ResponseFields.BODY, bodyMap);
           }
           jsonResp.put(ResponseFields.BUSINESS_CONTEXT, businessContextMap);
       }
       logger.debug("orig response {}", origResponse);
       return jsonResp;
    }

    private Map<String, Object> parseCenterInfo(String centerInfo) {
        Map<String, Object> centerInfoMap = new HashMap<String, Object>();
        if (!StringUtils.isEmpty(centerInfo)) {
            for (String info : centerInfo.split("\\|")) {
                if (info.isEmpty()) {
                    continue;
                }
                String[] infos = info.split("=");
                if (infos.length == 2) {
                    centerInfoMap.put(infos[0], infos[1]);
                }
            }
        }
        return centerInfoMap;
    }
}
