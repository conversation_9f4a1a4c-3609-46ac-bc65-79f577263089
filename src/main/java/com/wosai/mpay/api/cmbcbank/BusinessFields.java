package com.wosai.mpay.api.cmbcbank;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/7/5.
 */
public class BusinessFields {
    public static final String PLATFORM_ID = "platformId";// 接入平台号	
    public static final String MERCHANT_NO = "merchantNo";// 民生商户号	
    public static final String SELECT_TRADE_TYPE = "selectTradeType";// 支付类型	
    public static final String AMOUNT = "amount";// 交易金额,以分为单位
    public static final String ORDER_INFO = "orderInfo";// 商户订单内容，商品信息
    public static final String MERCHANT_SEQ = "merchantSeq";// 商户流水号，商户须保证流水唯一，建议是商户平台号+8位日期+商户自定的订单号
    public static final String TRANS_DATE = "transDate";// 订单日期	yyyyMMdd
    public static final String TRANS_TIME = "transTime";// 订单时间	yyyyMMddHHmmssSSS
    public static final String NOTIFY_URL = "notifyUrl";// 通知地址
    public static final String REMARK = "remark";// 备注信息 反扫模式下该部分必输，填扫描客户二维码Base64后的值
    public static final String SUB_APP_ID = "subAppId";// 子商户appId 公众号支付API下，该部分必输，填子商户appId
    public static final String SUB_OPEN_ID = "subOpenId";//subOpenId 公众号支付API下，该部分必输，填子商户openId
    public static final String TRADE_TYPE = "tradeType";// 查询类型	 1.支付 2.退款
    public static final String RESERVE = "reserve";// 备注字段
    public static final String ORGVOUCHER_NO = "orgvoucherNo";// 当查询类型为退款时必须输入
    public static final String USER_ID = "userId"; // 支付宝 - 用户ID
    public static final String GPS = "gps"; // 定位信息
    public static final String STATION = "station"; // 基站信息
    public static final String ORDER_AMOUNT = "orderAmount";
    public static final String TERM_IP = "termIP";  // 商户端设备 IP
    public static final String IN_EXT_DAT = "inExtData"; // 上送透传给第三方的信息
    public static final String MCH_SEQ_NO = "mchSeqNo"; // 商户流水号，本次交易流水，商户自主生成
    public static final String ORDER_NO = "orderNo"; // 原交易商户订单号

    public static final String CONFIG_PLATFORM_ID = "platform_id";
    public static final String CONFIG_OPER_ID = "oper_id";
    public static final String CONFIG_PRIVATE_KEY = "private_key";
    public static final String CONFIG_PRIVATE_PWD = "private_pwd";
    public static final String CONFIG_PARENT_MCHNT_ID = "parent_mchnt_id";
    public static final String CONFIG_PUBLIC_KEY = "public_key";
    public static final String CMBCBANK_MCHNT_ID = "cmbc_mchnt_id";
    
}
