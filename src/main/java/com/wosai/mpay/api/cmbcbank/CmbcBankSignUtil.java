package com.wosai.mpay.api.cmbcbank;

import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;

import cfca.sadk.algorithm.common.Mechanism;
import cfca.sadk.algorithm.common.PKIException;
import cfca.sadk.algorithm.sm2.SM2PrivateKey;
import cfca.sadk.lib.crypto.JCrypto;
import cfca.sadk.lib.crypto.Session;
import cfca.sadk.util.CertUtil;
import cfca.sadk.util.EnvelopeUtil;
import cfca.sadk.util.KeyUtil;
import cfca.sadk.x509.certificate.X509Cert;

public class CmbcBankSignUtil {
    private static Session session;

    static {
        try {
            JCrypto.getInstance().initialize(JCrypto.JSOFT_LIB, null);
            session = JCrypto.getInstance().openSession(JCrypto.JSOFT_LIB);
        } catch (PKIException e) {
            throw new RuntimeException("民生银行支付环境启动失败", e);
        }
    }

    /**
     * 签名
     * 
     * @param sign
     * @param context
     * @return
     */
    public static String sign(String sign, String context) {
        Map<String, String> paramMap = new HashMap<String, String>();
        paramMap.put("sign", sign);
        paramMap.put("body", context);
        return JsonUtil.toJsonStr(paramMap); // 待加密字符串
    }

    /**
     * 加密
     * 
     * @param signContext
     *            需要加密的报文
     * @return
     * @throws BuilderException 
     */
    public static String encrypt(String publicKey, String signContext) throws MpayException {
        try {
            X509Cert cert = new X509Cert(Base64.getDecoder().decode(publicKey.getBytes()));
            X509Cert[] certs = { cert };
            return new String(EnvelopeUtil.envelopeMessage(signContext.getBytes("UTF8"), Mechanism.SM4_CBC, certs), "UTF-8");
        } catch (Exception e) {
            throw new MpayException("加密失败", e);
        }
    }

    /**
     * 解密
     * 
     * @param encryptContext
     *            需要解密的报文
     * @return
     * @throws BuilderException 
     */
    public static String dncrypt(String privateKey, String priKeyPWD, String encryptContext) throws MpayException {
        String decodeText = null;
        byte [] privateKeyBytes = Base64.getDecoder().decode(privateKey);
        try {
            PrivateKey priKey = KeyUtil.getPrivateKeyFromSM2(privateKeyBytes, priKeyPWD);
            X509Cert cert = CertUtil.getCertFromSM2(privateKeyBytes);
            byte[] sourceData = EnvelopeUtil.openEvelopedMessage(encryptContext.getBytes("UTF8"), priKey, cert, session);
            decodeText = new String(sourceData, "UTF8");
        } catch (Exception e) {
            throw new MpayException("解密失败", e);
        }
        return decodeText;
    }

    /**
     * 验证签名
     * 
     * @param decryptContext
     *            需要验证签名的明文
     * @return
     * @throws BuilderException 
     */
    public static boolean signCheck(String publicKey, String decryptContext) throws MpayException {
        @SuppressWarnings("unchecked")
        Map<String, Object> paraMap = JsonUtil.jsonStrToObject(decryptContext, Map.class);
        String sign = paraMap.get("sign").toString();
        String body = paraMap.get("body").toString();
        boolean isSignOK = false;
        try {
            X509Cert cert = new X509Cert(Base64.getDecoder().decode(publicKey.getBytes()));
            PublicKey pubKey = cert.getPublicKey();
            isSignOK = new cfca.sadk.util.Signature().p1VerifyMessage(Mechanism.SM3_SM2, body.getBytes("UTF8"), sign.getBytes(), pubKey, session);
        } catch (Exception e) {
            throw new MpayException("验签失败", e);
        }
        return isSignOK;
    }

    public static String getSign(String privateKey, String pwd, String context) throws MpayException {
        String sign = "";
        try {
            SM2PrivateKey priKey = KeyUtil.getPrivateKeyFromSM2(Base64.getDecoder().decode(privateKey.getBytes()), pwd);
            sign = new String(new cfca.sadk.util.Signature().p1SignMessage(Mechanism.SM3_SM2, context.getBytes("UTF-8"), priKey, session));
        } catch (Exception e) {
            throw new MpayException("签名失败", e);
        }
        return sign;
    }
}
