package com.wosai.mpay.api.cmbcbank;

public class CMBCBankConstants {

    public static final String DATE_TIME_FORMAT_DATE = "yyyyMMdd";
    public static final String DATE_TIME_FORMAT_TIME = "yyyyMMddHHmmssSSS";
    public static final String PAY_WAY_ALIPAY_SCAN = "API_ZFBSCAN"; // 支付宝反扫
    public static final String PAY_WAY_ALIPAY_QRCODE = "API_ZFBQRCODE"; // 支付宝正扫
    public static final String PAY_WAY_ALIPAY_H5_ZFBJSAPI = "H5_ZFBJSAPI"; // 支付宝服务窗
    public static final String PAY_WAY_WEIXIN_SCAN = "API_WXSCAN"; // 微信反扫
    public static final String PAY_WAY_WEIXIN_QRCODE = "API_WXQRCODE"; // 微信正扫
    public static final String PAY_WAY_WEIXIN_H5_WXJSAPI = "H5_WXJSAPI"; // 微信公众号跳转支付
    public static final String PAY_WAY_WEIXIN_WX_APPLET = "WX_APPLET"; // 微信小程序
    public static final String PAY_WAY_API_UNIONQRCODE = "API_UNIONQRCODE"; // 银联聚合码（原银联正扫）
    public static final String PAY_WAY_DCNY_SCAN = "DCNY_SCAN"; // 数字货币反扫
    public static final String PAY_WAY_DCNY_QRCODE = "DCNY_QRCODE"; // 数字货币正扫
    public static final String PAY_WAY_API_CMBCSCAN = "API_CMBCSCAN"; // 民生反扫

    public static final String GATE_RETURN_TYPE_SUCCESS = "S";
    public static final String GATE_RETURN_TYPE_E = "E";
    public static final String GATE_RETURN_TYPE_R = "R";
    public static final String TRADE_STATUS_R = "R"; // R 原订单成功，未支付（待支付)
    public static final String TRADE_STATUS_S = "S"; // R 订单交易成功
    public static final String TRADE_STATUS_E = "E"; // E 订单失败
    public static final String TRADE_STATUS_C = "C"; // C 已撤销（理论上不存在) 已关闭
    public static final String TRADE_STATUS_T = "T"; // T 订单转入退款

    public static final String RED_CHNL_ECASH = "ECASH"; // 数字人民币权益
    public static final String RED_CHNL_CMBC = "CMBC"; // 民生银行自有权益
    public static final String RED_CHNL_ALPAY = "ALPAY"; // 支付宝
    public static final String RED_CHNL_WXPAY = "WXPAY"; // 微信
    public static final String RED_CHNL_UPAY = "UPAY"; // 银联二维码


    public static final String CARD_TYPE_CREDIT = "1"; // 贷记卡
    public static final String CARD_TYPE_DEBIT = "0"; // 借记卡

    public static final String RETURN_MESSAGE_REPLACE = "流水号：[0-9a-zA-Z]*,";
    public static final String RETURN_MESSAGE_PAY_REPEAT = "收单系统出现错误:流水号重复,请重新下单";
    public static final String RETURN_MESSAGE_PAY_INVALID_BARCODE = "每个二维码仅限使用一次，请刷新再试AUTH_CODE_INVALID";
    public static final String RETURN_MESSAGE_CANCEL_FAIL_PAY_SUCESS = "已支付完成，不允许关单";
    public static final String RETURN_MESSAGE_REFUND_REPEAT = "原交易已退货完成";
    public static final String RETURN_MESSAGE_QUERY_ORDER_NOT_EXISTS = "收单系统出现错误:查询日期超限或查询记录不存在, 请通过对账文件进行核对!";
    
}
