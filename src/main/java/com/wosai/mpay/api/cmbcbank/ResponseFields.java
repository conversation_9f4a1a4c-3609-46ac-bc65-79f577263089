package com.wosai.mpay.api.cmbcbank;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/7/5.
 */
public class ResponseFields {

    public static final String BUSINESS_CONTEXT= "businessContext";
    public static final String BODY = "body";
    public static final String GATE_RETURN_TYPE = "gateReturnType"; // S:成功，E:失败，R:待查
    public static final String GATE_RETURN_CODE = "gateReturnCode";
    public static final String GATE_RETURN_MESSAGE = "gateReturnMessage";

    public static final String PAY_INFO = "payInfo";// 微信/支付宝正扫下单返回的是base64二维码字符串 公众号支付API下单返回的是prepay_id
    public static final String MERCHANT_NAME = "merchantName"; // 商户名
    public static final String MERCHANT_SEQ = "merchantSeq";// 商户订单号
    public static final String AMOUNT = "amount";// 交易金额
    public static final String ORDER_INFO = "orderInfo";// 订单详情
    public static final String REMARK = "remark";//备注
    public static final String VOUCHER_NO = "voucherNo";//收单凭证号
    public static final String BANK_TRADE_NO = "bankTradeNo";//收单系统流水号
    public static final String TRADE_STATUS = "tradeStatus";//交易结果 S 订单交易成功 E 订单失败 R 原订单成功，未支付（待支付) C 已撤销（理论上不存在) 已关闭 T 订单转入退款
    public static final String REF_NO = "refNo";//参考号
    public static final String BATCH_NO = "batchNo";//批次号
    public static final String CARD_TYPE = "cardType";//卡类型
    public static final String CARD_NO = "cardNo";//卡号 前六后四中间*
    public static final String CB_CODE = "cbCode";//发卡行行号
    public static final String CARD_NAME = "cardName";//发卡行行名
    public static final String FEE = "fee";//交易手续费
    public static final String TRANS_TYPE = "transType";//交易类型
    public static final String CUP_TERM_ID = "cupTermId";//银联终端号
    public static final String CUP_TSAM_NO = "cupTsamNo";//设备序列号
    public static final String CENTER_INFO = "centerInfo";//其他信息
    public static final String CENTER_SEQ_ID = "centerSeqId";//微信订单号	
    public static final String BANK_ORDER_NO = "bankOrderNo";//收单到微信下单编号	
    public static final String PIC_URL = "picURL";//支付地址
    public static final String TRAN_DATE = "tranDate"; //格式：YYYYMMDD
    public static final String TRAN_TIME = "tranTime"; //格式：HHMMSS
    public static final String ACTUAL_PAY_AMT = "actualPayAmt"; // 实付金额

    public static final String RED_INFO = "redInfo"; // 营销信息
    public static final String RED_TOTAL_AMT = "red_total_amt"; // 权益使用总金额
    public static final String RED_TOTAL_CNT = "red_total_cnt"; // 权益使用总笔数
    public static final String RED_LIST = "red_list"; // 权益使用明细
    public static final String RED_ORG = "red_org"; // 营销活动运营机构
    public static final String RED_NAME = "red_name"; // 营销活动名称
    public static final String red_type = "red_type"; // 营销活动类型
    public static final String REFUND_TYPE = "refund_type"; // 权益退款属性
    public static final String RED_FAV_AMT = "red_fav_amt"; // 权益金额
    public static final String RED_CHNL = "red_chnl"; // 权益渠道

    // 支付宝
    public static final String BUYER_LOGON_ID = "buyer_logon_id";
    public static final String BUYER_ID = "buyer_id";
    public static final String BUYER_USER_ID = "buyer_user_id";
    public static final String FUND_BILL_LIST = "fund_bill_list";

    // 微信
    public static final String OPEN_ID = "openid";

    public static final String BANK_TYPE = "bank_type";
    public static final String PREPAY_ID = "prepayId";
    public static final String TRADE_NO = "tradeNo";

}
