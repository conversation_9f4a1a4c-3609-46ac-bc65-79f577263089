package com.wosai.mpay.api.lakala.open;

public class ResponseFields {

    public static final String RET_CODE = "retCode";// 返回业务代码 返回业务代码(000000为成功，其余按照错误信息来定)
    public static final String RET_MSG = "retMsg";// 返回业务代码描述
    public static final String RESP_DATA = "respData";// 返回数据.下文定义的响应均为该属性中的内容

    public static final String TRADE_TIME = "tradeTime"; // 平台交易时间yyyyMMddHHmmss
    public static final String ORDER_ID = "orderId"; // 如果请求中携带，则返回
    public static final String LKL_ORDER_ID = "lklOrderId"; // 拉卡拉订单号
    public static final String PAY_MODE = "payMode"; // 微信：WECHAT 支付宝：ALIPAY 银联：UQRCODEPAY 翼支付: BESTPAY 苏宁易付宝: SUNING 数字货币: DCPAY
    public static final String WE_ORDER_NO = "weOrderNo"; // 账户端交易订单号
    public static final String OPEN_ID = "openId"; // 用户在商户appid下的唯一标识
    public static final String SETTLEMENT_TOTAL_FEE = "settlementTotalFee"; // 应结订单金额，单位分
    public static final String CLIENT_DISCOUNT_AMT = "clientDiscountAmt"; // 账户端返回商户优惠金额，单位分
    public static final String SERVER_DISCOUNT_AMT = "serverDiscountAmt"; // 账户端返回账户端优惠金额，单位分
    public static final String PLATFORM_DISCOUNT_AMOUNT = "platformDiscountAmount"; // 平台优惠金额，单位分
    public static final String AMOUNT = "amount"; // 单位分
    public static final String TRADE_STATE = "tradeState"; // INIT-初始化 CREATE-下单成功 SUCCESS-交易成功 FAIL-交易失败 DEAL-交易处理中 UNKNOWN-未知状态 CLOSE-订单关闭 PART_REFUND-部分退款 REFUND-全部退款 REVOKED-订单撤销
    public static final String PAY_TIME = "payTime"; // 实际支付时间。yyyyMMddHHmmss
    public static final String WE_ORDERNO = "weOrderNo"; // 账户端交易订单号
    public static final String LKL_ORDER_NO = "lklOrderNo"; // 拉卡拉生成的交易流水
    public static final String BANK_TYPE = "bankType"; // 付款银行
    public static final String ACTIVITY_ID = "activityId"; // 在账户端商户后台配置的批次 ID
    public static final String UP_ISS_ADDN_DATA = "upIssAddnData"; // 参与单品营销优惠时返回
    public static final String UP_COUPON_INFO = "upCouponInfo"; // 参与单品营销优惠时返回 数字货币中行返回示例说明：[{“fundchannel”:”BOC”,”amount”:”18”}]
    public static final String RESP_RET_CODE = "retcode";// 响应码 resp 中

    // payMode=WECHAT 时返回
    public static final String PAY_SIGN = "paySign"; // 签名
    public static final String APP_ID = "appId"; // 商户注册具有支付权限的公众号成功后即可获得公众号id
    public static final String TIMESTAMP = "timeStamp"; // 当前的时间
    public static final String NONCE_STR = "nonceStr"; // 随机字符串
    public static final String PACKAGE = "package"; // 统一下单接口返回的prepay_id参数值
    public static final String SIGN_TYPE = "signType"; // 签名类型，支持RSA
    public static final String PARTNER_ID = "partnerId"; // 支付分配的从业机构号

    // payMode=ALIPAY,LKLACC 时返回
    public static final String PREPAY_ID = "prepayId"; // 预支付交易会话ID

    // payMode=UQRCODEPAY 时返回
    public static final String REDIRECT_URL = "redirectUrl"; // 银联JS支付返回重定向地址

    // transType=41(NATIVE) 时返回
    public static final String CODE = "code"; // 商户可用此参数自定义去生成二维码后展示出来进行扫码支付
    public static final String CODEIMAGE = "codeImage"; // 商户收款二维码图片。Base64编码，暂无

    // transType=81(支付宝H5支付) 时返回
    public static final String FORM_DATA = "formData"; // 为银联支付宝H5支付时，为开发者生成前台页面请求需要的完整form 表单的 html

    public static final String RET_AMT = "retAmt"; // 单位分
    public static final String REFUND_ORDER_ID = "refundOrderId"; // 如果请求中携带，则返回
    public static final String REFUND_LKL_ORDER_NO = "refundLklOrderNo"; // 拉卡拉退款单号
    public static final String USER_ID = "userId";













}
