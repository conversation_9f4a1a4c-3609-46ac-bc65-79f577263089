package com.wosai.mpay.api.lakala.open;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class LakalaOpenV3Constants {
    public static final String CARD_TYPE_DEBIT_CARD = "00"; // 借记卡
    public static final String CARD_TYPE_CREDIT_CARD = "01"; // 贷记卡
    public static final String NEED_QUERY_FALSE = "0"; // 不需要重新查询，状态已确认
    public static final String COUPON_INFO_SPNSR_ID_UNIONPAY = "00010000"; //银联出资方
    public static final String COUPON_INFO_TYPE_DD01 = "DD01"; //随机立减
    public static final String COUPON_INFO_TYPE_CP01 = "CP01"; //抵金券 1:无需领取，交易时直接适配并承兑的优惠券
    public static final String COUPON_INFO_TYPE_CP02 = "CP02"; //抵金券 2:事前领取，交易时上送银联并承兑的优惠券
    public static String DATE_TIME_FORMAT = "yyyyMMddHHmmss";
    public static final String VERSION = "3.0";

    public enum ResultCode {
        SUCCESS("BBS00000", "成功"),
        SUCCESS_IPS("IPS00000", "成功"),
        TRANSACTION_RESULT_UNKNOWN("BBS11112", "交易结果未知"),
        USER_PAYING("BBS11105", "用户支付中"),
        TRANSACTION_STATUS_PAYING("BBS10000", "交易状态为支付中"),
        SYSTEM_EXCEPTION("BBS00001", "系统异常"),
        NETWORK_REQUEST_FAILED("BBS00100", "网络请求失败"),
        NETWORK_REQUEST_TIMEOUT("BBS00101", "网络请求超时"),
        PARAMETER_VALIDATION_FAILED("BBS11000", "参数校验失败"),
        SIGNATURE_VERIFICATION_FAILED("BBS11200", "验签失败"),
        PARAMETER_NOT_FOUND("BBS11106", "未查到参数"),
        TRANSACTION_NOT_EXIST("BBS11107", "交易不存在"),
        TRANSACTION_NOT_EXIST_IPS("IPS11107", "交易不存在"),
        TRANSACTION_NOT_ALLOWED("BBS11110", "不允许做该交易"),
        ORDER_ID_ALREADY_EXISTS("BBS11111", "订单号已存在"),
        TRANSACTION_RESULT_UNKNOWN_2("BBS11112", "交易结果未知"),
        ORDER_NOT_EXIST("BBS11114", "订单不存在"),
        INSTITUTION_NOT_EXIST("BBS11170", "机构信息不存在"),
        MERCHANT_NOT_EXIST("BBS11172", "商户信息不存在"),
        WECHAT_SUB_MERCHANT_NOT_EXIST("BBS11199", "微信子商户信息不存在"),
        ALIPAY_SUB_MERCHANT_NOT_EXIST("BBS11198", "支付宝子商户信息不存在"),
        UNIONPAY_QR_CODE_SUB_MERCHANT_NOT_EXIST("BBS11197", "银联二维码子商户信息不存在"),
        TERMINAL_NOT_EXIST("BBS11177", "该终端不存在"),
        TERMINAL_CARD_APPLICATION_NOT_EXIST("BBS11178", "该终端卡应用信息不存在"),
        TRANSACTION_PERMISSION_CLOSED("BBS11182", "交易权限已关闭"),
        TRANSACTION_PERMISSION_NOT_OPENED("BBS11183", "交易权限未开通"),
        MERCHANT_DISABLED("BBS11184", "该商户已停用"),
        TERMINAL_DISABLED("BBS11185", "该终端已停用"),
        TRANSACTION_AMOUNT_EXCEEDS_SINGLE_LIMIT("BBS11109", "交易金额超过单笔限额"),
        EXCEEDS_SINGLE_DAY_CUMULATIVE_LIMIT("BBS11109", "已超单日累计限额"),
        EXCEEDS_SINGLE_MONTH_CUMULATIVE_LIMIT("BBS11109", "已超单月累计限额"),
        QR_CODE_NOT_EXIST("BBS16139", "无此二维码"),
        REFUND_ORDER_NOT_EXIST("BBS11203", "商户退款订单号不存在");


        private final String code;
        private final String description;

        ResultCode(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static ResultCode fromCode(String code) {
            for (ResultCode resultCode : ResultCode.values()) {
                if (resultCode.getCode().equals(code)) {
                    return resultCode;
                }
            }
            return null;
        }
    }

    public static final List<String> SUCCESS_LIST = Arrays.asList(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS_IPS.getCode());

    public static final List<String> TRANSACTION_NOT_EXISTS_LIST = Arrays.asList(ResultCode.TRANSACTION_NOT_EXIST.getCode(), ResultCode.TRANSACTION_NOT_EXIST_IPS.getCode(), ResultCode.ORDER_NOT_EXIST.getCode());

    public static final Set<String> PAY_RESP_CODE_FAIL_SET = new HashSet<String>(Arrays.asList(ResultCode.PARAMETER_VALIDATION_FAILED.getCode(), ResultCode.SIGNATURE_VERIFICATION_FAILED.getCode(), ResultCode.PARAMETER_NOT_FOUND.getCode(),
            ResultCode.TRANSACTION_NOT_ALLOWED.getCode(), ResultCode.INSTITUTION_NOT_EXIST.getCode(), ResultCode.MERCHANT_NOT_EXIST.getCode(), ResultCode.WECHAT_SUB_MERCHANT_NOT_EXIST.getCode(), ResultCode.ALIPAY_SUB_MERCHANT_NOT_EXIST.getCode(),
            ResultCode.UNIONPAY_QR_CODE_SUB_MERCHANT_NOT_EXIST.getCode(), ResultCode.TERMINAL_NOT_EXIST.getCode(), ResultCode.TERMINAL_CARD_APPLICATION_NOT_EXIST.getCode(), ResultCode.TRANSACTION_PERMISSION_CLOSED.getCode(), 
            ResultCode.TRANSACTION_PERMISSION_NOT_OPENED.getCode(), ResultCode.MERCHANT_DISABLED.getCode(), ResultCode.TERMINAL_DISABLED.getCode(), ResultCode.TRANSACTION_AMOUNT_EXCEEDS_SINGLE_LIMIT.getCode(),
            ResultCode.EXCEEDS_SINGLE_DAY_CUMULATIVE_LIMIT.getCode(), ResultCode.EXCEEDS_SINGLE_MONTH_CUMULATIVE_LIMIT.getCode(), ResultCode.QR_CODE_NOT_EXIST.getCode()));

    public static final Set<String> PAY_IN_PROCESS_SET = new HashSet<String>(Arrays.asList(ResultCode.TRANSACTION_RESULT_UNKNOWN.getCode(), ResultCode.USER_PAYING.getCode(), ResultCode.TRANSACTION_STATUS_PAYING.getCode(),
            ResultCode.ORDER_ID_ALREADY_EXISTS.getCode(), ResultCode.TRANSACTION_RESULT_UNKNOWN_2.getCode()));

    public enum OrderStatus {

        INIT("INIT", "初始化"),
        CREATE("CREATE", "下单成功"),
        SUCCESS("SUCCESS", "交易成功"),
        FAIL("FAIL", "交易失败"),
        DEAL("DEAL", "交易处理中"),
        UNKNOWN("UNKNOWN", "未知状态"),
        CLOSE("CLOSE", "订单关闭"),
        PART_REFUND("PART_REFUND", "部分退款"),
        REFUND("REFUND", "全部退款(或订单被撤销）");

        private final String code;
        private final String description;

        OrderStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static OrderStatus fromCode(String code) {
            for (OrderStatus orderStatus : OrderStatus.values()) {
                if (orderStatus.getCode().equals(code)) {
                    return orderStatus;
                }
            }
            return null;
        }

    }

    public enum AccountType {

        WECHAT("WECHAT", "微信"),
        ALIPAY("ALIPAY", "支付宝"),
        UQRCODEPAY("UQRCODEPAY", "银联"),
        BESTPAY("BESTPAY", "翼支付"),
        SUNING("SUNING", "苏宁易付宝"),
        LKLACC("LKLACC", "拉卡拉支付账户"),
        NUCSPAY("NUCSPAY", "网联小钱包");

        private final String code;
        private final String description;

        AccountType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static AccountType fromCode(String code) {
            for (AccountType accountType : AccountType.values()) {
                if (accountType.getCode().equals(code)) {
                    return accountType;
                }
            }
            return null;
        }

    }

    public enum OrderSource {
        WEIXIN_APP("微信APP扫一扫"),
        JD_FINANCE("京东金融"),
        JD("京东")
        ;

        OrderSource(String value) {
            this.value = value;
        }

        private final String value;

        public String getValue() {
            return value;
        }
    }

    public enum MerchantType {
        SOLE_PROPRIETOR("企业或个体户"),
        INDIVIDUAL("自然人");

        MerchantType(String value) {
            this.value = value;
        }

        private final String value;

        public String getValue() {
            return value;
        }

    }

    public enum TransType {

        NATIVE("41", "ALIPAY，云闪付支持,京东白条"),
        JSAPI("51", "微信公众号支付，支付宝服务窗支付，银联JS支付，翼支付JS支付、拉卡拉钱包支付"),
        MINI("71", "微信小程序支付");

        private final String code;
        private final String description;

        TransType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static TransType fromCode(String code) {
            for (TransType transType : TransType.values()) {
                if (transType.getCode().equals(code)) {
                    return transType;
                }
            }
            return null;
        }

    }

    public enum DeviceType {
        WEB("Web"),
        IOS("iOS"),
        ANDROID("Android");

        DeviceType(String value) {
            this.value = value;
        }

        private final String value;

        public String getValue() {
            return value;
        }
    }

    /**
     * platform
     * 下单平台
     * String(10)
     * 是
     * WEB:PC端浏览器 WAP:手机浏览器APP: 手机app MINI: 小程序 INWALLET: 钱包浏览器
     */
    public enum Platform {
        WEB("WEB"),
        WAP("WAP"),
        APP("APP"),
        MINI("MINI"),
        INWALLET("INWALLET");

        Platform(String value) {
            this.value = value;
        }

        private final String value;

        public String getValue() {
            return value;
        }
    }
}
