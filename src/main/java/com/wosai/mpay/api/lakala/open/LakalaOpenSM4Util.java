package com.wosai.mpay.api.lakala.open;

import com.wosai.mpay.exception.MpayClientError;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Base64;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import java.security.*;

import static org.bouncycastle.util.encoders.Base64.toBase64String;

public class LakalaOpenSM4Util {
    public static final String ALGORITHM_NAME = "SM4";
    public static final String ALGORITHM_NAME_ECB_PADDING = "SM4/ECB/PKCS5Padding";
    public static final int DEFAULT_KEY_SIZE = 128;
    public static final String ENCODING = "UTF-8";

    static {
        Security.addProvider(new BouncyCastleProvider());
    }


    public static String generateKeyToBase64(int keySize) throws MpayClientError {
        return toBase64String(generateKey(keySize));
    }

    public static byte[] generateKey() throws MpayClientError {
        return generateKey(DEFAULT_KEY_SIZE);
    }

    public static byte[] generateKey(int keySize) throws MpayClientError {
        KeyGenerator kg = null;
        try {
            kg = KeyGenerator.getInstance(ALGORITHM_NAME, BouncyCastleProvider.PROVIDER_NAME);
        } catch (Exception e) {
            throw new MpayClientError("SM4Util generateKey error:", e);
        }
        kg.init(keySize, new SecureRandom());
        return kg.generateKey().getEncoded();
    }

    public static byte[] encrypt_ECB_Padding(byte[] key, byte[] data) throws MpayClientError {
        try {
            Cipher cipher = generateECBCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.ENCRYPT_MODE, key);
            return cipher.doFinal(data);
        } catch (Exception e) {
            throw new MpayClientError("SM4Util encrypt_ECB_Padding error:", e);
        }
    }

    public static String encrypt(String key, String data) throws MpayClientError {
        try {
            Cipher cipher = generateECBCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.ENCRYPT_MODE, Base64.decode(key));
            byte[] enData = cipher.doFinal(data.getBytes(ENCODING));
            return toBase64String(enData);
        } catch (Exception e) {
            throw new MpayClientError("SM4Util encrypt_ECB_Padding error:", e);
        }
    }

    public static String decrypt(String key, String cipherText) throws MpayClientError {
        try {
            Cipher cipher = generateECBCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.DECRYPT_MODE, Base64.decode(key));
            byte[] data = cipher.doFinal(Base64.decode(cipherText));
            return new String(data, ENCODING);
        } catch (Exception e) {
            throw new MpayClientError("SM4Util decrypt_ECB_Padding error:", e);
        }
    }

    public static byte[] decrypt_ECB_Padding(byte[] key, byte[] cipherText) throws MpayClientError {
        try {
            Cipher cipher = generateECBCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.DECRYPT_MODE, key);
            return cipher.doFinal(cipherText);
        } catch (Exception e) {
            throw new MpayClientError("SM4Util decrypt_ECB_Padding error:", e);
        }
    }

    private static Cipher generateECBCipher(String algorithmName, int mode, byte[] key)
            throws NoSuchAlgorithmException, NoSuchProviderException, NoSuchPaddingException,
            InvalidKeyException {
        Cipher cipher = Cipher.getInstance(algorithmName, BouncyCastleProvider.PROVIDER_NAME);
        Key sm4Key = new SecretKeySpec(key, ALGORITHM_NAME);
        cipher.init(mode, sm4Key);
        return cipher;
    }

}