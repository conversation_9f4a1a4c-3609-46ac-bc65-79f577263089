package com.wosai.mpay.api.lakala.open;

public class ResponseV3Fields {
    //交易接口v3.0
    public static final String RESP_DATA = "resp_data";
    public static final String MSG ="msg"; //返回响应信息
    public static final String RESP_TIME = "resp_time"; //返回时间
    public static final String PAY_ORDER_NO = "pay_order_no"; //平台订单号
    public static final String COUNTER_URL = "counter_url"; //收银台地址信息
    public static final String ORDER_STATUS = "order_status"; //0:待支付 1:支付中 2:支付成功 3:支付失败 4:已过期 5:已取消 6：部分退款或者全部退款 7:已关单
    public static final String ORDER_INFO = "order_info"; //订单描述
    public static final String TOTAL_AMOUNT = "total_amount"; //订单金额，单位：分
    public static final String SPLIT_MARK = "split_mark"; //合单标识	 “1”为合单，不填默认是为非拆单
    public static final String SPLIT_INFO = "split_info"; //交易拆单信息
    public static final String ORDER_TRADE_INFO_LIST = "order_trade_info_list"; //订单交易信息列表
    public static final String TRADE_NO = "trade_no";//交易流水号
    public static final String LOG_NO = "log_no"; //对账单流水号
    public static final String TRADE_REF_NO = "trade_ref_no"; //交易参考号
    public static final String TRADE_TYPE = "trade_type"; //PAY-消费 REFUND-退款 CANCEL-撤销
    public static final String TRADE_STATUS = "trade_status";//返回状态 S:成功 F:失败 C:被冲正 U:预记状态 X:发送失败 T: 发送超时 P: 处理中
    public static final String TRADE_AMOUNT = "trade_amount";//交易金额，单位：分
    public static final String PAYER_AMOUNT = "payer_amount";//付款人实际支付金额，单位：分
    public static final String BUSI_TYPE = "busi_type"; //UPCARD-银行卡 SCPAY-扫码支付 DCPAY-数币支付 ONLINE-线上支付
    public static final String BUSI_MODE = "busi_mode"; // 业务类型
    public static final String ACC_TRADE_NO = "acc_trade_no"; //付款受理交易流水号 支付宝流水号、微信流水号
    public static final String PAYER_ACCOUNT_NO = "payer_account_no"; //付款人账号
    public static final String PAYER_NAME = "payer_name"; //付款人名称（仅ONLINE交易返回）
    public static final String TRADE_TIME = "trade_time"; //交易完时间格式yyyyMMddHHmmss
    public static final String PAYER_ACCOUNT_BANK = "payer_account_bank"; //付款账号开户行
    public static final String ACC_TYPE = "acc_type"; //账户类型	busi_type为UPCARD时返回：00-借记卡,01-贷记卡 busi_type为SCPAY时返回：02-微信零钱,03-支付宝花呗,04-支付宝钱包,99-未知
    public static final String PAY_MODE = "pay_mode"; //付款方式	busi_type为SCPAY时返回：UQRCODEPAY-银联、WECHAT-微信、ALIPAY-支付宝
    public static final String CLIENT_BATCH_NO = "client_batch_no"; //终端批次号	刷卡交易终端批次号，只有busi_type为UPCARD时返回
    public static final String CLIENT_SEQ_NO = "client_seq_no"; //终端流水号	刷卡交易终端流水号，只有busi_type为UPCARD时返回
    public static final String SETTLE_MERCHANT_NO = "settle_merchant_no"; //结算商户号
    public static final String SETTLE_TERM_NO = "settle_term_no"; //结算终端号
    public static final String ORIGIN_TRADE_NO = "origin_trade_no"; //原交易流水号(扫码交易的退款场景中，对应原交易流水号)
    public static final String AUT_COD = "aut_cod"; //预授权授权号

    public static final String SUB_TRADE_NO = "sub_trade_no"; //子单交易流水号
    public static final String SUB_LOG_NO = "sub_log_no"; //子单对账单流水号
    public static final String OUT_SUB_ORDER_NO = "out_sub_order_no"; //子单对账单流水号
    public static final String CODE = "code";
    public static final String AMOUNT = "amount"; // 交易金额（单位分）
    public static final String REFUND_AMOUNT = "refund_amount"; //退款金额
    public static final String ORIGIN_OUT_TRADE_NO = "origin_out_trade_no"; //原商户请求流水号
    public static final String DCC_FLG = "dcc_flg"; //DCC\EDC标志 
    public static final String ACC_RESP_FIELDS = "acc_resp_fields";//账户端返回信息域
    // 云闪付
    public static final String USER_ID = "user_id";//用户id
    public static final String UP_ISS_ADDN_DATA = "up_iss_addn_data";//银联单品营销 附加数据
    public static final String UP_COUPON_INFO = "up_coupon_info";//银联优惠信息/出资方信息
    public static final String ACC_SETTLE_AMOUNT = "acc_settle_amount";//账户端应结订单金额
    public static final Object CARD_TYPE = "card_type";//银行卡类型
    public static final String COUPON_INFO_SPNSR_ID = "spnsrId";// 出资方id
    public static final String COUPON_INFO_OFFST_AMT = "offstAmt";// 出资方金额
    public static final String COUPON_INFO_ID = "id";//id
    public static final String COUPON_INFO_TYPE = "type";
    public static final String NEED_QUERY = "need_query"; // 0=不需要 1=需要 当返回1时，代表订单处理中，商户需主动发起查询
    public static final String TRADE_STATE = "trade_state";// 交易状态INIT-初始化 CREATE-下单成功 SUCCESS-交易成功 FAIL-交易失败 DEAL-交易处理中 UNKNOWN-未知状态 CLOSE-订单关闭 PART_REFUND-部分退款 REFUND-全部退款(或订单被撤销）
    public static final String PAY_TIME = "pay_time";

    public static final String PAYER_INFO = "payer_info";

    public static final String ACC_NO = "accNo";// 用户登陆信息

    public static final String CARD_ATTR = "cardAttr";// 银行代码


    public static final String NAME = "name"; // 姓名

    public static final String BANK_TYPE = "bank_type";







    public enum TradeTypeEnum {
        TRAN_CONSUME("TRAN_CONSUME","消费"),
        TRAN_PREAUTH("TRAN_PREAUTH","预授权交易"),
        TRAN_COMPLETE("TRAN_COMPLETE","预授权完成/担保完成"),
        TRAN_TRANFER("TRAN_TRANFER","转账"),
        TRAN_COMPLETE_REVOKE("TRAN_COMPLETE_REVOKE","完成撤销"),
        TRAN_REVOKE("TRAN_REVOKE","撤销"),
        TRAN_REFUND("TRAN_REFUND","退货"),
        TRAN_COMBINE_MUSTER("TRAN_COMBINE_MUSTER", "组合支付主订单"),
        TRAN_COMBINE_SUB("TRAN_COMBINE_SUB", "组合支付子订单");
        ;

        TradeTypeEnum(String value, String meaning) {
            this.value = value;
            this.meaning = meaning;
        }

        private String value;
        private String meaning;

        public String getValue() {
            return value;
        }

        public String getMeaning() {
            return meaning;
        }
    }

    public enum AccTypeEnum {
        DEBIT_CARD("D","借记卡"),
        CREDIT_CARD("C","贷记卡"),
        UNKNOWN("U","未知");
        AccTypeEnum(String code, String meaning) {
            this.code = code;
            this.meaning = meaning;
        }

        private String code;
        private String meaning;

        public String getCode() {
            return code;
        }

        public String getMeaning() {
            return meaning;
        }
    }

    public enum TradeStatusEnum {
        SUCCESS("SUCCESS","成功"),
        FAIL("FAILURE","失败"),
        UNKNOWN("UNKNOWN","未知"),
        PROCESSING("PROCESSING","处理中");

        TradeStatusEnum(String value, String meaning) {
            this.value = value;
            this.meaning = meaning;
        }

        private String value;
        private String meaning;

        public String getValue() {
            return value;
        }

        public String getMeaning() {
            return meaning;
        }
    }

    public enum WildCardBusiModeEnum {
        BS1_OUT_CARD("BS1_OUT_CARD","银行卡外卡业务"),
        BS1_ONLINE_WK("BS1_ONLINE_WK","外卡线上业务");

        WildCardBusiModeEnum(String code, String meaning) {
            this.code = code;
            this.meaning = meaning;
        }

        private String code;
        private String meaning;

        public String getCode() {
            return code;
        }

        public String getMeaning() {
            return meaning;
        }
    }

    public enum DccFlgEnum {
        EDC("0","edc"),
        DCC("1","dcc");

        DccFlgEnum(String code, String meaning) {
            this.code = code;
            this.meaning = meaning;
        }

        private String code;
        private String meaning;

        public String getCode() {
            return code;
        }

        public String getMeaning() {
            return meaning;
        }
    }
}
