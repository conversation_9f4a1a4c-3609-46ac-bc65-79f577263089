package com.wosai.mpay.api.lakala.open;

import java.util.HashMap;
import java.util.Map;

public class RequestV3Builder {
    private Map<String, Object> request;
    private Map<String, Object> reqData;

    public RequestV3Builder() {
        request = new HashMap<String, Object>();
        reqData = new HashMap<String, Object>();
    }

    public void reqDataSet(String field, Object value) {
        reqData.put(field, value);
    }
    public void reqSet(String field,Object value){request.put(field,value);}
    public Map<String, Object> build() {
        request.put(BusinessV3Fields.REQ_DATA, reqData);
        return request;
    }

    public Map<String, Object> getReqData() {
        return reqData;
    }
}
