package com.wosai.mpay.api.lakala.open;

import java.util.HashMap;
import java.util.Map;

public class RequestBuilder {
    private Map<String, Object> request;
    private Map<String, Object> reqData;
    private Map<String, Object> termExtInfo;

    public RequestBuilder() {
        request = new HashMap<String, Object>();
        reqData = new HashMap<String, Object>();
        termExtInfo = new HashMap<String, Object>();
    }

    public void reqDataSet(String field, Object value) {
        reqData.put(field, value);
    }

    public void termExtInfoSet(String field, Object value) {
        termExtInfo.put(field, value);
    }

    public Map<String, Object> build() {
        request.put(BusinessFields.REQ_DATA, reqData);
        request.put(BusinessFields.TERM_EXT_INFO, termExtInfo);
        return request;
    }
}
