package com.wosai.mpay.api.lakala.open;

public class ProtocolFields {
    public static final String AUTHORIZATION = "Authorization"; // 认证类型 签名信息 如 Authorization: “LKLAPI-SHA256withRSA appid=”${appid}”,serial_no=”${serialNo}”,timestamp=”${timeStamp}”,nonce_str=”${nonceStr}”,signature=”${signature}”“
    public static final String APP_ID = "appid"; // 机构或渠道接入申请的appid
    public static final String SERIAL_NO = "serial_no"; // 接入方加签用的证书序列号
    public static final String TIMESTAMP = "timestamp"; // 接入方交易请求发起的系统时间（秒）
    public static final String NONCE_STR = "nonce_str"; // 12个字符的随机字符串
    public static final String SIGNATURE = "signature"; // 签名
}
