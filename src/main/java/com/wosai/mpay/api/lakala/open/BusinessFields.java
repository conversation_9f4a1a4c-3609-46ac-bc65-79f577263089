package com.wosai.mpay.api.lakala.open;

public class BusinessFields {

    public static final String REQ_DATA = "reqData"; // 请求参数
    // 终端信息
    public static final String TERM_EXT_INFO = "termExtInfo";// 终端信息
    public static final String TERM_SN = "termSN";//终端设备序列号,商户终端设备的SN号，存在必填，如：QR930000001172
    public static final String TERM_BASESTATION = "termBaseStation";//终端基站信息
    public static final String TERM_LOC = "termLoc";//终端地理位置
    public static final String TERM_IP = "termIp";//终端IP地址
    public static final String TERM_SERIALNO = "termSerialNo";//终端设备串号
    public static final String TERM_TYPE = "termType";//终端设备类型
    public static final String TERM_MODEL = "termModel";//终端型号
    public static final String TERM_MANU = "termManu";//终端厂商
    public static final String APP_CODE = "appCode";//终端app代码
    public static final String APP_VER = "appVer";//终端app版本
    public static final String TERM_FP = "termFP";//终端指纹

    public static final String MERC_ID = "mercId"; // 拉卡拉分配的商户号
    public static final String TERM_NO = "termNo"; // 拉卡拉分配的终端号
    public static final String PAY_MODE = "payMode"; // 支付模式，微信：WECHAT 支付宝：ALIPAY 银联：UQRCODEPAY 数字货币：DCPAY 拉卡拉钱包：LKLACC
    public static final String AMOUNT = "amount"; // 交易金额（单位分）
    public static final String AUTHCODE = "authCode"; // 扫码支付授权码，设备读取用户APP中的条码或者二维码信息，用户付款码条形码规则见说明
    public static final String OPEN_ID = "openId"; // transType=51时（即公众号支付），此参数必传，只对微信支付有效 // 注：建议使用静默授权 微信公众号为用户的 openid 支付宝服务窗为用户的 buyer_id 银联JS支付为用户的 userId，获取用户授权返回00时必传 翼支付不需要上送 拉卡拉钱包为用户的userid
    public static final String SP_BILL_CREATEIP = "spbillCreateIp"; // 用户的客户端IP，格式如***********
    public static final String TRANS_TYPE = "transType"; // 41:NATIVE（微信不支持）51:JSAPI（微信公众号支付，支付宝服务窗支付，银联JS支付，数字货币钱包支付，拉卡拉钱包）61:微信APP支付（暂不支持）71:微信小程序支付 81:支付宝H5支付(暂时只对labs使用) (61 71 81仅labs接口支持)
    public static final String ORDER_ID = "orderId"; // 商户系统唯一，可认为是交易的请求流水号
    public static final String APP_ID = "appId"; // transType=51时（即公众号支付），有此值时，此参数必传，只对微信支付有效
    public static final String EXTEND_PARAMS = "extendParams"; // 支付宝业务扩展参数 JSON格式，见下列说明(目前不支持分期)
    public static final String FRONT_URL = "frontUrl"; // 收款方向银联推送订单时上送的前台通知地址（仅允许为外网地址），用户完成支付点击，“返回”后，银联通过浏览器POST请求到该地址。当transType为JSAPI，payMode为UQRCODEPAY时，可选填此字段
    public static final String FRONT_FAILURL = "frontFailUrl"; // 收款方向银联推送订单时上送的失败交易前台通知地址（仅允许为外网地址），用户支付过程中交易失败，点击“返回”后，银联通过浏览器GET请求到该地址。当transType为JSAPI，payMode为UQRCODEPAY时，可选填此字段，需与frontUrl同时出现，未上送默认为frontUrl的值
    public static final String SUBJECT = "subject"; // 标题，用于简单描述订单或商品
    public static final String SETTLE_TYPE = "settleType"; // 0:正常结算 1:暂缓结算
    public static final String EXTER_ORDER_SOURCE = "exterOrderSource"; // 一般一个机构一个分配好的订单来源，订单来源决定了主扫结果的通知
    public static final String EXTER_MEROR_DERNO = "exterMerOrderNo"; // 同一个订单来源，外部商户订单号不可重复
    public static final String DETAIL = "detail"; // 订单包含的商品列表信息，Json格式。payMode为WECHAT，ALIPAY时，可选填此字段
    public static final String SCANTYPE = "scanType"; // 0或不填：扫码支付 1：刷脸支付

    //extendParams字段说明
    public static final String SYSSERVICEPROVIDERID = "sysServiceProviderId"; // 系统商编号，该参数作为系统商返佣数据提取的依据，请填写系统商签约协议的 PID
    public static final String HBFQNUM = "hbFqNum"; // 支付宝花呗分期必送字段: 花呗分期数 3：3期 6：6期 12：12期
    public static final String HBFQSELLERPERCENT = "hbFqSellerPercent"; // 支付宝花呗分期必送字段: 卖家承担收费比例，商家承担手续费传入100，用户承担手续费传入0，仅支持传入100、0两种，其他比例暂不支持 100：代表商家贴息 0：代表用户承担手续费

    public static final String ORN_MER_ORDER_ID = "ornMerOrderId"; // 同生成动态码中的商户订单号exterMerOrderNo
    public static final String ORN_ORDER_SOURCE = "ornOrderSource"; // 原订单来源-同生成动态码接口中exterOrderSource
    public static final String ORN_ORDER_ID = "ornOrderId"; // 主扫支付原商户订单号-orderId
    public static final String LKL_ORDER_NO = "lklOrderNo"; // 拉卡拉生成的交易流水
    public static final String REFUND_ORDER_ID = "refundOrderId"; // 商户退款单号，若上送需保证商户退款单号唯一，不可重复退款
    public static final String TRADE_CODE = "tradeCode"; // 交易码
    public static final String APP_UP_IDENTIFIER = "appUpIdentifier";//银联支付标识
    public static final String AUTH_CODE = "authCode";

    public static final String M_MERCHANT = "mMerchant";
    public static final String M_CUST_ID = "mCustId";
    public static final String M_OUT_TRADER_NO = "mOutTraderNo";
}

