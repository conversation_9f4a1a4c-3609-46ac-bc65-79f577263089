package com.wosai.mpay.api.lakala.open;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import com.wosai.pantheon.util.MapUtil;


public class LakalaOpenClientTest {
    private static String appid = "800000010358001";
    private static String seriaNo = "13186972655027586573";
    private static String privateKey = "";

    private static String v3_appid = "800000020021006";
    private static String v3_seriaNo = "017d509e7315";
    private static String v3_privateKey = "";

    private static SafeSimpleDateFormat sa = new SafeSimpleDateFormat(com.wosai.mpay.api.lakala.open.LakalaConstants.DATE_TIME_FORMAT);

    public static void main(String[] args) throws MpayApiNetworkError, MpayException {
//        testQuery();
//        testV3Query();
        testPreorder();

    }

    private static void testQuery() throws MpayApiNetworkError, MpayException {
        LakalaOpenClient client = new LakalaOpenClient();
        RequestBuilder request = new RequestBuilder();
        request.reqDataSet(BusinessFields.MERC_ID, "822584058121P7R\"");
        request.reqDataSet(BusinessFields.TERM_NO, "47781282");
        request.reqDataSet(BusinessFields.ORN_ORDER_ID, "7894259213993272");
        request.termExtInfoSet(BusinessFields.TERM_SN, "2101218070002378613");
        request.termExtInfoSet(BusinessFields.TERM_IP, "************");

        Map<String, Object> result = client.call("https://test.wsmsd.cn/sit/labs/txn/labs_order_query", appid, seriaNo, privateKey, request.build());
        System.out.println(result);
    }

    private static void testV3Query() throws MpayApiNetworkError, MpayException {
        LakalaOpenClient client = new LakalaOpenClient();
        RequestV3Builder builder = new RequestV3Builder();
        builder.reqSet(BusinessV3Fields.REQ_TIME, sa.format(new Date()));
        builder.reqSet(BusinessV3Fields.VERSION, LakalaConstants.VERSION_3);
        builder.reqDataSet(BusinessV3Fields.MERCHANT_NO, "822731070110B6S");
        builder.reqDataSet(BusinessV3Fields.OUT_ORDER_NO, "7896259230587900");
//        builder.reqDataSet(BusinessV3Fields.OUT_TRADE_NO, "7896259230581678");
//        builder.reqDataSet(BusinessV3Fields.VPOS_ID, "686902826224467968");
//        builder.reqDataSet(BusinessV3Fields.CHANNEL_ID, "28");
        Map<String, Object> result = client.call("https://s2.lakala.com/api/v3/searcher/base_core/trans_query", v3_appid, v3_seriaNo, v3_privateKey, builder.build());
        System.out.println(result);
    }

    private static void testPreorder() throws MpayException, MpayApiNetworkError {
        LakalaOpenClient client = new LakalaOpenClient();
        RequestV3Builder builder = new RequestV3Builder();
        builder.reqSet(BusinessV3Fields.REQ_TIME, sa.format(new Date()));
        builder.reqSet(BusinessV3Fields.VERSION, LakalaConstants.VERSION_3);
        builder.reqDataSet(BusinessV3Fields.MERCHANT_NO,"822290058120PBH");
        builder.reqDataSet(BusinessV3Fields.TERM_NO,"F0132442");
        builder.reqDataSet(BusinessV3Fields.OUT_TRADE_NO,"****************");
        builder.reqDataSet(BusinessV3Fields.ACCOUNT_TYPE,LakalaOpenV3Constants.AccountType.UQRCODEPAY.getCode());
        builder.reqDataSet(BusinessV3Fields.TRANS_TYPE, LakalaOpenV3Constants.TransType.NATIVE.getCode());
        builder.reqDataSet(BusinessV3Fields.TOTAL_AMOUNT,"1");
        builder.reqDataSet(BusinessV3Fields.LOCATION_INFO, JsonUtil.jsonStrToObject("{\"location\":\"+31.230525,+121.473667\",\"request_ip\":\"127.0.0.1\"}",Map.class));
        builder.reqDataSet(BusinessV3Fields.SUBJECT,"白条支付预下单");
        Map accBusiFields = new HashMap();
        accBusiFields.put("timeout_express","15");
        accBusiFields.put("instal_will",0);
        accBusiFields.put("acq_addn_data_order_info", MapUtil.hashMap(
                "preproduct","at",
//                "lockplan",3,
                "risk_info",MapUtil.hashMap(
                        "item_no","*********",
                        "order_source","微信APP扫一扫",
                        "pay_user_id","oGFfks5G51ueavlh_ZdWw8D83Lto",
                        "pay_code_id","*********5269"
                )
        ));
        builder.reqDataSet(BusinessV3Fields.ACC_BUSI_FIELDS, accBusiFields);
        Map<String, Object> result = client.call("https://s2.lakala.com/api/v3/labs/trans/preorder", "", "", "", builder.build());
        System.out.println(result);
    }

}
