package com.wosai.mpay.api.lakala;

import com.wosai.mpay.api.HttpResourceCallback;
import com.wosai.mpay.api.alipay.FileItem;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by w<PERSON><PERSON><PERSON><PERSON> on 2019/12/16.
 * 拉卡拉异步客户端
 */
public class AsyncLakalaClient {
    public static final Logger logger = LoggerFactory.getLogger(AsyncLakalaClient.class);
    protected int connectTimeout = 2000;
    protected int readTimeout = 5000;

    private RequestConfig requestConfig;
    private CloseableHttpAsyncClient client;

    public AsyncLakalaClient() {
        initClient();
    }

    public AsyncLakalaClient(int readTimeout, int connectTimeout){
        this.readTimeout = readTimeout;
        this.connectTimeout = connectTimeout;
        initClient();
    }

    public void initClient(){
        requestConfig = RequestConfig.custom().setConnectionRequestTimeout(connectTimeout).setConnectTimeout(connectTimeout).setSocketTimeout(readTimeout).build();
        client = AsyncClientUtil.getCloseableHttpAsyncClient(null, null);
    }

    public void call(String serviceUrl, String signKey, int payMethod, Map<String, Object> request, HttpResourceCallback<Map<String, Object>> callback){
        try{
            if(payMethod == LakalaConstants.METHOD_PROFIT_SHARING_FILE_UPLOAD){
                doCallWithFile(serviceUrl, signKey, payMethod, request, callback);
            }else{
                doCall(serviceUrl, signKey, payMethod, request, callback);
            }
        }catch (Throwable t){
            callback.onError(t);
        }
    }

    private void doCall(String serviceUrl, String signKey, int payMethod, Map<String, Object> request, HttpResourceCallback<Map<String, Object>> callback) throws Exception {
        preProcess(signKey, payMethod, request);
        AsyncClientUtil.logRequest(logger, JsonUtil.objectMapper.writeValueAsString(request));
        HttpPost httpPost = new HttpPost(serviceUrl);
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setEntity(new ByteArrayEntity(JsonUtil.objectMapper.writeValueAsBytes(request)));
        httpPost.setConfig(requestConfig);
        long start = System.currentTimeMillis();
        client.execute(httpPost, AsyncClientUtil.getFutureCallback(logger, LakalaConstants.CHARSET_UTF8, AsyncClientUtil.ResponseType.JSON, HttpResourceCallback.<Map<String,Object>>create(
                TracingUtil.getTraceCarrierItem(),
                (response, t) -> {
                    String responseStr = null;
                    try {
                         responseStr = JsonUtil.objectToJsonString(response);
                    } catch (MpayException e) {
                        responseStr = response.toString();
                    }

                    AsyncClientUtil.logResponse(logger, serviceUrl, System.currentTimeMillis() - start, response != null ? responseStr : null, t);
                    if(t != null){
                        callback.onError(t);
                        return;
                    }else{
                        callback.onComplete(response);
                    }
                }
        )));
    }

    private void doCallWithFile(String serviceUrl, String signKey, int payMethod, Map<String, Object> request, HttpResourceCallback<Map<String, Object>> callback) throws Exception {
        preProcess(signKey, payMethod, request);
        Map<String, String>  params = new HashMap<>();
        Map<String, FileItem> fileParams = new HashMap<>();
        for(String key: request.keySet()){
            Object value = request.get(key);
            if(value == null){
                continue;
            }
            if(value instanceof File){
                fileParams.put(key, new FileItem((File) value));
            }else{
                params.put(key, value.toString());
            }
        }
        long start = System.currentTimeMillis();
        AsyncClientUtil.logRequest(logger, JsonUtil.objectMapper.writeValueAsString(request));
        String responseStr = WebUtils.doPost(null, null, serviceUrl, params, fileParams, connectTimeout, readTimeout);
        AsyncClientUtil.logResponse(logger, serviceUrl, System.currentTimeMillis() - start, responseStr, null);
        callback.onComplete(JsonUtil.jsonStringToObject(responseStr, Map.class));
    }

    public static void preProcess(String signKey, int payMethod, Map<String, Object> request) throws MpayException {
        for (Object mapKey : request.keySet().toArray()) {
            if (request.get(mapKey) == null) {
                request.remove(mapKey);
            }
        }
        if(payMethod == LakalaConstants.METHOD_PROFIT_SHARING_FILE_UPLOAD){
            //上传文件不做签名
            return;
        }
        if(LakalaClient.SHAIRNG_SIGN_WITH_MD5_PAY_METHODS.contains(payMethod)){
            request.put(BusinessFields.SIGN, LakalaSignature.getMd5Sign(request, signKey, LakalaConstants.CHARSET_UTF8).toUpperCase());
        }else{
            request.put(BusinessFields.MAC, LakalaSignature.getSign(request, signKey, LakalaClient.getSignatureColumns(payMethod), LakalaConstants.CHARSET_UTF8));
        }
    }

}
