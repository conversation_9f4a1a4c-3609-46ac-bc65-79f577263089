package com.wosai.mpay.api.lakala.open;

public class BusinessV3Fields {

    public static final String REQ_TIME = "req_time";
    public static final String REQ_DATA = "req_data"; // 请求参数
    public static final String VERSION = "version";
    public static final String OUT_ORG_CODE = "out_org_code";

    public static final String AMOUNT = "amount"; // 交易金额（单位分）
    //lakala开发平台交易接口v3.0
    public static final String SERIA_NO = "seria_no";
    public static final String OUT_ORDER_NO = "out_order_no"; // 商户订单号
    public static final String MERCHANT_NO = "merchant_no"; // 商户号
    public static final String VPOS_ID = "vpos_id"; // 交易设备标识（非合单场景必输该字段），进件返回接口中的termId字段
    public static final String CHANNEL_ID = "channel_id"; // 渠道号 用于接收订单通知报文，拉卡拉开通
    public static final String BUSI_MODE = "busi_mode"; //业务模式： ACQ-收单 PAY-付款 不填，默认为“ACQ-收单”
    public static final String TOTAL_AMOUNT = "total_amount"; //订单金额 单位：分
    public static final String ORDER_EFFICIENT_TIME = "order_efficient_time"; //订单有效期 格式yyyyMMddHHmmss,最大支持下单时间+2天
    public static final String NOTIFY_URL = "notify_url"; //订单支付成功后商户接收订单通知的地址 http://xxx.xxx.com
    public static final String SUPPORT_CANCEL = "support_cancel"; // 是否支持撤销 默认 0 不支持 busi_mode为“PAY-付款”不支持 撤销
    public static final String SUPPORT_REFUND = "support_refund"; // 是否支持退款 默认0 不支持
    public static final String SUPPORT_REPEAT_PAY = "support_repeat_pay"; // 是否支持“多次发起支付” 默认0 不支持
    public static final String SUPPORT_PAY_MODE = "support_pay_mode"; // 支持的支付方式，JSON字符串，如[{“busi_type”:”UPCARD”,”pay_mode”:”CREDIT|DEBIT”},{“busi_type”:”SCPAY”,”pay_mode”:”WETCHAT|ALIPAY”}]
    public static final String OUT_USER_ID = "out_user_id"; //发起订单方的userId，归属于channelId下的userId
    public static final String CALLBACK_URL = "callback_url"; // 客户端下单完成支付后返回的商户网页跳转地址。
    public static final String ORDER_INFO = "order_info"; // 订单标题，在使用收银台扫码支付时必输入，交易时送往账户端
    public static final String TERM_NO = "term_no"; // 结算终端号,合单场景必输该字段
    public static final String SPLIT_MARK = "split_mark"; // 合单标识，“1”为合单，不填默认是为非合单
    public static final String OUT_SPLIT_INFO = "out_split_info"; //拆单信息
    public static final String PAGE_STYLE = "page_style"; // 收银台样式（STD-标准收银台、FSWY - 福斯网银、FSZZ - 福斯转账、B2B-B2B收银台
    public static final String SETTLE_TYPE = "settle_type"; //结算类型（非合单） （“0”或者空，常规结算方式） 注意：该字段会影响结算方式，慎用。
    public static final String OUT_SUB_ORDER_NO = "out_sub_order_no"; // 拆单信息中 商户子订单号
    public static final String REFUND_AMOUNT = "refund_amount"; //退款金额
    public static final String ORIGIN_BIZ_TYPE = "origin_biz_type"; //原交易类型:1 银行卡，2 外卡，3 扫码，4 线上
    public static final String ORIGIN_TRADE_DATE = "origin_trade_date"; //原交易日期：yyyyMMdd
    public static final String ORIGIN_LOG_NO = "origin_log_no"; //交易返回的拉卡拉统一交易单号，扫码交易为66开头，POSP交易为年份后两位开头
    public static final String ORIGIN_TRADE_NO = "origin_trade_no"; //下单成功时，返回的扫码系统生成的送往账户方的交易流水号，在微信或支付宝交易信息界面显示为商户订单号
    public static final String ORIGIN_CARD_NO = "origin_card_no"; //原交易银行卡号，银行卡退款必填，如无完整卡号可送交易通知时的脱敏卡号如6226****8223
    public static final String REFUND_TYPE = "refund_type"; //当商户进件时退货模式配置的为 指定模式退货时，该字段有效。00：退货帐户,05：商户余额退货,06：终端余额退货
    public static final String LOCATION_INFO = "location_info"; //地址位置信息
    public static final String LOCATION_INFO_REQUEST_IP = "request_ip";

    public static final String ORIGIN_OUT_TRADE_NO = "origin_out_trade_no"; //退款请求流水号	原退款交易商户请求流水号
    public static final String OUT_TRADE_NO = "out_trade_no";//退款请求流水号
    public static final String TRADE_NO = "trade_no";//拉卡拉交易流水号

    public static final String ORIGIN_BIZ_TYPE_BANKCARD = "1";
    public static final String ORIGIN_BIZ_TYPE_WILD_CARD = "2";
    public static final String ORIGIN_BIZ_TYPE_QRCODE = "3";
    public static final String ORIGIN_BIZ_TYPE_ONLINE = "4";

    public static final String SUPPORT = "1";
    public static final String NOT_SUPPORT = "0";

    public static final String AUTH_CODE = "auth_code";//扫码支付授权码，设备读取用户APP中的条码或者二维码信息，用户付款码条形码规则见说明
    public static final String SUBJECT = "subject";//标题，用于简单描述订单或商品（账户端控制，实际最多42个字符），微信支付必送。
    public static final String ACC_BUSI_FIELDS = "acc_busi_fields";//账户端业务信息域
    public static final String ACQ_ADDN_DATA_GOODS_INFO = "acq_addn_data_goods_info";//银联商品信息
    public static final String REQUEST_IP = "request_ip";//请求方的IP地址，存在必填，格式如***********
    public static final String BASE_STATION = "base_station";//客户端设备的基站信息（主扫时基站信息使用该字段）
    public static final String LOCATION = "location";//商户终端的地理位置，银联二维码交易必填
    public static final String ACCOUNT_TYPE = "account_type"; // 微信：WECHAT 支付宝：ALIPAY 银联：UQRCODEPAY 翼支付: BESTPAY 苏宁易付宝: SUNING 拉卡拉支付账户：LKLACC 网联小钱包：NUCSPAY
    public static final String TRANS_TYPE = "trans_type"; // 接入方式
    public static final String TIMEOUT_EXPRESS = "timeout_express";//预下单的订单的有效时间，以分钟为单位
    public static final String USER_ID = "user_id";//用户id
    public static final String UN_QRCODE = "un_qrcode";//银联二维码qrcode, 标识交易信息
    public static final String USER_AUTH_CODE = "user_auth_code";//用户授权码
    public static final String FRONT_URL = "front_url"; // 银联前台通知地址
    public static final String FRONT_FAIL_URL = "front_fail_url"; // 银联失败交易前台通知地址
    public static final String REDIRECT_URL = "redirect_url";
    public static final String APP_UP_IDENTIFIER="app_up_identifier"; //银联支付标识
    public static final String PAY_CHANNEL="pay_channel"; //支付渠道：云闪付微信渠道交易出现01:云闪付微信小程序02:云闪付微信公众号
    public static final String THIRD_PART_APP_ID="third_part_app_id"; //第三方appid
    public static final String THIRD_PART_OPEN_ID="third_part_open_id"; //第三方openid
    public static final String THIRD_APP_ID="thirdAppId"; //第三方appid
    public static final String THIRD_OPEN_ID="thirdOpenId"; //第三方openid



    public static final String INSTAL_WILL ="instal_will"; //分期意愿
    public static final String ACQ_ADDN_DATA_ORDER_INFO ="acq_addn_data_order_info"; //银联订单信息
    public static final String PRE_PRODUCT = "preproduct";//白条前置标识
    public static final String LOCK_PLAN = "lockplan";//锁定分期期数
    public static final String RICK_INFO = "riskInfo";//采集信息

    public static final String ITEM_NO = "itemNo"; //由白条侧前置预先分配的项目编号
    public static final String ORDER_SOURCE = "orderSource";//消费者下单来源，传中文，枚举如下：微信APP扫一扫、京东金融、京东（目前仅支持微信APP扫一扫）
    public static final String PAY_USER_Id ="payUserId";//微信侧用户openid（当order_source为微信app扫一扫时上送openid
    public static final String PAY_CODE_ID = "payCodeId";//商户收款码ID，可以是商家展业所应用的静态码/动态码所发生的终端设备唯一识别序列号
    public static final String MERCHANT_TYPE = "merchantType";//商户类型，传中文，自然人必传枚举如下：企业或个体户、自然人
    public static final String ADDN_INFO = "addnInfo";//京东白条分期必传

    public static final String REMARK = "remark";
    public static final String DEVICE_TYPE = "device_type";

    public static final String CARD_INFO = "card_info";
    public static final String CARD_INFO_CARD_NUMBER = "card_number";
    public static final String CARD_INFO_EXPIRE_DATE = "expiry_date";
    public static final String CARD_INFO_CVC = "cvc";
    public static final String CARD_INFO_HOLDER_NAME = "holder_name";

    public static final String PLATFORM = "platform";
    public static final String RETURN_URL = "return_url";
    public static final String ORIGIN_URL = "origin_url";

    // user_info 字段
    public static final String USER_INFO = "user_info";
    public static final String USER_INFO_USER_AGENT = "user_agent";
    public static final String USER_INFO_USER_EMAIL = "user_email";

    //goods_detail
    public static final String GOODS_DETAIL = "goods_detail";

    public static final String AUTH_RESULT = "auth_result";//3ds认证结果

    // Apple Pay payment_token 相关字段
    public static final String PAYMENT_TOKEN = "payment_token";

    public static final String APPLICATION_PRIMARY_ACCOUNT_NUMBER = "application_primary_account_number";
    public static final String APPLICATION_EXPIRATION_DATE = "application_expiration_date"; // YYMMDD
    public static final String CARDHOLDER_NAME = "cardholder_name";
    public static final String PAYMENT_DATA_TYPE = "payment_data_type";
    public static final String PAYMENT_DATA = "payment_data";
    public static final String PAYMENT_METHOD = "payment_method";

    public static final String DISPLAY_NAME = "display_name";
    public static final String NETWORK = "network";
    public static final String TYPE = "type";

    public static final String DEVICE_MANUFACTURER_IDENTIFIER = "device_manufacturer_identifier";
    public static final String CURRENCY_CODE = "currency_code";
    public static final String TRANSACTION_AMOUNT = "transaction_amount";
    public static final String AUTHENTICATION_RESPONSES = "authentication_responses";
    public static final String MERCHANT_TOKEN_IDENTIFIER = "merchant_token_identifier";
    public static final String MERCHANT_TOKEN_METADATA = "merchant_token_metadata";
    public static final String ONLINE_PAYMENT_CRYPTOGRAM = "online_payment_cryptogram";

    /**
     * 云闪付服务商pid返佣参数
     */
    public static final String PID_INFO = "pid_info";


}

