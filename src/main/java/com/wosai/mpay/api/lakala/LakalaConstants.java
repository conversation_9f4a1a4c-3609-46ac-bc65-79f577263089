package com.wosai.mpay.api.lakala;

import java.util.Arrays;
import java.util.List;

/**
 * Created by maoyu
 */
public class LakalaConstants {

    public static final String FUNCOD_BSC = "8001";//B扫C对应的功能码,固定为 8001
    public static final String FUNCOD_BSC_CANCEL = "8002";//B扫C,撤单
    public static final String FUNCOD_CSB = "8011";//C扫B,或者门店码
    public static final String FUNCOD_CSB_CANCEL = "8012";//C扫B撤单
    public static final String FUNCOD_QUERY = "8021";//订单查询接口
    public static final String FUNCOD_QUERY_POSITIVE = "8022";//B扫C、C扫B的查询场景。已在用户端生成成功电子凭证，根据原订单号进行查询。
    public static final String FUNCOD_REFUND = "8031";//退款申请
    public static final String FUNCOD_REFUND_QUERY = "8032";//退款查询
    public static final String FUNCOD_PROFIT_SHARING = "9301";//分账
    public static final String FUNCOD_PROFIT_SHARING_QUERY = "9302";//分账查询

    public static final String CHARSET_GBK = "GBK";
    public static final String CHARSET_UTF8 = "utf8";
    public static String DATE_TIME_FORMAT = "yyyyMMddHHmmss";
    /**
     * Date默认时区
     **/
    public static final String DATE_TIMEZONE = "GMT+8";
    /**
     * sign type
     **/
    public static String SIGN_TYPE_MD5 = "MD5";
    /**
     * 交易类型
     **/
    public static final String TRADE_TYPE_JSAPI = "JSAPI";//公众号支付
    public static final String TRADE_TYPE_NATIVE = "NATIVE";//原生扫码支付
    public static final String TRADE_TYPE_APP = "APP";//app支付

    public static final String TRADE_STATUS_SUCC = "000000";//本次操作成功
    public static final String TRADE_STATE_USERPAYING = "0100C0";//支付进行中
    public static final String UNIONPAY_REJEC = "010038";//银联风险解决
    public static final String CARD_INVALID = "010061";//卡号无效
    public static final String REQUEST_LOG_NO_DUPLICATE = "100015";//请求流水号重复
    public static final String TRADE_NOT_EXISTS = "100016";//流水不存在
    public static final String AMOUNT_LIMIT = "010033";//交易金额超限
    public static final String TRADE_FAIL = "010097";//交易返回失败
    public static final String TRADE_CLOSE = "010005";//不承兑，拉卡拉反馈改笔交易已支付失败
    
    public static final String AUTH_CODE_EXPRIE = "010020";//无此二维码
    public static final String AUTH_CODE_INVALID = "010023";//二维码已失效
    public static final String CARD_STATUS_ERROR = "010063"; //  卡状态不正确
    public static final String BALANCE_NOT_ENOUGH = "010064"; //  余额不足
    public static final String CARD_NO_PAYMENT_INSTRUMENT = "010077"; // 银行卡未开通认证支付
    public static final String INVALID_PARAMS = "010010"; // 报文格式错误
    public static final String CARD_REJEC = "010068"; // 您的银行卡暂不支持该业务，请向您的银行或95516咨询
    public static final String USER_INFO_ERROR_1 = "010065"; // 输入的密码、有效期或CVN2有误，交易失败
    public static final String USER_INFO_ERROR_2 = "010066"; // 持卡人身份信息、手机号或CVN2输入不正确，验证失败
    public static final String INPUT_PASSWORD_LIMIT = "010067"; // 密码输入次数超限
    public static final String CARD_EXPIRE = "010073"; // 支付卡已超过有效期
    public static final String TRAD_LIMIT = "010032"; // 无此交易权限
    
    /**
     * 支付渠道类型
     **/
    public static final String PAY_WAY_WEIXIN = "WECHAT";
    public static final String PAY_WAY_ALIPAY = "ALIPAY";
    public static final String PAY_WAY_BAIDU = "BAIDUPAY";
    public static final String PAY_WAY_LKLPAY = "LKLPAY";//拉卡拉钱包
    public static final String PAY_WAY_UNIONPAY = "UNIONPAY";//银联钱包
    public static final String PAY_WAY_JDPAY = "JDPAY";//京东钱包
    public static final String PAY_WAY_CCBLZF = "CCBLZF";//建行龙支付钱包
    public static final String PAY_WAY_UQRCODEPAY = "UQRCODEPAY";//银联二维码支付


    public static final String CANCEL_REQ_TYPE_USER_CANCEL = "01";//用户主动撤销
    public static final String CANCEL_REQ_TYPE_POS_TIMEOUT = "02";//POS轮询超时



    /**
     * 分账类型
     */
    public static final String LEDGER_TYPE_SHARING_PAY = "01";
    public static final String LEDGER_TYPE_SHARING_RESTITUTE = "02";

    /**
     * WOSAI_0000：受理成功
     * WOSAI_0001：必传值为空
     * WOSAI_0002：受理失败
     * WOSAI_0003：喔噻分账处理中
     * WOSAI_1002：查询成功
     * WOSAI_1003：查询失败，交易不存在
     */
    public static final String SHARING_RESPONSE_CODE_SUCC = "000000";
    public static final String SHARING_RESPONSE_CODE_ACCEPT_SUCC = "WOSAI_0000";
    public static final String SHARING_RESPONSE_CODE_INVALID_PARAMS = "WOSAI_0001";
    public static final String SHARING_RESPONSE_CODE_FAIL = "WOSAI_0002";
    public static final String SHARING_RESPONSE_CODE_SHARING_INPROG = "WOSAI_0003";
    public static final String SHARING_RESPONSE_CODE_QUERY_SUCC = "WOSAI_1002";
    public static final String SHARING_RESPONSE_CODE_QUERY_FAIL = "WOSAI_1003";



    public static final String SHARING_OPEN_RESPONSE_CODE_SUCC = "000000";
    public static final String SHARING_OPEN_RESPONSE_CODE_SYSTEM_ERROR = "100001"; //系统错误
    public static final String SHARING_OPEN_RESPONSE_CODE_APPLY_EXISTS = "100298"; //商户分账已经申请
    public static final String SHARING_OPEN_RESPONSE_CODE_INVALID_SIGN = "100304"; //验签失败
    public static final String SHARING_OPEN_RESPONSE_CODE_MERCHANT_KEY_NOT_MATCH = "100299"; //认开通商户号和秘钥不匹配
    public static final String SHARING_OPEN_RESPONSE_CODE_FILE_SIZE_LIMIT = "100306"; //上传文件超过了5M
    public static final String SHARING_OPEN_RESPONSE_CODE_FILE_EMPTY = "100308"; //上传文件内容为空
    public static final String SHARING_OPEN_RESPONSE_CODE_SEND_MERCHANT_NOT_EXISTS = "100300"; //分账发送方商户号不存在
    public static final String SHARING_OPEN_RESPONSE_CODE_SEND_MERCHANT_KEY_NOT_MATCH = "100302"; //分账发送方商户号对应的秘钥和上送秘钥不匹配
    public static final String SHARING_OPEN_RESPONSE_CODE_REVC_MERCHANT_NOT_EXISTS = "100301"; //分账接收方商户号不存在
    public static final String SHARING_OPEN_RESPONSE_CODE_IS_BACK_ERROR = "100303"; //是否可以回退的参数错误

    /**
     * 00：初始化
     * 01：分账失败
     * 02：分账成功
     * 03：分账未知
     */
    public static final String SHARING_TXN_STATUS_INIT = "00";
    public static final String SHARING_TXN_STATUS_SHARING_FAIL = "01";
    public static final String SHARING_TXN_STATUS_SHARING_SUCC = "02";
    public static final String SHARING_TXN_STATUS_SHARING_UNKNOWN = "03";

    /**
     * 分账关系是否启用
     */
    public static final String SHARING_RELATION_STAT_DISABLE = "00";
    public static final String SHARING_RELATION_STAT_ENABLE = "01";

    /**
     * 分账关系同步是否允许回退
     */
    public static final String SHARING_RELATION_IS_BACK_YES = "00";
    public static final String SHARING_RELATION_IS_BACK_NO = "01";

    /**
     * 各种接口的简易编号
     */
    public static final int METHOD_BSC_PAY = 1;
    public static final int METHOD_BSC_CANCEL = 2;
    public static final int METHOD_CSB_PAY = 3;
    public static final int METHOD_CSB_CANCEL = 4;
    public static final int METHOD_QUERY = 5;
    public static final int METHOD_POSITIVE_QUERY = 6;
    public static final int METHOD_REFUND = 7;
    public static final int METHOD_REFUND_QUERY = 8;
    public static final int METHOD_PROFIT_SHARING_PAY = 9;
    public static final int METHOD_PROFIT_SHARING_RESTITUTE = 10;
    public static final int METHOD_PROFIT_SHARING_QUERY = 11;
    public static final int METHOD_PROFIT_SHARING_RELATION_SYNC = 12;
    public static final int METHOD_PROFIT_SHARING_OPEN_SUBMIT = 13;
    public static final int METHOD_PROFIT_SHARING_OPEN_CONFIRM = 14;
    public static final int METHOD_PROFIT_SHARING_FILE_UPLOAD = 15;
    public static final int METHOD_PROFIT_SHARING_RELATION_APPLAY = 16;


    public static final List<String> PAY_FAIL_ERR_CODE_LISTS = Arrays.asList(
            BALANCE_NOT_ENOUGH, AUTH_CODE_EXPRIE, AMOUNT_LIMIT, UNIONPAY_REJEC,
            CARD_NO_PAYMENT_INSTRUMENT, INVALID_PARAMS, AUTH_CODE_INVALID, CARD_REJEC,
            CARD_INVALID, USER_INFO_ERROR_1, USER_INFO_ERROR_2, TRAD_LIMIT,
            INPUT_PASSWORD_LIMIT, CARD_EXPIRE, CARD_STATUS_ERROR
            
    );
}
