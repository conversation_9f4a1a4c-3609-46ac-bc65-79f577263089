package com.wosai.mpay.api.lakala;

import java.util.Arrays;
import java.util.List;

/**
 * Created by maoyu
 */
public class BusinessFields {

    public static final String FUNCOD = "FunCod";// 功能码
    public static final String COMPORGCODE = "compOrgCode";//拉卡拉提供的结构代码
    public static final String REQLOGNO = "reqLogNo";//请求流水号
    public static final String REQUEST_ID = "requestId";//分账方请求流水号
    public static final String REQTM = "reqTm";//请求时间,yyyyMMddHHmmss
    public static final String PAYCHLTYP = "payChlTyp";//支付渠道类型:微信二维码消费：WECHAT; 支付宝二维码消费: ALIPAY;  百度二维码消费: BAIDUPAY; 注：全大写。
    public static final String MERCID = "mercId";//商户号
    public static final String TERMID = "termId";//终端号
    public static final String TXNAMT = "txnAmt";//交易金额,以分为单位
    public static final String AUTHCODE = "authCode";//授权码
    public static final String GOODSTAG = "goodsTag";//营销活动信息
    public static final String ORDERINFO = "orderInfo";//订单描述
    public static final String EXTDATA = "extData";//扩展信息
    public static final String REQTYPE = "reqType";//发起撤销类型: 01:用户主动撤销;02POS轮询超时
    public static final String TRADETYPE = "tradeType";//交易接口类型: JSAPI,NATIVE,APP
    public static final String SUBAPPID = "sub_appid";//子商户微信appid
    public static final String OPENID = "openId";//用户标识
    public static final String PRODUCTID = "productId";//NATIVE时必输，此ID为二维码中包含的商品ID
    public static final String ORNREQLOGNO = "ornReqLogNo";//原请求流水号
    public static final String MERORDERNO = "merOrderNo";//拉卡拉支付平台产生的订单号
    public static final String MAC = "MAC";//MD5计算值
    public static final String SIGNATURE = "signature";//
    public static final String INST_ID = "instId";//清结算机构号

    public static final String TRANDATE = "tranDate";//交易日期
    public static final String TRANTIME = "tranTime";//交易时间
    public static final String LOGNO = "logNo";//拉卡拉订单流水号
    public static final String LOGDAT = "logdat";//订单日期
    public static final String QUERY_ID = "queryId";//查询流水号
    public static final String LEDGER_TYPE = "ledgerType";//分账类型 01：分账，02：分账回退
    public static final String LEDGERTRANS_ID = "ledgerTranSid";//分账请求流水id
    public static final String OLEDGERTRANS_ID = "oledgerTranSid";//原分账请求流水id
    public static final String SEND_MERID = "sendMerId";//分账发起商户号
    public static final String SEND_TERMID = "sendTermId";//分账发起终端号
    public static final String REVC_MERID = "revcMerId";//分账接收商户号
    public static final String REVC_TERMID = "revcTermId";//分账接收终端号
    public static final String LEDGER_PERCENT = "ledgerPercent";//分账百分比例
    public static final String REVC_DATA = "revcData";//
    public static final String STAT = "stat";//分账关系状态 00未启用 01启用
    public static final String IS_BACK = "isBack";// 是否可回退 00：可回退，01：不可回退
    public static final String NOTIFY_URL = "notifyUrl";//通知用url
    public static final String VERIFY_CODE = "verifycode";//验证码


    public static final String SIGN = "sign"; //签名
    public static final String MER_ID = "merId"; //商户号
    public static final String MER_NAME = "merName"; //商户名称
    public static final String INST_NAME = "instName"; //机构名称
    public static final String LINK_MOBILE = "linkMobile"; //联系人手机号
    public static final String SECRET_KEY = "secretKey"; //商户密钥
    public static final String MER_KEY = "merKey"; //
    public static final String FILE = "file"; //文件
    public static final String ATT_TYPE = "attType"; //附件类型 AGREE_MENT("AGREE_MENT","合作协议"), RULE_TYPE("RULE_TYPE","清分规则"), SHARE_RATIO("SHARE_RATIO","分账比例"), ACCOUNTING_RULE("ACCOUNTING_RULE","分账规则"),
    public static final String RELATION = "relation"; //文件
    public static final String ATTACH_LIST = "attachList"; //附件列表
    public static final String ATTACH_TYPE = "attachType"; //附件类型
    public static final String ATTACH_PATH = "attachPath"; //附件路径


    /**
     * 各个接口需要参与签名的字段
     **/

    public static final List<String> BSC_PAY_SIGNATURE_PARAMS = Arrays.asList(COMPORGCODE, REQLOGNO, REQTM, PAYCHLTYP, MERCID, TERMID, TXNAMT, AUTHCODE);
    public static final List<String> BSC_CANCEL_SIGNATURE_PARAMS = Arrays.asList(COMPORGCODE, REQLOGNO, ORNREQLOGNO, REQTM, PAYCHLTYP, MERCID, TERMID);
    public static final List<String> CSB_PAY_SIGNATURE_PARAMS = Arrays.asList(COMPORGCODE, REQLOGNO, REQTM, PAYCHLTYP, MERCID, TERMID, TXNAMT);
    public static final List<String> CSB_CANCEL_SIGNATURE_PARAMS = Arrays.asList(COMPORGCODE, REQLOGNO, ORNREQLOGNO, REQTM, PAYCHLTYP, MERCID, TERMID);
    public static final List<String> QUERY_SIGNATURE_PARAMS = Arrays.asList(COMPORGCODE, REQLOGNO, ORNREQLOGNO, REQTM, PAYCHLTYP, MERCID, TERMID);
    public static final List<String> POSITIVE_QUERY_SIGNATURE_PARAMS = Arrays.asList(COMPORGCODE, REQLOGNO, REQTM, PAYCHLTYP, MERCID, TERMID, MERORDERNO);
    public static final List<String> REFUND_SIGNATURE_PARAMS = Arrays.asList(COMPORGCODE, REQLOGNO, ORNREQLOGNO, REQTM, PAYCHLTYP, MERCID, TERMID, TXNAMT);
    public static final List<String> REFUND_QUERY_SIGNATURE_PARAMS = Arrays.asList(COMPORGCODE, REQLOGNO, ORNREQLOGNO, REQTM, PAYCHLTYP, MERCID, TERMID);
    public static final List<String> PROFIT_SHARING_SIGNATURE_PARAMS = Arrays.asList(LEDGER_TYPE, COMPORGCODE, INST_ID, LEDGERTRANS_ID, TRANDATE, TRANTIME);
    public static final List<String> PROFIT_SHARING_QUERY_SIGNATURE_PARAMS = Arrays.asList(COMPORGCODE, QUERY_ID, LEDGERTRANS_ID);
    public static final List<String> PROFIT_SHARING_RELATION_SYNC_SIGNATURE_PARAMS = Arrays.asList(COMPORGCODE, REQUEST_ID, SEND_MERID, REVC_MERID, STAT, LEDGER_PERCENT);

}

