package com.wosai.mpay.api.lakala.open;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class LakalaConstants {
    // 拉卡拉签名方式
    public static final String SIGN_LAKLA_RSA2 = "LKLAPI-SHA256withRSA";
    public static final String CHARSET_UTF_8 = "UTF-8";
    public static String DATE_TIME_FORMAT = "yyyyMMddHHmmss";
    public static String DATE_TIME_FORMAT_V3 = "yyyy-MM-dd HH:mm:ss";

    public static String RET_CODE_BPS_SUCCESS = "000000";
    public static String RET_CODE_BPS_ORDER_NOT_EXISTS = "BPS10032";
    public static String RET_CODE_BPS_IN_PROG = "BPS10029";
    public static String RET_CODE_BPS_PAY_FAIL = "BPS10030";
    public static String RET_CODE_BPS_PRECREATE_FAIL = "BPS10028";

    
    public static String RET_CODE_BBS_SUCCESS = "BBS00000";
    
    public static String RET_CODE_LABS_SUCCESS = "LABS00000";
    public static String RET_CODE_LABS_PAY_IN_PROG = "LABS00200";
    public static String RET_CODE_LABS_UNKNOWN= "LABS00201";
    public static String RET_CODE_LABS_SYSTEM_ERROR= "LABS00202";
    public static String RET_CODE_LABS_TRANS_NOT_EXISTS= "LABS10200";
    public static String RET_CODE_LABS_ORDER_NOT_EXISTS= "LABS10202";
    public static String RET_CODE_LABS_MCH_ORDER_NOT_EXISTS= "LABS10203";

    // 交易状态
    public static String TRADE_STATE_INIT = "INIT"; // 初始化
    public static String TRADE_STATE_CREATE = "CREATE"; // 下单成功
    public static String TRADE_STATE_SUCCESS = "SUCCESS"; // 交易成功
    public static String TRADE_STATE_FAIL = "FAIL"; // 交易失败
    public static String TRADE_STATE_DEAL = "DEAL"; // 交易处理中
    public static String TRADE_STATE_UNKNOWN = "UNKNOWN"; // 未知状态
    public static String TRADE_STATE_CLOSE = "CLOSE"; // 订单关闭
    public static String TRADE_STATE_PART_REFUND = "PART_REFUND"; // 部分退款
    public static String TRADE_STATE_REFUND = "REFUND"; // 全部退款
    public static String TRADE_STATE_REVOKED = "REVOKED"; // 订单撤销
    //开发平台v3接口
    public static Set<String> REFUND_RETRY_CODE = new HashSet<>(Arrays.asList("710039", "710038"));
    public static final String RETRY_MSG = "请求服务失败【无访问权限(API无访问权限,请联系相关人员开通)】";
    public static final String SUCCESS_CODE = "000000";
    public static final String VERSION_3 = "3.0";
    public static final String TRADE_CODE_UNIONPAY = "030304";


    /**
     * 服务商机构标识码
     */
    public static final String PID = "C1000001";


    public static final String  MICRO_MESSENGER="MicroMessenger";
    public static final String  PAY_CHANNEL_01 ="01";
}
