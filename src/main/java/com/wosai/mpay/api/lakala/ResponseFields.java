package com.wosai.mpay.api.lakala;

/**
 * Created by ma<PERSON>u
 */
public class ResponseFields {

    public static final String RESPONSECODE = "responseCode";// 返回码000000;成功返回000000，否则返回六位错误码。;当返回0000C0时，表示本次支付进行中，需间隔10秒后通过订单查询接口发起
    public static final String REQLOGNO = "reqLogNo";// 请求流水号
    public static final String MERCID = "mercId";// 商户号
    public static final String USERID = "userId";// 用户识别码,微信为sub_openid，支付宝为pay_id
    public static final String TXNTM = "txnTm";// 交易时间
    public static final String TXNAMT = "txnAmt";// 交易金额
    public static final String PAYORDERID = "payOrderId";// 第三方钱包订单号
    public static final String MERORDERNO = "merOrderNo";// 商户订单号
    public static final String PAYCHLDESC = "payChlDesc";// 支付渠道描述
    public static final String MRKINFO = "mrkInfo";// 营销优惠信息
    public static final String EXTRPDATA = "extRpData";// 扩展返回数据
    public static final String MESSAGE = "message";// 返回码描述
    public static final String ERR_CODE = "errCode";//第三方钱包返回错误码
    public static final String MAC = "MAC";// MD5计算值

    public static final String PREPAYID = "prePayId";//公众号支付返回的预付订单号
    public static final String AUTHCODE = "authCode";//支付二维码链接,NATIVE时会返回
    public static final String STATUS = "status";//状态
    public static final String DETAILS = "details";//分账明细
    public static final String TXN_STAT = "txnStat";//
    public static final String LEDGERTRANS_ID = "ledgerTranSid";//分账请求流水id
    public static final String SEND_MERID = "sendMerId";//分账发起商户号
    public static final String SEND_TERMID = "sendTermId";//分账发起终端号
    public static final String REVC_MERID = "revcMerId";//分账接收商户号
    public static final String REVC_TERMID = "revcTermId";//分账接收终端号


    public static final String ORNREQLOGNO = "ornReqLogNo";//原请求流水号

    public static final String CODE = "code";
    public static final String MSG = "msg";
    public static final String DATA = "data";
    public static final String SERIAL_NO = "serialNo";

}
