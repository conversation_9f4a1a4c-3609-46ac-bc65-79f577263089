package com.wosai.mpay.api.lakala;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * Created by maoyu
 */
public class LakalaClient {

    public static final Logger logger = LoggerFactory.getLogger(LakalaClient.class);
    private int connectTimeout = 3000;
    private int readTimeout = 15000;


    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }


    /**
     * 分账业务部分接口不走sips，直接对接清结算，签名算法不一样
     */
    public static List<Integer> SHAIRNG_SIGN_WITH_MD5_PAY_METHODS = Arrays.asList(
            LakalaConstants.METHOD_PROFIT_SHARING_OPEN_SUBMIT, LakalaConstants.METHOD_PROFIT_SHARING_OPEN_CONFIRM,
            LakalaConstants.METHOD_PROFIT_SHARING_FILE_UPLOAD, LakalaConstants.METHOD_PROFIT_SHARING_RELATION_APPLAY,
            LakalaConstants.METHOD_PROFIT_SHARING_RELATION_SYNC
    );

    /**
     * 根据不同的接口调用返回需要参与签名的字段
     *
     * @param payMethod
     * @return
     */
    public static List<String> getSignatureColumns(int payMethod) {
        List<String> signColumns = Arrays.asList();
        switch (payMethod) {
            case LakalaConstants.METHOD_BSC_PAY:
                signColumns = BusinessFields.BSC_PAY_SIGNATURE_PARAMS;
                break;
            case LakalaConstants.METHOD_BSC_CANCEL:
                signColumns = BusinessFields.BSC_CANCEL_SIGNATURE_PARAMS;
                break;
            case LakalaConstants.METHOD_CSB_PAY:
                signColumns = BusinessFields.CSB_PAY_SIGNATURE_PARAMS;
                break;
            case LakalaConstants.METHOD_CSB_CANCEL:
                signColumns = BusinessFields.CSB_CANCEL_SIGNATURE_PARAMS;
                break;
            case LakalaConstants.METHOD_QUERY:
                signColumns = BusinessFields.QUERY_SIGNATURE_PARAMS;
                break;
            case LakalaConstants.METHOD_POSITIVE_QUERY:
                signColumns = BusinessFields.POSITIVE_QUERY_SIGNATURE_PARAMS;
                break;
            case LakalaConstants.METHOD_REFUND:
                signColumns = BusinessFields.REFUND_SIGNATURE_PARAMS;
                break;
            case LakalaConstants.METHOD_REFUND_QUERY:
                signColumns = BusinessFields.REFUND_QUERY_SIGNATURE_PARAMS;
                break;
            case LakalaConstants.METHOD_PROFIT_SHARING_PAY:
                signColumns = BusinessFields.PROFIT_SHARING_SIGNATURE_PARAMS;
                break;
            case LakalaConstants.METHOD_PROFIT_SHARING_RESTITUTE:
                signColumns = BusinessFields.PROFIT_SHARING_SIGNATURE_PARAMS;
                break;
            case LakalaConstants.METHOD_PROFIT_SHARING_QUERY:
                signColumns = BusinessFields.PROFIT_SHARING_QUERY_SIGNATURE_PARAMS;
                break;
        }
        return signColumns;
    }

    public Map<String, Object> call(String serviceUrl, String signKey, int payMethod, Map<String, Object> request) throws MpayException, MpayApiNetworkError {
        String requestXml = preProcess(signKey, payMethod, request);
        logger.debug("request {}", requestXml);
//        String response = WebUtils.doPost(null, null, serviceUrl, "text/xml", getBytes(requestXml, LakalaConstants.CHARSET_GBK), connectTimeout, readTimeout, LakalaConstants.CHARSET_GBK);
        String response = HttpClientUtils.doPost(LakalaClient.class.getName(), null, null, serviceUrl, "text/xml", requestXml, LakalaConstants.CHARSET_GBK, connectTimeout, readTimeout);
        logger.debug("response {}", response.replaceAll("\\n", ""));//返回的xml报文，有换行，打印日志时，打印在一行上面。
       return postProcess(response);
    }


    public static String preProcess(String signKey, int payMethod, Map<String, Object> request) throws MpayException {
        for (Object mapKey : request.keySet().toArray()) {
            if (request.get(mapKey) == null) {
                request.remove(mapKey);
            }
        }
        request.put(BusinessFields.MAC, LakalaSignature.getSign(request, signKey, getSignatureColumns(payMethod), LakalaConstants.CHARSET_GBK));
        String requestXml = XmlUtils.map2XmlString(request, "xml");
        return requestXml;
    }


    public static Map<String,Object> postProcess(String response){
        Map<String, Object> result = XmlUtils.parse(response);
        return result;
    }

    public static void main(String[] args) throws NoSuchAlgorithmException, MpayException, IOException {
//        testPay();
//        testOrderQuery();
//        testReverse();
//        testRefund();
//        testRefundQuery();
//        testUnifiedOrder();
//        testCloseOrder();
//          testPreCreate();
//            query();
//        csbcancel();
//        refund();
//        refundQuery();
//        testBscCancel();
        testWapPay();

    }


    public static void testPay() throws MpayException, MpayApiNetworkError {
        SimpleDateFormat format = new SimpleDateFormat(LakalaConstants.DATE_TIME_FORMAT);
        String timm = format.format(new Date(System.currentTimeMillis()));
        LakalaClient client = new LakalaClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_BSC);//Y
        builder.set(BusinessFields.COMPORGCODE, LakalaConfig.LKL_COMPORTCODE);//Y
        builder.set(BusinessFields.REQLOGNO, "2016051819960112");//Y
        builder.set(BusinessFields.REQTM, timm);//Y
        builder.set(BusinessFields.PAYCHLTYP, LakalaConstants.PAY_WAY_WEIXIN);//Y
        builder.set(BusinessFields.MERCID, LakalaConfig.MERCHANTID);//Y
        builder.set(BusinessFields.TERMID, LakalaConfig.TERMID);//Y
        builder.set(BusinessFields.TXNAMT, "1");//Y
        builder.set(BusinessFields.AUTHCODE, "130233436138544054");//Y
        builder.set(BusinessFields.GOODSTAG, "guangzhou");//N
        builder.set(BusinessFields.ORDERINFO, "wosai");//N
        builder.set(BusinessFields.EXTDATA, "2|3|4");//N
        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(LakalaConfig.BSC_PAY_URL, LakalaConfig.LKL_SECRET, LakalaConstants.METHOD_BSC_PAY, request);
        logger.debug("lklWx pay response {}", result);
    }

    public static void testBscCancel() throws MpayException, MpayApiNetworkError {
        LakalaClient client = new LakalaClient();
        RequestBuilder builder = new RequestBuilder();
        SimpleDateFormat format = new SimpleDateFormat(LakalaConstants.DATE_TIME_FORMAT);
        String timm = format.format(new Date(System.currentTimeMillis()));
        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_BSC_CANCEL);//Y
        builder.set(BusinessFields.COMPORGCODE, LakalaConfig.LKL_COMPORTCODE);//Y
        builder.set(BusinessFields.REQLOGNO, "2016051829517613");//Y
        builder.set(BusinessFields.REQTM, timm);//Y
        builder.set(BusinessFields.ORNREQLOGNO, "2016051819960112");//Y
        builder.set(BusinessFields.PAYCHLTYP, LakalaConstants.PAY_WAY_WEIXIN);//Y
        builder.set(BusinessFields.MERCID, LakalaConfig.MERCHANTID);//Y
        builder.set(BusinessFields.TERMID, LakalaConfig.TERMID);//Y
        builder.set(BusinessFields.REQTYPE, LakalaConstants.CANCEL_REQ_TYPE_USER_CANCEL);//Y
        builder.set(BusinessFields.EXTDATA, "2|3|4");//N
        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(LakalaConfig.BSC_CANCEL_URL, LakalaConfig.LKL_SECRET, LakalaConstants.METHOD_BSC_CANCEL, request);
        logger.debug("lklWx bsc cancel response {}", result);
    }

    public static void testPreCreate() throws MpayException, MpayApiNetworkError {
        SimpleDateFormat format = new SimpleDateFormat(LakalaConstants.DATE_TIME_FORMAT);
        String timm = format.format(new Date(System.currentTimeMillis()));
        LakalaClient client = new LakalaClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_CSB);//Y
        builder.set(BusinessFields.COMPORGCODE, LakalaConfig.LKL_COMPORTCODE);//Y
        builder.set(BusinessFields.REQTM, timm);//Y
        builder.set(BusinessFields.REQLOGNO, "2016051871003012");//Y
        builder.set(BusinessFields.PAYCHLTYP, LakalaConstants.PAY_WAY_WEIXIN);//Y
        builder.set(BusinessFields.MERCID, LakalaConfig.MERCHANTID);//Y
        builder.set(BusinessFields.TERMID, LakalaConfig.TERMID);//Y
        builder.set(BusinessFields.TXNAMT, "1");//Y
        builder.set(BusinessFields.TRADETYPE, LakalaConstants.TRADE_TYPE_NATIVE);//Y
        builder.set(BusinessFields.PRODUCTID, "wosai");//
        builder.set(BusinessFields.OPENID, "");
        //builder.set(BusinessFields.GOODSTAG, "guangzhou");//N
        builder.set(BusinessFields.ORDERINFO, "wosai");//N
        //builder.set(BusinessFields.EXTDATA, "2|3|4");//N
        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(LakalaConfig.CSB_PRECREATE_URL, LakalaConfig.LKL_SECRET, LakalaConstants.METHOD_CSB_PAY, request);
        logger.debug("lklWx precreate response {}", result);
    }


    public static void testWapPay() throws MpayException, MpayApiNetworkError {
        SimpleDateFormat format = new SimpleDateFormat(LakalaConstants.DATE_TIME_FORMAT);
        String timm = format.format(new Date(System.currentTimeMillis()));
        LakalaClient client = new LakalaClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_CSB);//Y
        builder.set(BusinessFields.COMPORGCODE, LakalaConfig.LKL_COMPORTCODE);//Y
        builder.set(BusinessFields.REQTM, timm);//Y
        builder.set(BusinessFields.REQLOGNO, "2016052716503012");//Y
        builder.set(BusinessFields.PAYCHLTYP, LakalaConstants.PAY_WAY_WEIXIN);//Y
        builder.set(BusinessFields.MERCID, LakalaConfig.MERCHANTID);//Y
        builder.set(BusinessFields.TERMID, LakalaConfig.TERMID);//Y
        builder.set(BusinessFields.TXNAMT, "1");//Y
        builder.set(BusinessFields.TRADETYPE, LakalaConstants.TRADE_TYPE_JSAPI);//Y
        builder.set(BusinessFields.PRODUCTID, "wosai");//
        builder.set(BusinessFields.OPENID, "oSTAxt9OVTVuEqc5BHX4WR89Dgkg");
        builder.set(BusinessFields.SUBAPPID, "wxd986b5cd3f7e0be0");

        //builder.set(BusinessFields.GOODSTAG, "guangzhou");//N
        builder.set(BusinessFields.ORDERINFO, "wosai");//N
        //builder.set(BusinessFields.EXTDATA, "2|3|4");//N
        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(LakalaConfig.CSB_PRECREATE_URL, LakalaConfig.LKL_SECRET, LakalaConstants.METHOD_CSB_PAY, request);
        logger.debug("lklWx precreate response {}", result);
    }



    public static void csbcancel() throws MpayException, MpayApiNetworkError {
        LakalaClient client = new LakalaClient();
        RequestBuilder builder = new RequestBuilder();
        SimpleDateFormat format = new SimpleDateFormat(LakalaConstants.DATE_TIME_FORMAT);
        String timm = format.format(new Date(System.currentTimeMillis()));
        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_CSB_CANCEL);//Y
        builder.set(BusinessFields.COMPORGCODE, LakalaConfig.LKL_COMPORTCODE);//Y
        builder.set(BusinessFields.REQLOGNO, "2016051817880233");//Y
        builder.set(BusinessFields.ORNREQLOGNO, "2016051871003012");//Y
        builder.set(BusinessFields.REQTM, timm);//Y
        builder.set(BusinessFields.PAYCHLTYP, LakalaConstants.PAY_WAY_WEIXIN);//Y
        builder.set(BusinessFields.MERCID, LakalaConfig.MERCHANTID);//Y
        builder.set(BusinessFields.TERMID, LakalaConfig.TERMID);//Y
        builder.set(BusinessFields.REQTYPE, LakalaConstants.CANCEL_REQ_TYPE_POS_TIMEOUT);//Y
        builder.set(BusinessFields.EXTDATA, "2|3|4");//N

        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(LakalaConfig.CSB_CANCEL_URL, LakalaConfig.LKL_SECRET, LakalaConstants.METHOD_CSB_CANCEL, request);
        logger.debug("lklWx precreate cancel response {}", result);
    }

    public static void query() throws MpayException, MpayApiNetworkError {
        LakalaClient client = new LakalaClient();
        RequestBuilder builder = new RequestBuilder();

        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_QUERY);//Y
        builder.set(BusinessFields.COMPORGCODE, LakalaConfig.LKL_COMPORTCODE);//Y
        builder.set(BusinessFields.REQLOGNO, "2016051817776271");//Y
        builder.set(BusinessFields.ORNREQLOGNO, "2016050315517613");//Y
        SimpleDateFormat format = new SimpleDateFormat(LakalaConstants.DATE_TIME_FORMAT);
        String timm = format.format(new Date(System.currentTimeMillis()));
        builder.set(BusinessFields.REQTM, timm);//Y
        builder.set(BusinessFields.PAYCHLTYP, LakalaConstants.PAY_WAY_WEIXIN);
        builder.set(BusinessFields.MERCID, LakalaConfig.MERCHANTID);//Y
        builder.set(BusinessFields.TERMID, LakalaConfig.TERMID);//Y
        builder.set(BusinessFields.EXTDATA, "2|3|4");//N

        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(LakalaConfig.QUERY_URL, LakalaConfig.LKL_SECRET, LakalaConstants.METHOD_QUERY, request);
        logger.debug("lklWx query response {}", result);

    }


    public static void positiveQuery() throws MpayException, MpayApiNetworkError {
        LakalaClient client = new LakalaClient();
        RequestBuilder builder = new RequestBuilder();

        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_QUERY_POSITIVE);//Y
        builder.set(BusinessFields.COMPORGCODE, LakalaConfig.LKL_COMPORTCODE);//Y
        builder.set(BusinessFields.REQLOGNO, "2342342342342");//Y
        SimpleDateFormat format = new SimpleDateFormat(LakalaConstants.DATE_TIME_FORMAT);
        String timm = format.format(new Date(System.currentTimeMillis()));
        builder.set(BusinessFields.REQTM, timm);//Y
        builder.set(BusinessFields.PAYCHLTYP, LakalaConstants.PAY_WAY_WEIXIN);
        builder.set(BusinessFields.MERCID, LakalaConfig.MERCHANTID);//Y
        builder.set(BusinessFields.TERMID, LakalaConfig.TERMID);//Y
        builder.set(BusinessFields.MERORDERNO, "lsdlsdks");//Y 拉卡拉支付平台产生的订单号
        builder.set(BusinessFields.EXTDATA, "2|3|4");//N

        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(LakalaConfig.POSITIVE_QUERY_URL, LakalaConfig.LKL_SECRET, LakalaConstants.METHOD_POSITIVE_QUERY, request);
        logger.debug("lklWx positive query response {}", result);

    }

    public static void refund() throws MpayException, MpayApiNetworkError {
        LakalaClient client = new LakalaClient();
        RequestBuilder builder = new RequestBuilder();

        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_REFUND);//Y
        builder.set(BusinessFields.COMPORGCODE, LakalaConfig.LKL_COMPORTCODE);//Y
        builder.set(BusinessFields.REQLOGNO, "2016051827992345");//Y
        builder.set(BusinessFields.ORNREQLOGNO, "2016051819960112");//Y
        SimpleDateFormat format = new SimpleDateFormat(LakalaConstants.DATE_TIME_FORMAT);
        String timm = format.format(new Date(System.currentTimeMillis()));
        builder.set(BusinessFields.REQTM, timm);//Y
        builder.set(BusinessFields.PAYCHLTYP, LakalaConstants.PAY_WAY_WEIXIN);//Y
        builder.set(BusinessFields.MERCID, LakalaConfig.MERCHANTID);//Y
        builder.set(BusinessFields.TERMID, LakalaConfig.TERMID);//Y
        builder.set(BusinessFields.TXNAMT, "1");//Y
        builder.set(BusinessFields.EXTDATA, "2|3|4");//N

        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(LakalaConfig.REFUND_URL, LakalaConfig.LKL_SECRET, LakalaConstants.METHOD_REFUND, request);
        logger.debug("lklWx refund  response {}", result);
    }


    public static void refundQuery() throws MpayException, MpayApiNetworkError {
        LakalaClient client = new LakalaClient();
        RequestBuilder builder = new RequestBuilder();

        builder.set(BusinessFields.FUNCOD, LakalaConstants.FUNCOD_REFUND_QUERY);//Y
        builder.set(BusinessFields.COMPORGCODE, LakalaConfig.LKL_COMPORTCODE);//Y
        builder.set(BusinessFields.REQLOGNO, "2016051817992341");//Y
        builder.set(BusinessFields.ORNREQLOGNO, "2016051817992345");//Y
        SimpleDateFormat format = new SimpleDateFormat(LakalaConstants.DATE_TIME_FORMAT);
        String timm = format.format(new Date(System.currentTimeMillis()));
        builder.set(BusinessFields.REQTM, timm);//Y
        builder.set(BusinessFields.PAYCHLTYP, LakalaConstants.PAY_WAY_WEIXIN);//Y
        builder.set(BusinessFields.MERCID, LakalaConfig.MERCHANTID);//Y
        builder.set(BusinessFields.TERMID, LakalaConfig.TERMID);//Y
        builder.set(BusinessFields.EXTDATA, "2|3|4");//N

        Map<String, Object> request = builder.build();
        Map<String, Object> result = client.call(LakalaConfig.REFUND_QUERY_URL, LakalaConfig.LKL_SECRET, LakalaConstants.METHOD_REFUND_QUERY, request);
        logger.debug("lklWx refund  response {}", result);

    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }
}
