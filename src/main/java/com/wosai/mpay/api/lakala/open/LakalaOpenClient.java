package com.wosai.mpay.api.lakala.open;

import java.util.Map;
import java.util.StringJoiner;
import java.util.concurrent.ThreadLocalRandom;

import com.wosai.mpay.util.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.RsaSignature;


/**
 * 拉卡拉开放平台
 */
public class LakalaOpenClient {

    public static final Logger logger = LoggerFactory.getLogger(LakalaOpenClient.class);
    private int connectTimeout = 3000;
    private int readTimeout = 15000;
    


    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }


    public Map<String, Object> call(String serviceUrl, String appid, String seriaNo, String privateKey, Map<String, Object> request) throws MpayException, MpayApiNetworkError {
        return call(serviceUrl, appid, seriaNo, privateKey, null, request);
    }

    public Map<String, Object> call(String serviceUrl, String appid, String seriaNo, String privateKey, String encryptKey, Map<String, Object> request) throws MpayException, MpayApiNetworkError {
        String requestStr = JsonUtil.toJsonStr(request);
        logger.debug("request {}", requestStr);
        if(encryptKey != null){
            requestStr = LakalaOpenSM4Util.encrypt(encryptKey, requestStr);
        }
        String response = HttpClientUtils.doPost(LakalaOpenClient.class.getName(), null, null, serviceUrl, "application/json", requestStr, getAuthorization(appid, seriaNo, privateKey, requestStr), LakalaConstants.CHARSET_UTF_8, connectTimeout, readTimeout, true);
        logger.debug("response {}", response.replaceAll("\\n", ""));//返回的xml报文，有换行，打印日志时，打印在一行上面。
        return JsonUtil.jsonStringToObject(response, Map.class);
    }



    private Map<String, String> getAuthorization(String appid, String seriaNo, String privateKey, String requestStr) throws MpayException {
        String nonceStr = getNonceStr();

        String timestamp = System.currentTimeMillis()/1000 + "";
        StringJoiner joiner = new StringJoiner("\n");
        joiner.add(appid)
            .add(seriaNo)
            .add(timestamp)
            .add(nonceStr)
            .add(requestStr)
            .add("");
        String signature = RsaSignature.sign(joiner.toString(), RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, privateKey);
        
        StringBuilder builder = new StringBuilder();
        builder.append(LakalaConstants.SIGN_LAKLA_RSA2).append(" ")
            .append(ProtocolFields.APP_ID).append("=\"").append(appid).append("\",")
            .append(ProtocolFields.SERIAL_NO).append("=\"").append(seriaNo).append("\",")
            .append(ProtocolFields.TIMESTAMP).append("=\"").append(timestamp).append("\",")
            .append(ProtocolFields.NONCE_STR).append("=\"").append(nonceStr).append("\",")
            .append(ProtocolFields.SIGNATURE).append("=\"").append(signature).append("\"");
        return MapUtils.hashMap(ProtocolFields.AUTHORIZATION, builder.toString());
    }
    
    private String getNonceStr(){
        String str = ThreadLocalRandom.current().nextLong() + "";
        if(str.length() > 12) {
            return str.substring(str.length() - 12, str.length());
        }
        return str;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }
}
