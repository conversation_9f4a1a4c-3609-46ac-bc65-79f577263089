package com.wosai.mpay.api.NuccBestPay;

public class NuccBestPayResponseFields {
    /**
     * 系统返回码
     */
    public final static String SYS_RTN_CD = "SysRtnCd";

    /**
     * 系统返回说明
     */
    public final static String SYS_RTN_DESC = "SysRtnDesc";

    /**
     * 系统返回时间
     */
    public final static String SYS_RTN_TM = "SysRtnTm";

    /**
     * 业务返回码
     */
    public final static String BIZ_STS_CD = "BizStsCd";

    /**
     * 业务返回说明
     */
    public final static String BIZ_STS_DESC = "BizStsDesc";

    /**
     * 账户合作号
     */
    public final static String ACCOUNT_PARTNER_ID = "AccountPartnerId";

    /**
     * 收单合作号
     */
    public final static String Accept_PARTNER_ID = "AcceptPartnerId";

    /**
     * 商户标识码
     */
    public final static String MRCHNT_ID = "MrchntId";

    /**
     * 账户机构扩展字段
     */
    public final static String ACCOUNT_EXTENSION = "AccountExtension";

    /**
     * 支付类型
     */
    public final static String TRX_TYPE_CD = "TrxTypeCd";

    /**
     * 用户支付方式
     */
    public final static String TRX_USER_TYPE_CD = "TrxUserTypeCd";

    /**
     * 平台流水号
     */
    public final static String TRX_ID = "TrxId";

    /**
     * 原交易平台流水号
     */
    public static final String ORI_TRX_ID = "OriTrxId";

    /**
     * 商户订单号
     */
    public final static String TRX_ORDER_NO = "TrxOrderNo";

    /**
     * 订单金额
     */
    public final static String TRX_AMT = "TrxAmt";


    /**
     * 应结订单金额
     */
    public final static String TRX_SETTLE_AMT = "TrxSettleAmt";


    /**
     * 用户支付金额
     */
    public final static String TRX_PAYER_AMT = "TrxPayerAmt";


    /**
     * 交易状态
     */
    public final static String TRX_STATUS = "TrxStatus";

    /**
     * 交易完成时间
     */
    public final static String TRX_FINISH_TM = "TrxFinishTm";

    /**
     * 交易批次号
     */
    public final static String BATCH_ID = "BatchId";


    /**
     * 原始交易批次号
     */
    public final static String ORI_BATCH_ID = "OriBatchId";

    /**
     * 原商户订单号
     */
    public final static String ORI_TRX_ORDER_NO = "OriTrxOrderNo";

    /**
     * 原交易完成时间
     */
    public final static String ORI_TRX_FINISH_TM = "OriTrxFinishTm";


    /**
     * 原系统返回码
     */
    public final static String ORI_SYS_RTN_CD = "OriSysRtnCd";

    /**
     * 原业务返回码
     */
    public final static String ORI_BIZ_STS_CD = "OriBizStsCd";

    /**
     * 原交易状态
     */
    public final static String ORI_TRX_STATUS = "OriTrxStatus";


    /**
     * 订单会话标识
     */
    public final static String TRX_SESSION_ID = "TrxSessionId";


}
