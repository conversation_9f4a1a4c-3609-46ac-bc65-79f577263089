package com.wosai.mpay.api.NuccBestPay;

import com.google.common.collect.Maps;
import com.wosai.mpay.api.SSLEnvFlag;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import java.io.UnsupportedEncodingException;
import java.util.Map;

public class NuccBestPayClient {

    public static final Logger logger = LoggerFactory.getLogger(NuccBestPayClient.class);

    private int connectTimeout = 3000;

    private int readTimeout = 15000;

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map<String, Object> call(String serviceUrl, String msgTy, String channelRsaKey, String signSN, SSLContext sslContext, Map<String, Object> request) throws MpayException, MpayApiNetworkError {
        String requestXml = RequestGenerator.genRequestBody(msgTy, signSN, channelRsaKey, request, request.get(NuccBestPayBusinessFields.TRX_DT_TM));
        Map<String, String> requestHeaders = Maps.newHashMap();
        requestHeaders.put(ProtocolFields.MSG_TP, msgTy);
        byte[] content = getBytes(requestXml, NuccBestPayConstants.CHARSET_UTF8);
        requestHeaders.put(ProtocolFields.CONTENT_LENGTH,String.valueOf(content.length));
        logger.info("request -> {}, requestHeaders -> {}", requestXml, JsonUtil.toJsonStr(requestHeaders));
        //由于某些支付通道的测试环境会出现证书不符等各种异常的情况，故此处做特殊处理
        sslContext = SSLEnvFlag.turnOffSSl() ? SSLUtil.getUnsafeSSLContext() : sslContext;
        HostnameVerifier hostnameVerifier = SSLEnvFlag.getNotVerifyHostNames().size() > 0 ? SSLUtil.getUnsafeHostnameVerifier(SSLEnvFlag.getNotVerifyHostNames()) : null;
        String response =  WebUtils.doPost(sslContext, hostnameVerifier, serviceUrl, "application/xml; charset=utf-8",requestHeaders, content, connectTimeout, readTimeout);
        logger.info("response -> {}", response.replaceAll("\\n", "").replaceAll("\\r",""));//返回的xml报文，有换行，打印日志时，打印在一行上面。
        response = response.split("(\\{S:)")[0];
        Map<String, Object> result = XmlUtils.parse(response);
        return (result == null) ? null : (Map<String, Object>)result.get(NuccBestPayConstants.MSG_BODY);
    }


    private static byte[] getBytes(String string, String charset) throws MpayException {
        if (StringUtils.isEmpty(string)) {
            return null;
        } else {
            try {
                return string.getBytes(charset);
            } catch (UnsupportedEncodingException e) {
                throw new MpayException("UnsupportedEncodingException :", e);
            }
        }
    }

}
