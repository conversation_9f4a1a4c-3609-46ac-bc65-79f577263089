package com.wosai.mpay.api.NuccBestPay;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class NuccBestPayConstants {

    /** UTF-8字符集 **/
   public final static String CHARSET_UTF8 = "UTF-8";


    public final static String DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";
    /**
     * 商户信息登记申请报文
     */
     public final static String  MERCHANT_REGISTER = "bcs.101.001.01";


    /**
     * 商户信息登记回执报文
     */
     public final static String  MERCHANT_REGISTER_RETURN = "bcs.102.001.01";


    /**
     * 商户信息查询申请报文
     */
     public final static String  MERCHANT_REGISTER_QUERY = "bcs.103.001.01";

    /**
     * 商户信息查询回执报文
     */
     public final static String  MERCHANT_REGISTER_QUERY_RETURN = "bcs.104.001.01";

    /**
     * 收款扫码交易申请报文
     */

     public final static String PAY = "bcs.301.001.01";

    /**
     * 收款扫码交易回执报文
     */
     public final static String PAY_RETURN = "bcs.302.001.01";

    /**
     * 统一下单交易申请报文
     */
     public final static String PRE_CREATE = "bcs.303.001.01";

    /**
     * 统一下单交易回执报文
     */
     public final static String PRE_CREATE_RETURN = "bcs.336.001.01";


    /**
     * 退款申请报文
     */
     public final static String REFUND = "bcs.401.001.01";


    /**
     * 退款回执报文
     */
     public final static String REFUND_RETUND = "bcs.402.001.01";


    /**
     * 交易查询申请报文
     */
    public final static String NORMAL_QUERY = "bcs.307.001.01";


    /**
     * 交易查询回执报文
     */
     public final static String NORMAL_QUERY_RETURN = "bcs.308.001.01";


    /**
     * 退款查询报文
     */
     public final static String REFUND_QUERY = "bcs.403.001.01";


    /**
     * 退款查询回执报文
     */
    public final static String REFUND_QUERY_RETURN = "bcs.404.001.01";


    /**
     * 撤销申请报文
     */
    public final static String CANCEL = "bcs.305.001.01";


    /**
     * 撤销回执报文
     */
    public final static String CANCEL_RETURN = "bcs.306.001.01";



    /**
     * 通用应答报文
     */
    public final static String  COMMON_RETURN = "bcs.901.001.01";


    /**
     * 报文头
     */
    public final static String MSG_HEADER = "MsgHeader";

    /**
     * 报文体
     */
    public final static String MSG_BODY = "MsgBody";



    public final static String SYS_RTN_CODE_SUCCESS = "00000000";

    public final static String BIZ_STATE_CODE_SUCCESS = "00000000";

    public final static String BIZ_STATE_IN_PROG = "BB000001";//交易未完成，进行中

    public final static String BIZ_STATE_UN_KNOW = "BB000098";//业务处理异常，结果未知

    public final static String BIZ_TRANS_NO_ALREADY_EXISTS = "BB000014";//该流水号交易已存在

    public final static String BIZ_FAIL = "BB000099";//业务处理失败
    
    public final static String BIZ_ORI_TRADE_CLOSED = "BB000002";// 订单超时引起交易关闭


    public final static String BIZ_ORI_TRANS_NOT_EXISTS = "BB000031";//原交易不存在

    public final static String BIZ_ORI_TRANS_FAIL = "BB000035";//原支付交易为非成功状态


    public final static String ORI_TRX_STATUS_SUCCESS = "00"; // 订单状态：支付成功

    public final static String SUB_PAYWAY_BARCODE = "02";//收款扫码

    public final static String SUB_PAYWAY_QRCODE = "01";//付款扫码

    public final static String SUB_PAYWAY_APP = "03";//APP 支付

    public final static String SUB_PAYWAY_OTHER = "04";//


    public final static String TRXTYPECD_BALANCE_FIRST = "00";//如果用户设置的支付方式是余额优先，则下单同步返回支付结果，交易状态为 00

    public final static String TRXTYPECD_BANK_CARD_FIRST = "02";//;若用户设置的支付方式是银行卡优先， 则下单返回等待支付的交易状态，值为 02



    /**
     * 网联业务返回码
     */
    public final static Map<String, String> SYS_CODE_MESSAGE_MAP = Collections.unmodifiableMap(new HashMap<String, String>() {
        {
            put("BS000001", "平台限制交易流量");
            put("BS000004", "报文编号错误");
            put("BS000021", "请求 IDC 非法");
            put("BS000022", "请求报文格式有误");
            put("BS000024", "请求报文的参数有误");
            put("BS000025", "请求报文签名未通过验证");
            put("BS000033", "平台与收单机构通讯异常");
            put("BS000034", "平台与账户机构通讯异常");
            put("BS000035", "交易时间与系统时间不一致");
            put("BS000098", "系统异常");
            put("BS000099", "系统正忙");
        }
    });

    /**
     * 网联业务返回码
     */
    public final static Map<String, String> BIZ_CODE_MESSAGE_MAP = Collections.unmodifiableMap(new HashMap<String, String>() {
        {
            put("BB000001", "交易未完成，进行中");
            put("BB000002", "交易状态已置为终态");
            put("BB000009", "账户合作号不正确");
            put("BB000010", "收单合作号不正确");
            put("BB000025", "收单机构返回结果处理失败");
            put("BB000026", "账户机构返回结果处理失败");
            put("BB000031", "原交易不存在");
            put("BB000033", "交易金额超过商户单笔限额");
            put("BB000035", "原支付交易为非成功状态");
            put("BB000036", "退款金额超过原支付交易可退款金额");
            put("BB000037", "原支付交易已超过退款有效期");
            put("BB000038", "退款次数超限");
            put("BB000039", "超出可查询的时间范围");
            put("BB000040", "退款对应的原支付交易不存在");
            put("BB000098", "业务处理异常，结果未知");
            put("BB000099", "业务处理失败");
        }
    });


    /** 交易状态 **/
    public final static String TRADE_STATE_SUCCESS = "SUCCESS";//支付成功
    String TRADE_STATE_REFUND = "REFUND";//转入退款
    String TRADE_STATE_NOTPAY = "NOTPAY";//未支付
    String TRADE_STATE_CLOSED = "CLOSED";//已关闭
    String TRADE_STATE_REVOKED = "REVOKED";//已撤销（刷卡支付）
    String TRADE_STATE_USERPAYING = "USERPAYING";//用户支付中
    String TRADE_STATE_PAYERROR = "PAYERROR";//支付失败

    public final static List<String> PAY_FAIL_SYS_RETURN_CODE = Arrays.asList(
            "BS000001", "BS000004", "BS000021", "BS000022", "BS000024", "BS000025", "BS000035"
    );
    
    public final static List<String> PAY_FAIL_BIZ_RETURN_CODE = Arrays.asList(
            "BB000009", "BB000010", "BB000025", "BB000026", "BB000033"
    );
}
