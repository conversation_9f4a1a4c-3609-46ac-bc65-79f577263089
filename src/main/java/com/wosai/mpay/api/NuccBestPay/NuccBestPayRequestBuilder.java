package com.wosai.mpay.api.NuccBestPay;

import java.util.HashMap;
import java.util.Map;

public class NuccBestPayRequestBuilder {

    private Map<String, Object> request;

    public NuccBestPayRequestBuilder() {
        request = new HashMap<String, Object>();
    }

    public NuccBestPayRequestBuilder set(String field, Object value) {
        request.put(field, value);
        return this;

    }

    public Map<String, Object> build() {
        return request;
    }

}
