package com.wosai.mpay.api.NuccBestPay;

public class NuccBestPayBusinessFields {

    /**
     * 账户合作号
     */
    public static final String ACCOUNT_PARTNER_ID = "AccountPartnerId";

    /**
     * 收单合作号
     */
    public static final String ACCEPT_PARTNER_ID = "AcceptPartnerId";

    /**
     * 收单合作号
     */
    public static final String REQ_ID = "ReqId";

    /**
     * 日期时间
     */
    public static final String TRX_DT_TM = "TrxDtTm";

    /**
     * 商户名称
     */
    public static final String MRCHNT_NM = "MrchntNm";

    /**
     * 商户简称
     */
    public static final String MRCHNT_SHRT_NM = "MrchntShrtNm";

    /**
     * 商户地址
     */
    public static final String MRCHNT_ADR = "MrchntAdr";

    /**
     * 营业证件类型
     */
    public static final String MRCHNT_CERTID_TP_CD = "MrchntCertIdTpCd";

    /**
     * 营业证件编号
     */
    public static final String MRCHNT_CERT_ID = "MrchntCertId";

    /**
     * 内部商户标识码
     */
    public static final String MRCHNT_INNER_NO = "MrchntInnerNo";

    /**
     * 商户行业类别
     */
    public static final String MRCHNT_CTGY_CD = "MrchntCtgyCd";

    /**
     * 客服电话
     */
    public static final String SERVICE_PHONE = "ServicePhone";

    /**
     * 商户联系电话
     */
    public static final String MRCHNT_CTCTER_NM = "MrchntCtcterNm";

    /**
     * 商户 E-mail
     */
    public static final String MRCHNT_CTCTER_PHNE = "MrchntCtcterPhne";

    /**
     * 外包服务商标识
     */
    public static final String OUT_SRC_ID = "OutsrcId";

    /**
     * 收单机构扩展字段
     */
    public static final String ACCEPT_EXTENSION = "AcceptExtension";

    /**
     * 条码信息
     */
    public static final String BAR_CD = "BarCd";

    /**
     * 商户标识码
     */
    public static final String MRCHNT_ID = "MrchntId";

    /**
     * 场景信息
     */
    public static final String SCENE_INFO = "SceneInfo";

    /**
     * 受理终端IP地址
     */
    public static final String DEVICE_IP = "DeviceIp";


    /**
     * 受理终端设备号
     */
    public static final String DEVICE_ID = "DeviceId";

    /**
     * 用户终端 IP
     */
    public static final String Payer_Client_Ip = "PayerClientIp";

    /**
     * 操作员 ID
     */
    public static final String OPERATOR_ID = "OperatorId";

    /**
     * 商户门店 ID
     */
    public static final String STORE_ID = "StoreId";

    /**
     * 网联条码支付 IDC 标识
     */
    public static final String IDC_FLAG = "IdcFlag";

    /**
     * 平台流水号
     */
    public static final String TRX_ID = "TrxId";

    /**
     * 商户订单号
     */
    public static final String TRX_ORDER_NO = "TrxOrderNo";

    /**
     * 限制支付方式
     */
    public static final String TRX_LIMIT_TYPE_CD = "TrxLimitTypeCd";

    /**
     * 支付类型
     */
    public static final String TRX_TYPE_CD = "TrxTypeCd";

    /**
     * 订单金额
     */
    public static final String TRX_AMT = "TrxAmt";

    /**
     * 订单描述
     */
    public static final String DESCRIPTION = "Description";

    /**
     * 通知地址
     */
    public static final String NOTIFY_URL = "NotifyUrl";

    /**
     * 交易批次号
     */
    public static final String BATCH_ID = "BatchId";


    /**
     * 原始交易批次号
     */
    public static final String ORI_BATCH_ID = "OriBatchId";

    /**
     * 原交易日期时间
     */
    public static final String ORI_TRXDT_TM = "OriTrxDtTm";

    /**
     * 原交易平台流水号
     */
    public static final String ORI_TRX_ID = "OriTrxId";

    /**
     * 原商户订单号
     */
    public static final String ORI_TRX_ORDER_NO = "OriTrxOrderNo";

    /**
     * 原交易日期时间
     */
    public static final String ORI_TRX_DT_TM = "OriTrxDtTm";

    /**
     * 原订单金额
     */
    public static final String ORI_TRX_AMT = "OriTrxAmt";


    /**
     * 原交易网联条码支付 IDC 标识
     */
    public static final String ORI_IDC_FLAG = "OriIdcFlag";


    /**
     * 原交易商户订单号
     */
    public static final String ORI_FORWARD_TRX_ORDER_NO = "OriForwardTrxOrderNo";


    /**
     * 省编码
     */
    public static final String PROVINCE_CODE = "ProvinceCode";


    /**
     * 市编码
     */
    public static final String CITY_CODE = "CityCode";


    /**
     * 区编码
     */
    public static final String AREA_CODE = "AreaCode";


}
