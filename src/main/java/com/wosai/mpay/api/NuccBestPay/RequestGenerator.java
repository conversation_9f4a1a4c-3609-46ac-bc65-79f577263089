package com.wosai.mpay.api.NuccBestPay;

import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import com.wosai.mpay.util.XmlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import static com.wosai.mpay.api.NuccBestPay.NuccBestPayConstants.MSG_BODY;
import static com.wosai.mpay.api.NuccBestPay.NuccBestPayConstants.MSG_HEADER;

public class RequestGenerator {

    public static final Logger logger = LoggerFactory.getLogger(RequestGenerator.class);
    public static final SafeSimpleDateFormat SIMPLE_DATE_FORMAT = new SafeSimpleDateFormat(NuccBestPayConstants.DATE_TIME_FORMAT);

    public static String genRequestBody(String msgTy,String SignSN, String channelRsaKey, Map msgBodyMap, Object trxDtTm) throws MpayException {
        StringBuilder sb = new StringBuilder();
        sb.append("<root xmlns=\"namespace_string\">");
        Map<String, Object> msgHeaderMap = bulidMsgHeaderMap(msgTy, SignSN, trxDtTm);
        sb.append(XmlUtils.map2XmlString(msgHeaderMap, MSG_HEADER));
        sb.append(XmlUtils.map2XmlString(msgBodyMap, MSG_BODY));
        sb.append("</root>");
        sb.append("{S:"+RsaSignature.sign(sb.toString(), RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, channelRsaKey)+"}");
        sb.insert(0, "<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        return sb.toString();
    }

    public static String getCurrentTime(){
       return SIMPLE_DATE_FORMAT.format(new Date());
    }

    private static Map<String, Object> bulidMsgHeaderMap(String msgTy, String SignSN,Object trxDtTm){
        Map msgHeaderMap = new HashMap();
        msgHeaderMap.put(ProtocolFields.SND_DT, trxDtTm);
        msgHeaderMap.put(ProtocolFields.MSG_TP, msgTy);
        msgHeaderMap.put(ProtocolFields.SIGN_SN, SignSN);
        return msgHeaderMap;
    }


}
