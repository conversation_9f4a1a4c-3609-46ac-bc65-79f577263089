package com.wosai.mpay.api.chinaums;

/**
 * <AUTHOR> Date: 2019/4/26 Time: 5:28 PM
 */
public class ResponseFields {

    public static final String ERR_CODE = "errCode";//错误代码
    public static final String ERR_INFO = "errInfo";//错误说明
    public static final String TRANSACTION_TIME = "transactionTime";//交易时间,格式:hhmmss
    public static final String TRANSACTION_DATE_WITH_YEAR = "transactionDateWithYear";//交易日期,格式:yyyyMMdd
    public static final String SETTLEMENT_DATE_WITH_YEAR = "settlementDateWithYear";//格式:yyyyMMdd
    public static final String RETRIEVAL_REF_NUM = "retrievalRefNum";//检索参考号,长度12位
    public static final String TRANSACTION_AMOUNT = "transactionAmount";//订单金额,与交易请求金额一致
    public static final String ACTUAL_TRANSACTION_AMOUNT = "actualTransactionAmount";//营销联盟优惠后交易金额,营销联盟优惠后交易金额，最大长度12位
    public static final String AMOUNT = "amount";//实际支付金额,用户实际支付金额，最大长度12位
    public static final String ORDER_ID = "orderId";//订单号,最大长度50位
    public static final String MARKETING_ALLIANCE_DISCOUNT_INSTRUCTION = "marketingAllianceDiscountInstruction";//营销联盟优惠说明,最大长度200位
    public static final String THIRD_PARTY_DISCOUNT_INSTRUCTION = "thirdPartyDiscountInstruction";//第三方优惠说明,最大长度200位
    public static final String THIRD_PARTY_NAME = "thirdPartyName";//第三方名称,最大长度60位
    public static final String BANK_CARD_NO = "bankCardNo";//云闪付卡号
    public static final String THIRD_PARTY_BUYER_ID = "thirdPartyBuyerId";//第三方买家Id,最大长度32位
    public static final String THIRD_PARTY_BUYER_USER_NAME = "thirdPartyBuyerUserName";//第三方买家用户名
    public static final String THIRD_PARTY_ORDER_ID = "thirdPartyOrderId";//第三方订单号
    public static final String THIRD_PARTY_PAY_INFORMATION = "thirdPartyPayInformation";//第三方支付信息,格式为:方式:金额(单位:分)|方式:金额|......
    public static final String TOTAL_DISCOUNT_AMOUNT = "totalDiscountAmount";//优惠金额(合计)
    public static final String DISCOUNT_STATUS = "discountStatus";//优惠状态,1:订单有优惠但未找到 2:订单有优惠且找到
    public static final String PROMOTION_LIST = "promotionList";//优惠活动活动列表,单品营销优惠活动列表,数组-JSON
    public static final String CHANNEL_NAME = "channelName";//渠道方,UNIONPAY:银联 UMS:银联商务 ALIPAY:支付宝 WXPAY:微信
    public static final String DISCOUNT_ID = "discountId";//优惠流水ID,最大长度50位
    public static final String EVENT_NO = "eventNo";//活动编号,最大长度50位
    public static final String EVENT_DISCOUNT_AMOUNT = "eventDiscountAmount";//活动优惠金额,最大长度15位
    public static final String GOODS_LIST = "goodsList";//单品列表,数组-JSON,
    public static final String GOODS_ID= "goodsId";//商品编号，
    public static final String DISCOUNT_AMOUNT = "discountAmount";//单品优惠金额
    public static final String GOODS_NUMBER = "goodsNumber";//商品数量
    public static final String GOODS_PRICE = "goodsPrice";//商品价格
    public static final String ORIGINAL_TRANSACTION_TIME = "originalTransactionTime";//原始交易时间，格式:YYYYMMDDHHmmSS
    public static final String ORIGINAL_SETTLEMENT_DATE = "originalSettlementDate";//原始交易清算日期，格式:yyyyMMdd
    public static final String QUERY_RES_CODE = "queryResCode";//查询结果，0:成功 1:超时 2:已撤销 3:已退货 4:已冲正 5:失败(失败情况，后面追加失败描 FF:交易状态未知
    public static final String QUERY_RES_DESC = "queryResDesc";//查询结果描述，
    public static final String ORIGINAL_PAY_CODE = "originalPayCode";//原交易付款码
    public static final String ORIGINAL_BATCH_NO = "originalBatchNo";//原交易批次号
    public static final String ORIGINAL_SYSTEM_TRACE_NUM = "originalSystemTraceNum";//原交易流水号
    public static final String ORIGIAL_RETRIEVAL_REF_NUM = "origialRetrievalRefNum";//原检索参考号
    public static final String ORIGINAL_TRANSACTION_AMOUNT= "originalTransactionAmount";//原交易金额
    public static final String MERCHANT_ORDER_ID = "merchantOrderId";//商户订单号
    public static final String REFUNDED_AMOUNT = "refundedAmount";//已退货金额
    public static final String QUERY_RES_INFO = "actualTransactionAmount";//营销联盟优惠后交易金额
    public static final String PAY_CODE = "payCode";//付款码
    public static final String DEAL_DATE = "dealDate";//所查询交易交易日期
    public static final String DEAL_TIME = "dealTime";//所查询交易交易时间
    public static final String ORIGINAL_AMOUNT = "originalAmount";//原终端上送金额
    public static final String DEAL_TYPE = "dealType";//交易类型
    public static final String DEAL_SYSTEM_TRACE_NUM = "dealSystemTraceNum";//所查询交易流水
    public static final String DEAL_RETRIEVAL_REF_NUM = "dealRetrievalRefNum";//所查询交易系统参考号
    public static final String BATCH_NO = "batchNo";//批次号
    public static final String ORIGINAL_TRANSACTION_DATE = "originalTransactionDate";//原交易日期
    public static final String CARD_ATTR = "cardAttr";//卡类型，01：储蓄卡 03：信用卡 91：账户余额

    // v1 接口
    public static final String ERR_MSG = "errMsg";//错误说明
    public static final String JSPAY_REQUEST = "jsPayRequest";//JSAPI支付用的请求报文，带有签名信息
    public static final String MINIPAY_REQUEST = "miniPayRequest";//miniPayRequest支付用的请求报文，带有签名信息
    public static final String REDIRECTURL = "redirectUrl";//云闪付支付调整url
    public static final String TARGET_ORDER_ID = "targetOrderId";//第三方订单号
    public static final String STATUS = "status";//交易状态
    public static final String MER_ORDER_ID = "merOrderId";//商户订单号
    public static final String BUYER_ID = "buyerId";//买家id
    public static final String BUYER_USER_NAME = "buyerUserName";//第三方买家用户名
    public static final String PAY_TIME = "payTime";//支付时间，格式yyyy-MM-ddHH:mm:ss
    public static final String SUB_BUYER_ID = "subBuyerId";//子买家id
    public static final String INVOICE_AMOUNT = "invoiceAmount";//交易中可给用户开具发票的金额
    public static final String RECEIPT_AMOUNT = "receiptAmount";//商户实收金额，支付宝会有
    public static final String BANK_INFO = "bankInfo";//银行信息
    public static final String REFUND_STATUS = "refundStatus";//退款状态
    public static final String BILL_FUNDS = "billFunds";//支付信息
    public static final String PROMOTION_ID = "promotionId";// 优惠流水id
    public static final String PROMOTION_TYPE = "promotionType";// 优惠类型
    public static final String PROMOTION_AMOUNT = "eventPromotionAmt";// 活动优惠金额
    public static final String PLAT_PROMOTION_AMOUNT = "platPromotionAmt";// 平台优惠金额
    public static final String MCH_PROMOTION_AMOUNT = "mchntPromotionAmt";// 商户优惠金额
    public static final String THIRDPARTY_PROMOTION_AMOUNT = "thirdPartyPromotionAmt";// 第三方优惠金额
    public static final String ORI_PROMOTION_LIST = "oriPromotionList";//渠道和收单机构测原始优惠信息列表
    public static final String WXPAY_PROMOTION_DETAIL = "wxpayPromotionDetail";//渠道和收单机构测原始优惠信息列表
    public static final String TOTAL_AMOUNT = "totalAmount";//支付总金额
    public static final String REFUND_ORDER_ID = "refundOrderId";//联机退款订单号
    public static final String CHNL_INFO = "chnlInfo";//优惠信息
    public static final String CUP_PROMOTION_DETAIL = "cupPromotionDetail"; // 云闪付优惠
    public static final String CUP_PROMOTION_DETAIL_ID = "id"; // 出资方id
    public static final String CUP_PROMOTION_DETAIL_TYPE = "type"; // 出资方类型
    public static final String CUP_PROMOTION_DETAIL_SPNSR_ID = "spnsrId"; // 出资方
    public static final String CUP_PROMOTION_DETAIL_OFFST_AMT = "offstAmt"; // 金额

    public static final String ALIPAY_PROMOTION_DETAIL = "alipayPromotionDetail"; // 支付宝优惠
    public static final String ALIPAY_CHNL_LIST = "chnlList"; // 支付宝交易支付使用chnlList的资金渠道
    public static final String ORI_INFO = "oriInfo"; // 退款优惠信息
    public static final String REFUND_DETAIL_ITEM_LIST = "refundDetailItemList"; // 退款优惠详情
    public static final String REFUND_SEND_BACK_AMOUNT = "sendBackAmount";//商户实退金额，微信和支付宝会返回
    public static final String COUPON_MERCHANT_CONTRIBUTE ="couponMerchantContribute";


    // 免密支付
    public static final String CONTRACT_CODE = "contractCode"; // 签约协议号
    public static final String SEQ_ID = "seqId"; // 平台流水号
    public static final String RESPONSE_TIMESTAMP = "responseTimeStamp"; // 报文响应时间

    public static final String CQPMPAPPID = "cqpMpAppId";
    public static final String CQPMPPATH = "cqpMpPath";

}
