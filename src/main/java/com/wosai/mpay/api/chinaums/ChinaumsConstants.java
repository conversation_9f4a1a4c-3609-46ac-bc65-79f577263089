package com.wosai.mpay.api.chinaums;


import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> Date: 2019/4/26 Time: 10:25 AM
 */
public class ChinaumsConstants {

    public static final String CHARSET_UTF8 = "UTF-8";
    public static final String PATTERN = "yyyyMMddHHmmss";
    public static final String AUTH_HEAD = "OPEN-BODY-SIG";
    public static final String SPACE = " ";
    public static final String EQUAL = "=";
    public static final String COMMA = ",";
    public static final String DOUBLE_QUOTATION_MARKS = "\"";
    public static final String TRANSACTION_CURRENCY_CODE = "156"; //交易币种，固定为156
    public static final String DEVICE_TYPE_11 = "11"; //条码支付辅助受理终端

    /**系统返回码**/
    public static final String RESP_CODE_SUCCESS = "0000"; //响应成功
    public static final String RESP_CODE_AUTH_FAIL = "1000"; //认证失败
    public static final String RESP_CODE_EMPOWER_FAIL = "1000"; //授权失败
    public static final String RESP_CODE_PARAMS_VALID_FAIL = "9001"; //参数校验失败
    public static final String RESP_CODE_SYSTEM_ERROR = "9999"; //系统错误

    /**常见交易返回码**/
    public static final String RESP_CODE_BUSINESS_ACCEPTANCE_SUCCESS = "00"; //业务受理成功
    public static final String RESP_CODE_INVALID_MCH = "03"; //无效商户
    public static final String RESP_CODE_INVALID_AMOUNT = "13"; //无效金额
    public static final String RESP_CODE_ORI_TRADE_NOT_FOUND_22 = "22"; //找不到原始交易
    public static final String RESP_CODE_ORI_TRADE_NOT_FOUND_25 = "25"; //找不到原始交易
    public static final String RESP_CODE_ORI_TRADE_NOT_FOUND_G1 = "G1"; //找不到原始交易

    public static final String RESP_CODE_MESSAGE_FORMAT_ERR = "30"; //报文格式错误
    public static final String RESP_CODE_TRADE_FORBID = "57"; //不允许此交易
    public static final String RESP_CODE_OVER_AMOUNT_LIMIT = "61"; //超出金额限制
    public static final String RESP_CODE_ORI_AMOUNT_ERR = "64"; //原始金额错误
    public static final String RESP_CODE_CARD_ISSUERS_LINE_EXT = "92"; //发卡方线路异常
    public static final String RESP_CODE_DUPLICATE_TRADE = "94"; //重复交易
    public static final String RESP_CODE_SWITCHING_CENTRE_EXT = "96"; //交换中心异常
    public static final String RESP_CODE_TERMINAL_NO_NOT_REGISTER = "97"; //终端号未登记
    public static final String RESP_CODE_SAFE_DISPOSAL_FAIL = "A7"; //安全处理失败
    public static final String RESP_CODE_ER = "ER"; //参见具体返回信息
    public static final String RESP_CODE_TRADE_NOT_FOUNT = "FF"; //查不到交易信息

    /**交易状态查询状态码**/
    public static final String PAY_QUERY_RES_CODE_SUCCESS = "0";
    public static final String PAY_QUERY_RES_CODE_TIMEOUT = "1";
    public static final String PAY_QUERY_RES_CODE_CANCELED = "2";
    public static final String PAY_QUERY_RES_CODE_REFUNDED = "3";
    public static final String PAY_QUERY_RES_CODE_REVERSED = "4";
    public static final String PAY_QUERY_RES_CODE_FAIL = "5";
    public static final String PAY_QUERY_RES_CODE_UNKNOWN = "FF";

    /**退款状态查询状态码**/
    public static final String REFUND_QUERY_RES_CODE_SUCCESS = "00";

    public static final String RESP_MSG_CANCELED = "10000交易已撤销";
    public static final String RESP_MSG_NOT_SUPPORT_CANCEL = "渠道暂不支持撤销,请发起退款交易";
    public static final String RESP_MSG_ORDER_NOT_EXISTS = "原交易不存在";

    public static final String QRCODE_TYPE_DYNAMIC = "0"; //二维码类型 动态码
    public static final String QRCODE_TYPE_STATIC = "1"; //二维码类型 静态码

    public static final Set<String> PAY_RESP_CODE_FAIL_SET = new HashSet<String>(){{
        List<String> list = Arrays.asList(RESP_CODE_INVALID_MCH, RESP_CODE_INVALID_AMOUNT, RESP_CODE_MESSAGE_FORMAT_ERR,
                RESP_CODE_TRADE_FORBID, RESP_CODE_OVER_AMOUNT_LIMIT, RESP_CODE_CARD_ISSUERS_LINE_EXT,
                RESP_CODE_DUPLICATE_TRADE, RESP_CODE_TERMINAL_NO_NOT_REGISTER, RESP_CODE_SAFE_DISPOSAL_FAIL);
        addAll(list);
    }};
    public static final Set<String> PAY_RESP_CODE_UNKNOWN_SET = new HashSet<String>(){{
        List<String> list = Arrays.asList(RESP_CODE_DUPLICATE_TRADE, RESP_CODE_ER, RESP_CODE_SWITCHING_CENTRE_EXT);
        addAll(list);
    }};

    public static final Set<String> REFUND_RESP_CODE_FAIL_SET = new HashSet<String>(){{
        List<String> list = Arrays.asList(RESP_CODE_INVALID_MCH, RESP_CODE_INVALID_MCH,
                RESP_CODE_ORI_TRADE_NOT_FOUND_22, RESP_CODE_ORI_TRADE_NOT_FOUND_25,
                RESP_CODE_ORI_TRADE_NOT_FOUND_G1, RESP_CODE_MESSAGE_FORMAT_ERR,
                RESP_CODE_TRADE_FORBID, RESP_CODE_OVER_AMOUNT_LIMIT, RESP_CODE_ORI_AMOUNT_ERR,
                RESP_CODE_CARD_ISSUERS_LINE_EXT, RESP_CODE_TERMINAL_NO_NOT_REGISTER,
                RESP_CODE_SAFE_DISPOSAL_FAIL, RESP_CODE_TRADE_NOT_FOUNT);
        addAll(list);
    }};
    public static final Set<String> REFUND_RESP_CODE_UNKNOWN_SET = new HashSet<String>(){{
        List<String> list = Arrays.asList(RESP_CODE_SWITCHING_CENTRE_EXT, RESP_CODE_DUPLICATE_TRADE, RESP_CODE_ER);
        addAll(list);
    }};

    public static final Set<String> CANCEL_RESP_CODE_FAIL_SET = new HashSet<String>(){{
        List<String> list = Arrays.asList(RESP_CODE_INVALID_MCH, RESP_CODE_MESSAGE_FORMAT_ERR,
                RESP_CODE_MESSAGE_FORMAT_ERR, RESP_CODE_TRADE_FORBID, RESP_CODE_CARD_ISSUERS_LINE_EXT,
                RESP_CODE_TERMINAL_NO_NOT_REGISTER, RESP_CODE_SAFE_DISPOSAL_FAIL, RESP_CODE_TRADE_NOT_FOUNT);
        addAll(list);
    }};
    public static final Set<String> CANCEL_RESP_CODE_UNKNOWN_SET = new HashSet<String>(){{
        List<String> list = Arrays.asList(RESP_CODE_ORI_TRADE_NOT_FOUND_22,
                RESP_CODE_ER);
        addAll(list);
    }};
    public static final Set<String> CANCEL_RESP_CODE_RETRY_SET = new HashSet<String>(){{
        List<String> list = Arrays.asList(RESP_CODE_CARD_ISSUERS_LINE_EXT, RESP_CODE_SWITCHING_CENTRE_EXT);
        addAll(list);
    }};

    public enum PayModeEnum {

        E_CASH("电子现金"),
        SOUNDWAVE("声波"),
        NFC("NFC"),
        CODE_SCAN("扫码"),
        MANUAL("手输"),

        ;

        private String desc;

        public String getDesc() {
            return desc;
        }

        PayModeEnum(String desc) {
            this.desc = desc;
        }

    }

    public enum CardAttrEnum {

        DEBIT_CARD("01", "储蓄卡"),
        CREDIT_CARD("03", "信用卡"),
        WALLET_BALANCE("91", "账户余额"),

        ;

        private String code;

        private String desc;

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        CardAttrEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        /**
         * 通过code查询枚举
         *
         * @param code
         * @return
         */
        public static CardAttrEnum queryByCode(String code) {
            if (code == null || code.length() == 0) {
                return null;
            }
            CardAttrEnum[] cardAttrEnums = CardAttrEnum.values();
            for (CardAttrEnum cardAttrEnum : cardAttrEnums) {
                if (cardAttrEnum.getCode().equals(code)) {
                    return cardAttrEnum;
                }
            }
            return null;
        }
    }


    // v1 版本
    public static final String YUEDAN_DEFAULT = "YUEDANDEFAULT";
    public static final String MINI_DEFAULT = "MINIDEFAULT";
    public static final String TRADE_TYPE_JSAPI = "JSAPI";
    public static final String TRADE_TYPE_MINI = "MINI";
    public static final String TRADE_TYPE_UP_WX_MINI = "UP_WX_MINI";
    public static final String RESP_CODE_V1_SUCCESS = "SUCCESS";
    public static final String RESP_CODE_V1_NO_ORDER = "NO_ORDER";
    public static final String RESP_CODE_V1_NO_CROSS_DAY_TRADING = "NO_CROSS_DAY_TRADING";
    public static final String RESP_CODE_V1_DUP_ORDER = "DUP_ORDER"; //支付请求的merOrderId重复，请检查终端是否做过复位操作，导致流水号等重复。
    public static final String RESP_CODE_V1_TARGET_PROCESSING="TARGET_PROCESSING";

    public static final String ERR_MSG_TRADE_HAS_REFUND = "10000交易已退货";
    public static final String BIZ_IDENTIFIER_YRY = "YRY"; // 悦融益开放平台接入
    public static final String INVOKE_SCENE_03 ="03";  //交易发起场景 使用云闪付云微小程序支付时必传，如果交易从商户微信小程序内发起需要上送03


    /**交易状态查询状态码**/
    public static final String STATUS_TRADE_SUCCESS = "TRADE_SUCCESS"; // 支付成功
    public static final String STATUS_NEW_ORDER = "NEW_ORDER"; // 新订单
    public static final String STATUS_UNKNOWN = "UNKNOWN"; // 不明确的交易状态
    public static final String STATUS_TRADE_CLOSED = "TRADE_CLOSED"; // 支付失败
    public static final String STATUS_WAIT_BUYER_PAY = "WAIT_BUYER_PAY"; // 等待买家付款
    public static final String STATUS_TRADE_REFUND = "TRADE_REFUND"; // 订单转入退货流程

    /**退款状态码**/
    public static final String REFUND_STATUS_SUCCESS = "SUCCESS"; // 退款成功
    public static final String REFUND_STATUS_UNKNOWN = "UNKNOWN"; // 不明确的退款状态
    public static final String REFUND_STATUS_FAIL = "FAIL"; // 退款失败
    public static final String REFUND_STATUS_PROCESSING = "PROCESSING"; // 退款处理中
    public static final Set<String> RESP_CODE_V1_CANCEL_RETRY_SET = new HashSet<String>(){{
        List<String> list = Arrays.asList("NET_ERROR", "INTERNAL_ERROR");
        addAll(list);
    }};

    public static final Set<String> RESP_CODE_V1_PAY_FAIL_SET = new HashSet<String>(){{
        List<String> list = Arrays.asList("BAD_REQUEST", "OPERATION_NOT_ALLOWED", "NO_MERCHANT", "INACTIVE_MERCHANT", "ABNORMAL_REQUEST_TIME", "TARGET_FAIL");
        addAll(list);
    }};

}
