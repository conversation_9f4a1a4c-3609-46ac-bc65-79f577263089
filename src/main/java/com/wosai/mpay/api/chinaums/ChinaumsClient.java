package com.wosai.mpay.api.chinaums;

import com.google.common.collect.Maps;
import com.google.common.net.HttpHeaders;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.ChinaumsSignature;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.Map;
import java.util.Random;

/**
 * <AUTHOR> Date: 2019/4/25 Time: 5:43 PM
 */
public class ChinaumsClient {
    public static final Logger logger = LoggerFactory.getLogger(ChinaumsClient.class);

    private static final String CONTENT_TYPE = "application/json";

    private static final SafeSimpleDateFormat dateFormat = new SafeSimpleDateFormat(ChinaumsConstants.PATTERN);


    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public ChinaumsClient() {
    }

    public ChinaumsClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }



    public Map<String,Object> call(String gateway, String appId, String appKey, Map<String,Object> request) throws MpayException, MpayApiNetworkError {
        return this.call(gateway, appId, appKey, request, readTimeout);

    }

    public Map<String,Object> call(String gateway, String appId, String appKey, Map<String,Object> request, int readTimeout) throws MpayException, MpayApiNetworkError {

        String timestamp = buildTimestamp();
        String nonce = buildNonce();

        String requestStr = JsonUtil.objectToJsonString(request);
        String signature = ChinaumsSignature.sign(requestStr, appId, appKey, timestamp, nonce);
        String authorization = buildAuthorization(signature, appId, timestamp, nonce);

        Map<String, String> headers = buildHttpHeader(authorization);

        logger.debug("request {}", requestStr);
        String responseStr = HttpClientUtils.doPost(ChinaumsClient.class.getName(), null, null, gateway, CONTENT_TYPE
                ,requestStr, headers, ChinaumsConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        logger.debug("response {}", responseStr);
        try{
            Map<String,Object> result = JsonUtil.jsonStringToObject(responseStr, Map.class);
            return result;
        }catch (Exception e){
            throw new MpayApiUnknownResponse("parse response error: " + e.getMessage(), e);
        }


    }

    private String buildAuthorization(String signature, String appId, String timestamp, String nonce) {
        StringBuilder authorizationTemp = new StringBuilder(256);
        authorizationTemp
                .append(ChinaumsConstants.AUTH_HEAD)
                .append(ChinaumsConstants.SPACE)
                .append(ProtocolFields.APP_ID).append(ChinaumsConstants.EQUAL)
                .append(ChinaumsConstants.DOUBLE_QUOTATION_MARKS).append(appId).append(ChinaumsConstants.DOUBLE_QUOTATION_MARKS)
                .append(ChinaumsConstants.COMMA)
                .append(ChinaumsConstants.SPACE)
                .append(ProtocolFields.TIMESTAMP).append(ChinaumsConstants.EQUAL)
                .append(ChinaumsConstants.DOUBLE_QUOTATION_MARKS).append(timestamp).append(ChinaumsConstants.DOUBLE_QUOTATION_MARKS)
                .append(ChinaumsConstants.COMMA)
                .append(ChinaumsConstants.SPACE)
                .append(ProtocolFields.NONCE).append(ChinaumsConstants.EQUAL)
                .append(ChinaumsConstants.DOUBLE_QUOTATION_MARKS).append(nonce).append(ChinaumsConstants.DOUBLE_QUOTATION_MARKS)
                .append(ChinaumsConstants.COMMA)
                .append(ChinaumsConstants.SPACE)
                .append(ProtocolFields.SIGNATURE).append(ChinaumsConstants.EQUAL)
                .append(ChinaumsConstants.DOUBLE_QUOTATION_MARKS).append(signature).append(ChinaumsConstants.DOUBLE_QUOTATION_MARKS);

        return authorizationTemp.toString();
    }

    private Map<String, String> buildHttpHeader(String authorization) {
        Map<String, String> headers = Maps.newHashMapWithExpectedSize(4);
        headers.put(ProtocolFields.AUTHORIZATION, authorization);

        return headers;
    }

    private String buildNonce() {
        Random random = new Random();
        return String.valueOf(random.nextLong());
    }

    private String buildTimestamp() {
        return dateFormat.format(new Date());
    }

}
