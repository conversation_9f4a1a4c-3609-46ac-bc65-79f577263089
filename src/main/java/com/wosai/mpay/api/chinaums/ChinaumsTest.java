package com.wosai.mpay.api.chinaums;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Date: 2019/5/5 Time: 1:54 PM
 */
public class ChinaumsTest {


    private static Map<String, Object> payTest() throws MpayException, MpayApiNetworkError {
        Map<String, Object> map = new HashMap();
        map.put(ProtocolFields.MERCHANT_CODE, "123456789900081");
        map.put(ProtocolFields.TERMINAL_CODE, "00810001");
        map.put(BusinessFields.TRANSACTION_AMOUNT, 1);
        map.put(BusinessFields.TRANSACTION_CURRENCY_CODE, ChinaumsConstants.TRANSACTION_CURRENCY_CODE);
        map.put(BusinessFields.MERCHANT_ORDER_ID, "20190501414320000000008");
        map.put(BusinessFields.MERCHANT_REMARK, "测试商品");
        map.put(BusinessFields.PAY_MODE, ChinaumsConstants.PayModeEnum.CODE_SCAN);
        map.put(BusinessFields.PAY_CODE, "6225340812174977590");


        String url = "http://58.247.0.18:29015/v2/poslink/transaction/pay";
        String appId = "f0ec96ad2c3848b5b810e7aadf369e2f";
        String appKey = "";

        System.out.println(JsonUtil.objectToJsonString(map));

        return new ChinaumsClient().call(url, appId, appKey, map);
    }

    private static Map<String, Object> cancelTest() throws MpayException, MpayApiNetworkError {
        Map<String, Object> map = new HashMap();
        map.put(ProtocolFields.MERCHANT_CODE, "123456789900081");
        map.put(ProtocolFields.TERMINAL_CODE, "00810001");
        map.put(BusinessFields.MERCHANT_ORDER_ID, "20190501414320000000008");

        String url = "http://58.247.0.18:29015/v2/poslink/transaction/voidpayment";
        String appId = "f0ec96ad2c3848b5b810e7aadf369e2f";
        String appKey = "";

        System.out.println(JsonUtil.objectToJsonString(map));

        return new ChinaumsClient().call(url, appId, appKey, map);
    }

    private static Map<String, Object> refundTest() throws MpayException, MpayApiNetworkError {
        Map<String, Object> map = new HashMap();
        map.put(ProtocolFields.MERCHANT_CODE, "123456789900081");
        map.put(ProtocolFields.TERMINAL_CODE, "00810001");
//        map.put(BusinessFields.ORIGINAL_ORDER_ID, "20190507115836000019178215");
        map.put(BusinessFields.MERCHANT_ORDER_ID, "20190501414320000000008");
        map.put(BusinessFields.REFUND_REQUEST_ID, System.currentTimeMillis() + "");
        map.put(BusinessFields.TRANSACTION_AMOUNT, 1);

        String url = "http://58.247.0.18:29015/v2/poslink/transaction/refund";
        String appId = "f0ec96ad2c3848b5b810e7aadf369e2f";
        String appKey = "";

        System.out.println(JsonUtil.objectToJsonString(map));

        return new ChinaumsClient().call(url, appId, appKey, map);
    }

    private static Map<String, Object> payQueryTest() throws MpayException, MpayApiNetworkError {
        Map<String, Object> map = new HashMap();
        map.put(ProtocolFields.MERCHANT_CODE, "123456789900081");
        map.put(ProtocolFields.TERMINAL_CODE, "00810001");
        map.put(BusinessFields.MERCHANT_ORDER_ID, "201905014143200000000041");

        String url = "http://58.247.0.18:29015/v2/poslink/transaction/query";
        String appId = "f0ec96ad2c3848b5b810e7aadf369e2f";
        String appKey = "";

        System.out.println(JsonUtil.objectToJsonString(map));

        return new ChinaumsClient().call(url, appId, appKey, map);
    }

    private static Map<String, Object> refundQueryTest() throws MpayException, MpayApiNetworkError {
        Map<String, Object> map = new HashMap();
        map.put(ProtocolFields.MERCHANT_CODE, "123456789900081");
        map.put(ProtocolFields.TERMINAL_CODE, "00810001");
        map.put(BusinessFields.MERCHANT_ORDER_ID, "1557798119554");
        map.put(BusinessFields.REFUND_REQUEST_ID, "1557798156775");

        String url = "http://58.247.0.18:29015/v2/poslink/transaction/query-refund";
        String appId = "f0ec96ad2c3848b5b810e7aadf369e2f";
        String appKey = "";

        System.out.println(JsonUtil.objectToJsonString(map));

        return new ChinaumsClient().call(url, appId, appKey, map);
    }

    public static void main(String[] args) {
//        String content = "11223";
//        String appId = "f0ec96ad2c3848b5b810e7aadf369e2f";
//        String appKey = "775481e2556e4564985f5439a5e6a277";
//        String timestamp = String.valueOf(System.currentTimeMillis()) + "112";
//        String nonce = String.valueOf(ThreadLocalRandom.current().nextLong(1222222222,10000000000L));
//        String signature = ChinaumsSignature.sign(content, appId, appKey, timestamp, nonce);
//        System.out.println(new ChinaumsClient().buildAuthorization(signature, appId, timestamp, nonce));

        try {
//            String orderId = String.valueOf(System.currentTimeMillis());
            System.out.println(JsonUtil.objectToJsonString(payTest()));
//            System.out.println(JsonUtil.objectToJsonString(payQueryTest()));
//            System.out.println(JsonUtil.objectToJsonString(cancelTest()));
//            System.out.println(JsonUtil.objectToJsonString(refundTest()));
//            System.out.println(JsonUtil.objectToJsonString(refundQueryTest()));
        } catch (MpayException e) {
            e.printStackTrace();
        } catch (MpayApiNetworkError mpayApiNetworkError) {
            mpayApiNetworkError.printStackTrace();
        }
    }
}
