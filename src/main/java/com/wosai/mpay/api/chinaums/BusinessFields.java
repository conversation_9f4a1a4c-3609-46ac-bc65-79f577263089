package com.wosai.mpay.api.chinaums;

/**
 * <AUTHOR> Date: 2019/4/25 Time: 3:40 PM
 */
public class BusinessFields {

    public static final String TRANSACTION_AMOUNT = "transactionAmount";//交易金额，单位:分，数字型
    public static final String TRANSACTION_CURRENCY_CODE = "transactionCurrencyCode";//交易币种，固定为156
    public static final String MERCHANT_ORDER_ID = "merchantOrderId";//商户订单号，全局唯一，不可重复，长度不超过50位
    public static final String MERCHANT_REMARK = "merchantRemark";//商户备注，长度不超过30位
    public static final String PAY_MODE = "payMode";//支付方式，E_CASH–电子现金 SOUNDWAVE–声波 NFC–NFC CODE_SCAN–扫码 MANUAL–手输
    public static final String PAY_CODE = "payCode";//支付码
    public static final String GOODS = "goods";//商品信息，数组-JSON
    public static final String GOODS_ID = "goodsId";//商品ID
    public static final String GOODS_NAME = "goodsName";//商品名称
    public static final String QUANTITY = "quantity";//商品数量
    public static final String PRICE = "price";//商品单价
    public static final String GOODS_CATEGORY = "goodsCategory";//商品单价
    public static final String BODY = "body";//商品说明
    public static final String DISCOUNT = "discount";//商品折扣
    public static final String SRC_RESERVED = "srcReserved";//商户冗余信息，用于多种用途，如在特定支付渠道下，冗余信息将被渠道利用，长度不超过255位
    public static final String STORE_ID = "storeId";//门店号
    public static final String LIMIT_CREDIT_CARD = "limitCreditCard";//是否限制信用卡
    public static final String OPERATOR_ID = "operatorId";//操作员编号
    public static final String BIZ_IDENTIFIER = "bizIdentifier";//业务标识，标识接入的具体业务除非特殊说明，一般不需要上送
    public static final String GOODS_TAG = "goodsTag";//商品标识
    public static final String ORIGINAL_ORDER_ID = "originalOrderId";//银商订单号，必须与原支付交易返回的订单号一致
    public static final String LIMIT_CROSS_DEVICE = "limitCrossDevice";//是否限制跨终端，true:不可以跨终端 false:可以跨 终端
    public static final String REFUND_REQUEST_ID = "refundRequestId";//退款请求标识，标识一次退款请求，同一笔订单多次退款需要保证唯一，长度不超过50位
    
    // v1接口参数
    public static final String REQUEST_TIMESTAMP = "requestTimestamp";//报文请求时间，格式yyyy-MM-dd HH:mm:ss
    public static final String EXT_ORDER_ID = "extOrderId";//商户订单号
    public static final String INST_MID = "instMid";
    public static final String ORIGINAL_AMOUNT = "originalAmount";// 订单原始金额，单位分，用于记录前端系统打折前的金额
    public static final String TOTAL_AMOUNT = "totalAmount";// 支付总金额，单位分，用于记录前端系统打折前的金额
    public static final String SUB_OPEN_ID = "subOpenId";// 用户子标识，商户自己公众号appid下的用户openid，可以通过微信oauth接口获取。
    public static final String SUB_APP_ID = "subAppId";// 微信子商户appId
    public static final String INSTALLMENT_NUMBER = "installmentNumber";// 花呗分期数，仅支持3、6、12
    public static final String FEE_RATIO = "feeRatio";// 手续费比例
    public static final String TRADE_TYPE = "tradeType";// 交易类型
    public static final String USER_ID = "userId";// 支付宝或云闪付用户标识
    public static final String REFUND_AMOUNT = "refundAmount";// 退款金额
    public static final String REFUND_EXT_ORDERID = "refundExtOrderId";// 退款单号
    public static final String ORDER_DESC = "orderDesc";// 订单描述
    public static final String BARCODE = "barCode";// 支付码
    public static final String RET_COMM_PARAMS = "retCommParams";// 返佣字段
    public static final String FOOD_ORDER_TYPE = "foodOrderType";// 扫码点餐字段
    public static final String NOTIFY_URL = "notifyUrl";// 支付结果通知地址
    public static final String EXPIRE_TIME = "expireTime";// 订单过期时间，为空则使用系统默认过期时间（30分钟），格式yyyy-MM-ddHH:mm:ss
    public static final String COST_SUBSIDY = "costSubsidy";// 确认成本补贴
    public static final String DEVICE_TYPE = "deviceType";// 设备类型
    public static final String LONGITUDE = "longitude";// 经度
    public static final String LATITUDE = "latitude";// 维度
    public static final String SERIAL_NUM = "serialNum";// 序列号
    public static final String IP = "ip";// 终端设备IP地址
    public static final String THIRD_PARTY_GOODS_NAME = "thirdPartyGoodsName"; // 商品名称
    public static final String INVOKE_SCENE="invokeScene";

    // 免密支付
    public static final String CONTRACT_ID = "contractId"; //银联免密支付签约协议号
    public static final String MER_ORDER_ID = "merOrderId"; //商户订单号
    public static final String REFUND_ORDER_ID = "refundOrderId"; //退款单号


    public static final String QRCODE_TYPE = "qrCodeType";// 二维码类型

    public static final String QRCODE = "qrCode"; // 二维码

    public static final String CODE = "code"; // 用户授权码

}
