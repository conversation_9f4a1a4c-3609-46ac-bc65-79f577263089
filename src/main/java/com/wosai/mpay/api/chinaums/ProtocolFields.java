package com.wosai.mpay.api.chinaums;


/**
 * <AUTHOR> Date: 2019/4/25 Time: 4:03 PM
 */
public class ProtocolFields {
    // v2版本接口
    public static final String MERCHANT_CODE = "merchantCode";//商户号
    public static final String TERMINAL_CODE = "terminalCode";//终端号
    public static final String APP_ID = "AppId";//产品ID，由银联商务方提供
    public static final String APP_KEY = "AppKey";//产品密钥，由银联商务方提供
    public static final String TIMESTAMP = "Timestamp";//时间戳，格式yyyyMMddHHmmss
    public static final String NONCE = "Nonce";//随机数，长度<=128
    public static final String SIGNATURE = "Signature";//签名，Base64_Encode(HmacSHA256(AppId + timestamp + nonce + SHA256(报文体), AppKey))
    public static final String AUTHORIZATION = "Authorization";//认证内容
    
    // v1版本接口
    public static final String MID = "mid";//商户号
    public static final String TID = "tid";//终端号

}
