package com.wosai.mpay.api.psbc;

public class PSBCRequestFields {

    /**
     * 请求报文
     * <p>
     * 表达加密后的请求报文，详见报文加解密规则。
     */
    public static final String REQUEST = "request";

    /**
     * 数字签名
     * <p>
     * 表达签名，详见签名规则，长度为256。
     */
    public static final String SIGNATURE = "signature";

    /**
     * 访问令牌
     * <p>
     * 请求方的访问令牌，长度为1000。
     */
    public static final String ACCESS_TOKEN = "accessToken";

    /**
     * 报文加密密钥
     * <p>
     * 使用sm2加密后的sm4加密密钥，详见报文加解密规则，长度为200。
     */
    public static final String ENCRYPT_KEY = "encryptKey";


    /**
     * 合作方交易流水号
     * <p>
     * 交易唯一标识，规则为日期+时间+10位数字，格式为YYYYMMDDHHMMSS+10位数字。
     */
    public static final String PARTNER_TX_SRI_NO = "partnerTxSriNo";

    /**
     * 接口代码
     * <p>
     * 详见具体接口名，长度为32。
     */
    public static final String METHOD = "method";

    /**
     * 接口版本
     * <p>
     * 默认为1，长度为8。
     */
    public static final String VERSION = "version";

    /**
     * 合作方编号
     * <p>
     * 服开门户上注册后获取的合作方编号，长度为32。
     */
    public static final String MERCHANT_ID = "merchantId";

    /**
     * 伙伴应用id
     * <p>
     * 门户首页分配给商户的APP_ID，长度为22。
     */
    public static final String APP_ID = "appID";

    /**
     * 报文发起时间
     * <p>
     * 格式为yyyyMMddHHmmss，长度为14。
     */
    public static final String REQ_TIME = "reqTime";

    /**
     * 接入方式
     * <p>
     * API/H5，长度为5。
     */
    public static final String ACCESS_TYPE = "accessType";

    /**
     * 保留字段
     * <p>
     * KV方式，长度为256，用于扩展。
     */
    public static final String RESERVE = "reserve";


    /**
     * 业务主ID
     * <p>
     * 用于接口串联使用，规则同合作方交易流水号，长度为24。
     */
    public static final String BUSI_MAIN_ID = "busiMainId";

    /**
     * 交易时间
     * <p>
     * 格式为 yyyyMMddHHmmss，长度为14。
     */
    public static final String REQ_TRANS_TIME = "reqTransTime";

    /**
     * 业务数据
     * <p>
     * 业务数据，格式为 JSON，必输字段。
     */
    public static final String DATA = "data";

    /**
     * 收单公共请求响应报文
     */

    /**
     * 统一收单商户号
     * <p>
     * 当单商户订单时，填写实际发生交易的商户的商户号；当多商户合并支付分账订单时，填写平台或者合作机构在统一收单系统开设的具有收款功能的商户的商户号，长度为15。
     */
    public static final String MCHT_NO = "mchtNo";

    /**
     * 交易金额
     * <p>
     * 必须包含两位小数，长度为14。
     */
    public static final String TXN_AMT = "txnAMT";

    /**
     * 交易币种
     * <p>
     * 值为 156（人民币），长度为3。
     */
    public static final String CURRENCY_CODE = "currencyCode";

    /**
     * 二维码
     * <p>
     * 二维码值，长度为300。
     */
    public static final String QR_CODE = "qrCode";

    /**
     * 订单信息
     * <p>
     * 采用JSON格式，描述订单是单商户订单或者多商户合并支付分账订单。具体见 7.2 数据元说明 - 7.2.9 orderData。
     */
    public static final String ORDER_DATA = "orderData";

    /**
     * 交易通知地址
     * <p>
     * 当 whetherNotify 取值为 00 时，必须传递，长度为200，可选字段。
     */
    public static final String BACK_URL = "backUrl";

    /**
     * 终端号
     * <p>
     * 当有终端号时，必须提供，长度为32，可选字段。
     */
    public static final String TERM_ID = "termID";

    /**
     * 终端信息
     * <p>
     * JSON字符串，当为银联交易下单时必须上送经纬度，必输字段。
     */
    public static final String TERM_INFO = "termInfo";

    /**
     * 信用卡交易标识
     * <p>
     * 商户是否允许信用卡交易，长度为1，可选字段。
     */
    public static final String IS_CREDIT = "isCredit";

    /**
     * 是否需要消息结果通知
     * 00-需要；01-不需要；
     */
    public static final String WHETHER_NOTIFY = "whetherNotify";


    /**
     * 微信分配的子商户公众号ID
     */

    public static final String SUB_APP_ID = "subAppIDWX";


    /**
     * 微信侧用户子标识，对应
     * sub_openid
     */
    public static final String SUB_OPEN_ID_WX = "subOpenIDWX";


    /**
     * 买家的支
     * 付宝唯一
     * 用户号
     */

    public static final String BUYE_ID = "buyeId";


    /**
     * 订单标题
     */
    public static final String SUBJECT = "subject";

    /**
     * 订单描述
     */
    public static final String BODY = "body";


    /**
     * 退货请求时上送的流水
     * 号
     */

    public static final String ORG_REQ_TRACE_ID = "orgReqTraceID";


    /**
     * 原请求方交易时间
     */
    public static final String ORG_REQ_DATE = "orgReqDate";


    /**
     * 0-邮储收单订单号查询；
     * 1-商户订单号查询；如果为空，默认按商户订单号查询
     */
    public static final String QUERY_FLAG = "queryFlag";

    /**
     * 交易码
     */
    public static final String TXN_CODE = "txnCode";

    /**
     * 渠道ID
     */
    public static final String CHANNEL_ID = "channelID";

    /**
     * 平台编号
     */
    public static final String PLATFORM_ID = "platformID";

    /**
     * 请求方交易流水号
     */
    public static final String REQ_TRACE_ID = "reqTraceID";

    /**
     * 请求方交易时间
     */
    public static final String REQ_DATE = "reqDate";


    /**
     * 请求方自定义字段
     */
    public static final String REQ_RESERVED = "reqReserved";
    public static final String ORDER_FLAG = "orderFlag";
    public static final String ORDER_TITLE = "orderTitle";
    public static final String ORDER_AMT = "orderAMT";

    public static final String DEVICE_TYPE = "deviceType";

    public static final String DEVICE_ID = "deviceId";

    /**
     * 微信侧商品描述信息
     */
    public static final String BODY_WX = "bodyWX";


    public static final String TRADE_TYPE_WX = "tradeTypeWX";

    //实名支付
    public static final String IDENTITY = "identity";

}
