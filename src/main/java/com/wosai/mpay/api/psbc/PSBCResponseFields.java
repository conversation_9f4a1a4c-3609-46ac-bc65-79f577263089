package com.wosai.mpay.api.psbc;

public class PSBCResponseFields {

    /**
     * 响应报文
     * <p>
     * 表达加密后的响应报文，详见报文加解密规则。
     */
    public static final String RESPONSE = "response";

    /**
     * 数字签名
     * <p>
     * 表达签名，详见签名规则，长度为256。
     */
    public static final String SIGNATURE = "signature";

    /**
     * 访问令牌
     * <p>
     * 请求方的访问令牌，长度为1000。
     */
    public static final String ACCESS_TOKEN = "accessToken";

    /**
     * 报文加密密钥
     * <p>
     * 使用sm2加密后的sm4加密密钥，详见报文加解密规则，长度为200。
     */
    public static final String ENCRYPT_KEY = "encryptKey";


    /**
     * 合作方交易流水号
     * <p>
     * 交易唯一标识，长度为24。
     */
    public static final String PARTNER_TX_SRI_NO = "partnerTxSriNo";

    /**
     * 接口代码
     * <p>
     * 详见具体接口名，长度为32。
     */
    public static final String METHOD = "method";

    /**
     * 接口版本
     * <p>
     * 默认为1，长度为8。
     */
    public static final String VERSION = "version";

    /**
     * 合作方编号
     * <p>
     * 邮储分配给合作方的编号，长度为32。
     */
    public static final String MERCHANT_ID = "merchantId";

    /**
     * 伙伴应用id
     * <p>
     * 邮储分配给合作方的应用系统编号，长度为22。
     */
    public static final String APP_ID = "appId";

    /**
     * 报文响应时间
     * <p>
     * 格式为yyyyMMddHHmmss，长度为14。
     */
    public static final String RESP_TIME = "respTime";

    /**
     * 预留字段
     * <p>
     * KV方式，长度为256，用于扩展。
     */
    public static final String RESERVE = "reserve";

    /**
     * 响应码
     * 000000-交易成功
     */

    public static final String RESP_CODE = "respCode";


    /**
     * 响应信息
     */
    public static final String RESP_MSG = "respMsg";

    /**
     * 收单响应码
     */
    public static final String RESP_CD = "respCd";

    /**
     * 收单响应描述
     */
    public static final String RESP_DESC = "respDesc";

    /**
     * 统一收单商户号
     */
    public static final String MCHT_NO = "mchtNo";
    /**
     * 统一收单系统订单号
     */
    public static final String ORDER_NO = "orderNo";


    /**
     * 统一收单系统订单时间
     */
    public static final String ORDER_TIME = "orderTime";


    /**
     * 交易金额
     */
    public static final String TXN_AMT = "txnAMT";


    /**
     * 交易币种
     */

    public static final String CURRENCY_CODE = "currencyCode";

    /**
     * 中文名称: 统一收单系统记账日期
     */
    public static final String SETT_DATE = "settDate";


    /**
     * 订单应答数据
     */
    public static final String RSP_ORDER_DATA = "rspOrderData";

    /**
     * 支付系统类型
     */
    public static final String EXT_PAY_TYPE = "extPayType";

    /**
     * 银联二维码通道支付信息
     */
    public static final String PAYER_INFO_CUPS = "payerInfoCUPS";


    /**
     * 交易类型
     */
    public static final String TRADE_TYPE_WX = "tradeTypeWX";


    /**
     * 微信公众号支付信息
     */
    public static final String WC_PAY_DATA = "wcPayData";


    /**
     * 支付宝支付信息
     */

    public static final String ALI_PAY_DATA = "aliPayData";


    /**
     * 退款标识
     */
    public static final String REFUND_FLAG = "refundFlag";


    /**
     * 原支付订单
     * 号
     */
    public static final String ORG_ORDER_NO = "orgOrderNo";


    /**
     * 原交易金额
     */
    public static final String ORG_TXN_AMT = "orgtxnAMT";


    /**
     * 退款状态
     */

    public static final String TXN_STA = "txnSta";


    /**
     * 订单关闭结果
     */
    public static final String CLOSE_STA = "closeSta";


    /**
     * 交易标识
     */
    public static final String TXN_FLAG = "txnFlag";


    /**
     * 支付宝通道支付信息
     */
    public static final String PAYER_INFO_ALIPAY = "payerInfoAlipay";


    /**
     * 微信通道支付信息
     */
    public static final String PAYER_INFO_WECHAT = "payerInfoWechat";


    /**
     * 抵扣信息
     */
    public static final String DEDUCTION_INFO = "deductionInfo";


    // 以下为支付宝支付信息字段
    /**
     * 买家支付宝账号
     */
    public static final String BUYER_LOGON_ID = "buyer_logon_id";


    /**
     * 买家的支付宝用户Uid
     */
    public static final String BUYER_ID = "buyer_id";


    /**
     * 用户在交易中支付的可开具发票的金额
     */
    public static final String INVOICE_AMOUNT = "invoiceAmount";


    /**
     * 商家在交易中实际收到的款项,单位为元
     */
    public static final String RECEIPT_AMOUNT = "receipt_amount";


    /**
     * 使用积分报支付的金额
     */
    public static final String POINT_AMOUNT = "point_amount";


    /**
     * 用户在交易中支付的金额
     */
    public static final String BUYER_PAY_AMOUNT = "buyer_pay_amount";


    /**
     * 交易支付时间 2014-11-27 15:45:57
     */
    public static final String GMT_PAYMENT = "gmt_payment";

    // 以下为微信支付信息字段

    /**
     * 用户标识
     */
    public static final String OPEN_ID = "openid";

    /**
     * 用户子标识
     */
    public static final String SUB_OPENID = "sub_openid";


    /**
     * 总金额
     */
    public static final String TOTAL_FEE = "total_fee";


    /**
     * 现金支付金额
     */
    public static final String CASH_FEE = "cash_fee";


    /**
     * 支付完成时间 YYYYMMddHHmmss
     */
    public static final String TIME_END = "time_end";

    /**
     * 商户订单号
     */
    public static final String OUT_TRADE_NO = "out_trade_no";

    /**
     * 银联交易号 ，商户/收单机 构页面通过 JSSDK直接调用支付宝 APP时，商户/收单需要将银联返回的交易号去掉前两
     * 位后再调用支付宝 APP  trade_no：992015042321001004720200028594
     */
    public static final String TRADE_NO = "trade_no";


    public static final String TIMESTAMP = "timeStamp";

    public static final String NONCE_STR = "nonceStr";

    public static final String PACKAGE = "package";

    public static final String SIGN_TYPE = "signType";

    public static final String PAY_SIGN = "paySign";

    public static final String PREPAY_ID = "prepay_id";



}
