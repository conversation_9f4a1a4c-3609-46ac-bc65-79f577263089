package com.wosai.mpay.api.psbc;


import java.util.HashMap;
import java.util.Map;

public class PSBCRequestBuilder {
    private Map<String, Object> body;

    private Map<String, String> head;

    public PSBCRequestBuilder() {
        this.body = new HashMap<>();
        this.head = new HashMap<>();
    }

    public PSBCRequestBuilder(Map<String, String> head, Map<String, Object> body) {
        this.body = body;
        this.head = head;

    }

    public void setBody(String field, Object value) {
        body.put(field, value);
    }

    public void setHead(String field, String value) {
        head.put(field, value);
    }

    public Map<String, String> getHead() {
        return head;
    }

    public Map<String, Object> getBody() {
        return body;
    }

    public Map<String, Object> build() {
        HashMap<String, Object> hashMap = new HashMap<String, Object>() {{
            put(PSBCProtocolFields.BODY, body);
            put(PSBCProtocolFields.HEAD, head);
        }};
        return hashMap;
    }

}
