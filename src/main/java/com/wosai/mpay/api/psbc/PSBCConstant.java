package com.wosai.mpay.api.psbc;


public class PSBCConstant {

    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";


    public static final String YY_MM_DD_HH_MM_SS = "yy-MM-dd HH:mm:ss";

    public static final String CNY = "156";


    /**
     * 是否需要通知 * 00-需要；01-不需要；
     */
    public static final String NOTIFY_FLAG = "00";

    public static final String NON_NOTIFY_FLAG = "01";


    /**
     * 8007 微信公众号/小程序支付
     * 8016 支付宝服务窗
     * 8003 被扫二维码消费
     * 8002 申请动态二维码
     * 8004 消费结果通知
     * 8006 收款查询
     * 8005 退款
     * 8009 退款查询
     * 8010 订单关闭
     */

    public static final String WX_PAY_CODE = "8007";

    public static final String ALIPAY_PAY_CODE = "8016";

    public static final String SCANNED_PAY_CODE = "8003";

    public static final String DYNAMIC_QR_CODE_APPLICATION_CODE = "8002";

    public static final String CONSUMPTION_RESULT_NOTIFICATION_CODE = "8004";


    public static final String PAYMENT_QUERY_CODE = "8006";


    public static final String REFUND_CODE = "8005";

    public static final String REFUND_QUERY_CODE = "8009";

    public static final String ORDER_CLOSE_CODE = "8010";


    /**
     * 交易成功时出现
     * 05 - 银联被扫支付
     */
    public static final String UNIONPAY_SCANNED = "05";

    /**
     * 交易成功时出现
     * 07 - 微信被扫支付
     */
    public static final String WECHAT_SCANNED = "07";

    /**
     * 交易成功时出现
     * 09 - 支付宝被扫支付
     */
    public static final String ALIPAY_SCANNED = "09";


    /**
     * 用户支付中
     */
    public static final String USER_PAYING = "719030";


    /**
     * 调用三方异常
     */
    public static final String THIRD_PARTY_EXCEPTION = "719607";


    /**
     * 交易正在处理中请稍后请求
     */
    public static final String TRANSACTION_PROCESSING = "719022";


    public static final String TRANSACTION_PROCESSING_V2 = "710022";

    /**
     * 交易成功
     */
    public static final String SUCCESS_CODE = "000000";


    /**
     * 退款受理成功，等待资金到账
     */
    public static final String REFUND_STATUS_PENDING = "1";
    /**
     * 退款受理成功，资金已到账
     */
    public static final String REFUND_STATUS_SUCCESS = "2";

    /**
     * 退款受理成功，资金到账失败
     */
    public static final String REFUND_STATUS_FAILED = "3";


    /**
     * 01：初始态
     * 02：待支付
     * 03：成功
     * 04：失败
     * 05：校验失败
     * 99：支付中/退款中
     */
    public static final String INITIAL = "01";


    public static final String PENDING_PAYMENT = "02";


    public static final String SUCCESS = "03";


    public static final String FAILURE = "04";

    public static final String VALIDATION_FAILURE = "05";

    public static final String PROCESSING = "99";


    /**
     * 订单终态时有值
     * 01：关闭成功
     * 02：支付成功，订单关闭失败
     * 03：支付失败，订单关闭失败常量
     */
    public static final String CLOSE_SUCCESS = "01";


    public static final String PAY_SUCCESS_CLOSE_FAILURE = "02";

    public static final String PAY_FAILURE_CLOSE_FAILURE = "03";

    /**
     * 00：原生态
     * 01：已关闭
     * 02：已撤销
     * 03：部分退货
     * 04：全部退货
     * 05：已冲正
     */
    public static final String ORIGINAL_STATE = "00";

    public static final String CLOSED = "01";

    public static final String REVOKED = "02";

    public static final String PARTIAL_REFUND = "03";

    public static final String FULL_REFUND = "04";

    public static final String CORRECTED = "05";


    /**
     * 0-邮储收单订单号查询；
     * 1-商户订单号查询；如果为空，默认按商户订单号查询
     */
    public static final String YS_QUERY = "0";

    public static final String MERCHANT_QUERY = "1";


    public static final String WX_PRECREATE_METHOD = "unionpay.uniPay";

    public static final String ALIPAY_PRECREATE_METHOD = "unionpay.aliCreOrder";

    public static final String B2C_METHOD = "unionpay.orderPay";

    public static final String QUERY_METHOD = "unionpay.orderQuery";

    public static final String REFUND_METHOD = "unionpay.orderRefund";

    public static final String REFUND_QUERY_METHOD = "unionpay.refundQuery";

    public static final String CLOSE_METHOD = "unionpay.orderClose";

    public static final String NOTIFY_METHOD = "unionpay.offlineNotify";


    public static final String VERSION = "1";

    public static final String API = "API";

    public static final String CHARSET = "UTF-8";

    public static final String ACCEPT_TYPE = "application/json";


    public static final String CONTENT_TYPE = "application/json";

    public static final String POST = "post";


    public static final String GET = "get";

    public static final String INTERFACE_VERSION = "v1.2.8";

    public static final String ORDER_FLAG_0 = "0";

    public static final String ORDER_FLAG_1 = "1";

    public static final String CHANNEL_ID = "08";


    public static final String WX_TYPE = "JSAPI";

    // 信用卡支付
    public static final String IS_CREDIT_0 = "0";

    // 不使用信用卡支付
    public static final String IS_CREDIT_1 = "1";

}
