package com.wosai.mpay.api.psbc;


import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class PSBCClient {

    public static final Logger logger = LoggerFactory.getLogger(PSBCClient.class);

    private static SM2Util sm2Util;

    private static SM4Util sm4Util;

    private static String iv;

    static {
        sm2Util = SM2Util.getInstance();
        sm4Util = new SM4Util();
        iv = "UISwD9fW6cFh9SNS";
    }

    private int connectTimeout = 50000;
    private int readTimeout = 50000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map call(Map<String, Object> request, String serviceUrl, String serviceProviderPartnerId, String sopPublicKey, String publicKey, String privateKey, String method) throws MpayApiNetworkError {
        String requestData = "";
        try {
            requestData = JsonUtil.objectToJsonString(request);
        } catch (Exception e) {
            throw new MpayApiNetworkError("请求报文序列化错误");
        }
        logger.info("request {}", requestData);
        //1.生成 sm4Key
        String sm4Key = SMUtil.getSM4Key();
        //2.使用 sm4Key 加密请求报文
        String encryptRequest = "";
        try {
            encryptRequest = Base64.getMimeEncoder().encodeToString(SM4Util.encryptCBC(requestData.getBytes("UTF-8"), sm4Key.getBytes(), iv.getBytes()));
        } catch (Exception e) {
            throw new MpayApiNetworkError("SM4加密失败");
        }
        Map<String, String> pscbRequest = new HashMap<>();
        pscbRequest.put(PSBCRequestFields.REQUEST, encryptRequest);
        String encryptKey = "";
        try {
            encryptKey = sm2Util.encryptWithSM3Hash(sopPublicKey, sm4Key);
        } catch (Exception e) {
            throw new MpayApiNetworkError("SM2加密失败");
        }
        pscbRequest.put(PSBCRequestFields.ENCRYPT_KEY, encryptKey);
        pscbRequest.put(PSBCRequestFields.ACCESS_TOKEN, "");
        //4.签名
        StringBuilder sb = new StringBuilder();
        sb.append(MapUtils.getString(pscbRequest, PSBCRequestFields.REQUEST, ""));
        sb.append(MapUtils.getString(pscbRequest, PSBCRequestFields.ENCRYPT_KEY, ""));
        sb.append(MapUtils.getString(pscbRequest, PSBCRequestFields.ACCESS_TOKEN, ""));
        SignatureInfo sign = sm2Util.sign(serviceProviderPartnerId, privateKey, sb.toString(), publicKey);
        String signature = SMUtil.toSignStr(sign);
        pscbRequest.put(PSBCRequestFields.SIGNATURE, signature);
        String requestBody = "";
        try {
            requestBody = JsonUtil.objectToJsonString(pscbRequest);
        } catch (Exception e) {
            throw new MpayApiNetworkError("请求报文序列化错误");
        }
        Map<String, Object> result = HttpClientUtils.doCommonMethod(PSBCClient.class.getName(), null, null, serviceUrl, null, PSBCConstant.CONTENT_TYPE, requestBody, null, PSBCConstant.CHARSET, connectTimeout, readTimeout, method);
        String responseData = MapUtils.getString(result, PSBCProtocolFields.BODY, "");
        Map responseMap;
        try {
            responseMap = JsonUtil.jsonStringToObject(responseData, Map.class);
        } catch (Exception e) {
            throw new MpayApiNetworkError("响应报文解析错误");
        }
        // 验签
        sb.setLength(0);
        sb.append(MapUtils.getString(responseMap, PSBCResponseFields.RESPONSE, ""));
        sb.append(MapUtils.getString(responseMap, PSBCResponseFields.ENCRYPT_KEY, ""));
        sb.append(MapUtils.getString(responseMap, PSBCResponseFields.ACCESS_TOKEN, ""));
        boolean checked = sm2Util.verifySign(serviceProviderPartnerId, sopPublicKey, sb.toString(), SMUtil.fromString(MapUtils.getString(responseMap, PSBCResponseFields.SIGNATURE, "")));
        if (checked) {
            // 解析密钥
            String respSm4Key = "";
            try {
                respSm4Key = sm2Util.decryptWithSM3Hash(privateKey, MapUtils.getString(responseMap, PSBCResponseFields.ENCRYPT_KEY, ""));
            } catch (Exception e) {
                throw new MpayApiNetworkError("响应报文sm4解密失败");
            }
            // 解析报文
            String respMessage = "";
            try {
                respMessage = new String((SM4Util.decryptCBC(Base64.getMimeDecoder().decode(MapUtils.getString(responseMap, PSBCResponseFields.RESPONSE, "")), respSm4Key.getBytes(), iv.getBytes())), "UTF-8");
            } catch (Exception e) {
                throw new MpayApiNetworkError("响应报文解密失败");
            }
            logger.info("response {}", respMessage);
            Map<String, Object> response = null;
            try {
                response = JsonUtil.jsonStringToObject(respMessage, Map.class);
            } catch (Exception e) {
                throw new MpayApiNetworkError("响应报文解析失败");
            }
            return MapUtils.getMap(response, PSBCProtocolFields.BODY);
        } else {
            throw new MpayApiNetworkError("验签失败");
        }
    }

}
