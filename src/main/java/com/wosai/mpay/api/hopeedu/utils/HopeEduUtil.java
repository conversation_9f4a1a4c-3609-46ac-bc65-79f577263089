package com.wosai.mpay.api.hopeedu.utils;

import com.wosai.mpay.api.hopeedu.constants.HopeEduProtocolFieldsConstants;
import com.wosai.mpay.api.hopeedu.constants.HopeEduRequestFieldsConstants;
import com.wosai.mpay.api.hopeedu.constants.HopeEduResponseFieldsConstants;
import com.wosai.mpay.api.hopeedu.enums.HopeEduResultCodeEnum;
import com.wosai.mpay.util.StringUtils;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className HopeEduUtil
 * @description:
 * @create: 2025-05-21 09:38
 **/
public class HopeEduUtil {

    private static final String ALPHABET = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";

    private static final int BASE_LENGTH = ALPHABET.length();

    private static final int TERMINAL_SN_LENGTH = 8;

    private static final int MERCHANT_SN_LENGTH = 15;

    private static final String MERCHANT_PRE_STR = "mch-";

    public static String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(HopeEduProtocolFieldsConstants.DATE_TIME_FOTMAT);
        return sdf.format(date);
    }


    public static Map<String, Object> getBody(Map<String, Object> respMap) {
        Map<String, Object> body = (Map<String, Object>)respMap.getOrDefault(HopeEduRequestFieldsConstants.BODY, HopeEduProtocolFieldsConstants.emptyMap);

        String resultCode = body.getOrDefault(HopeEduResponseFieldsConstants.Body.RESULT_CODE, "").toString();

        if (StringUtils.isEmpty(resultCode)) {
            return getErrorBody("");
        }
        return body;
    }

    private static Map<String, Object> getErrorBody(String msg) {
        Map<String, Object> errorBody = new HashMap<>();
        errorBody.put(HopeEduResponseFieldsConstants.Body.RESULT_CODE, HopeEduResultCodeEnum.UNKNOWN_ERROR.getCode());
        if (StringUtils.isEmpty(msg)) {
            errorBody.put(HopeEduResponseFieldsConstants.Body.MESSAGE, HopeEduResultCodeEnum.UNKNOWN_ERROR.getMsg());
        } else {
            errorBody.put(HopeEduResponseFieldsConstants.Body.MESSAGE, msg);
        }
        return errorBody;
    }

    public static Map<String, Object> getErrorResponse(String msg) {
        Map<String, Object> respMap = new HashMap<>();
        respMap.put(HopeEduResponseFieldsConstants.BODY, getErrorBody(msg));
        return respMap;
    }

    public static String convertToBase62(String decimalString) {
        // 使用 BigInteger 处理长数字字符串，避免溢出
        BigInteger decimalValue = new BigInteger(decimalString);
        BigInteger base = BigInteger.valueOf(BASE_LENGTH);
        StringBuilder result = new StringBuilder();
        // 循环取余数，构建新字符串
        while (decimalValue.compareTo(BigInteger.ZERO) > 0) {
            BigInteger[] quotientAndRemainder = decimalValue.divideAndRemainder(base);
            BigInteger remainder = quotientAndRemainder[1];
            result.insert(0, ALPHABET.charAt(remainder.intValue())); // 前插余数对应的字符
            decimalValue = quotientAndRemainder[0]; // 更新被除数
        }
        // 处理输入为0的情况
        return result.length() == 0 ? "0" : result.toString();
    }

    public static String convertToTerminal(String terminalSn) {
        // 院校通终端sn长度不能超过8
        if (StringUtils.isEmpty(terminalSn)) {
            return "";
        }
        int length = terminalSn.length();
        if (length <= TERMINAL_SN_LENGTH){
            return terminalSn;
        }

        try {
            if (isNumericRegex(terminalSn)) {
                String newSn = convertToBase62(terminalSn);
                int len = newSn.length();
                if (len > TERMINAL_SN_LENGTH) {
                    newSn = newSn.substring(len - TERMINAL_SN_LENGTH);
                }
                return newSn;
            }
        } catch (Exception e) {
        }
        return terminalSn.substring(length - TERMINAL_SN_LENGTH);
    }


    public static String convertToMerchant(String merchantSn) {
        // 院校通商户sn长度不能超过15
        if (StringUtils.isEmpty(merchantSn)) {
            return "";
        }

        if (merchantSn.contains(MERCHANT_PRE_STR)) {
            merchantSn = replace(merchantSn, MERCHANT_PRE_STR, "");
        }

        int length = merchantSn.length();
        if (length <= MERCHANT_SN_LENGTH){
            return merchantSn;
        }

        try {
            if (isNumericRegex(merchantSn)) {
                String newSn = convertToBase62(merchantSn);
                int len = newSn.length();
                if (len > MERCHANT_SN_LENGTH) {
                    newSn = newSn.substring(len - MERCHANT_SN_LENGTH);
                }
                return newSn;
            }
        } catch (Exception e) {
        }
        return merchantSn.substring(length - MERCHANT_SN_LENGTH);
    }

    /**
     * 使用正则表达式检查字符串是否只包含数字字符
     *
     * @param str 要检查的字符串。
     * @return 如果字符串只包含数字字符，则返回 true；否则返回 false。
     */
    public static boolean isNumericRegex(String str) {
        if (str == null || str.isEmpty()) {
            return false; // 空字符串或 null 不是纯数字
        }
        return str.matches("\\d+"); // 使用正则表达式匹配一个或多个数字
    }



    public static String replace(String text, String searchString, String replacement) {
        return replace(text, searchString, replacement, -1);
    }

    public static String replace(String text, String searchString, String replacement, int max) {
        if (!StringUtils.isEmpty(text) && !StringUtils.isEmpty(searchString) && replacement != null && max != 0) {
            int start = 0;
            int end = text.indexOf(searchString, start);
            if (end == -1) {
                return text;
            } else {
                int replLength = searchString.length();
                int increase = replacement.length() - replLength;
                increase = increase < 0 ? 0 : increase;
                increase *= max < 0 ? 16 : (max > 64 ? 64 : max);

                StringBuilder buf;
                for(buf = new StringBuilder(text.length() + increase); end != -1; end = text.indexOf(searchString, start)) {
                    buf.append(text.substring(start, end)).append(replacement);
                    start = end + replLength;
                    --max;
                    if (max == 0) {
                        break;
                    }
                }

                buf.append(text.substring(start));
                return buf.toString();
            }
        } else {
            return text;
        }
    }

}
