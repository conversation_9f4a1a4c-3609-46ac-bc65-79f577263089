package com.wosai.mpay.api.hopeedu.constants;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className HopeEduResponseFieldsConstants
 * @description:
 * @create: 2025-05-20 19:08
 **/
public class HopeEduResponseFieldsConstants {


    public static final String CODE = "code";

    public static final String MSG = "msg";

    /**
     * 响应请求
     */
    public static final String RESPONSE = "response";


    /**
     * 签名字符串
     */
    public static final String SIGNATURE = "signature";


    /**
     * 请求头
     */
    public static final String HEAD = "head";

    /**
     * 请求主体
     */
    public static final String BODY = "body";


    public static final String RETURN_CODE = "return_code";

    public static final String RETURN_MSG = "return_msg";

    /**
     * 请求头
     */
    public static final class Head {

        /**
         * 接口版本号 (最⼤10位)
         */
        public static final String VERSION = "version";
        /**
         * 签名算法， “SHA256”: SHAWith256
         */
        public static final String SIGN_TYPE = "sign_type";
        /**
         * 请求时间 (最⼤64位)
         */
        public static final String REQUEST_TIME = "request_time";
        /**
         * 渠道编码 (由我们提供)
         */
        public static final String CHANNEL_CODE = "channel_code";

    }


    public static final class Body {
        /**
         * 响应结果编号
         */
        public static final String RESULT_CODE = "result_code";


        /**
         * 响应结果信息
         */
        public static final String MESSAGE = "message";

        /**
         * 响应结果
         */
        public static final String DATA = "data";


        public static final class Data {

            /**
             * 订单号
             * 数据类型: String(32)
             * 必填: 是
             */
            public static final String ORDER_ID = "orderId";
            /**
             * 订单状态
             * 数据类型: String(32)
             * 必填: 是
             */
            public static final String ORDER_STATUS = "orderStatus";
        }
    }

    /**
     * 真实接口地址
     */
    public static final String FIELD = "field";
}
