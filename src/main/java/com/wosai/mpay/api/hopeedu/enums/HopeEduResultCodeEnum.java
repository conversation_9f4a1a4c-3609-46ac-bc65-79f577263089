package com.wosai.mpay.api.hopeedu.enums;

import com.wosai.mpay.api.xzx.enums.XZXResponseErrorEnum;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className HopeEduResultCodeEnum
 * @description:
 * @create: 2025-05-21 08:47
 **/
public enum HopeEduResultCodeEnum {

    GET_FIELD_SUCCESS("000000", "成功"),
    RECEIVE_SUCCESS("01", "接受支付消息成功"),
    SUCCESS("200", "操作成功"),
    PARAM_ERROR("422", "参数错误"),
    SIGN_ERROR("421", "签名错误"),
    INSUFFICIENT_BALANCE("423", "余额不⾜"),
    PAYMENT_FAILED("424", "⽀付失败"),
    PAYMENT_CODE_EXPIRED("425", "⽀付码过期"),
    UNKNOWN_ERROR("500", "未知错误");

    private final String code;
    private final String msg;

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    HopeEduResultCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static HopeEduResultCodeEnum of(String code) {
        if (null == code) {
            return null;
        }
        for (HopeEduResultCodeEnum e : HopeEduResultCodeEnum.values()) {
            if (code.equals(e.code)) {
                return e;
            }
        }
        return null;
    }

    /**
     * 请求是否成功
     *
     * @param errorCode
     * @return
     */
    public static boolean isSuccess(String errorCode) {
        return HopeEduResultCodeEnum.SUCCESS.getCode().equals(errorCode);
    }

    /**
     * 请求是否成功
     *
     * @param errorCode
     * @return
     */
    public static boolean isReceiveSuccess(String errorCode) {
        return HopeEduResultCodeEnum.RECEIVE_SUCCESS.getCode().equals(errorCode);
    }

    /**
     * 获取真实地址是否成功
     * @param code
     * @return
     */
    public static boolean isGetFieldSuccess(String code) {
        return HopeEduResultCodeEnum.GET_FIELD_SUCCESS.getCode().equals(code);
    }
}
