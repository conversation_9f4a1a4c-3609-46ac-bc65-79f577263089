package com.wosai.mpay.api.hopeedu.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className HopeEduPayStatusCodeEnum
 * @description:
 * @create: 2025-05-21 09:02
 **/
public enum HopeEduPayStatusCodeEnum {
    SUCCESS(1, "⽀付成功"),
    FAILED(2, "⽀付失败"),
    PENDING(3, "⽀付中"),
    CANCELED(4, "已撤销"),
    REFUNDED_SUCCESS(5, "退款成功"),
    REFUNDED_FAILED(6, "退款失败");

    private final Integer code;
    private final String description;

    HopeEduPayStatusCodeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static HopeEduPayStatusCodeEnum of(Integer code) {
        for (HopeEduPayStatusCodeEnum status : HopeEduPayStatusCodeEnum.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }
}