package com.wosai.mpay.api.hopeedu;

import com.wosai.mpay.api.hopeedu.constants.HopeEduProtocolFieldsConstants;
import com.wosai.mpay.api.hopeedu.constants.HopeEduRequestFieldsConstants;
import com.wosai.mpay.api.hopeedu.constants.HopeEduUrlPathConstants;
import com.wosai.mpay.api.hopeedu.utils.HopeEduUtil;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className HopeEduRequestBuilder
 * @description:
 * @create: 2025-05-21 09:43
 **/
public class HopeEduRequestBuilder {

    private final Map<String,Object> request;
    private final Map<String, Object> head;
    private final Map<String, Object> body;


    public HopeEduRequestBuilder(String channelCode){
        request = new LinkedHashMap<>(4);

        head = new LinkedHashMap<>(8);
        body = new LinkedHashMap<>(16);

        head.put(HopeEduRequestFieldsConstants.Head.VERSION, HopeEduProtocolFieldsConstants.VERSION);
        head.put(HopeEduRequestFieldsConstants.Head.SIGN_TYPE, HopeEduProtocolFieldsConstants.SIGN_TYPE_SHA256);
        head.put(HopeEduRequestFieldsConstants.Head.REQUEST_TIME, HopeEduUtil.formatDate(new Date()));
        head.put(HopeEduRequestFieldsConstants.Head.CHANNEL_CODE, channelCode);
        head.put(HopeEduRequestFieldsConstants.Head.AGENT, HopeEduProtocolFieldsConstants.AGENT_NAME);

    }

    public void setBodyField(String field, Object value) {
        body.put(field,  value);
    }

    public Map<String,Object> build(){
        request.put(HopeEduRequestFieldsConstants.HEAD, head);
        request.put(HopeEduRequestFieldsConstants.BODY, body);
        return request;
    }

    public Map<String,Object> buildBody(){
        return body;
    }
}
