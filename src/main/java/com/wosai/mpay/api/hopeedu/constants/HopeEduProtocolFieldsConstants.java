package com.wosai.mpay.api.hopeedu.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className HopeEduProtocolFieldsConstants
 * @description:
 * @create: 2025-05-20 19:07
 **/
public class HopeEduProtocolFieldsConstants {

    public static final String emptyStr = "";

    public static final Map<String, Object> emptyMap = new HashMap<>();

    /**
     * 请求头
     */
    public static final String CONTENT_TYPE = "Content-Type";

    /**
     * 请求content type 值
     */
    public static final String CONTENT_TYPE_JSON = "application/json";

    /**
     * 请求content type 值
     */
    public static final String CHARSET = "utf-8";


    /**
     * 接口版本号
     */
    public static final String VERSION = "1.0.0";

    /**
     * 签名算法
     */
    public static final String SIGN_TYPE_SHA256 = "SHA256";


    /**
     * 日期格式化
     * "yyyy-MM-dd'T'HH:mm:ss.SSS"
     * "yyyy-MM-dd'T'HH:mm:ssZZZ"
     */
    public static final String DATE_TIME_FOTMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS";
//    public static final String DATE_TIME_FOTMAT = "yyyy-MM-dd'T'HH:mm:ssZZZ";

    /**
     * 交易消息接口日期格式化
     */
    public static final String DATE_TIME_MESSAGE_FORMAT = "yyyyMMddHHmmss";

    /**
     * 默认签名算法
     */
    public static final String DEFAULT_SIGN_ALGORITHM = "SHA256WithRSA";

    /**
     * 订单类型值
     */
    public static final String ORDER_TYPE_VALUE = "1";

    /**
     * AGENT名称
     */
    public  static final String AGENT_NAME= "shouqianba";


    public static final String ORDER_STATUS_PAY = "已支付";
    public static final String ORDER_STATUS_CANCEL = "未支付";
}
