package com.wosai.mpay.api.hopeedu;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wosai.mpay.api.hopeedu.constants.HopeEduProtocolFieldsConstants;
import com.wosai.mpay.api.hopeedu.constants.HopeEduRequestFieldsConstants;
import com.wosai.mpay.api.hopeedu.constants.HopeEduResponseFieldsConstants;
import com.wosai.mpay.api.hopeedu.constants.HopeEduUrlPathConstants;
import com.wosai.mpay.api.hopeedu.enums.HopeEduResultCodeEnum;
import com.wosai.mpay.api.hopeedu.utils.HopeEduSignUtil;
import com.wosai.mpay.api.hopeedu.utils.HopeEduUtil;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import com.wosai.pantheon.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className HopeEduClient
 * @description:
 * @create: 2025-05-20 19:06
 **/
public class HopeEduClient {


    public static final Logger log = LoggerFactory.getLogger(HopeEduClient.class);

    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    //打印日志时需要加密的字段
    private static final List<String> NEED_ENCRYPT_FIELDS_WHEN_LOG = Arrays.asList(
            HopeEduRequestFieldsConstants.Body.ORDER_AMOUNT);

    //默认加密后的值
    private static final String DEFAULT_ENCRYPT_VALUE = "*";

    private static final String REAL_PRE_URL_KEY = "hopeedu:preurl";
    public static final Cache<String, String> REAL_PRE_URL_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(60, TimeUnit.SECONDS)
            .maximumSize(10).recordStats().build();

    /**
     * 获取真实地址
     * @param requestUrl
     * @return
     */
    private String getRealPreUrl(String requestUrl) {
        try {
            // 增加缓存
            String value = REAL_PRE_URL_CACHE.getIfPresent(REAL_PRE_URL_KEY);
            if (!Objects.isNull(value)){
                return value;
            }

            if (!requestUrl.contains(HopeEduUrlPathConstants.GET_CAM_SETTING_FIELD)) {
                requestUrl += HopeEduUrlPathConstants.GET_CAM_SETTING_FIELD;
            }
            log.info("request getRealPreUrl {}", requestUrl);
            String response = HttpClientUtils.doGet(HopeEduClient.class.getName(), null, null,
                    requestUrl, null, HopeEduProtocolFieldsConstants.CHARSET, connectTimeout, readTimeout);

            log.info("response getRealPreUrl content {}", response);

            TreeMap<String, Object> result = JsonUtil.jsonStringToObject(response, new TypeReference<TreeMap<String, Object>>() {});

            String code = MapUtils.getString(result, "code", "");
            if (HopeEduResultCodeEnum.isGetFieldSuccess(code)) {
                Map<String, Object> data = MapUtils.getMap(result, "data", new HashMap<String, Object>());
                String preUrl = MapUtils.getString(data, HopeEduResponseFieldsConstants.FIELD, "");

                // 增加缓存
                REAL_PRE_URL_CACHE.put(REAL_PRE_URL_KEY, preUrl);
                return preUrl;
            }
            return "";
        } catch (Exception e) {
            log.error("院校通请求异常: getRealPreUrl e.Msg={}", e.getMessage(), e);

            return "";
        }
    }

    /**
     * 请求接口
     *
     * @param requestUrl   请求地址
     * @param urlPath       请求方法
     * @param params       请求参数
     * @param privateKey   私钥
     * @param publicKey    公钥
     * @return
     * @throws MpayException
     * @throws MpayApiNetworkError
     */
    public Map<String, Object> call(String requestUrl, String urlPath, Map<String, Object> params, String privateKey, String publicKey) throws MpayException, MpayApiNetworkError {

        // 请求之前要获取真实接口地址
        String preUrl = getRealPreUrl(requestUrl);
        if (StringUtils.isEmpty(preUrl)) {
            return HopeEduUtil.getErrorResponse("");
        }

        String url = preUrl + urlPath;

        //加签
        String request = HopeEduSignUtil.generateSign(params, privateKey);

        try {
            log.info("request {}", request);
            String response = HttpClientUtils.doPost(HopeEduClient.class.getName(), null, null,
                    url, HopeEduProtocolFieldsConstants.CONTENT_TYPE_JSON, request, HopeEduProtocolFieldsConstants.CHARSET, connectTimeout, readTimeout);

            log.info("response content {}", response);

            return verifyResponse(url, response, publicKey);
        } catch (Exception e) {
            log.error("院校通请求异常: e.Msg={}", e.getMessage(), e);

            return HopeEduUtil.getErrorResponse("");
        }
    }

        /**
         * 请求接口
         *
         * @param requestUrl   请求地址
         * @param urlPath       请求方法
         * @param params       请求参数
         * @param accessToken
         * @return
         * @throws MpayException
         * @throws MpayApiNetworkError
         */
    public Map<String, Object> callMessagePush(String requestUrl, String urlPath, Map<String, Object> params, String accessToken) throws MpayException, MpayApiNetworkError {

        // 请求之前要获取真实接口地址
        String preUrl = getRealPreUrl(requestUrl);
        if (StringUtils.isEmpty(preUrl)) {
            return HopeEduUtil.getErrorResponse("");
        }

        String url = preUrl + urlPath;

        String terminalSn = params.getOrDefault(HopeEduRequestFieldsConstants.Body.TERMINAL_ID, "").toString();
        params.put(HopeEduRequestFieldsConstants.Body.TERMINAL_ID, HopeEduUtil.convertToTerminal(terminalSn));

        // 消息推送，需要增加md5
        String md5 = HopeEduSignUtil.getMd5Str(params, accessToken);
        params.put(HopeEduRequestFieldsConstants.Body.KEY_SIGN, md5);

        try {
            log.info("request {}", toJsonStringWithEncryptFields(params));
            
            String request = JsonUtil.objectToJsonString(params);
            String response = HttpClientUtils.doPost(HopeEduClient.class.getName(), null, null,
                    url, HopeEduProtocolFieldsConstants.CONTENT_TYPE_JSON, request, HopeEduProtocolFieldsConstants.CHARSET, connectTimeout, readTimeout);

            log.info("response content {}", response);
            TreeMap<String, Object> result = JsonUtil.jsonStringToObject(response, new TypeReference<TreeMap<String, Object>>() {});

            return result;
        } catch (Exception e) {
            log.error("院校通请求异常: e.Msg={}", e.getMessage(), e);

            return HopeEduUtil.getErrorResponse("");
        }
    }



    /**
     * 验签响应数据
     *
     * @param url
     * @param response
     * @param publicKey
     * @return
     * @throws MpayException
     */
    private Map<String, Object> verifyResponse(String url, String response, String publicKey) throws MpayException {
        TreeMap<String, Object> result = JsonUtil.jsonStringToObject(response, new TypeReference<TreeMap<String, Object>>() {});
        Map<String, Object> respMap = (Map<String, Object>)result.getOrDefault(HopeEduResponseFieldsConstants.RESPONSE, HopeEduProtocolFieldsConstants.emptyMap);


        String sign = result.getOrDefault(HopeEduResponseFieldsConstants.SIGNATURE, HopeEduProtocolFieldsConstants.emptyStr).toString();

        if (!HopeEduSignUtil.verify(respMap, sign, publicKey)) {
            log.info("response sign error {}", response);

            return HopeEduUtil.getErrorResponse("sign verify error");
        }

        return respMap;
    }

    /**
     * 给指定需要脱敏的字段进行脱敏，并返回Json String
     *
     * @param data
     * @return
     * @throws MpayException
     */
    private static String toJsonStringWithEncryptFields(Map<String, Object> data) throws MpayException {
        Map<String, Object> newMap = MapEncryptUtil.resetValueAndReturnNewMap(data, NEED_ENCRYPT_FIELDS_WHEN_LOG, DEFAULT_ENCRYPT_VALUE);
        return JsonUtil.objectToJsonString(newMap);
    }

}
