package com.wosai.mpay.api.hopeedu.constants;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className HopeEduRequestFieldsConstants
 * @description:
 * @create: 2025-05-20 19:08
 **/
public class HopeEduRequestFieldsConstants {

    /**
     * 标识请求
     */
    public static final String REQUEST = "request";


    /**
     * 签名字符串
     */
    public static final String SIGNATURE = "signature";


    /**
     * 请求头
     */
    public static final String HEAD = "head";

    /**
     * 请求主体
     */
    public static final String BODY = "body";

    /**
     * 请求头
     */
    public static final class Head {

        /**
         * 接口版本号 (最⼤10位)
         */
        public static final String VERSION = "version";
        /**
         * 签名算法， “SHA256”: SHAWith256
         */
        public static final String SIGN_TYPE = "sign_type";
        /**
         * 请求时间 (最⼤64位)
         */
        public static final String REQUEST_TIME = "request_time";
        /**
         * 渠道编码 (由我们提供)
         */
        public static final String CHANNEL_CODE = "channel_code";

        /**
         * agent
         */
        public static final String AGENT = "agent";

    }


    public static final class Body {
        /**
         * 金额
         * 数据类型: BigDecimal(10,2)
         * 必填: 是
         */
        public static final String ORDER_AMOUNT = "orderAmount";
        /**
         * 订单类型，暂时只有⼀种（默认为1）
         * 数据类型: Integer(6)
         * 必填: 是
         */
        public static final String ORDER_TYPE = "orderType";
        /**
         * 消费机编号（唯⼀）
         * 数据类型: String(100以内)
         * 必填: 是
         */
        public static final String MACHINE_NUMBER = "machineNumber";
        /**
         * 机器码（唯⼀）
         * 数据类型: String(100以内)
         * 必填: 是
         */
        public static final String MACHINE_CODE = "machineCode";
        /**
         * 付款码
         * 数据类型: String(18-32)
         * 必填: 是
         */
        public static final String CODE_VALUE = "codeValue";
        /**
         * 订单号
         * 数据类型: String(32)
         * 必填: 是
         */
        public static final String ORDER_ID = "orderId";
        /**
         * 版本号，当前版本210
         * 数据类型: String(3)
         * 必填: 是
         */
        public static final String PAY_VER = "pay_ver";
        /**
         * ⽤户银联卡类型 0储蓄卡，1信⽤卡，2未知
         * 数据类型: String(2)
         * 必填: 是
         */
        public static final String CARD_TYPE = "card_type";
        /**
         * 商户号
         * 数据类型: String(15)
         * 必填: 是
         */
        public static final String MERCHANT_NO = "merchant_no";
        /**
         * 商户名称
         * 数据类型: String(40)
         * 必填: 是
         */
        public static final String MERCHANT_NAME = "merchant_name";
        /**
         * 终端号(设备号在系统中的映射)
         * 数据类型: String(8)
         * 必填: 是
         */
        public static final String TERMINAL_ID = "terminal_id";
        /**
         * 终端交易时间，yyyyMMddHHmmss，全局统⼀时间格式
         * 数据类型: String(14)
         * 必填: 是
         */
        public static final String TERMINAL_TIME = "terminal_time";
        /**
         * 终端流⽔号，此处传商户发起预⽀付或公众号⽀付时所传⼊的交易流⽔号
         * 数据类型: String(32)
         * 必填: 是
         */
        public static final String TERMINAL_TRACE = "terminal_trace";
        /**
         * 对接⽅平台唯⼀订单号
         * 数据类型: String(32)
         * 必填: 是
         */
        public static final String OUT_TRADE_NO = "out_trade_no";
        /**
         * 退款订单号
         * 数据类型: String(32)
         * 必填: 是
         */
        public static final String OUT_REFUND_NO = "out_refund_no";
        /**
         * ⾦额，单位分
         * 数据类型: String(12)
         * 必填: 是
         */
        public static final String TOTAL_FEE = "total_fee";
        /**
         * 交易类型010微信，020 ⽀付宝，030银⾏卡，060qq钱包，080京东钱包，090⼝碑,100翼⽀付，110银联⼆维码
         * 数据类型: String(3)
         * 必填: 是
         */
        public static final String PAY_TYPE = "pay_type";
        /**
         * ⽀付完成时间，yyyyMMddHHmmss，全局统⼀时间格式
         * 数据类型: String(14)
         * 必填: 是
         */
        public static final String END_TIME = "end_time";
        /**
         * 设备SN
         * 数据类型: String(128)
         * 必填: 是
         */
        public static final String ATTACH = "attach";
        /**
         * 付款⽅⽤户id，“微信openid”、“⽀付宝账户”、“qq号”等
         * 数据类型: String(32)
         * 必填: 是
         */
        public static final String USER_ID = "user_id";
        /**
         * 通道订单号，微信订单号、⽀付宝订单号等
         * 数据类型: String(32)
         * 必填: 是
         */
        public static final String CHANNEL_TRADE_NO = "channel_trade_no";
        /**
         * ⽀付状态，1⽀付成功，2⽀付失败，3⽀付中，4已撤销，5退款成功， 6退款失败
         * 数据类型: int(2)
         * 必填: 是
         */
        public static final String PAY_STATUS_CODE = "pay_status_code";
        /**
         * MD5(32位）签名
         * 数据类型: String(32)
         * 必填: 是
         */
        public static final String KEY_SIGN = "key_sign";
        /**
         * ⽀付渠道，⻓度未限制，不⼊库
         * 数据类型: String
         * 必填: 是
         */
        public static final String PAY_CHANNEL = "pay_channel";
        /**
         * 备注信息
         * 数据类型: String(128)
         * 必填: 否
         */
        public static final String ORDER_BODY = "order_body";
    }
}
