package com.wosai.mpay.api.hopeedu.enums;

import com.wosai.mpay.api.xzx.enums.XZXPayPrdCodeEnum;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className HopeEduPayTypeEnum
 * @description:
 * @create: 2025-05-21 09:03
 **/
public enum HopeEduPayTypeEnum {

    WECHAT("010", "微信"),
    ALIPAY("020", "⽀付宝"),
    BANK_CARD("030", "银⾏卡"),
    QQ_WALLET("060", "qq钱包"),
    JD_WALLET("080", "京东钱包"),
    KOUBEI("090", "⼝碑"),
    YI_PAY("100", "翼⽀付"),
    UNIONPAY_QRCODE("110", "银联⼆维码"),
    APPLE_PAY("15", "Apple Pay"),
    LAKALA_WALLET("16", "拉卡拉钱包"),
    CMCC("17", "移动和包"),
    SODEXO("18", "索迪斯预付卡"),
    DCEP("19", "数字人民币"),
    FOXCONN("20", "富圈圈钱包"),
    GRABPAY("21", "grabpay钱包"),
    BANKACCOUNT("22", "银行转账"),
    MACAU_PASS("23", "澳门通"),
    OTHER("24", "其他");


    private final String code;
    private final String description;

    HopeEduPayTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static HopeEduPayTypeEnum of(String type) {
        if (null == type) {
            return null;
        }
        for (HopeEduPayTypeEnum e : HopeEduPayTypeEnum.values()) {
            if (type.equals(e.code)) {
                return e;
            }
        }
        return null;
    }
}


