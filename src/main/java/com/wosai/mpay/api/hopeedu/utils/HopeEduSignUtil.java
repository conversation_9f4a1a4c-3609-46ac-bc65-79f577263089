package com.wosai.mpay.api.hopeedu.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wosai.mpay.api.hopeedu.HopeEduRequestBuilder;
import com.wosai.mpay.api.hopeedu.constants.HopeEduProtocolFieldsConstants;
import com.wosai.mpay.api.hopeedu.constants.HopeEduRequestFieldsConstants;
import com.wosai.mpay.api.macaupass.constants.MacauPassProtocolFieldsConstants;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.StringUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className HopeEduSignUtil
 * @description:
 * @create: 2025-05-21 09:15
 **/
public class HopeEduSignUtil {
    public static final Logger log = LoggerFactory.getLogger(HopeEduSignUtil.class);

    private static final String SEP = "&";
    private static final String EQ = "=";

    public static String generateSign(Map<String, Object> params, String privateKey) throws MpayException {

        try {
            String signStr = getSignStr(params);

            String sign = RsaSignature.sign(signStr, HopeEduProtocolFieldsConstants.DEFAULT_SIGN_ALGORITHM, privateKey);

            params.put(HopeEduRequestFieldsConstants.SIGNATURE, sign);
            String request = String.format("{\"request\":%s,\"signature\":\"%s\"}", signStr, sign);
            return request;
        } catch (MpayException e) {
            throw e;
        }
    }

    /**
     * 使用公钥验证 SHA256WithRSA 签名
     *
     * @param params      待验签数据
     * @param signature 签名字符串 (Base64 编码)
     * @param publicKey 公钥字符串 (Base64 编码)
     * @return true 如果验签成功，false 否则返回
     * @throws Exception 如果验签过程中发生错误
     */
    public static boolean verify(Map<String, Object> params, String signature, String publicKey) throws MpayException {

        try {
            String signStr = getSignStr(params);

            Boolean type = RsaSignature.validateSign(signStr, signature, HopeEduProtocolFieldsConstants.DEFAULT_SIGN_ALGORITHM, publicKey);

            if (!type) {
                log.info("hopeedu response sign error responseSign: {}; signStr: {}; ", signature, signStr);
            }

            return type;
        } catch (MpayException e) {
            throw e;
        }
    }

    public static String getSignStr(Map<String, Object> params) throws MpayException {

        String signStr = JsonUtil.objectToJsonString(params);
        if (signStr == null || signStr.length() <= 1) {
            return "";
        }
        return signStr;
    }


    /**
     * 将 Map<String, Object> 转换为按 Key 字母顺序排序的字符串。
     * Value 为字符串、数字或 Map<String, Object>，Map<String, Object> 则转换为 JSON 对象。
     *
     * @param params Map<String, Object> 对象
     * @return 排序后的字符串
     * @throws JsonProcessingException 如果 JSON 处理过程中发生错误
     */
    public static String getMd5Str(Map<String, Object> params, String accessToken) throws MpayException {

        StringBuilder sb = new StringBuilder();
        sb.append(HopeEduRequestFieldsConstants.Body.PAY_VER).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.PAY_VER));
        sb.append(SEP).append(HopeEduRequestFieldsConstants.Body.CARD_TYPE).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.CARD_TYPE));
        sb.append(SEP).append(HopeEduRequestFieldsConstants.Body.MERCHANT_NO).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.MERCHANT_NO));
        sb.append(SEP).append(HopeEduRequestFieldsConstants.Body.MERCHANT_NAME).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.MERCHANT_NAME));
        sb.append(SEP).append(HopeEduRequestFieldsConstants.Body.TERMINAL_ID).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.TERMINAL_ID));
        sb.append(SEP).append(HopeEduRequestFieldsConstants.Body.TERMINAL_TIME).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.TERMINAL_TIME));
        sb.append(SEP).append(HopeEduRequestFieldsConstants.Body.TERMINAL_TRACE).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.TERMINAL_TRACE));
        sb.append(SEP).append(HopeEduRequestFieldsConstants.Body.OUT_TRADE_NO).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.OUT_TRADE_NO));
        sb.append(SEP).append(HopeEduRequestFieldsConstants.Body.OUT_REFUND_NO).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.OUT_REFUND_NO));
        sb.append(SEP).append(HopeEduRequestFieldsConstants.Body.TOTAL_FEE).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.TOTAL_FEE));
        sb.append(SEP).append(HopeEduRequestFieldsConstants.Body.PAY_TYPE).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.PAY_TYPE));
        sb.append(SEP).append(HopeEduRequestFieldsConstants.Body.END_TIME).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.END_TIME));
        sb.append(SEP).append(HopeEduRequestFieldsConstants.Body.ATTACH).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.ATTACH));
        sb.append(SEP).append(HopeEduRequestFieldsConstants.Body.USER_ID).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.USER_ID));
        sb.append(SEP).append(HopeEduRequestFieldsConstants.Body.CHANNEL_TRADE_NO).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.CHANNEL_TRADE_NO));
        sb.append(SEP).append(HopeEduRequestFieldsConstants.Body.PAY_STATUS_CODE).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.PAY_STATUS_CODE));
        sb.append(SEP).append(HopeEduRequestFieldsConstants.Body.PAY_CHANNEL).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.PAY_CHANNEL));
        sb.append(SEP).append(HopeEduRequestFieldsConstants.Body.ORDER_BODY).append(EQ).append(params.get(HopeEduRequestFieldsConstants.Body.ORDER_BODY));

        String str = sb.toString()+"&access_token="+ accessToken;

        return encryptToMD5(str).toLowerCase();
    }

    public static String encryptToMD5(String str) {
        return DigestUtils.md5Hex(str);
    }
}
