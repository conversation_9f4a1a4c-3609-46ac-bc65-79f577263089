package com.wosai.mpay.api.boc.b2c;

import com.wosai.mpay.api.lzccb.LZCCBProtocolFields;

import java.util.HashMap;
import java.util.Map;

public class BocpayRequestBuilder {
    private Map<String, Object> body;

    private Map<String, String> head;

    public BocpayRequestBuilder() {
        this.body = new HashMap<>();
        this.head = new HashMap<>();
    }

    public BocpayRequestBuilder(Map<String, String> head, Map<String, Object> body) {
        this.body = body;
        this.head = head;

    }

    public void setBody(String field, Object value) {
        body.put(field, value);
    }

    public void setHead(String field, String value) {
        head.put(field, value);
    }

    public Map<String, String> getHead() {
        return head;
    }

    public Map<String, Object> getBody() {
        return body;
    }

    public Map<String, Object> build() {
        HashMap<String, Object> hashMap = new HashMap<String, Object>() {{
            put(LZCCBProtocolFields.BODY, body);
            put(LZCCBProtocolFields.HEAD, head);
        }};
        return hashMap;
    }


}
