package com.wosai.mpay.api.boc.b2c;


import java.util.Arrays;
import java.util.List;

/**
 * Constants for Bank of China API integration
 */
public class BocpayConstant {

    /**
     * 支付宝
     */
    public static final String ZFBA = "ZFBA";

    /**
     * 微信支付
     */
    public static final String WEIX = "WEIX";

    /**
     * 银联二维码（不支持银联二维码反扫交易）
     */
    public static final String UPAY = "UPAY";

    /**
     * 数字货币
     */
    public static final String DZZF = "DZZF";

    /**
     * 澳门通支付
     */
    public static final String MPZF = "MPZF";

    public static final String CHARSET = "UTF-8";

    public static final String CCY = "156";


    /**
     * 支付宝消费
     */

    public static final String ALIPAY_CONSUME = "201001";


    /**
     * 微信消费
     */
    public static final String WEIXIN_CONSUME = "201002";

    /**
     * 银联二维码消费[暂不支持]
     */
    public static final String UPAY_CONSUME = "201003";


    /**
     * 当日撤销
     */
    public static final String CANCEL_DAY = "201004";


    /**
     * 退货
     */
    public static final String REFUND = "201005";

    /**
     * 支付结果查询
     */
    public static final String PAYMENT_RESULT_QUERY = "201006";

    /**
     * 退货/撤销结果查询
     */
    public static final String REFUND_CANCEL_RESULT_QUERY = "201007";


    /**
     * 通用支付结果查询
     */
    public static final String GENERAL_PAYMENT_RESULT_QUERY = "201011";


    /**
     * 数字货币消费
     */
    public static final String DIGITAL_CURRENCY_CONSUM = "`201012";


    /**
     * 标识报文版本，初始版本号为 1000
     */
    public static final String VERSION = "1000";


    /**
     * 普通商户
     */
    public static final String MERCHANT = "01";


    /**
     * 支付机构
     */
    public static final String PAYMENT_INSTITUTION = "02";


    /**
     * 时分秒
     */
    public static final String HHMMSS = "HHmmss";

    /**
     * 年月日时分秒
     */
    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    /**
     * 年月日
     */
    public static final String YYYYMMDD = "yyyyMMdd";

    /**
     * 年月日
     */
    public static final String YYMMDD = "yyMMdd";


    public static final String SUCCESS = "000000";


    public static final List<String> PROCESSING = Arrays.asList("888888", "99999");


    public static final List<String> SUCCESS_CODE_LIST = Arrays.asList("000000", "888888", "99999");


    public static final String CONTENT_TYPE = "text/xml;charset=utf-8";

    public static final String REQUEST_CODE = "11";

    public static final String RESPONSE_CODE = "12";


    public static final String METHOD = "method";


    public static final String POST = "post";

    public static final String GET = "get";

    public static final String Y = "Y";

    public static final String N = "N";


}
