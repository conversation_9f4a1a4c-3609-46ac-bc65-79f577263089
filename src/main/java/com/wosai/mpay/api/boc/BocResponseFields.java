package com.wosai.mpay.api.boc;

/**
 * Response parameter fields for BOC API
 */
public class BocResponseFields {

    /**
     * 处理状态，字符串长度为1，表示订单处理的状态
     * 可能的值包括：'S' - 成功，'F' - 失败，'P' - 进行中等
     */
    public static final String HANDLE_STATUS = "hdlSts";

    /**
     * 业务体报文块存在标识，字符串长度为1，标识业务体报文块是否存在
     * 可能的值包括：'Y' - 存在，'N' - 不存在
     */
    public static final String BUSINESS_DATA_FLAG = "bdFlg";

    /**
     * 报文处理返回码，字符串长度为N，表示报文处理的结果代码
     * 成功时返回码为'0000'，失败时返回具体的错误码
     */
    public static final String RETURN_CODE = "rtnCd";

    /**
     * 报文处理返回信息，字符串长度为N，表示报文处理的具体描述信息
     * 成功时返回信息为'处理成功'，失败时返回具体的错误描述
     */
    public static final String RETURN_MESSAGE = "rtnMsg";

    /**
     * 银行订单流水号，字符串长度为20，用于唯一标识银行生成的订单
     */
    public static final String ORDER_SEQUENCE = "orderSeq";

    /**
     * 二维码，字符串长度为300，仅在扫码支付和公众号支付时返回
     * 扫码支付商户需将此二维码展示在收银台，公众号支付则需跳转到该二维码链接
     */
    public static final String QR_CODE = "qrCode";

    /**
     * 二维码有效时间，字符串长度为14，仅在扫码支付时返回
     * 格式为：yyyyMMddHHmmss，表示二维码的有效截止时间
     */
    public static final String QR_CODE_VALID_TIME = "qrValidTime";

    /**
     * 错误码，字符串长度为20，当订单处理失败时返回具体的错误代码
     */
    public static final String TRANSACTION_CODE = "tranCode";

    /**
     * 错误描述，字符串长度为128，当订单处理失败时返回具体的错误描述信息
     */
    public static final String TRANSACTION_MESSAGE = "tranMsg";


    /**
     * 预支付交易会话标识，STRING(64)
     * 银联生成的预支付会话标识，用于后续接口调用中使用，该值有效期为2小时。
     */
    public static final String PREPAY_ID = "prepayId";

    /**
     * 小程序id，STRING(32)
     * 商户注册具有支付权限的小程序成功后即可获得小程序id。
     */
    public static final String WC_PAY_DATA_APP_ID = "wcPayDataAppId";

    /**
     * 时间戳，STRING(32)
     * 当前的时间。
     */
    public static final String WC_PAY_DATA_TIME_STAMP = "wcPayDataTimeStamp";

    /**
     * 随机字符串，STRING(32)
     * 随机字符串，不长于32位。推荐使用随机数生成算法。
     */
    public static final String WC_PAY_DATA_NONCE_STR = "wcPayDataNonceStr";

    /**
     * 订单详情扩展字符串，STRING(128)
     * 订单的详细信息扩展字符串。
     */
    public static final String WC_PAY_DATA_PACKAGE = "wcPayDataPackage";

    /**
     * 签名方式，STRING(32)
     * 使用的签名方式。
     */
    public static final String WC_PAY_DATA_SIGN_TYPE = "wcPayDataSignType";

    /**
     * 签名，STRING(64)
     * 交易的签名信息。
     */
    public static final String WC_PAY_DATA_PAY_SIGN = "wcPayDataPaySign";


    /**
     * 报文处理返回码
     * 类型：STRING(N)
     * 可为空
     */
    public static final String RTN_CD = "rtnCd";

    /**
     * 报文处理返回信息
     * 类型：STRING(N)
     * 订单处理结果的详细描述信息
     */
    public static final String RTN_MSG = "rtnMsg";


    /**
     * 商户号，即BOC商户ID
     * 数据类型：STRING(20)
     */
    public static final String MERCHANT_NO = "merchantNo";

    /**
     * 商户订单号，商户系统产生的订单号
     * 数据类型：STRING(19)
     */
    public static final String ORDER_NO = "orderNo";

    /**
     * 商户流水号，商户系统唯一，退款时作为mRefundSeq
     * 数据类型：STRING(30)
     */
    public static final String CUST_TRAN_ID = "custTranId";


    /**
     * 交易时间，支付时间（第三方返回的），格式为YYYYMMDDhhmiss
     * 数据类型：NUMBER(14)
     */
    public static final String TRAN_TIME = "tranTime";

    /**
     * 交易金额
     * 数据类型：STRING(13)
     */
    public static final String TRAN_AMOUNT = "tranAmount";


    /**
     * 银联订单号，当使用第三方支付（如微信、支付宝）时返回
     * 对于支付宝，交易支付和退款此值相同；对于微信，此值不同
     * 数据类型：STRING(32)
     */
    public static final String UNION_PAY_SEQ = "unionPaySeq";


    /**
     * 交易状态
     */
    public static final String TRAN_STATUS = "tranStatus";


    /**
     * 二维码超期标识
     */

    public static final String QR_OVERTIME_FLAG = "qrOvertimeFlag";


    /**
     * app支付请求参数字符串
     */
    public static final String ORDER_STR = "orderStr";

    /**
     * 返回操作类型
     */
    public static final String RETURN_ACT_FLAG = "returnActFlag";

    /**
     * 处理状态
     */
    public static final String DEAL_STATUS = "dealStatus";

    /**
     * 包体标志
     */
    public static final String BODY_FLAG = "bodyFlag";

    /**
     * 错误码
     */
    public static final String EXCEPTION = "exception";


    /**
     * 退款金额
     */
    public static final String REFUND_AMOUNT = "refundAmount";

    /**
     * 币种
     */
    public static final String CUR_CODE = "curCode";


    /**
     * 订单金额
     */
    public static final String ORDER_AMOUNT = "orderAmount";
    /**
     * 银行交易流水号
     */
    public static final String BANK_TRAN_SEQ = "bankTranSeq";

    /**
     * 中行签名数据
     */
    public static final String SIGN_DATA = "signData";


    /**
     * 支付宝交易号
     */
    public static final String ZFB_TRADE_NO = "zfbTradeNo";

    /**
     * 查询返回列表
     */
    public static final String ORDER_TRANS = "orderTrans";


    /**
     * 退款查询响应code
     */

    public static final String RESPONSE_CODE = "responseCode";

    /**
     * 退款查询响应message
     */
    public static final String RESPONSE_INFO = "responseInfo";


    /**
     * 响应码，字符串长度为N，交易结果代码，参考附录 3 响应码
     */
    public static final String RESPONSE_CODE_V2 = "RespCode";

    /**
     * 响应信息，字符串长度为N，交易结果描述，APP 端回显此信息
     */
    public static final String RESPONSE_INFO_V2 = "RespMsg";

    /**
     * 银行交易日期，字符串长度为8，银行返回的交易日期（成功时必返）
     */
    public static final String BANK_DATE = "BankDate";


    /**
     * 银行交易时间
     */
    public static final String BANK_TIME = "BankTime";

    /**
     * 外部系统日期
     */
    public static final String OUT_DATE = "OutDate";

    /**
     * 外部系统完成交易的日期
     */
    public static final String OUT_TIME = "OutTime";

    /**
     * 支付宝交易单号
     */
    public static final String TRADE_NO = "TradeNo";

    /**
     * 微信交易单号
     */
    public static final String TRADE_ID = "TradeId";

    /**
     * 第三方交易单号
     */
    public static final String OTH_TRADE_NO = "OthTradeNo";

    /**
     * 银联付款凭证号
     */
    public static final String PAY_VOUNUM = "PayVounum";

    /**
     * 银联支付订单号
     */
    public static final String PAY_NO = "PayNo";

    /**
     * 柜员号
     */
    public static final String OPER_NO = "OperNo";

    /**
     * 支付有效时间
     */
    public static final String PAY_VALID_TIME = "PayValidTime";

    /**
     * 优惠金额
     */
    public static final String DIS_PRICE = "DisPrice";

    /**
     * 备注
     */
    public static final String ATTACH = "Attach";

    /**
     * 是否需要返回优惠详情
     */
    public static final String NEED_COUPON_INFO = "NeedCouponInfo";

    /**
     * 订单优惠标记
     */
    public static final String GOODS_TAG = "GoodsTag";


    /**
     * 加密随机因子
     */
    public static final String ENCRYPT_RAND_NUM = "EncryptRandNum";

    /**
     * 密文数据
     */
    public static final String SECRET_TEXT = "SecretText";

    /**
     * 终端入网认证编号
     */
    public static final String NETWORK_LICENSE = "NetworkLicense";

    /**
     * 应结订单金额
     */
    public static final String SETTLEMENT_TOTAL_FEE = "SettlementTotalFee";

    /**
     * 代金券金额
     */
    public static final String COUPON_FEE = "CouponFee";

    /**
     * 优惠信息
     */
    public static final String PROMOTION_DETAIL = "PromotionDetail";


    /**
     * 券或者立减优惠 id
     */
    public static final String PROMOTION_ID = "PromotionId";

    /**
     * 优惠名称
     */
    public static final String NAME = "Name";

    /**
     * 优惠范围
     */
    public static final String SCOPE = "Scope";

    /**
     * 全场代金券
     */
    public static final String SCOPE_GLOBAL = "GLOBAL";

    /**
     * 单品优惠
     */
    public static final String SCOPE_SINGLE = "SINGLE";

    /**
     * 优惠类型
     */
    public static final String TYPE = "Type";

    /**
     * 充值型代金券
     */
    public static final String TYPE_COUPON = "COUPON";

    /**
     * 免充值型优惠券
     */
    public static final String TYPE_DISCOUNT = "DISCOUNT";

    /**
     * 优惠券面额
     */
    public static final String AMOUNT = "Amount";

    /**
     * 活动ID
     */
    public static final String ACTIVITY_ID = "ActivityId";

    /**
     * 微信出资
     */
    public static final String WX_PAY_CONTRIBUTE = "WxpayContribute";

    /**
     * 商户出资
     */
    public static final String MERCHANT_CONTRIBUTE = "MerchantContribute";

    /**
     * 其他出资
     */
    public static final String OTHER_CONTRIBUTE = "OtherContribute";


    /**
     * 买家用户账号
     */
    public static final String BUYER_ID = "BuyerId";


    /**
     * 用户子标识
     */
    public static final String SUB_OPEN_ID = "SubOpenId";


    /**
     * 交易状态
     * 网关交易流水号
     */

    public static final String TRAN_SEQ = "tranSeq";

    /**
     * 支付金额
     */
    public static final String PAY_AMOUNT = "payAmount";



    /**
     *支付时间
     */
    public static final String PAY_TIME = "pay_time";







}
