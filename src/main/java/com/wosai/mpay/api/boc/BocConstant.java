package com.wosai.mpay.api.boc;


import java.util.Arrays;
import java.util.List;

/**
 * Constants for Bank of China API integration
 */
public class BocConstant {


    /**
     * 处理状态 - 成功
     */
    public static final String HDL_STS_SUCCESS = "A";
    /**
     * 处理状态 - 失败
     */
    public static final String HDL_STS_FAIL = "B";
    /**
     * 处理状态 - 未明
     */
    public static final String HDL_STS_UNKNOWN = "K";

    /**
     * 业务体标识 - 有包体
     */
    public static final String BD_FLG_WITH_BODY = "0";
    /**
     * 业务体标识 - 无包体
     */
    public static final String BD_FLG_NO_BODY = "1";

    public static final String TRAN_SUCCESS = "1";

    public static final String TRAN_FAILURE = "0";

    /**
     * 微信小程序支付
     */
    public static final String WXXCX = "WXXCX";

    /**
     * 小程序内支付请传"WEB"
     */
    public static final String WEB = "WEB";


    /**
     * 支付宝小程序支付
     */
    public static final String ZFBXCX = "ZFBXCX";


    /**
     * 交易状态：
     * 0 - 待处理
     * 1 - 成功
     * 2 - 失败
     * 3 - 未明
     * 数据类型：STRING(4)
     */
    public static final String TRAN_STATUS_PENDING = "0";
    public static final String TRAN_STATUS_SUCCESS = "1";
    public static final String TRAN_STATUS_FAILURE = "2";
    public static final String TRAN_STATUS_UNKNOWN = "3";


    /**
     * 二维码超期标识，仅在循环外部使用
     * Y - 超期
     * N - 未超期
     * 数据类型：STRING(1)
     */
    public static final String QR_OVERTIME_FLAG_EXPIRED = "Y";
    public static final String QR_OVERTIME_FLAG_NOT_EXPIRED = "N";


    /**
     * 交易类型：
     * 01 - 支付
     * 03 - 批量退货
     * 06 - 联机退货
     * 数据类型：STRING(2)
     */
    public static final String TRAN_CODE_PAYMENT = "01";
    public static final String TRAN_CODE_BATCH_REFUND = "03";
    public static final String TRAN_CODE_ONLINE_REFUND = "06";


    /**
     * 返回操作类型
     * 1：支付结果通知
     * 2：电话订单结果通知
     * 3：退款结果通知
     * 4：订单查询结果
     */
    public static final String PAYMENT_RESULT_NOTIFICATION = "1";
    public static final String PHONE_ORDER_RESULT_NOTIFICATION = "2";
    public static final String REFUND_RESULT_NOTIFICATION = "3";
    public static final String ORDER_QUERY_RESULT = "4";


    /**
     * 处理状态
     * 0：成功
     * 1：失败
     * 注意：铁道部商户只有0、1两个状态
     */

    public static final String REFUND_SUCCESS = "0";
    public static final String REFUND_FAILURE = "1";
    public static final String REFUND_UNKNOWN = "2";


    /**
     * APP_ZFB：APP支付（支付宝）
     */
    public static final String TRADE_TYPE_APP_ZFB = "APP_ZFB";

    public static final String CONTENT_TYPE = "application/x-www-form-urlencoded";

    public static final String CHARSET = "UTF-8";


    /**
     * 手机标识
     */
    public static final String PHONE = "08";


    public static final String METHOD = "method";


    public static final String POST = "post";

    public static final String GET = "get";

    /**
     * 币种：人民币
     */
    public static final String CNY = "001";


    /**
     * 网上购物标识
     */
    public static final String ONLINE_PAY = "1";


    // 支付方式常量
    public static final String PAY_TYPE_ZFBA = "ZFBA"; // 支付宝
    public static final String PAY_TYPE_WEIX = "WEIX"; // 微信
    public static final String PAY_TYPE_UPAY = "UPAY"; // 银联二维码（暂不支持）
    public static final String PAY_TYPE_DZZF = "DZZF"; // 数字货币
    public static final String PAY_TYPE_MPZF = "MPZF"; // 澳门通支付


    // 预下单等签名算法
    public static final String DIGEST_ALGORITHM = "SHA1";
    /**
     * 签名算法
     */
    public static final String SIGNING_ALGORITHM = "SHA1withRSA";


    //B2C
    public static final String B2C_DIGEST_ALGORITHM = "SHA256";
    /**
     * 签名算法
     */
    public static final String B2C_SIGNING_ALGORITHM = "SHA256withRSA";


    public static final String SIGN_SPLIT_KEY_COLON = ":";
    public static final String SIGN_SPLIT_KEY_PIPE = "|";

    public static final List<String> REQUIRED_QUERY_SIGN_FIELDS = Arrays.asList(BocRequestFields.MERCHANT_NO, BocRequestFields.M_REFUND_SEQ);


    public static final List<String> QUERY_SIGN_FIELDS = Arrays.asList(BocRequestFields.MERCHANT_NO, BocRequestFields.ORDER_NO);


    public static final List<String> REFUND_SIGN_FIELDS = Arrays.asList(BocRequestFields.MERCHANT_NO, BocRequestFields.M_REFUND_SEQ, BocRequestFields.CUR_CODE, BocRequestFields.REFUND_AMOUNT, BocRequestFields.ORDER_NO);



    public static final List<String> ALIPAY_PRECREATE_SIGN_FIELDS = Arrays.asList(BocRequestFields.ORDER_NO, BocRequestFields.ORDER_TIME, BocRequestFields.CUR_CODE, BocRequestFields.ORDER_AMOUNT, BocRequestFields.MERCHANT_NO);


    public static final List<String> WEIXIN_PRECREATE_SIGN_FIELDS = Arrays.asList(BocRequestFields.ORDER_NO, BocRequestFields.ORDER_TIME, BocRequestFields.CUR_CODE, BocRequestFields.ORDER_AMOUNT, BocRequestFields.MERCHANT_NO, BocRequestFields.MCHT_CUST_IP);






}
