package com.wosai.mpay.api.boc;

import com.wosai.mpay.api.boc.security.PKCSTool;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Utility functions for Bank of China API
 */
public class BocUtil {

    private static final SafeSimpleDateFormat SAFE_SIMPLE_DATE_FORMAT = new SafeSimpleDateFormat("yyyyMMddHHmmss");


    public static String formatDate(Date date) {
        if (date == null) {
            return null;
        }
        return SAFE_SIMPLE_DATE_FORMAT.format(date);
    }

    /**
     * 将body数据转换为管道符分隔的字符串
     *
     * @param body Map<String, String> 类型的body数据
     * @return 管道符分隔的字符串
     */
    public static String convertBodyToPipeSeparatedString(Map<String, String> body, String splitKey) {
        StringBuilder bodyString = new StringBuilder();
        for (String key : body.keySet()) {
            if (body.containsKey(key)) {
                bodyString.append(body.get(key)).append(splitKey);
            } else {
                bodyString.append(splitKey); // 如果没有该字段，则添加一个空字符串
            }
        }
        // 移除最后一个多余的管道符
        return bodyString.deleteCharAt(bodyString.length() - 1).toString();
    }


    /**
     * 将body数据转换为POST请求的POST数据
     *
     * @param body
     * @throws UnsupportedEncodingException
     */
    public static String convertToPostData(Map<String, String> body) throws UnsupportedEncodingException {
        StringBuilder postData = new StringBuilder();
        for (Map.Entry<String, String> param : body.entrySet()) {
            if (postData.length() != 0) postData.append('&');
            postData.append(URLEncoder.encode(param.getKey(), "UTF-8"));
            postData.append('=');
            postData.append(URLEncoder.encode(param.getValue(), "UTF-8"));
        }
        return postData.toString();
    }


    public static String buildBocRequestXml(Map<String, String> msgHeader, Map<String, String> msgBody, String keyStorePfx, String keyStorePassword, String keyPassword) throws Exception {
        StringBuilder xmlBuilder = new StringBuilder();
        // 开始根节点
        xmlBuilder.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");

        StringBuilder rootBuilder = new StringBuilder();
        rootBuilder.append("<root>");

        // 添加报文头节点
        rootBuilder.append("<MsgHeader>");
        for (Map.Entry<String, String> entry : msgHeader.entrySet()) {
            rootBuilder.append("<").append(entry.getKey()).append(">").append(entry.getValue()).append("</").append(entry.getKey()).append(">");
        }
        rootBuilder.append("</MsgHeader>");

        // 添加报文体节点
        rootBuilder.append("<MsgBody>");
        for (Map.Entry<String, String> entry : msgBody.entrySet()) {
            rootBuilder.append("<").append(entry.getKey()).append(">").append(entry.getValue()).append("</").append(entry.getKey()).append(">");
        }
        rootBuilder.append("</MsgBody>");

        // 结束根节点
        rootBuilder.append("</root>");
        byte plainTextByte[] = rootBuilder.toString().getBytes(BocConstant.CHARSET);
        //获取私钥证书
        PKCSTool tool = BocCache.getPKCSSigner(keyStorePfx, keyStorePassword, keyPassword);
        String signData = tool.p7BocpaySign(plainTextByte, BocConstant.B2C_DIGEST_ALGORITHM, BocConstant.B2C_SIGNING_ALGORITHM);
        xmlBuilder.append(rootBuilder);
        xmlBuilder.append("\n");
//        // 添加签名内容
        xmlBuilder.append("{S:").append(signData).append("}");
        return xmlBuilder.toString();
    }

    /**
     * 提取 XML 内容部分
     */
    public static String extractXmlContent(String input) {
        int startIndex = input.indexOf("<root");
        int endIndex = input.indexOf("</root>");

        if (startIndex != -1 && endIndex != -1) {
            return input.substring(startIndex, endIndex + "</root>".length());
        }
        return "XML内容未找到";
    }

    /**
     * 提取标签内容部分
     */
    public static String extractTagContent(String input) {
        // 查找标签开始位置
        int startIndex = input.indexOf("{S:");
        if (startIndex != -1) {
            // 从 "{S:" 开始到字符串结束
            return input.substring(startIndex);
        }
        return "标签内容未找到";
    }

    /**
     * 解析 XML 为 DOM 文档
     */
    public static Document parseXml(String xmlString) throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();

        // 安全设置
        factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
        factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
        factory.setXIncludeAware(false);
        factory.setExpandEntityReferences(false);

        DocumentBuilder builder = factory.newDocumentBuilder();
        return builder.parse(new ByteArrayInputStream(xmlString.getBytes("UTF-8")));
    }

    /**
     * 提取指定元素的数据为 Map
     */
    public static Map<String, String> extractElementData(Document document, String elementName) {
        Map<String, String> map = new HashMap<>();
        NodeList elementList = document.getElementsByTagName(elementName);

        if (elementList.getLength() > 0 && elementList.item(0).getNodeType() == Node.ELEMENT_NODE) {
            Element element = (Element) elementList.item(0);
            NodeList children = element.getChildNodes();

            for (int i = 0; i < children.getLength(); i++) {
                Node child = children.item(i);
                if (child.getNodeType() == Node.ELEMENT_NODE) {
                    String key = child.getNodeName();
                    String value = child.getTextContent().trim();
                    map.put(key, value);
                }
            }
        }
        return map;
    }

    /**
     * 提取标签数据
     */
    public static Map<String, String> extractTagData(String tagContent) {
        Map<String, String> result = new HashMap<>();

        // 正则表达式匹配 {S:XXXX} 格式
        Pattern pattern = Pattern.compile("\\{S:(.*?)\\}");
        Matcher matcher = pattern.matcher(tagContent);

        if (matcher.find()) {
            result.put("S", matcher.group(1));
        } else {
            // 备用匹配方案
            pattern = Pattern.compile("\\{S:(.*)");
            matcher = pattern.matcher(tagContent);

            if (matcher.find()) {
                // 提取到结尾的所有内容
                String value = matcher.group(1);
                // 去除可能的结尾大括号
                if (value.endsWith("}")) {
                    value = value.substring(0, value.length() - 1);
                }
                result.put("S", value);
            }
        }

        // 如果没有匹配到，尝试简单提取
        if (result.isEmpty() && tagContent.contains("S:")) {
            int start = tagContent.indexOf("S:") + 2;
            String value = tagContent.substring(start).trim();
            // 去除可能的结尾大括号
            if (value.endsWith("}")) {
                value = value.substring(0, value.length() - 1);
            }
            result.put("S", value);
        }

        return result;
    }

    /**
     * 取1到number之间的随机数
     *
     * @param number
     * @return
     */
    public static int getRandomNumber(int number) {
        Random random = new Random();
        return random.nextInt(number) + 1;
    }


}
