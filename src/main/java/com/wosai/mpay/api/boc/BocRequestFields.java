package com.wosai.mpay.api.boc;

import java.util.HashMap;
import java.util.Map;

/**
 * Request parameter fields for BOC API
 */
public class BocRequestFields {
    /**
     * 商户号，最大长度为20的字符串
     */
    public static final String MERCHANT_NO = "merchantNo";

    /**
     * 支付类型，最大长度为10的字符串
     */
    public static final String PAY_TYPE = "payType";

    /**
     * 商户订单号，最大长度为30的字符串
     */
    public static final String ORDER_NO = "orderNo";

    /**
     * 订单币种，最大长度为3的字符串
     */
    public static final String CUR_CODE = "curCode";

    /**
     * 订单金额，最大长度为13的字符串
     */
    public static final String ORDER_AMOUNT = "orderAmount";

    /**
     * 订单时间，长度为14的数字
     */
    public static final String ORDER_TIME = "orderTime";

    /**
     * 订单说明，最大长度为200的字符串
     */
    public static final String ORDER_NOTE = "orderNote";

    /**
     * 商户接收通知URL，最大长度为200的字符串
     */
    public static final String ORDER_URL = "orderUrl";

    /**
     * 超时时间，长度为14的数字
     */
    public static final String ORDER_TIMEOUT_DATE = "orderTimeoutDate";

    /**
     * 商户签名数据，最大长度为4000的字符串
     */
    public static final String SIGN_DATA = "signData";


    /**
     * 银行订单流水号，用于标识银行系统中的订单唯一流水号，最大长度为20个数字。
     */
    public static final String ORDER_SEQ = "orderSeq";

    /**
     * 银行卡类别，用于标识银行卡的类型，最大长度为2个字符。
     */
    public static final String CARD_TYP = "cardTyp";

    /**
     * 支付时间，用于记录支付发生的时间，格式为14位数字。
     */
    public static final String PAY_TIME = "payTime";

    /**
     * 订单状态，用于表示订单当前的状态，最大长度为2个字符。
     */
    public static final String ORDER_STATUS = "orderStatus";

    /**
     * 支付金额，用于记录实际支付的金额，最大长度为13个字符。
     */
    public static final String PAY_AMOUNT = "payAmount";

    /**
     * 客户支付IP地址，用于记录客户进行支付时的IP地址，最大长度为40个字符。
     */
    public static final String ORDER_IP = "orderIp";

    /**
     * 客户浏览器Refer信息，用于记录客户进行支付时的浏览器Refer信息，最大长度为200个字符。
     */
    public static final String ORDER_REFER = "orderRefer";

    /**
     * 银行交易流水号，用于标识银行交易的唯一流水号，最大长度为22个字符。
     */
    public static final String BANK_TRAN_SEQ = "bankTranSeq";

    /**
     * 返回操作类型，用于表示支付结果返回的操作类型，最大长度为1个字符。
     */
    public static final String RETURN_ACT_FLAG = "returnActFlag";

    /**
     * 电话号码，用于记录客户的联系电话，最大长度为50个字符。
     */
    public static final String PHONE_NUM = "phoneNum";


    /**
     * 交易终端类型
     * 数据类型：STRING(2)
     * 业务说明：08：手机
     */
    public static final String TERMINAL_CHNL = "terminalChnl";

    /**
     * 商户获取的客户IP地址
     * 数据类型：STRING(39)
     * 业务说明：商户获取的客户IP地址，仅支持IPv4地址
     */
    public static final String MCHT_CUST_IP = "mchtCustIP";

    /**
     * 交易类型
     * 数据类型：STRING(16)
     * 业务说明：WXAPP ：微信公众号支付
     */
    public static final String TRADE_TYPE = "tradeType";

    /**
     * 设备号
     * 数据类型：STRING(32)
     * 业务说明：第三方支付（微信）该字段必送
     * 终端设备号(门店号或收银设备ID)，公众号内支付请传"WEB"
     */
    public static final String DEVICE_INFO = "deviceInfo";

    /**
     * 商品描述
     * 数据类型：STRING(128)
     * 业务说明：第三方支付（微信）该字段必送
     * 商品描述交易字段格式根据不同的应用场景按照以下格式：
     * 公众号——传入公众号名称-实际商品名称，例如：腾讯形象店- image-QQ公仔；
     */
    public static final String BODY = "body";

    /**
     * 附加数据
     * 数据类型：STRING(127)
     * 业务说明：第三方支付（微信）该字段选送
     * 附加数据，该字段主要用于商户携带订单的自定义数据
     */
    public static final String ATTACH = "attach";

    /**
     * 终端ip
     * 数据类型：STRING(16)
     * 业务说明：第三方支付（微信）该字段必送
     * APP和网页支付提交用户端ip
     */
    public static final String SPBILL_CREATE_IP = "spbillCreateIp";

    /**
     * 指定支付方式
     * 数据类型：STRING(32)
     * 业务说明：第三方支付（微信）该字段选送
     * no_credit-指定不能使用信用卡支付
     */
    public static final String LIMIT_PAY = "limitPay";

    /**
     * 证件类型
     * 数据类型：STRING(32)
     * 业务说明：第三方支付（微信）该字段选送
     * 证件类型 目前只支持身份证:IDCARD
     * 实名支付使用
     */
    public static final String IDENTITY_TYPE = "identityType";

    /**
     * 证件号
     * 数据类型：STRING(32)
     * 业务说明：第三方支付（微信）该字段选送
     * 证件号，目前仅支持身份证号
     * 实名支付使用
     */
    public static final String IDENTITY_NUMBER = "identityNumber";

    /**
     * 姓名
     * 数据类型：STRING(32)
     * 业务说明：第三方支付（微信）该字段选送
     * 证件姓名
     * 实名支付使用
     */
    public static final String IDENTITY_NAME = "identityName";


    /**
     * 二维码有效时间，第三方扫码支付该字段选送。
     * 长度：8。
     * 必填：否 (O)。
     * 单位：秒，如果商户上送该字段，二维码的有效期使用商户上送值；如果商户不送二维码默认有效期10分钟。
     */
    public static final String QR_VALID_TIME = "qrValidTime";


    /**
     * 商户退款交易流水号，必填，商户系统产生的交易流水号，格式要求为：商户号（merchantNo）后6位+商户按自己规则生成的退款交易流水号；eg：221338123456789
     */
    public static final String M_REFUND_SEQ = "mRefundSeq";


    /**
     * 退款金额，退款金额格式：整数位不前补零,小数位补齐2位；即：不超过10位整数位+1位小数点+2位小数；无效格式如123，.10，1.1,有效格式如1.00，0.10
     */
    public static final String REFUND_AMOUNT = "refundAmount";

    /**
     * 订单标题
     * STRING(256) R 第三方支付（支付宝）该字段必送
     * 对于一笔交易的具体描述信息。 如果是多种商品，请将商品描述字符串累加传给body。
     * 商品的标题/交易标题/订单标题/订单关键字等。
     */
    public static final String ORDER_SUBJECT = "subject";

    /**
     * 返回商户网站的地址
     * STRING(256) R (支付宝H5支付必送)
     * 用户付款中途退出返回商户网站的地址
     * eg.http://www.taobao.com/product/113714.html
     */
    public static final String QUIT_URL = "quitUrl";

    /**
     * 回跳地址
     * STRING(256) R (支付宝H5支付必送)
     * HTTP/HTTPS 开头字符串
     * eg. https://m.alipay.com/Gk8NF23
     * 支付宝服务器主动通知商户服务器里指定的页面http/https路径。
     */
    public static final String RETURN_URL = "returnUrl";


    /**
     * 支付卡号，最大长度为19个字符。第三方支付时为空，需要持卡人卡户信息的商户例如人行通过的第三方支付公司，银行在返回结果时提供该项，否则用****表示。
     */
    public static final String ACCT_NO = "acctNo";

    /**
     * 持卡人姓名，最大长度为30个字符。第三方支付时为空，需要持卡人卡户信息的商户例如人行通过的第三方支付公司，银行在返回结果时提供该项，否则用****表示。
     */
    public static final String HOLDER_NAME = "holderName";


    /**
     * 银联订单号，最大长度为32个字符。第三方支付时为空，需要银联支付的商户，银联在返回结果时提供该项，否则用****表示。
     */
    public static final String UNION_PAY_SEQ = "unionPaySeq";

    /**
     * 买家用户号 第三方支付（支付宝）该字段必送
     */

    public static final String BUYER_ID = "buyerId";

    /**
     * 微信分配的子商户小程序appid
     */
    public static final String SUB_APPID = "subAppid";

    /**
     * 第三方支付（微信）该字段必送
     */
    public static final String SUB_OPENID = "subOpenid";


    /**
     * 查询标识
     */
    public static final String QUERY_FLAG = "queryFlag";


    /**
     * 商户号
     */
    public static final String MER_ID = "MerId";

    /**
     * 终端号
     */
    public static final String TERM_ID = "TermId";

    /**
     * 前端唯一流水号
     */
    public static final String PAY_LS = "PayLs";

    /**
     * 终端流水号
     */

    public static final String TRACE_NO = "TraceNo";

    /**
     * 批次号
     */
    public static final String BATCH_NO = "BatchNo";

    /**
     * 支付方式
     */
    public static final String PAY_TYPE_V2 = "PayType";


    /**
     * 支付授权码
     */
    public static final String AUTH_CODE = "AuthCode";

    /**
     * 交易金额
     */
    public static final String TRAN_AMT = "TranAmt";


    /**
     * 交易币种
     */
    public static final String CCY_CODE = "CcyCode";

    /**
     * signFields,用于标识签名的key 逗号分隔字符串
     */

    public static final String SIGN_FIELDS = "signFields";


    /**
     * 中行appId
     */
    public static final String SUB_APP_ID = "subAppid";

    /**
     * 报文版本
     */
    public static final String MSG_VER = "MsgVer";

    /**
     * 前端系统日期
     */
    public static final String IN_DATE = "InDate";

    /**
     * 前端系统时间
     */
    public static final String IN_TIME = "InTime";

    /**
     * 交易编码
     */
    public static final String TRAN_ID = "TranId";

    /**
     * 业务编号
     */
    public static final String BUS_ID = "BussId";


    /**
     * 接入商户类型
     */
    public static final String MER_TYPE = "MerType";


    /**
     * 报文方向
     */
    public static final String DRCTN = "Drctn";


    /**
     * IP地址
     */
    public static final String IP = "IP";



    /**
     * 加密密钥
     */
    public static final String ENC_KEY = "EncKey";

    /**
     * 公钥
     */
    public static final String PUB_KEY_ID = "PubKeyId";

    /**
     * 前端系统标识
     */
    public static final String SYS_NO = "SysNo";

    /**
     * 通讯密钥索引
     */
    public static final String ZEK_KEY_IND = "ZekKeyInd";





}
