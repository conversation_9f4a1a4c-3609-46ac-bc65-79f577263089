package com.wosai.mpay.api.boc.b2c;

/**
 * Request parameter fields for BOC API
 */
public class BocpayRequestFields {

    /**
     * 报文版本
     */
    public static final String MSG_VER = "MsgVer";

    /**
     * 前端系统日期
     */
    public static final String IN_DATE = "InDate";

    /**
     * 前端系统时间
     */
    public static final String IN_TIME = "InTime";

    /**
     * 交易编码
     */
    public static final String TRAN_ID = "TranId";

    /**
     * 业务编号
     */
    public static final String BUS_ID = "BussId";


    /**
     * 接入商户类型
     */
    public static final String MER_TYPE = "MerType";


    /**
     * 报文方向
     */
    public static final String DRCTN = "Drctn";


    /**
     * IP地址
     */
    public static final String IP = "IP";


    /**
     * 加密密钥
     */
    public static final String ENC_KEY = "EncKey";

    /**
     * 公钥
     */
    public static final String PUB_KEY_ID = "PubKeyId";

    /**
     * 前端系统标识
     */
    public static final String SYS_NO = "SysNo";

    /**
     * 通讯密钥索引
     */
    public static final String ZEK_KEY_IND = "ZekKeyInd";


    /**
     * 商户号
     */
    public static final String MER_ID = "MerId";

    /**
     * 终端号
     */
    public static final String TERM_ID = "TermId";

    /**
     * 前端唯一流水号
     */
    public static final String PAY_LS = "PayLs";

    /**
     * 终端流水号
     */

    public static final String TRACE_NO = "TraceNo";

    /**
     * 批次号
     */
    public static final String BATCH_NO = "BatchNo";

    /**
     * 支付方式
     */
    public static final String PAY_TYPE_V2 = "PayType";


    /**
     * 支付授权码
     */
    public static final String AUTH_CODE = "AuthCode";

    /**
     * 交易金额
     */
    public static final String TRAN_AMT = "TranAmt";


    /**
     * 交易币种
     */
    public static final String CCY_CODE = "CcyCode";

    /**
     * signFields,用于标识签名的key 逗号分隔字符串
     */

    public static final String SIGN_FIELDS = "signFields";


    /**
     * 中行appId
     */
    public static final String SUB_APP_ID = "SubAppId";

    public static final String MER_ORDER_NO = "MerOrderNo";


    public static final String BODY = "Body";


    /**
     * 原支付方式
     */
    public static final String OLD_PAY_TYPE = "OldPayType";

    /**
     * 原银行交易日期
     */
    public static final String OLD_BANK_DATE = "OldBankDate";

    /**
     * 原商户订单号
     */
    public static final String OLD_ORDER_NO = "OldOrderNo";

    /**
     * 原付款凭证号
     */
    public static final String OLD_PAY_VOUNUM = "OldPayVounum";

    /**
     * 商户退货订单号
     */
    public static final String REFUND_ORDER_NO = "RefundOrderNo";

    /**
     * 退款金额
     */
    public static final String REFUND_AMT = "RefundAmt";


    /**
     * 原交易编码
     */
    public static final String OLD_TRAN_ID = "OldTranId";

    /**
     * 原唯一流水号
     */
    public static final String OLD_PAY_LS = "OldPayLs";


    /**
     * 原交易金额
     */
    public static final String OLD_TRAN_AMT = "OldTranAmt";

    /**
     * 原交易币种
     */
    public static final String OLD_CCY_CODE = "OldCcyCode";

    /**
     * 是否需要返回优惠详情
     */
    public static final String NEED_COUPON_INFO = "NeedCouponInfo";


    public static final String MSG_HEADER = "MsgHeader";

    public static final String MSG_BODY = "MsgBody";

    public static final String OPER_NO = "OperNo";

    /**
     * 支付有效时间
     */
    public static final String PAY_VALID_TIME = "PayValidTime";


    public static final String ATTACH = "Attach";


    /**
     * 订单优惠标记
     */
    public static final String GOODS_TAG = "GoodsTag";


    public static final String DEVICE_TYPE = "DeviceType";


    /**
     * 密文数据
     */
    public static final String SECRET_TEXT = "SecretText";

    /**
     * 加密随机因子
     */
    public static final String ENCRYPT_RAND_NUM = "EncryptRandNum";

    /**
     * 终端入网认证编号
     */
    public static final String NETWORK_LICENSE = "NetworkLicense";


}
