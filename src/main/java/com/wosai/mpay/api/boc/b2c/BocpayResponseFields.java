package com.wosai.mpay.api.boc.b2c;

/**
 * Response parameter fields for BOC API
 */
public class BocpayResponseFields {

    /**
     * 响应码
     */
    public static final String RESP_CODE = "RespCode";

    /**
     * 响应信息
     */
    public static final String RESP_MSG = "RespMsg";

    /**
     * 银行交易日期
     */
    public static final String BANK_DATE = "BankDate";


    /**
     * 银行交易时间
     */
    public static final String BANK_TIME = "BankTime";

    /**
     * 外部系统日期
     */
    public static final String OUT_DATE = "OutDate";

    /**
     * 外部系统完成交易的日期，如外部系
     */
    public static final String OUT_TIME = "OutTime";

    /***
     * 商户订单号
     */
    public static final String ORDER_NO = "OrderNo";

    /**
     * 支付宝交易单号
     */
    public static final String TRADE_NO = "TradeNo";

    /**
     * 微信交易单号
     */
    public static final String TRADE_ID = "TradeId";

    /**
     * 第三方交易单号
     */
    public static final String OTH_TRADE_NO = "OthTradeNo";

    /**
     * 银联付款凭证号
     */
    public static final String PAY_VOUNUM = "PayVounum";

    /**
     * 银联支付订单号
     */
    public static final String PAY_NO = "PayNo";

    /**
     * 柜员号
     */
    public static final String OPER_NO = "OperNo";

    /**
     * 支付有效时间
     */
    public static final String PAY_VALID_TIME = "PayValidTime";


    /**
     * 设备类型
     */
    public static final String DEVICE_TYPE = "DeviceType";

    /**
     * 优惠金额
     */
    public static final String DIS_PRICE = "DisPrice";

    /**
     * 备注
     */
    public static final String ATTACH = "Attach";
    /**
     * 是否需要返回优惠
     * 详情
     */
    public static final String NEED_COUPON_INFO = "NeedCouponInfo";




    /**
     * 应结订单金额
     */
    public static final String SETTLEMENT_TOTAL_FEE = "SettlementTotalFee";

    /**
     * 代金券金额
     */
    public static final String COUPON_FEE = "CouponFee";

    /**
     * 优惠信息
     */
    public static final String PROMOTION_DETAIL = "PromotionDetail";

    /**
     * 优惠信息
     */
    public static final String PROMOTION_ID = "PromotionId";

    /**
     * 优惠名称
     */
    public static final String PROMOTION_NAME = "Name";

    /**
     * 优惠范围
     */
    public static final String PROMOTION_SCOPE = "Scope";

    /**
     * 优惠类型
     */
    public static final String PROMOTION_TYPE = "Type";

    /**
     * 优惠券面额
     */
    public static final String PROMOTION_AMOUNT = "Amount";

    /**
     * 活动 ID
     */
    public static final String PROMOTION_ACTIVITY_ID = "ActivityId";

    /**
     * 微信出资
     */
    public static final String PROMOTION_WXPAY_CONTRIBUTE = "WxpayContribute";


    public static final String BUYER_ID = "BuyerId";

    public static final String SUB_OPEN_ID = "SubOpenId";

    /**
     * 商户出资
     */
    public static final String MERCHANT_CONTRIBUTE = "MerchantContribute";

    /**
     * 其他出资
     */
    public static final String OTHER_CONTRIBUTE = "OtherContribute";


    /**
     * 应结退款金额
     */
    public static final String SETTLEMENT_REFUND_FEE = "SettlementRefundFee";


    /**
     * 代金券退款金额
     */

    public static final String COUPON_REFUND_FEE = "CouponRefundFee";

    /**
     * 优惠退款详情
     */
    public static final String REFUND_DETAIL = "RefundDetail";


    /**
     * 优惠券退款额
     */
    public static final String REFUND_AMOUNT = "RefundAmount";

    /**
     * 原交易响应码
     */
    public static final String OLD_RESP_CODE = "OldRespCode";

    /**
     * 原交易响应信息
     */
    public static final String OLD_RESP_MSG = "OldRespMsg";


    /**
     * 原银行交易日期
     */

    public static final String OLD_BANK_DATE = "OldBankDate";


    /**
     * 原银行交易时间
     */
    public static final String OLD_BANK_TIME = "OldBankTime";

    /**
     * 原微信交易单号
     */
    public static final String OLD_TRADE_ID = "OldTradeId";


    /**
     * 原支付宝交易单号
     */
    public static final String OLD_TRADE_NO = "OldTradeNo";


    /**
     * 原第三方交易单号
     */
    public static final String OLD_THIRD_TRADE_NO = "OldThirdTradeNo";


    /**
     * 原银联付款凭证号
     */
    public static final String OLD_PAY_VOUNUM = "OldPayVounum";


    /**
     * 原银联支付订单号
     */
    public static final String OLD_PAY_NO = "OldPayNo";


    /**
     * 原终端流水号
     */
    public static final String OLD_TRACE_NO = "OldTraceNo";


    /**
     * 原商户订单号
     */
    public static final String OLD_ORDER_NO = "OldOrderNo";


    /**
     * 原优惠金额
     */
    public static final String OLD_DIS_PRICE = "OldDisPrice";


    /**
     * 外部商户订单号
     */
    public static final String MER_ORDER_NO = "MerOrderNo";

    /**
     * 原实付金额
     */
    public static final String OLD_CASH_AMOUNT = "OldCashAmount";

    /**
     * 检查终端号标志
     */
    public static final String CHECK_TERM_ID = "CheckTermId";


    /**
     * 原支付方式
     */

    public static final String OLD_PAY_TYPE = "OldPayType";


    /**
     * 原交易终端号
     */
    public static final String OLD_TERM_ID = "OldTermId";

    /**
     * 原交易金额
     */
    public static final String OLD_TRAN_AMT = "OldTranAmt";


    /**
     * 原交易币种
     */
    public static final String OLD_CCY_CODE = "OldCcyCode";


    public static final String MSG_HEADER = "MsgHeader";

    public static final String MSG_BODY = "MsgBody";

    /**
     * 退货单号
     */
    public static final String REFUND_ORDER_NO = "RefundOrderNo";


    /**
     * 查询时的退款单号
     */
    public static final String OLD_REFUND_ORDER_NO = "OldRefundOrderNo";


    /**
     * 商户号
     */
    public static final String MER_ID = "MerId";

    /**
     * 终端号
     */
    public static final String TERM_ID = "TermId";

    /**
     * 前端唯一流水号
     */
    public static final String PAY_LS = "PayLs";

    /**
     * 终端流水号
     */

    public static final String TRACE_NO = "TraceNo";

    /**
     * 批次号
     */
    public static final String BATCH_NO = "BatchNo";


    /**
     * 交易金额
     */
    public static final String TRAN_AMT = "TranAmt";


    /**
     * 中行appId
     */
    public static final String SUB_APP_ID = "SubAppId";


    /**
     * 原交易编码
     */
    public static final String OLD_TRAN_ID = "OldTranId";


    /**
     * 退款金额
     */
    public static final String REFUND_AMT = "RefundAmt";


    /**
     * 原唯一流水号
     */
    public static final String OLD_PAY_LS = "OldPayLs";


}
