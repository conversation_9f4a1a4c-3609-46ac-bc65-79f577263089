package com.wosai.mpay.api.boc.b2c;

import com.wosai.mpay.api.SSLEnvFlag;
import com.wosai.mpay.api.boc.BocCache;
import com.wosai.mpay.api.boc.BocClient;
import com.wosai.mpay.api.boc.BocUtil;
import com.wosai.mpay.api.boc.security.PKCSTool;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.SSLUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import java.util.HashMap;
import java.util.Map;

public class BocpayClient {

    public static final Logger logger = LoggerFactory.getLogger(BocpayClient.class);


    private int connectTimeout = 10000;
    private int readTimeout = 30000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map<String, Object> call(Map<String, Object> bocRequest, String serviceUrl, String keyStorePfx, String keyStorePassword, String keyPassword
            , String rootCertificate, String method) throws MpayApiNetworkError {
        Map<String, String> body = MapUtils.getMap(bocRequest, BocpayProtocolFields.BODY);
        Map<String, String> head = MapUtils.getMap(bocRequest, BocpayProtocolFields.HEAD);
        String request = "";
        try {
            request = BocUtil.buildBocRequestXml(head, body, keyStorePfx, keyStorePassword, keyPassword);
        } catch (Exception e) {
            logger.error("Error while building XML request", e);
            throw new MpayApiNetworkError("Error while building XML request", e);
        }
        logger.info("request {}", request);
        SSLContext sslContext = SSLUtil.getUnsafeSSLContext();
        HostnameVerifier hostnameVerifier = SSLUtil.getUnsafeHostnameVerifier(SSLEnvFlag.getNotVerifyHostNames());
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Length", request.length() + "");
        headers.put("Content-Type", BocpayConstant.CONTENT_TYPE);
        headers.put(BocpayRequestFields.TRAN_ID, MapUtils.getString(head, BocpayRequestFields.TRAN_ID));
        logger.info("headers {}", headers);
        Map<String, Object> result = HttpClientUtils.doCommonMethod(BocClient.class.getName(), sslContext, hostnameVerifier, serviceUrl, null, BocpayConstant.CONTENT_TYPE, request, headers, BocpayConstant.CHARSET, connectTimeout, readTimeout, method);
        logger.info("result {}", result);
        String xml = MapUtils.getString(result, "body");
        // 分离 XML 和标签数据
        String xmlContent = BocUtil.extractXmlContent(xml);
        String tagContent = BocUtil.extractTagContent(xml);
        // 提取 {S:} 标签数据
        Map<String, String> tagData = BocUtil.extractTagData(tagContent);
        String responseSignature = MapUtils.getString(tagData, BocpayProtocolFields.SIGN);
        PKCSTool verifyTool = BocCache.getPKCSVerifier(rootCertificate);
        try {
            verifyTool.p7Verify(responseSignature, xmlContent.getBytes(BocpayConstant.CHARSET));
        } catch (Exception e) {
            logger.error("Error while verifying response signature", e);
            throw new MpayApiNetworkError("Error while verifying response signature", e);
        }

        // 解析 XML 数据
        Document document = null;
        try {
            document = BocUtil.parseXml(xmlContent);
        } catch (Exception e) {
            throw new MpayApiNetworkError("Error while parsing XML response", e);
        }
        Map<String, String> msgHeaderMap = BocUtil.extractElementData(document, BocpayResponseFields.MSG_HEADER);

        Map<String, String> msgBodyMap = BocUtil.extractElementData(document, BocpayResponseFields.MSG_BODY);

        Map<String, Object> response = new HashMap<>();

        response.put(BocpayProtocolFields.HEAD, msgHeaderMap);
        response.put(BocpayProtocolFields.BODY, msgBodyMap);
        logger.info("response {}", response);
        return response;
    }

}
