package com.wosai.mpay.api.boc;

import com.wosai.mpay.api.SSLEnvFlag;
import com.wosai.mpay.api.boc.security.PKCSTool;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Client for Bank of China API operations
 */
public class BocClient {

    public static final Logger logger = LoggerFactory.getLogger(BocClient.class);


    private int connectTimeout = 10000;
    private int readTimeout = 30000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map<String, Object> call(Map<String, Object> bocRequest, String serviceUrl, String keyStorePfx, String keyStorePassword, String keyPassword
            , String rootCertificate, List<String> signFields, String method, boolean queryFlag) throws MpayApiNetworkError {
        Map<String, String> body = MapUtils.getMap(bocRequest, BocProtocolFields.BODY);
        Map<String, String> head = MapUtils.getMap(bocRequest, BocProtocolFields.HEAD);

        logger.info("request {}", bocRequest);
        StringBuilder plainTextBuilder = new StringBuilder();
        String splitKey = queryFlag ? BocConstant.SIGN_SPLIT_KEY_COLON : BocConstant.SIGN_SPLIT_KEY_PIPE;
        for (String signField : signFields) {
            plainTextBuilder.append(MapUtils.getString(body, signField)).append(splitKey);
        }
        String plainText = plainTextBuilder.toString().substring(0, plainTextBuilder.length() - 1);
        String signData = "";
        //签名
        try {
            byte plainTextByte[] = plainText.getBytes(BocConstant.CHARSET);
            //获取私钥证书
            PKCSTool tool = BocCache.getPKCSSigner(keyStorePfx, keyStorePassword, keyPassword);
            signData = tool.p7Sign(plainTextByte, BocConstant.DIGEST_ALGORITHM, BocConstant.SIGNING_ALGORITHM);
        } catch (Exception e) {
            logger.error("获取签名数据错误", e);
            throw new MpayApiNetworkError("获取签名数据错误");
        }
        body.put(BocRequestFields.SIGN_DATA, signData);

        String postData = "";
        try {
            postData = BocUtil.convertToPostData(body);
        } catch (Exception e) {
            logger.error("body数据转换为表单数据错误", e);
            throw new MpayApiNetworkError("body数据转换为表单数据错误", e);
        }
        SSLContext sslContext = SSLUtil.getUnsafeSSLContext();
        HostnameVerifier hostnameVerifier = SSLUtil.getUnsafeHostnameVerifier(SSLEnvFlag.getNotVerifyHostNames());
        Map<String, Object> result = HttpClientUtils.doCommonMethod(BocClient.class.getName(), sslContext, hostnameVerifier, serviceUrl, null, BocConstant.CONTENT_TYPE, postData, head, BocConstant.CHARSET, connectTimeout, readTimeout, method);
        logger.info("response {}", result);
        //验签
        Map<String, Object> reultMap = new HashMap<>();
        try {
            reultMap = XmlUtils.parse(MapUtils.getString(result, BocProtocolFields.BODY));
        } catch (Exception e) {
            logger.error("返回请求体数据转换为map错误", e);
            throw new MpayApiNetworkError("返回请求体数据转换为map错误", e);
        }
        Map<String, String> returnBody = MapUtils.getMap(reultMap, BocProtocolFields.BODY);
        String rtnSignData = MapUtils.getString(reultMap, BocRequestFields.SIGN_DATA);
        // 查询请求签名用:分割,其他请求签名用|分割
        if (!StringUtils.isEmpty(rtnSignData)) {
            String rtnPlainText = BocUtil.convertBodyToPipeSeparatedString(returnBody, BocConstant.SIGN_SPLIT_KEY_PIPE);
            try {
                PKCSTool verifyTool = BocCache.getPKCSVerifier(rootCertificate);
                verifyTool.p7Verify(rtnSignData, rtnPlainText.getBytes("UTF-8"));
            } catch (Exception e) {
                logger.error("验签失败", e);
                throw new MpayApiNetworkError("验签失败", e);
            }
        }
        return reultMap;
    }


}
