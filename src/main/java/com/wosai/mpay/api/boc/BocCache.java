package com.wosai.mpay.api.boc;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.wosai.mpay.api.boc.security.PKCSTool;
import com.wosai.mpay.exception.MpayApiNetworkError;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

public class BocCache {

    public static final Logger logger = LoggerFactory.getLogger(BocCache.class);

    // 定义一个缓存实例
    private static final LoadingCache<String, PKCSTool> signerCache = CacheBuilder.newBuilder()
            .maximumSize(100) // 设置缓存的最大数量
            .expireAfterAccess(10, TimeUnit.MINUTES) // 设置缓存的过期时间
            .build(new CacheLoader<String, PKCSTool>() {
                @Override
                public PKCSTool load(String key) throws Exception {
                    // key 可以是一个组合键，例如 keyStorePfx + keyStorePassword + keyPassword
                    String[] keys = key.split(":");
                    if (keys.length != 3) {
                        throw new MpayApiNetworkError("Invalid key format");
                    }
                    String keyStorePfx = keys[0];
                    String keyStorePassword = keys[1];
                    String keyPassword = keys[2];
                    return PKCSTool.getSigner(keyStorePfx, keyStorePassword, keyPassword, "PKCS7");
                }
            });

    private static final LoadingCache<String, PKCSTool> verifierCache = CacheBuilder.newBuilder()
            .maximumSize(100) // 设置缓存的最大数量
            .expireAfterAccess(10, TimeUnit.MINUTES) // 设置缓存的过期时间
            .build(new CacheLoader<String, PKCSTool>() {
                @Override
                public PKCSTool load(String rootCertificate) throws Exception {
                    byte[] rctBytes = Base64.getDecoder().decode(rootCertificate);
                    // 创建一个字节数组输入流
                    InputStream fis4cer = new ByteArrayInputStream(rctBytes);
                    return PKCSTool.getVerifier(fis4cer, null);
                }
            });


    public static PKCSTool getPKCSSigner(String keyStorePfx, String keyStorePassword, String keyPassword) throws MpayApiNetworkError {
        try {
            return signerCache.get(keyStorePfx + ":" + keyStorePassword + ":" + keyPassword);
        } catch (Exception e) {
            logger.error("Failed to get signer from cache", e);
            throw new MpayApiNetworkError("Failed to get signer from cache",e);
        }
    }

    public static PKCSTool getPKCSVerifier(String key) throws MpayApiNetworkError {
        try {
            return verifierCache.get(key);
        } catch (Exception e) {
            logger.error("Failed to get signer from cache", e);
            throw new MpayApiNetworkError("Invalid root certificate",e);
        }
    }



}
