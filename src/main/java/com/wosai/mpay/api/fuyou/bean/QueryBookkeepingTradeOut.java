package com.wosai.mpay.api.fuyou.bean;

import com.wosai.mpay.api.fuyou.util.ClearTextUtil;

import java.util.ArrayList;
import java.util.List;

public class QueryBookkeepingTradeOut extends BaseOut {
    /**
     * 操作流水号，调用方自己传，会校验唯一性 对私 对公
     */
    private String traceNo;

    /**
     * 商户号 对私 对公
     */
    private String mchntCd;

    private String startDate;

    private String endDate;

    private Integer totalNum;

    private List<Item> items = new ArrayList<>();


    @Override
    public String generateClearText() {
        List<String> excludeFieldList = new ArrayList<String>();
        excludeFieldList.add("signature");
        excludeFieldList.add("items");
        return ClearTextUtil.generateClearText(this, excludeFieldList);
    }

    public String getTraceNo() {
        return traceNo;
    }

    public void setTraceNo(String traceNo) {
        this.traceNo = traceNo;
    }

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public List<Item> getItems() {
        return items;
    }

    public void setItems(List<Item> items) {
        this.items = items;
    }

    public static class Item {
        /**
         * 分账时间
         */
        private String tradeTime;

        /**
         * 源交易流水号
         */
        private String batchNo;
        /**
         * 源交易商户子流
         * 水号
         */
        private String mchntCdTraceNo;

        /**
         * 反序列化
         */
        private String mchntCdChildTraceNo;

        /**
         * 源交易商户子流
         * 水号
         */
        private String srcFasSsn;


        /**
         * 分账金额
         */
        private Long txnAmt;
        /**
         * 转出用户编号或商户号
         */
        private String accountOut;
        /**
         * 转入用户编号或商户号
         */
        private String accountIn;
        /**
         * 01 处理中 05 成功 06 失败 07 已撤销 08 有回退
         */
        private String status;

        /**
         * 当状态为 06 时返回
         */
        private String errorMsg;

        /**
         * 交易类型
         */
        private String tradeType;

        public String getTradeTime() {
            return tradeTime;
        }

        public void setTradeTime(String tradeTime) {
            this.tradeTime = tradeTime;
        }

        public String getBatchNo() {
            return batchNo;
        }

        public void setBatchNo(String batchNo) {
            this.batchNo = batchNo;
        }

        public String getMchntCdTraceNo() {
            return mchntCdTraceNo;
        }

        public void setMchntCdTraceNo(String mchntCdTraceNo) {
            this.mchntCdTraceNo = mchntCdTraceNo;
        }

        public String getSrcFasSsn() {
            return srcFasSsn;
        }

        public void setSrcFasSsn(String srcFasSsn) {
            this.srcFasSsn = srcFasSsn;
        }

        public Long getTxnAmt() {
            return txnAmt;
        }

        public void setTxnAmt(Long txnAmt) {
            this.txnAmt = txnAmt;
        }

        public String getAccountOut() {
            return accountOut;
        }

        public void setAccountOut(String accountOut) {
            this.accountOut = accountOut;
        }

        public String getAccountIn() {
            return accountIn;
        }

        public void setAccountIn(String accountIn) {
            this.accountIn = accountIn;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getErrorMsg() {
            return errorMsg;
        }

        public void setErrorMsg(String errorMsg) {
            this.errorMsg = errorMsg;
        }

        public String getTradeType() {
            return tradeType;
        }

        public void setTradeType(String tradeType) {
            this.tradeType = tradeType;
        }

        public String getMchntCdChildTraceNo() {
            return mchntCdChildTraceNo;
        }

        public void setMchntCdChildTraceNo(String mchntCdChildTraceNo) {
            this.mchntCdChildTraceNo = mchntCdChildTraceNo;
        }
    }
}
