package com.wosai.mpay.api.fuyou.bean;

import java.util.List;

public class AddConcentrateRelationResultOut extends EncrptNotifyOut {

    private AddConcentrateRelationResultInfo data;

    public AddConcentrateRelationResultInfo getData() {
        return data;
    }

    public void setData(AddConcentrateRelationResultInfo data) {
        this.data = data;
    }

    public static class AddConcentrateRelationResultInfo {
        /**
         * 订单归集比例
         */
        private Integer orderConcentrateScale;
        /**
         * 余额归集比例
         */
        private Integer balanceConcentrateScale;
        /**
         * 商户号
         */
        private String mchntCd;

        /**
         * 被归集商户号
         */
        private String mchntCdConcentrate;
        /**
         * 资金用途
         * 01:缴纳品牌费
         * 02:缴纳管理费
         * 03:缴纳服务费
         * 04:资金归集
         * 05:缴纳其他费用
         */
        private String useType;
        /**
         * 01:余额归集 02:按转结金额归集 03:订单预归集 04:订单事后归集
         */
        private List<String> concentrateTypes;
        /**
         * 00：初始化
         * 01：成功
         * 02：已拒绝
         * 03：关系解除
         * 04：授权失败
         * 05：失效
         */
        private String status;

        /**
         * 协议url
         */
        private String url;

        /**
         * 富友批次号
         */
        private String batchNo;

        public Integer getOrderConcentrateScale() {
            return orderConcentrateScale;
        }

        public void setOrderConcentrateScale(Integer orderConcentrateScale) {
            this.orderConcentrateScale = orderConcentrateScale;
        }

        public Integer getBalanceConcentrateScale() {
            return balanceConcentrateScale;
        }

        public void setBalanceConcentrateScale(Integer balanceConcentrateScale) {
            this.balanceConcentrateScale = balanceConcentrateScale;
        }

        public String getMchntCd() {
            return mchntCd;
        }

        public void setMchntCd(String mchntCd) {
            this.mchntCd = mchntCd;
        }

        public String getMchntCdConcentrate() {
            return mchntCdConcentrate;
        }

        public void setMchntCdConcentrate(String mchntCdConcentrate) {
            this.mchntCdConcentrate = mchntCdConcentrate;
        }

        public String getUseType() {
            return useType;
        }

        public void setUseType(String useType) {
            this.useType = useType;
        }

        public List<String> getConcentrateTypes() {
            return concentrateTypes;
        }

        public void setConcentrateTypes(List<String> concentrateTypes) {
            this.concentrateTypes = concentrateTypes;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getBatchNo() {
            return batchNo;
        }

        public void setBatchNo(String batchNo) {
            this.batchNo = batchNo;
        }
    }

}
