package com.wosai.mpay.api.fuyou.bean;

import com.wosai.mpay.api.fuyou.util.ClearTextUtil;

import java.util.ArrayList;
import java.util.List;

public class AddConcentrateRelationOut extends BaseOut {
    /**
     * 操作流水号，调用方自己传，会校验唯一性 对私 对公
     */
    private String traceNo;

    /**
     * 商户号 对私 对公
     */
    private String mchntCd;

    private String mchntCdConcentrate;

    private String batchNo; //批次号

    private String checkUrl;


    @Override
    public String generateClearText() {
        List<String> excludeFieldList = new ArrayList<String>();
        excludeFieldList.add("signature");
        return ClearTextUtil.generateClearText(this, excludeFieldList);
    }

    public String getTraceNo() {
        return traceNo;
    }

    public void setTraceNo(String traceNo) {
        this.traceNo = traceNo;
    }

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getMchntCdConcentrate() {
        return mchntCdConcentrate;
    }

    public void setMchntCdConcentrate(String mchntCdConcentrate) {
        this.mchntCdConcentrate = mchntCdConcentrate;
    }

    public String getCheckUrl() {
        return checkUrl;
    }

    public void setCheckUrl(String checkUrl) {
        this.checkUrl = checkUrl;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }
}
