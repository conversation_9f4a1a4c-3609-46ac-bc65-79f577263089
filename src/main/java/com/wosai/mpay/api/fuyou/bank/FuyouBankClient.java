package com.wosai.mpay.api.fuyou.bank;

import com.wosai.mpay.api.fuyou.FuyouClient;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import com.wosai.pantheon.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class FuyouBankClient {
    public static final Logger logger = LoggerFactory.getLogger(FuyouClient.class);
    protected int connectTimeout = 2000;
    protected int readTimeout = 5000;

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map<String, Object> queryCall(String serviceUrl, String privateKey, Map<String, Object> request) throws MpayException, MpayApiNetworkError {
        preCall(privateKey, request);
        logger.info("request: {}", JsonUtil.objectToJsonString(request));
        String response = HttpClientUtils.doGet(FuyouBankClient.class.getName(), null, null, serviceUrl, request, FuyouBankProtocolFields.UTF_8, connectTimeout, readTimeout);
        logger.info("url: {}, get response normal:  {}", serviceUrl, response);
        return JsonUtil.jsonStringToObject(response, Map.class);
    }

    public Map<String, Object> refundCall(String serviceUrl, String privateKey, Map<String, Object> request) throws MpayException, MpayApiNetworkError {
        request.remove(FuyouBankBusinessFields.SIGN);
        String signContent = getSignContent(request);
        request.put(FuyouBankBusinessFields.SIGN, RsaSignature.sign(signContent, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, privateKey, FuyouBankProtocolFields.GBK));
        logger.info("request: {}", JsonUtil.objectToJsonString(request));
        String response = HttpClientUtils.doPost(FuyouBankClient.class.getName(), null, null, serviceUrl, "application/json;charset=UTF-8", JsonUtil.objectToJsonString(request), FuyouBankProtocolFields.UTF_8, connectTimeout, readTimeout);
        logger.info("url: {}, get response normal:  {}", serviceUrl, response);
        return JsonUtil.jsonStringToObject(response, Map.class);
    }

    public static String getSignContent(Map<String, Object> sortedParams) {
        StringBuffer content = new StringBuffer();
        List<String> keys = new ArrayList<String>(sortedParams.keySet());
        keys.removeAll(FuyouBankProtocolFields.OPT_OUT_SIGNATURE_FIELDS);
        Collections.sort(keys);
        int index = 0;
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = null;
            if(sortedParams.get(key) instanceof Map) {
                value = JsonUtil.toJsonStr(sortedParams.get(key));
            }else if(sortedParams.get(key) != null){
                value = sortedParams.get(key).toString();
            }
            if (StringUtils.areNotEmpty(key, value)) {
                content.append((index == 0 ? "" : "&") + key + "=" + value);
                index++;
            }
        }
        return content.toString();
    }

    private void preCall(String privateKey, Map<String, Object> request) {
        String inOrderNo = MapUtil.getString(request, FuyouBankBusinessFields.IN_ORDER_NO, "");
        try {
            String signature = Digest.md5((inOrderNo + privateKey).getBytes(StandardCharsets.UTF_8));
            request.put(FuyouBankBusinessFields.SIGNATURE, signature);
        } catch (Exception e) {
            logger.error("fuyou bank query md5 error", e);
        }
    }

}
