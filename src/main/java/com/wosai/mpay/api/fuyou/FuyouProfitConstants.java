package com.wosai.mpay.api.fuyou;

public class FuyouProfitConstants {
    public static final String RESP_CODE = "respCode";
    public static final String RESP_DESC = "respDesc";
    public static final String RESP_CODE_SUCCESS = "0000";
    public static final String RESP_CODE_DOING = "7777";

    public static final String OPEN_USE_TYPE_BRAND = "01";      //01:缴纳品牌费
    public static final String OPEN_USE_TYPE_MANAGE = "02";    //02:缴纳管理费
    public static final String OPEN_USE_TYPE_SERVE = "03";    //03:缴纳服务费
    public static final String OPEN_USE_TYPE_CONCENTRATION = "04";    //04:资金归集
    public static final String OPEN_USE_TYPE_OTHER = "05";    //05:缴纳其他费用

    public static final String CONCENTRATE_TYPE_BALANCE = "01"; //余额归集
    public static final String CONCENTRATE_TYPE_CARRIED_FORWARD = "02"; //按转结金额归集
    public static final String CONCENTRATE_TYPE_ORDER_PRE = "03"; //订单预归集
    public static final String CONCENTRATE_TYPE_ORDER_AFTER = "04"; //订单事后归集

    public static final String CHECK_TYPE_MAIL = "1";
    public static final String CHECK_TYPE_URL = "2";

    public static final String CONCENTRATE_TYPE_REAL_TIME = "01";//实时归集
    public static final String CONCENTRATE_TYPE_ACCEPT = "02";//受理归集

    public static final String REFUND_BUSI_TYPE_ORDER = "01";//订单归集回退
    public static final String REFUND_BUSI_TYPE_BALANCE = "02";//订单归集回退

    public static final String QUERY_TRADE_TYPE_BALANCE = "7";//商户资金归集
    public static final String QUERY_TRADE_TYPE_BATCH_ORDER = "8";//批量订单归集
    public static final String QUERY_TRADE_TYPE_BALANCE_REFUND = "12";//归集退回


    public static final String TRADE_STATUS_DOING = "01";//处理中
    public static final String TRADE_STATUS_SUCCESS = "05";//成功
    public static final String TRADE_STATUS_FAIL = "06"; //失败
    public static final String TRADE_STATUS_CANCEL = "07"; //已撤销
    public static final String TRADE_STATUS_EXIST_REFUND = "08";//有回退

}
