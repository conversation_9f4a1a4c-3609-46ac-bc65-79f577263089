package com.wosai.mpay.api.fuyou.bean;

import com.wosai.mpay.api.fuyou.util.ClearTextUtil;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;

import java.util.ArrayList;
import java.util.List;

@JacksonXmlRootElement(localName = "xml")
public class CancelConcentrateRelationApplyIn extends BaseRequest {
    /**
     * 操作流水号，调用方自己传，会校验唯一性
     */
    private String traceNo;

    /**
     * 商户号
     */
    private String mchntCd;

    private String mchntCdConcentrate;

    /**
     * 源自动结算分账批次号
     */
    private String batchNo;

    private String signature;

    @Override
    public String generateClearText() {
        List<String> excludeFieldList = new ArrayList<>();
        excludeFieldList.add("signature");
        return ClearTextUtil.generateClearText(this, excludeFieldList);
    }


    public String getTraceNo() {
        return traceNo;
    }

    public void setTraceNo(String traceNo) {
        this.traceNo = traceNo;
    }

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getMchntCdConcentrate() {
        return mchntCdConcentrate;
    }

    public void setMchntCdConcentrate(String mchntCdConcentrate) {
        this.mchntCdConcentrate = mchntCdConcentrate;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }
}
