package com.wosai.mpay.api.fuyou;

import com.wosai.mpay.util.MapUtils;

import java.util.*;

/***
 * @ClassName: FuyouConstants
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/9/13 9:42 AM
 */
public class FuyouConstants {

    /** GBK字符集 **/
    public static final String CHARSET_GBK = "GBK";

    public static final String DEFAULT_VERSION = "1.0";

    //order_type 订单类型  ALIPAY(支付宝)  WECHAT(微信)  UNIONPAY(银联二维码)  BESTPAY(翼支付)  DIGICCY(数字货币) PY68(银联分期-商户贴息)  PY69(银联分期-持卡人贴息)
    public static final String ORDER_TYPE_ALIPAY = "ALIPAY";
    public static final String ORDER_TYPE_WECHAT = "WECHAT";
    public static final String ORDER_TYPE_UNIONPAY = "UNIONPAY";
    public static final String ORDER_TYPE_BESTPAY = "BESTPAY";
    public static final String ORDER_TYPE_DIGICCY = "DIGICCY";
    public static final String ORDER_TYPE_JDBT = "JDBT";
    public static final String ORDER_TYPE_PY68 = "PY68";
    public static final String ORDER_TYPE_PY69 = "PY69";

    public static final Map<Integer, String> PAYWAY_ORDER_TYPE_MAPPING = MapUtils.hashMap(1, ORDER_TYPE_ALIPAY,
            2, ORDER_TYPE_ALIPAY, 3, ORDER_TYPE_WECHAT, 17, ORDER_TYPE_UNIONPAY, 5, ORDER_TYPE_JDBT);

    //sence 支付场景  1: 条码支付  2: 声波支付  3: 刷脸支付
    public static final String SCENE_BARCODE = "1";
    public static final String SCENE_SOUND = "2";
    public static final String SCENE_FACE = "3";

    //limit_pay 限制支付  no_credit:不能使用信用卡  credit_group：不能使用花呗以及信用卡
    public static final String LIMIT_PAY_NO_CREDIT = "no_credit";
    public static final String LIMIT_PAY_CREDIT_GROUP = "credit_group";

    //term_type  0:其他  1:富友终端  2:POS机  3:台卡  4:PC软件
    public static final String TERM_TYPE_OTHER = "0";
    public static final String TERM_TYPE_FUYOU = "1";
    public static final String TERM_TYPE_POS = "2";
    public static final String TERM_TYPE_CARD = "3";
    public static final String TERM_TYPE_PC = "4";

    //food_order_type  点餐场景类型  qr_order(店内扫码点餐)，pre_order(预点到店自提)，home_delivery(外送到家)，direct_payment(直接付款)，other(其他)
    public static final String FOOD_ORDER_TYPE_QR_CODE = "qr_order";
    public static final String FOOD_ORDER_TYPE_PRE_ORDER = "pre_order";
    public static final String FOOD_ORDER_TYPE_HOME_DELIVERY = "home_delivery";
    public static final String FOOD_ORDER_TYPE_DIRECT_PAYMENT = "direct_payment";
    public static final String FOOD_ORDER_TYPE_OTHER = "other";

    //is_credit 1：表示信用卡  0：表示其他(非信用方式)
    public static final String IS_CREDIT_YES = "1";
    public static final String IS_CREDIT_NO = "0";

    //trade_type JSAPI--公众号支付  FWC--支付宝服务窗、支付宝小程序  LETPAY-微信小程序  BESTPAY--翼支付js  MPAY--云闪付小程序（控件支付）  UNIONPAY--云闪付扫码  UPBXJS--云闪付保险缴费 JDBT--京东白条
    public static final String TRADE_TYPE_JSAPI = "JSAPI";
    public static final String TRADE_TYPE_FWC = "FWC";
    public static final String TRADE_TYPE_LETPAY = "LETPAY";
    public static final String TRADE_TYPE_BESTPAY = "BESTPAY";
    public static final String TRADE_TYPE_MPAY = "MPAY";
    public static final String TRADE_TYPE_UNIONPAY = "UNIONPAY";
    public static final String TRADE_TYPE_UPBXJS = "UPBXJS";
    public static final String TRADE_TYPE_JDBT = "JDBT";
    public static final String TRADE_TYPE_UNIONWXH5 = "UNIONWXH5"; //微信h5拉起云闪付app支付
    public static final String TRADE_TYPE_UNIONLETP = "UNIONLETP"; //小程序拉起微信小程序

    //trans_stat SUCCESS—支付成功  REFUND—转入退款  NOTPAY—未支付  CLOSED—已关闭  REVOKED—已撤销  USERPAYING--用户支付中  PAYERROR--支付失败(其他原因，如银行返回失败)
    public static final String TRANS_STAT_SUCCESS = "SUCCESS";
    public static final String TRANS_STAT_REFUND = "REFUND";
    public static final String TRANS_STAT_NOTPAY = "NOTPAY";
    public static final String TRANS_STAT_CLOSED = "CLOSED";
    public static final String TRANS_STAT_REVOKED = "REVOKED";
    public static final String TRANS_STAT_USERPAYING = "USERPAYING";
    public static final String TRANS_STAT_PAYERROR = "PAYERROR";

    //是否需要新调用撤销(当为Y时，需要重新调用撤销接口)
    public static final String RECALL_YES = "Y";

    /**
     * 各种接口的简易编号
     */
    public static final int METHOD_PAY = 1;
    public static final int METHOD_PRECREATE = 2;
    public static final int METHOD_CANCEL = 3;
    public static final int METHOD_CLOSE = 4;
    public static final int METHOD_QUERY = 5;
    public static final int METHOD_HISTORY_QUERY = 6;
    public static final int METHOD_REFUND = 7;
    public static final int METHOD_REFUND_QUERY = 8;
    public static final int METHOD_NOTIFY = 9;
    public static final int METHOD_WALLET_QUERY = 10;
    public static final int METHOD_SETTLE = 11; //结算同步打款
    public static final int METHOD_TXN_FEE_QUERY = 12;

    public static final int METHOD_QUERY_SETTLEMENT = 13; //结算交易查询接口
    public static final int METHOD_AUTH_2_OPENID = 14;    //查询云闪付openid

    //富友营销转账相关接口
    public static final int METHOD_TRANSFER = 15;         //富友营销转账接口
    public static final int METHOD_TRANSFER_QUERY = 16;   //富友营销转账流水查询接口
    public static final int METHOD_TRANSFER_REFUND = 17;  //富友营销转账退回接口

    public static final int METHOD_ASYNC_SETTLE = 18; //结算异步打款
    /**
     * 错误码
     */
    public static final String RESULT_CODE_SUCCESS = "000000"; // 成功
    public static final String RESULT_CODE_OVERTIME = "010001"; //超时
    public static final String RESULT_CODE_SYSTEM_ERROR = "010002"; //系统异常
    public static final String RESULT_CODE_SERVICE_NOT_AVAILABLE = "010003"; //系统异常
    public static final String RESULT_CODE_SYSTEM_MAINTENANCE = "010005"; //系统维护中
    public static final String RESULT_CODE_SYSTEM_BUSY = "010006"; //系统繁忙
    public static final String RESULT_CODE_CHANNEL_NOT_AVAILABLE = "010007"; //渠道不可用
    public static final String RESULT_CODE_INSTITUTION_ACCEPTATION_ERROR = "010008"; //机构受理异常
    public static final String RESULT_CODE_PARAM_NOT_VALID = "020001"; //报文错误   格式不正确、必填字段非空等等
    public static final String RESULT_CODE_ERROR = "030001"; //其他错误
    public static final String RESULT_CODE_BARCODE_NOT_VALID = "030002"; //授权码不正确   授权码不合法、授权码已过期、授权码校验错误等
    public static final String RESULT_CODE_REPEAT_ORDER = "030003"; //订单已重复
    public static final String RESULT_CODE_ORDER_PAID = "030004"; //订单已支付
    public static final String RESULT_CODE_MERCHANT_NOT_EXIST = "030005"; //商户不存在   商户未在渠道开通商户
    public static final String RESULT_CODE_ORDER_CANCELED = "030006"; //订单已撤销
    public static final String RESULT_CODE_ORDER_CLOSED = "030007"; //订单已关闭
    public static final String RESULT_CODE_ORDER_NOT_EXIST = "030008"; //订单不存在
    public static final String RESULT_CODE_MERCHANT_NO_AUTH = "030009"; //商户无权限
    public static final String RESULT_CODE_USER_PAYING = "030010"; //用户支付中
    public static final String RESULT_CODE_ORDER_REFUNDED = "030011"; //订单已退款
    public static final String RESULT_CODE_REFUND_ORDER_NOT_EXIST = "030012"; //退款订单不存在
    public static final String RESULT_CODE_INSUFFICIENT_POSITION = "030013"; //头寸不足 商户正交易不足以支持撤销
    public static final String RESULT_CODE_REFUND_AMT_OVER_ORIGINAL = "030014"; //退款金额超过原订单总金额
    public static final String RESULT_CODE_BALANCE_NOT_ENOUGH = "030015"; //余额不足
    public static final String RESULT_CODE_ORIGINAL_ORDER_NOT_EXIST = "030016"; //原订单不存在    退货才有
    public static final String RESULT_CODE_ORDER_CAN_NOT_REFUND = "030017"; //订单不可退款
    public static final String RESULT_CODE_MERCHANT_ALREADY_EXIST = "030018"; //商户已入驻
    public static final String RESULT_CODE_BUSINESS_CATEGORY_NOT_EXIST = "030020"; //商户经营类目不存在

    public static final String RESULT_CODE_NECESSARY_PARAM_NOT_EMPTY = "1001"; //非空字段出现空值
    public static final String RESULT_CODE_SIGN_VALID_ERROR = "1002"; //验签错误
    public static final String RESULT_CODE_PARAM_CONTENT_ERROR = "1003"; //字段内容错误
    public static final String RESULT_CODE_MERCHANT_SN_NOT_EXIST = "1009"; //商户号不存在/XX交易未开通
    public static final String RESULT_CODE_TRANSACTION_NOT_EXIST = "1010"; //找不到交易  找不到/交易状态异常
    public static final String RESULT_CODE_CAN_NOT_REFUND = "1011"; //金额超限不允许退款
    public static final String RESULT_CODE_BALANCE_NOT_ENOUGH_FOR_REFUND = "1012"; //余额不足不允许退款/银联主扫不允许退款
    public static final String RESULT_CODE_REPEAT_ORDER_SN = "1013"; //商户订单号重复
    public static final String RESULT_CODE_MESSAGE_FORMAT_ERROR = "1014"; //报文格式错
    public static final String RESULT_CODE_NOT_SUPPORT = "1015"; //请求功能尚不支持
    public static final String RESULT_CODE_C2B_CAN_NOT_CLOSE = "1016"; //订单状态为其他，不许关单
    public static final String RESULT_CODE_TARGET_OVERTIME = "2001"; //目标方超时
    public static final String RESULT_CODE_TARGET_CONNECT_FAIL = "2002"; //目标方连接失败
    public static final String RESULT_CODE_SYSTEM_WRONG = "9999"; //系统错误
    public static final String RETURN_CODE_SUCCESS = "SUCCESS";//返回状态码, SUCCESS/FAILED，此字段是通信标识，非交易标识，交易是否成功需要查看result_code来判断
    public static final String RETURN_CODE_FAILED = "FAILED";//返回状态码, SUCCESS/FAILED，此字段是通信标识，非交易标识，交易是否成功需要查看result_code来判断
    public static final String WITHDRAW_RESULT_CODE_SUCCESS = "SUCCESS";//业务结果, SUCCESS/FAIL
    public static final String WITHDRAW_RESULT_CODE_FAIL = "FAIL";//业务结果, SUCCESS/FAIL

    /**
     * 服务商机构标识码
     */
    public static final String PID = "C1000001";


    public static final String RESULT_MSG_C2B_CAN_NOT_CLOSE = "订单状态为其他，不许关单"; //订单状态为其他，不许关单

    //条码支付/退款 状态未知
    public static final Set<String> RESULT_CODE_UNKNOWN_SET = new HashSet<String>(){{
        List<String> list = Arrays.asList(RESULT_CODE_SYSTEM_ERROR, RESULT_CODE_OVERTIME, RESULT_CODE_TARGET_OVERTIME,
                RESULT_CODE_TARGET_CONNECT_FAIL, RESULT_CODE_SYSTEM_WRONG);
        addAll(list);
    }};

}
