package com.wosai.mpay.api.fuyou.bean;

import com.wosai.mpay.api.fuyou.util.ClearTextUtil;

import java.util.ArrayList;
import java.util.List;

public class CancelConcentrateTradeOut extends BaseOut {
    /**
     * 操作流水号，调用方自己传，会校验唯一性 对私 对公
     */
    private String traceNo;

    /**
     * 商户号 对私 对公
     */
    private String mchntCd;

    private Long srcAmt;

    private String busiType;  //01 订单归集 02 资金归集

    private String srcBatchNo;


    @Override
    public String generateClearText() {
        List<String> excludeFieldList = new ArrayList<String>();
        excludeFieldList.add("signature");
        return ClearTextUtil.generateClearText(this, excludeFieldList);
    }

    public String getTraceNo() {
        return traceNo;
    }

    public void setTraceNo(String traceNo) {
        this.traceNo = traceNo;
    }

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public Long getSrcAmt() {
        return srcAmt;
    }

    public void setSrcAmt(Long srcAmt) {
        this.srcAmt = srcAmt;
    }

    public String getBusiType() {
        return busiType;
    }

    public void setBusiType(String busiType) {
        this.busiType = busiType;
    }

    public String getSrcBatchNo() {
        return srcBatchNo;
    }

    public void setSrcBatchNo(String srcBatchNo) {
        this.srcBatchNo = srcBatchNo;
    }
}
