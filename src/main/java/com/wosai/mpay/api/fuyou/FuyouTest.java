package com.wosai.mpay.api.fuyou;

import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.api.zjtlcb.MD5Util;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.FuyouSignature;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import com.wosai.pantheon.util.MapUtil;

import java.io.UnsupportedEncodingException;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/***
 * @ClassName: FuyouTest
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/9/14 4:30 PM
 */
public class FuyouTest {

    private static final String PAY_URL = "https://fundwx.fuiou.com/micropay";
    private static final String PRECREATE_URL = "https://spay-cloud.fuioupay.com/wxPreCreate";
    private static final String CANCEL_URL = "https://fundwx.fuiou.com/cancelorder";
    private static final String QUERY_URL = "https://fundwx.fuiou.com/commonQuery";
    private static final String REFUND_URL = "https://fundwx.fuiou.com/commonRefund";
    private static final String REFUND_QUERY_URL = "https://fundwx.fuiou.com/refundQuery";
    private static final String HISTORY_QUERY_URL = "https://fundwx.fuiou.com/hisTradeQuery";
    private static final String WALLET_QUERY_URL = "https://scan-rim-test.fuioupay.com/queryWithdrawAmt";

    private static final String TXN_FEE_QUERY_URL = "https://scan-rim-test.fuioupay.com/txnFeeQuery";

    private static final String QUERY_SETTLEMENT = "https://scan-rim-mc.fuioupay.com/querySettlement";

    private static final String AUTH_2_OPENID = "https://fundwx.fuiou.com/auth2Openid";

    //富友营销转账相关url
    private static final String TRANSFER_URL = "http://180.168.100.155:9600/fatspAccountTransfer.fuiou";
    private static final String TRANSFER_QUERY_URL = "http://180.168.100.155:9600/queryFatspAccountTransfer.fuiou";
    private static final String TRANSFER_REFUND_URL = "http://180.168.100.155:9600/fatspAccountTransferCancel.fuiou";

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter
            .ofPattern("yyyyMMddHHmmss");


    private static final String ins_cd = "08A9999999";
    private static final String account_in = "0000110F8007477";
    private static final String mchnt_cd = "0002900F0370542";
    private static final String wx_appid = "wxfa089da95020ba1a";

    private static SafeSimpleDateFormat df = new SafeSimpleDateFormat("yyyyMMddHHmmss");

    //公钥
    private static final String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCBv9K+jiuHqXIehX81oyNSD2RfVn+KTPb7NRT5HDPFE35CjZJd7Fu40r0U2Cp7Eyhayv/mRS6ZqvBT/8tQqwpUExTQQBbdZjfk+efb9bF9a+uCnAg0RsuqxeJ2r/rRTsORzVLJy+4GKcv06/p6CcBc5BI1gqSKmyyNBlgfkxLYewIDAQAB";
    //私钥
    private static final String privateKey  = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJgAzD8fEvBHQTyxUEeK963mjziM\n" +
            "WG7nxpi+pDMdtWiakc6xVhhbaipLaHo4wVI92A2wr3ptGQ1/YsASEHm3m2wGOpT2vrb2Ln/S7lz1\n" +
            "ShjTKaT8U6rKgCdpQNHUuLhBQlpJer2mcYEzG/nGzcyalOCgXC/6CySiJCWJmPyR45bJAgMBAAEC\n" +
            "gYBHFfBvAKBBwIEQ2jeaDbKBIFcQcgoVa81jt5xgz178WXUg/awu3emLeBKXPh2i0YtN87hM/+J8\n" +
            "fnt3KbuMwMItCsTD72XFXLM4FgzJ4555CUCXBf5/tcKpS2xT8qV8QDr8oLKA18sQxWp8BMPrNp0e\n" +
            "pmwun/gwgxoyQrJUB5YgZQJBAOiVXHiTnc3KwvIkdOEPmlfePFnkD4zzcv2UwTlHWgCyM/L8SCAF\n" +
            "clXmSiJfKSZZS7o0kIeJJ6xe3Mf4/HSlhdMCQQCnTow+TnlEhDTPtWa+TUgzOys83Q/VLikqKmDz\n" +
            "kWJ7I12+WX6AbxxEHLD+THn0JGrlvzTEIZyCe0sjQy4LzQNzAkEAr2SjfVJkuGJlrNENSwPHMugm\n" +
            "vusbRwH3/38ET7udBdVdE6poga1Z0al+0njMwVypnNwy+eLWhkhrWmpLh3OjfQJAI3BV8JS6xzKh\n" +
            "5SVtn/3Kv19XJ0tEIUnn2lCjvLQdAixZnQpj61ydxie1rggRBQ/5vLSlvq3H8zOelNeUF1fT1QJA\n" +
            "DNo+tkHVXLY9H2kdWFoYTvuLexHAgrsnHxONOlSA5hcVLd1B3p9utOt3QeDf6x2i1lqhTH2w8gzj\n" +
            "vsnx13tWqg==";


    private static void payTest() throws Exception {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouProtocolFields.VERSION, "1.0");
        requestBuilder.set(FuyouProtocolFields.INS_CD, ins_cd);
        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, mchnt_cd);
        requestBuilder.set(FuyouBusinessFields.TERM_ID, "88888888");
        requestBuilder.set(FuyouBusinessFields.ORDER_TYPE, "ALIPAY");
        requestBuilder.set(FuyouBusinessFields.GOODS_DES, "ALIPAY");
        requestBuilder.set(FuyouBusinessFields.MCHNT_ORDER_NO, "2024010300001");
        requestBuilder.set(FuyouBusinessFields.ORDER_AMT, 1);
        requestBuilder.set(FuyouBusinessFields.TERM_IP, "***********");
        requestBuilder.set(FuyouBusinessFields.TXN_BEGIN_TS, "20230926153817");
        requestBuilder.set(FuyouBusinessFields.AUTH_CODE, "288513286037852153");
        requestBuilder.set(FuyouBusinessFields.RESERVED_EXPIRE_MINUTE, "1");

//        {
//            "store_info" : {
//            "id": "SZTX001",
//                    "name": "腾大餐厅",
//                    "area_code": "440305",
//                    "address": "科技园中一路腾讯大厦" }
//        }

        Map<String, Object> sceneInfo = new HashMap<>();
        Map<String, Object> storeInfo = new HashMap<>();
        storeInfo.put("id", "SZTX001");
        storeInfo.put("name", "腾大餐厅");
        sceneInfo.put("store_info", storeInfo);
        requestBuilder.set(FuyouBusinessFields.RESERVED_SCENE_INFO, JsonUtil.toJsonStr(sceneInfo));


        //[{
        //    "goods_id":"apple-01",//商品编号
        //    "alipay_goods_id":"20010001",//支付宝定义的统一商品编号
        //    "goods_name":"单品",//商品名称
        //    "quantity":"1",//商品数量
        //    "price":"0.21",//商品单价，单位为元
        //    "goods_category":"",//商品类目
        //    "categories_tree":"124868003|126232002|126252004",//商品类目树，从商品类目根节点到叶子节点的类目 id 组成，类目 id 值使用|分割
        //    "body":"",//商品描述信息
        //    "show_url":"",//商品的展示地址
        //}]


        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> detail = new HashMap<>();
        detail.put("goods_id", "apple-01");
        detail.put("alipay_goods_id", "20010001");
        detail.put("quantity", "1");
        detail.put("price", "1");
        detail.put("goods_name", "单品");
        list.add(detail);
        requestBuilder.set(FuyouBusinessFields.GOODS_DETAIL, JsonUtil.toJsonStr(list));

        System.out.println(requestBuilder.build());

        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(PAY_URL, privateKey, requestBuilder.build(), FuyouConstants.METHOD_PAY);


        System.out.println(result);
    }

    private static void cancelTest() throws Exception {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouProtocolFields.VERSION, "1.0");
        requestBuilder.set(FuyouProtocolFields.INS_CD, ins_cd);
        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, mchnt_cd);
        requestBuilder.set(FuyouBusinessFields.TERM_ID, "88888888");
        requestBuilder.set(FuyouBusinessFields.MCHNT_ORDER_NO, "20230919000001");
        requestBuilder.set(FuyouBusinessFields.ORDER_TYPE, "ALIPAY");
        requestBuilder.set(FuyouBusinessFields.CANCEL_ORDER_NO, "20230919000001_C");

        System.out.println(requestBuilder.build());

        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(CANCEL_URL, privateKey, requestBuilder.build(), FuyouConstants.METHOD_CANCEL);


        System.out.println(result);
    }

    private static void queryTest() throws Exception {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouProtocolFields.VERSION, "1.0");
        requestBuilder.set(FuyouProtocolFields.INS_CD, ins_cd);
        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, mchnt_cd);
        requestBuilder.set(FuyouBusinessFields.TERM_ID, "88888888");
        requestBuilder.set(FuyouBusinessFields.ORDER_TYPE, "ALIPAY");
        requestBuilder.set(FuyouBusinessFields.MCHNT_ORDER_NO, "20230926000001");
        System.out.println(requestBuilder.build());

        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(QUERY_URL, privateKey, requestBuilder.build(), FuyouConstants.METHOD_QUERY);


        System.out.println(result);
    }

    private static void hisQueryTest() throws Exception {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouProtocolFields.VERSION, "1.0");
        requestBuilder.set(FuyouProtocolFields.INS_CD, ins_cd);
        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, mchnt_cd);
        requestBuilder.set(FuyouBusinessFields.TERM_ID, "88888888");
        requestBuilder.set(FuyouBusinessFields.ORDER_TYPE, "ALIPAY");
        requestBuilder.set(FuyouBusinessFields.MCHNT_ORDER_NO, "20230919000002");
        requestBuilder.set(FuyouBusinessFields.TRADE_DT, "20230919");
        System.out.println(requestBuilder.build());

        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(HISTORY_QUERY_URL, privateKey, requestBuilder.build(), FuyouConstants.METHOD_HISTORY_QUERY);


        System.out.println(result);
    }

    private static void refundTest() throws Exception {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouProtocolFields.VERSION, "1.0");
        requestBuilder.set(FuyouProtocolFields.INS_CD, ins_cd);
        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, mchnt_cd);
        requestBuilder.set(FuyouBusinessFields.TERM_ID, "88888888");
        requestBuilder.set(FuyouBusinessFields.MCHNT_ORDER_NO, "20230919000002");
        requestBuilder.set(FuyouBusinessFields.ORDER_TYPE, "ALIPAY");
        requestBuilder.set(FuyouBusinessFields.REFUND_ORDER_NO, "20230919000002_R");
        requestBuilder.set(FuyouBusinessFields.TOTAL_AMT, 1);
        requestBuilder.set(FuyouBusinessFields.REFUND_AMT, 1);
        requestBuilder.set(FuyouBusinessFields.RESERVED_ORIGI_DT, "20230919");

        System.out.println(requestBuilder.build());

        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(REFUND_URL, privateKey, requestBuilder.build(), FuyouConstants.METHOD_REFUND);


        System.out.println(result);
    }

    private static void refundQueryTest() throws Exception {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouProtocolFields.VERSION, "1.0");
        requestBuilder.set(FuyouProtocolFields.INS_CD, ins_cd);
        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, mchnt_cd);
        requestBuilder.set(FuyouBusinessFields.TERM_ID, "88888888");
        requestBuilder.set(FuyouBusinessFields.REFUND_ORDER_NO, "20230919000002_R");
        requestBuilder.set("goods", "20230919000002_R");

        System.out.println(requestBuilder.build());

        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(REFUND_QUERY_URL, privateKey, requestBuilder.build(), FuyouConstants.METHOD_REFUND_QUERY);


        System.out.println(result);
    }


    private static void preCreateTest() throws Exception {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouProtocolFields.VERSION, "1.0");
        requestBuilder.set(FuyouProtocolFields.INS_CD, ins_cd);
        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, mchnt_cd);
        requestBuilder.set(FuyouBusinessFields.TERM_ID, "88888888");
        requestBuilder.set(FuyouBusinessFields.GOODS_DES, "ALIPAY test");
        requestBuilder.set(FuyouBusinessFields.MCHNT_ORDER_NO, "20230914000012");
        requestBuilder.set(FuyouBusinessFields.ORDER_AMT, 1);
        requestBuilder.set(FuyouBusinessFields.TERM_IP, "***********");
        requestBuilder.set(FuyouBusinessFields.TXN_BEGIN_TS, "20230918104017");
        requestBuilder.set(FuyouBusinessFields.NOTIFY_URL, "https://shouqianba.com");
        requestBuilder.set(FuyouBusinessFields.TRADE_TYPE, "FWC");
//        requestBuilder.set(FuyouBusinessFields.OPENID, "");
        requestBuilder.set(FuyouBusinessFields.SUB_OPENID, "2088412435733404");
//        requestBuilder.set(FuyouBusinessFields.SUB_APPID, "wxfa089da95020ba1a");
        requestBuilder.set(FuyouBusinessFields.RESERVED_EXPIRE_MINUTE, "1");

        System.out.println(requestBuilder.build());

        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(PRECREATE_URL, privateKey, requestBuilder.build(), FuyouConstants.METHOD_PRECREATE);


        System.out.println(result);
    }

    private static void walletQuery() throws MpayApiNetworkError, MpayException, UnsupportedEncodingException {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
//        requestBuilder.set(FuyouProtocolFields.INS_CD, "08M0031385");
                requestBuilder.set(FuyouProtocolFields.INS_CD, ins_cd);

//        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, "0003050F6518836");
//        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, mchnt_cd);
//        requestBuilder.set(FuyouBusinessFields.RESERVED_BUSI_TYPE,1);
        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(WALLET_QUERY_URL, privateKey, requestBuilder.build(), FuyouConstants.METHOD_WALLET_QUERY);
        System.out.println(JsonUtil.toJsonStr(result));
    }

    private static void txnFeeQuery() throws MpayApiNetworkError, MpayException, UnsupportedEncodingException {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouProtocolFields.VERSION, "1.0");
        requestBuilder.set(FuyouProtocolFields.INS_CD, ins_cd);
        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, mchnt_cd);
        requestBuilder.set(FuyouBusinessFields.MCHNT_ORDER_NO, "2024010300001");
        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(TXN_FEE_QUERY_URL, privateKey, requestBuilder.build(), FuyouConstants.METHOD_TXN_FEE_QUERY);
        System.out.println(JsonUtil.toJsonStr(result));
    }

    private static void querySettlement() throws Exception {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouProtocolFields.INS_CD, "08M0031385");
        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, "0003050F6518836");
//        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, "0003320F6723689");
        requestBuilder.set(FuyouBusinessFields.MCHNT_ORDER_NO, "6789000000545600");
        requestBuilder.set(FuyouBusinessFields.RESERVED_ORDER_NO, "6789000000545600");
        requestBuilder.set(FuyouBusinessFields.DATE, "20240122");
        requestBuilder.set(FuyouBusinessFields.START_INDEX, 1);
        requestBuilder.set(FuyouBusinessFields.END_INDEX, 1);
//        requestBuilder.set(FuyouBusinessFields.FY_TRACE_NO, "091820797670"); //040700999345
        requestBuilder.set(FuyouBusinessFields.WITHDRAW_TYPE, 1);
        requestBuilder.set(FuyouBusinessFields.FY_TRACE_NO, "221428632168");
        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(QUERY_SETTLEMENT, privateKey, requestBuilder.build(), FuyouConstants.METHOD_QUERY_SETTLEMENT);
        System.out.println(JsonUtil.toJsonStr(result));
    }

    private static void unionPayTest() throws Exception {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouProtocolFields.VERSION, "1.0");
        requestBuilder.set(FuyouProtocolFields.INS_CD, ins_cd);
        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, mchnt_cd);
        requestBuilder.set(FuyouBusinessFields.TERM_ID, "88888888");
        requestBuilder.set(FuyouBusinessFields.ORDER_TYPE, "UNIONPAY");
        requestBuilder.set(FuyouBusinessFields.GOODS_DES, "商品名称");
        requestBuilder.set(FuyouBusinessFields.MCHNT_ORDER_NO, "20240527010103");
        requestBuilder.set(FuyouBusinessFields.ORDER_AMT, 1);
        requestBuilder.set(FuyouBusinessFields.TERM_IP, "***********");
        requestBuilder.set(FuyouBusinessFields.TXN_BEGIN_TS, "20240527114220");
        requestBuilder.set(FuyouBusinessFields.AUTH_CODE, "6223108127294127300");
        requestBuilder.set(FuyouBusinessFields.RESERVED_EXPIRE_MINUTE, "1");

        requestBuilder.set(FuyouBusinessFields.RESERVED_TERMINAL_INFO, JsonUtil.jsonStringToObject("{\"serial_num\":\"12345678901SN012\"}", Map.class));
        System.out.println(JsonUtil.objectToJsonString(requestBuilder.build()));

        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(PAY_URL, privateKey, requestBuilder.build(), FuyouConstants.METHOD_PAY);

        System.out.println(JsonUtil.objectToJsonString(result));
    }


    private static void getOpenid() throws Exception {
        String privateKey = "";
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouProtocolFields.VERSION, "1.0");
        requestBuilder.set(FuyouProtocolFields.INS_CD, "08M0031385");
        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, "0007910F7261094");
        requestBuilder.set(FuyouBusinessFields.SUB_APPID, "UnionPay/1.0 CloudPay");
        requestBuilder.set(FuyouBusinessFields.AUTH_CODE, "6223361223582317454");
        requestBuilder.set(FuyouBusinessFields.TERM_IP, "************");
        requestBuilder.set(FuyouBusinessFields.ORDER_TYPE, "UNIONPAY");

        System.out.println(JsonUtil.objectToJsonString(requestBuilder.build()));

        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(AUTH_2_OPENID, privateKey, requestBuilder.build(), FuyouConstants.METHOD_AUTH_2_OPENID);

        System.out.println(JsonUtil.objectToJsonString(result));
    }

    //富友营销转账接口测试
    private static void transferTest() throws Exception {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouTransferFields.TRACE_NO, "***************");
        requestBuilder.set(FuyouTransferFields.INS_CD, ins_cd);
        requestBuilder.set(FuyouTransferFields.ACCOUNT_IN, account_in);
        requestBuilder.set(FuyouTransferFields.MCHNT_CD, mchnt_cd);
        requestBuilder.set(FuyouTransferFields.TXN_AMT, 1);
        requestBuilder.set(FuyouTransferFields.TRANS_TYPE, 1);
        requestBuilder.set(FuyouTransferFields.REMARK, "");
        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(TRANSFER_URL, privateKey, requestBuilder.build(), FuyouConstants.METHOD_TRANSFER);
        System.out.println(JsonUtil.toJsonStr(result));
    }

    //富友营销转账查询流水明细接口测试
    private static void transferQueryTest() throws Exception {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouTransferFields.TRACE_NO, "***************");
        requestBuilder.set(FuyouTransferFields.INS_CD, ins_cd);
        requestBuilder.set(FuyouTransferFields.ACCOUNT_IN, account_in);
        requestBuilder.set(FuyouTransferFields.BEGIN_DATE, "********");
        requestBuilder.set(FuyouTransferFields.END_DATE, "********");
        requestBuilder.set(FuyouTransferFields.STATUS, 1);
        requestBuilder.set(FuyouTransferFields.REMARK, "");
        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(TRANSFER_QUERY_URL, privateKey, requestBuilder.build(), FuyouConstants.METHOD_TRANSFER_QUERY);
        System.out.println(JsonUtil.toJsonStr(result));
    }

    //富友营销转账退回接口测试
    private static void transferRefundTest() throws Exception {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouTransferFields.TRACE_NO, "***************");
        requestBuilder.set(FuyouTransferFields.SRC_TRACE_NO, "***************");
        requestBuilder.set(FuyouTransferFields.INS_CD, ins_cd);
        requestBuilder.set(FuyouTransferFields.ACCOUNT_IN, account_in);
        requestBuilder.set(FuyouTransferFields.MCHNT_CD, mchnt_cd);
        requestBuilder.set(FuyouTransferFields.TXN_AMT, 1);
        requestBuilder.set(FuyouTransferFields.TRANS_DATE, "********");
        requestBuilder.set(FuyouTransferFields.REMARK, "");
        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(TRANSFER_REFUND_URL, privateKey, requestBuilder.build(), FuyouConstants.METHOD_TRANSFER_REFUND);
        System.out.println(JsonUtil.toJsonStr(result));
    }

    private static void testWxPrecreate() throws MpayApiNetworkError, MpayException, UnsupportedEncodingException {
        String privateKey = "";
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouProtocolFields.VERSION, "1.0");
        requestBuilder.set(FuyouProtocolFields.INS_CD, "08M0031385");
        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, "0003050F6573416");
        requestBuilder.set(FuyouBusinessFields.TERM_ID, "13035841");
        requestBuilder.set(FuyouBusinessFields.MCHNT_ORDER_NO, "7894259268780002");
        requestBuilder.set(FuyouBusinessFields.ORDER_AMT, 1000);
        requestBuilder.set(FuyouBusinessFields.TERM_IP, "***********");
        requestBuilder.set(FuyouBusinessFields.TXN_BEGIN_TS, "20240911101100");
        requestBuilder.set(FuyouBusinessFields.NOTIFY_URL, "https://shouqianba.com");
        requestBuilder.set(FuyouBusinessFields.TRADE_TYPE, "JDBT");
        requestBuilder.set(FuyouBusinessFields.GOODS_DES,"京东白嫖");
//        requestBuilder.set(FuyouBusinessFields.OPENID, "");
//        requestBuilder.set(FuyouBusinessFields.SUB_OPENID, "2088412435733404");
//        requestBuilder.set(FuyouBusinessFields.SUB_APPID, "wxfa089da95020ba1a");
//        requestBuilder.set(FuyouBusinessFields.RESERVED_EXPIRE_MINUTE, "1");
        requestBuilder.set(FuyouBusinessFields.RESERVED_ALI_EXTEND_PARAMS, MapUtil.hashMap("hb_fq_num",3));
        System.out.println(requestBuilder.build());

        FuyouClient fuyouClient = new FuyouClient();

        Map<String, Object> result = fuyouClient.call(PRECREATE_URL, privateKey, requestBuilder.build(), FuyouConstants.METHOD_PRECREATE);

        System.out.println(JsonUtil.toJsonStr(result));
        System.out.println(result);
    }

    private static void testQueryUrl()throws MpayApiNetworkError, MpayException, UnsupportedEncodingException {
        String privateKey = "";
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set("insCd", "08M0031385");
//        requestBuilder.set("notifyType","mchtreform");
        requestBuilder.set("traceNo",System.currentTimeMillis()+"");
//        requestBuilder.set("key","********************************");
        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> build = requestBuilder.build();
        String signContent = FuyouSignature.getSignContent(build, new ArrayList<>(build.keySet()));
        signContent+="&key=de8abce6f17d41cab503343ea1207b5b";
        build.put("sign", MD5Util.md5_(signContent));
        String response = HttpClientUtils.doPost(FuyouClient.class.getName(), null, null, "http://www-1.fuiou.com:28090/wmp/notifyConfig.fuiou?action=queryNotifyConfig", build, "utf-8", 5000, 15000);
        System.out.println(response);
    }

    public static void main(String[] args) throws Exception {
        testQueryUrl();
//        transferTest();
        //transferQueryTest();
        //transferRefundTest();
        //getOpenid();
        //unionPayTest();
        //querySettlement();
        //querySettlement();
        //walletQuery();
        //payTest();
        //refundTest();
        //cancelTest();
        //queryTest();
        //hisQueryTest();
        //refundQueryTest();
        //preCreateTest();
    }
}
