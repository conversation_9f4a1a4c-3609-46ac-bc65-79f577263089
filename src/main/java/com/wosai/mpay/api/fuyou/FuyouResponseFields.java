package com.wosai.mpay.api.fuyou;

/***
 * @ClassName: FuyouResponseFields
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/9/13 10:40 AM
 */
public class FuyouResponseFields {

    public static final String RESULT_CODE = "result_code";     //错误代码, 000000 成功
    public static final String RESULT_MSG = "result_msg";      //错误代码描述
    public static final String INS_CD = "ins_cd";               //机构号,接入机构在富友的唯一代码
    public static final String MCHNT_CD = "mchnt_cd";           //商户号,富友分配给二级商户的商户号
    public static final String TERM_ID = "term_id";             //终端号(没有真实终端号统一填88888888)
    public static final String RANDOM_STR = "random_str";       //随机字符串
    public static final String SIGN = "sign";                   //签名
    public static final String SUB_MER_ID = "sub_mer_id";       //支付通道对应的子商户识别码
    public static final String SESSION_ID = "session_id";       //预支付交易会话标识
    public static final String QR_CODE = "qr_code";             //二维码链接  trade_type 为 APPLEPAY、UNIONPAY时返回，用以重定向拉起支付
    public static final String SUB_APPID = "sub_appid";         //子商户公众号id
    public static final String SUB_OPENID = "sub_openid";       //子商户用户标识
    public static final String ORDER_TYPE = "order_type";       //订单类型：ALIPAY(支付宝)、WECHAT(微信)、UNIONPAY(银联二维码)、BESTPAY(翼支付)、DIGICCY(数字货币)
    public static final String TOTAL_AMOUNT = "total_amount";   //订单金额，分为单位的整数
    public static final String ORDER_AMT = "order_amt";         //订单金额, 单位为(分)
    public static final String BUYER_ID = "buyer_id";           //买家在渠道(微信、支付宝)的用户ID
    public static final String USER_ID = "user_id";             //用户在商户的id
    public static final String TRANSACTION_ID = "transaction_id";   //渠道交易流水号
    public static final String ADDN_INF = "addn_inf";               //附加数据 如果需要用到微信点餐数据回传，该字段需要填写OrderSource=FoodOrder
    public static final String SDK_APPID = "sdk_appid";             //公众号id
    public static final String SDK_TIMESTAMP = "sdk_timestamp";     //时间戳，自1970年1月1日 0点0分0秒以来的秒数
    public static final String SDK_NONCESTR = "sdk_noncestr";       //随字符串
    public static final String SDK_PACKAGE = "sdk_package";         //订单性情扩展字符串
    public static final String SDK_SIGNTYPE = "sdk_signtype";       //签名方式，trade_type 为 JSAPI、LETPAY时才返回
    public static final String SDK_PAYSIGN = "sdk_paysign";         //签名
    public static final String SDK_PARTNERID = "sdk_partnerid";     //trade_type 为 APP时才返回
    public static final String TRANS_STAT = "trans_stat";           //查询状态
    public static final String MCHNT_ORDER_NO = "mchnt_order_no";   //商户订单号, 商户系统内部的订单号 (5到30个字符、只能包含字母数字,区分大小写)
    public static final String CURR_TYPE = "curr_type";             //货币类型,默认人民币：CNY
    public static final String REFUND_ORDER_NO = "refund_order_no"; //商户退款单号 (5到30个字符、只能包含字母数字或者下划线，区分大小写)
    public static final String REFUND_ID = "refund_id";             //渠道退款流水号
    public static final String CANCEL_ORDER_NO = "cancel_order_no"; //商户撤销单号
    public static final String CANCEL_ID = "cancel_id";             //渠道撤销流水号
    public static final String FUND_CHANGE = "fund_change";         //是否有资金流向(只有支付宝有该字段)
    public static final String RECALL = "recall";                   //是否需要新调用撤销(当为Y时，需要重新调用撤销接口)

    public static final String RESERVED_FY_ORDER_NO = "reserved_fy_order_no";               //富友生成的订单号,需要商户与商户订单号进行关联
    public static final String RESERVED_FY_SETTLE_DT = "reserved_fy_settle_dt";             //富友交易日期
    public static final String RESERVED_FY_TRACE_NO = "reserved_fy_trace_no";               //富友系统内部追踪号
    public static final String RESERVED_FY_TERM_ID = "reserved_fy_term_id";                 //富友终端号(富友终端号与TUSN号二选一)
    public static final String RESERVED_TRANSACTION_ID = "reserved_transaction_id";         //渠道交易流水号  trade_type 为 FWC、MPAY时返回（用于调起支付）
    public static final String RESERVED_MCHNT_ORDER_NO = "reserved_mchnt_order_no";         //商户订单号, 商户系统内部的订单号
    public static final String RESERVED_COUPON_FEE = "reserved_coupon_fee";                 //优惠金额（分） 微信、银联二维码类型返回
    public static final String RESERVED_BUYER_LOGON_ID = "reserved_buyer_logon_id";         //买家在渠道登录账号
    public static final String RESERVED_OPENID = "reserved_openid";                         //微信openid
    public static final String RESERVED_FUND_BILL_LIST = "reserved_fund_bill_list";         //支付宝交易资金渠道,详细渠道
    public static final String RESERVED_CHANNEL_ORDER_ID = "reserved_channel_order_id";     //条码流水号，用户账单二维码对应的流水
    public static final String RESERVED_IS_CREDIT = "reserved_is_credit";                   //1：表示信用卡、0：表示其他(非信用方式)、不填，表示未知
    public static final String RESERVED_TXN_FIN_TS = "reserved_txn_fin_ts";                 //用户支付时间yyyyMMddHHmmss
    public static final String RESERVED_SETTLEMENT_AMT = "reserved_settlement_amt";         //应结算订单金额，以分为单位的整数  只有成功交易才会返回 如果使用了商户免充值优惠券，该值为订单金额-商户免充值 如果没有使用商户免充值，该值等于订单金额
    public static final String RESERVED_BANK_TYPE = "reserved_bank_type";                   //付款方式
    public static final String RESERVED_HB_IS_SELLER = "reserved_hb_is_seller";             //返回值：1=商户出息
    public static final String RESERVED_SERVICE_CHARGE_FLAG = "reserved_service_charge_flag";//手续费减免标识 Y：表示减免（默认）、N：表示不减免
    public static final String RESERVED_SUB_MCHNT_CD = "reserved_sub_mchnt_cd";             //交易子商户号
    public static final String RESERVED_TRADE_TYPE = "reserved_trade_type";                 //交易类型  ALBS支付宝被扫、WXBS微信被扫、WXFP微信刷脸、ALFP支付宝刷脸
    public static final String RESERVED_PAY_INFO = "reserved_pay_info";                     //支付参数
    public static final String RESERVED_ADDN_INF = "reserved_addn_inf";                     //附加数据
    public static final String RESERVED_REFUND_AMT = "reserved_refund_amt";                 //退款金额
    public static final String RESERVED_PAYER_CARD_NO = "reserved_payer_card_no";           //云闪付卡号

    //[{"activity_id":"12345","amount":"1","merchant_contribute":"1","name":"BSagiiBBXm","other_contribute":"0","promotion_id":"10000","scope":"GLOBAL","type":"DISCOUNT","wxpay_contribute":"0"}]
    public static final String RESERVED_PROMOTION_DETAIL = "reserved_promotion_detail";     //微信营销详情（资金单位：分）
    public static final String RESERVED_PROMOTION_NAME = "name";                            //优惠名称
    public static final String RESERVED_PROMOTION_DETAIL_TYPE = "type";                     //营销详情 优惠类型(COUPON- 代金券，需要走结算资金的充值型代金券, (境外商户券币种不支付币种一致) DISCOUNT- 优惠券，丌走结算资金的免充值型优惠 券，(境外商户券币种不标价币种一致)
    public static final String RESERVED_PROMOTION_SCOPE = "scope";                          //优惠范围 GLOBAL- 全场代金券 SINGLE- 单品优惠
    public static final String RESERVED_PROMOTION_ACTIVITY_ID = "activity_id";              //活动ID  在微信商户后台配置的批次ID
    public static final String RESERVED_PROMOTION_DETAIL_PROMOTION_ID = "promotion_id";     // 优惠 ID，券或者立减优惠 id
    public static final String RESERVED_PROMOTION_DETAIL_AMOUNT = "amount";                 //营销详情 优惠券面额，优惠券面额=微信出资金额+商家出资金额+其他出资方金额
    public static final String RESERVED_PROMOTION_DETAIL_MERCHANT_CONTRIBUTE = "merchant_contribute";   //营销详情  商家出资金额(指交 易发起方)，单位为分
    public static final String RESERVED_PROMOTION_DETAIL_OTHER_CONTRIBUTE = "other_contribute";         //营销详情  必填 32 其他出资方出资金 额，单位为分
    public static final String RESERVED_PROMOTION_DETAIL_WXPAY_CONTRIBUTE = "wxpay_contribute";         //营销详情 必填 32 微信出资金额，单 位为分

    //"[{"amount":"1.00","merchant_contribute":"1.00", "name":"1.00元代金券","other_contribute":"0.00","template_id":"20221107000730017617007JGOUG","id":"202211070007300282330EB2Y9QQ","type":"ALIPAY_BIZ_VOUCHER"}]"
    public static final String RESERVED_VOUCHER_DETAIL_LIST = "reserved_voucher_detail_list";   //支付宝交易支付时所使用的所有优惠券信息（资金单位：元）
    public static final String RESERVED_VOUCHER_DETAIL_LIST_ID = "id";
    public static final String RESERVED_VOUCHER_DETAIL_LIST_NAME = "name";
    public static final String RESERVED_VOUCHER_DETAIL_LIST_TYPE = "type";
    public static final String RESERVED_VOUCHER_DETAIL_LIST_TEMPLATE_ID = "template_id";
    public static final String RESERVED_VOUCHER_DETAIL_LIST_AMOUNT = "amount";                          //优惠券面额，优惠券面额=商家出资金额+其他出资方金额
    public static final String RESERVED_VOUCHER_DETAIL_LIST_MERCHANT_CONTRIBUTE = "merchant_contribute";//商家出资金额(指交 易发起方)，单位为元
    public static final String RESERVED_VOUCHER_DETAIL_LIST_OTHER_CONTRIBUTE = "other_contribute";      //其他出资方出资金额，单位为元

    //"[{"goodsId":"STANDARD1026181538"," goodsName":"雪碧","discountAmount":"10.00"}]"
    public static final String RESERVED_DISCOUNT_GOODS_DETAIL = "reserved_discount_goods_detail";   //支付宝交易支付所使用的单品券优惠的商品优惠信息（资金单位：元）

    public static final String RETURN_CODE = "return_code";//返回状态码, SUCCESS/FAILED，此字段是通信标识，非交易标识，交易是否成功需要查看result_code来判断
    public static final String RETURN_MSG = "return_msg";//返回信息, 返回错误原因
    public static final String SETTLE_AMT = "settle_amt";//已转结金额（就是银行已经结算到商户富友账户上的金额）
    public static final String NOT_SETTLE_AMT = "not_settle_amt";//未转结金额（就是银行还未结算到商户富友账户上，如果需要发起结算，就需要富友垫付资金，所以就需要收取一定的手续费的）
    public static final String RESERVED_BOOK_BALANCE = "reserved_book_balance";//账面余额

    public static final String NOTIFY_CHNL_PAY_SSN = "chnlPaySsn";//结算交易本地跟踪号
    public static final String NOTIFY_OUT_ACNT_NO = "outAcntNo";//入账卡号（脱敏）
    public static final String NOTIFY_OUT_ACNT_NM = "outAcntNm";//入账姓名
    public static final String NOTIFY_SETTLE_DT = "settleDt";//入账卡号（脱敏）
    public static final String NOTIFY_BUSI_CD = "busiCd";//业务代码
    public static final String NOTIFY_RESERVED = "reserved";//退票原因（不参与签名）
    public static final String NOTIFY_PAY_AMT = "payAmt";//划款金额(单位：分)

    public static final String ERR_CODE = "err_code"; //错误代码, 0000成功,其他详细参见错误列表
    public static final String ERR_CODE_DES = "err_code_des"; //错误代码描述
    public static final String COUNT = "count"; //笔数
    public static final String TXN_LIST = "txnList"; //txnList列表 不参与验签
    public static final String SRC_MCHNT_CD = "src_mchnt_cd"; // 商户号
    public static final String KBPS_TRACE_NO = "kbps_trace_no"; // 流水号
    public static final String DEST_TXN_AMT = "dest_txn_amt"; // 金额（分）（除去手续费，实际到账金额）
    public static final String TXN_FEE_AMT = "txn_fee_amt"; // 手续费
    public static final String TXN_RCV_TS = "txn_rcv_ts"; // 发起结算时间，格式为：yyyyMMddHHmmss
    public static final String KBPS_SRC_SETTLE_DT = "kbps_src_settle_dt"; // 清算日期，格式为：yyyyMMdd
    public static final String PAY_ST = "pay_st"; // 结算状态：1：成功 2：超时 3：失败
    public static final String PAY_ST_DESC = "pay_st_desc"; // 结算状态描述
    public static final String PAY_MSG = "pay_msg"; // 结算失败时，失败的原因
    public static final String ACNT_NM = "acnt_nm"; // 入账卡户名
    public static final String ACNT_NO = "acnt_no"; // 入账卡号
    public static final String ISS_BANK_NM = "iss_bank_nm"; // 入账卡开户行名称

    public static final String OPEN_ID = "openid"; //用户在商户appid下的唯一标识
    public static final String RESERVED_BUSI_CD = "reserved_busi_cd"; //业务场景标识

    public enum BusiCdEnum {
        TX68("TX68", "dcc"),
        TX70("TX70", "edc");

        private String code;
        private String meaning;

        BusiCdEnum(String code, String meaning) {
            this.code = code;
            this.meaning = meaning;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getMeaning() {
            return meaning;
        }

        public void setMeaning(String meaning) {
            this.meaning = meaning;
        }
    }
}
