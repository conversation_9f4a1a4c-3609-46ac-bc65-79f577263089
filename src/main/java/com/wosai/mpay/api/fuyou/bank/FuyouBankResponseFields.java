package com.wosai.mpay.api.fuyou.bank;

public class FuyouBankResponseFields {
    public static final String IN_ORDER_NO = "inOrderNo"; //外部订单号
    public static final String VERSION = "version";
    public static final String INS_CD = "insCd";
    public static final String MERCHANT_NO_FUIOU = "merchantnoFuiou";
    public static final String CARD_TYPE = "cardType"; //卡属性, 卡属性, 01借记卡, 02信用卡, 03准贷记卡,04预付卡
    public static final String PAY_TYPE = "payType";
    public static final String SCAN_TYPE = "scanType";
    public static final String TERMINAL_ID = "terminalId";
    public static final String OUT_TRADE_NO = "outTradeNo";
    public static final String RETRI_FEF_NO = "retriFefNo";
    public static final String OUT_REFUND_NO = "outRefundNo";
    public static final String CHANNEL_TRADE_NO = "channelTradeNo";
    public static final String PAY_STATUS = "payStatus";
    public static final String PAY_MSG = "payMsg";
    public static final String TOTAL_FEE = "totalFee"; // 总费用
    public static final String REFUND_FEE = "refundFee"; // 退款金额
    public static final String CREATE_TIME = "createtime"; // 创建时间
    public static final String SETTLE_DATE = "settleDate"; // 结算日期
    public static final String CARDNO_ENCRYPT = "cardno_encrypt"; // 加密后的卡号
    public static final String CARDNO = "cardno"; // 卡号
    public static final String ISSUER = "issuer"; // 发卡机构
    public static final String TERMINAL_TRACE = "terminalTrace"; // 终端交易追踪号
    public static final String REFERENCE = "reference"; // 参考号或订单号
    public static final String BUYER_ID = "buyerId"; // 买家ID
    public static final String KEY_SIGN = "keySign"; // 签名密钥
    public static final String FEE_AMT = "feeAmt"; // 手续费金额
    public static final String KBPS_RSP_CD = "kbpsRspCd"; // Kbps响应代码
    public static final String KBPS_RSP_DESC = "kbpsRspDesc"; // Kbps响应描述


    public static final String MCHNT_CD = "mchntCd"; // Kbps响应描述
    public static final String MCHNT_NAME = "mchntName"; // Kbps响应描述
    public static final String RANDOM_STR = "randomStr"; // Kbps响应描述
    public static final String STR_ORDER_NO = "srcOrderNo"; // Kbps响应描述
    public static final String REFUND_ORDER_NO = "refundOrderNo"; // Kbps响应描述
    public static final String BUIS_CD = "busiCd"; // Kbps响应描述
    public static final String RESERVED_TELLER_INFO = "reserved_teller_info"; // Kbps响应描述
    public static final String TXN_DATA2 = "txnData2"; // Kbps响应描述
    public static final String REFUND_AMT = "refundAmt"; // Kbps响应描述
    public static final String RESERVER_JNL_NO = "reserver_jnl_no"; // Kbps响应描述
    public static final String SIGN = "sign"; // Kbps响应描述
    public static final String FY_TRACE_NO = "fyTraceNo"; // Kbps响应描述
    public static final String REFUND_TRACE_NO = "refundTraceNo"; // Kbps响应描述
    public static final String REFUND_SETTLE_DT = "refundSettleDt"; // Kbps响应描述

}
