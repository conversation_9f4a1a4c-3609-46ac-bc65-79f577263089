package com.wosai.mpay.api.fuyou;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/20
 */
public class FuyouTransferConstants {
    //转账类型 1:营销
    public static final String TRANSFER_TYPE_MARKET = "1"; // 交易成功



    /**
     * 错误码
     */
    public static final String SUCCESS = "0000"; // 交易成功
    public static final String BIZ_FAILED = "0066"; // 交易失败（转账或撤销转账失败）
    public static final String MERCHANT_NOT_FOUND = "1101"; //商户不存在
    public static final String SIGN_ERROR = "5002"; //验签失败
    public static final String DATA_VERIFY_FAILED = "5019"; // 数据校验失败
    public static final String GET_PUBLIC_KEY_FAILED = "5203"; //获取商户公钥失败
    public static final String USER_ACCOUNT_ERROR = "5204"; // 用户帐户状态异常
    public static final String TRACE_NO_USED = "5209"; // 该流水号已使用过，不允许重复使用
    public static final String SYSTEM_ERROR = "5138"; //内部错误
    public static final String USER_NOT_EXIST = "5348"; // 交易用户不存在
    public static final String MERCHANT_STATUS_ERROR = "5856"; //商户状态异常

    /**
     * 是否成功
     *
     * @param respCode
     * @return
     */
    public static boolean isSuccess(String respCode) {
        return SUCCESS.equals(respCode);
    }
}
