package com.wosai.mpay.api.fuyou;

import com.wosai.mpay.api.HttpResourceCallback;
import com.wosai.mpay.api.fuyou.bean.*;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.api.fuyou.util.Constants;

import java.util.Arrays;
import java.util.Date;

public class FuyouSharingTest {

    public static void main(String[] args) throws Exception {
        FuyouSharingTest fuyouSharingTest = new FuyouSharingTest();
//        fuyouSharingTest.testAddConcentrateRelationIn();
//        fuyouSharingTest.doWalletSharing();
//        fuyouSharingTest.testBatchSharing();
        fuyouSharingTest.queryWalletSharing();
    }

    public void testBatchSharing() throws MpayException {
        BatchTradeConcentrateIn batchTradeConcentrateIn = new BatchTradeConcentrateIn();
        batchTradeConcentrateIn.setTraceNo("batch_pay_test_007");
        batchTradeConcentrateIn.setConcentrateType("02");
        batchTradeConcentrateIn.setMchntCd("0002900F0370542");
        batchTradeConcentrateIn.setMchntCdConcentrate("0002900F0096235");
        batchTradeConcentrateIn.setAmt(1l);
        BatchTradeConcentrateIn.Item item = new BatchTradeConcentrateIn.Item();
        item.setSrcFasDate("20231016");
        item.setSrcFasSsn("100075582756");
        batchTradeConcentrateIn.setSrcFasSsnList(Arrays.asList(item));
        System.out.println(JsonUtil.objectToJsonString(batchTradeConcentrateIn));
        AsyncFuyouClient asyncFuyouClient = new AsyncFuyouClient();
        HttpResourceCallback<BatchTradeConcentrateOut> httpResourceCallback = new HttpResourceCallback<BatchTradeConcentrateOut>() {
            @Override
            public void onComplete(BatchTradeConcentrateOut result) {
                try {
                    System.out.println(JsonUtil.objectToJsonString(result));
                } catch (MpayException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(Throwable t) {

            }
        };
        asyncFuyouClient.call(Constants.BATCH_TRADE_CONCENTRATE, batchTradeConcentrateIn, Constants.MCHNT_PRIVATE_KEY, httpResourceCallback, BatchTradeConcentrateOut.class);
    }

    public void doWalletSharing(){
        MchntBalanceConcentrateIn in = new MchntBalanceConcentrateIn();
        in.setMchntCd("0002900F0370542");
        in.setMchntCdConcentrate("0002900F0096235");
        in.setTraceNo("test_sharing_003");
        in.setConcentrateType("02");
        in.setAmt(1l);
        AsyncFuyouClient asyncFuyouClient = new AsyncFuyouClient();
        HttpResourceCallback<MchntBalanceConcentrateOut> httpResourceCallback = new HttpResourceCallback<MchntBalanceConcentrateOut>() {
            @Override
            public void onComplete(MchntBalanceConcentrateOut result) {
                try {
                    System.out.println(JsonUtil.objectToJsonString(result));
                } catch (MpayException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(Throwable t) {

            }
        };

        asyncFuyouClient.call("https://richfront.fuioupay.com/cancleConcentrateRelationApply.fuiou", in, Constants.MCHNT_PRIVATE_KEY, httpResourceCallback, MchntBalanceConcentrateOut.class);
    }

    public void queryWalletSharing(){
        AsyncFuyouClient asyncFuyouClient = new AsyncFuyouClient();
        String receiverNo = "0002900F6571320";
        String payerAccountNo = "0007090F6715061";
        QueryBookkeepingTradeIn queryBookkeepingTradeIn = new QueryBookkeepingTradeIn();
        //traceNo长度30 transaction.id长度16 - 4 + nanotime 长度15
        queryBookkeepingTradeIn.setTraceNo("1234" + System.nanoTime());
        queryBookkeepingTradeIn.setMchntCd(receiverNo);
        queryBookkeepingTradeIn.setTradeType(FuyouProfitConstants.QUERY_TRADE_TYPE_BALANCE);
        queryBookkeepingTradeIn.setMchntCdTraceNo("****************");
//            queryBookkeepingTradeIn.setBatchNo(sharingBooks.get(0).getTradeNo());
        queryBookkeepingTradeIn.setStartDate("********");
        queryBookkeepingTradeIn.setEndDate("********");
        queryBookkeepingTradeIn.setPageNo(1);
        queryBookkeepingTradeIn.setPageSize(10);
        HttpResourceCallback<QueryBookkeepingTradeOut> httpResourceCallback = new HttpResourceCallback<QueryBookkeepingTradeOut>() {
            @Override
            public void onComplete(QueryBookkeepingTradeOut result) {

                System.out.println(result);
            }

            @Override
            public void onError(Throwable t) {

                System.out.println(t);
            }
        };
        asyncFuyouClient.call("", queryBookkeepingTradeIn, "", httpResourceCallback, QueryBookkeepingTradeOut.class);
    }

    public void testAddConcentrateRelationIn() throws Exception {
        AddConcentrateRelationIn in = new AddConcentrateRelationIn();
        in.setTraceNo(System.currentTimeMillis()+"");
//        in.setMchntCd("A003310F0002085");
        in.setMchntCd("0002900F6521360");

        //0002900F0370542
        //0002900F0096235
        in.setMchntCdConcentrate("0003050F6518836");

        //0003050F6518836
        //0002900F6521360
        in.setConcentrateTypes(Arrays.asList(
                "01","02","03","04"
        ));
        in.setOrderConcentrateScale(2000);
        in.setBalanceConcentrateScale(2000);
        in.setCheckType("2");
//        in.setMobile("18650258627");
        in.setUseType("04");
        AsyncFuyouClient asyncFuyouClient = new AsyncFuyouClient();
        HttpResourceCallback<AddConcentrateRelationOut> httpResourceCallback = new HttpResourceCallback<AddConcentrateRelationOut>() {
            @Override
            public void onComplete(AddConcentrateRelationOut result) {
                try {
                    System.out.println(JsonUtil.objectToJsonString(result));
                } catch (MpayException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(Throwable t) {
                System.out.println(t);
            }
        };
        asyncFuyouClient.call("https://richfront.fuioupay.com/addConcentrateRelation.fuiou", in, "", httpResourceCallback, AddConcentrateRelationOut.class);
    }
}
