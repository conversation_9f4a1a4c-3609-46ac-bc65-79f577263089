package com.wosai.mpay.api.fuyou.bean;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.wosai.mpay.api.fuyou.util.ClearTextUtil;

import java.util.ArrayList;
import java.util.List;

@JacksonXmlRootElement(localName = "xml")
public class BatchTradeConcentrateIn extends BaseRequest {
    /**
     * 操作流水号，调用方自己传，会校验唯一性 对私 对公
     */
    private String traceNo;

    /**
     * 商户号 对私 对公
     */
    private String mchntCd;

    private String mchntCdConcentrate; //被归集商户号

    private Long amt;  //分账金额，单位：分

    private String concentrateType;  //01：余额归集 02：待转结资金归集

    private List<Item> srcFasSsnList;

    private String signature;

    @Override
    public String generateClearText() {
        List<String> excludeFieldList = new ArrayList<>();
        excludeFieldList.add("signature");
        excludeFieldList.add("srcFasSsnList");
        return ClearTextUtil.generateClearText(this, excludeFieldList);
    }

    public String getTraceNo() {
        return traceNo;
    }

    public void setTraceNo(String traceNo) {
        this.traceNo = traceNo;
    }

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getMchntCdConcentrate() {
        return mchntCdConcentrate;
    }

    public void setMchntCdConcentrate(String mchntCdConcentrate) {
        this.mchntCdConcentrate = mchntCdConcentrate;
    }

    public Long getAmt() {
        return amt;
    }

    public void setAmt(Long amt) {
        this.amt = amt;
    }

    public String getConcentrateType() {
        return concentrateType;
    }

    public void setConcentrateType(String concentrateType) {
        this.concentrateType = concentrateType;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public List<Item> getSrcFasSsnList() {
        return srcFasSsnList;
    }

    public void setSrcFasSsnList(List<Item> srcFasSsnList) {
        this.srcFasSsnList = srcFasSsnList;
    }

    public static class Item{
        private String srcFasSsn;
        private String srcFasDate;

        public String getSrcFasSsn() {
            return srcFasSsn;
        }

        public void setSrcFasSsn(String srcFasSsn) {
            this.srcFasSsn = srcFasSsn;
        }

        public String getSrcFasDate() {
            return srcFasDate;
        }

        public void setSrcFasDate(String srcFasDate) {
            this.srcFasDate = srcFasDate;
        }
    }
}
