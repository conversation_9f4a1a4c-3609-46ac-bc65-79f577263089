package com.wosai.mpay.api.fuyou.bean;

import com.wosai.mpay.api.fuyou.util.ClearTextUtil;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;

import java.util.ArrayList;
import java.util.List;

@JacksonXmlRootElement(localName = "xml")
public class AddConcentrateRelationIn extends BaseRequest {
    /**
     * 操作流水号，调用方自己传，会校验唯一性 对私 对公
     */
    private String traceNo;

    /**
     * 商户号 对私 对公
     */
    private String mchntCd;

    private String mchntCdConcentrate; //被归集商户号 被归集商户在富友的唯一代码

    private List<String> concentrateTypes;  //01:余额归集 02:按转结金额归集 03:订单预归集 04:订单事后归集

    private Integer orderConcentrateScale; //订单归集最大比例 如：100% 传 10000 归集模式为 03 和 04 必填

    private Integer balanceConcentrateScale; //余额归集最大比例 如：100% 传 10000 归集模式为 01 和 02 必填

    private String checkType;//授权模式 1 短信模式 2 返回 url 不传默认 1

    private String mobile; //验证类型为短信必填，用来受签约短信

    private String useType; //01:缴纳品牌费 02:缴纳管理费 03:缴纳服务费 04:资金归集 05:缴纳其他费用

    private String signature;

    @Override
    public String generateClearText() {
        List<String> excludeFieldList = new ArrayList<>();
        excludeFieldList.add("signature");
        return ClearTextUtil.generateClearText(this, excludeFieldList);
    }

    public String getTraceNo() {
        return traceNo;
    }

    public void setTraceNo(String traceNo) {
        this.traceNo = traceNo;
    }

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getMchntCdConcentrate() {
        return mchntCdConcentrate;
    }

    public void setMchntCdConcentrate(String mchntCdConcentrate) {
        this.mchntCdConcentrate = mchntCdConcentrate;
    }

    public Integer getOrderConcentrateScale() {
        return orderConcentrateScale;
    }

    public void setOrderConcentrateScale(Integer orderConcentrateScale) {
        this.orderConcentrateScale = orderConcentrateScale;
    }

    public Integer getBalanceConcentrateScale() {
        return balanceConcentrateScale;
    }

    public void setBalanceConcentrateScale(Integer balanceConcentrateScale) {
        this.balanceConcentrateScale = balanceConcentrateScale;
    }

    public String getCheckType() {
        return checkType;
    }

    public void setCheckType(String checkType) {
        this.checkType = checkType;
    }

    public String getUseType() {
        return useType;
    }

    public void setUseType(String useType) {
        this.useType = useType;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public List<String> getConcentrateTypes() {
        return concentrateTypes;
    }

    public void setConcentrateTypes(List<String> concentrateTypes) {
        this.concentrateTypes = concentrateTypes;
    }
}
