package com.wosai.mpay.api.fuyou.bean;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.wosai.mpay.api.fuyou.util.ClearTextUtil;

import java.util.ArrayList;
import java.util.List;

@JacksonXmlRootElement(localName = "xml")
public class QueryBookkeepingTradeIn extends BaseRequest {
    /**
     * 操作流水号，调用方自己传，会校验唯一性 对私 对公
     */
    private String traceNo;

    /**
     * 商户号 对私 对公
     */
    private String mchntCd;

    private String tradeType;

    private String batchNo;

    private String srcFasSsn;

    private String mchntCdTraceNo;

    private String mchntCdChildTraceNo;

    private String startDate;

    private String endDate;

    private Integer pageNo;

    private Integer pageSize;

    private String signature;

    @Override
    public String generateClearText() {
        List<String> excludeFieldList = new ArrayList<>();
        excludeFieldList.add("signature");
        excludeFieldList.add("tradeType");
        excludeFieldList.add("batchNo");
        excludeFieldList.add("srcFasSsn");
        excludeFieldList.add("mchntCdTraceNo");
        excludeFieldList.add("mchntCdChildTraceNo");
        return ClearTextUtil.generateClearText(this, excludeFieldList);
    }

    public String getTraceNo() {
        return traceNo;
    }

    public void setTraceNo(String traceNo) {
        this.traceNo = traceNo;
    }

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getSrcFasSsn() {
        return srcFasSsn;
    }

    public void setSrcFasSsn(String srcFasSsn) {
        this.srcFasSsn = srcFasSsn;
    }

    public String getMchntCdTraceNo() {
        return mchntCdTraceNo;
    }

    public void setMchntCdTraceNo(String mchntCdTraceNo) {
        this.mchntCdTraceNo = mchntCdTraceNo;
    }

    public String getMchntCdChildTraceNo() {
        return mchntCdChildTraceNo;
    }

    public void setMchntCdChildTraceNo(String mchntCdChildTraceNo) {
        this.mchntCdChildTraceNo = mchntCdChildTraceNo;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }
}
