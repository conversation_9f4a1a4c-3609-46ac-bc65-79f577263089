package com.wosai.mpay.api.fuyou;

import java.util.Arrays;
import java.util.List;

/***
 * @ClassName: FuyouBusinessFields
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/9/13 9:42 AM
 */
public class FuyouBusinessFields {

    public static final String MCHNT_CD = "mchnt_cd";               //商户号,富友分配给二级商户的商户号
    public static final String TERM_ID = "term_id";                 //终端号(没有真实终端号统一填88888888)
    public static final String ORDER_TYPE = "order_type";           //订单类型：ALIPAY(支付宝)、WECHAT(微信)、UNIONPAY(银联二维码)、BESTPAY(翼支付)、DIGICCY(数字货币)
    public static final String GOODS_DES = "goods_des";             //商品名称, 显示在用户账单的商品、商品说明等地方
    public static final String GOODS_DETAIL = "goods_detail";       //单品优惠功能字段
    public static final String ADDN_INF = "addn_inf";               //附加数据 如果需要用到微信点餐数据回传，该字段需要填写OrderSource=FoodOrder
    public static final String MCHNT_ORDER_NO = "mchnt_order_no";   //商户订单号, 商户系统内部的订单号（5到30个字符、 只能包含字母数字,区分大小写)
    public static final String CURR_TYPE = "curr_type";             //货币类型,默认人民币：CNY
    public static final String ORDER_AMT = "order_amt";             //总金额, 订单总金额，单位为分
    public static final String TERM_IP = "term_ip";                 //实时交易终端IP(后期富友、银联侧风控主要依据，请真实填写) 暂时仅支持IPV4
    public static final String TXN_BEGIN_TS = "txn_begin_ts";       //交易起始时间, 订单生成时间，格式为yyyyMMddHHmmss
    public static final String GOODS_TAG = "goods_tag";             //商品标记
    public static final String PRODUCT_ID = "product_id";           //商品标识
    public static final String AUTH_CODE = "auth_code";             //扫码支付授权码，设备读取用户的条码或者二维码信息
    public static final String SCENE = "sence";                     //支付场景,默认1；1: 条码支付、2: 声波支付、3: 刷脸支付
    public static final String NOTIFY_URL = "notify_url";           //通知地址,接收富友异步通知回调地址,通知url必须为直接可访问的url,不能携带参数
    public static final String LIMIT_PAY = "limit_pay";             //限制支付, no_credit:不能使用信用卡 、credit_group：不能使用花呗以及信用卡
    public static final String TRADE_TYPE = "trade_type";           //订单类型: JSAPI--公众号支付、FWC--支付宝服务窗、支付宝小程序、LETPAY-微信小程序、BESTPAY--翼支付js、MPAY--云闪付小程序（控件支付）、UNIONPAY--云闪付扫码、UPBXJS--云闪付保险缴费
    public static final String OPENID = "openid";                   //用户标识(暂已废弃,不影响已对接完成的)
    public static final String SUB_OPENID = "sub_openid";           //子商户用户标识  支付宝服务窗为用户buyer_id(此场景必填)、微信公众号为用户的openid(小程序,公众号,服务窗必填)、APPLEPAY（相机扫码）该值不用填、UNIONPAY（云闪付扫码）该值必填
    public static final String SUB_APPID = "sub_appid";             //子商户公众号id, 微信交易为商户的appid(小程序,公众号必填)
    public static final String RESERVED_SUB_APPID = "reserved_sub_appid";           //子商户公众号id
    public static final String RESERVED_LIMIT_PAY = "reserved_limit_pay";           //限制支付 no_credit:不能使用信用卡 、credit_group：不能使用花呗以及信用卡
    public static final String RESERVED_EXPIRE_MINUTE = "reserved_expire_minute";   //交易关闭时间 默认120min，上限：360min
    public static final String RESERVED_FY_TERM_ID = "reserved_fy_term_id";         //富友终端号(富友终端号与TUSN号二选一),富友采购或自带机入网填此字段
    public static final String RESERVED_FY_TERM_TYPE = "reserved_fy_term_type";     //0:其他、1:富友终端、2:POS机、3:台卡、4:PC软件
    public static final String RESERVED_FY_TERM_SN = "reserved_fy_term_sn";         //终端序列号
    public static final String RESERVED_TXN_BONUS = "reserved_txn_bonus";           //积分抵扣金额,单位为分
    public static final String RESERVED_DEVICE_INFO = "reserved_device_info";       //设备信息，托传给微信。用于单品券核销
    public static final String RESERVED_QR_CODE = "reserved_qr_code";       //二维码（填写商户码牌的链接）


    //花呗分期示例值：{"dynamic_token_out_biz_no":"66666","hb_fq_num":"3","industry_reflux_info":{"scene_code":"metro_tradeorder","channel":"xxxx","scene_data":{"asset_name":"ALIPAY"}},"food_order_type":"qr_order"}
    //信用卡分期示例值：{"fq_num":"3"}
    // 银联分期示例值：{"hb_fq_num":"3","industry_reflux_info":"ICBC,ABC,CCB"}
    public static final String RESERVED_ALI_EXTEND_PARAMS = "reserved_ali_extend_params";     //JSON串
    /*  RESERVED_ALI_EXTEND_PARAMS */
    public static final String HB_FQ_NUM = "hb_fq_num";                                 //花呗分期期数：仅支持3、6、12
    public static final String HB_FQ_SELLER_PERCENT = "hb_fq_seller_percent";           //花呗分期商家手续费比例，目前仅支持用户出资，如需使用，请填写0！目前该字段未生效，我司会默认当0处理。
    public static final String INDUSTRY_REFLUX_INFO = "industry_reflux_info";           //行业数据回流信息。示例值：{\"scene_code\":\"metro_tradeorder\",\"channel\":\"xxxx\",\"scene_data\":{\"asset_name\":\"ALIPAY\"}}
    public static final String DYNAMIC_TOKEN_OUT_BIZ_NO = "dynamic_token_out_biz_no";   //条码支付中，目前支持的用法为：sence_no
    public static final String FQ_NUM = "fq_num";                       //信用卡分期期数：仅支持3、6、12
    public static final String FQ_SELLER_PERCENT = "fq_seller_percent"; //信用卡分期商家手续费比例，目前仅支持用户出资，如需使用，请填写0！目前该字段未生效，我司会默认当0处理。
    public static final String FQ_CHANNELS = "fq_channels";             //优先使⽤该资产进⾏⽀付，信用卡分期时传入，目前只支持传alipayfq_cc
    public static final String FOOD_ORDER_TYPE = "food_order_type";     //点餐场景类型：qr_order(店内扫码点餐)，pre_order(预点到店自提)，home_delivery(外送到家)，direct_payment(直接付款)，other(其他)
    /*  RESERVED_ALI_EXTEND_PARAMS */

    public static final String RESERVED_STORE_CODE = "reserved_store_code";             //门店id（目前仅支持支付宝，自定义门店id）
    public static final String RESERVED_ALIPAY_STORE_ID = "reserved_alipay_store_id";   //支付宝店铺编号(支付宝返回id)
    public static final String RESERVED_USER_TRUENAME = "reserved_user_truename";       //姓名
    public static final String RESERVED_USER_CREID = "reserved_user_creid";             //身份证
    public static final String RESERVED_FRONT_URL = "reserved_front_url";               //云闪付交易（UNIONPAY、MPAY、UPBXJS）支付成功跳转页面
    public static final String RESERVED_RESERVED_FRONT_FAIL_URL = "reserved_reserved_front_fail_url";   //云闪付交易（UNIONPAY、MPAY、UPBXJS）支付未完成跳转

    public static final String RESERVED_TERMINAL_INFO = "reserved_terminal_info";     //终端信息
    /*  RESERVED_TERMINAL_INFO */
    public static final String LOCATION = "location";                   //受理终端实时交易所在地经纬度信息  格式为纬度/经度，+表示北纬、东经，-表示南纬、西经
    public static final String SERIAL_NUM = "serial_num";               //TUSN号(富友终端号reserved_fy_term_id与reserved_terminal_info.serial_num号二选一)，请填写终端采集请求接口中的TUSN号
    public static final String ENCRYPT_RAND_NUM = "encrypt_rand_num";   //加密随机因子：仅在被扫支付类交易报文中出现：若付款码为 19 位数字，则取后6 位；若付款码为 EMV 二维码，则取其tag 57 的卡号/token 号的后 6 位
    public static final String SECRET_TEXT = "secret_text";             //密文数据：仅在被扫支付类交易报文中出现：64bit 的密文数据，对终端硬件序列号和加密随机因子加密后的结果。本子域取值为：64bit 密文数据进行base64 编码后的结果
    public static final String APP_VERSION = "app_version";             //终端应用程序的版本号。应用程序变更应保证版本号不重复。当长度不足时，右补空格
    /*  RESERVED_TERMINAL_INFO */

    public static final String RESERVED_BUSINESS_PARAMS = "reserved_business_params";     //商户传入业务信息，应用于安全，营销等参数直传场景，格式为 json 格式：{"data":"123"}

    public static final String RESERVED_SCENE_INFO = "reserved_scene_info";     //场景信息
    /*  RESERVED_SCENE_INFO */
    public static final String ID = "id";               //门店id
    public static final String NAME = "name";           //门店名称
    public static final String AREA_CODE = "area_code"; //门店行政区划码
    public static final String ADDRESS = "address";     //门店详细地址
    /*  RESERVED_TERMINAL_INFO */

    /*回调请求*/
    public static final String USER_ID = "user_id";                                 //用户在商户的id
    public static final String SETTLE_ORDER_AMT = "settle_order_amt";               //应结订单金额
    public static final String TRANSACTION_ID = "transaction_id";                   //渠道交易流水号
    public static final String TXN_FIN_TS = "txn_fin_ts";                           //支付完成时间，订单支付时间，格式为yyyyMMddHHmmss
    public static final String RESERVED_FY_SETTLE_DT = "reserved_fy_settle_dt";     //富友交易日期
    public static final String RESERVED_COUPON_FEE = "reserved_coupon_fee";         //优惠金额（分） 微信、银联二维码类型返回
    public static final String RESERVED_BUYER_LOGON_ID = "reserved_buyer_logon_id"; //买家在渠道登录账号
    public static final String RESERVED_FUND_BILL_LIST = "reserved_fund_bill_list"; //支付宝交易资金渠道,详细渠道
    public static final String RESERVED_FY_TRACE_NO = "reserved_fy_trace_no";       //富友系统内部追踪号
    public static final String RESERVED_CHANNEL_ORDER_ID = "reserved_channel_order_id"; //条码流水号，用户账单二维码对应的流水
    public static final String RESERVED_IS_CREDIT = "reserved_is_credit";           //1：表示信用卡、0：表示其他(非信用方式)、不填，表示未知
    public static final String RESERVED_ADDN_INF = "reserved_addn_inf";             //附加数据
    public static final String RESERVED_SETTLEMENT_AMT = "reserved_settlement_amt"; //应结算订单金额，以分为单位的整数  只有成功交易才会返回 如果使用了商户免充值优惠券，该值为订单金额-商户免充值 如果没有使用商户免充值，该值等于订单金额
    public static final String RESERVED_BANK_TYPE = "reserved_bank_type";           //付款方式
    public static final String RESERVED_HB_IS_SELLER = "reserved_hb_is_seller";     //返回值：1=商户出息
    public static final String RESERVED_SERVICE_CHARGE_FLAG = "reserved_service_charge_flag";   //手续费减免标识 Y：表示减免（默认）、N：表示不减免
    public static final String RESERVED_SUB_MCHNT_CD = "reserved_sub_mchnt_cd";                 //交易子商户号

    //[{"activity_id":"12345","amount":"1","merchant_contribute":"1","name":"BSagiiBBXm","other_contribute":"0","promotion_id":"10000","scope":"GLOBAL","type":"DISCOUNT","wxpay_contribute":"0"}]
    public static final String RESERVED_PROMOTION_DETAIL = "reserved_promotion_detail";     //微信营销详情（资金单位：分）

    //"[{"amount":"1.00","merchant_contribute":"1.00", "name":"1.00元代金券","other_contribute":"0.00","template_id":"20221107000730017617007JGOUG","id":"202211070007300282330EB2Y9QQ","type":"ALIPAY_BIZ_VOUCHER"}]"
    public static final String RESERVED_VOUCHER_DETAIL_LIST = "reserved_voucher_detail_list";   //支付宝交易支付时所使用的所有优惠券信息（资金单位：元）

    //"[{"goodsId":"STANDARD1026181538"," goodsName":"雪碧","discountAmount":"10.00"}]"
    public static final String RESERVED_DISCOUNT_GOODS_DETAIL = "reserved_discount_goods_detail";   //支付宝交易支付所使用的单品券优惠的商品优惠信息（资金单位：元）

    /*退款请求*/
    public static final String REFUND_ORDER_NO = "refund_order_no";     //商户退款单号 (5到30个字符、只能包含字母数字或者下划线，区分大小写)
    public static final String TOTAL_AMT = "total_amt";                 //总金额
    public static final String REFUND_AMT = "refund_amt";               //退款金额
    public static final String OPERATOR_ID = "operator_id";             //操作员
    public static final String RESERVED_ORIGI_DT = "reserved_origi_dt"; //原交易日期(yyyyMMdd)！该值必定等于reserved_fy_settle_dt(富友接收交易时间。理论和合作方下单时间一致。微量跨日交易会不一致)。不填该值，支持30天内的交易进行退款。填写该值，支持90天。

    /* 撤单请求 */
    public static final String CANCEL_ORDER_NO = "cancel_order_no"; //商户撤销单号

    /* 历史订单查询 */
    public static final String TRADE_DT = "trade_dt";                   //交易日期，非必填，默认当天。 查询往日交易时，必填，支持查询90天内交易
    public static final String CHANNEL_ORDER_ID = "channel_order_id";   //条码流水(线下支付，条码流水号)

    /* 手续费回调通知 */
    public static final String ORIG_MCHNT_ORDER_NO = "orig_mchnt_order_no";     //原商户订单号（退款交易时为原正订单号）
    public static final String CHANNEL_ORDER_NO = "channel_order_no";           //渠道订单号
    public static final String TXN_AMT = "txn_amt";                             //交易金额
    public static final String FEE_AMT = "fee_amt";                             //手续费金额（退款交易手续费为“-”）
    public static final String KBPS_TRACE_NO = "kbps_trace_no";                 //富友本地跟踪号
    public static final String RESERVED_FAS_SETTLE_DT = "reserved_fas_settle_dt";//清算日期
    public static final String RESERVED_DISCOUNT_AMT = "reserved_discount_amt"; //减免手续费金额
    public static final String RESERVED_SET_CD = "reserved_set_cd";             //交易手续费扣率模版

    public static final String RESERVED_BUSI_TYPE = "reserved_busi_type";       //查询余额类型 1：普通查询（默认） 2：钱包查询
    public static final String SETTLE_AMT = "amt";                              //提现金额
    public static final String SETTLED_AMT = "settled_amt";   //待结算已转结金额（txn_type为3时必传）

    public static final String RESERVED_SETTLED_AMT = "reserved_settled_amt";   //待结算已转结金额（txn_type为3时必传）
    public static final String RESERVED_NOT_SETTLE_AMT = "reserved_not_settle_amt";   //待结算已转结金额（txn_type为3时必传）
    public static final String TXN_TYPE = "txn_type";   //1：未转结金额结算 2：已转结金额结算 3：合并结算（已转结+未转结 同时结算，一笔到账） 4：交班到账
    public static final String RESERVED_POSTSCRIPT = "reserved_postscript";//附言（注：交班到账上送附言不生效）
    public static final String DATE = "date";   //查询日期（yyyyMMdd）
    public static final String START_INDEX = "start_index"; //分页开始序号 从1开始
    public static final String END_INDEX = "end_index"; //分页结束序号
    public static final String FY_TRACE_NO = "fy_trace_no"; //富友本地跟踪号, 结算响应的富友本地跟踪号，用于查询单笔结算交易（与withdraw_type字段必须填其中一个字段）

    public static final String RESERVED_ORDER_NO = "reserved_order_no"; //结算发起支持送外部订单号

    public static final String RESERVED_NOTIFY_URL = "reserved_notify_url"; //结算交易异步通知地址，非必填，不参与签名

    public static final String OUT_ORDER_NO = "out_order_no"; //结算发起支持送外部订单号　　txnList.out_order_no
    /**
     * 1：已转结余额结算
     * 2：D0手动结算
     * 3：钱包次日到账
     * 4：钱包即时到账
     * 5：合并结算
     * （与fy_trace_no字段必须填其中一个字段）
     */
    public static final String WITHDRAW_TYPE = "withdraw_type";


    /**
     * 云闪付服务商pid返佣参数
     */
    public static final String RESERVED_PID_INFO = "reserved_pid_info";


    /**
     * 各个接口需要参与签名的字段
     **/
    public static final List<String> PAY_SIGNATURE_PARAMS = Arrays.asList(FuyouProtocolFields.VERSION, FuyouProtocolFields.INS_CD, MCHNT_CD, TERM_ID, FuyouProtocolFields.RANDOM_STR, ORDER_TYPE, GOODS_DES, GOODS_DETAIL, ADDN_INF, MCHNT_ORDER_NO, CURR_TYPE, ORDER_AMT, TERM_IP, TXN_BEGIN_TS, GOODS_TAG, AUTH_CODE, SCENE);
    public static final List<String> PRECREATE_SIGNATURE_PARAMS = Arrays.asList(FuyouProtocolFields.VERSION, FuyouProtocolFields.INS_CD, MCHNT_CD, TERM_ID, FuyouProtocolFields.RANDOM_STR, GOODS_DES, GOODS_DETAIL, GOODS_TAG, PRODUCT_ID, ADDN_INF, MCHNT_ORDER_NO, CURR_TYPE, ORDER_AMT, TERM_IP, TXN_BEGIN_TS, NOTIFY_URL, LIMIT_PAY, TRADE_TYPE, OPENID, SUB_OPENID, SUB_APPID);
    public static final List<String> CANCEL_SIGNATURE_PARAMS = Arrays.asList(FuyouProtocolFields.VERSION, FuyouProtocolFields.INS_CD, MCHNT_CD, TERM_ID, FuyouProtocolFields.RANDOM_STR, ORDER_TYPE, MCHNT_ORDER_NO, CANCEL_ORDER_NO, OPERATOR_ID);
    public static final List<String> CLOSE_SIGNATURE_PARAMS = Arrays.asList(FuyouProtocolFields.VERSION, FuyouProtocolFields.INS_CD, MCHNT_CD, TERM_ID, FuyouProtocolFields.RANDOM_STR, ORDER_TYPE, MCHNT_ORDER_NO, SUB_APPID);
    public static final List<String> QUERY_SIGNATURE_PARAMS = Arrays.asList(FuyouProtocolFields.VERSION, FuyouProtocolFields.INS_CD, MCHNT_CD, TERM_ID, FuyouProtocolFields.RANDOM_STR, ORDER_TYPE, MCHNT_ORDER_NO);
    public static final List<String> HISTORY_QUERY_SIGNATURE_PARAMS = Arrays.asList(FuyouProtocolFields.VERSION, FuyouProtocolFields.INS_CD, MCHNT_CD, TERM_ID, FuyouProtocolFields.RANDOM_STR, ORDER_TYPE, TRADE_DT, MCHNT_ORDER_NO, CHANNEL_ORDER_ID, TRANSACTION_ID);
    public static final List<String> REFUND_SIGNATURE_PARAMS = Arrays.asList(FuyouProtocolFields.VERSION, FuyouProtocolFields.INS_CD, MCHNT_CD, TERM_ID, FuyouProtocolFields.RANDOM_STR, MCHNT_ORDER_NO, ORDER_TYPE, REFUND_ORDER_NO, TOTAL_AMT, REFUND_AMT, OPERATOR_ID);
    public static final List<String> REFUND_QUERY_SIGNATURE_PARAMS = Arrays.asList(FuyouProtocolFields.VERSION, FuyouProtocolFields.INS_CD, MCHNT_CD, TERM_ID, FuyouProtocolFields.RANDOM_STR, REFUND_ORDER_NO);
    public static final List<String> NOTIFY_SIGNATURE_PARAMS = Arrays.asList(FuyouResponseFields.RESULT_CODE, FuyouResponseFields.RESULT_MSG, FuyouProtocolFields.INS_CD, MCHNT_CD, TERM_ID, FuyouProtocolFields.RANDOM_STR, USER_ID, ORDER_AMT, SETTLE_ORDER_AMT, CURR_TYPE, TRANSACTION_ID, MCHNT_ORDER_NO, ORDER_TYPE, TXN_FIN_TS);
    public static final List<String> WALLET_QUERY_SIGNATURE_PARAMS = Arrays.asList(FuyouProtocolFields.INS_CD, MCHNT_CD, FuyouProtocolFields.RANDOM_STR);
    public static final List<String> SETTLE_SIGNATURE_PARAMS = Arrays.asList(FuyouProtocolFields.INS_CD, MCHNT_CD, FuyouProtocolFields.RANDOM_STR, SETTLE_AMT, FEE_AMT, TXN_TYPE);

    public static final List<String> TXN_FEE_QUERY_PARAMS = Arrays.asList(FuyouProtocolFields.VERSION, FuyouProtocolFields.INS_CD, MCHNT_CD, MCHNT_ORDER_NO, FuyouProtocolFields.RANDOM_STR);

    public static final List<String> QUERY_SETTLEMENT_PARAMS = Arrays.asList(FuyouProtocolFields.INS_CD, MCHNT_CD, MCHNT_ORDER_NO, FuyouProtocolFields.RANDOM_STR, DATE, START_INDEX, END_INDEX, FY_TRACE_NO, WITHDRAW_TYPE);

    public static final List<String> AUTH_2_OPENID_PARAMS = Arrays.asList(FuyouProtocolFields.VERSION, FuyouProtocolFields.INS_CD, MCHNT_CD, SUB_APPID, FuyouProtocolFields.RANDOM_STR, AUTH_CODE, ORDER_TYPE, TERM_IP, ORDER_AMT);

    //富友营销转账
    public static final List<String> TRANSFER_PARAMS = Arrays.asList(FuyouTransferFields.TRACE_NO, FuyouTransferFields.INS_CD, FuyouTransferFields.ACCOUNT_IN, FuyouTransferFields.MCHNT_CD, FuyouTransferFields.TXN_AMT, FuyouTransferFields.TRANS_TYPE, FuyouTransferFields.REMARK);
    //富友营销转账查询
    public static final List<String> TRANSFER_QUERY_PARAMS = Arrays.asList(FuyouTransferFields.TRACE_NO, FuyouTransferFields.INS_CD, FuyouTransferFields.ACCOUNT_IN, FuyouTransferFields.BEGIN_DATE, FuyouTransferFields.END_DATE, FuyouTransferFields.STATUS, FuyouTransferFields.REMARK);
    //富友营销转账退回
    public static final List<String> TRANSFER_REFUND_PARAMS = Arrays.asList(FuyouTransferFields.TRACE_NO, FuyouTransferFields.INS_CD, FuyouTransferFields.ACCOUNT_IN, FuyouTransferFields.MCHNT_CD, FuyouTransferFields.TXN_AMT, FuyouTransferFields.SRC_TRACE_NO, FuyouTransferFields.TRANS_DATE, FuyouTransferFields.REMARK);
}
