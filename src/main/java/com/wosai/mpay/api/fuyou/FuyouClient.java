package com.wosai.mpay.api.fuyou;

import com.google.common.collect.ImmutableList;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.FuyouSignature;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.XmlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/***
 * @ClassName: FuyouClient
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/9/14 11:35 AM
 */
public class FuyouClient {

    public static final Logger logger = LoggerFactory.getLogger(FuyouClient.class);
    private static final String XML_DEFINE = "<?xml version=\"1.0\" encoding=\"GBK\" standalone=\"yes\"?>";

    /**
     * 富友营销转账相关接口集合
     */
    public static final List<Integer> FUYOU_TRANS_METHOD_LIST = ImmutableList.of(
            FuyouConstants.METHOD_TRANSFER,
            FuyouConstants.METHOD_TRANSFER_QUERY,
            FuyouConstants.METHOD_TRANSFER_REFUND
    );

    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    /**
     *
     * @param serviceUrl
     * @param privateKey
     * @param request
     * @return
     * @throws MpayException
     * @throws MpayApiNetworkError
     */
    public Map<String,Object> call(String serviceUrl, String privateKey,  Map<String, Object> request, int method) throws MpayException, MpayApiNetworkError, UnsupportedEncodingException {
        String requestXml = preProcess(privateKey, request, method);
        String decodeResponse = callXml(serviceUrl, requestXml);
        return postProcess(decodeResponse);
    }

    /**
     * 原始数据返回，让调用者自行处理
     *
     * @param serviceUrl
     * @param requestXml
     * @return
     * @throws UnsupportedEncodingException
     * @throws MpayApiNetworkError
     */
    public String callXml(String serviceUrl, String requestXml) throws UnsupportedEncodingException, MpayApiNetworkError {
        logger.info("request {}", requestXml);
        String encodeRequest = URLEncoder.encode(requestXml, FuyouConstants.CHARSET_GBK);
        Map<String, Object> req = new HashMap<>();
        req.put(FuyouProtocolFields.REQ, encodeRequest);
        String response = HttpClientUtils.doPost(FuyouClient.class.getName(), null, null, serviceUrl, req, WeixinConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        String decodeResponse = URLDecoder.decode(response, FuyouConstants.CHARSET_GBK);
        logger.info("response {}",decodeResponse.replaceAll("\\n", ""));//返回的xml报文，有换行，打印日志时，打印在一行上面。
        return decodeResponse;
    }

    public static String preProcess(String privateKey, Map<String, Object> request, int method) throws MpayException, UnsupportedEncodingException {
        for(Object mapKey: request.keySet().toArray()){
            Object value = request.get(mapKey);
            if(value == null){
                request.remove(mapKey);
                continue;
            }
            String valueStr;
            if (value instanceof Map || value instanceof List) {
                valueStr = JsonUtil.toJsonStr(value);
            } else {
                valueStr = String.valueOf(value);
            }
            request.put(String.valueOf(mapKey), valueStr);
        }

        if (FUYOU_TRANS_METHOD_LIST.contains(method)) {
            //富友营销转账相关接口，签名字段名必须是"signature", 且不能上送非接口要求的字段，
            request.remove(ProtocolFields.SIGNATURE);
            String signature = FuyouSignature.md5WithRsaSign(request, privateKey, FuyouConstants.CHARSET_GBK, method);
            request.put(ProtocolFields.SIGNATURE, signature);
            return XML_DEFINE + XmlUtils.map2XmlString(request, "xml");
        }

        request.put(FuyouProtocolFields.RANDOM_STR, getRandomStr());
        request.remove(FuyouProtocolFields.SIGN);
        String sign = FuyouSignature.md5WithRsaSign(request, privateKey, FuyouConstants.CHARSET_GBK, method);
        request.put(ProtocolFields.SIGN, sign);

        return XML_DEFINE + XmlUtils.map2XmlString(request, "xml");
    }


    public static Map<String, Object> postProcess(String response) throws UnsupportedEncodingException {
        return XmlUtils.parse(response);
    }

    public static String getRandomStr(){
        return ThreadLocalRandom.current().nextLong() + "";
    }

}
