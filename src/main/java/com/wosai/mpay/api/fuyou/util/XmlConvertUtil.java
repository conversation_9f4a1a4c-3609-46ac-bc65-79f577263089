package com.wosai.mpay.api.fuyou.util;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class XmlConvertUtil {

	private static Logger logger = LoggerFactory
			.getLogger(XmlConvertUtil.class);
	private static final ObjectMapper xmlMapper = new XmlMapper();
	static {
		xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
	}

	public static <T> T xml2Bean(String xmlText, Class<T> clazz) throws Exception {
		return xmlMapper.readValue(xmlText, clazz);
	}

	public static <T> String bean2Xml(T bean) throws Exception{
		return xmlMapper.writeValueAsString(bean);
	}

}
