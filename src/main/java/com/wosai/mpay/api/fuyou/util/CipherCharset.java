package com.wosai.mpay.api.fuyou.util;

import java.nio.charset.Charset;

public class CipherCharset {
    public static String CIPHER_CHARSET = Charset.defaultCharset().name();
    public static final String CIPHER_CHARSET_GBK = "GBK";
    public static final String CIPHER_CHARSET_UTF8 = "UTF-8";

    public CipherCharset() {
    }

    public static void initCharset(String charset) {
        CIPHER_CHARSET = charset;
    }

    public static void initGBK() {
        CIPHER_CHARSET = "GBK";
    }

    public static void initUTF8() {
        CIPHER_CHARSET = "UTF-8";
    }

    public static String getCharset() {
        return CIPHER_CHARSET;
    }
}
