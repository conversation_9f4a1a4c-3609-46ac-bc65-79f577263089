package com.wosai.mpay.api.fuyou.bank;

public class FuyouBankConstants {
    public static final String CARD_TYPE_DEBIT = "01"; //借记卡
    public static final String CARD_TYPE_CREDIT = "02"; //贷记卡
    public static final String CARD_TYPE_SEMI_CREDIT = "03"; //准贷记卡
    public static final String CARD_TYPE_PREPAID_CARD = "04"; //预付卡

    public static final String PAY_TYPE_WX = "1";
    public static final String PAY_TYPE_ALIPAY = "2";
    public static final String PAY_TYPE_BANK = "3";
    public static final String PAY_TYPE_CASH = "4";
    public static final String PAY_TYPE_VCARD = "5";
    public static final String PAY_TYPE_QQ = "6";
    public static final String PAY_TYPE_BAIDU = "7";
    public static final String PAY_TYPE_JD = "8";
    public static final String PAY_TYPE_UNIONPAY = "9";
    public static final String PAY_TYPE_BESTPAY = "10";
    public static final String PAY_TYPE_PRE_AUTHORIZATION = "11";
    public static final String PAY_TYPE_WILD_CARD = "12";
    public static final String PAY_TYPE_WILD_CARD_PRE_AUTHORIZATION = "13";
    public static final String PAY_TYPE_FQ = "14";
    public static final String PAY_TYPE_DCEP = "15";

    public static final String PAY_STATUS_SUCCESS = "1";
    public static final String PAY_STATUS_REFUND_SUCCESS = "2";
    public static final String PAY_STATUS_CANCEL_SUCCESS = "3";
    public static final String PAY_STATUS_REVERSAL_SUCCESS = "4";
    public static final String PAY_STATUS_FAIL = "5";
    public static final String PAY_STATUS_IN_PRO = "6";

    public static final String CREATE_TIME_FORMAT = "yyyyMMddHHmmss";
    public static final String SETTLE_DATE = "yyyyMMdd";

    public static final String ORDER_TYPE_CARD = "CARD";
    public static final String ORDER_TYPE_SCAN = "SCAN";

    public static final String QUERY_SUCCESS_CODE = "0";

    public static final String REFUND_SUCCESS_CODE = "000000";

    public static final String NOTIFY_RESERVED_BUSI_CD_REVERSAL = "RX18"; //手续费冲正

}
