package com.wosai.mpay.api.fuyou;

import com.wosai.mpay.api.HttpResourceCallback;
import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.api.fuyou.bean.BaseOut;
import com.wosai.mpay.api.fuyou.bean.BaseRequest;
import com.wosai.mpay.api.fuyou.util.Sign;
import com.wosai.mpay.api.fuyou.util.XmlConvertUtil;
import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.util.AsyncClientUtil;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.TracingUtil;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.message.BasicNameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AsyncFuyouClient {
    public static final Logger logger = LoggerFactory.getLogger(AsyncFuyouClient.class);
    protected int connectTimeout = 2000;
    protected int readTimeout = 5000;

    private RequestConfig requestConfig;
    private CloseableHttpAsyncClient client;

    public AsyncFuyouClient() {
        initClient();
    }

    public AsyncFuyouClient(int readTimeout, int connectTimeout) {
        this.readTimeout = readTimeout;
        this.connectTimeout = connectTimeout;
        initClient();
    }

    public void initClient() {
        requestConfig = RequestConfig.custom().setConnectionRequestTimeout(connectTimeout).setConnectTimeout(connectTimeout).setSocketTimeout(readTimeout).build();
        client = AsyncClientUtil.getCloseableHttpAsyncClient(null, null);
    }

    public <T extends BaseOut, R extends BaseRequest> void call(String serviceUrl, R request, String privateKey, HttpResourceCallback<T> callback, Class<T> clazz) {
        try {
            doCall(serviceUrl, request, privateKey, callback, clazz);
        } catch (Throwable t) {
            callback.onError(t);
        }
    }

    private <T extends BaseOut, R extends BaseRequest> void doCall(String serviceUrl, R request, String privateKey, HttpResourceCallback<T> callback, Class<T> clazz) throws Exception {
        AsyncClientUtil.logRequest(logger, JsonUtil.objectMapper.writeValueAsString(request));
        String sign = Sign.sign(request.generateClearText(), privateKey);
        request.setSignature(sign);
        String reqWithSign = XmlConvertUtil.bean2Xml(request);
        logger.info("encrypt request is {}", reqWithSign);
        Map<String, String> param = new HashMap<String, String>();
        reqWithSign = reqWithSign.replaceFirst(" xmlns=\"\"", "");
        param.put("req", URLEncoder.encode(reqWithSign, "GBK"));
        HttpPost httpPost = new HttpPost(serviceUrl);
        httpPost.setConfig(requestConfig);
        List<NameValuePair> params = new ArrayList<>();
        for (String key : param.keySet()) {
            params.add(new BasicNameValuePair(key, param.get(key)));
        }
        try {
            httpPost.setEntity(new UrlEncodedFormEntity(params, AlipayConstants.CHARSET_UTF8));
        } catch (UnsupportedEncodingException e) {
        }
        long start = System.currentTimeMillis();
        client.execute(httpPost, AsyncClientUtil.getFutureCallback(logger, WeixinConstants.CHARSET_UTF8, AsyncClientUtil.ResponseType.STRING, HttpResourceCallback.<String>create(
                TracingUtil.getTraceCarrierItem(),
                (response, t) -> {
                    AsyncClientUtil.logResponse(logger, serviceUrl, System.currentTimeMillis() - start, response != null ? response.replaceAll("\\n", "") : null, t);
                    if (t != null) {
                        callback.onError(t);
                        return;
                    } else {
                        T baseOut = null;
                        try {
                            response = URLDecoder.decode(response, "GBK");
                            baseOut = XmlConvertUtil.xml2Bean(response, clazz);
                            logger.info(String.format("url: %s,  get response normal:  %s", serviceUrl, JsonUtil.toJsonStr(baseOut)));
                        } catch (Exception e) {
                            callback.onError(e);
                        }
                        callback.onComplete(baseOut);
                    }
                }
        )));
    }
}
