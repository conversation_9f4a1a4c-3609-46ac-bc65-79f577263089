package com.wosai.mpay.api.fuyou.bean;


import java.util.List;

public class MchntOrderConcentrateResultOut extends EncrptNotifyOut {

    private MchntBalanceConcentrateResultOutInfo data;

    public MchntBalanceConcentrateResultOutInfo getData() {
        return data;
    }

    public void setData(MchntBalanceConcentrateResultOutInfo data) {
        this.data = data;
    }

    public static class MchntBalanceConcentrateResultOutInfo {
        /**
         * 商户号
         */
        private String mchntCd;

        /**
         * 被归集商户号
         */
        private String mchntCdConcentrate;
        /**
         * 被归集金额
         */
        private Long amt;
        /**
         * 01：余额归集
         * 02：待转结资金归集
         */
        private String concentrateType;
        /**
         * 批次号
         */
        private String batchNo;

        /**
         * 0000 表示归集成功
         * 其他返回码表示失败
         */
        private String respCode;

        /**
         * 返回码描述
         */
        private String respDesc;

        private List<Item> list;

        public String getMchntCd() {
            return mchntCd;
        }

        public void setMchntCd(String mchntCd) {
            this.mchntCd = mchntCd;
        }

        public String getMchntCdConcentrate() {
            return mchntCdConcentrate;
        }

        public void setMchntCdConcentrate(String mchntCdConcentrate) {
            this.mchntCdConcentrate = mchntCdConcentrate;
        }

        public Long getAmt() {
            return amt;
        }

        public void setAmt(Long amt) {
            this.amt = amt;
        }

        public String getConcentrateType() {
            return concentrateType;
        }

        public void setConcentrateType(String concentrateType) {
            this.concentrateType = concentrateType;
        }

        public String getBatchNo() {
            return batchNo;
        }

        public void setBatchNo(String batchNo) {
            this.batchNo = batchNo;
        }

        public String getRespCode() {
            return respCode;
        }

        public void setRespCode(String respCode) {
            this.respCode = respCode;
        }

        public String getRespDesc() {
            return respDesc;
        }

        public void setRespDesc(String respDesc) {
            this.respDesc = respDesc;
        }

        public List<Item> getList() {
            return list;
        }

        public void setList(List<Item> list) {
            this.list = list;
        }
    }

    public static class Item{
        private String allocateTime;//归集时间 yyyy-MM-dd HH:mm:ss
        private String srcFasSsn;//源支付订单号 源交易流水号
        private String srcFasDate;//源交易日期 源交易日期,YYYYMMdd
        private Long amt;//归集金额 单位：分

        public String getAllocateTime() {
            return allocateTime;
        }

        public void setAllocateTime(String allocateTime) {
            this.allocateTime = allocateTime;
        }

        public String getSrcFasSsn() {
            return srcFasSsn;
        }

        public void setSrcFasSsn(String srcFasSsn) {
            this.srcFasSsn = srcFasSsn;
        }

        public String getSrcFasDate() {
            return srcFasDate;
        }

        public void setSrcFasDate(String srcFasDate) {
            this.srcFasDate = srcFasDate;
        }

        public Long getAmt() {
            return amt;
        }

        public void setAmt(Long amt) {
            this.amt = amt;
        }
    }
}
