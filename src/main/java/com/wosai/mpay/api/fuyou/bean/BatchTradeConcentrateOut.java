package com.wosai.mpay.api.fuyou.bean;

import com.wosai.mpay.api.fuyou.util.ClearTextUtil;

import java.util.ArrayList;
import java.util.List;

public class BatchTradeConcentrateOut extends BaseOut {
    /**
     * 操作流水号，调用方自己传，会校验唯一性 对私 对公
     */
    private String traceNo;

    /**
     * 商户号 对私 对公
     */
    private String mchntCd;

    private String mchntCdConcentrate;

    private Long amt;

    private String concentrateType; //01：余额归集 02：待转结资金归集

    private String batchNo;


    @Override
    public String generateClearText() {
        List<String> excludeFieldList = new ArrayList<String>();
        excludeFieldList.add("signature");
        return ClearTextUtil.generateClearText(this, excludeFieldList);
    }

    public String getTraceNo() {
        return traceNo;
    }

    public void setTraceNo(String traceNo) {
        this.traceNo = traceNo;
    }

    public String getMchntCd() {
        return mchntCd;
    }

    public void setMchntCd(String mchntCd) {
        this.mchntCd = mchntCd;
    }

    public String getMchntCdConcentrate() {
        return mchntCdConcentrate;
    }

    public void setMchntCdConcentrate(String mchntCdConcentrate) {
        this.mchntCdConcentrate = mchntCdConcentrate;
    }

    public Long getAmt() {
        return amt;
    }

    public void setAmt(Long amt) {
        this.amt = amt;
    }

    public String getConcentrateType() {
        return concentrateType;
    }

    public void setConcentrateType(String concentrateType) {
        this.concentrateType = concentrateType;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }
}
