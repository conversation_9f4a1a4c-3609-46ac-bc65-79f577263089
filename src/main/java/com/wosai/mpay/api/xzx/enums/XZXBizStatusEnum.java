package com.wosai.mpay.api.xzx.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className XZXBizStatusEnum
 * @description:
 * @create: 2025-04-24 09:39
 **/
public enum XZXBizStatusEnum {

    IN_TRADING("20", "交易中"),
    FAILED("80","交易失败"),
    SUCCEED("90","交易成功");

    private final String code;
    private final String description;

    XZXBizStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static XZXBizStatusEnum of(String type) {
        if (null == type) {
            return null;
        }
        for (XZXBizStatusEnum e : XZXBizStatusEnum.values()) {
            if (type.equals(e.code)) {
                return e;
            }
        }
        return null;
    }
}
