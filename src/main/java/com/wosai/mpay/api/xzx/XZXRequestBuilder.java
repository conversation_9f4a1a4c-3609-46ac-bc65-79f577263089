package com.wosai.mpay.api.xzx;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 新中新请求构建
 * @date 2025/4/23
 */
public class XZXRequestBuilder {
    private final Map<String,Object> request;

    public XZXRequestBuilder(){
        request = new LinkedHashMap<>(16);
    }

    public void set(String field, Object value) {
        request.put(field,  value);
    }

    public Map<String,Object> build(){
        return request;
    }
}
