package com.wosai.mpay.api.xzx.constants;

/**
 * <AUTHOR>
 * @description 新中新响应参数常量定义
 * @date 2025/4/22
 */
public class XZXResponseFieldsConstants {

    /**
     * 系统级应答参数
     * 0：成功
     * 其他：错误
     * 不可为空
     */
    public static final String SYSTEM_ERROR_CODE = "errcode";

    /**
     * 系统级应答参数
     * 对 API 调用参数（除sign外）进行签名获得。
     * 不可为空
     */
    public static final String SYSTEM_SIGN = "sign";


    //业务错误码
    public static final String BIZ_ERROR_CODE = "retcode";
    //业务错误信息
    public static final String BIZ_ERROR_MSG = "errmsg";
    public static final String ERROR_MSG = "retmsg";
    //token失效时间, 单位: 秒
    public static final String EXPIRES_IN = "expires_in";

    /**
     * 业务处理错误代码
     * 0000：成功
     * 其他：错误
     * 不可为空
     */
    public static final String RESP_CODE = "respCode";
    /**
     * 详细错误信息
     * 可空
     */
    public static final String RESP_INFO = "respInfo";
    /**
     * 请参考以下具体定义
     * 可空
     */
    public static final String OBJ = "obj";



    /// 获取access token 响应 ///////////

    /**
     * access_token: 表示访问令牌，获取访问令牌交易中返回，其他交易此字段为空
     * 类型：String(128)
     * 是否可为空：否
     * 样例：后续交易中的访问令牌使用此返回值
     */
    public static final String ACCESS_TOKEN = "access_token";

    /// 商户下单 ////////////////////////
    /**
     * mchAcctId: 商户号
     * 类型：String(12)
     * 是否可为空：否
     * 样例：平台分配的商户号
     */
    public static final String MCH_ACCT_ID = "mchAcctId";
    /**
     * outTradeNo: 商户订单号
     * 类型：String(32)
     * 是否可为空：否
     * 样例：最长32位
     */
    public static final String OUT_TRADE_NO = "outTradeNo";
    /**
     * tranDt: 交易时间
     * 类型：String(14)
     * 是否可为空：是
     * 样例：格式为(YYYYMMDDHH24MISS)
     */
    public static final String TRAN_DT = "tranDt";
    /**
     * prepayId: 预付订单号
     * 类型：String(32)
     * 是否可为空：否
     * 样例：调起收银台时传入
     */
    public static final String PREPAY_ID = "prepayId";
    /**
     * transactionId: 交易号
     * 类型：String(32)
     * 是否可为空：是
     * 样例：需传入custMemberId参数才会返回
     */
    public static final String TRANSACTION_ID = "transactionId";
    /**
     * devId: 终端类型
     * 类型：String
     * 是否可为空：否
     * 样例：详情见终端类型枚举
     */
    public static final String DEV_ID = "devId";
    /**
     * sysCode: 子系统号
     * 类型：Integer
     * 是否可为空：否
     */
    public static final String SYS_CODE = "sysCode";
    /**
     * custId: 租户ID
     * 类型：String(3)
     * 是否可为空：否
     * 样例：最长12位
     */
    public static final String CUST_ID = "custId";
    /**
     * extendParams: 扩展参数
     * 类型：Map<String, String>
     * 是否可为空：是
     * Map中各个key的含义：
     *   cashierUrl：收银台链接
     */
    public static final String EXTEND_PARAMS = "extendParams";
    /**
     * extendParams - cashierUrl: 收银台链接
     *  (Nested under EXTEND_PARAMS)
     */
    public static final String EXTEND_PARAMS_CASHIER_URL = "cashierUrl";


    /// 调起收银台 /////////
    /**
     * capDate: 日结日期
     * 类型：String(8)
     * 是否可为空：否
     * 样例：格式为YYYYMMDD
     */
    public static final String CAP_DATE = "capDate";
    /**
     * tranAmt: 交易金额
     * 类型：Integer
     * 是否可为空：否
     * 样例：交易金额，单位为分
     */
    public static final String TRAN_AMT = "tranAmt";
    /**
     * bizStatus: 交易状态
     * 类型：String(2)
     * 是否可为空：否
     * 样例：交易状态20-交易中，80-交易失败，90-交易成功
     */
    public static final String BIZ_STATUS = "bizStatus";
    /**
     * attach: 商户附加数据
     * 类型：String(64)
     * 是否可为空：是
     * 样例：订单查询、对账中返回
     */
    public static final String ATTACH = "attach";
    /**
     * systemDt: 交易时间
     * 类型：String(14)
     * 是否可为空：是
     * 样例：格式为(YYYYMMDDHH24MISS)
     */
    public static final String SYSTEM_DT = "systemDt";
    /**
     * extendParams - QRCode: 二维码链接 (Nested under EXTEND_PARAMS)
     */
    public static final String EXTEND_PARAMS_QR_CODE = "QRCode";
    /**
     * extendParams - appId: 小程序ID或公众号id (Nested under EXTEND_PARAMS)
     */
    public static final String EXTEND_PARAMS_APP_ID = "appId";
    /**
     * extendParams - timeStamp: 时间戳 (Nested under EXTEND_PARAMS)
     */
    public static final String EXTEND_PARAMS_TIME_STAMP = "timeStamp";
    /**
     * extendParams - nonceStr: 随机串 (Nested under EXTEND_PARAMS)
     */
    public static final String EXTEND_PARAMS_NONCE_STR = "nonceStr";
    /**
     * extendParams - package: 数据包 (Nested under EXTEND_PARAMS)
     */
    public static final String EXTEND_PARAMS_PACKAGE = "package";
    /**
     * extendParams - signType: 签名方式 (Nested under EXTEND_PARAMS)
     */
    public static final String EXTEND_PARAMS_SIGN_TYPE = "signType";


    /// 订单查询 ///////////
    /**
     * transDt: 交易时间
     * 类型：String(14)
     * 是否可为空：是
     * 样例：格式为(YYYYMMDDHH24MISS)
     */
    public static final String TRANS_DT = "transDt";

    /// 支付退款 ///////////////////

}
