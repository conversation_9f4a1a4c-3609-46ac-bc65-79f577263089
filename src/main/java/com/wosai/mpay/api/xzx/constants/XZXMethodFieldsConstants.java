package com.wosai.mpay.api.xzx.constants;

/**
 * <AUTHOR>
 * @description 新中新请求method常量定义
 * @date 2025/4/22
 */
public class XZXMethodFieldsConstants {

    /**
     * 请求方法定义, 获取访问令牌
     */
    public static final String METHOD_API_TOKEN = "synjones.cloudcard.api.token";

    /**
     * 请求方法定义, 商户下单
     */
    public static final String METHOD_PAYMENT_ORDERS_APPLY = "synjones.cloudcard.payment.orders.apply";

    /**
     * 请求方法定义, 调起收银台
     */
    public static final String METHOD_PAYMENT_CASHIER = "synjones.cloudcard.payment.cashier";

    /**
     * 请求方法定义, 订单查询
     */
    public static final String METHOD_PAYMENT_ORDERS_QUERY = "synjones.cloudcard.payment.orders.query";

    /**
     * 请求方法定义, 取消支付
     */
    public static final String METHOD_PAYMENT_ORDERS_CANCEL_PAY = "synjones.cloudcard.payment.orders.cancelPay";

    /**
     * 请求方法定义, 支付退款
     */
    public static final String METHOD_PAYMENT_ORDERS_REFUND = "synjones.cloudcard.payment.orders.refund";

    /**
     * 请求方法定义, 对账单下载
     */
    public static final String METHOD_PAYMENT_CMPFLIE_DOWNLOAD = "synjones.cloudcard.payment.cmpflie.download";

    /**
     * 请求方法定义, 交易结果通知
     */
    public static final String METHOD_PAYMENT_ORDER_NOTIFY = "synjones.cloudcard.payment.orders.notify";

}
