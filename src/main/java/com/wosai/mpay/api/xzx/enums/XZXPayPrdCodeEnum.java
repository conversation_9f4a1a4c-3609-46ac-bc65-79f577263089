package com.wosai.mpay.api.xzx.enums;

/**
 * <AUTHOR>
 * @description 新中新支付渠道代码枚举
 * @date 2025/4/22
 */
public enum XZXPayPrdCodeEnum {

    ACCT("$$$","账户"),
    ACCT_PRE("$01","账户(预支付)"),
    CASH_BASE("A01","现金"),
    BANK_BOC("A31","中国银行"),
    BANK_ICBC("A32","工商银行"),
    BANK_UNIONPAY("A36","银联"),
    BANK_ALIPAY("A37","支付宝"),
    BANK_WXPAY("A38","微信"),
    BANK_SHOUQIANBA("A40","收钱吧"),
    BANK_DUOLABAO("A41","哆啦宝"),
    BANK_TIANYI("A42","重庆大学缴费平台"),
    BANK_UNIONPAYSF("A43","银联商服");

    private final String code;
    private final String description;

    XZXPayPrdCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static XZXPayPrdCodeEnum of(String type) {
        if (null == type) {
            return null;
        }
        for (XZXPayPrdCodeEnum e : XZXPayPrdCodeEnum.values()) {
            if (type.equals(e.code)) {
                return e;
            }
        }
        return null;
    }
}


