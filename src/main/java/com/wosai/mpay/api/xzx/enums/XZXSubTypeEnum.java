package com.wosai.mpay.api.xzx.enums;

/**
 * <AUTHOR>
 * @description 新中新子交易枚举类型
 * @date 2025/4/22
 */
public enum XZXSubTypeEnum {

    SUB_TYPE_CREDIT_CONSUME_OFFLINE("#81", "卡"),
    SUB_TYPE_CARD_CONSUME_OFFLINE("###", "卡"),
    SUB_TYPE_CARD("#8#", "卡"),
    SUB_TYPE_CARD_IDENT("#80", "卡"),
    SUB_TYPE_ACCT("000", "在线"),
    SUB_TYPE_ACCT_OVERDRAW("00#", "在线（卡透支）"),
    SUB_TYPE_CARD_RECHARGE_EXT("#88", "卡"),
    SUB_TYPE_SELF_CARD_CHANGE("#90", "卡"),
    SUB_TYPE_YTJ_CARD_CHANGE("#91", "卡"),
    SUB_TYPE_AUTHCODE_CONSUME_LOCAL("#67", "付款码"),
    SUB_TYPE_AUTHCODE_CONSUME_BANK("#69", "付款码"),
    SUB_TYPE_AUTHCODE_CONSUME_LOCAL_OFFLINE("#70", "脱机码"),
    SUB_TYPE_ACCT_SPECIAL("A01", ""),
    SUB_TYPE_ACCT_BALANCE_SYSTEM("A02", "系统平帐"),
    SUB_TYPE_ACCT_BALANCE_MANUAL("A03", "手工平帐"),
    SUB_TYPE_ACCT_BALANCE_AUTO("A04", "自动平帐"),
    SUB_TYPE_PRE_CLOSE_ACCT("B04", "预销户标识"),
    SUB_TYPE_ACCT_CONSUME_SPECIAL("B05", "特殊费用"),
    SUB_TYPE_CARD_AND_ACCT_TRANS("K00", "卡到账户转账"),
    SUB_TYPE_ACCT_AND_CARD_TRANS("K02", "账户到卡转账");


    private final String code;
    private final String description;

    XZXSubTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static XZXSubTypeEnum of(String type) {
        if (null == type) {
            return null;
        }
        for (XZXSubTypeEnum e : XZXSubTypeEnum.values()) {
            if (type.equals(e.code)) {
                return e;
            }
        }
        return null;
    }
}