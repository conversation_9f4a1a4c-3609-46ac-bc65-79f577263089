package com.wosai.mpay.api.xzx;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.mpay.api.xzx.constants.XZXMethodFieldsConstants;
import com.wosai.mpay.api.xzx.constants.XZXProtocolFieldsConstants;
import com.wosai.mpay.api.xzx.constants.XZXResponseFieldsConstants;
import com.wosai.mpay.api.xzx.enums.XZXResponseErrorEnum;
import com.wosai.mpay.api.xzx.enums.XZXSystemErrorEnum;
import com.wosai.mpay.api.xzx.util.XZXUtil;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;

import static com.wosai.mpay.util.MapEncryptUtil.resetValueAndReturnNewMap;

/**
 * <AUTHOR>
 * @description 新中新请求
 * @date 2025/4/22
 */
public class XZXClient {

    public static final Logger log = LoggerFactory.getLogger(XZXClient.class);
    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    //打印日志时需要加密的字段
    private static final List<String> NEED_ENCRYPT_FIELDS_WHEN_LOG = Arrays.asList(
            XZXProtocolFieldsConstants.ACCESS_TOKEN);

    //默认加密后的值
    private static final String DEFAULT_ENCRYPT_VALUE = "*";

    /**
     * 请求接口
     *
     * @param requestUrl   请求地址
     * @param method       请求方法
     * @param params       请求参数
     * @param appSecretKey 密钥
     * @param privateKey   私钥
     * @param publicKey    公钥
     * @return
     * @throws MpayException
     * @throws MpayApiNetworkError
     */
    public Map<String, Object> call(String requestUrl, String method, Map<String, Object> params, String appSecretKey, String privateKey, String publicKey)
            throws MpayException, MpayApiNetworkError {

        //将方法添加到参数中
        params.put(XZXProtocolFieldsConstants.METHOD, method);

        log.info("request {}", toJsonStringWithEncryptFields(params));

        //加密应用级参数
        encryptApplicationRequestParams(params, appSecretKey);

        TreeMap<String, Object> requestMap = new TreeMap<>(params);

        //加签
        String signValue = getSignValue(requestMap, privateKey);
        requestMap.put(XZXProtocolFieldsConstants.SIGN, signValue);

        log.info("request add sign {}", toJsonStringWithEncryptFields(params));

        String response = "";

        try {
            response = HttpClientUtils.doPost(XZXClient.class.getName(), null, null,
                    requestUrl, requestMap, WebUtils.DEFAULT_CHARSET_UTF8, connectTimeout, readTimeout);

            log.info("response content {}", response);
        } catch (Exception e) {
            // access token 失效也会抛异常
            log.error("新中新请求异常: e.Msg={}", e.getMessage(), e);
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put(XZXResponseFieldsConstants.SYSTEM_ERROR_CODE, XZXSystemErrorEnum.REQUEST_FAILED.getCode());
            resultMap.put(XZXResponseFieldsConstants.RESP_CODE, XZXResponseErrorEnum.REQUEST_FAILED.getCode());
            resultMap.put(XZXResponseFieldsConstants.RESP_INFO, XZXResponseErrorEnum.REQUEST_FAILED.getMsg());
            return resultMap;
        }

        if (XZXMethodFieldsConstants.METHOD_PAYMENT_CASHIER.equals(method)) {
            return formatResponse(method, response);
        } else {
            return decryptResponse(method, response, publicKey, appSecretKey);
        }
    }

    /**
     * 加密应用级参数
     *
     * @param params
     * @throws MpayException
     */
    private void encryptApplicationRequestParams(Map<String, Object> params, String appSecretKey) throws MpayException {
        Map applicationRequestParams = MapUtils.getMap(params, XZXProtocolFieldsConstants.APPLICATION_PARAMS_KEY);
        if (null == applicationRequestParams) {
            log.error("加密应用级参数: 未查到应用级参数, method={}", params.get(XZXProtocolFieldsConstants.METHOD));
            throw new MpayException("加密应用级参数: 未查到应用级参数");
        }

        //将参数转换为字符串
        String paramsString = JsonUtil.objectToJsonString(applicationRequestParams);

        //加密
        String encryptValue = XZXUtil.encrypt(paramsString, appSecretKey, XZXProtocolFieldsConstants.DEFAULT_ENCRYPT_ALGORITHM);

        //用加密后的数据覆盖原有的值
        params.put(XZXProtocolFieldsConstants.APPLICATION_PARAMS_KEY, encryptValue);
    }

    /**
     * 获取加签值
     *
     * @param params
     * @param privateKey
     * @return
     */
    private String getSignValue(TreeMap<String, Object> params, String privateKey) throws MpayException {
        StringBuilder paramsString = new StringBuilder(1024);
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            paramsString.append(entry.getKey()).append(entry.getValue());
        }

        return RsaSignature.sign(paramsString.toString(), XZXProtocolFieldsConstants.DEFAULT_SIGN_ALGORITHM, privateKey);
    }

    /**
     * 解密响应数据
     *
     * @param method
     * @param response
     * @param publicKey
     * @param appSecretKey
     * @return
     * @throws MpayException
     */
    public Map<String, Object> decryptResponse(String method, String response, String publicKey, String appSecretKey) throws MpayException {
        TreeMap<String, String> responseMap = parseFormData(method, response);

        String systemErrorCode = responseMap.get(XZXResponseFieldsConstants.SYSTEM_ERROR_CODE);
        String errorCode = responseMap.getOrDefault(XZXResponseFieldsConstants.RESP_CODE, XZXResponseErrorEnum.SUCCESS.getCode());
        String errorInfo = responseMap.getOrDefault(XZXResponseFieldsConstants.RESP_INFO, XZXResponseErrorEnum.SUCCESS.getMsg());

        if (!XZXSystemErrorEnum.isSuccess(systemErrorCode) || !XZXResponseErrorEnum.isSuccess(errorCode)) {
            log.info("response {}", response);
            Map<String, Object> result = new HashMap<>();
            result.put(XZXResponseFieldsConstants.SYSTEM_ERROR_CODE, errorCode);
            result.put(XZXResponseFieldsConstants.BIZ_ERROR_MSG, errorInfo);
            return result;
        }

        //验签
        checkSign(method, responseMap, publicKey);

        //解密响应结果
        String applicationRequestParams = MapUtils.getString(responseMap, XZXProtocolFieldsConstants.APPLICATION_PARAMS_KEY);
        String resultString = XZXUtil.decrypt(applicationRequestParams, appSecretKey, XZXProtocolFieldsConstants.DEFAULT_ENCRYPT_ALGORITHM);
        Map<String, Object> resultMap = JsonUtil.jsonStringToObject(resultString, new TypeReference<Map<String, Object>>() {});

        log.info("resultMap {}", toJsonStringWithEncryptFields(resultMap));
        resultMap.put(XZXResponseFieldsConstants.SYSTEM_ERROR_CODE, systemErrorCode);
        return resultMap;
    }

    /**
     * 格式化响应数据
     *
     * @param method
     * @param response
     * @return
     * @throws MpayException
     */
    private Map<String, Object> formatResponse(String method, String response) throws MpayException {
        
        Map<String, Object> resultMap = JsonUtil.jsonStringToObject(response, new TypeReference<Map<String, Object>>() {});

        log.info("resultMap {}", toJsonStringWithEncryptFields(resultMap));
        return resultMap;
    }

    /**
     * 解析form格式的数据
     *
     * @param method
     * @param formData
     * @return
     * @throws MpayException
     */
    private TreeMap<String, String> parseFormData(String method, String formData) throws MpayException {
        TreeMap<String, String> resultMap = new TreeMap<>();
        try {
            for (String string : formData.split("&")) {
                int index = string.indexOf("=");
                if (index > 0 && index <= string.length() - 1) {
                    resultMap.put(string.substring(0, index), URLDecoder.decode(string.substring(index + 1), "utf-8"));
                }
            }
            return resultMap;
        } catch (UnsupportedEncodingException e) {
            log.error("解析响应数据异常, method={}, formData={}, error={}", method, formData, e.getMessage(), e);
            throw new MpayException("解析响应数据异常, method=" + method);
        }
    }

    /**
     * 检查签名
     *
     * @param method
     * @param resultMap
     * @param publicKey
     * @throws MpayException
     */
    private void checkSign(String method, TreeMap<String, String> resultMap, String publicKey) throws MpayException {
        StringBuilder text = new StringBuilder();
        String signValue = null;

        for (Map.Entry<String, String> entry : resultMap.entrySet()) {
            if (XZXProtocolFieldsConstants.SIGN.equals(entry.getKey())) {
                signValue = entry.getValue();
                continue;
            }
            text.append(entry.getKey()).append(entry.getValue());
        }

        if (!RsaSignature.validateSign(text.toString().getBytes(), signValue, XZXProtocolFieldsConstants.DEFAULT_SIGN_ALGORITHM, publicKey)) {
            log.warn("响应数据验签失败, method={}, resultMap={}", method, JsonUtil.objectToJsonString(resultMap));
            throw new MpayException("响应数据验签失败, method=" + method);
        }
    }

    /**
     * 给指定需要脱敏的字段进行脱敏，并返回Json String
     *
     * @param data
     * @return
     * @throws MpayException
     */
    private static String toJsonStringWithEncryptFields(Map<String, Object> data) throws MpayException {
        Map<String, Object> newMap = resetValueAndReturnNewMap(data, NEED_ENCRYPT_FIELDS_WHEN_LOG, DEFAULT_ENCRYPT_VALUE);
        return JsonUtil.objectToJsonString(newMap);
    }
}
