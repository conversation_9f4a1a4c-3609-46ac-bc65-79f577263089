package com.wosai.mpay.api.xzx.util;

import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Base64;
import com.wosai.mpay.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import java.security.GeneralSecurityException;

/**
 * <AUTHOR>
 * 新中新工具类
 */
public class XZXUtil {
    public static final Logger log = LoggerFactory.getLogger(XZXUtil.class);

    /**
     * 加密
     */
    public static String encrypt(String text, String key, String algorithm) throws MpayException {
        byte[] bytes = text.getBytes(); // 待加/解密的数据
        byte[] keyData = Base64.decode(key); // 密钥数据
        try {
            byte[] cipherBytes = SymmtricCryptoUtil.symmtricCrypto(bytes,
                    keyData, algorithm, Cipher.ENCRYPT_MODE);
            return Base64.encode(cipherBytes);
        } catch (GeneralSecurityException e) {
            log.error("加密错误: key={}, algorithm={}, text={}, error={}", key, algorithm, text, e.getMessage(), e);
            throw new MpayException("加密错误", e);
        }
    }

    /**
     * 解密
     */
    public static String decrypt(String text, String key, String algorithm) throws MpayException {
        byte[] bytes = Base64.decode(text); // 待加/解密的数据
        byte[] keyData = Base64.decode(key); // 密钥数据
        try {
            byte[] cipherBytes = SymmtricCryptoUtil.symmtricCrypto(bytes,
                    keyData, algorithm, Cipher.DECRYPT_MODE);
            return new String(cipherBytes);
        } catch (GeneralSecurityException e) {
            log.error("解密错误: key={}, algorithm={}, text={}, error={}", key, algorithm, text, e.getMessage(), e);
            throw new MpayException("解密错误", e);
        }
    }

////    /**
////     * 测试请求输出日志
////     * @param msg
////     */
//    public static void testLog(String msg, Object obj) {
////        return;
//        if (null == obj) {
//            System.out.println(msg);
//        } else if ( obj instanceof String) {
//            System.out.println(msg + "__" + obj);
//        } else {
//            try {
//                System.out.println(msg + "__" + JsonUtil.objectToJsonString(obj));
//            }catch (Exception e) {
//                System.out.println(e.getMessage());
//            }
//        }
//    }
}
