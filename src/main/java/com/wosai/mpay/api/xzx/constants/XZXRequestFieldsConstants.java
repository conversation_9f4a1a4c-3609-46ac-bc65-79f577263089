package com.wosai.mpay.api.xzx.constants;


import com.sun.xml.internal.bind.v2.model.core.ID;

/**
 * <AUTHOR>
 * @description 新中新请求参数常量定义
 * @date 2025/4/22
 */
public class XZXRequestFieldsConstants {

    /// 获取access token ///////////////////

    /**
     * 密钥key
     * 不可为空
     */
    public static final String APP_KEY = "appKey";


    /// 商户下单 //////////////

    /**
     * 商户号
     * 平台分配的商户号，不可为空
     */
    public static final String MCH_ACCT_ID = "mchAcctId";
    /**
     * 终端类型
     * 详情见终端类型枚举，不可为空
     */
    public static final String DEV_ID = "devId";
    /**
     * 商户订单号
     * 最长32位，不可为空
     */
    public static final String OUT_TRADE_NO = "outTradeNo";
    /**
     * 交易时间
     * 格式为(YYYYMMDDHH24MISS)，可为空
     */
    public static final String TRAN_DT = "tranDt";
    /**
     * 支付超时时间
     * 格式为(YYYYMMDDHH24MISS)，可为空
     */
    public static final String PAY_TIMEOUT = "payTimeout";
    /**
     * 交易金额
     * 交易金额，单位为分，不可为空
     */
    public static final String TRAN_AMT = "tranAmt";
    /**
     * 商品标题
     * 不可为空
     */
    public static final String BODY = "body";
    /**
     * 身份id
     * 传此参数则会返回transactionId，可为空
     */
    public static final String CUST_MEMBER_ID = "custMemberId";
    /**
     * 租户id
     * 可为空
     */
    public static final String CUST_ID = "custId";
    /**
     * 商品详情
     * 可为空
     */
    public static final String GOODS_DETAIL = "goodsDetail";
    /**
     * 商户附加数据
     * 订单查询、对账中返回，可为空
     */
    public static final String ATTACH = "attach";
    /**
     * 后台通知地址
     * 必须以http开头，可为空
     */
    public static final String NOTIFY_URL = "notifyUrl";
    /**
     * 前台通知地址
     * 必须以http开头，可为空
     */
    public static final String RETURN_URL = "returnUrl";
    /**
     * 扩展参数
     * 可为空
     */
    public static final String EXTEND_PARAMS = "extendParams";

    /// GoodsDetailRo 对象属性 //////////////////

    /**
     * 商品编号
     * 可空
     */
    public static final String GOODS_ID = "goodsId";
    /**
     * 商品名称
     * 可空
     */
    public static final String GOODS_NAME = "goodsName";
    /**
     * 商品数量
     * 可空
     */
    public static final String QUANTITY = "quantity";
    /**
     * 商品单价
     * 可空
     */
    public static final String PRICE = "price";
    /**
     * 商品类目
     * 可空
     */
    public static final String GOODS_CATEGORY = "goodsCategory";
    /**
     * 商品展示地址
     * 可空
     */
    public static final String SHOW_URL = "show_url";


    /// extendParams ////////////////////////

    /**
     * openid
     * 可空
     * devId为JSAPI时必须
     */
    public static final String OPEN_ID = "openId";
    /**
     * spbill_create_ip
     * 可空
     * 支持IPV4和IPV6两种格式的IP地址。调用微信支付API的机器IP
     */
    public static final String SPBILL_CREATE_IP = "spbill_create_ip";



    /// 调起收银台 //////////////
    /**
     * 预付订单号
     * 下单时返回的，不可为空
     */
    public static final String PREPAY_ID = "prepayId";
    /**
     * 学工号
     * 可为空
     */
    public static final String EMPNO = "empno";
    /**
     * APP的ticket
     * 可为空
     */
    public static final String TICKET = "ticket";
    /**
     * 快捷跳转标识
     * 1-开启快捷跳转，可为空
     */
    public static final String QUICK_JUMP = "quickJump";
    /**
     * 支付渠道
     * 当quickJump=1时不可空，可为空
     */
    public static final String PAY_PRD_CODE = "payPrdCode";


    /// 订单查询，取消支付 ///////////////////////


    /**
     * 交易号
     * 最长32位,与(商户订单号+sysCode)二选一即可，优先使用交易号,不可为空
     */
    public static final String TRANSACTION_ID = "transactionId";
    /**
     * 子系统号
     */
    public static final String SYS_CODE = "sysCode";


    /// 支付退款 /////////////////////////


    /**
     * 原交易号
     * 原订单的交易号,最长32位，不可为空
     */
    public static final String ORI_TRANSACTION_ID = "oriTransactionId";
    /**
     * 原交易金额
     * 交易金额，单位为分，不可为空
     */
    public static final String TOTAL_AMT = "totalAmt";

    /// 对账单下载 ///////////////////////////

    /**
     * 对账日期
     * YYYYMMDD，不可为空
     */
    public static final String CMP_DATE = "cmpDate";


    /// 交易结果通知 ///////////////////////

    /**
     * 平台入账时间
     * 格式为(YYYYMMDDHH24MISS)，可空
     */
    public static final String SYSTEM_DT = "systemDt";
    /**
     * 交易状态
     * 交易状态20-交易中，80-交易失败，90-交易成功，可空
     */
    public static final String BIZ_STATUS = "bizStatus";
}
