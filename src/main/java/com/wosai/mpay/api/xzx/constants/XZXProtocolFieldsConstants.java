package com.wosai.mpay.api.xzx.constants;

/**
 * <AUTHOR>
 * @description 新中新请求protocol常量定义
 * @date 2025/4/22
 */
public class XZXProtocolFieldsConstants {
    /**
     * 请求头
     */
    public static final String CONTENT_TYPE = "Content-Type";

    /**
     * 系统级请求参数
     */
    public static final String METHOD = "method";               //请求方法
    public static final String APP_KEY = "app_key";             //分配给应用的AppKey, 创建应用时可获得
    public static final String FORMAT = "format";               //应用级参数格式。目前支持格式为json
    public static final String TIMESTAMP = "timestamp";         //时间戳，格式为yyyy-mm-dd HH:mm:ss，例如：2013-05-06 13:52:03。API服务端允许客户端请求时间误差为6分钟
    public static final String ACCESS_TOKEN = "access_token";   //表示访问令牌，第一次获取访问令牌为128个字节的字符0
    public static final String API_VERSION = "v";               //API协议版本，可选值:2.0
    public static final String SIGN = "sign";                   //签名值，对API调用参数（除sign字段外）进行签名获得。
    public static final String SIGN_METHOD= "sign_method";      //签名算法

    /**
     * 应用级请求参数
     */
    public static final String APPLICATION_PARAMS_KEY = "request";

    /**
     * 协议默认值
     */
    public static final String DEFAULT_APPLICATION_FORMAT = "json"; //默认的应用级参数格式
    public static final String DEFAULT_API_VERSION = "2.0"; //默认的接口版本
    public static final String DEFAULT_SIGN_METHOD = "rsa"; //签名方法
    public static final String DEFAULT_SIGN_ALGORITHM = "SHA1withRSA"; //默认的签名算法
    public static final String DEFAULT_ENCRYPT_ALGORITHM = "DESede"; //默认的加解密算法
    //初始请求时的访问令牌。第一次获取访问令牌时为128个字节的字符0
    public static final String DEFAULT_ACCESS_TOKEN = "00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"; //
}
