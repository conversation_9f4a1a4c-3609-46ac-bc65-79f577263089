package com.wosai.mpay.api.xzx.enums;

/**
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className XZXResponseErrorEnum
 * @description: 新中新应用级错误
 * @create: 2025-04-23 13:48
 * @version 1.0
 **/
public enum XZXResponseErrorEnum {


    UNKNOWN("999999", "未知错误"),
    // token 失效也会报该错误
    REQUEST_FAILED("999998", "请求失败"),
    SUCCESS("0000", "成功"),
    PROCESSING_SUCCESS("6206", "处理成功");

    private final String code;
    private final String msg;

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    XZXResponseErrorEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static XZXResponseErrorEnum of(String code) {
        if (null == code) {
            return UNKNOWN;
        }
        for (XZXResponseErrorEnum e : XZXResponseErrorEnum.values()) {
            if (code.equals(e.code)) {
                return e;
            }
        }
        return UNKNOWN;
    }

    /**
     * 请求是否成功
     *
     * @param errorCode
     * @return
     */
    public static boolean isSuccess(String errorCode) {
        return XZXResponseErrorEnum.SUCCESS.getCode().equals(errorCode) || XZXResponseErrorEnum.PROCESSING_SUCCESS.getCode().equals(errorCode);
    }
}
