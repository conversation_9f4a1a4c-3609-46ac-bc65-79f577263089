package com.wosai.mpay.api.xzx.enums;

/**
 * <AUTHOR>
 * @description 新中新终端类型枚举
 * @date 2025/4/22
 */
public enum XZXDevIdEnum {
    DEV_ID_APP("APP", "ticket登录"),
    DEV_ID_API("API","api信任登录"),
    DEV_ID_H5("WAP","h5前端"),
    DEV_ID_WEB("WEB","web前端"),
    DEV_ID_BARCODE("BARCODE","二维码"),
    DEV_ID_ALIPAY("ALIPAY","支付宝客户端"),
    DEV_ID_WECHAT("WECHAT","微信客户端"),
    DEV_ID_MINI_PROGRAM("MINI_PROGRAM","小程序");

    private final String code;
    private final String description;

    XZXDevIdEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static XZXDevIdEnum of(String type) {
        if (null == type) {
            return null;
        }
        for (XZXDevIdEnum e : XZXDevIdEnum.values()) {
            if (type.equals(e.code)) {
                return e;
            }
        }
        return null;
    }
}
