package com.wosai.mpay.api.bocom;

import com.wosai.mpay.api.SSLEnvFlag;
import com.wosai.mpay.api.icbc.ICBCClient;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description BOCOMClient
 * @Date 2023/12/7 5:38 PM
 */
public class BOCOMClient {
    public static final Logger log = LoggerFactory.getLogger(BOCOMClient.class);

    private int connectTimeout = 3000;
    private int readTimeout = 30000;
    private static HostnameVerifier hostnameVerifier = null;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map<String, Object> call(String serviceUrl, Map<String, Object> request, String privateKey, String publicKey) throws Exception {

        SSLContext sslContext = SSLEnvFlag.turnOffSSl() ? SSLUtil.getUnsafeSSLContext() : null;
        //sign 不参与签名
        request.remove(BOCOMProtocolFields.SIGN);
        //generate sign
        String sign = RsaSignature.bocomSign(serviceUrl, request, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, privateKey, BOCOMConstants.CHARSET_UTF8);
        request.put(BOCOMProtocolFields.SIGN, sign);
        log.info("bocom request is {}" , JsonUtil.objectToJsonString(request));

        String responseStr = HttpClientUtils.doPost(ICBCClient.class.getName(), sslContext, getHostnameVerifier(), serviceUrl, request, BOCOMConstants.CHARSET_UTF8, connectTimeout, readTimeout);

        log.info("bocom response is {}" , responseStr);

        Map<String, Object> response = JsonUtil.jsonStrToObject(responseStr, Map.class);

        return MapUtils.getMap(response, BOCOMResponseFields.RSP_BIZ_CONTENT);
    }

    public static  HostnameVerifier  getHostnameVerifier() {
        if(null == hostnameVerifier){
            hostnameVerifier = (arg0, arg1) -> true;
        }
        return hostnameVerifier;
    }
}
