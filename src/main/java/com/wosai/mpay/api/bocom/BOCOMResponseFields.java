package com.wosai.mpay.api.bocom;

/**
 * <AUTHOR>
 * @Description BOCOMResponseFields
 * @Date 2023/12/7 5:09 PM
 */
public class BOCOMResponseFields {
    public static final String RSP_BIZ_CONTENT = "rsp_biz_content";

    public static final String BIZ_STATE = "biz_state"; //返回访问状态字 S-成功 F-失败
    public static final String RSP_CODE = "rsp_code";   //返回码
    public static final String RSP_MSG = "rsp_msg";     //逻辑异常返回信息

    /**    RSP_HEAD   **/
    public static final String RSP_HEAD = "rsp_head";
    public static final String RESPONSE_CODE = "response_code";     //返回码
    public static final String RESPONSE_MSG = "response_msg";       //返回码描述
    public static final String TRANS_CODE = "trans_code";           //交易标识
    public static final String RESPONSE_STATUS = "response_status"; //交易状态 P-处理中  F-失败  S-成功
    public static final String RESPONSE_TIME = "response_time";     //响应时间

    /**   RSP_BODY   **/
    public static final String RSP_BODY = "rsp_body";
    public static final String PAY_MER_TRAN_NO = "pay_mer_tran_no";     //商户交易编号
    public static final String TOTAL_AMOUNT = "total_amount";           //商户订单总金额
    public static final String TRD_DSCT_AMOUNT = "trd_dsct_amount";     //第三方活动优惠金额
    public static final String CURRENCY = "currency";                   //币种
    public static final String DETAIL = "detail";                       //商品详情
    public static final String BUYER_PAY_AMOUNT = "buyer_pay_amount";   //买家实付金额
    public static final String SYS_ORDER_NO = "sys_order_no";           //交行内部订单号
    public static final String PAY_DSCT_AMOUNT = "pay_dsct_amount";     //支付优惠金额 | 退款支付优惠金额

    /*--- REQUIRE_VALUES START ---- */
    //require_values:{}
    public static final String REQUIRE_VALUES = "require_values";   //额外返回的字段
    public static final String OPEN_ID = "open_id";                 //付款人信息（微信用户标识/支付宝用户ID）
    public static final String SUB_OPENID = "sub_openid";           //付款人信息（微信用户子标识/买家支付宝账号）
    public static final String BANK_TRAN_NO = "bank_tran_no";       //银行端交易流水号
    public static final String REFUND_INFO = "refund_info";         //微信支付宝详细退货信息
    public static final String PAYMENT_INFO = "payment_info";       //微信支付宝详细付款信息
    public static final String THIRD_PARTY = "third_party";         //第三方渠道
    public static final String THIRD_PARTY_TRAN_NO = "third_party_tran_no"; //第三方渠道交易流水号
    /*--- REQUIRE_VALUES END ---- */

    //INITIAL初始化、PAIED交易成功、WAITPAY等待支付、REFUNDED部分退款、REFUNDALL全部退款、CLOSED订单关闭
    public static final String ORDER_STATUS = "order_status";       //订单状态 以此字段来确定订单状态
    public static final String TRAN_STATE = "tran_state";           //PROCESS 处理中 SUCCESS 交易成功 FAILURE 交易失败
    public static final String TRAN_STATE_CODE = "tran_state_code"; //交易状态码
    public static final String TRAN_STATE_MSG = "tran_state_msg";   //交易状态提示
    public static final String REFUNDED_AMOUNT = "refunded_amt";    //商户已退款金额

    public static final String DONE_REFUND_AMOUNT = "done_refund_amount";   //合计已退款金额
    public static final String REFUND_AMOUNT = "refund_amount";             //本次退款请求对应的退款金额
    public static final String CHANNEL_TYPE = "channel_type";               //通道类型 01本行B2C 02跨行B2C 03本行B2B 04跨行B2B
    public static final String REFUND_ORDER_NO = "refund_order_no";         //退款单据号

}
