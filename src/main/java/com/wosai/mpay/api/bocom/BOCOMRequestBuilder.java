package com.wosai.mpay.api.bocom;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.api.alipay.ProtocolV2Fields;
import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.util.MapUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Description BOCOMRequestBuilder
 * @Date 2023/12/7 5:45 PM
 */
public class BOCOMRequestBuilder {
    private static ObjectMapper om = new ObjectMapper();
    DateFormat df = new SimpleDateFormat(BOCOMConstants.DATE_TIME_FORMAT);
    Map<String, Object> request = new TreeMap<>();
    Map<String, Object> bizContent = new TreeMap<>();
    Map<String, Object> reqHead = new TreeMap<>();
    Map<String, Object> reqBody = new TreeMap<>();

    public BOCOMRequestBuilder() {

        request.put(BOCOMProtocolFields.CHARSET, BOCOMConstants.CHARSET_UTF8);
        request.put(BOCOMProtocolFields.FMT_TYPE, BOCOMConstants.FORMAT_JSON);
        request.put(BOCOMProtocolFields.MSG_ID, UUID.randomUUID().toString().replace("-", ""));
        request.put(BOCOMProtocolFields.AUTH_TOKEN, "");
        request.put(BOCOMProtocolFields.TIMESTAMP, df.format(new Date()));
    }

    public void bizSet(String field, Object value) {
        request.put(field,  value);
    }

    public Map<String,Object> getRequest(){
        return request;
    }

    public Map<String,Object> getBizContent(){
        return bizContent;
    }

    public void setReqHead(String field, Object value) {
        reqHead.put(field,  value);
    }

    public Map<String, Object> getReqHead() {
        return reqHead;
    }

    public void setReqBody(String field, Object value) {
        reqBody.put(field,  value);
    }

    public Map<String, Object> getReqBody() {
        return reqBody;
    }

    public Map<String, Object> build() throws BuilderException {

        if (MapUtils.isNotEmpty(reqHead)) {
            bizContent.put(BOCOMBusinessFields.REQ_HEAD, reqHead);
        }

        if (MapUtils.isNotEmpty(reqBody)) {
            bizContent.put(BOCOMBusinessFields.REQ_BODY, reqBody);
        }

        if (MapUtils.isNotEmpty(bizContent)) {
            try {
                request.put(ProtocolV2Fields.BIZ_CONTENT, om.writeValueAsString(bizContent));
            }
            catch (JsonProcessingException e) {
                throw new BuilderException("unable to serialize bizContent in JSON format.", e);
            }
        }
        return request;
    }

}
