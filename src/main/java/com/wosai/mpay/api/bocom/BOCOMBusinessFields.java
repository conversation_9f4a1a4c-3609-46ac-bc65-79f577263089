package com.wosai.mpay.api.bocom;

/**
 * <AUTHOR>
 * @Description BOCOMBusinessFields
 * @Date 2023/12/7 4:40 PM
 */
public class BOCOMBusinessFields {

    public static final String REQ_HEAD = "req_head";
    public static final String TRANS_TIME = "trans_time";   //交易时间 yyyyMMddHHmmss
    public static final String VERSION = "version";         //接口版本号

    public static final String REQ_BODY = "req_body";
    public static final String VALID_PERIOD = "valid_period";       //交易失效时间 yyyyMMddHHmmss
    public static final String SCAN_CODE_TEXT = "scan_code_text";   //付款码文本
    public static final String TERMINAL_INFO = "terminal_info";     //终端号
    public static final String PARTNER_ID = "partner_id";           //服务商编号
    public static final String MER_MEMO = "mer_memo";               //商户内部备注
    public static final String CURRENCY = "currency";               //币种 CNY
    public static final String TERM_BATCH_NO = "term_batch_no";     //终端批次号
    public static final String TRAN_SCENE = "tran_scene";           //交易场景 B2C-API-SCANCODE
    public static final String IP = "ip";                           //ip
    public static final String MER_PTC_ID = "mer_ptc_id";           //商户编号
    public static final String MER_TRADE_TIME = "mer_trade_time";   //商户侧交易时间 HHmmss
    public static final String MER_TRADE_DATE = "mer_trade_date";   //商户侧交易时间 yyyyMMdd
    public static final String TERM_POS_NO = "term_pos_no";         //终端流水号
    public static final String SHOP_ID = "shop_id";                 //门店编号
    public static final String PAY_MER_TRAN_NO = "pay_mer_tran_no"; //商户交易编号
    public static final String TOTAL_AMOUNT = "total_amount";       //商户订单总金额（元）
    public static final String LOCATION = "location";               //线上或线下
    public static final String DETAIL = "detail";                   //商品详情
    public static final String TRAN_CONTENT = "tran_content";       //交易内容
    public static final String DISABLE_PAY_CHANNELS = "disable_pay_channels"; //支付禁用渠道  balance, moneyFund多个之间用,分隔
    public static final String LONGITUDE = "longitude";             //经度
    public static final String LATITUDE = "latitude";               //纬度
    public static final String NOTIFY_URL = "notify_url";           //后台通知地址

    /*--- REQUIRE_FIELDS START ---- */
    //[{"require_field":"bank_tran_no"},{"require_field":"third_party"}]
    public static final String REQUIRE_FIELDS = "require_fields";   //需要通知或查询中返回的字段，可以上送需要在后台通知中额外返回的字段
    public static final String REQUIRE_FIELD = "require_field";     //额外返回的属性
    /*--- REQUIRE_FIELDS END ---- */

    /*--- ADDI_TRADE_DATA START ---- */
    //{ "method":"CI","value":{"dynamic_token_out_biz_no":"5453113"}}
    public static final String ADDI_TRADE_DATA = "addi_trade_data"; //附加交易信息
    public static final String METHOD = "method";       //用法标识
    public static final String VALUE = "value";         //用法取值
    public static final String CARD_NO = "card_no";     //证件号
    public static final String NAME = "name";           //持卡人姓名
    public static final String CARD_TYPE = "card_type"; //证件类型
    /*--- ADDI_TRADE_DATA END ---- */

    /*--- ROYALTY_INFO START ---- */
    //[{"serial_no":"01","amount":"5.00"},{"serial_no":"02","amount":"5.00"}]
    public static final String ROYALTY_INFO = "royalty_info";   //商户分账信息
    public static final String SERIAL_NO = "serial_no";         //分账的序号 商户信息里配置的分账序号
    public static final String AMOUNT = "amount";               //分账金额  商户指定的分账金额
    /*--- ROYALTY_INFO END ---- */

    /*--- TERM_INFO START ---- */
    public static final String TERM_INFO = "term_info"; //终端信息
    //设备类型（终端设备类型，受理方可参考终端注册时的设备类型填写，
    // 取值如下：01：自动柜员机（含ATM和CDM）和多媒体自助终端 02：传统POS 03：mPOS 04：智能POS 05：II型固定电话 06：云闪付终端； 07：保留使用；
    // 08：手机POS；09：刷脸付终端； 10：条码支付受理终端； 11：条码支付辅助受理终端；12：行业终端（公交、地铁用于指定行业的终端）； 13：MIS终端）
    public static final String DEVICE_TYPE = "device_type";
    public static final String SECRET_TEXT = "secret_text";             //密文数据：仅在被扫支付类交易报文中出现：64bit 的密文数据，对终端硬件序列号和加密随机因子加密后的结果。本子域取值为：64bit 密文数据进行base64 编码后的结果
    public static final String APP_VERSION = "app_version";             //终端应用程序的版本号。应用程序变更应保证版本号不重复。当长度不足时，右补空格
    public static final String SERIAL_NUM = "serial_num";               //终端序列号（出现要求：终端类型（device_type）填写为 02、03、04、05、06、08、09或10时，必须填写终端序列号。）
    public static final String ENCRYPT_RAND_NUM = "encrypt_rand_num";   //加密随机因子（仅在被扫支付类交易报文中出现：若付款码为19位数字，则取后6位；若付款码为EMV二维码，则取其tag57的卡号/token号的后6位）
    public static final String NETWORK_LICENSE = "network_license";     //终端入网认证编号（银行卡受理终端产品应用认证编号。该编号由“中国银联标识产品企业资质认证办公室”为通过入网认证的终端进行分配。银联直连终端必填。格式：5位字符，例如P3100）
    /*--- TERM_INFO END ---- */


    public static final String REFUND_MER_TRAN_NO = "refund_mer_tran_no";   //商户退款的交易编号
    public static final String MER_REFUND_TIME = "mer_refund_time";         //商户侧退款时间 格式：hhmmss
    public static final String MER_REFUND_DATE = "mer_refund_date";         //商户侧退款日期 格式：yyyyMMdd
    public static final String SYS_ORDER_NO = "sys_order_no";               //交行内部订单号，交行内部订单号和商户交易编号二选一，若同时上送优先使用系统订单号

}
