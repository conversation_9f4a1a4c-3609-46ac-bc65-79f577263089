package com.wosai.mpay.api.bocom;

/**
 * <AUTHOR>
 * @Description BOCOMConstants
 * @Date 2023/12/7 5:10 PM
 */
public class BOCOMConstants {

    //接口版本号
    public static final String VERSION = "1.0";
    public static final String CHARSET_UTF8 = "UTF-8";
    public static final String FORMAT_JSON = "json";

    /** 默认时间格式 **/
    public static final String DATE_SIMPLE_FORMAT = "yyyyMMdd";
    public static final String DATE_TIME_SIMPLE_FORMAT = "HHmmss";
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_TIME_FORMAT_SIMPLE = "yyyyMMddHHmmss";
    /**  Date默认时区 **/
    public static final String DATE_TIMEZONE    = "GMT+8";

    public static final String TRAN_SCENE = "B2C-API-SCANCODE"; //交易场景
    public static final String TRANSACTION_CURRENCY = "CNY";    //交易币种
    public static final String DEVICE_TYPE_11 = "11";           //条码支付辅助受理终端

    /** require_fields **/
    public static final String BANK_TRAN_NO = "bank_tran_no";               //银行端交易流水号
    public static final String THIRD_PARTY = "third_party";                 //第三方渠道 01: 微信支付 02：支付宝 03：银联扫码 04：交行
    public static final String THIRD_PARTY_TRAN_NO = "third_party_tran_no"; //第三方渠道交易流水号
    public static final String PAYMENT_INFO = "payment_info";               //微信支付宝详细付款信息
    public static final String REFUND_INFO = "refund_info";                 //微信支付宝详细付款信息
    public static final String OPEN_ID = "open_id";                         //付款人信息（微信用户标识/支付宝用户ID）
    public static final String SUB_OPEN_ID = "sub_openid";                  //付款人信息（微信用户子标识/买家支付宝账号）

    /** disable_pay_channels **/
    public static final String CREDIT = "credit";                           //微信：no_credit 信用卡  支付宝：credit_group （信用卡卡通，信用卡快捷,花呗，花呗分期）
    public static final String BALANCE = "balance";                         //支付宝 余额
    public static final String MONEY_FUND = "moneyFund";                    //支付宝 余额宝
    public static final String BANK_PAY = "bankPay";                        //支付宝 网银
    public static final String DEBIT_CARD_EXPRESS = "debitCardExpress";     //支付宝 借记卡快捷
    public static final String CREDIT_CARD_EXPRESS = "creditCardExpress";   //支付宝 信用卡快捷
    public static final String CREDIT_CARD_CARTOON = "creditCardCartoon";   //支付宝 信用卡卡通
    public static final String CREDIT_CARD = "creditCard";                  //支付宝 信用卡
    public static final String CARTOON = "cartoon";                         //支付宝 卡通
    public static final String PCREDIT = "pcredit";                         //支付宝 花呗
    public static final String PCREDIT_INSTALLMENT = "pcreditpayInstallment"; //支付宝 花呗分期
    public static final String COUPON = "coupon";                           //支付宝 红包
    public static final String POINT = "point";                             //支付宝 积分
    public static final String PROMOTION = "promotion";                     //支付宝 优惠（包含实时优惠+商户优惠）
    public static final String VOUCHER = "voucher";                         //支付宝 营销券
    public static final String MDISCOUNT = "mdiscount";                     //支付宝 商户优惠
    public static final String HONEYPAY = "honeyPay";                       //支付宝 亲密付
    public static final String MCARD = "mcard";                             //支付宝 商户预存卡
    public static final String PCARD = "pcard";                             //支付宝 个人预存卡

    /** location **/
    public static final String ONLINE = "ONLINE";   //线上
    public static final String OFFLINE = "OFFLINE"; //线下

    /** biz_state **/
    public static final String BIZ_STATE_SUCCESS = "S"; //成功
    public static final String BIZ_STATE_FAIL = "F";    //失败

    /** response_status **/
    public static final String RESPONSE_STATUS_PROCESS = "P";   //处理中
    public static final String RESPONSE_STATUS_SUCCESS = "S";   //成功
    public static final String RESPONSE_STATUS_FAIL = "F";      //失败

    /** tran_state **/
    public static final String TRAN_STATE_PROCESS = "PROCESS"; //处理中
    public static final String TRAN_STATE_SUCCESS = "SUCCESS"; //成功
    public static final String TRAN_STATE_FAILURE = "FAILURE"; //失败

    /** order_status **/
    public static final String ORDER_STATUS_INITIAL = "INITIAL";    //初始化
    public static final String ORDER_STATUS_PAIED = "PAIED";        //交易成功
    public static final String ORDER_STATUS_WAITPAY = "WAITPAY";    //等待支付
    public static final String ORDER_STATUS_REFUNDED = "REFUNDED";  //部分退款
    public static final String ORDER_STATUS_REFUNDALL = "REFUNDALL";//全部退款
    public static final String ORDER_STATUS_CLOSED = "CLOSED";      //订单关闭

    /** response_code **/
    public static final String RESPONSE_CODE_SUCCESS = "CIPP0004PY0000";    //成功

}
