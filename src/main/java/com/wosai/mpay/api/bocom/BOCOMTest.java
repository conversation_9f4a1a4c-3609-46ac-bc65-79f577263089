package com.wosai.mpay.api.bocom;

import com.google.common.collect.Lists;
import com.wosai.mpay.util.SafeSimpleDateFormat;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Description BOCOMTest
 * @Date 2023/12/7 5:53 PM
 */
public class BOCOMTest {


    public static final String PRIVATE_KEY = "";
    public static final String PUBLIC_KEY = "";
    public static final String APP_ID = "appohbtr202312180001";
    public static final String MER_PTC_ID = "***************";
    public static final String BASE_URL = "https://open.bankcomm.com";
    private static final String PAY_URL = BASE_URL + "/api/pmssMpng/MPNG210002/v1";
    private static final String QUERY_URL = BASE_URL + "/api/pmssMpng/MPNG020702/v1";
    private static final String REFUND_URL = BASE_URL + "/api/pmssMpng/MPNG020701/v2";
    private static final String REFUND_QUERY_URL = BASE_URL + "/api/pmssMpng/MPNG020703/v1";

    private static final SafeSimpleDateFormat dateSimpleFormat = new SafeSimpleDateFormat(BOCOMConstants.DATE_SIMPLE_FORMAT);
    private static final SafeSimpleDateFormat dateTimeSimpleFormat = new SafeSimpleDateFormat(BOCOMConstants.DATE_TIME_SIMPLE_FORMAT);


    private static void payTest() throws Exception {
        BOCOMRequestBuilder requestBuilder = new BOCOMRequestBuilder();
        requestBuilder.bizSet(BOCOMProtocolFields.APP_ID, APP_ID);

        DateFormat df = new SimpleDateFormat(BOCOMConstants.DATE_TIME_FORMAT_SIMPLE);
        df.setTimeZone(TimeZone.getTimeZone(BOCOMConstants.DATE_TIMEZONE));
        requestBuilder.setReqHead(BOCOMBusinessFields.TRANS_TIME, df.format(new Date()));
        requestBuilder.setReqHead(BOCOMBusinessFields.VERSION, BOCOMConstants.VERSION);


        List<Map<String, Object>> requires = new ArrayList<>();
        List<String> requireData = Lists.newArrayList(BOCOMConstants.BANK_TRAN_NO, BOCOMConstants.THIRD_PARTY, BOCOMConstants.THIRD_PARTY_TRAN_NO, BOCOMConstants.PAYMENT_INFO, BOCOMConstants.OPEN_ID, BOCOMConstants.SUB_OPEN_ID);
        requireData.forEach(item -> {
            Map<String, Object> require = new HashMap<>();
            require.put(BOCOMBusinessFields.REQUIRE_FIELD, item);
            requires.add(require);
        });
        requestBuilder.setReqBody(BOCOMBusinessFields.REQUIRE_FIELDS, requires);

        //币种
        requestBuilder.setReqBody(BOCOMBusinessFields.CURRENCY, BOCOMConstants.TRANSACTION_CURRENCY);
        //交易场景
        requestBuilder.setReqBody(BOCOMBusinessFields.TRAN_SCENE, BOCOMConstants.TRAN_SCENE);
        //ip
        requestBuilder.setReqBody(BOCOMBusinessFields.IP, "127.0.0.1");
        //商户编号
        requestBuilder.setReqBody(BOCOMBusinessFields.MER_PTC_ID, MER_PTC_ID);
        //商户侧交易日期
        requestBuilder.setReqBody(BOCOMBusinessFields.MER_TRADE_DATE, dateSimpleFormat.format(new Date()));
        //商户侧交易时间
        requestBuilder.setReqBody(BOCOMBusinessFields.MER_TRADE_TIME, dateTimeSimpleFormat.format(new Date()));
        //订单总金额（元）
        requestBuilder.setReqBody(BOCOMBusinessFields.TOTAL_AMOUNT, "0.01");
        requestBuilder.setReqBody(BOCOMBusinessFields.LOCATION, BOCOMConstants.OFFLINE);


        //条码
        requestBuilder.setReqBody(BOCOMBusinessFields.SCAN_CODE_TEXT, "133960040254917133");
        //商户交易编号
        requestBuilder.setReqBody(BOCOMBusinessFields.PAY_MER_TRAN_NO, "************");

        BOCOMClient bocomClient = new BOCOMClient();
        Map<String, Object> result = bocomClient.call(PAY_URL, requestBuilder.build(), PRIVATE_KEY, PUBLIC_KEY);

        System.out.println(result);
    }

    private static void queryTest() throws Exception {
        BOCOMRequestBuilder requestBuilder = new BOCOMRequestBuilder();
        requestBuilder.bizSet(BOCOMProtocolFields.APP_ID, APP_ID);

        DateFormat df = new SimpleDateFormat(BOCOMConstants.DATE_TIME_FORMAT_SIMPLE);
        df.setTimeZone(TimeZone.getTimeZone(BOCOMConstants.DATE_TIMEZONE));
        requestBuilder.setReqHead(BOCOMBusinessFields.TRANS_TIME, df.format(new Date()));
        requestBuilder.setReqHead(BOCOMBusinessFields.VERSION, BOCOMConstants.VERSION);


        List<Map<String, Object>> requires = new ArrayList<>();
        List<String> requireData = Lists.newArrayList(BOCOMConstants.BANK_TRAN_NO, BOCOMConstants.THIRD_PARTY, BOCOMConstants.THIRD_PARTY_TRAN_NO, BOCOMConstants.PAYMENT_INFO, BOCOMConstants.OPEN_ID, BOCOMConstants.SUB_OPEN_ID);
        requireData.forEach(item -> {
            Map<String, Object> require = new HashMap<>();
            require.put(BOCOMBusinessFields.REQUIRE_FIELD, item);
            requires.add(require);
        });
        requestBuilder.setReqBody(BOCOMBusinessFields.REQUIRE_FIELDS, requires);

        //交易场景
        requestBuilder.setReqBody(BOCOMBusinessFields.TRAN_SCENE, BOCOMConstants.TRAN_SCENE);
        //商户编号
        requestBuilder.setReqBody(BOCOMBusinessFields.MER_PTC_ID, MER_PTC_ID);
        //商户侧交易日期
        requestBuilder.setReqBody(BOCOMBusinessFields.MER_TRADE_DATE, dateSimpleFormat.format(new Date()));
        //商户交易编号
        requestBuilder.setReqBody(BOCOMBusinessFields.PAY_MER_TRAN_NO, "************");

        BOCOMClient bocomClient = new BOCOMClient();
        Map<String, Object> result = bocomClient.call(QUERY_URL, requestBuilder.build(), PRIVATE_KEY, PUBLIC_KEY);

        System.out.println(result);
    }


    private static void refundTest() throws Exception {
        BOCOMRequestBuilder requestBuilder = new BOCOMRequestBuilder();
        requestBuilder.bizSet(BOCOMProtocolFields.APP_ID, APP_ID);

        DateFormat df = new SimpleDateFormat(BOCOMConstants.DATE_TIME_FORMAT_SIMPLE);
        df.setTimeZone(TimeZone.getTimeZone(BOCOMConstants.DATE_TIMEZONE));
        requestBuilder.setReqHead(BOCOMBusinessFields.TRANS_TIME, df.format(new Date()));
        requestBuilder.setReqHead(BOCOMBusinessFields.VERSION, BOCOMConstants.VERSION);


        //币种
        requestBuilder.setReqBody(BOCOMBusinessFields.CURRENCY, BOCOMConstants.TRANSACTION_CURRENCY);
        //交易场景
        requestBuilder.setReqBody(BOCOMBusinessFields.TRAN_SCENE, BOCOMConstants.TRAN_SCENE);
        //原交易商户侧交易日期
        requestBuilder.setReqBody(BOCOMBusinessFields.MER_TRADE_DATE, dateSimpleFormat.format(new Date()));
        //商户交易编号
        requestBuilder.setReqBody(BOCOMBusinessFields.PAY_MER_TRAN_NO, "************");

        //商户退款的交易编号
        requestBuilder.setReqBody(BOCOMBusinessFields.REFUND_MER_TRAN_NO, "************001");
        //商户侧退款日期
        requestBuilder.setReqBody(BOCOMBusinessFields.MER_REFUND_DATE, dateSimpleFormat.format(new Date()));
        //商户侧退款时间
        requestBuilder.setReqBody(BOCOMBusinessFields.MER_REFUND_TIME, dateTimeSimpleFormat.format(new Date()));
        //退款金额，单位元
        requestBuilder.setReqBody(BOCOMBusinessFields.AMOUNT, "0.01");


        //商户编号
        requestBuilder.setReqBody(BOCOMBusinessFields.MER_PTC_ID, MER_PTC_ID);

        BOCOMClient bocomClient = new BOCOMClient();
        Map<String, Object> result = bocomClient.call(REFUND_URL, requestBuilder.build(), PRIVATE_KEY, PUBLIC_KEY);

        System.out.println(result);
    }

    public static void main(String[] args) throws Exception {
        payTest();
        queryTest();
        refundTest();
    }
}
