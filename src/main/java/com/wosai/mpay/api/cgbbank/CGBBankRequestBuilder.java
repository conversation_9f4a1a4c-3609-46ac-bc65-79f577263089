package com.wosai.mpay.api.cgbbank;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @Description CGBBankRequestBuilder
 * @Date 2021/6/2 2:54 PM
 */
public class CGBBankRequestBuilder {

    private static ObjectMapper om = new ObjectMapper();
    private Map<String, Object> request;
    private Map<String, Object> bizHeaderSet;
    private Map<String, Object> bizBodySet;
    private static SafeSimpleDateFormat df = new SafeSimpleDateFormat(CGBBankConstants.DATE_TIME_FORMAT);

    public CGBBankRequestBuilder() {
        request = new TreeMap<>();
        bizHeaderSet = new TreeMap<>();
        bizBodySet = new TreeMap<>();

        bizHeaderSet.put(CGBBankProtocolFields.REQUEST_TIME, df.format(new Date()));
        bizHeaderSet.put(CGBBankProtocolFields.VERSION, "1.0.0");  //统一收单系统交互使用的接口版本号

    }

    public Map<String, Object> build() {
        request.put(CGBBankProtocolFields.HEADER, bizHeaderSet);
        request.put(CGBBankProtocolFields.BODY, bizBodySet);
        return request;
    }

    public void set(String field, Object value) {
        request.put(field,  value);
    }

    public void bizHeaderSet(String field, Object value) {
        bizHeaderSet.put(field,  value);
    }

    public void bizBodySet(String field, Object value) {
        bizBodySet.put(field,  value);
    }

    public Map<String,Object> getRequest(){
        return request;
    }
}
