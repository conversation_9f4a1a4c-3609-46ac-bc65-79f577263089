package com.wosai.mpay.api.cgbbank;

/**
 * <AUTHOR>
 * @Description CGBBankBusinessFields
 * @Date 2021/5/27 4:49 PM
 */
public class CGBBankBusinessFields {

    public static final String MERCHANT_NUM = "merchantNum";    //商户编号
    public static final String REQ_RESERVED = "reqReserved";    //请求方保留域
    public static final String RESERVED = "reserved";           //保留域
    public static final String SERVICE = "service";             //接口类型
    public static final String MCH_ID = "mch_id";               //商户号,平台分配
    public static final String MCH_NAME = "merchant_name";      //商户名称
    public static final String SUB_CHANNEL = "sub_channel";     //二级渠道
    public static final String OUT_TRADE_NO = "out_trade_no";   //商户订单号
    public static final String DEVICE_INFO = "device_info";     //设备号
    public static final String BODY = "body";                   //商品描述
    public static final String GOODS_DETAIL = "goods_detail";   //单品信息,单品优惠活动该字段必传,且必须按照规范上传,JSON格式
    public static final String ATTACH = "attach";               //附加信息
    public static final String TOTAL_FEE = "total_fee";         //总金额,以分为单位,不允许包含任何字、符号
    public static final String FEE_TYPE = "fee_type";           //货币类型,默认人民币:CNY
    public static final String MCH_CREATE_IP = "mch_create_ip"; //终端IP,上传商户真实的发起交易的终端出网IP
    public static final String AUTH_CODE = "auth_code";         //扫码支付授权码，设备读取用户展示的条码或者二维码F信息（通过auth_code判断微信/支付宝/银联）
    public static final String TIME_START = "time_start";       //订单生成时间,格式为yyyyMMddHHmmss。注：订单生成时间与超时时间需要同时传入才会生效。
    public static final String DEVICE_LOCATION = "device_location";     //经纬度:30位存放地理位置信息（前6位为地区代码+24位经纬），地区代码：ANS6，不足后补空格；POS经纬度：ANS24，经纬度字段的长度固定为24位，经度与纬度之间用英文逗号‘，’分隔，不足后补空格
    public static final String TIME_EXPIRE = "time_expire";     //订单超时时间
    public static final String OP_USER_ID = "op_user_id";       //操作员,默认为商户号
    public static final String OP_SHOP_ID = "op_shop_id";       //门店编号
    public static final String OP_DEVICE_ID = "op_device_id";   //设备编号
    public static final String GOODS_TAG = "goods_tag";         //商品标记
    public static final String TERM_TYPE = "term_type";         //终端类型
    public static final String LAUNCH_MODEL = "launch_model";   //交易发起方式 01：现场  02：自主
    public static final String NOTIFY_URL = "notify_url";       //回调通知

    //支付宝参数
    public static final String HB_FQ_NUM = "hb_fq_num";                         //花呗分期数【支付宝】,只支持传"3"|"6"|"12",只适用于支付宝支付
    public static final String HB_FQ_SELLER_PERCENT = "hb_fq_seller_percent";   //承担手续费角色【支付宝】,只支持传"0"|"100",商家承担手续费传"100",用户承担传"0",在有hb_fq_num字段时默认为"0"

    //微信参数
    public static final String SUB_APP_ID = "sub_appid";        //公众账号ID【微信】
    public static final String NEED_RECEIPT = "need_receipt";   //电子发票【微信】,需要和微信公众平台的发票功能联合，传入true时，微信支付成功消息和支付详情页将出现开票入口,[新增need_receipt【适用于微信】]

    public static final String PAY_WAY = "pay_way";             //支付渠道  WXPAY:微信渠道 ALIPAY:支付宝
    public static final String PAY_TYPE = "pay_type";           //支付类型
    public static final String OPEN_ID = "openid";              //用户标识
    public static final String PAYMENT_VALID_TIME = "paymentValidTime"; //支付有效时间(秒为单位）
    public static final String PRODUCT_ID = "product_id";               //支付有效时间(秒为单位）
    public static final String LIMIT_CREDIT_PAY = "limit_credit_pay";   //限定用户使用时能否使用信用卡 值为1即禁用信用卡 值为0或者不传此参数则不禁用
    public static final String APPLETS_TYPE = "applets_type";           //SUBAPP-公众号  APPLETS-小程序
    public static final String AREA_CODE = "area_code";                 //地区码

    public static final String TRANSACTION_ID = "transaction_id";   //平台订单号
    public static final String OUT_REFUND_NO = "out_refund_no";     //商户退款单号.32个字符内、可包含字母,确保在商户系统唯一。同个退款单号多次请求,平台当一个单处理,只会退一次款。如果出现退款不成功，请采用原退款单号重新发起,避免出现重复退款。
    public static final String REFUND_FEE = "refund_fee";           //退款总金额,单位为分,可以做部分退款
    public static final String REFUND_CHANNEL = "refund_channel";   //ORIGINAL-原路退款,默认

    public static final String OUT_CANCEL_NO = "out_cancel_no";     //商户撤销订单号
}
