package com.wosai.mpay.api.cgbbank;

import com.google.common.collect.Lists;
import java.util.List;

/**
 * <AUTHOR>
 * @Description CGBConstants
 * @Date 2021/5/27 4:48 PM
 */
public class CGBBankConstants {

    /** 默认时间格式 **/
    public static final String DATE_TIME_FORMAT = "yyyyMMddHHmmss";
    public static final String ENCODING = "UTF-8";

    public static final String VERSION = "1.0.0";
    public static final String FEE_TYPE = "CNY";
    public static final String OH_YES = "Y";
    public static final String CERTAINLY_NOT = "N";

    //接口编码
    public final static String GET_OPEN_ID = "getOpenid";   //获取用户标识 商户 -> 广发
    public final static String MICRO_PAY = "micropay";      //统一被扫 商户 -> 广发
    public final static String NOTIFY = "notify";           //统一通知 广发 -> 商户
    public final static String REFUND_QUERY = "refundQuery";//退款查询 商户 -> 广发
    public final static String QUERY_ORDER = "queryOrder";  //订单查询 商户 -> 广发
    public final static String CREATE_ORDER = "createOrder";//统一下单 商户 -> 广发
    public final static String REFUND = "refund";           //退款 商户 -> 广发
    public final static String CLOSE_ORDER = "closeOrder";  //关闭订单 商户 -> 广发
    public final static String REVERSE = "reverse";         //撤销订单 商户 -> 广发

    public static final String SERVICE_MICROPAY = "unified.trade.micropay"; //统一被扫
    public static final String SERVICE_JSPAY = "unified.trade.jspay"; //统一下单
    public static final String SERVICE_QUERY = "unified.trade.query"; // 订单查询
    public static final String SERVICE_REFUND = "unified.trade.refund"; // 退款
    public static final String SERVICE_REFUND_QUERY = "unified.trade.refundquery"; //退款查询
    public static final String SERVICE_CLOSE = "unified.trade.close"; //关闭订单
    public static final String SERVICE_REVERSE = "unified.trade.reverse"; //撤销订单

    //支付宝花呗分期期数
    public static final String HB_FQ_NUM_3 = "3";
    public static final String HB_FQ_NUM_6 = "6";
    public static final String HB_FQ_NUM_12 = "12";

    //支付宝承担手续费角色
    public static final String HB_FQ_SELLER_PERCENT_CUSTOMER = "0"; //用户承担
    public static final String HB_FQ_SELLER_PERCENT_MERCHANT = "100"; //商家承担

    //终端类型
    public static final String TERM_TYPE_PC = "PC"; //一体机（复杂的终端）或PC收银台
    public static final String TERM_TYPE_POS = "POS"; //销售受理终端（普通终端）
    public static final String TERM_TYPE_QRCODE = "QRCODE"; //二维码
    public static final String TERM_TYPE_APP = "APP"; //指手机APP,若在POS机安装慧收款APP时做交易时,上送的也是APP

    //交易发起方式
    public static final String LAUNCH_MODEL_SCENE = "01"; //现场
    public static final String LAUNCH_MODEL_SELF = "02"; //自主

    //支付渠道
    public static final String PAY_WAY_WXPAY = "WXPAY"; //微信
    public static final String PAY_WAY_ALIPAY = "ALIPAY"; //支付宝
    public static final String PAY_WAY_UNIONPAY = "UNIONPAY"; //支付宝

    //支付类型
    public static final String PAY_TYPE_QR = "QR";  //二维码下单
    public static final String PAY_TYPE_OF = "OF";  //公众号、小程序、服务窗
    public static final String PAY_TYPE_APP = "APP";//APP支付

    //端程序类型
    public static final String APPLETS_TYPE_SUBAPP = "SUBAPP";  //公众号
    public static final String APPLETS_TYPE_APPLETS = "APPLETS";  //小程序
    public static final String APPLETS_TYPE_ALISUBAPP = "ALISUBAPP";//支付宝服务窗

    //交易类型
    public static final String TRADE_TYPE_WEIXIN_MICROPAY = "pay.weixin.micropay"; //微信付款码支付
    public static final String TRADE_TYPE_ALIPAY_MICROPAY= "pay.alipay.micropay"; //支付宝付款码支付
    public static final String TRADE_TYPE_JDPAY_MICROPAY = "pay.jdpay.micropay"; //京东付款码支付
    public static final String TRADE_TYPE_QQ_MICROPAY = "pay.qq.micropay"; //Q钱包付款码支付
    public static final String TRADE_TYPE_SHIMING_MICROPAY = "pay.shiming.micropay"; //会员卡支付
    public static final String TRADE_TYPE_UNIONPAY_MICROPAY = "pay.unionpay.micropay"; //银联付款码支付
    public static final String TRADE_TYPE_BESTPAY_MICROPAY = "pay.bestpay.micropay"; //翼支付付款码支付

    public static final String TRADE_TYPE_WEIXIN_JSPAY = "pay.weixin.jspay"; //

    //错误代码
    public static final String ERROR_CODE_SUCCESS = "SUCCESS"; //业务执行完成
    public static final String ERROR_CODE_INVALID_PARAMS = "0001"; //非法参数
    public static final String ERROR_CODE_WRONG_TYPE = "0002"; //报文格式错误
    public static final String ERROR_CODE_UNKNOWN_SERVICE = "0003"; //接口未定义
    public static final String ERROR_CODE_UNAVAILABLE_SERVICE = "0004"; //接口不可用
    public static final String ERROR_CODE_UNAVAILABLE_MERCHANT = "0005"; //商户不可用
    public static final String ERROR_CODE_UNAVAILABLE_MERCHANT_APP = "0006"; //商户 APP 不可用
    public static final String ERROR_CODE_DECRYPT_ERROR = "0007"; //报文解密错误, 全报文解密失败
    public static final String ERROR_CODE_SIGN_ERROR = "0008"; //签名错误, 报文签名校验不通过
    public static final String ERROR_CODE_FIELD_DECRYPT_ERROR = "0009"; //字段解密失败 - 敏感字段解密失败
    public static final String ERROR_CODE_PARAMS_LACK = "0010"; //必填参数缺失 - 必填参数缺失
    public static final String ERROR_CODE_OVERLOAD_OF_PLATFORM = "0011"; //当前系统访问量过大，请稍后再试 - 开放平台的访问量超出阀值
    public static final String ERROR_CODE_OVERLOAD_OF_API = "0012"; //该业务访问量过大，请稍后再试 - 当前 API 的访问量超出阀值
    public static final String ERROR_CODE_OVERLOAD_OF_APP = "0013"; //当前应用访问量过大，请稍后再试 - 商户 APP 访问系统超出阀值
    public static final String ERROR_CODE_OVERLOAD_OF_APP_API = "0014"; //当前业务访问量过大，请稍后再试 - 商户 APP 访问某个 API 超出阀值
    public static final String ERROR_CODE_UNKNOWN_ERROR = "9000"; //未知系统异常
    public static final String ERROR_CODE_COMMUNICATE_ERROR = "GWLN2000"; //主机通信异常 - 开放平台访问后端系统出现网络异常

    public static final String ERROR_CODE_PARAM_ERROR = "PARAM_ERROR"; //参数错误 缺少关键参数或参数上送错误
    public static final String ERROR_CODE_PAY_BARCODE_EXPIRED = "40004"; //条码过期或已支付 支付失败，获取顾客账户信息失败，请顾客刷新付款码后重新收款，如再次收款失败，请联系管理员处理。[SOUNDWAVE_PARSER_FAIL]
    public static final String ERROR_CODE_ORDER_AMT_LIMIT_ERROR = "ORDER_AMT_LIMIT_ERROR"; //金额超限 PM4001:当前交易金额超出商户单笔交易最大金额，拒绝交易
    public static final String ERROR_CODE_SYS_ERROR = "SYS_ERROR"; //参数系统异常 （device_location 不足30位、mch_id不正确、退款时total_fee 和 refund_fee 出现小数点）
    public static final String ERROR_CODE_ORDER_REPEAT_ERROR = "ORDER_REPEAT_ERROR"; //商户订单号、退款单号等重复使用
    public static final String ERROR_CODE_ORDER_NO_FOUND_ERROR = "ORDER_NO_FOUND_ERROR"; //查询、退款、退款查询时未查询到订单
    public static final String ERROR_CODE_REFUNDAMT_ILLEGAL_ERROR = "REFUNDAMT_ILLEGAL_ERROR"; //累计退款金额超额
    public static final String ERROR_CODE_REFUNDAMT_GT_SETTLEAMT_ERROR = "REFUNDAMT_GT_SETTLEAMT_ERROR"; //退款金额大于待结算金额
    public static final String ERROR_CODE_MCHNT_TRANS_IS_FORBID = "MCHNT_TRANS_IS_FORBID"; //PM2002:商户业务状态异常，请商户联系银行客户经理确认合同是否归档
    public static final String ERROR_CODE_MCHNT_NOT_FOUND = "MCHNT_NOT_FOUND"; //PM1001:商户未完成审批，暂不支持交易，请商户联系银行客户经理确认商户审批状态
    public static final String ERROR_CODE_MCHNT_TERM_INVALID = "MCHNT_TERM_INVALID"; //商户终端设备状态异常或未绑定
    public static final String ERROR_CODE_MCHNT_TERM_OVERSTEP_BOUNDARY = "MCHNT_TERM_OVERSTEP_BOUNDARY"; //商户终端设备越界交易


    public static final String RESULT_CODE_SUCCESS = "0"; //业务结果,0表示成功，非0表示失败
    public static final String PAY_RESULT_SUCCESS = "0"; //支付结果,0表示成功，非0表示失败

    //交易状态
    public static final String TRADE_STATE_SUCCESS = "SUCCESS"; //支付成功
    public static final String TRADE_STATE_USERPAYING = "USERPAYING"; //支付中
    public static final String TRADE_STATE_PAYERROR = "PAYERROR"; //支付失败
    public static final String TRADE_STATE_REFUND = "REFUND"; //转入退款
    public static final String TRADE_STATE_NOTPAY = "NOTPAY"; //未支付
    public static final String TRADE_STATE_CLOSED = "CLOSED"; //已关闭
    public static final String TRADE_STATE_REVSAL = "REVSAL"; //已冲正
    public static final String TRADE_STATE_CANCEL = "CANCEL"; //已撤销

    //退款状态
    public static final String REFUND_STATUS_SUCCESS = "SUCCESS"; //退款成功
    public static final String REFUND_STATUS_FAIL = "FAIL"; //退款失败
    public static final String REFUND_STATUS_PROCESSING = "PROCESSING"; //退款处理中
    public static final String REFUND_STATUS_CHANGE = "CHANGE"; //转入代发，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，资金回流到商户的现金帐号，需要商户人工干预，通过线下或者平台转账的方式进行退款

    public static final List<String> HB_FQ_NUM_LIST = Lists.newArrayList(HB_FQ_NUM_3, HB_FQ_NUM_6, HB_FQ_NUM_12);
    public static final List<String> HB_FQ_SELLER_PERCENT_LIST = Lists.newArrayList(HB_FQ_SELLER_PERCENT_CUSTOMER, HB_FQ_SELLER_PERCENT_MERCHANT);

    public static final List<String> PROTOCOL_ERROR_CODE_LIST = Lists.newArrayList(ERROR_CODE_INVALID_PARAMS, ERROR_CODE_WRONG_TYPE,
            ERROR_CODE_UNKNOWN_SERVICE, ERROR_CODE_UNAVAILABLE_SERVICE, ERROR_CODE_UNAVAILABLE_MERCHANT,
            ERROR_CODE_UNAVAILABLE_MERCHANT_APP, ERROR_CODE_DECRYPT_ERROR, ERROR_CODE_SIGN_ERROR,
            ERROR_CODE_FIELD_DECRYPT_ERROR, ERROR_CODE_PARAMS_LACK, ERROR_CODE_MCHNT_TRANS_IS_FORBID,
            ERROR_CODE_MCHNT_NOT_FOUND,ERROR_CODE_MCHNT_TERM_INVALID);

    public static final List<String> UNKNOWN_ERROR_CODE_LIST = Lists.newArrayList(ERROR_CODE_OVERLOAD_OF_PLATFORM, ERROR_CODE_OVERLOAD_OF_API,
            ERROR_CODE_OVERLOAD_OF_APP, ERROR_CODE_OVERLOAD_OF_APP_API, ERROR_CODE_UNKNOWN_ERROR, ERROR_CODE_COMMUNICATE_ERROR);

    public static final List<String> FAIL_ERROR_CODE_LIST = Lists.newArrayList(ERROR_CODE_PARAM_ERROR, ERROR_CODE_PAY_BARCODE_EXPIRED, ERROR_CODE_ORDER_AMT_LIMIT_ERROR,
            ERROR_CODE_SYS_ERROR, ERROR_CODE_ORDER_REPEAT_ERROR, ERROR_CODE_ORDER_NO_FOUND_ERROR, ERROR_CODE_REFUNDAMT_ILLEGAL_ERROR, ERROR_CODE_REFUNDAMT_GT_SETTLEAMT_ERROR,
            ERROR_CODE_MCHNT_TERM_OVERSTEP_BOUNDARY);
}
