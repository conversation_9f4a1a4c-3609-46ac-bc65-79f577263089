package com.wosai.mpay.api.cgbbank;

import java.io.File;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Description CGBBankTest
 * @Date 2021/6/16 3:17 PM
 */
public class CGBBankTest {

    public static String url = "http://************:30042/gateway/API";

    private static final String privateKey = "";
    private static final String publicKey = "O1xQ//m/n6P9maIX5c3dcRj7XE2sJu3p2QhjXMT0octne6nE5UBsHDR0/FUgwojBdiZmv/pRn+XyLPjEgUyy3A==";
    private static final String gfPublicKey = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAESLqoLJz4CID5V5aqsGXB9k8kSzIMlLgsn7kWBY8+b0o5s/JTABvwFJJcynYPx5F4F2/APT3/B+o81AW6R/MRiA==";
    private static final String appId = "****************0001";
    private static final String instId = "****************";
    private static final String productCode = "e_PayTicket";

    public static void main(String[] args) throws Exception {
        testPay();
//        testPreCreate();
//        testQuery();
//        testRefund();
//        testRefundQuery();
//        testReverse();
    }

    public static void testPay() throws Exception {
        CGBBankClient client = new CGBBankClient();
        CGBBankRequestBuilder builder = new CGBBankRequestBuilder();

        //广发分配的商户号
        builder.bizHeaderSet(CGBBankProtocolFields.INST_ID, instId);
        //广发分配的商户应用编号
        builder.bizHeaderSet(CGBBankProtocolFields.APP_ID, appId);
        //接口的产品归类
        builder.bizHeaderSet(CGBBankProtocolFields.PRODUCT_CODE, productCode);
        //接口编号
        builder.bizHeaderSet(CGBBankProtocolFields.TRADE_CODE, CGBBankConstants.MICRO_PAY);
        //请求流水号
        builder.bizHeaderSet(CGBBankProtocolFields.SENDER_SN, UUID.randomUUID().toString());

        /* 请求报文体 */
        //商户编号
        builder.bizBodySet(CGBBankBusinessFields.MERCHANT_NUM, "GUANGFA-0001");
        //设备号
//        builder.bizBodySet(CGBBankBusinessFields.DEVICE_INFO, "shouqianba00001");
        builder.bizBodySet(CGBBankBusinessFields.DEVICE_INFO, "**************");
        //经纬度
//        builder.bizBodySet(CGBBankBusinessFields.DEVICE_LOCATION, "********.********,121.********");
        builder.bizBodySet(CGBBankBusinessFields.DEVICE_LOCATION, "********.125981,113.244261    ");
        //二级渠道
        builder.bizBodySet(CGBBankBusinessFields.SUB_CHANNEL, "SHOUQIANBA");
        //接口类型
        builder.bizBodySet(CGBBankBusinessFields.SERVICE, CGBBankConstants.SERVICE_MICROPAY);
        //商户号
//        builder.bizBodySet(CGBBankBusinessFields.MCH_ID, "30658109999E00H");
//        builder.bizBodySet(CGBBankBusinessFields.MCH_ID, "30658105814E007");
//        builder.bizBodySet(CGBBankBusinessFields.MCH_ID, "30658805499E001");
        builder.bizBodySet(CGBBankBusinessFields.MCH_ID, "30658105499E002");
        //商户订单号
        builder.bizBodySet(CGBBankBusinessFields.OUT_TRADE_NO, "shouqianba2021072301048");
        //商品描述
        builder.bizBodySet(CGBBankBusinessFields.BODY, "BODY");
        //总金额
        builder.bizBodySet(CGBBankBusinessFields.TOTAL_FEE, 1);
        //终端IP
        builder.bizBodySet(CGBBankBusinessFields.MCH_CREATE_IP, "127.0.0.1");
        //授权码
        builder.bizBodySet(CGBBankBusinessFields.AUTH_CODE, "134521979536579694");
//        builder.bizBodySet(CGBBankBusinessFields.AUTH_CODE, "6220546385381216046");//银联
        //终端类型 TODO
        builder.bizBodySet(CGBBankBusinessFields.TERM_TYPE, "POS");
        //交易发起方式 TODO
        builder.bizBodySet(CGBBankBusinessFields.LAUNCH_MODEL, "01");

//        Map<String, Object> result = client.call(url + File.separator + productCode + File.separator + CGBBankConstants.MICRO_PAY + File.separator + CGBBankConstants.VERSION, builder.build(), appId, privateKey, publicKey, gfPublicKey);
        Map<String, Object> result = client.call("http://127.0.0.1:9966/upay-gateway/upay/support/test", builder.build(), appId, privateKey, publicKey, gfPublicKey);

        System.out.println(result);
    }

    public static void testPreCreate() throws Exception {
        CGBBankClient client = new CGBBankClient();
        CGBBankRequestBuilder builder = new CGBBankRequestBuilder();

        //广发分配的商户号
        builder.bizHeaderSet(CGBBankProtocolFields.INST_ID, instId);
        //广发分配的商户应用编号
        builder.bizHeaderSet(CGBBankProtocolFields.APP_ID, appId);
        //接口的产品归类
        builder.bizHeaderSet(CGBBankProtocolFields.PRODUCT_CODE, productCode);
        //接口编号
        builder.bizHeaderSet(CGBBankProtocolFields.TRADE_CODE, CGBBankConstants.CREATE_ORDER);
        //请求流水号
        builder.bizHeaderSet(CGBBankProtocolFields.SENDER_SN, UUID.randomUUID().toString());

        /* 请求报文体 */
        //商户编号
//        builder.bizBodySet(CGBBankBusinessFields.MERCHANT_NUM, "GUANGFA-0001");
        //接口类型
        builder.bizBodySet(CGBBankBusinessFields.SERVICE, CGBBankConstants.SERVICE_JSPAY);
        //商户号
        builder.bizBodySet(CGBBankBusinessFields.MCH_ID, "30658105499E002");
//        builder.bizBodySet(CGBBankBusinessFields.MCH_ID, "30658109999E00H");
//        builder.bizBodySet(CGBBankBusinessFields.MCH_ID, "30658105814E007");
        //商户订单号
        builder.bizBodySet(CGBBankBusinessFields.OUT_TRADE_NO, "shouqianbapre2021072312");
        //二级渠道
        builder.bizBodySet(CGBBankBusinessFields.SUB_CHANNEL, "SHOUQIANBA");
        //支付渠道
        builder.bizBodySet(CGBBankBusinessFields.PAY_WAY, "WXPAY");
//        builder.bizBodySet(CGBBankBusinessFields.PAY_WAY, "ALIPAY");
//        builder.bizBodySet(CGBBankBusinessFields.PAY_WAY, "UNION");
        //支付类型
        builder.bizBodySet(CGBBankBusinessFields.PAY_TYPE, "QR");
        //用户标识
//        builder.bizBodySet(CGBBankBusinessFields.OPEN_ID, "o0GKn5MoAtL1xzQhU1Jpj0iRrz9M");
//        builder.bizBodySet(CGBBankBusinessFields.OPEN_ID, "****************");
        builder.bizBodySet(CGBBankBusinessFields.SUB_APP_ID, "wx93cb51211802708f");
//        builder.bizBodySet(CGBBankBusinessFields.SUB_APP_ID, "wx2b029c08a6232582");
        builder.bizBodySet(CGBBankBusinessFields.OPEN_ID, "oBmIts7raMlsmURGGQnM2AyMmyLQ");

        //设备号
        builder.bizBodySet(CGBBankBusinessFields.DEVICE_INFO, "**************");
//        builder.bizBodySet(CGBBankBusinessFields.DEVICE_INFO, "*****************");
        //商品描述
        builder.bizBodySet(CGBBankBusinessFields.BODY, "BODY");
        //总金额
        builder.bizBodySet(CGBBankBusinessFields.TOTAL_FEE, 1);
        //终端IP
        builder.bizBodySet(CGBBankBusinessFields.MCH_CREATE_IP, "127.0.0.1");
        //支付有效时间
        builder.bizBodySet(CGBBankBusinessFields.PAYMENT_VALID_TIME, "**************");
        //终端类型 TODO
        builder.bizBodySet(CGBBankBusinessFields.TERM_TYPE, "QRCODE");
        //交易发起方式 TODO
        builder.bizBodySet(CGBBankBusinessFields.LAUNCH_MODEL, "02");
        //门店编号
        builder.bizBodySet(CGBBankBusinessFields.OP_SHOP_ID, "shouqianba-No.1");
        //经纬度
        builder.bizBodySet(CGBBankBusinessFields.DEVICE_LOCATION, "********.********,121.********");
        //
        builder.bizBodySet(CGBBankBusinessFields.APPLETS_TYPE, "SUBAPP");

        Map<String, Object> result = client.call(url + File.separator + productCode + File.separator + CGBBankConstants.CREATE_ORDER + File.separator + CGBBankConstants.VERSION, builder.build(), appId, privateKey, publicKey, gfPublicKey);

        System.out.println(result);
    }

    public static void testQuery() throws Exception {
        CGBBankClient client = new CGBBankClient();
        CGBBankRequestBuilder builder = new CGBBankRequestBuilder();

        //广发分配的商户号
        builder.bizHeaderSet(CGBBankProtocolFields.INST_ID, instId);
        //广发分配的商户应用编号
        builder.bizHeaderSet(CGBBankProtocolFields.APP_ID, appId);
        //接口的产品归类
        builder.bizHeaderSet(CGBBankProtocolFields.PRODUCT_CODE, productCode);
        //接口编号
        builder.bizHeaderSet(CGBBankProtocolFields.TRADE_CODE, CGBBankConstants.QUERY_ORDER);
        //请求流水号
        builder.bizHeaderSet(CGBBankProtocolFields.SENDER_SN, UUID.randomUUID().toString());

        /* 请求报文体 */
        //商户编号
        builder.bizBodySet(CGBBankBusinessFields.MERCHANT_NUM, "GUANGFA-0001");
        //接口类型
        builder.bizBodySet(CGBBankBusinessFields.SERVICE, CGBBankConstants.SERVICE_QUERY);
        //商户号
        builder.bizBodySet(CGBBankBusinessFields.MCH_ID, "30658805499E001");
        //商户订单号
        builder.bizBodySet(CGBBankBusinessFields.OUT_TRADE_NO, "shouqianba2021072301044");

        Map<String, Object> result = client.call(url + File.separator + productCode + File.separator + CGBBankConstants.QUERY_ORDER + File.separator + CGBBankConstants.VERSION, builder.build(), appId, privateKey, publicKey, gfPublicKey);

        System.out.println(result);
    }

    public static void testRefund() throws Exception {
        CGBBankClient client = new CGBBankClient();
        CGBBankRequestBuilder builder = new CGBBankRequestBuilder();

        //广发分配的商户号
        builder.bizHeaderSet(CGBBankProtocolFields.INST_ID, instId);
        //广发分配的商户应用编号
        builder.bizHeaderSet(CGBBankProtocolFields.APP_ID, appId);
        //接口的产品归类
        builder.bizHeaderSet(CGBBankProtocolFields.PRODUCT_CODE, productCode);
        //接口编号
        builder.bizHeaderSet(CGBBankProtocolFields.TRADE_CODE, CGBBankConstants.REFUND);
        //请求流水号
        builder.bizHeaderSet(CGBBankProtocolFields.SENDER_SN, UUID.randomUUID().toString());

        /* 请求报文体 */
        //商户编号
        builder.bizBodySet(CGBBankBusinessFields.MERCHANT_NUM, "GUANGFA-0001");
        //接口类型
        builder.bizBodySet(CGBBankBusinessFields.SERVICE, CGBBankConstants.SERVICE_REFUND);
        //商户号
        builder.bizBodySet(CGBBankBusinessFields.MCH_ID, "30658805499E001");
        //平台订单号
        builder.bizBodySet(CGBBankBusinessFields.TRANSACTION_ID, "202107231648360000000440003017");
        //商户退款单号
        builder.bizBodySet(CGBBankBusinessFields.OUT_REFUND_NO, "shouqianba-refund-************");
        //商户退款单号
        builder.bizBodySet(CGBBankBusinessFields.TOTAL_FEE, 1);
        //商户退款单号
        builder.bizBodySet(CGBBankBusinessFields.REFUND_FEE, 1);
        //操作员
        builder.bizBodySet(CGBBankBusinessFields.OP_USER_ID, "GUANGFA-0001");

        Map<String, Object> result = client.call(url + File.separator + productCode + File.separator + CGBBankConstants.REFUND + File.separator + CGBBankConstants.VERSION, builder.build(), appId, privateKey, publicKey, gfPublicKey);

        System.out.println(result);
    }

    public static void testRefundQuery() throws Exception {
        CGBBankClient client = new CGBBankClient();
        CGBBankRequestBuilder builder = new CGBBankRequestBuilder();

        //广发分配的商户号
        builder.bizHeaderSet(CGBBankProtocolFields.INST_ID, instId);
        //广发分配的商户应用编号
        builder.bizHeaderSet(CGBBankProtocolFields.APP_ID, appId);
        //接口的产品归类
        builder.bizHeaderSet(CGBBankProtocolFields.PRODUCT_CODE, productCode);
        //接口编号
        builder.bizHeaderSet(CGBBankProtocolFields.TRADE_CODE, CGBBankConstants.REFUND_QUERY);
        //请求流水号
        builder.bizHeaderSet(CGBBankProtocolFields.SENDER_SN, UUID.randomUUID().toString());

        /* 请求报文体 */
        //商户编号
//        builder.bizBodySet(CGBBankBusinessFields.MERCHANT_NUM, "GUANGFA-0001");
        //接口类型
        builder.bizBodySet(CGBBankBusinessFields.SERVICE, CGBBankConstants.SERVICE_REFUND_QUERY);
        //商户号
        builder.bizBodySet(CGBBankBusinessFields.MCH_ID, "30658805499E001");
        //商户订单号
        builder.bizBodySet(CGBBankBusinessFields.OUT_TRADE_NO, "shouqianba2021072301044");
        //商户退款单号
        builder.bizBodySet(CGBBankBusinessFields.OUT_REFUND_NO, "shouqianba-refund-************");

        Map<String, Object> result = client.call(url + File.separator + productCode + File.separator + CGBBankConstants.REFUND_QUERY + File.separator + CGBBankConstants.VERSION, builder.build(), appId, privateKey, publicKey, gfPublicKey);

        System.out.println(result);
    }

    public static void testReverse() throws Exception {
        CGBBankClient client = new CGBBankClient();
        CGBBankRequestBuilder builder = new CGBBankRequestBuilder();

        //广发分配的商户号
        builder.bizHeaderSet(CGBBankProtocolFields.INST_ID, instId);
        //广发分配的商户应用编号
        builder.bizHeaderSet(CGBBankProtocolFields.APP_ID, appId);
        //接口的产品归类
        builder.bizHeaderSet(CGBBankProtocolFields.PRODUCT_CODE, productCode);
        //接口编号
        builder.bizHeaderSet(CGBBankProtocolFields.TRADE_CODE, CGBBankConstants.REVERSE);
        //请求流水号
        builder.bizHeaderSet(CGBBankProtocolFields.SENDER_SN, UUID.randomUUID().toString());

        /* 请求报文体 */
        //商户编号
        builder.bizBodySet(CGBBankBusinessFields.MERCHANT_NUM, "GUANGFA-0001");
        //接口类型
        builder.bizBodySet(CGBBankBusinessFields.SERVICE, CGBBankConstants.SERVICE_REVERSE);
        //商户号
        builder.bizBodySet(CGBBankBusinessFields.MCH_ID, "30658805499E001");
        //商户订单号
        builder.bizBodySet(CGBBankBusinessFields.OUT_TRADE_NO, "shouqianba2021072301045");
        //商户退款单号
        builder.bizBodySet(CGBBankBusinessFields.OUT_CANCEL_NO, "shouqianba-cancel-***********");

        Map<String, Object> result = client.call(url + File.separator + productCode + File.separator + CGBBankConstants.REVERSE + File.separator + CGBBankConstants.VERSION, builder.build(), appId, privateKey, publicKey, gfPublicKey);

        System.out.println(result);
    }
}
