package com.wosai.mpay.api.cgbbank;

/**
 * <AUTHOR>
 * @Description CGBBankProtocolFields
 * @Date 2021/5/27 4:50 PM
 */
public class CGBBankProtocolFields {

    public static final String CERT_ID = "certId";              //公钥证书ID
    public static final String SIGNATURE = "signature";         //请求报文签名值
    public static final String SIGN_TYPE = "signType";          //签名方法(SM2)
    public static final String CONTENT_LENGTH = "Content-Length";    //报文长度
    public static final String CONTENT_TYPE = "Content-Type";    //报文类型,application/xml;charset=UTF-8表示为XML格式报文,报文编码为UTF-8  application/json;charset=UTF-8表示为JSON格式报文,报文编码为UTF-8
    public static final String ENCRYPT_TYPE = "encryptType";    //加密算法,全报文加密时必填，支持国密SM4非对称加密算法
    public static final String ENCRYPT_KEY = "encryptKey";      //加密密钥,全报文加密时必填，加密密钥动态生成,使用对方公用加密后填充到此域

    public static final String APP_ID = "appId";                //在开放平台注册时分配的应用编号
    public static final String INST_ID = "instId";              //在开放平台注册时分配的商户号
    public static final String PRODUCT_CODE = "productCode";    //产品编号,接口的产品归类
    public static final String TRADE_CODE = "tradeCode";        //接口编号,接口定义中的报文编号,明确接口功能,关联接口文档
    public static final String VERSION = "version";             //接口文档版本

    public static final String REQUEST_TIME = "requestTime";    //报文发起时间, 格式：yyyyMMddHHmmss
    public static final String RESPONSE_TIME = "responseTime";    //报文发起时间, 格式：yyyyMMddHHmmss
    public static final String SENDER_SN = "senderSN";          //请求流水号,唯一定位一次报文请求，由发起方生成，应答方原样返回，uuid 生成，全局唯一
    public static final String RESERVE = "reserve";             //保留字段,KV方式: productCode=XXX&tradeCode=XXX&...
    public static final String HEADER = "Header";
    public static final String BODY = "Body";
}
