package com.wosai.mpay.api.cgbbank;

import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Base64;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description CGBBankClient
 * @Date 2021/5/27 4:49 PM
 */
public class CGBBankClient {

    public static final Logger logger = LoggerFactory.getLogger(CGBBankClient.class);
    private static final String CONTENT_TYPE = "application/json";
    public static final String SM2 = "SM2";
    public static final String SM4 = "SM4";

    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }


    public Map<String,Object> call(String serviceUrl, Map<String, Object> request, String appId, String merchantPriKey
            , String merchantPubKey, String guangfaPubKey) throws Exception {

        String requestStr = JsonUtil.objectToJsonString(request);

        //1、签名
        String headerSignature = SM2Util.signature(requestStr, Base64.getDecoder().decode(merchantPriKey),  CGBBankConstants.ENCODING);
        String certId= Digest.md5(Base64.getDecoder().decode(merchantPubKey));

        //2、加密
        //生成报文加密随机key
        String randomKey = SM4Util.getRandomKey();
        //请求报文加密
        String encryptContent = SM4Util.gfEncrypt(requestStr, randomKey);

        //3、使用广发公钥对密码进行加密
        String encryptKeyValue = SM2Util.sm2EncryptString(randomKey, Base64.getDecoder().decode(guangfaPubKey), CGBBankConstants.ENCODING);

        Map<String, String> httpHeaders = buildHttpHeader(certId, appId, headerSignature, encryptKeyValue);
        httpHeaders.put(CGBBankProtocolFields.CONTENT_LENGTH, String.valueOf(encryptContent.length()));
        logger.info("original request {}",  requestStr);
        logger.info("httpHeaders {}, request {}", httpHeaders, encryptContent);
        String responseStr = HttpClientUtils.doPost(CGBBankClient.class.getName(), null, null, serviceUrl
                , CONTENT_TYPE, encryptContent, httpHeaders, CGBBankConstants.ENCODING, connectTimeout, readTimeout);
        logger.info("response {}", responseStr);

        //5、解密
        byte[] decryptBytes=SM4Util.decryptCBC(Base64.getDecoder().decode(responseStr), randomKey.getBytes(), randomKey.getBytes());
        String respStr=new String(decryptBytes, "utf-8");
        logger.info("解密后的response {}", respStr);

        try{
            Map<String,Object> result = JsonUtil.jsonStringToObject(respStr, Map.class);
            return result;
        }catch (Exception e){
            throw new MpayApiUnknownResponse("parse response error: " + e.getMessage(), e);
        }
    }

    private Map<String, String> buildHttpHeader(String certId, String appId, String headerSignature, String encryptKey) {
        return MapUtils.hashMap(CGBBankProtocolFields.CERT_ID, certId.toUpperCase(),
                CGBBankProtocolFields.APP_ID, appId,
                CGBBankProtocolFields.SIGNATURE, headerSignature,
                CGBBankProtocolFields.SIGN_TYPE, SM2,
                CGBBankProtocolFields.CONTENT_TYPE, CONTENT_TYPE,
                CGBBankProtocolFields.ENCRYPT_TYPE, SM4,
                CGBBankProtocolFields.ENCRYPT_KEY, encryptKey);
    }

}
