package com.wosai.mpay.api.cgbbank;

/**
 * <AUTHOR>
 * @Description CGBBankResponseFields
 * @Date 2021/5/27 4:51 PM
 */
public class CGBBankResponseFields {

    public static final String MERCHANT_NUM = "merchantNum";    //商户编号
    public static final String REQ_RESERVED = "reqReserved";    //请求方保留域
    public static final String RESERVED = "reserved";           //保留域
    public static final String RESULT_CODE = "result_code";     //业务结果 0表示成功,非0表示失败
    public static final String MCH_ID = "mch_id";               //商户号,平台分配
    public static final String DEVICE_INFO = "device_info";     //设备号
    public static final String ERROR_CODE = "errorCode";        //错误代码
    public static final String ERROR_MSG = "errorMsg";          //错误代码描述
    public static final String SIGN = "sign";                   //签名,MD5签名结果
    public static final String APP_ID = "appid";                //受理商户appid
    public static final String OPEN_ID = "openid";              //用户在受理商户appid下的唯一标识
    public static final String SUB_OPEN_ID = "sub_openid";      //用户在子商户appid下的唯一标识
    /**
     * 交易类型
     *  pay.weixin.micropay——微信付款码支付
     *  pay.alipay.micropay——支付宝付款码支付
     *  pay.jdpay.micropay——京东付款码支付
     *  pay.qq.micropay——QQ钱包付款码支付
     *  pay.shiming.micropay——会员卡支付
     *  pay.unionpay.micropay——银联付款码支付
     *  pay.bestpay.micropay——翼支付付款码支付
     */
    public static final String TRADE_TYPE = "trade_type";                       //交易类型
    public static final String IS_SUBSCRIBE = "is_subscribe";                   //用户是否关注公众账号, Y-关注, N-未关注, 仅在公众账号类型支付有效
    public static final String PAY_RESULT = "pay_result";                       //支付结果  0—成功  其它—失败
    public static final String PAY_SCENE = "pay_scene";                         //支付场景【微信】, "micropay"-表示反扫  "facepay"-表示刷脸
    public static final String PAY_INFO = "pay_info";                           //支付结果信息,支付成功时为空
    public static final String TRANSACTION_ID = "transaction_id";               //平台订单号(平台交易号)
    public static final String OUT_TRANSACTION_ID = "out_transaction_id";       //第三方订单号
    public static final String SUB_IS_SUBSCRIBE = "sub_is_subscribe";           //用户是否关注子公众账号, Y-关注  N-未关注   仅在公众账号类型支付有效
    public static final String SUB_APP_ID = "sub_appid";                        //公众账号ID【微信】,子商户appid
    public static final String OUT_TRADE_NO = "out_trade_no";                   //商户系统内部的定单号,32个字符内、可包含字母
    public static final String TOTAL_FEE = "total_fee";                         //总金额,以分为单位,不允许包含任何字、符号
    public static final String CASH_FEE = "cash_fee";                           //现金支付金额【微信】,现金支付金额订单现金支付金额
    public static final String INVOICE_AMOUNT = "invoice_amount";               //开票金额【支付宝】,用户在交易中支付的可开发票的金额
    public static final String BUY_LOGON_ID = "buy_logon_id";                   //买家支付宝账号【支付宝】
    public static final String BUY_USER_ID = "buy_user_id";                     //买家支付宝用户ID【支付宝】
    public static final String COUPON_FEE = "coupon_fee";                       //现金券金额
    public static final String PROMOTION_DETAIL = "promotion_detail";           //优惠详情【微信】
    public static final String DISCOUNT_GOODS_DETAIL = "discount_goods_detail"; //优惠详情【支付宝】
    public static final String RECEIPT_AMOUNT = "receipt_amount";               //实收金额【支付宝】 单位为元,两位小数。该金额为本笔交易,商户账户能够实际收到的金额
    public static final String BUY_PAY_AMOUNT = "buy_pay_amount";               //买家实付金额【支付宝】 单位为元,两位小数。该金额代表该笔交易买家实际支付的金额,不包含商户折扣等金额
    public static final String POINT_AMOUNT = "point_amount";                   //积分支付的金额【支付宝】 单位为元,两位小数。该金额代表该笔交易中用户使用积分支付的金额,比如集分宝或者支付宝实时优惠等
    public static final String SERVICE_FEE = "service_fee";                     //手续费,单位分
    public static final String PRFRN_IMMNTY_AMT = "prfrn_immnty_amt";           //优免手续费,单位分
    public static final String CASH_PAY_AMT = "cash_pay_amt";                   //下单金额,单位分
    public static final String COUPON_PAY_AMT = "coupon_pay_amt";               //广发优惠支付金额,单位分
    public static final String ATTACH = "attach";                               //附加信息
    public static final String BANK_TYPE = "bank_type";                         //付款银行,银行类型
    public static final String BANK_BILL_NO = "bank_billno";                    //银行订单号,若为微信支付则为空
    public static final String TIME_END = "time_end";                           //支付完成时间,格式为yyyyMMddHHmmss
    public static final String M_DISCOUNT = "mdiscount";                        //免充值优惠金额
    public static final String CASH_FEE_TYPE = "cash_fee_type";                 //现金支付货币种类,默认人民币:CNY
    public static final String FEE_TYPE = "fee_type";                           //现金支付货币种类,默认人民币:CNY
    public static final String GMT_PAYMENT = "gmt_payment";                     //用户支付时间,格式为yyyyMMddHHmmss
    public static final String SETTLEMENT_TOTAL_FEE = "settlement_total_fee";   //应结订单金额=订单金额-免充值代金券金额
    public static final String FUND_BILL_LIST = "fund_bill_list";               //交易支付使用的资金渠道
    public static final String SETTLE_KEY = "settle_key";                       //清算主键
    public static final String SETTLE_DATE = "settle_date";                     //清算日期
    public static final String TRADE_DATE = "trade_date";                       //交易日期
    public static final String OUT_REFUND_NO = "out_refund_no";                 //商户退款单号
    public static final String REFUND_ID = "refund_id";                         //平台退款单号
    public static final String REFUND_CHANNEL = "refund_channel";               //退款渠道
    public static final String REFUND_FEE = "refund_fee";                       //退款金额,单位为分,可以做部分退款
    public static final String COUPON_REFUND_FEE = "coupon_refund_fee";         //现金券退款金额
    public static final String SETTLEMENT_REFUND_FEE = "settlement_refund_fee"; //应结退款金额
    //统一被扫
    public static final String NEED_QUERY = "need_query";                       //查询判断,判断是否需要调用查询接口,值为Y时需要,值为N时不需要
    //订单查询
    public static final String TRADE_STATE = "trade_state";                     //交易状态
    public static final String TRADE_STATE_DESC = "trade_state_desc";           //交易状态描述
    //撤销订单
    public static final String RETRY_FLAG = "retry_flag";                       //撤销重试标志 N表示不用再重试,Y表示需要重试
    //退款查询
    public static final String REFUND_COUNT = "refund_count";                   //退款笔数
    public static final String REFUND_LISTS = "lists";                     //退款查询列表
    public static final String LIST_OUT_REFUND_ID = "out_refund_id";            //第三方退款单号
    public static final String LIST_OUT_REFUND_NO = "out_refund_no";            //商户退款单号
    public static final String LIST_REFUND_ID = "refund_id";                    //平台退款单号
    public static final String LIST_REFUND_CHANNEL = "refund_channel";          //退款渠道
    public static final String LIST_REFUND_FEE = "refund_fee";                  //退款金额
    public static final String LIST_COUPON_REFUND_FEE = "coupon_refund_fee";    //现金券退款金额
    public static final String LIST_REFUND_TIME = "refund_time";                //退款时间
    public static final String LIST_REFUND_STATUS = "refund_status";            //退款状态
    public static final String LIST_REFUND_STATUS_INFO = "refund_status_info";    //退款状态信息
    public static final String LIST_SETTLEMENT_REFUND_FEE = "settlement_refund_fee";  //应结退款金额
    public static final String LIST_SETTLE_KEY = "settle_key";                  //清算主键
    public static final String LIST_SETTLE_DATE = "settle_date";                //清算日期
    public static final String LIST_TRADE_DATE = "trade_date";                  //交易主键

    /**
     * PAY_INFO 包含的字段
     */
    public static final String PAY_INFO_TRADE_NO = "tradeNo";                   //支付宝返回
    public static final String PAY_INFO_PREPAY_ID = "prepayId";                 //微信返回预支付交易会话标识
    public static final String PAY_INFO_WC_PAY_DATA = "wcPayData";              //微信返回微信APP调用数据
    public static final String PAY_INFO_REDIRECT_URL = "redirectUrl";           //银联返回重定向地址
}
