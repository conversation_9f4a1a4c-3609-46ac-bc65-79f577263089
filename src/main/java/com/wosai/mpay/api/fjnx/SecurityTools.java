package com.wosai.mpay.api.fjnx;

/**
 * <AUTHOR>
 * @Date 2024/6/25、11:46
 **/

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.BCUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import org.bouncycastle.crypto.engines.SM2Engine.Mode;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;

public class SecurityTools {
    private final SM2 sm2;

    public SecurityTools(String certPrivateKey, String certPassword, String publicKey) throws Exception {
        String privateKey = DcepSignature.getSM2HexPrivateKeyV2(certPassword, certPrivateKey);
        this.sm2 = getSM2(privateKey, publicKey);
    }

    public String sign(String data) {
        byte[] sign = this.sm2.sign(data.getBytes());
        return HexUtil.encodeHexStr(sign).toUpperCase();
    }

    public boolean verify(String signature, String sourceData) {
        return this.sm2.verify(sourceData.getBytes(), HexUtil.decodeHex(signature));
    }

    public String digitalEnvelope(String secretKey) {
        return this.sm2.encryptHex(secretKey, KeyType.PublicKey).toUpperCase().substring(2);
    }

    public String decryptDigitalEnvelope(String digitalEnvelope) {
        return this.sm2.decryptStr("04".concat(digitalEnvelope), KeyType.PrivateKey).toUpperCase();
    }

    private static SM2 getSM2(String privateKey, String publicKey) {
        ECPrivateKeyParameters privateKeyParameters = BCUtil.toSm2Params(privateKey);
        String xHex = publicKey.substring(0, 64);
        String yHex = publicKey.substring(64, 128);
        ECPublicKeyParameters publicKeyParameters = BCUtil.toSm2Params(xHex, yHex);
        SM2 sm2 = new SM2(privateKeyParameters, publicKeyParameters);
        sm2.usePlainEncoding();
        sm2.setMode(Mode.C1C2C3);
        return sm2;
    }
}
