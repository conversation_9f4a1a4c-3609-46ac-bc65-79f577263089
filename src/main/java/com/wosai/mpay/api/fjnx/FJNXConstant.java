package com.wosai.mpay.api.fjnx;

/**
 * <AUTHOR>
 * @Date 2024/6/26、10:31
 **/

public class FJNXConstant {

    public static final String NAME = "provider.fjnx";

    //serviceId
    public static final String ALIPAY_PRE = "nepay/front/order/AlPay";

    public static final String WX_PRE = "nepay/front/order/WxPay";


    public static final String QUERY = "nepay/front/order/QueryOrder";


    public static final String REFUND = "nepay/front/order/RefundOrder";


    public static final String FORMAT_YYYYMMDD = "yyyyMMdd";

    public static final String FORMAT_HHMMSS = "HHmmss";

    public static final String SIGN_TYPE= "03";

    public static final String ALIPAY_TRADE_TYPE= "JSPAY";

    public static final String OFFICIAL= "OFFICIAL";

    public static final String OFFLINE= "OFFLINE";

    public static final String YYYYMMDD= "yyyyMMdd";

    public static final String HHMMSS= "HHmmss";

    public static final String VERSION= "V1.0";

    public static final String ACTION_TYPE_QUERY= "QT001";

    public static final String ACTION_TYPE_REFUND= "QT002";
    public static final String RT001= "RT001";

    /* retCode 交易响应码 */
    public static final String RET_CODE_SUCCESS = "0000";       //处理成功
    public static final String RET_CODE_PROCESSING = "2001";    //交易处理中
    public static final String RET_CODE_UNKNOWN = "2002";       //交易结果未知
    public static final String RET_CODE_FJNX_TIMEOUT = "9005";  //内部系统通讯异常(超时)
    public static final String RET_CODE_SYSTEM_ERROR = "9999";  //系统异常

    /* status 业务状态 */
    public static final String CLOSED = "CLOSED";       //已关单
    public static final String FAIL = "FAIL";           //交易失败
    public static final String INIT = "INIT";           //初始化
    public static final String PROCESSING = "PROCESSING";//处理中
    public static final String SUCCESS = "SUCCESS";     //交易成功
    public static final String UNKNOWN = "UNKNOWN";     //交易超时

    /* payScene 支付场景 */
    public static final String WEB = "WEB";         //网上商城
    public static final String H5= "H5";            //页面收银台
    public static final String APPLETS = "APPLETS"; //商家小程序
    public static final String POS = "POS";         //pos终端
    public static final String APP = "APP";         //商户APP
    public static final String OTHER = "OTHER";     //其他场景

    /* tradeType 业务类型 */
    public static final String MICROPAY = "MICROPAY";   //付款码支付
    public static final String FACEPAY= "FACEPAY";      //刷脸支付

    /* payType 支付方式 刷脸支付时必传，付款码根据付款码匹配支付方式 */
    public static final String WX = "WX";   //微信
    public static final String AL= "AL";    //支付宝
    public static final String UN= "UN";    //银联二维码

    /* merchantOrderInfo 商户订单信息描述 */
    public static final String DEFAULT_MERCHANT_ORDER_INFO= "固定商户";    //商户订单信息描述

    /* merchantOrderInfoDtl 商户订单信息详情 */
    public static final String DEFAULT_GOODS_ID= "0001";    //商品的编号
    public static final String DEFAULT_GOODS_NAME= "固定商品";//商品名称
    public static final String DEFAULT_GOODS_NUM= "1";      //商品数量

    /* deviceType 设备类型 */
    public static final String TERM_INFO_TERMINAL_TYPE= "11";   //条码支付辅助受理终端


}
