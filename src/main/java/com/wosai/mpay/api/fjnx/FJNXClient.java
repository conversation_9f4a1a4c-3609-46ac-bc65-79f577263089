package com.wosai.mpay.api.fjnx;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.digest.SM3;
import cn.hutool.crypto.symmetric.SM4;
import com.wosai.mpay.api.pab.PabClient;

import com.wosai.mpay.api.tl.TlConstants;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Date 2024/6/25、17:51
 **/

public class FJNXClient {

    public static final Logger logger = LoggerFactory.getLogger(FJNXClient.class);

     private int connectTimeout = 3000;
     private int readTimeout = 15000;
     private String charset = "UTF-8";

    public FJNXClient() {
    }

    public FJNXClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public FJNXClient(int connectTimeout, int readTimeout, String charset) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
        this.charset = charset;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }


    public Map<String, Object> call(FJNXRequestBuilder requestBuilder, String gateway
            , String mchPrivateKey, String platPublicKey, String password)
            throws MpayException, MpayApiNetworkError {
        Map<String, Object> allMap = new HashMap<>();

        try {

            //请求报文体
            Map<String, Object> body = requestBuilder.getBody();
            String bodyString = JsonUtil.objectToJsonString(body);
            //请求参数body
            String digest = SM3.create().digestHex(bodyString).toUpperCase();
            SecurityTools securityTools = new SecurityTools(mchPrivateKey, password, platPublicKey);
            String signature = securityTools.sign(digest);

            // 32位的随机对称秘钥C（且满足正则表达式^[A-Fa-f0-9]+$） 32 * 8
            String secretKey = UUID.randomUUID().toString().replace("-", "").toUpperCase();

            String encryptedBody = new SM4(HexUtil.decodeHex(secretKey)).encryptHex(bodyString).toUpperCase();

            String digitalEnvelope = securityTools.digitalEnvelope(secretKey);
            Map<String, Object> head = requestBuilder.getHead();


            paramsHead(digitalEnvelope, signature, head);
            allMap.put(FJNXRequestFields.BODY, encryptedBody);
            allMap.put(FJNXRequestFields.HEAD, head);
        } catch (Exception e) {
            throw new MpayException("参数构建异常", e);
        }
        String requestStr = JsonUtil.objectToJsonString(requestBuilder.getBody());

        logger.info("request is {}", requestStr);
        String response = "";
        try {
            response = HttpClientUtils.doPost(FJNXClient.class.getName(), null, null, gateway, "application/json", JsonUtil.objectToJsonString(allMap), TlConstants.CHARSET_UTF8, connectTimeout, readTimeout);

            return startDecrypt(response, mchPrivateKey, platPublicKey, password);
        } catch (Exception e) {
            throw new MpayApiUnknownResponse("parse response error: " + response, e);
        }
    }


    private static void paramsHead(String digitalEnvelope, String signature, Map<String, Object> head) {
        head.put(FJNXRequestFields.DIGITAL_ENVELOPE, digitalEnvelope);
        head.put(FJNXRequestFields.SIGNATURE, signature);

    }


    public Map<String, Object> startDecrypt(String response, String privateKey, String publicKey, String password) throws Exception {

        Map<String, Object> map = JsonUtil.jsonStringToObject(response, Map.class);

        //获取body信息
        String respBodyString = MapUtils.getString(map, FJNXRequestFields.BODY);

        Map<String, Object> respHeadMap = MapUtils.getMap(map, FJNXRequestFields.HEAD);

        if(StringUtils.isEmpty(respBodyString)) {
            return  respHeadMap;
        }

        String digitalEnvelopeString = MapUtils.getString(respHeadMap, FJNXRequestFields.DIGITAL_ENVELOPE);

        SecurityTools securityTools = new SecurityTools(privateKey, password, publicKey);

        String secretKey = securityTools.decryptDigitalEnvelope(digitalEnvelopeString);

        String decryptedBody = new SM4(HexUtil.decodeHex(secretKey)).decryptStr(respBodyString);

        logger.info("response is {}", decryptedBody);

        Map result = JsonUtil.jsonStringToObject(decryptedBody, Map.class);

        return result;
    }



}
