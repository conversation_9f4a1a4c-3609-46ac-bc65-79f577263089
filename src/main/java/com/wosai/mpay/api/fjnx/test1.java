//package com.wosai.mpay.api.fjnx;
//
//
//import cn.hutool.core.util.HexUtil;
//import cn.hutool.crypto.digest.SM3;
//import cn.hutool.crypto.symmetric.SM4;
//import com.wosai.mpay.api.tl.TlConstants;
//import com.wosai.mpay.api.zjtlcb.TLCBConstant;
//import com.wosai.mpay.util.HttpClientUtils;
//import com.wosai.mpay.util.JsonUtil;
//
//import java.util.HashMap;
//import java.util.Map;
//
//public class test1 {
//
//	public static void main(String[] args) throws Exception {
//		test11();
//
//	}
//
//    private static void test11() {
//    	System.out.println("1111111");
//        //  收银台公钥
//        String mchPrivateKey = "MIICKjCCAdCgAwIBAgIGAYx/u3n6MAoGCCqBHM9VAYN1MHUxEzARBgNVBAMTClMwMDAwMDAwMDIx\n" +
//	            "IjAgBgNVBAsTGUZVSklBTiBSVVJBTCBDUkVESVQgVU5JT04xCzAJBgNVBAoTAklUMQ8wDQYDVQQH\n" +
//	            "EwZGVVpIT1UxDzANBgNVBAgTBkZVSklBTjELMAkGA1UEBhMCQ04wHhcNMjMxMjE5MDE0MDQyWhcN\n" +
//	            "MzMxMjE5MDE0MDQyWjB1MRMwEQYDVQQDEwpTMDAwMDAwMDAyMSIwIAYDVQQLExlGVUpJQU4gUlVS\n" +
//	            "QUwgQ1JFRElUIFVOSU9OMQswCQYDVQQKEwJJVDEPMA0GA1UEBxMGRlVaSE9VMQ8wDQYDVQQIEwZG\n" +
//	            "VUpJQU4xCzAJBgNVBAYTAkNOMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE3BtAxxo5HZEcd2wy\n" +
//	            "1RnhobUeRa29JzabKbAnX1aukbMaRC1bzXtUbv7JuroiFVJUS535GUyrcvn6VZOpqmZ47KNMMEow\n" +
//	            "CwYDVR0PBAQDAgbAMAkGA1UdEwQCMAAwEQYJYIZIAYb4QgEBBAQDAgeAMB0GA1UdJQQWMBQGCCsG\n" +
//	            "AQUFBwMCBggrBgEFBQcDBDAKBggqgRzPVQGDdQNIADBFAiBIgdwtxAc3mUZI+PlmylF8NqUAAVPV\n" +
//	            "4gsj9+J/VX4RWQIhAOZJOlUHxgxyb9X6uArwyt5uWjcWMVc+KyfHw3CjuMW2";
//
//        //   发起方私钥
//        String platPublicKey = "MIIClwIBATBHBgoqgRzPVQYBBAIBBgcqgRzPVQFoBDCrgg5ftvRQCFvpdnSjj3XRDTh3w1n4LSVg\r\n" +
//        		"kaa2AEaNg620vKVt01P2WgrAr/+OCEswggJHBgoqgRzPVQYBBAIBBIICNzCCAjMwggHaoAMCAQIC\r\n" +
//        		"BgGO3yTycTAKBggqgRzPVQGDdTB6MRgwFgYDVQQDEw80MTczOTExNTMxMTBaMzMxIjAgBgNVBAsT\r\n" +
//        		"GUZVSklBTiBSVVJBTCBDUkVESVQgVU5JT04xCzAJBgNVBAoTAklUMQ8wDQYDVQQHEwZGVVpIT1Ux\r\n" +
//        		"****************************************************************************\r\n" +
//        		"NTI0WjB6MRgwFgYDVQQDEw80MTczOTExNTMxMTBaMzMxIjAgBgNVBAsTGUZVSklBTiBSVVJBTCBD\r\n" +
//        		"UkVESVQgVU5JT04xCzAJBgNVBAoTAklUMQ8wDQYDVQQHEwZGVVpIT1UxDzANBgNVBAgTBkZVSklB\r\n" +
//        		"TjELMAkGA1UEBhMCQ04wWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAAQoIZzPPWFWg00ORr6fhU2n\r\n" +
//        		"zrs32V4GsCaLXqkCXKOERtQzepYzFHhalipxAniIyG/oIWavw8tXG5tUavp+sgtio0wwSjALBgNV\r\n" +
//        		"HQ8EBAMCBsAwCQYDVR0TBAIwADARBglghkgBhvhCAQEEBAMCB4AwHQYDVR0lBBYwFAYIKwYBBQUH\r\n" +
//        		"AwIGCCsGAQUFBwMEMAoGCCqBHM9VAYN1A0cAMEQCIB1eoJP/1pUVv0aLkSulJgzymFrtyruwAamD\r\n" +
//        		"WgS7TRv5AiBQo+BADuwnzdvjUdjtagtkPt4w/keIMC80HJ8d/1u4+Q==\r\n";
//        //请求地址
//        String url="https://nepayuat.fjnx.com.cn/nepay/front/order/WxPay";
//        //终极请求完全体
//        Map<String, Object> allMap = new HashMap<>();;
//        //请求头部
//        Map<String,String> headMap = new HashMap<>();;
//        //请求报文体
//        Map<String,String> body = paramsBody();
//        //请求报文体转换
//        String bodyString = "";
//        bodyString = "{\"tradeTime\":\"200000\",\"orderNo\":\"0013320230824152117088004\",\"tradeNo\":\"202010200007\",\"application\":\"S00002802\",\"mchtNo\":\"4175912AAAAAAAA\",\"tradeDate\":\"20220831\",\"version\":\"V1.0\",\"terminalIp\":\"127.0.0.1\"}";
//        System.out.println("body: " + bodyString);
//        try {
//
//            //请求参数body
//            String digest = SM3.create().digestHex(bodyString).toUpperCase();
//            SecurityTools securityTools = new SecurityTools(platPublicKey, "123456", mchPrivateKey);
//            String signature = securityTools.sign(digest);
//            System.out.println("请求签名：" + signature);
//
//            // 32位的随机对称秘钥C（且满足正则表达式^[A-Fa-f0-9]+$） 32 * 8
////            String secretKey = UUID.randomUUID().toString().replace("-", "").toUpperCase();
//            String secretKey = "7F9D0FDC1CD44BD2B6043CC686514037";
//            System.out.println("请求秘钥：" + secretKey);
//
//            String encryptedBody = new SM4(HexUtil.decodeHex(secretKey)).encryptHex(bodyString).toUpperCase();
//            System.out.println("请求密文：" + encryptedBody);
//
//            String digitalEnvelope = securityTools.digitalEnvelope(secretKey);
//            System.out.println("请求信封：" + digitalEnvelope);
//            //获取头部
//            headMap = paramsHead(digitalEnvelope,signature);
//            allMap.put("body",encryptedBody);
//            allMap.put("head",headMap);
//
//            // HTTP工具调用...
////            String response = HttpUtlis.sendHttpRequest(JSONObject.toJSONString(allMap),
//////                    "https://nepayuat.fjnx.com.cn",
////                    "https://enk3s77jj2z7r.x.pipedream.net",
////                    "/nepay/front/order/ScanPay",
////                    "100000",
////                    "100000");
////            String response = HttpClientUtils.doPost(ClientTest.class.getName(), null, null, "https://enk3s77jj2z7r.x.pipedream.net/sdk", "application/json", JsonUtil.toJsonStr(allMap), TlConstants.CHARSET_UTF8, 100000, 100000);
//        String response = HttpClientUtils.doPost(ClientTest.class.getName(), null, null, "https://nepayuat.fjnx.com.cn/nepay/front/order/ScanPay", "application/json", JsonUtil.toJsonStr(allMap), TlConstants.CHARSET_UTF8, 100000, 100000);
//
//            System.out.println(response+"!!!!!");
//
//            //开始解密
//            startDecrypt(response);
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            System.out.println(e.getMessage());
//        }
//    }
//    /**
//     * 请求参数---body
//     * @return
//     */
//    private static Map<String,String> paramsBody(){
//        Map<String,String> body = new HashMap<>();
//        body.put("tradeNo","202010200007");
//        body.put("tradeDate","20220831");
//        body.put("tradeTime","200000");
//        body.put("mchtNo","4175912AAAAAAAA");
//        body.put("application","S00002802");
//        body.put("version","V1.0");
//        body.put("terminalIp","127.0.0.1");
//        body.put("orderNo","0013320230824152117088004");
//        return body;
//    }
//
//    /**
//     * 请求参数---head
//     * @return
//     */
//    private static Map<String,String> paramsHead(String digitalEnvelope,String signature){
//        Map<String,String> head = new HashMap<>();
//        head.put("merInstId","KARGOOK006003");
//        head.put("sysInstId","S000000002");
//        head.put("digitalEnvelope",digitalEnvelope);
//        head.put("signature",signature);
//        head.put("signType","03");
//        return head;
//    }
//
//    /**
//     * 开始解密
//     * @param response 返回报文
//     * @throws Exception
//     */
//    public static void startDecrypt(String response) throws Exception {
//        System.out.println("------------解密流程开始---------------");
//        Map<String, Object> map = JsonUtil.jsonStringToObject(response, Map.class);
//        //获取head信息
//        String respHeadString = map.get("head").toString();
//        //获取body信息
//        String respBodyString = map.get("body").toString();
//
//        //该对象转换成map集合
//        Map<String, String> respHeadMap = JsonUtil.jsonStringToObject(respHeadString, Map.class);
//        //通过该集合拿到签名 数字信封 等加密信息
//        String digitalEnvelopeString = respHeadMap.get("digitalEnvelope").toString();
//        String signatureString = respHeadMap.get("signature").toString();
//        //调用解密方法
//        decryptRequest(digitalEnvelopeString,signatureString,respBodyString);
//        System.out.println("------------解密流程结束---------------");
//    }
//
//
//    /**
//     * 模拟收银台解密过程
//     * 解密，验签请求数据
//     *
//     * @param digitalEnvelope 数字信封
//     * @param signature       签名
//     */
//    public static void decryptRequest(String digitalEnvelope, String signature, String encryptedBody) throws Exception {
//
//        //  收银台公钥
//        String publicKey = "MIICKjCCAdCgAwIBAgIGAYx/u3n6MAoGCCqBHM9VAYN1MHUxEzARBgNVBAMTClMwMDAwMDAwMDIx\r\n" +
//        		"IjAgBgNVBAsTGUZVSklBTiBSVVJBTCBDUkVESVQgVU5JT04xCzAJBgNVBAoTAklUMQ8wDQYDVQQH\r\n" +
//        		"EwZGVVpIT1UxDzANBgNVBAgTBkZVSklBTjELMAkGA1UEBhMCQ04wHhcNMjMxMjE5MDE0MDQyWhcN\r\n" +
//        		"MzMxMjE5MDE0MDQyWjB1MRMwEQYDVQQDEwpTMDAwMDAwMDAyMSIwIAYDVQQLExlGVUpJQU4gUlVS\r\n" +
//        		"QUwgQ1JFRElUIFVOSU9OMQswCQYDVQQKEwJJVDEPMA0GA1UEBxMGRlVaSE9VMQ8wDQYDVQQIEwZG\r\n" +
//        		"VUpJQU4xCzAJBgNVBAYTAkNOMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE3BtAxxo5HZEcd2wy\r\n" +
//        		"1RnhobUeRa29JzabKbAnX1aukbMaRC1bzXtUbv7JuroiFVJUS535GUyrcvn6VZOpqmZ47KNMMEow\r\n" +
//        		"CwYDVR0PBAQDAgbAMAkGA1UdEwQCMAAwEQYJYIZIAYb4QgEBBAQDAgeAMB0GA1UdJQQWMBQGCCsG\r\n" +
//        		"AQUFBwMCBggrBgEFBQcDBDAKBggqgRzPVQGDdQNIADBFAiBIgdwtxAc3mUZI+PlmylF8NqUAAVPV\r\n" +
//        		"4gsj9+J/VX4RWQIhAOZJOlUHxgxyb9X6uArwyt5uWjcWMVc+KyfHw3CjuMW2";
//
//        //   发起方私钥
//        String privateKey = "MIIClwIBATBHBgoqgRzPVQYBBAIBBgcqgRzPVQFoBDCrgg5ftvRQCFvpdnSjj3XRDTh3w1n4LSVg\r\n" +
//        		"kaa2AEaNg620vKVt01P2WgrAr/+OCEswggJHBgoqgRzPVQYBBAIBBIICNzCCAjMwggHaoAMCAQIC\r\n" +
//        		"BgGO3yTycTAKBggqgRzPVQGDdTB6MRgwFgYDVQQDEw80MTczOTExNTMxMTBaMzMxIjAgBgNVBAsT\r\n" +
//        		"GUZVSklBTiBSVVJBTCBDUkVESVQgVU5JT04xCzAJBgNVBAoTAklUMQ8wDQYDVQQHEwZGVVpIT1Ux\r\n" +
//        		"****************************************************************************\r\n" +
//        		"NTI0WjB6MRgwFgYDVQQDEw80MTczOTExNTMxMTBaMzMxIjAgBgNVBAsTGUZVSklBTiBSVVJBTCBD\r\n" +
//        		"UkVESVQgVU5JT04xCzAJBgNVBAoTAklUMQ8wDQYDVQQHEwZGVVpIT1UxDzANBgNVBAgTBkZVSklB\r\n" +
//        		"TjELMAkGA1UEBhMCQ04wWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAAQoIZzPPWFWg00ORr6fhU2n\r\n" +
//        		"zrs32V4GsCaLXqkCXKOERtQzepYzFHhalipxAniIyG/oIWavw8tXG5tUavp+sgtio0wwSjALBgNV\r\n" +
//        		"HQ8EBAMCBsAwCQYDVR0TBAIwADARBglghkgBhvhCAQEEBAMCB4AwHQYDVR0lBBYwFAYIKwYBBQUH\r\n" +
//        		"AwIGCCsGAQUFBwMEMAoGCCqBHM9VAYN1A0cAMEQCIB1eoJP/1pUVv0aLkSulJgzymFrtyruwAamD\r\n" +
//        		"WgS7TRv5AiBQo+BADuwnzdvjUdjtagtkPt4w/keIMC80HJ8d/1u4+Q==\r\n";
//
//        SecurityTools securityTools = new SecurityTools(privateKey, "123456", publicKey);
//
//        String secretKey = securityTools.decryptDigitalEnvelope(digitalEnvelope);
//        System.out.println("解密秘钥：" + secretKey);
//
//        String decryptedBody = new SM4(HexUtil.decodeHex(secretKey)).decryptStr(encryptedBody);
//        System.out.println("解密明文: " + decryptedBody);
//
//        // 通过明文 获取摘要
//        String digest = SM3.create().digestHex(decryptedBody).toUpperCase();
//        System.out.println("验签：" + securityTools.verify(signature, digest));
//    }
//}
