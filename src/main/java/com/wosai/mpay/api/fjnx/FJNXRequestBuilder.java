package com.wosai.mpay.api.fjnx;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/6/25、18:01
 **/

public class FJNXRequestBuilder {

    private Map<String,Object> body;
    private Map<String,Object> head;

    public FJNXRequestBuilder(){
        body = new HashMap<>();
        head = new HashMap<>();
    }

    public FJNXRequestBuilder(Map<String, Object> head, Map<String, Object> body){
        this.body = body;
        this.head = head;
    }

    public void setBody(String field, Object value) {
        body.put(field,  value);
    }

    public void setBody(Map<String, Object> body) {
        this.body = body;
    }



    public void setHead(String field, Object value) {
        head.put(field,  value);
    }

    public Map<String,Object> getHead(){

        return head;
    }
    public Map<String,Object> getBody(){
        return body;
    }

    public Map<String, Object> buildRequest() {
        Map<String, Object> requestMap = new HashMap<>();

        requestMap.put(FJNXRequestFields.HEAD, head);
        requestMap.put(FJNXRequestFields.BODY, body);

        return requestMap;
    }


}
