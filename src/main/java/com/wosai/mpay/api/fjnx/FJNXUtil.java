package com.wosai.mpay.api.fjnx;


import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/6/17、14:32
 **/

public class FJNXUtil {

    public static String getTime(String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date());
    }

    public static String getTime(String format,long timeStamp) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date(timeStamp));
    }


    //获取时间在xx毫秒之后
    public static String getTimeAfterMS(String format,long ms ) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        String timestamp = sdf.format(new Date(System.currentTimeMillis() + ms));
        return timestamp;
    }




}
