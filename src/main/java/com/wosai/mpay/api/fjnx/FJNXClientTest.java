package com.wosai.mpay.api.fjnx;

import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/8/27、09:54
 **/

public class FJNXClientTest {
    public static void main(String[] args) throws Exception {
//        FJNXClient fjnxClient = new FJNXClient();
//
//        Map<String, Object> body = JsonUtil.jsonStringToObject("{\"payChnl\":\"OFFLINE\",\"tradeNo\":\"7895009722104987\",\"mchtNo\":\"41739115451NNMZ\",\"tradeDate\":\"20240826\",\"terminalId\":\"80057586\",\"version\":\"V1.0\",\"merchantOrderInfoDtl\":[],\"tradeTime\":\"182823\",\"merchantOrderInfo\":\"merchantOrderInfo\",\"isNotifyFlg\":\"N\",\"timeStart\":1724668103732,\"application\":\"41739115451NNMZ\",\"subOpenid\":\"ofDgL0WDbCcjplXn3aZX--gC-cNI\",\"payScene\":\"H5\",\"txnAmt\":\"10.00\",\"tradeType\":\"OFFICIAL\",\"terminalIp\":\"************\",\"subAppid\":\"wxccbcac9a3ece5112\"}", Map.class);
//
//        FJNXRequestBuilder fjnxRequestBuilder = new FJNXRequestBuilder();
//        fjnxRequestBuilder.setBody(body);
//
//
//        fjnxRequestBuilder.setHead(FJNXRequestFields.MER_INSTID, "KARGPAE001516");
//        fjnxRequestBuilder.setHead(FJNXRequestFields.SYS_INSTID, "S000000002");
//
//        fjnxRequestBuilder.setHead(FJNXRequestFields.SIGN_TYPE, FJNXConstant.SIGN_TYPE);
//
//
//        fjnxClient.call(fjnxRequestBuilder, "https://epay.fjnx.com.cn/nepay/front/order/WxPay", "", "", "");

//        payTest();
    }



    private static void payTest() throws Exception {
        FJNXRequestBuilder requestBuilder = new FJNXRequestBuilder();
        //请求头
        requestBuilder.setHead(FJNXRequestFields.MER_INSTID, "KARGOOK006003");
        requestBuilder.setHead(FJNXRequestFields.SYS_INSTID, "S000000002");
        requestBuilder.setHead(FJNXRequestFields.SIGN_TYPE, FJNXConstant.SIGN_TYPE);


        requestBuilder.setBody(FJNXRequestFields.USER_AHR_CD, "284733808565553813");
        requestBuilder.setBody(FJNXRequestFields.TRADE_NO, "fjnx-2024092300002");
        requestBuilder.setBody(FJNXRequestFields.MCHT_NO, "4175912AAAAAAAA");
        requestBuilder.setBody(FJNXRequestFields.TRADE_DATE, "20240923");
        requestBuilder.setBody(FJNXRequestFields.TRADE_TIME, "142235");
        requestBuilder.setBody(FJNXRequestFields.TERMINAL_IP, "127.0.0.1");
        requestBuilder.setBody(FJNXRequestFields.TERMINAL_ID, "21102969");
        requestBuilder.setBody(FJNXRequestFields.VERSION, FJNXConstant.VERSION);
        requestBuilder.setBody(FJNXRequestFields.APPLICATION, "S00002802");
        //支付场景
        requestBuilder.setBody(FJNXRequestFields.PAY_SCENE, FJNXConstant.OTHER);
        //交易金额
        String orderAmount = StringUtils.cents2yuan(1);
        requestBuilder.setBody(FJNXRequestFields.TXN_AMT, orderAmount);
        //业务类型
        requestBuilder.setBody(FJNXRequestFields.TRADE_TYPE, FJNXConstant.MICROPAY);
        //支付方式
        requestBuilder.setBody(FJNXRequestFields.PAY_TYPE, FJNXConstant.AL);
//        requestBuilder.setBody(FJNXRequestFields.PAY_TYPE, FJNXConstant.WX);
//        requestBuilder.setBody(FJNXRequestFields.PAY_TYPE, FJNXConstant.UN);
        //商户订单信息描述
        requestBuilder.setBody(FJNXRequestFields.MERCHANT_ORDER_INFO, FJNXConstant.DEFAULT_MERCHANT_ORDER_INFO);
        //商户订单信息详情
        List<Map<String, Object>> merchantOrderInfoDtl = new ArrayList<>();
        merchantOrderInfoDtl.add(MapUtil.hashMap(FJNXRequestFields.GOODS_ID, FJNXConstant.DEFAULT_GOODS_ID, FJNXRequestFields.GOODS_NAME, FJNXConstant.DEFAULT_GOODS_NAME,
                FJNXRequestFields.GOODS_NUM, FJNXConstant.DEFAULT_GOODS_NUM, FJNXRequestFields.GOODS_PRICE, orderAmount));
        requestBuilder.setBody(FJNXRequestFields.MERCHANT_ORDER_INFO_DTL, merchantOrderInfoDtl);
        //附加信息
        requestBuilder.setBody(FJNXRequestFields.ATTACH, "123");
        //终端信息
        List<Map<String, Object>> termInfos = new ArrayList<>();
        termInfos.add(MapUtil.hashMap(FJNXRequestFields.DEVICE_TYPE, FJNXConstant.TERM_INFO_TERMINAL_TYPE,
                FJNXRequestFields.DEVICE_IP, requestBuilder.getBody().get(FJNXRequestFields.TERMINAL_IP)
        ));
        requestBuilder.setBody(FJNXRequestFields.TERM_INFO, termInfos);

        System.out.println(JsonUtil.objectToJsonString(requestBuilder));

        FJNXClient fjnxClient = new FJNXClient();
        Map<String, Object> result = fjnxClient.call(requestBuilder, "https://nepayuat.fjnx.com.cn/nepay/front/order/ScanPay", "MIICmAIBATBHBgoqgRzPVQYBBAIBBgcqgRzPVQFoBDBaXtGP4/CkwKg/0VYezdI+lTnKuK1Q2eN5\n" +
                "5aM5R6JX88+QSPtD8fo2QIkBf8u0gf4wggJIBgoqgRzPVQYBBAIBBIICODCCAjQwggHaoAMCAQIC\n" +
                "BgGR71uCgjAKBggqgRzPVQGDdTB6MRgwFgYDVQQDEw80MTc1OTEyQUFBQUFBQUExIjAgBgNVBAsT\n" +
                "GUZVSklBTiBSVVJBTCBDUkVESVQgVU5JT04xCzAJBgNVBAoTAklUMQ8wDQYDVQQHEwZGVVpIT1Ux\n" +
                "DzANBgNVBAgTBkZVSklBTjELMAkGA1UEBhMCQ04wHhcNMjQwOTE0MDcwNzE3WhcNMzQwOTE0MDcw\n" +
                "NzE3WjB6MRgwFgYDVQQDEw80MTc1OTEyQUFBQUFBQUExIjAgBgNVBAsTGUZVSklBTiBSVVJBTCBD\n" +
                "UkVESVQgVU5JT04xCzAJBgNVBAoTAklUMQ8wDQYDVQQHEwZGVVpIT1UxDzANBgNVBAgTBkZVSklB\n" +
                "TjELMAkGA1UEBhMCQ04wWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAARRABaQrMLrjlkrHSiqkuyT\n" +
                "hrrj5uaNpHywvkA/OBaPM2hHQFHurotq9Tj3xS2ZDEgLVqH9lklOVIFsD0oe2TEdo0wwSjALBgNV\n" +
                "HQ8EBAMCBsAwCQYDVR0TBAIwADARBglghkgBhvhCAQEEBAMCB4AwHQYDVR0lBBYwFAYIKwYBBQUH\n" +
                "AwIGCCsGAQUFBwMEMAoGCCqBHM9VAYN1A0gAMEUCIF7zqQ2HctuNk0oJ1J2a++V5FuMFo8sOVP6G\n" +
                "MtDCC8ChAiEAvm2xRMTFWtDefT36z8erIEFXbDsAZEzPxao/g0zdk50=", "dc1b40c71a391d911c776c32d519e1a1b51e45adbd27369b29b0275f56ae91b31a442d5bcd7b546efec9baba221552544b9df9194cab72f9fa5593a9aa6678ec", "123456");

        System.out.println(JsonUtil.objectToJsonString(result));

    }
}
