package com.wosai.mpay.api.fjnx;

import cfca.sadk.algorithm.sm2.SM2PrivateKey;
import cfca.sadk.util.KeyUtil;
import com.wosai.mpay.exception.MpayClientError;
import com.wosai.mpay.util.Base64;
import com.wosai.mpay.util.Hex;
import com.wosai.mpay.util.SM2Util;
import com.wosai.mpay.util.SM4Util;
import com.wosai.mpay.util.cmbapp.CertUtil;
import kl.ssl.gmvpn.crypto.impl.jcajce.JcaTlsCertificate;
import kl.ssl.gmvpn.crypto.impl.jcajce.JcaTlsCryptoProvider;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.io.IOException;
import java.math.BigInteger;
import java.security.SecureRandom;

/**
 * 数字人民币工具类,福建农信可以沿用
 * Created by w<PERSON><PERSON><PERSON><PERSON> on 2023/7/25.
 */
public class DcepSignature {


    /**
     * 返回SM2密钥
     *
     * @param sm2Pass
     * @param sm2Text
     * @return sm2 秘钥
     * @throws Exception
     */
    public static String getSM2HexPrivateKey(String sm2Pass, String sm2Text) throws Exception {
        SM2PrivateKey privateKeyFromSM2 = KeyUtil.getPrivateKeyFromSM2(sm2Text.getBytes(), sm2Pass);
        return Hex.byteToHex(privateKeyFromSM2.getD_Bytes()).toLowerCase();
    }


    public static String getSM2HexPrivateKeyV2(String sm2Pass, String sm2Text) throws Exception {
        SM2PrivateKey privateKeyFromSM2 = KeyUtil.getPrivateKeyFromSM2(sm2Text.getBytes(), sm2Pass);
        return new BigInteger(privateKeyFromSM2.getD_Bytes()).toString(16);
    }



    /**
     *
     * @param content
     * @param privateKey hex 私钥
     * @return
     * @throws MpayClientError
     */
    public static String getSign(String content, String privateKey) throws Exception {
        byte[] bytes = Hex.hexToByte(privateKey.getBytes());
        byte[] signs = SM2Util.sign(bytes, content.getBytes());
        return Base64.encode(signs);
    }



    public static boolean verifySign(String xml, String publicKey, String sign) throws Exception {
        byte[] bytes = getPublicKey(publicKey);
        return SM2Util.verifySign(bytes, xml.getBytes("utf8"), java.util.Base64.getDecoder().decode(sign));
    }


    public static String sm2Encrypt(String data, String publicKey) throws Exception{
        byte[] bytes = getPublicKey(publicKey);
        byte[] encrypt = SM2Util.encrypt(bytes, data.getBytes());
        return Base64.encode(encrypt);
    }

    public static String sm4Encrypt(String data, byte[] key) throws Exception{
        return Base64.encode(SM4Util.encryptECB(data.getBytes(), key));
    }


    public static byte[] getPublicKey(String publicKey) throws IOException {
        JcaTlsCryptoProvider cryptoProvider = new JcaTlsCryptoProvider();
        cryptoProvider.setProvider(new BouncyCastleProvider());
        JcaTlsCertificate jcaTlsCertificate = CertUtil.loadJcaTlsCertificate(cryptoProvider.create(new SecureRandom()), publicKey);
        byte[] encoded = jcaTlsCertificate.getX509Certificate().getPublicKey().getEncoded();
        return SM2Util.getPublicKey(encoded);
    }


}
