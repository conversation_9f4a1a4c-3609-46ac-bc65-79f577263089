package com.wosai.mpay.api.yeahpay.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayPaywayEnum
 * @description:
 * @create: 2025-06-20 18:32
 **/
public enum YeahPayPaywayEnum {

    /** 微信支付 */
    WXZF("WXZF", "微信支付"),

    /** 支付宝支付 */
    ZFBZF("ZFBZF", "支付宝支付"),

    /** PayNow支付 (新加坡) */
    PAY_NOW_PAY("PayNowPay", "PayNow支付"),

    /** LiquidPay支付 */
    LIQUID_PAY("LiquidPay", "LiquidPay支付"),

    /** Dash支付 */
    DASH_PAY("DashPay", "Dash支付"),

    /** GrabPay支付 */
    GRAB_PAY("GrabPay", "GrabPay支付"),

    /** 香港微信支付 */
    HKWXZF("HKWXZF", "香港微信支付");

    private final String code;
    private final String description;

    /**
     * 构造函数
     *
     * @param code 支付方式代码
     * @param description 支付方式描述
     */
    YeahPayPaywayEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取支付方式代码
     *
     * @return 支付方式代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取支付方式描述
     *
     * @return 支付方式描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据支付方式代码获取枚举实例
     *
     * @param code 支付方式代码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static YeahPayPaywayEnum fromCode(String code) {
        for (YeahPayPaywayEnum payway : YeahPayPaywayEnum.values()) {
            if (payway.getCode().equals(code)) {
                return payway;
            }
        }
        return null;
    }
}

