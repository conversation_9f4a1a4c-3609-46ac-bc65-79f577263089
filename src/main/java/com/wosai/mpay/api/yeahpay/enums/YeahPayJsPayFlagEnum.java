package com.wosai.mpay.api.yeahpay.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayJsPayFlagEnum
 * @description:
 * @create: 2025-06-22 14:49
 **/
public enum YeahPayJsPayFlagEnum {


    /**
     * JSAPI - Public account payment - 公众号支付
     */
    JSAPI("JSAPI", "公众号支付"),

    /**
     * NATIVE - 扫码支付，用户主扫
     */
    NATIVE("NATIVE", "扫码支付-用户主扫"),

    /**
     * NATIVE - 扫码支付，用户被扫
     */
    MICROPAY("MICROPAY", "扫码支付-用户被扫"),

    /**
     * APPLET - 小程序 支付
     */
    APPLET("APPLET", "小程序支付"),

    /**
     * PCPAY - PC网站支付
     */
    PCPAY("PCPAY", "PC网站支付"),

    /**
     * ONLINE - 线上支付
     */
    ONLINE("ONLINE", "线上支付"),

    /**
     * APP - APP Payment - APP 支付
     */
    APP("APP", "APP 支付"),

    /**
     * MWEB -移动端H5支付
     */
    MWEB("MWEB", "移动端H5支付");

    private final String code;
    private final String description;

    YeahPayJsPayFlagEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static YeahPayJsPayFlagEnum fromCode(String code) {
        for (YeahPayJsPayFlagEnum typeEnum : YeahPayJsPayFlagEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null; // Or throw an exception if you prefer
    }

}
