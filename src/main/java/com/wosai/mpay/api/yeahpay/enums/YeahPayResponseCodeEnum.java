package com.wosai.mpay.api.yeahpay.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayResponseCodeEnum
 * @description:
 * @create: 2025-06-20 16:40
 **/
public enum YeahPayResponseCodeEnum {


    SUCCESS("0", "成功"),
    ORDER_NOT_EXIST("10012", "订单不存在"),
    REQUEST_FAILED("999998", "请求失败");

    private final String code;
    private final String msg;

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    YeahPayResponseCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static YeahPayResponseCodeEnum of(String code) {
        for (YeahPayResponseCodeEnum e : YeahPayResponseCodeEnum.values()) {
            if (code.equals(e.code)) {
                return e;
            }
        }
        return null;
    }

    /**
     * 请求是否成功
     *
     * @param code
     * @return
     */
    public static boolean isSuccess(String code) {
        return YeahPayResponseCodeEnum.SUCCESS.getCode().equals(code);
    }
}
