package com.wosai.mpay.api.yeahpay;

import cn.hutool.core.lang.Pair;
import com.wosai.mpay.api.yeahpay.constants.YeahPayProtocolFieldsConstants;
import com.wosai.mpay.api.yeahpay.constants.YeahPayRequestFieldsConstants;
import com.wosai.mpay.api.yeahpay.util.YeahPaySignUtil;
import com.wosai.mpay.api.yeahpay.util.YeahPayUtil;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;

import java.net.URISyntaxException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayRequestBuilder
 * @description:
 * @create: 2025-06-20 10:08
 **/
public class YeahPayRequestBuilder {

    private final Map<String, String> head;
    private final Map<String, Object> body;
    private final String timestamp;
    private final String nonce;
    private final String appId;
    private final String urlPath;

    public YeahPayRequestBuilder(String appId, String urlPath){

        head = new HashMap<>(16);
        body = new HashMap<>(16);

        timestamp = Long.toString(System.currentTimeMillis());
        nonce = YeahPayUtil.generateUUID();
        this.urlPath = urlPath;
        this.appId = appId;

        head.put(YeahPayRequestFieldsConstants.Common.VERSION, YeahPayProtocolFieldsConstants.HEADER_VERSION_VALUE);
        head.put(YeahPayRequestFieldsConstants.Common.TIMESTAMP, timestamp);
        head.put(YeahPayRequestFieldsConstants.Common.ALGORITHM, YeahPayProtocolFieldsConstants.HEADER_ALGORITHM_VALUE);
        head.put(YeahPayRequestFieldsConstants.Common.APP_ID, appId);
        head.put(YeahPayRequestFieldsConstants.Common.NONCE, nonce);

    }

    public void setBodyField(String field, Object value) {
        body.put(field,  value);
    }

    public Pair<String, Map<String, String>> build(String apiKey, String preUrl) throws MpayException, NoSuchAlgorithmException, URISyntaxException {

        String suffixUrl = YeahPayUtil.extractPathFromUrl(preUrl + this.urlPath);

        String bodyJson = JsonUtil.objectToJsonString(body);

        String source = String.join(YeahPayProtocolFieldsConstants.LINE, suffixUrl, this.appId, this.timestamp,
                YeahPayProtocolFieldsConstants.HEADER_VERSION_VALUE, nonce, bodyJson, apiKey);

        String sign = YeahPaySignUtil.generateSign(source);

        head.put(YeahPayRequestFieldsConstants.Common.SIGNATURE, sign);

        return new Pair<>(bodyJson, head);
    }

    public Map<String, Object> getBody(){
        return this.body;
    }

    public String getUrlPath() {
        return this.urlPath;
    }

    public String getTimestamp() {
        return this.timestamp;
    }

}
