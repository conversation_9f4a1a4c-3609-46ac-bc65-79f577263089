package com.wosai.mpay.api.yeahpay.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayOrderNotifyTransTypeEnum
 * @description:
 * @create: 2025-07-10 20:12
 **/
public enum YeahPayOrderNotifyTransTypeEnum {

    /** 0: 其他或未指定标记 */
    OTHER(0, "其他"),
    /** 1: C2P (Customer-to-POS)，通常指客户扫码发起的交易 */
    C2P(1, "c2p"),
    /** 2: DCS 预授权 */
    DCS_PRE_AUTH(2, "DCS 预授权"),
    /** 3: DCS 小费 */
    DCS_TIP(3, "DCS 小费");

    /**
     * 该交易类型对应的所有技术编码。
     */
    private final Integer code;
    /**
     * 交易类型的业务描述。
     */
    private final String description;
    /**
     * 枚举的私有构造函数。
     *
     * @param code       对应的技术编码数组
     * @param description 业务描述
     */
    YeahPayOrderNotifyTransTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
    /**
     * 获取交易类型的业务描述。
     *
     * @return 交易类型的描述字符串
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取该交易类型关联的所有技术编码。
     *
     * @return 包含所有技术编码的 int 数组
     */
    public Integer getCode() {
        return code;
    }

    public static YeahPayOrderNotifyTransTypeEnum fromCode(Integer code) {
        for (YeahPayOrderNotifyTransTypeEnum typeEnum : YeahPayOrderNotifyTransTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
