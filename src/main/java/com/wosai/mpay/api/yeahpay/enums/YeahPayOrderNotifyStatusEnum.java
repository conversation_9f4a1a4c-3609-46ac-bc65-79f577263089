package com.wosai.mpay.api.yeahpay.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayOrderNotifyStatusEnum
 * @description:
 * @create: 2025-07-10 20:11
 **/
public enum YeahPayOrderNotifyStatusEnum {

    /** -1: 冲正成功 */
    REVERSAL_SUCCESS("-1", "冲正成功"),
    /** 2: 交易成功 */
    SUCCESS("2", "交易成功"),
    /** 3: 交易失败 */
    FAILED("3", "交易失败"),
    /** 4: 订单关闭 */
    CLOSED("4", "订单关闭"),
    /** 14: 退款成功 */
    REFUND_SUCCESS("14", "退款成功"),
    /** 15: 退款失败 */
    REFUND_FAILED("15", "退款失败"),
    /** 24: 撤销成功 */
    VOID_SUCCESS("24", "撤销成功"),
    /** 25: 撤销失败 */
    VOID_FAILED("25", "撤销失败"),
    /** 200: 预授权完成成功 */
    PRE_AUTH_COMPLETE_SUCCESS("200", "预授权完成成功"),
    /** 201: 预授权完成失败 */
    PRE_AUTH_COMPLETE_FAILED("201", "预授权完成失败"),
    /** 202: 预授权完成撤销成功 */
    PRE_AUTH_VOID_COMPLETE_SUCCESS("202", "预授权完成撤销成功"),
    /** 203: 预授权完成撤销失败 */
    PRE_AUTH_VOID_COMPLETE_FAILED("203", "预授权完成撤销失败");

    /**
     * 该交易类型对应的所有技术编码。
     */
    private final String code;
    /**
     * 交易类型的业务描述。
     */
    private final String description;
    /**
     * 枚举的私有构造函数。
     *
     * @param code       对应的技术编码数组
     * @param description 业务描述
     */
    YeahPayOrderNotifyStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    /**
     * 获取交易类型的业务描述。
     *
     * @return 交易类型的描述字符串
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取该交易类型关联的所有技术编码。
     *
     * @return 包含所有技术编码的 int 数组
     */
    public String getCode() {
        return code;
    }

    public static YeahPayOrderNotifyStatusEnum fromCode(String code) {
        for (YeahPayOrderNotifyStatusEnum typeEnum : YeahPayOrderNotifyStatusEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }


    public static boolean isCompleted(String code) {
        return REVERSAL_SUCCESS.getCode().equals(code)
                || REFUND_SUCCESS.getCode().equals(code)
                || SUCCESS.getCode().equals(code)
                || VOID_SUCCESS.getCode().equals(code);
    }


    public static boolean isPreAuth(String code) {
        return PRE_AUTH_COMPLETE_SUCCESS.getCode().equals(code)
                || PRE_AUTH_COMPLETE_FAILED.getCode().equals(code)
                || PRE_AUTH_VOID_COMPLETE_SUCCESS.getCode().equals(code)
                || PRE_AUTH_VOID_COMPLETE_FAILED.getCode().equals(code);
    }
}
