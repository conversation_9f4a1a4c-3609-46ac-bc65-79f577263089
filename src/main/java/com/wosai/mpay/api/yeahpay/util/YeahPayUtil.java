package com.wosai.mpay.api.yeahpay.util;

import com.wosai.mpay.api.yeahpay.constants.YeahPayResponseFieldsConstants;
import com.wosai.mpay.api.yeahpay.enums.YeahPayResponseCodeEnum;
import com.wosai.mpay.util.StringUtils;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayUtil
 * @description:
 * @create: 2025-06-20 10:07
 **/
public class YeahPayUtil {

    public static String generateUUID() {
        return UUID.randomUUID().toString();
    }


    public static Map<String, Object> getErrorResponse(String msg) {
        Map<String, Object> respMap = new HashMap<>();
        respMap.put(YeahPayResponseFieldsConstants.Common.CODE, YeahPayResponseCodeEnum.REQUEST_FAILED.getCode());
        if (StringUtils.isEmpty(msg)) {
            respMap.put(YeahPayResponseFieldsConstants.Common.MESSAGE, YeahPayResponseCodeEnum.REQUEST_FAILED.getMsg());
        } else {
            respMap.put(YeahPayResponseFieldsConstants.Common.MESSAGE, msg);
        }
        return respMap;
    }

    public static String extractPathFromUrl(String urlString) throws URISyntaxException {
        URI uri = new URI(urlString);
        return concatFromSecond(uri.getPath().split("/"));
    }

    /**
     * Concatenates elements from the second position to the end of the array
     * @param array The input string array
     * @return Concatenated string from index 1 to end
     */
    public static String concatFromSecond(String[] array) {
        if (array == null || array.length <= 2) {
            return ""; // Handle null or arrays with <=1 elements
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 2; i < array.length; i++) {
            sb.append("/").append(array[i]);
        }
        return sb.toString();
    }
}

