package com.wosai.mpay.api.yeahpay.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayTradeTypeEnum
 * @description:
 * @create: 2025-06-20 09:34
 **/
public enum YeahPayTradeTypeEnum {

    /**
     * NATIVE - Use a householder sweep - 使用商户扫码
     */
    NATIVE("NATIVE", "Use a householder sweep"),

    /**
     * JSAPI - Public account payment - 公众号支付
     */
    JSAPI("JSAPI", "Public account payment"),

    /**
     * MICROPAY - User is swept - 用户被扫
     */
    MICROPAY("MICROPAY", "User is swept"),

    /**
     * APP - APP Payment - APP 支付
     */
    APP("APP", "APP Payment"),

    /**
     * SmPgPay - Applet Pay - 小程序支付
     */
    SMPGPAY("SmPgPay", "Applet Pay"),

    /**
     * H5Pay - H5 Pay - H5 支付
     */
    H5PAY("H5Pay", "H5 Pay"),

    /**
     * ONLINE_CARD - Online card payment - 在线银行卡支付 (仅用作结账订单回调的基本数据说明)
     */
    ONLINE_CARD("ONLINE_CARD", "Online card payment");

    private final String code;
    private final String description;

    YeahPayTradeTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static YeahPayTradeTypeEnum fromCode(String code) {
        for (YeahPayTradeTypeEnum typeEnum : YeahPayTradeTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null; // Or throw an exception if you prefer
    }
}

