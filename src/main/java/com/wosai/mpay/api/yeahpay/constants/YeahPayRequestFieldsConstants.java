package com.wosai.mpay.api.yeahpay.constants;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayRequestFieldsConstants
 * @description:
 * @create: 2025-06-19 20:01
 **/
public class YeahPayRequestFieldsConstants {


    /**
     * **********************************************************
     * 公共参数
     * **********************************************************
     */
    public static class Common {
        public static final String VERSION = "version"; // 版本号, 传固定值 1.0
        public static final String TIMESTAMP = "timestamp"; // 时间戳, 示例值 1694082355494
        public static final String ALGORITHM = "algorithm"; // 验签算法, SHA-512
        public static final String SIGNATURE = "signature"; // 签名串
        public static final String APP_ID = "appId"; // 每个接入的代理商都会分配一个 appId
        public static final String NONCE = "nonce"; // 随机字符串
    }

    /**
     * **********************************************************
     * 4.1 主扫(消费者扫商户收款码、线上支付) - UnifiedOrder
     * **********************************************************
     */
    public static class UnifiedOrder {
        public static final String PAY_WAY = "payWay"; // 支付方式，微信:WXZF 支付宝:ZFBZF
        public static final String MERCHANT_ID = "merchantId"; // YeahPay 商户号，由 YeahPay 分配
        public static final String THIRD_ORDER_ID = "thirdOrderId"; // 商户内部订单号, 可以包含字母
        public static final String AMOUNT = "amount"; // 标价金额
        public static final String CURRENCY = "currency"; // 标价币种
        public static final String JSPAY_FLAG = "jspayFlag"; // 支付类型
        public static final String SUB_APP_ID = "subAppid"; // 微信分配的子商户公众账号 ID
        public static final String SUB_OPEN_ID = "subOpenid"; // 用户在子商户 appid 下的唯一标识
        public static final String OPEN_ID = "openid"; // 用户在主商戶appid下的唯一标识
        public static final String CLIENT_IP = "clientIp"; // 商户发起交易的IP地址
        public static final String BODY = "body"; // 描述
        public static final String ATTACH = "attach"; // 附加数据
        public static final String POS_NO = "posNo"; // 终端设备号
        public static final String EXTEND_BUSINESS_PARAMS = "extendBusinessParams"; // 可透传的alipay 参数
        public static final String LIMIT_PAY = "limitPay"; // 禁用信用卡交易
        public static final String ORDER_EXPIRATION = "orderExpiration"; // 单位是秒, 不上送默认10分钟
        public static final String REGION = "region"; // 支付宝PC网站支付,APP支付,WAP支付时必须上送
        public static final String PHONE_TYPE = "phoneType"; // 支付宝APP支付,WAP支付时必须上送  安卓设备:ANDROID;
        public static final String JUMP_URL = "jumpUrl"; // 线上支付成功后跳转 url
        public static final String NOTIFY_URL = "notifyUrl"; // 订单支付之后的回调地址
    }
    /**
     * **********************************************************
     * 4.3 被扫(商户扫消费者付款码) - MicroPay
     * **********************************************************
     */
    public static class MicroPay {
        public static final String MERCHANT_ID = "merchantId"; // YeahPay 商户号，由 YeahPay 分配
        public static final String THIRD_ORDER_ID = "thirdOrderId"; // 商户内部订单号
        public static final String AMOUNT = "amount"; // 标价金额
        public static final String CURRENCY = "currency"; // 标价币种
        public static final String APP_ID = "appid"; // 微信子商户公众账号 ID  (注意这里的key是appid,不是appId)
        public static final String CLIENT_IP = "clientIp"; // 商户发起交易的IP地址
        public static final String BODY = "body"; // 描述
        public static final String ATTACH = "attach"; // 附加数据
        public static final String AUTH_CODE = "authCode"; // 扫码支付授权码
        public static final String POS_NO = "posNo"; // 终端设备号
        public static final String NOTIFY_URL = "notifyUrl"; // 订单支付之后的回调地址
    }
    /**
     * **********************************************************
     * 4.2 支付查询(扫码、刷卡) - QueryOrder
     * **********************************************************
     */
    public static class QueryOrder {
        public static final String MERCHANT_ID = "merchantId"; // YeahPay 商户号，由 YeahPay 分配
        public static final String THIRD_ORDER_ID = "thirdOrderId"; // 商户内部订单号
        public static final String LESHUA_ORDER_ID = "leshuaOrderId"; // leshua订单号
    }
    /**
     * **********************************************************
     * 4.4 订单关闭/撤销 - CloseOrder
     * **********************************************************
     */
    public static class CloseOrder {
        public static final String MERCHANT_ID = "merchantId"; // YeahPay 商户号，由 YeahPay 分配
        public static final String THIRD_ORDER_ID = "thirdOrderId"; // 商户内部订单号
        public static final String LESHUA_ORDER_ID = "leshuaOrderId"; // leshua订单号
    }
    /**
     * **********************************************************
     * 4.5 退款(扫码、刷卡) - Refund
     * **********************************************************
     */
    public static class Refund {
        public static final String MERCHANT_ID = "merchantId"; // YeahPay 商户号，由 YeahPay 分配
        public static final String LESHUA_ORDER_ID = "leshuaOrderId"; // leshua订单号
        public static final String AMOUNT = "amount"; // 标价金额,单位:分
        public static final String THIRD_ORDER_ID = "thirdOrderId"; // 第三方订单号(商户内部订单号)
        public static final String MERCHANT_REFUND_ID = "merchantRefundId"; // 商戶自己的退款编号(第三方退款订单编号)
        public static final String POS_NO = "posNo"; // SN 编号
        public static final String NOTIFY_URL = "notifyUrl"; // 订单支付之后的回调地址
    }
    /**
     * **********************************************************
     * 4.6 退款查询(扫码、刷卡) - QueryRefund
     * **********************************************************
     */
    public static class QueryRefund {
        public static final String MERCHANT_ID = "merchantId"; // YeahPay 商户号，由 YeahPay 分配
        public static final String THIRD_ORDER_ID = "thirdOrderId"; // 商户内部订单号
        public static final String LESHUA_ORDER_ID = "leshuaOrderId"; // leshua订单号
        public static final String MERCHANT_REFUND_ID = "merchantRefundId"; // 商户退款单号
    }
    /**
     * **********************************************************
     * 5.1 线下刷卡订单退款(线下刷卡) - refundCardOffLineOrder
     * **********************************************************
     */
    public static class RefundCardOffLineOrder {
        public static final String MERCHANT_ID = "merchantId"; // YeahPay 商户号,由 YeahPay 分配
        public static final String ORI_THIRD_ORDER_ID = "oriThirdOrderId"; // 原交易业务侧订单号
        public static final String ORDER_ID = "orderId"; // 原交易订单号
        public static final String AMOUNT = "amount"; // 标价金额,单位：分
        public static final String THIRD_ORDER_ID = "thirdOrderId"; // 业务侧退款订单号
        public static final String TERMINAL_ID = "terminalId"; // YeahPay 终端号
        public static final String NOTIFY_URL = "notifyUrl"; // 订单支付之后的回调地址
    }
    /**
     * **********************************************************
     * 5.2 线下刷卡订单撤销(线下刷卡) - voidCardOffLineOrder
     * **********************************************************
     */
    public static class VoidCardOffLineOrder {
        public static final String MERCHANT_ID = "merchantId"; // YeahPay 商户号,由 YeahPay 分配
        public static final String ORI_THIRD_ORDER_ID = "oriThirdOrderId"; // 原交易业务侧订单号
        public static final String ORDER_ID = "orderId"; // 原交易订单号
        public static final String THIRD_ORDER_ID = "thirdOrderId"; // 业务侧退款订单号
        public static final String TERMINAL_ID = "terminalId"; // YeahPay 终端号
        public static final String NOTIFY_URL = "notifyUrl"; // 订单支付之后的回调地址
    }
    /**
     * **********************************************************
     * 5.3 ApplePay,GooglePay 支付 - googleAndApplePay
     * **********************************************************
     */
    public static class GoogleAndApplePay {
        public static final String AMOUNT = "amount"; // 金额:单位:分
        public static final String CURRENCY = "currency"; // 交易币种:SGD
        public static final String MERCHANT_ID = "merchantId"; // 商户编号
        public static final String METHOD_TYPE = "methodType"; // 枚举值
        public static final String THIRD_ORDER_ID = "thirdOrderId"; // 业务订单号
        public static final String NOTIFY_URL = "notifyUrl"; // 订单结果回调地址
        public static final String CLIENT_SECRET = "clientSecret"; // 密文,交易使用加密
    }
    /**
     * **********************************************************
     * 6.1 获取收银台地址 - getRedirectUrl
     * **********************************************************
     */
    public static class GetRedirectUrl {
        public static final String MERCHANT_ID = "merchantId"; // 商户编号
        public static final String ORDER_ID = "orderId"; // 订单号
        public static final String MERCHANT_REFERENCE_NUMBER = "merchantReferenceNumber"; // 商户参考号
        public static final String CURRENCY = "currency"; // 币种
        public static final String AMOUNT = "amount"; // 金额
        public static final String GOODS_LIST = "goodsList"; // 商品信息jsonString 格式

        public static final String GOODS_LIST_NAME = "name"; // 商品信息 商品名称
        public static final String GOODS_LIST_NUMBER = "number"; // 商品信息 商品数量
        public static final String GOODS_LIST_CURRENCY = "currency"; // 商品信息 商品币种
        public static final String GOODS_LIST_AMOUNT = "amount"; // 商品信息 商品金额

        public static final String TIMESTAMP = "timestamp"; // 时间戳
        public static final String CALLBACK = "callback"; // 交易结果页点击成功后回调地址
        public static final String LANG = "lang"; // 语言
        public static final String NOTIFY_URL = "notifyUrl"; // 订单支付之后的回调地址
        public static final String PAY_WAY = "payWay"; // 支付方式
        public static final String LIMIT_PAY = "limitPay"; // 子钱包
        public static final String COUNTDOWN = "countdown"; // YeahPay支付结果页停留时长,默认5秒
        public static final String TARGET = "target"; // 默认显示YeahPay支付结果页,传other 则直接跳转第三方回调页
    }
    /**
     * **********************************************************
     * 6.2 查询支付方式 - getTradeType
     * **********************************************************
     */
    public static class GetTradeType {
        public static final String MERCHANT_ID = "merchantId"; // 商户编号
    }


    public static class OrderNotify {

        /** 订单创建时间，Unix 时间戳（秒） */
        public static final String FIELD_ORDER_CREATE_TIME = "orderCreateTime";
        /** 订单更新时间，Unix 时间戳（秒） */
        public static final String FIELD_ORDER_UPDATE_TIME = "orderUpdateTime";
        /** 交易订单号（支付系统订单号） */
        public static final String FIELD_ORDER_ID = "orderId";
        /** 退款订单号（如为退款订单时存在） */
        public static final String FIELD_REFUND_ORDER_ID = "refundOrderId";
        /** 业务订单号（商户订单号） */
        public static final String FIELD_MERCHANT_ORDER_ID = "merchantOrderId";
        /** 商户编号 */
        public static final String FIELD_MERCHANT_ID = "merchantId";
        /** 直属代理商编号 */
        public static final String FIELD_AGENT_ID = "agentId";
        /** 国家地区代码 */
        public static final String FIELD_COUNTRY = "country";
        /** 交易通道 ID */
        public static final String FIELD_PAY_CATALOG = "payCatalog";
        /** 支付钱包名称 */
        public static final String FIELD_MPP_NAME = "mppName";
        /** 终端号 */
        public static final String FIELD_DEVICE_SN = "deviceSn";
        /** 支付方式 */
        public static final String FIELD_PAY_METHOD = "payMethod";
        /** 金额 */
        public static final String FIELD_AMOUNT = "amount";
        /** 币种 */
        public static final String FIELD_CURRENCY = "currency";
        /** 交易类型 */
        public static final String FIELD_BIZ_TYPE = "bizType";
        /** 交易状态 */
        public static final String FIELD_STATUS = "status";
        /** 订单标记 */
        public static final String FIELD_TRANS_TYPE = "transType";

    }
}
