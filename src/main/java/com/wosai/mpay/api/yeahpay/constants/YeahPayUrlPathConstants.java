package com.wosai.mpay.api.yeahpay.constants;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayUrlPathConstants
 * @description:
 * @create: 2025-06-20 09:37
 **/
public class YeahPayUrlPathConstants {

    /** ********************************************************** 4.1 主扫(消费者扫商户收款码、线上支付) - UnifiedOrder ********************************************************** */
    public static final String UNIFIED_ORDER = "/order/unifiedOrder"; // 统一下单

    /** ********************************************************** 4.2 支付查询(扫码、刷卡) - QueryOrder ********************************************************** */
    public static final String QUERY_ORDER = "/order/queryOrder"; // 订单查询

    /** ********************************************************** 4.3 被扫(商户扫消费者付款码) - MicroPay ********************************************************** */
    public static final String MICRO_PAY = "/order/microPay"; // 刷卡支付

    /** ********************************************************** 4.4 订单关闭/撤销 - CloseOrder ********************************************************** */
    public static final String CLOSE_ORDER = "/order/closeOrder"; // 关闭订单

    /** ********************************************************** 4.5 退款(扫码、刷卡) - Refund ********************************************************** */
    public static final String REFUND = "/cardOrder/refund"; // 订单退款

    /** ********************************************************** 4.6 退款查询(扫码、刷卡) - QueryRefund ********************************************************** */
    public static final String QUERY_REFUND = "/order/queryRefund"; // 退款查询

    /** ********************************************************** 4.7 支付结果通知(回调接口) - NOTIFY ********************************************************** */
    //  public static final String NOTIFY = "/pay/notify"; //支付结果通知 (此路径通常由YeahPay服务器调用，无需开发者主动请求)

    /** ********************************************************** 5.1 线下刷卡订单退款(线下刷卡) - refundCardOffLineOrder ********************************************************** */
    public static final String REFUND_CARD_OFFLINE_ORDER = "/cardOrder/refundCardOffLineOrder"; // 线下刷卡订单退款

    /** ********************************************************** 5.2 线下刷卡订单撤销(线下刷卡) - voidCardOffLineOrder ********************************************************** */
    public static final String VOID_CARD_OFFLINE_ORDER = "/cardOrder/voidCardOffLineOrder"; // 线下刷卡订单撤销

    /** ********************************************************** 5.3 ApplePay,GooglePay 支付 - googleAndApplePay ********************************************************** */
    public static final String GOOGLE_AND_APPLE_PAY = "/cardOrder/googleAndApplePay"; // 谷歌支付&苹果支付

    /** ********************************************************** 6.1 获取收银台地址 - getRedirectUrl ********************************************************** */
    public static final String GET_REDIRECT_URL = "/cardOrder/getRedirectUrl"; // 获取收银台地址

    /** ********************************************************** 6.2 查询支付方式 - getTradeType ********************************************************** */
    public static final String GET_TRADE_TYPE = "/cardOrder/getTradeType"; // 查询支付方式
}

