package com.wosai.mpay.api.yeahpay.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayRefundApplyStatusEnum
 * @description: 1-失败；2-成功；3-退款中
 * @create: 2025-06-23 14:33
 **/
public enum YeahPayRefundApplyStatusEnum {

    /**
     * 3 - Refunds pending - 退款中
     */
    REFUNDS_PENDING("3", "Refunds pending"),
    /**
     * 11 - Successful refund - 退款成功
     */
    SUCCESSFUL_REFUND("2", "Successful refund"),
    /**
     * 1 - Refund failure - 退款失败
     */
    REFUND_FAILURE("1", "Refund failure");

    private final String code;
    private final String description;

    private YeahPayRefundApplyStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static YeahPayRefundApplyStatusEnum fromCode(String code) {

        for (YeahPayRefundApplyStatusEnum statusEnum : YeahPayRefundApplyStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }

        return null;
    }


    /**
     * 退款是否完成
     *
     * @param status
     * @return
     */
    public static boolean isRefundCompleted(String status) {
        return YeahPayRefundApplyStatusEnum.SUCCESSFUL_REFUND.getCode().equals(status)
                || YeahPayRefundApplyStatusEnum.REFUND_FAILURE.getCode().equals(status);
    }


    /**
     * 退款是否成功
     *
     * @param status
     * @return
     */
    public static boolean isRefundSuccessful(String status) {
        return YeahPayRefundApplyStatusEnum.SUCCESSFUL_REFUND.getCode().equals(status);
    }

    /**
     * 退款是否失败
     *
     * @param status
     * @return
     */
    public static boolean isRefundFailure(String status) {
        return YeahPayRefundApplyStatusEnum.REFUND_FAILURE.getCode().equals(status);
    }
}
