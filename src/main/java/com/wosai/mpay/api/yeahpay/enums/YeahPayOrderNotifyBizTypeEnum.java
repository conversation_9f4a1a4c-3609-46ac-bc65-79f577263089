package com.wosai.mpay.api.yeahpay.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayOrderNotifyBizTypeEnum
 * @description:
 * @create: 2025-07-10 20:10
 **/
public enum YeahPayOrderNotifyBizTypeEnum {

    /** 1: 扫码支付 */
    SCAN_CODE(1, "扫码"),
    /** 2, 3: 刷卡支付 (包括刷卡、插卡、挥卡等物理卡交易) */
    CARD_PRESENT2(2, "刷卡"),
    CARD_PRESENT3(3, "刷卡-插卡"),
    /** 4: 预授权 */
    PRE_AUTH(4, "预授权");
    /**
     * 该交易类型对应的所有技术编码。
     */
    private final Integer code;
    /**
     * 交易类型的业务描述。
     */
    private final String description;
    /**
     * 枚举的私有构造函数。
     *
     * @param code       对应的技术编码数组
     * @param description 业务描述
     */
    YeahPayOrderNotifyBizTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
    /**
     * 获取交易类型的业务描述。
     *
     * @return 交易类型的描述字符串
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取该交易类型关联的所有技术编码。
     *
     * @return 包含所有技术编码的 int 数组
     */
    public Integer getCode() {
        return code;
    }

    public static YeahPayOrderNotifyBizTypeEnum fromCode(Integer code) {
        for (YeahPayOrderNotifyBizTypeEnum typeEnum : YeahPayOrderNotifyBizTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

}
