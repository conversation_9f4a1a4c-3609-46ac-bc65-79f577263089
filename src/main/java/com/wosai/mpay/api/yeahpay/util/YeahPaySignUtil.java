package com.wosai.mpay.api.yeahpay.util;

import com.wosai.mpay.api.spdb.security.DigestUtils;
import com.wosai.mpay.api.yeahpay.constants.YeahPayProtocolFieldsConstants;
import com.wosai.mpay.api.zjtlcb.MD5Util;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Base64;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.SecurityUtil;
import com.wosai.mpay.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPaySignUtil
 * @description:
 * @create: 2025-06-20 10:26
 **/
public class YeahPaySignUtil {
    public static final Logger log = LoggerFactory.getLogger(YeahPaySignUtil.class);


    /**
     * 生成签名
     * @param source
     * @return
     * @throws MpayException
     */
    public static String generateSign(String source) {

        String sign = DigestUtils.dataDigest(YeahPayProtocolFieldsConstants.HEADER_ALGORITHM_VALUE, source, YeahPayProtocolFieldsConstants.CHAR_SET_UTF_8);

        return sign;
    }


    public static boolean verifySign(String body, String sign, String publicKey) {
        try {
            String  source = body + YeahPayProtocolFieldsConstants.LINE + publicKey;
            // 获取 SHA - 512 算法的 MessageDigest 实例
            MessageDigest digest = MessageDigest.getInstance(YeahPayProtocolFieldsConstants.HEADER_ALGORITHM_VALUE);
            // 对输入的字符串进行编码并更新到 digest 中
            byte[] encodedHash = digest.digest(source.getBytes(StandardCharsets.UTF_8));
            String localSign = Base64.encode(encodedHash);

            if (localSign.equals(sign)) {
                return true;
            }
            log.info("yeahpay verifySign fail, source: {}, sign: {}, localSign: {}", source, sign, localSign);
        } catch (NoSuchAlgorithmException e) {
            log.error("yeahpay verifySign fail", e);
        }
        return false;
    }
}
