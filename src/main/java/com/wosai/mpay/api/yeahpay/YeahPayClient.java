package com.wosai.mpay.api.yeahpay;

import cn.hutool.core.lang.Pair;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.mpay.api.yeahpay.constants.YeahPayProtocolFieldsConstants;
import com.wosai.mpay.api.yeahpay.util.YeahPayUtil;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayClient
 * @description:
 * @create: 2025-06-19 19:55
 **/
public class YeahPayClient {


    public static final Logger log = LoggerFactory.getLogger(YeahPayClient.class);

    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    /**
     * 请求接口
     * @param requestUrl
     * @param yeahPayRequestBuilder
     * @param apiKey
     * @return
     * @throws MpayException
     * @throws MpayApiNetworkError
     */
    public Map<String, Object> call(String requestUrl, YeahPayRequestBuilder yeahPayRequestBuilder, String apiKey) throws MpayException, MpayApiNetworkError {

        String url = requestUrl + yeahPayRequestBuilder.getUrlPath();

        try {
            Pair<String, Map<String, String>> pair = yeahPayRequestBuilder.build(apiKey, requestUrl);

            log.info("yeahpay request {}. url {} ", pair.getKey(), yeahPayRequestBuilder.getUrlPath());

            String response = HttpClientUtils.doPost(YeahPayClient.class.getName(), null, null,
                    url, YeahPayProtocolFieldsConstants.CONTENT_TYPE_JSON, pair.getKey(), pair.getValue(),
                    YeahPayProtocolFieldsConstants.CHAR_SET_UTF_8, connectTimeout, readTimeout);

            log.info("yeahpay response content {}", response);

            return JsonUtil.jsonStringToObject(response, new TypeReference<HashMap<String, Object>>() {});
        } catch (Exception e) {
            log.error("yeahpay 请求异常: e.Msg={}", e.getMessage(), e);

            return YeahPayUtil.getErrorResponse(null);
        }
    }
}
