package com.wosai.mpay.api.yeahpay.constants;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayProtocolFieldsConstants
 * @description:
 * @create: 2025-06-20 09:13
 **/
public class YeahPayProtocolFieldsConstants {

    public static final String HEADER_ACCEPT_LANGUAGE = "Accept-Language";

    public static final String HEADER_ACCEPT_TIMEZONE = "Accept-Timezone";

    public static final String HEADER_CONTENT_TYPE = "Content-Type";

    public static final String HEADER_SIGNATURE = "signature";

    public static final String ACCEPT_LANGUAGE_ZH_CN = "zh-CN";

    public static final String ACCEPT_LANGUAGE_EN_US = "en-US";

    public static final String ACCEPT_LANGUAGE_ZH_TW = "zh-TW";

    public static final String ACCEPT_TIMEZONE_GMT_8 = "GMT+8";

    public static final String COUNTRY_SINGAPORE = "SGP";

    public static final String CURRENCY_SGD = "SGD";

    public static final String CURRENCY_HKD = "HKD";

    public static final String CONTENT_TYPE_JSON = "application/json";

    public static final String HEADER_VERSION_VALUE = "1.0";

    public static final String HEADER_ALGORITHM_VALUE = "SHA-512";

    public static final String AES_ALGORITHM = "AES";

    public static final String CHAR_SET_UTF_8 = "utf-8";

    public static final String DATE_TIME_FOTMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String REGION = "SG";

    public static final String PHONE_TYPE_ANDROID = "ANDROID";

    public static final String PHONE_TYPE_IOS = "IOS";

    public static final String NOTIFY_SUCCESS = "success";

    public static final String LINE = String.valueOf('\n');
}
