package com.wosai.mpay.api.yeahpay.enums;

import java.util.stream.Stream;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayOrderNotifyPayMethodEnum
 * @description:
 * @create: 2025-07-10 20:08
 **/
public enum YeahPayOrderNotifyPayMethodEnum {

    /** 1: 线下支付 */
    OFFLINE("1", "线下"),
    /** 4: 线上-APP支付 */
    ONLINE_APP("4", "线上-APP"),
    /** 5: 线上小程序支付 */
    ONLINE_MINI_PROGRAM("5", "线上小程序"),
    /** 6: 线上-H5支付 */
    ONLINE_H5("6", "线上-H5"),
    /** 8: 线上-PC支付 */
    ONLINE_PC("8", "线上-PC"),
    /** 9: 线上支付（通用） */
    ONLINE("9", "线上"),
    /** 10: Apple Pay */
    APPLE_PAY("10", "APPLE_PAY"),
    /** 11: Google Pay */
    GOOGLE_PAY("11", "GOOGLE_PAY");
    /**
     * 支付方式的编码，用于系统间数据传输。
     */
    private final String code;
    /**
     * 支付方式的描述，用于界面展示或日志记录。
     */
    private final String description;
    /**
     * 枚举的私有构造函数。
     *
     * @param code 支付方式编码
     * @param description 支付方式描述
     */
    YeahPayOrderNotifyPayMethodEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    /**
     * 获取支付方式的编码。
     *
     * @return 代表支付方式的字符串编码
     */
    public String getCode() {
        return code;
    }
    /**
     * 获取支付方式的描述。
     *
     * @return 支付方式的中文描述
     */
    public String getDescription() {
        return description;
    }

    public static YeahPayOrderNotifyPayMethodEnum fromCode(String code) {
        for (YeahPayOrderNotifyPayMethodEnum typeEnum : YeahPayOrderNotifyPayMethodEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

}
