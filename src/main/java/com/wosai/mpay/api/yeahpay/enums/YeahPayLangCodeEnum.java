package com.wosai.mpay.api.yeahpay.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayLangCodeEnum
 * @description:
 * @create: 2025-07-08 16:20
 **/
public enum YeahPayLangCodeEnum {



    EN("en", "英文"),
    TW("TW", "中文繁体"),
    ZH("zh", "中文简体");

    private final String code;
    private final String msg;

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    YeahPayLangCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static YeahPayLangCodeEnum of(String code) {
        for (YeahPayLangCodeEnum e : YeahPayLangCodeEnum.values()) {
            if (code.equals(e.code)) {
                return e;
            }
        }
        return null;
    }

    public static YeahPayLangCodeEnum transLangCode(String langCode) {
        if ("en".equals(langCode)) {
            return EN;
        } else if ("zh-TW".equals(langCode)) {
            return TW;
        } else {
            return ZH;
        }
    }
}
