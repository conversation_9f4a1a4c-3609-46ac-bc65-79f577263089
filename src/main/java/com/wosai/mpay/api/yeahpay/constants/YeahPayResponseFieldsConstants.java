package com.wosai.mpay.api.yeahpay.constants;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayResponseFieldsConstants
 * @description:
 * @create: 2025-06-19 20:04
 **/
public class YeahPayResponseFieldsConstants {

    /** ********************************************************** 公共响应参数 ********************************************************** */
    public static class Common {
        public static final String CODE = "code"; // 业务响应码, 0-成功
        public static final String MESSAGE = "message"; // 错误信息
        public static final String DATA = "data"; // 业务数据
        public static final String TRACE_ID = "traceId"; // 响应标识
    }

    /**
     * ********************************************************** 4.1 主扫(消费者扫商户收款码、线上支付) - UnifiedOrder **********************************************************
     */
    public static class UnifiedOrder {
        public static final String AMOUNT = "amount"; // 标价金额
        public static final String COST_TIME = "costTime"; // 请求耗时，请求接口耗费的时间(ms)
        public static final String CURRENCY = "currency"; // 标价币种
        public static final String LESHUA_ORDER_ID = "leshuaOrderId"; // YeahPay 订单号
        public static final String MERCHANT_ID = "merchantId"; // YeahPay 商户号，由 YeahPay 分配
        public static final String PAY_INFO = "payInfo"; // JSAPI 返回支付信息，如alipay: "pay_info":
        public static final String TD_CODE = "tdCode"; // 二维码
        public static final String CHANNEL_ORDER_ID = "channelOrderId"; // 上游交易单号
        public static final String THIRD_ORDER_ID = "thirdOrderId"; // 商户内部订单号
    }
    /**
     * ********************************************************** 4.2 支付查询(扫码、刷卡) - QueryOrder **********************************************************
     */
    public static class QueryOrder {
        public static final String AMOUNT = "amount"; // 标价金额
        public static final String CURRENCY = "currency"; // 标价币种
        public static final String BUYER_PAY_AMOUNT = "buyerPayAmount"; // 买家实付金额
        public static final String BUYER_PAY_CURRENCY = "buyerPayCurrency"; // 买家实付币种
        public static final String BUYER_PAY_RATE = "buyerPayRate"; // 标价币种与支付币种的兑换比例
        public static final String CHANNEL_ORDER_ID = "channelOrderId"; // 通道订单号
        public static final String COST_TIME = "costTime"; // 请求耗时，请求接口耗费的时间(ms)
        public static final String LESHUA_ORDER_ID = "leshuaOrderId"; // YeahPay 订单号
        public static final String MERCHANT_ID = "merchantId"; // YeahPay 商户号，由 YeahPay 分配
        public static final String PAY_TIME = "payTime"; // 支付时间
        public static final String PAY_WAY = "payWay"; // 支付方式
        public static final String STATUS = "status"; // 订单状态
        public static final String SUB_MERCH_ID = "subMerchId"; // 通道商户号
        public static final String THIRD_ORDER_ID = "thirdOrderId"; // 商户内部订单号
        public static final String MPP_INFO = "mppInfo"; // 支付宝子钱包信息
        public static final String ORDER_SETTLE_STATUS = "orderSettleStatus"; // 订单请款状态
    }
    /**
     * ********************************************************** 4.3 被扫(商户扫消费者付款码) - MicroPay **********************************************************
     */
    public static class MicroPay {
        public static final String CHANNEL_CODE = "channelCode"; // 上游错误提示,,如微信
        public static final String COST_TIME = "costTime"; // 请求耗时，请求接口耗费的时间(ms)
        public static final String LESHUA_ORDER_ID = "leshuaOrderId"; // YeahPay 订单号
        public static final String MERCHANT_ID = "merchantId"; // YeahPay 商户号，由 YeahPay 分配
        public static final String PAY_WAY = "payWay"; // 支付方式,详见附录
        public static final String STATUS = "status"; // 订单状态
        public static final String SUB_MERCH_ID = "subMerchId"; // 通道商户号
        public static final String THIRD_ORDER_ID = "thirdOrderId"; // 商户内部订单号
        public static final String BUYER_PAY_AMOUNT = "buyerPayAmount"; // 买家实付金额
        public static final String BUYER_PAY_CURRENCY = "buyerPayCurrency"; // 买家实付币种
        public static final String MPP_INFO = "mppInfo"; // 支付宝子钱包信息
        public static final String AMOUNT = "amount"; // 标价金额
        public static final String CURRENCY = "currency"; // 标价币种
    }
    /**
     * ********************************************************** 4.4 订单关闭/撤销 - CloseOrder **********************************************************
     */
    public static class CloseOrder {
        public static final String LESHUA_ORDER_ID = "leshuaOrderId"; // YeahPay 订单号
        public static final String MERCHANT_ID = "merchantId"; // YeahPay 商户号，由 YeahPay 分配
        public static final String STATUS = "status"; // 订单状态
        public static final String THIRD_ORDER_ID = "thirdOrderId"; // 商户内部订单号
    }
    /**
     * ********************************************************** 4.5 退款(扫码、刷卡) - Refund **********************************************************
     */
    public static class Refund {
        public static final String AMOUNT = "amount"; // 标价金额, 单位:分
        public static final String PAY_TIME = "payTime"; // 交易时间
        public static final String MERCHANT_ID = "merchantId"; // YeahPay 商户号,由 YeahPay 分配
        public static final String REFUND_ID = "refundId"; // 退款单号
        public static final String STATUS = "status"; // 订单状态
    }
    /**
     * ********************************************************** 4.6 退款查询(扫码、刷卡) - QueryRefund **********************************************************
     */
    public static class QueryRefund {
        public static final String AMOUNT = "amount"; // 标价金额
        public static final String COST_TIME = "costTime"; // 请求耗时，请求接口耗费的时间(ms)
        public static final String CURRENCY = "currency"; // 标价币种
        public static final String LESHUA_ORDER_ID = "leshuaOrderId"; // YeahPay 订单号, 表示对应支付的订单号
        public static final String LESHUA_REFUND_ID = "leshuaRefundId"; // YeahPay 退款单号, 表示退款的订单号，与退款接口 refundId 一致
        public static final String MERCHANT_ID = "merchantId"; // YeahPay 商户号，由 YeahPay 分配
        public static final String MERCHANT_REFUND_ID = "merchantRefundId"; // 商户退款单号
        public static final String REFUND_AMOUNT = "refundAmount"; // 退款金额
        public static final String REFUND_TIME = "refundTime"; // 退款成功时间
        public static final String STATUS = "status"; // 订单状态
        public static final String THIRD_ORDER_ID = "thirdOrderId"; // 商户内部订单号
    }
    /**
     * ********************************************************** 4.7 支付结果通知(回调接口) - NOTIFY **********************************************************
     */
    public static class Notify {
        public static final String AMOUNT = "amount"; // 订单金额
        public static final String ATTACH = "attach"; // 附加信息
        public static final String CHANNEL_DATETIME = "channelDatetime"; // 用户实际支付时间
        public static final String CHANNEL_ORDER_ID = "channelOrderId"; // 通道订单号
        public static final String LESHUA_ORDER_ID = "leshuaOrderId"; // YeahPay 订单号
        public static final String MERCHANT_ID = "merchantId"; // 商户ID
        public static final String PAY_TIME = "payTime"; // YeahPay 结算时间
        public static final String PAY_WAY = "payWay"; // 支付方式
        public static final String SETTLEMENT_AMOUNT = "settlementAmount"; // 订单应结算金额
        public static final String STATUS = "status"; // 支付状态
        public static final String THIRD_ORDER_ID = "thirdOrderId"; // 商户订单号
        public static final String TRADE_TYPE = "tradeType"; // 支付类型
    }
    /**
     * **********************************************************
     * 5.1 线下刷卡订单退款(线下刷卡) - refundCardOffLineOrder
     * **********************************************************
     */
    public static class RefundCardOffLineOrder {
        public static final String CODE = "code"; //错误码:0-成功,其余皆为失败
        public static final String MESSAGE = "message"; //错误信息
        public static final String DATA = "data"; //数据
        public static final String AMOUNT = "amount"; //退款金额,单位：分
        public static final String PAY_TIME = "payTime"; //交易时间
        public static final String MERCHANT_ID = "merchantId"; //YeahPay 商户号由 YeahPay 分配
        public static final String ORDER_ID = "orderId"; //原交易订单号
        public static final String STATUS = "status"; //状态:1-失败,2-成功,3-退款中
        public static final String REFUND_ID = "refundId"; //退款订单号
    }
    /**
     * **********************************************************
     * 5.2 线下刷卡订单撤销(线下刷卡) - voidCardOffLineOrder
     * **********************************************************
     */
    public static class VoidCardOffLineOrder {
        public static final String CODE = "code"; //错误码:0-成功,其余皆为失败
        public static final String MESSAGE = "message"; //错误信息
        public static final String DATA = "data"; //数据
        public static final String AMOUNT = "amount"; //退款金额,单位：分
        public static final String PAY_TIME = "payTime"; //交易时间
        public static final String MERCHANT_ID = "merchantId"; //YeahPay 商户号由 YeahPay 分配
        public static final String ORDER_ID = "orderId"; //原交易订单号
        public static final String STATUS = "status"; //状态:1-失败,2-成功,3-退款中
        public static final String REFUND_ID = "refundId"; //退款订单号
    }
    /**
     * **********************************************************
     * 5.3 ApplePay,GooglePay 支付 - googleAndApplePay
     * **********************************************************
     */
    public static class GoogleAndApplePay {
        public static final String CODE = "code"; //错误码:0-成功,其余皆为失败
        public static final String MESSAGE = "message"; //错误信息
        public static final String DATA = "data"; //数据
        public static final String CLIENT_SECRET = "clientSecret"; //密文,交易使用加密
        public static final String COUNTRY_CODE = "countryCode"; //国家:新加坡-SG
        public static final String INTENT_ID = "intentId"; //通道订单号
        public static final String ORDER_ID = "orderId"; //YeahPay 交易订单号
        public static final String AMOUNT = "amount"; //金额:单位:分
        public static final String CURRENCY = "currency"; //交易币种:SGD
    }
    /**
     * **********************************************************
     * 6.1 获取收银台地址 - getRedirectUrl
     * **********************************************************
     */
    public static class GetRedirectUrl {
        public static final String CODE = "code"; //返回码,200:请求成功,其他都表示失败
        public static final String MESSAGE = "message"; //返回信息
        public static final String TRACE_ID = "traceId"; //请求标识
        public static final String DATA = "data"; //数据
        public static final String URL = "url"; //收银台地址+token e.g. http://www.teste.com?token=1234
    }
    /**
     * **********************************************************
     * 6.2 查询支付方式 - getTradeType
     * **********************************************************
     */
    public static class GetTradeType {
        public static final String CODE = "code"; //返回码,200:请求成功,其他都表示失败
        public static final String MESSAGE = "message"; //返回信息
        public static final String TRACE_ID = "traceId"; //请求标识
        public static final String DATA = "data"; //数据
        public static final String TRADE_CODE = "tradeCode"; //支付方式编码
        public static final String TRADE_NAME = "tradeName"; //支付方式名称
        public static final String PARENT_TRADE_CODE = "parentTradeCode"; //父类型编码
        public static final String LEVEL = "level"; //层级1支付类型2子钱包
        public static final String SUB_TRADE_TYPE = "subTradeType"; //子钱包列表
    }
}
