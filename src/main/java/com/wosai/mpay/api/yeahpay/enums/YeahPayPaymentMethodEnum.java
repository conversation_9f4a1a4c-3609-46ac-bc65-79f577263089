package com.wosai.mpay.api.yeahpay.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayPaymentMethodEnum
 * @description:
 * @create: 2025-06-20 09:31
 **/
public enum YeahPayPaymentMethodEnum {

    /**
     * WXZF - Wechat Pay - 新加坡商户，支持主扫/被扫
     */
    WXZF("WXZF", "Wechat Pay", "Singapore merchant with support for main scanning/being swept"),

    /**
     * ZFBZF - Alipay - 新加坡商户，支持主扫/被扫
     */
    ZFBZF("ZFBZF", "Alipay", "Singapore merchant that supports master scanning/being swept"),

    /**
     * PayNowPay - PayNow - 新加坡商户，支持主扫/被扫
     */
    PAYNOWPAY("PayNowPay", "PayNow", "Singapore merchant that supports master scanning/being swept"),

    /**
     * LiquidPay - Thai QR - 新加坡商户，支持主扫/被扫
     */
    LIQUIDPAY("LiquidPay", "Thai QR", "Singapore merchant that supports master scanning/being swept"),

    /**
     * DashPay - Dash - 新加坡商户，支持主扫/被扫
     */
    DASHPAY("DashPay", "Dash", "Singapore merchants that support master scanning/being swept"),

    /**
     * GrabPay - GrabPay - 新加坡商户，支持主扫/被扫
     */
    GRABPAY("GrabPay", "GrabPay", "Singapore merchant with support for master scanning/being swept"),

    /**
     * HKWXZF - Hong Kong wechat - 香港商户，支持主扫/被扫
     */
    HKWXZF("HKWXZF", "Hong Kong wechat", "Hong Kong merchants that support master scanning/being scanned"),

    /**
     * AMEX - AMEX - 仅用作结账订单回调的基本数据说明
     */
    AMEX("AMEX", "AMEX", "Only used as a basic data explanation for checkout order callback"),

    /**
     * ApplePay - ApplePay - 仅用作结账订单回调的基本数据说明
     */
    APPLEPAY("ApplePay", "ApplePay", "Only used as a basic data explanation for checkout order callback"),

    /**
     * GooglePay - GooglePay - 仅用作结账订单回调的基本数据说明
     */
    GOOGLEPAY("GooglePay", "GooglePay", "Only used as a basic data explanation for checkout order callback"),

    /**
     * MASTER - MASTER - 仅用作结账订单回调的基本数据说明
     */
    MASTER("MASTER", "MASTER", "Only used as a basic data explanation for checkout order callback"),

    /**
     * UnionPay - UnionPay - 仅用作结账订单回调的基本数据说明
     */
    UNIONPAY("UnionPay", "UnionPay", "Only used as a basic data explanation for checkout order callback"),

    /**
     * VISA - VISA - 仅用作结账订单回调的基本数据说明
     */
    VISA("VISA", "VISA", "Only used as a basic data explanation for checkout order callback");

    private final String code;
    private final String description;
    private final String supportInfo;

    private YeahPayPaymentMethodEnum(String code, String description, String supportInfo) {
        this.code = code;
        this.description = description;
        this.supportInfo = supportInfo;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public String getSupportInfo() {
        return supportInfo;
    }

    public static YeahPayPaymentMethodEnum fromCode(String code) {
        for (YeahPayPaymentMethodEnum methodEnum : YeahPayPaymentMethodEnum.values()) {
            if (methodEnum.getCode().equals(code)) {
                return methodEnum;
            }
        }
        return null; // Or throw an exception if you prefer
    }

}
