package com.wosai.mpay.api.yeahpay.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayOrderStatusEnum
 * @description:
 * @create: 2025-06-20 09:25
 **/
public enum YeahPayOrderStatusEnum {

    /**
     * 0 - In payment (unpaid) - 待支付
     */
    IN_PAYMENT("0", "In payment (unpaid)"),
    /**
     * 2 - Successful payment - 支付成功
     */
    SUCCESSFUL_PAYMENT("2", "Successful payment"),
    /**
     * 6 - Order closing (order cancellation) - 订单关闭（订单取消）
     */
    ORDER_CLOSING("6", "Order closing (order cancellation)"),
    /**
     * 8 - Payment failure - 支付失败
     */
    PAYMENT_FAILURE("8", "Payment failure"),
    /**
     * 10 - Refunds pending - 退款中
     */
    REFUNDS_PENDING("10", "Refunds pending"),
    /**
     * 11 - Successful refund - 退款成功
     */
    SUCCESSFUL_REFUND("11", "Successful refund"),
    /**
     * 12 - Refund failure - 退款失败
     */
    REFUND_FAILURE("12", "Refund failure");

    private final String code;
    private final String description;

    private YeahPayOrderStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static YeahPayOrderStatusEnum fromCode(String code) {

        for (YeahPayOrderStatusEnum statusEnum : YeahPayOrderStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }

        return null;
    }


    /**
     * 交易是否完成
     *
     * @param status
     * @return
     */
    public static boolean isOrderCompleted(String status) {
        return YeahPayOrderStatusEnum.SUCCESSFUL_PAYMENT.getCode().equals(status)
                || YeahPayOrderStatusEnum.PAYMENT_FAILURE.getCode().equals(status)
                || YeahPayOrderStatusEnum.ORDER_CLOSING.getCode().equals(status)
                || YeahPayOrderStatusEnum.SUCCESSFUL_REFUND.getCode().equals(status)
                || YeahPayOrderStatusEnum.REFUND_FAILURE.getCode().equals(status);
    }


    /**
     * 交易是否成功
     *
     * @param status
     * @return
     */
    public static boolean isPaymentSuccessful(String status) {
        return YeahPayOrderStatusEnum.SUCCESSFUL_PAYMENT.getCode().equals(status);
    }


    /**
     * 交易是否失败
     *
     * @param status
     * @return
     */
    public static boolean isPaymentFailure(String status) {
        return YeahPayOrderStatusEnum.PAYMENT_FAILURE.getCode().equals(status);
    }


    /**
     * 退款是否成功
     *
     * @param status
     * @return
     */
    public static boolean isRefundSuccessful(String status) {
        return YeahPayOrderStatusEnum.SUCCESSFUL_REFUND.getCode().equals(status);
    }

    /**
     * 退款是否失败
     *
     * @param status
     * @return
     */
    public static boolean isRefundFailure(String status) {
        return YeahPayOrderStatusEnum.REFUND_FAILURE.getCode().equals(status);
    }

    /**
     * 交易是否关闭
     *
     * @param status
     * @return
     */
    public static boolean isPaymentClose(String status) {
        return YeahPayOrderStatusEnum.ORDER_CLOSING.getCode().equals(status);
    }
}
