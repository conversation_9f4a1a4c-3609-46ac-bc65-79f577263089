package com.wosai.mpay.api.yeahpay.enums;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayOrderNotifyPayCatalogEnum
 * @description:
 * @create: 2025-07-15 13:49
 **/
public enum YeahPayOrderNotifyPayCatalogEnum {

    // E-Wallets / E-Money
    WECHAT_PAY("wechat", "WeChat Pay"),
    ALIPAY("alipay", "Alipay"),
    LIQUID("liquid", "Liquid Pay"),
    PAYNOW("paynow", "PayNow"),
    GRABPAY("grabpay", "GrabPay"),
    DASH_PAY("dashpay", "Dash Pay"),
    // Credit / Debit Cards
    VISA("visa", "Visa"),
    MASTERCARD("master", "Mastercard"),
    UNIONPAY("unionpay", "UnionPay"),
    AMEX("amex", "American Express"),
    // Bank Transfer
    FPS("fps", "Faster Payment System");

    private final String code;
    private final String displayName;


    YeahPayOrderNotifyPayCatalogEnum(String code, String displayName) {
        this.code = code;
        this.displayName = displayName;
    }
    /**
     * 获取支付方式的唯一编码（通常用于和外部API、数据库交互）。
     * @return 支付方式的小写编码字符串。
     */
    public String getCode() {
        return code;
    }
    /**
     * 获取支付方式的显示名称（通常用于UI展示或日志）。
     * @return 友好可读的显示名称。
     */
    public String getDisplayName() {
        return displayName;
    }


    public static YeahPayOrderNotifyPayCatalogEnum fromCode(String code) {
        for (YeahPayOrderNotifyPayCatalogEnum typeEnum : YeahPayOrderNotifyPayCatalogEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
