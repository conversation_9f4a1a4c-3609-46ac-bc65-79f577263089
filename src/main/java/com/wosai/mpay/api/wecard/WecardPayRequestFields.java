package com.wosai.mpay.api.wecard;

/**
 * <AUTHOR>
 * @description 腾讯云商付-请求字段定义
 * @date 2024/11/4
 */
public class WecardPayRequestFields {
    /**
     * 公共请求参数
     */
    public static final String APP_ID = "app_id"; // 应用ID
    public static final String MCH_ID = "mch_id"; // 商户号
    public static final String NONCE_STR = "nonce_str"; // 随机字符串
    public static final String SIGN = "sign"; // 签名
    public static final String SIGN_TYPE = "sign_type"; // 签名类型
    public static final String TIMESTAMP = "timestamp"; // 时间戳

    /**
     * 统一下单接口参数
     */
    public static final String CHANNEL_SUB_MERCHANT_ID = "ChannelSubMerchantId"; // 腾讯云商付平台商户编号
    public static final String TOTAL_AMOUNT = "TotalAmount"; // 订单金额，单位为分
    public static final String PAY_TYPE = "PayType"; // 支付场景
    public static final String PAY_CHANNEL = "PayChannel"; // 支付渠道
    public static final String OUT_ORDER_ID = "OutOrderId"; // 外部订单号，即业务系统侧订单
    public static final String ORDER_SUBJECT = "OrderSubject"; // 订单标题
    public static final String EXPIRE_TIME = "ExpireTime"; // 订单过期时间
    public static final String NOTIFY_URL = "NotifyUrl"; // 支付成功后的回调地址
    public static final String FRONT_URL = "FrontUrl"; // 用户支付成功后，前端跳转URL
    public static final String EXTERNAL_PAYMENT_DATA = "ExternalPaymentData"; // 支付渠道扩展字段
    public static final String PROFIT_SHARE_FLAG = "ProfitShareFlag"; // 是否允许分账标识
    public static final String PROFIT_SHARE_INFO_LIST = "ProfitShareInfoList"; // 分账信息列表
    public static final String PAYER_INFO = "PayerInfo"; // 用户信息
    public static final String STORE_INFO = "StoreInfo"; // 门店信息
    public static final String PRODUCT_LIST = "ProductList"; // 商品列表
    public static final String SCENE_INFO = "SceneInfo"; // 设备信息
    public static final String GOODS_TAG = "GoodsTag"; // 优惠标记
    public static final String ATTACHMENT = "Attachment"; // 附加信息
    public static final String REMARK = "Remark"; // 付款备注
    public static final String FUND_ALLOCATE_LIST = "FundAllocateList"; // 挂账信息
    public static final String WALLET_INFO = "WalletInfo"; // 支付钱包信息
    public static final String ASYNC_ORDER = "AsyncOrder"; // 是否异步下单扣款
    public static final String CURRENCY = "Currency"; // 货币类型
    public static final String PAY_CHANNEL_SPLIT_PARENT = "PayChannelSplitParent"; // 业务订单母单信息
    public static final String PAY_CHANNEL_SPLIT_REFUND_PARENT = "PayChannelSplitRefundParent"; // 业务订单母单信息


    /**
     * 分账标识
     */
    public static final String PROFIT_SHARE_FLAG_API = "API_PROFIT_SHARE"; // 允许调用请求分账
    public static final String PROFIT_SHARE_FLAG_NO = "NO_PROFIT_SHARE"; // 不允许调用请求分账

    /**
     * ExternalPaymentData 字段
     */
    public static final String AUTH_CODE = "AuthCode"; // 支付凭证：用户付款码
    public static final String WX_APP_ID = "WxAppId"; // 微信开放平台的应用 AppId
    public static final String WX_OPEN_ID = "WxOpenId"; // 微信用户在当前 WxAppId 下的 Openid
    public static final String WX_SERVICE_INFO = "WxServiceInfo"; // 微信订单服务信息
    public static final String WX_RISK_INFO = "WxRiskInfo"; // 微信订单风险金信息
    public static final String WX_POST_PAYMENTS = "WxPostPayments"; // 微信支付分免密订单后付费项目明细
    public static final String NEED_USER_CONFIRM = "NeedUserConfirm"; // 微信支付分免密订单模式
    public static final String ALI_APP_ID = "AliAppId"; // 支付宝开放平台的应用 AppId
    public static final String ALI_USER_ID = "AliUserId"; // 支付宝用户在当前 AliAppId 下的 Userid
    public static final String ALI_AGREEMENT_PARAMS = "AliAgreementParams"; // 支付宝免密代扣支付协议参数
    public static final String CMB_YWT_ARG_NO = "CmbYwtArgNo"; // 招行一网通支付免登录协议号

    /**
     * 退款
     */
    public static final String OUT_REFUND_ID = "OutRefundId"; // 退款外部订单号，即业务系统侧退款订单号
    public static final String REFUND_AMOUNT="RefundAmount";



}