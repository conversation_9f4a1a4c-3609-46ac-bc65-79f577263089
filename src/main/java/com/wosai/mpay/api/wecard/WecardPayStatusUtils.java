package com.wosai.mpay.api.wecard;

/**
 * <AUTHOR>
 * @description 腾讯云商付-状态工具类
 * @date 2024/11/4
 */
public class WecardPayStatusUtils {

    /**
     * 判断支付是否成功
     *
     * @param tradeState 交易状态
     * @return 是否支付成功
     */
    public static boolean isPaymentSuccess(String tradeState) {
        return WecardPayConstants.ORDER_STATUS_SUCCESS.equals(tradeState);
    }

    /**
     * 判断支付是否已完成（成功、退款、关闭或失败）
     *
     * @param tradeState 交易状态
     * @return 是否支付已完成
     */
    public static boolean isPaymentCompleted(String tradeState) {
        return WecardPayConstants.ORDER_STATUS_SUCCESS.equals(tradeState) ||
                WecardPayConstants.ORDER_STATUS_CLOSED.equals(tradeState) ||
                WecardPayConstants.ORDER_STATUS_PAY_FAIL.equals(tradeState) ||
                WecardPayConstants.ORDER_STATUS_PART_REFUND.equals(tradeState) ||
                WecardPayConstants.ORDER_STATUS_FULL_REFUND.equals(tradeState);
    }

    /**
     * 判断支付是否处理中
     *
     * @param tradeState 交易状态
     * @return 是否支付处理中
     */
    public static boolean isPaymentProcessing(String tradeState) {
        return WecardPayConstants.ORDER_STATUS_PAYING.equals(tradeState)
                || WecardPayConstants.ORDER_STATUS_WAIT_CONFIRM.equals(tradeState)
                || WecardPayConstants.ORDER_STATUS_BEFORE.equals(tradeState)
                || WecardPayConstants.ORDER_STATUS_INIT.equals(tradeState);
    }

    /**
     * 判断退款是否成功
     *
     * @param refundStatus 退款状态
     * @return 是否退款成功
     */
    public static boolean isRefundSuccess(String refundStatus) {
        return WecardPayConstants.REFUND_STATUS_SUCCESS.equals(refundStatus);
    }

    /**
     * 判断退款是否处理中
     *
     * @param refundStatus 退款状态
     * @return 是否退款处理中
     */
    public static boolean isRefundProcessing(String refundStatus) {
        return WecardPayConstants.REFUND_STATUS_PROCESSING.equals(refundStatus);
    }

    /**
     * 判断退款是否失败
     *
     * @param refundStatus 退款状态
     * @return 是否退款失败
     */
    public static boolean isRefundFailed(String refundStatus) {
        return WecardPayConstants.REFUND_STATUS_FAILED.equals(refundStatus);
    }
}