package com.wosai.mpay.api.wecard;

/**
 * <AUTHOR>
 * @description 腾讯云商付-响应字段定义
 * @date 2024/11/4
 */
public class WecardPayResponseFields {
    /**
     * 公共响应参数
     */
    public static final String RESPONSE = "Response"; // 响应
    public static final String REQUEST_ID = "RequestId"; // 请求ID
    public static final String RESULT = "Result"; // 结果对象
    public static final String ERR_CODE = "ErrCode"; // 错误码
    public static final String ERR_MESSAGE = "ErrMessage"; // 错误信息

    /**
     * 业务数据字段
     */
    public static final String OUT_ORDER_ID = "OutOrderId"; // 外部订单号，即业务系统侧订单
    public static final String CHANNEL_ORDER_ID = "ChannelOrderId"; // 腾讯云商付平台订单号
    public static final String THIRD_PAY_ORDER_ID = "ThirdPayOrderId"; // 收单方订单号
    public static final String PAY_INFO = "PayInfo"; // 返回支付参数
    public static final String PAY_INFO_TYPE = "PayInfoType"; // 支付参数类型
    public static final String ORDER_STATUS = "OrderStatus"; // 订单状态
    public static final String TOTAL_AMOUNT = "TotalAmount"; // 订单金额，单位：分
    public static final String PAY_AMOUNT = "PayAmount"; // 实际支付金额，单位：分
    public static final String MERCHANT_FEE_AMOUNT = "MerchantFeeAmount"; // 手续费，单位：分
    public static final String FAIL_REASON = "FailReason"; // 失败原因
    public static final String ORDER_TIME = "OrderTime"; // 订单交易时间，秒级时间戳
    public static final String FINISH_TIME ="TimeFinish";
    public static final String PAY_CHL_PAYER_ID="PayChlPayerId";
    public static final String PAY_CHL_ORDER_ID="PayChlOrderId";
    public static final String PAY_CHANNEL="PayChannel";

    public static final String REFUND_STATUS = "RefundStatus"; // 退款状态
    public static final String CHANNEL_REFUND_ID="ChannelRefundId";

}