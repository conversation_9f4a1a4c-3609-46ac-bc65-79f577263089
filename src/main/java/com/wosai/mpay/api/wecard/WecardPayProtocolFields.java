package com.wosai.mpay.api.wecard;

/**
 * <AUTHOR>
 * @description 腾讯云商付协议字段定义
 * @date 2024/11/4
 */
public class WecardPayProtocolFields {
    /**
     * 协议默认值
     */
    public static final String DEFAULT_CONTENT_TYPE = "application/json; charset=utf-8";

    public static final String DEFAULT_ACTION = "wxpay";
    public static final String DEFAULT_VERSION = "2023-04-13";
    public static final String DEFAULT_REGION = "ap-shanghai";
    public static final String DEFAULT_SERVICE = "wxpay";
    public static final String DEFAULT_REQUEST_TYPE = "tc3_request";

    /**
     * HTTP 请求头参数
     */
    public static final String ACTION = "X-TC-Action"; // 操作的接口名称
    public static final String REGION = "X-TC-Region"; // 地域参数
    public static final String TIMESTAMP = "X-TC-Timestamp"; // 当前 UNIX 时间戳
    public static final String VERSION = "X-TC-Version"; // API 版本
    public static final String OCODE = "X-TC-Ocode"; // 腾讯云商付主体标识

    public static final String AUTHORIZATION = "Authorization"; // 身份认证头部字段
    public static final String CONTENT_TYPE = "Content-Type"; // 内容类型
    public static final String HOST = "Host"; // 请求的主机

}
