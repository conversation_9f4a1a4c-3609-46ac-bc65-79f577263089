package com.wosai.mpay.api.wecard;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.bind.DatatypeConverter;
import java.io.IOException;
import java.net.URL;
import java.security.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class WecardPayClient {

    private static final Logger logger = LoggerFactory.getLogger(WecardPayClient.class);

    private int connectTimeout = 3000;

    private int readTimeout = 15000;

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }


    public Map<String, Object> call(String gatewayUrl, String ocode, String secretId, String privateKey, Map<String, Object> request) throws MpayException, MpayApiNetworkError {
        return call(gatewayUrl, ocode, secretId, privateKey, null, request);
    }

    /**
     * 调用接口
     *
     * @param gatewayUrl url
     * @param request    请求报文
     * @return 响应结果
     * @throws MpayException
     * @throws MpayApiNetworkError
     */
    public Map<String, Object> call(String gatewayUrl, String ocode, String secretId, String privateKey, String publicKey, Map<String, Object> request) throws MpayException, MpayApiNetworkError {
        try {
            URL url = new URL(gatewayUrl);
            String host = url.getHost();
            String uri = url.getPath();
            // 准备加签数据
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
            String action = WecardPayProtocolFields.DEFAULT_ACTION;
            String payload =JsonUtil.objectToJsonString(request);
            // 获取加签数据
            String authorization = genAuthorization(host, payload, uri, ocode, secretId, privateKey, publicKey, action, timestamp);

            HashMap<String, String> headers = new HashMap<>();
            headers.put(WecardPayProtocolFields.AUTHORIZATION, authorization);
            headers.put(WecardPayProtocolFields.CONTENT_TYPE, WecardPayProtocolFields.DEFAULT_CONTENT_TYPE);
            headers.put(WecardPayProtocolFields.HOST, host);
            headers.put(WecardPayProtocolFields.ACTION, action);
            headers.put(WecardPayProtocolFields.TIMESTAMP, timestamp);
            headers.put(WecardPayProtocolFields.VERSION, WecardPayProtocolFields.DEFAULT_VERSION);
            headers.put(WecardPayProtocolFields.REGION, WecardPayProtocolFields.DEFAULT_REGION);
            headers.put(WecardPayProtocolFields.OCODE, ocode);
            logger.info("request: {},{}", payload, headers);
            String resultdata = WebUtils.doPost(null, null, gatewayUrl, WecardPayProtocolFields.DEFAULT_CONTENT_TYPE, headers, payload.getBytes(WecardPayConstants.UTF8), connectTimeout, readTimeout);
            logger.info("response: {}", resultdata);
            return MapUtils.getMap(JsonUtil.jsonStringToObject(resultdata, Map.class), WecardPayResponseFields.RESPONSE);
        } catch (IOException e) {
            logger.error("Call TencentPay API error", e);
            throw new MpayApiNetworkError("Call TencentPay API error: " + e.getMessage());
        } catch (NoSuchAlgorithmException e) {
            logger.error("Call TencentPay API error", e);
            throw new MpayException("Call TencentPay API error: " + e.getMessage());
        }
    }

    // genAuthorization 计算签名
    private static String genAuthorization(String host, String payload, String uri, String ocode, String secretId, String privateKey, String publicKey, String action, String timestamp)
            throws NoSuchAlgorithmException, MpayException {
        return genAuthorization(host, payload, uri, ocode, secretId, privateKey, publicKey, action, timestamp, false, "");
    }

    // genAuthorization 计算签名
    private static String genAuthorization(String host, String payload, String uri, String ocode, String secretId, String privateKey, String publicKey, String action, String timestamp,
                                           boolean isVerify, String signature)
            throws NoSuchAlgorithmException, MpayException {
        // ************* 步骤 1：拼接规范请求串 *************
        String httpRequestMethod = WecardPayConstants.HTTP_METHOD_POST;
        String service = WecardPayProtocolFields.DEFAULT_SERVICE;
        String canonicalUri = uri;
        String canonicalQueryString = "";
        StringJoiner canonicalHeadersJoiner = new StringJoiner("\n");
        canonicalHeadersJoiner.add("content-type:application/json; charset=utf-8");
        canonicalHeadersJoiner.add("host:" + host);
        canonicalHeadersJoiner.add("x-tc-ocode:" + ocode);
        canonicalHeadersJoiner.add("x-tc-action:" + action.toLowerCase());
        canonicalHeadersJoiner.add("");
        String canonicalHeaders =canonicalHeadersJoiner.toString();
        String signedHeaders = "content-type;host;x-tc-ocode;x-tc-action";

        String hashedRequestPayload = sha256Hex(payload);
        StringJoiner canonicalRequestJoiner = new StringJoiner("\n");
        canonicalRequestJoiner.add(httpRequestMethod);
        canonicalRequestJoiner.add(canonicalUri);
        canonicalRequestJoiner.add(canonicalQueryString);
        canonicalRequestJoiner.add(canonicalHeaders);
        canonicalRequestJoiner.add(signedHeaders);
        canonicalRequestJoiner.add(hashedRequestPayload);
        String canonicalRequest=canonicalRequestJoiner.toString();
        logger.debug("canonicalRequest:{}", canonicalRequest);

        // ************* 步骤 2：拼接待签名字符串 *************
        String credentialScope = service + "/" + WecardPayProtocolFields.DEFAULT_REQUEST_TYPE;
        String hashedCanonicalRequest = sha256Hex(canonicalRequest);
        String stringToSign = WecardPayConstants.algorithm + "\n" + timestamp + "\n" + credentialScope + "\n" + hashedCanonicalRequest;
        logger.debug("stringToSign:{}", stringToSign);

        if (isVerify) {
            // 如果是验签，这里走验签的逻辑
            boolean flag = RsaSignature.validateSign(stringToSign, signature, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, publicKey);
            return flag ? "1" : "0";
        }

        // ************* 步骤 3：计算签名 *************
        signature = RsaSignature.sign(stringToSign, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, privateKey);
        logger.debug("signature:{}", signature);

        // ************* 步骤 4：拼接 Authorization *************
        String authorization = WecardPayConstants.algorithm + " " + "Credential=" + secretId + "/" + credentialScope + ", "
                + "SignedHeaders=" + signedHeaders + ", " + "Signature=" + signature;
        logger.debug("authorization:{}", authorization);
        return authorization;
    }

    public static String sha256Hex(String s) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance(Digest.SHA256);
        byte[] d = md.digest(s.getBytes(WecardPayConstants.UTF8));
        return DatatypeConverter.printHexBinary(d).toLowerCase();
    }
}