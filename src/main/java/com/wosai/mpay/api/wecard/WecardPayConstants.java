package com.wosai.mpay.api.wecard;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @description 腾讯云商付-常量定义
 * @date 2024/11/4
 */
public class WecardPayConstants {

    public static final String HTTP_METHOD_POST = "POST";

    public final static Charset UTF8 = StandardCharsets.UTF_8;

    public final static String algorithm = "SHA256withRSA";

    /**
     * 支付渠道
     */
    public static final String CHANNEL_WECHAT = "WXPAY"; // 微信支付
    public static final String CHANNEL_ALIPAY = "ALIPAY"; // 支付宝
    public static final String CHANNEL_UNIONPAY = "UNIONPAY"; // 银联支付
    /**
     * 支付场景
     */
    public static final String PAY_TYPE_SWIPE = "SWIPE"; // 被扫
    public static final String PAY_TYPE_MINI_PROGRAM = "MINI_PROGRAM"; // 小程序
    public static final String PAY_TYPE_PUBLIC = "PUBLIC"; // 公众号
    public static final String PAY_TYPE_H5 = "H5"; // H5

    /**
     * 交易状态
     */
    public static final String ORDER_STATUS_BEFORE = "BEFORE"; // 预下单
    public static final String ORDER_STATUS_INIT = "INIT"; // 初始化
    public static final String ORDER_STATUS_PAYING = "PAYING"; // 支付中
    public static final String ORDER_STATUS_SUCCESS = "SUCCESS"; // 支付成功
    public static final String ORDER_STATUS_WAIT_CONFIRM = "WAIT_CONFIRM"; // 待支付
    public static final String ORDER_STATUS_CLOSED = "CLOSED"; // 关单
    public static final String ORDER_STATUS_PAY_FAIL = "PAY_FAIL"; // 支付失败
    public static final String ORDER_STATUS_PART_REFUND = "PART_REFUND"; // 部分退款
    public static final String ORDER_STATUS_FULL_REFUND = "FULL_REFUND"; // 全部退款

    /**
     * 退款状态
     */
    public static final String REFUND_STATUS_SUCCESS = "SUCCESS"; // 退款成功
    public static final String REFUND_STATUS_FAILED = "FAILED"; // 退款失败
    public static final String REFUND_STATUS_PROCESSING = "PROCESSING"; // 退款处理中


    /**
     * 日期格式
     */
    public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss"; // 日期时间格式

    /**
     * 分账相关
     */
    public static final String PROFIT_SHARE_FLAG_API = "API_PROFIT_SHARE"; // 允许调用请求分账
    public static final String PROFIT_SHARE_FLAG_NO = "NO_PROFIT_SHARE"; // 不允许调用请求分账

    /**
     * 支付参数类型
     */
    public static final String PAY_INFO_TYPE_JSAPI = "JSAPI"; // JSAPI支付
    public static final String PAY_INFO_TYPE_APP = "APP"; // APP支付
    public static final String PAY_INFO_TYPE_H5 = "H5"; // H5支付
    public static final String PAY_INFO_TYPE_NATIVE = "NATIVE"; // 扫码支付

}