package com.wosai.mpay.api.wecard.enums;

import com.wosai.mpay.api.jsbank.enums.JSBRespCodeEnum;

/**
 * <AUTHOR>
 * @description 腾讯云商付-响应码枚举
 * @date 2024/11/4
 */
public enum WecardPayRespCodeEnum {
    // 通用返回码
    SUCCESS("SUCCESS", "成功"),
    FAILED_OPERATION_SYSTEM_ERROR("FailedOperation.SystemError", "系统未知异常"),

    // 订单相关错误码
    ORDER_INVALID_PARAMETER("ORDER.INVALID_PARAMETER", "参数有误"),
    ORDER_CURRENT_CANNOT_CLOSE("ORDER.CURRENT_CANNOT_CLOSE", "订单当前状态不允许关单"),
    ORDER_RECORD_ALREADY_EXIST("ORDER.ORDER_RECORD_ALREADY_EXIST", "订单记录已存在，请不要重复操作"),
    ORDER_RECORD_NOT_EXIST("ORDER.RECORD_NOT_EXIST", "订单记录不存在"),
    ORDER_OPERATE_IS_PROCESSING("ORDER.OPERATE_IS_PROCESSING", "订单正在执行，请稍后再试"),
    ORDER_CANNOT_REFUND("ORDER.ORDER_CANNOT_REFUND", "订单无法退款");

    private final String code;
    private final String desc;

    WecardPayRespCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static WecardPayRespCodeEnum of(String code) {
        if (code == null) {
            return null;
        }
        for (WecardPayRespCodeEnum e : WecardPayRespCodeEnum.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }

    /**
     * 响应是否成功
     *
     * @param respCode
     * @return
     */
    public static boolean isResponseSuccess(String respCode) {
        return WecardPayRespCodeEnum.SUCCESS.getCode().equals(respCode);
    }
}