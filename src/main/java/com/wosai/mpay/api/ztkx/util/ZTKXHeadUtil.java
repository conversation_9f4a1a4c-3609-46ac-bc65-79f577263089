package com.wosai.mpay.api.ztkx.util;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;

public class ZTKXHeadUtil {
    public final static String DATETIME_FORMAT_HHMMSS = "HHmmss";
    public final static String DATE_FORMAT_YYYYMMDD = "yyyyMMdd";
	public final static String DATETIME_FORMAT_YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

	/**
     * 获取交易流水号
     *
     * @return
     */
    public static String getTranFlow(String merchantId) {
        return merchantId + getNowDateStr(DATETIME_FORMAT_YYYYMMDDHHMMSS) + getRandom();
    }

    /**
     * 生成6位随机数
     *
     * @return
     */
    public static String getRandom() {
        String result = "";
        for (int i = 0; i < 6; i++) {
            int random = new Random().nextInt(10);
            result = result + random;
        }
        return result;
    }

    /**
     * 获取交易时间
     *
     * @return
     */
    public static String getTime() {
        return getNowDateStr(DATETIME_FORMAT_HHMMSS);
    }

    /**
     * 获取交易日期
     *
     * @return
     */
    public static String getDate() {
        return getNowDateStr(DATE_FORMAT_YYYYMMDD);
    }

    /**
     * 获得当前时间字符串
     *
     * @param formatStr 日期格式
     * @return string yyyy-MM-dd
     */
    public static String getNowDateStr(String formatStr) {
        SimpleDateFormat format = new SimpleDateFormat(formatStr);
        return format.format(getNowDate());
    }

    /**
     * 获得系统当前时间
     *
     * @return Date
     */
    public static Date getNowDate() {
        Calendar c = Calendar.getInstance();
        return c.getTime();
    }

}