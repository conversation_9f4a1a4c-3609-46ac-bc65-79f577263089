package com.wosai.mpay.api.ztkx.enums;

/**
 * 中投科信银行卡类型枚举
 * <AUTHOR>
 * @date 2025/2/26 22:45
 */
public enum ZTKXBankCardTypeEnum {
    DEBIT_CARD("01", "借记卡"),
    ENTERPRISE_ACCOUNT("99", "企业账户");

    private final String code;
    private final String desc;

    ZTKXBankCardTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ZTKXBankCardTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ZTKXBankCardTypeEnum type : ZTKXBankCardTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
