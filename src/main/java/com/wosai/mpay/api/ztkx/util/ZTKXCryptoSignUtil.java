package com.wosai.mpay.api.ztkx.util;

import cfca.sadk.algorithm.common.Mechanism;
import cfca.sadk.algorithm.common.PKCS7SignedData;
import cfca.sadk.algorithm.common.PKIException;
import cfca.sadk.algorithm.sm2.SM2PrivateKey;
import cfca.sadk.lib.crypto.JCrypto;
import cfca.sadk.lib.crypto.Session;
import cfca.sadk.util.CertUtil;
import cfca.sadk.util.EnvelopeUtil;
import cfca.sadk.util.KeyUtil;
import cfca.sadk.x509.certificate.X509Cert;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.wosai.mpay.api.ztkx.ZTKXProtocolFields;
import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.exception.MpayException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.util.Base64;

public class ZTKXCryptoSignUtil {
    public static final Logger log = LoggerFactory.getLogger(ZTKXCryptoSignUtil.class);

    private static Session session;

    static {
        try {
            JCrypto.getInstance().initialize(JCrypto.JSOFT_LIB, null);
            session = JCrypto.getInstance().openSession(JCrypto.JSOFT_LIB);
        } catch (PKIException e) {
            throw new RuntimeException("中投科信支付环境启动失败", e);
        }
    }

    /**
     * 加密
     *
     * @param signContext 需要加密的报文
     * @return
     * @throws BuilderException
     */
    public static String encrypt(String publicKey, String signContext) throws MpayException {
        try {
            X509Cert cert = new X509Cert(Base64.getDecoder().decode(publicKey.getBytes()));
            X509Cert[] certs = {cert};
            return new String(SM2EnvelopeUtil.envelopeMessage(signContext.getBytes(StandardCharsets.UTF_8), "SM4/ECB/PKCS7Padding", certs, session));
        } catch (Exception e) {
            throw new MpayException("中投科信加密失败", e);
        }
    }

    /**
     * 解密
     *
     * @param encryptContext 需要解密的报文
     * @return
     * @throws BuilderException
     */
    public static String decrypt(String privateKey, String priKeyPWD, String encryptContext) throws MpayException {
        try {
            byte[] privateKeyBytes = Base64.getDecoder().decode(privateKey);
            PrivateKey priKey = KeyUtil.getPrivateKeyFromSM2(privateKeyBytes, priKeyPWD);
            X509Cert cert = CertUtil.getCertFromSM2(privateKeyBytes);
            byte[] sourceData = EnvelopeUtil.openEvelopedMessage(encryptContext.getBytes(StandardCharsets.UTF_8), priKey, cert, session);
            return new String(sourceData, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new MpayException("中投科信解密失败", e);
        }
    }

    /**
     * 验证签名
     *
     * @param msg 需要验证签名的明文
     * @return
     * @throws BuilderException
     */
    public static boolean signCheck(String publicKey, String msg) throws MpayException {
        JSONObject obj = JSONUtil.parseObj(msg, JSONConfig.create().setOrder(true).setStripTrailingZeros(false));
        String decryptContext = obj.getStr(ZTKXProtocolFields.MESSAGE);
        JSONObject messageObj = JSONUtil.parseObj(decryptContext, JSONConfig.create().setOrder(true).setStripTrailingZeros(false));
        String dataStr = messageObj.getStr(ZTKXProtocolFields.DATA);
        String signStr = messageObj.getStr(ZTKXProtocolFields.SIGN);

        try {
            PKCS7SignedData p7 = new PKCS7SignedData(session);
            p7.loadBase64(signStr.getBytes(StandardCharsets.UTF_8));
            X509Cert signerX509Cert = p7.getSignerX509Cert();
            X509Cert clientCer = new X509Cert(Base64.getDecoder().decode(publicKey));
            BigInteger serialNumber = signerX509Cert.getSerialNumber();
            String subject = signerX509Cert.getSubject();
            BigInteger serialNumberOfclient = clientCer.getSerialNumber();
            String subjectOfclient = clientCer.getSubject();
            if (serialNumber.compareTo(serialNumberOfclient) != 0) {
                log.error("序列号[" + serialNumber + "]与商户上传证书序列号[" + serialNumberOfclient + "]不符");
                throw new MpayException("证书序列号不匹配");
            }
            if (!subject.equals(subjectOfclient)) {
                log.error("证书DN[" + subject + "]与商户上传证书序列号DN[" + subjectOfclient + "]不一致{}");
                throw new MpayException("证书DN不匹配");
            }
            boolean isSignOK = new cfca.sadk.util.Signature().p7VerifyMessageDetach(dataStr.getBytes(StandardCharsets.UTF_8), signStr.getBytes(), session);
            if (!isSignOK) {
                throw new MpayException("验签失败");
            }
            return true;
        } catch (Exception e) {
            throw new MpayException("验签失败", e);
        }
    }

    public static String getSign(String privateKey, String pwd, String context) throws MpayException {
        try {
            SM2PrivateKey priKey = KeyUtil.getPrivateKeyFromSM2(Base64.getDecoder().decode(privateKey.getBytes()), pwd);
            X509Cert certFromSM2 = CertUtil.getCertFromSM2(Base64.getDecoder().decode(privateKey.getBytes()));
            return new String(new cfca.sadk.util.Signature().p7SignMessageDetach(Mechanism.SM3_SM2, context.getBytes(StandardCharsets.UTF_8), priKey, certFromSM2, session));
        } catch (Exception e) {
            throw new MpayException("中投科信签名失败", e);
        }
    }
}
