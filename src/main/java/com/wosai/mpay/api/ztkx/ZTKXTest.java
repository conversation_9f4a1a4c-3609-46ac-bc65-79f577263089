package com.wosai.mpay.api.ztkx;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import com.wosai.mpay.api.HttpResourceCallback;
import com.wosai.mpay.api.ztkx.enums.ZTKXTradeCodeEnum;
import com.wosai.mpay.api.ztkx.util.ZTKXHeadUtil;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Base64;

import java.util.HashMap;
import java.util.Map;

/**
 * 文档地址：https://docs.msfpay.com/docs/ztkx-cas/ztkx-cas-1el6m59r9rhum
 *
 * <AUTHOR>
 * @date 2025/2/26 22:13
 */
public class ZTKXTest {

    // 测试环境地址以及密钥信息
    public static final String URL = "http://test.umbpay.com.cn:12080/cashier/transV2/service.do";
    public static final String MERCHANT_ID = "CF3000053368";
    public static final String NORMAL_PROVIDER_MCH_ID = "CM3000067971";
    public static final String MERID = "CM3000058180";
    public static final String SQB_MERCHANT_SIGN_PRIVATE_KEY = "";
    public static final String SQB_MERCHANT_DEC_PRIVATE_KEY = "";
    public static final String PWD = "UMBPAY2022";
    public static final String ZTKX_VER_SIGN_PUBLIC_KEY = "";
    public static final String ZTKX_ENC_PUBLIC_KEY = "";

    public static final String SHARING_RECEIVER_CREATE_URL = "http://test.umbpay.com.cn:12080/cmsmeraccess/transV2/service.do";
    public static final String SHARING_URL = "http://test.umbpay.com.cn:12080/cashier/transV2/service.do";

    public static void main(String[] args) {
        try {
            //queryTradeCAS008();
//            payCAS016();
//            refundCAS006();
//            cancelCAS027();
//            getUnionPayUserIdCAS059();
//            memberQueryAsync();
//            sharing();
//            uploadFile();
//            normalWithdraw();
            queryNormalWithdraw();
//            profitWithdraw();
//            queryMemberWithdraw();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void uploadFile() throws MpayException, MpayApiNetworkError, InterruptedException {
        String url = "reasoning.png";
        byte[] bytes = FileUtil.readBytes("/Users/<USER>/Downloads/d0bc733c24b8dd20c5423226a2de0d9f906b2c.png");
        String encode = Base64.encode(bytes);

        AsyncZTKXClient asyncClient = new AsyncZTKXClient();
        asyncClient.uploadFile("http://test.umbpay.com.cn:12080/cmsmeraccess/public/imageUploadV2.do", encode, MERCHANT_ID,
                SQB_MERCHANT_SIGN_PRIVATE_KEY,
                PWD,
                SQB_MERCHANT_DEC_PRIVATE_KEY,
                PWD,
                ZTKX_ENC_PUBLIC_KEY,
                ZTKX_VER_SIGN_PUBLIC_KEY,
                HttpResourceCallback.create((result, t) -> System.out.println(result)));

    }
    //{data={"pictureid":"group48/M00/01/DA/rB4BDWhRKTCAYyR0AAKvEMtxnlw281.png","respMsg":"交易成功","platmerId":"CF3000053368","respCode":"C000000000"}, sign=}

    /**
     * 会员注册接口 CMS049
     * 申请注册会员接口 - 异步方式
     */
    private static void cmsmeraccessAsync() {
        // 构建请求体参数 - 以个人会员注册为例
        Map<String, Object> body = new HashMap<>();
        // 基本信息
        body.put(ZTKXBusinessFields.PHONE, "***********");                // 签约手机号
        body.put(ZTKXBusinessFields.CUST_NAME, "张三");                    // 会员名称
        body.put(ZTKXBusinessFields.USER_TYPE, "3");                      // 会员类型：3个人

        // 证件信息
        body.put(ZTKXBusinessFields.ID_FRONT_IMG_URL, "group48/M00/00/D6/rB4BDWUsn7qAG-sHAAFg0KOVRSg96..jpg"); // 身份证正面照片
        body.put(ZTKXBusinessFields.ID_BACK_IMG_URL, "group48/M00/00/D6/rB4BDWUsn7qAG-sHAAFg0KOVRSg96..jpg");  // 身份证反面照片
        body.put(ZTKXBusinessFields.CERT_TYPE, "0");                      // 证件类型：0 身份证
        body.put(ZTKXBusinessFields.CERT_NO, "110101199001011234");       // 证件号码
        body.put(ZTKXBusinessFields.SIGN_DATE, "20210101");               // 证件有效期开始 yyyyMMdd
        body.put(ZTKXBusinessFields.CERT_DATE, "20310101");               // 证件有效期截止 yyyyMMdd

        // 地址信息
        body.put(ZTKXBusinessFields.PRO_CODE, "110000");                  // 证件地址省编码
        body.put(ZTKXBusinessFields.CITY_CODE, "110100");                 // 证件地址市编码
        body.put(ZTKXBusinessFields.REGION_CODE, "110101");               // 证件地址区县编码
        body.put(ZTKXBusinessFields.ADDRESS, "北京市东城区某某街道1号");     // 证件地址详细地址

        // 银行卡信息
        body.put(ZTKXBusinessFields.DEF_CARD, "Y");                       // 是否默认卡
        body.put(ZTKXBusinessFields.BANK_CARD_TYPE, "01");                // 银行卡类型：01 借记卡
        body.put(ZTKXBusinessFields.BANK_CARD_NO, "****************");    // 银行卡号
        body.put(ZTKXBusinessFields.BANK_CARD_NAME, "张三");               // 银行卡户名
        body.put(ZTKXBusinessFields.BANK_PRE_PHONE, "***********");       // 预留手机号
        body.put(ZTKXBusinessFields.BANK_CARD_IMG_URL, "group48/M00/00/D6/rB4BDWUsn7qAG-sHAAFg0KOVRSg96..jpg"); // 银行卡照片

        // 可选信息
        body.put(ZTKXBusinessFields.OCCUPATION, "工程师");                 // 职业
        body.put(ZTKXBusinessFields.RESIDENT_P_CODE, "110000");           // 常驻地址省编码
        body.put(ZTKXBusinessFields.RESIDENT_C_CODE, "110100");           // 常驻地址市编码
        body.put(ZTKXBusinessFields.RESIDENT_D_CODE, "110101");           // 常驻地址区县编码
        body.put(ZTKXBusinessFields.RESIDENT_ADDRESS, "北京市东城区某某街道2号"); // 常驻地址详细地址
        body.put(ZTKXBusinessFields.SERVER_URL, "http://example.com/callback"); // 回调URL

        // 包装请求
        Map<String, Object> registerRequest = wrapperRequestMap(body, MERCHANT_ID, ZTKXTradeCodeEnum.CMS049.getCode());

        // 创建异步客户端
        AsyncZTKXClient asyncClient = new AsyncZTKXClient();

        try {
            // 发送异步请求
            asyncClient.asyncCall(
                    SHARING_RECEIVER_CREATE_URL,  // 使用会员接口URL
                    registerRequest,
                    SQB_MERCHANT_SIGN_PRIVATE_KEY,
                    PWD,
                    SQB_MERCHANT_DEC_PRIVATE_KEY,
                    PWD,
                    ZTKX_ENC_PUBLIC_KEY,
                    ZTKX_VER_SIGN_PUBLIC_KEY,
                    HttpResourceCallback.create((result, t) -> System.out.println(result))
            );
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    private static void memberQueryAsync() {
        // 构建请求体参数 - 以个人会员注册为例 US1000002278
        Map<String, Object> body = new HashMap<>();
        body.put(ZTKXBusinessFields.MEM_USER_ID, "US1000002278");

        Map<String, Object> registerRequest = wrapperRequestMap(body, MERCHANT_ID, ZTKXTradeCodeEnum.CMS050.getCode());

        // 创建异步客户端
        AsyncZTKXClient asyncClient = new AsyncZTKXClient();

        try {
            // 发送异步请求
            asyncClient.asyncCall(
                    SHARING_RECEIVER_CREATE_URL,  // 使用会员接口URL
                    registerRequest,
                    SQB_MERCHANT_SIGN_PRIVATE_KEY,
                    PWD,
                    SQB_MERCHANT_DEC_PRIVATE_KEY,
                    PWD,
                    ZTKX_ENC_PUBLIC_KEY,
                    ZTKX_VER_SIGN_PUBLIC_KEY,
                    HttpResourceCallback.create((result, t) -> System.out.println(result))
            );
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //返回结果：{message={data={head={mertrandate=20250616, mertrantime=154058, platmerid=CF3000053368, respcode=C000000000, respmsg=交易成功, trancode=CMS050, tranflow=CF300005336820250616154058059272, ztpage=false}, body={acctBal=0, memState=03, memUserId=US1000002278}}, sign=}}
    }


    private static void sharing() {
        // 构建请求体参数 - 以个人会员注册为例 US1000002278
        Map<String, Object> body = new HashMap<>();
        body.put(ZTKXBusinessFields.MERID, MERID);
        body.put(ZTKXBusinessFields.TRAN_AMT, "0.01");

        body.put(ZTKXBusinessFields.MEM_USER_ID, "US1000002278");

        Map<String, Object> registerRequest = wrapperRequestMap(body, MERCHANT_ID, ZTKXTradeCodeEnum.CAS048.getCode());

        // 创建异步客户端
        AsyncZTKXClient asyncClient = new AsyncZTKXClient();

        try {
            // 发送异步请求
            asyncClient.asyncCall(
                    SHARING_URL,  // 使用会员接口URL
                    registerRequest,
                    SQB_MERCHANT_SIGN_PRIVATE_KEY,
                    PWD,
                    SQB_MERCHANT_DEC_PRIVATE_KEY,
                    PWD,
                    ZTKX_ENC_PUBLIC_KEY,
                    ZTKX_VER_SIGN_PUBLIC_KEY,
                    HttpResourceCallback.create((result, t) -> System.out.println(result))
            );
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        //{message={data={head={mertrandate=20250616, mertrantime=170407, platmerid=CF3000053368, respcode=C000000000, respmsg=交易成功, trancode=CAS048, tranflow=CF300005336820250616170407489538, ztpage=false}, body={memUserId=US1000002278, merid=CM3000058180, state=00, tranamt=0.1}}, sign=}}
    }


    private static void sharingQuery() {
        Map<String, Object> body = new HashMap<>();
        body.put(ZTKXBusinessFields.MERID, MERID);
        body.put(ZTKXBusinessFields.ORI_TRAN_FLOW, "CF300005336820250616170407489538");

        Map<String, Object> registerRequest = wrapperRequestMap(body, MERCHANT_ID, ZTKXTradeCodeEnum.CAS049.getCode());

        // 创建异步客户端
        AsyncZTKXClient asyncClient = new AsyncZTKXClient();

        try {
            // 发送异步请求
            asyncClient.asyncCall(
                    SHARING_URL,  // 使用会员接口URL
                    registerRequest,
                    SQB_MERCHANT_SIGN_PRIVATE_KEY,
                    PWD,
                    SQB_MERCHANT_DEC_PRIVATE_KEY,
                    PWD,
                    ZTKX_ENC_PUBLIC_KEY,
                    ZTKX_VER_SIGN_PUBLIC_KEY,
                    HttpResourceCallback.create((result, t) -> System.out.println(result))
            );
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        //{message={data={head={mertrandate=20250616, mertrantime=172535, platmerid=CF3000053368, respcode=C000000000, respmsg=交易成功, trancode=CAS049, tranflow=CF300005336820250616172535717902, ztpage=false}, body={memUserId=US1000002278, merid=CM3000058180, oriRespCode=C000000000, oriRespMsg=交易成功, state=00, tranamt=0.1}}, sign=MIIEFQYKKoEcz1UGAQQCAqCCBAUwggQBAgEBMQ4wDAYIKoEcz1UBgxEFADAMBgoqgRzPVQYBBAIBoIIDAjCCAv4wggKioAMCAQICBUA5l4ADMAwGCCqBHM9VAYN1BQAwYTELMAkGA1UEBhMCQ04xMDAuBgNVBAoMJ0NoaW5hIEZpbmFuY2lhbCBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTEgMB4GA1UEAwwXQ0ZDQSBBQ1MgVEVTVCBTTTIgT0NBMzEwHhcNMjIwNjI0MDgxMTE4WhcNMjIxMjI0MDgwMDE4WjCBqTELMAkGA1UEBhMCQ04xHDAaBgNVBAoME2NmY2Egc20yIHRlc3Qgb2NhMzExDDAKBgNVBAsMA2NlejEZMBcGA1UECwwQT3JnYW5pemF0aW9uYWwtMjFTMFEGA1UEAwxKY2V6QOWMl+S6rOS4reaKleenkeS/oeeUteWtkOWVhuWKoeaciemZkOi0o+S7u+WFrOWPuEA0OTExMTAxMDgwNTkyNzY1ODlHQDIwWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAATj9LLt1jiDECdxhioKY6lEWB8TLefYrYs5SQgUit0qLJxMxz5gNVVa2mYnLZ7Gb8YbWCcrZvpEzQmE7t8zgj72o4H7MIH4MD8GCCsGAQUFBwEBBDMwMTAvBggrBgEFBQcwAYYjaHR0cDovL29jc3B0ZXN0LmNmY2EuY29tLmNuOjgwL29jc3AwHwYDVR0jBBgwFoAUBMe8+VkBaT6MNDYgYhg83ry1uwwwDAYDVR0TAQH/BAIwADA4BgNVHR8EMTAvMC2gK6AphidodHRwOi8vMjEwLjc0LjQyLjMvT0NBMzEvU00yL2NybDE3OS5jcmwwDgYDVR0PAQH/BAQDAgbAMB0GA1UdDgQWBBSi0AScnlnj9fE8yxjp4kJoPNJ21jAdBgNVHSUEFjAUBggrBgEFBQcDAgYIKwYBBQUHAwQwDAYIKoEcz1UBg3UFAANIADBFAiBrantlASDOV7LvdKj9PGl7vO/aFWYMl3cX7Mpx7HmEwwIhAP1Ux2GPf4YNjog0ww1zwcs9eszP/YJ2IeJDTsYZ8XldMYHXMIHUAgEBMGowYTELMAkGA1UEBhMCQ04xMDAuBgNVBAoMJ0NoaW5hIEZpbmFuY2lhbCBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTEgMB4GA1UEAwwXQ0ZDQSBBQ1MgVEVTVCBTTTIgT0NBMzECBUA5l4ADMAwGCCqBHM9VAYMRBQAwDQYJKoEcz1UBgi0BBQAERjBEAiALMWXCT0LMrH/cPG+ZTuhaOqpkkNZa6QYJphmithcLtAIgeh1pMyc/zhf5pIQijltXgZr090SCctstmnqGtJtXvgw=}}
    }

    /**
     * 普通商户提现
     *
     * @throws MpayException
     * @throws MpayApiNetworkError
     */
    private static void normalWithdraw() throws MpayException, MpayApiNetworkError {
        Map<String, Object> body = new HashMap<>();
        body.put(ZTKXBusinessFields.MERID, NORMAL_PROVIDER_MCH_ID);
        body.put(ZTKXBusinessFields.AMOUNT, "0.01");
        body.put(ZTKXBusinessFields.WITHDRAW_FEE_AMT, "0.01");
        body.put(ZTKXBusinessFields.REMARK, "备注");

        Map<String, Map<String, Object>> payRequest = wrapperRequest(body, MERCHANT_ID, ZTKXTradeCodeEnum.CAS010.getCode());
        ZTKXClient ztkxClient = new ZTKXClient();
        Map<String, Object> result = ztkxClient.call(
                URL,
                payRequest,
                SQB_MERCHANT_SIGN_PRIVATE_KEY,
                PWD,
                SQB_MERCHANT_DEC_PRIVATE_KEY,
                PWD,
                ZTKX_ENC_PUBLIC_KEY,
                ZTKX_VER_SIGN_PUBLIC_KEY);
        System.out.println("请求地址：" + URL + "请求："+ JSONUtil.toJsonStr(payRequest) + "返回结果："+ JSONUtil.toJsonStr(result));
    }

    /**
     * 查询普通商户提现结果
     *
     * @throws MpayException
     * @throws MpayApiNetworkError
     */
    private static void queryNormalWithdraw() throws MpayException, MpayApiNetworkError {
        Map<String, Object> body = new HashMap<>();
        body.put(ZTKXBusinessFields.ORI_TRAN_CODE, ZTKXTradeCodeEnum.CAS010.getCode());
        body.put(ZTKXBusinessFields.ORI_TRANFLOW, "CF300005336820250707134307169641");

        Map<String, Map<String, Object>> payRequest = wrapperRequest(body, MERCHANT_ID, ZTKXTradeCodeEnum.CAS008.getCode());
        ZTKXClient ztkxClient = new ZTKXClient();
        Map<String, Object> result = ztkxClient.call(
                URL,
                payRequest,
                SQB_MERCHANT_SIGN_PRIVATE_KEY,
                PWD,
                SQB_MERCHANT_DEC_PRIVATE_KEY,
                PWD,
                ZTKX_ENC_PUBLIC_KEY,
                ZTKX_VER_SIGN_PUBLIC_KEY);
        System.out.println("请求地址：" + URL + "请求："+ JSONUtil.toJsonStr(payRequest) + "返回结果："+ JSONUtil.toJsonStr(result));
    }

    private static void profitWithdraw() throws MpayException, MpayApiNetworkError {
        Map<String, Object> body = new HashMap<>();
        body.put(ZTKXBusinessFields.MEM_USER_ID, "US1000002285");
        body.put(ZTKXBusinessFields.TRAN_AMT, "0.01");
        //body.put(ZTKXBusinessFields.REMARK, "备注");
        //body.put(ZTKXBusinessFields.BANK_CARD_NO, "****************");

        Map<String, Map<String, Object>> payRequest = wrapperRequest(body, MERCHANT_ID, ZTKXTradeCodeEnum.CAS061.getCode());
        ZTKXClient ztkxClient = new ZTKXClient();
        Map<String, Object> result = ztkxClient.call(
                URL,
                payRequest,
                SQB_MERCHANT_SIGN_PRIVATE_KEY,
                PWD,
                SQB_MERCHANT_DEC_PRIVATE_KEY,
                PWD,
                ZTKX_ENC_PUBLIC_KEY,
                ZTKX_VER_SIGN_PUBLIC_KEY);
        System.out.println("请求地址：" + URL + "请求："+ JSONUtil.toJsonStr(payRequest) + "返回结果："+ JSONUtil.toJsonStr(result));
    }


    /**
     * 查询会员商户提现结果
     *
     * @throws MpayException
     * @throws MpayApiNetworkError
     */
    private static void queryMemberWithdraw() throws MpayException, MpayApiNetworkError {
        Map<String, Object> body = new HashMap<>();
        body.put(ZTKXBusinessFields.MERID, "US1000002285");
        body.put(ZTKXBusinessFields.TRAN_CODE, ZTKXTradeCodeEnum.CAS061.getCode());
        body.put(ZTKXBusinessFields.ORI_TRANFLOW, "CF300005336820250707185700688715");

        Map<String, Map<String, Object>> payRequest = wrapperRequest(body, MERCHANT_ID, ZTKXTradeCodeEnum.CAS049.getCode());
        ZTKXClient ztkxClient = new ZTKXClient();
        Map<String, Object> result = ztkxClient.call(
                URL,
                payRequest,
                SQB_MERCHANT_SIGN_PRIVATE_KEY,
                PWD,
                SQB_MERCHANT_DEC_PRIVATE_KEY,
                PWD,
                ZTKX_ENC_PUBLIC_KEY,
                ZTKX_VER_SIGN_PUBLIC_KEY);
        System.out.println("请求地址：" + URL + "请求："+ JSONUtil.toJsonStr(payRequest) + "返回结果："+ JSONUtil.toJsonStr(result));
    }

    private static void queryTradeCAS008() throws MpayException, MpayApiNetworkError {
        Map<String, Object> params = new HashMap<>();
        params.put(ZTKXBusinessFields.ORI_TRAN_CODE, ZTKXTradeCodeEnum.CAS016.getCode());
        params.put(ZTKXBusinessFields.ORI_TRAN_FLOW, "CF300005336820250313114233183975");
        Map<String, Map<String, Object>> queryRequest = wrapperRequest(params, MERCHANT_ID, ZTKXTradeCodeEnum.CAS008.getCode());
        ZTKXClient ztkxClient = new ZTKXClient();
        Map<String, Object> result = ztkxClient.call(
                URL,
                queryRequest,
                SQB_MERCHANT_SIGN_PRIVATE_KEY,
                PWD,
                SQB_MERCHANT_DEC_PRIVATE_KEY,
                PWD,
                ZTKX_ENC_PUBLIC_KEY,
                ZTKX_VER_SIGN_PUBLIC_KEY);
        System.out.println("请求地址：" + URL + "请求：" + JSONUtil.toJsonStr(queryRequest) + "返回结果：" + JSONUtil.toJsonStr(result));
    }

    private static void payCAS016() throws MpayException, MpayApiNetworkError {
        Map<String, Object> body = new HashMap<>();
        body.put(ZTKXBusinessFields.MERID, MERID);
        body.put(ZTKXBusinessFields.PAY_TYPE, "07");
        body.put(ZTKXBusinessFields.TRAN_AMT, "0.02");
        body.put(ZTKXBusinessFields.SERVER_URL, "http://test.umbpay.com.cn:12080/cashier/test/testnotify.do");
        body.put(ZTKXBusinessFields.PAT_FEE, "0.01");  // CF3000048536为10%的费率
        body.put(ZTKXBusinessFields.PROFIT_TYPE, "00");
        body.put(ZTKXBusinessFields.PROFIT_LIST, "{'profitlist':[{'merid':'CM3000058180','proportion':'1'}]}");
        body.put(ZTKXBusinessFields.PRODUCT_LIST, "{'productlist':[{'productname':'牙刷','productnum':'1','productamt':'0.01'},{'productname':'电脑','productnum':'1','productamt':'0.01'}]}");
        body.put(ZTKXBusinessFields.SUB_APPID, "2016121304229688");
        body.put(ZTKXBusinessFields.OPENID, "2088002463095113");
        body.put(ZTKXBusinessFields.AUTH_CODE, "");
        body.put(ZTKXBusinessFields.REMARK, "备注");
        body.put(ZTKXBusinessFields.SPBILL_CREATE_IP, "127.0.0.1");

        Map<String, Map<String, Object>> payRequest = wrapperRequest(body, MERCHANT_ID, ZTKXTradeCodeEnum.CAS016.getCode());
        ZTKXClient ztkxClient = new ZTKXClient();
        Map<String, Object> result = ztkxClient.call(
                URL,
                payRequest,
                SQB_MERCHANT_SIGN_PRIVATE_KEY,
                PWD,
                SQB_MERCHANT_DEC_PRIVATE_KEY,
                PWD,
                ZTKX_ENC_PUBLIC_KEY,
                ZTKX_VER_SIGN_PUBLIC_KEY);
        System.out.println("请求地址：" + URL + "请求：" + JSONUtil.toJsonStr(payRequest) + "返回结果：" + JSONUtil.toJsonStr(result));
    }


    private static void refundCAS006() throws MpayException, MpayApiNetworkError {
        Map<String, Object> body = new HashMap<>();
        body.put(ZTKXBusinessFields.MERID, MERID);
        body.put(ZTKXBusinessFields.REFUND_AMT, "0.02");
        body.put(ZTKXBusinessFields.ORI_TRAN_FLOW, "CF300005336820250313114233183975");
        body.put(ZTKXBusinessFields.REFUND_LIST, "{'refundlist':[{'merid':'CF3000048536','refundamt':'0.01'}]}");
        body.put(ZTKXBusinessFields.SERVER_URL, "http://test.umbpay.com.cn:12080/cashier/test/testnotify.do");
        body.put(ZTKXBusinessFields.REMARK, "备注");
        Map<String, Map<String, Object>> refundRequest = wrapperRequest(body, MERCHANT_ID, ZTKXTradeCodeEnum.CAS006.getCode());
        ZTKXClient ztkxClient = new ZTKXClient();
        Map<String, Object> result = ztkxClient.call(
                URL,
                refundRequest,
                SQB_MERCHANT_SIGN_PRIVATE_KEY,
                PWD,
                SQB_MERCHANT_DEC_PRIVATE_KEY,
                PWD,
                ZTKX_ENC_PUBLIC_KEY,
                ZTKX_VER_SIGN_PUBLIC_KEY);
        System.out.println("请求地址：" + URL + "请求：" + JSONUtil.toJsonStr(refundRequest) + "返回结果：" + JSONUtil.toJsonStr(result));
    }

    private static void cancelCAS027() throws MpayException, MpayApiNetworkError {
        Map<String, Object> body = new HashMap<>();
        body.put(ZTKXBusinessFields.MERID, MERID);
        body.put(ZTKXBusinessFields.ORI_TRAN_FLOW, "CF300005336820250313143022895400");
        Map<String, Map<String, Object>> cancelRequest = wrapperRequest(body, MERCHANT_ID, ZTKXTradeCodeEnum.CAS027.getCode());
        ZTKXClient ztkxClient = new ZTKXClient();
        Map<String, Object> result = ztkxClient.call(
                URL,
                cancelRequest,
                SQB_MERCHANT_SIGN_PRIVATE_KEY,
                PWD,
                SQB_MERCHANT_DEC_PRIVATE_KEY,
                PWD,
                ZTKX_ENC_PUBLIC_KEY,
                ZTKX_VER_SIGN_PUBLIC_KEY);
        System.out.println("请求地址：" + URL + "请求：" + JSONUtil.toJsonStr(cancelRequest) + "返回结果：" + JSONUtil.toJsonStr(result));
    }

    private static void getUnionPayUserIdCAS059() throws MpayException, MpayApiNetworkError {
        Map<String, Object> body = new HashMap<>();
        body.put(ZTKXBusinessFields.USER_AUTH_CODE, "0e00f0d62006004f1lmy3cVS");
        body.put(ZTKXBusinessFields.APP_UP_IDENTIFIER, "UnionPay/1.0 CloudPay");
        Map<String, Map<String, Object>> request = wrapperRequest(body, MERCHANT_ID, ZTKXTradeCodeEnum.CAS059.getCode());

        ZTKXClient ztkxClient = new ZTKXClient();
        Map<String, Object> result = ztkxClient.call(
                URL,
                request,
                SQB_MERCHANT_SIGN_PRIVATE_KEY,
                PWD,
                SQB_MERCHANT_DEC_PRIVATE_KEY,
                PWD,
                ZTKX_ENC_PUBLIC_KEY,
                ZTKX_VER_SIGN_PUBLIC_KEY);
        System.out.println("请求地址： " + URL + "请求：" + JSONUtil.toJsonStr(request) + "返回结果：" + JSONUtil.toJsonStr(result));
    }


    private static Map<String, Map<String, Object>> wrapperRequest(Map<String, Object> params, String ztkxMchId, String tradeCode) {
        Map<String, Object> head = new HashMap<>();
        head.put(ZTKXCommonFields.MERTRANDATE, ZTKXHeadUtil.getDate());
        head.put(ZTKXCommonFields.MERTRANTIME, ZTKXHeadUtil.getTime());
        head.put(ZTKXCommonFields.PLATMERID, ztkxMchId); //商户号
        head.put(ZTKXCommonFields.RESPCODE, "");
        head.put(ZTKXCommonFields.RESPMSG, "");
        head.put(ZTKXCommonFields.TRANCODE, tradeCode); //交易码
        head.put(ZTKXCommonFields.TRANFLOW, ZTKXHeadUtil.getTranFlow(ztkxMchId));
        head.put(ZTKXCommonFields.ZTPAGE, Boolean.FALSE); //是否跳转页面

        Map<String, Map<String, Object>> data = new HashMap<>();
        data.put(ZTKXProtocolFields.HEAD, head);
        data.put(ZTKXProtocolFields.BODY, params);
        return data;
    }

    private static Map<String, Object> wrapperRequestMap(Map<String, Object> params, String ztkxMchId, String tradeCode) {
        Map<String, Object> head = new HashMap<>();
        head.put(ZTKXCommonFields.MERTRANDATE, ZTKXHeadUtil.getDate());
        head.put(ZTKXCommonFields.MERTRANTIME, ZTKXHeadUtil.getTime());
        head.put(ZTKXCommonFields.PLATMERID, ztkxMchId); //商户号
        head.put(ZTKXCommonFields.RESPCODE, "");
        head.put(ZTKXCommonFields.RESPMSG, "");
        head.put(ZTKXCommonFields.TRANCODE, tradeCode); //交易码
        head.put(ZTKXCommonFields.TRANFLOW, ztkxMchId + "7894000000002");
        head.put(ZTKXCommonFields.ZTPAGE, Boolean.FALSE); //是否跳转页面

        Map<String, Object> data = new HashMap<>();
        data.put(ZTKXProtocolFields.HEAD, head);
        data.put(ZTKXProtocolFields.BODY, params);
        return data;
    }
}

