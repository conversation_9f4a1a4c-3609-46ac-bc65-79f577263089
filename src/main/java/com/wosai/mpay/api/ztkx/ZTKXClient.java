package com.wosai.mpay.api.ztkx;

import com.wosai.mpay.api.ztkx.util.ZTKXCryptoSignUtil;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 中投科信支付接口客户端
 * 文档地址：https://docs.msfpay.com/docs/ztkx-cas/ztkx-cas-1el6m59r9rhum
 *
 * <AUTHOR>
 * @date 2025/2/26 13:43
 */
public class ZTKXClient {
    public static final Logger log = LoggerFactory.getLogger(ZTKXClient.class);

    private int connectTimeout = 10000;
    private int readTimeout = 30000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    /**
     * @param requestUrl           请求地址
     * @param params               请求参数
     * @param sqbSignPrivateKey    收钱吧签名私钥
     * @param sqbSignPrivateKeyPwd 收钱吧签名私钥密码
     * @param sqbDecPrivateKey     收钱吧解密私钥
     * @param sqbDecPrivateKeyPwd  收钱吧解密私钥密码
     * @param ztkxEncPublicKey     中投科信加密公钥
     * @param ztkxVerSignPublicKey 中投科信验签公钥
     * @return
     * @throws MpayException
     * @throws MpayApiNetworkError
     */
    public Map<String, Object> call(String requestUrl, Map<String, Map<String, Object>> params,
                                    String sqbSignPrivateKey, String sqbSignPrivateKeyPwd,
                                    String sqbDecPrivateKey, String sqbDecPrivateKeyPwd,
                                    String ztkxEncPublicKey, String ztkxVerSignPublicKey)
            throws MpayException, MpayApiNetworkError {
        Map<String, Object> resMsg = new HashMap<>();
        try {
            // 转换为签名字符串
            String signStr = JsonUtil.objectToJsonString(params);
            // 生成签名
            String sign = ZTKXCryptoSignUtil.getSign(sqbSignPrivateKey, sqbSignPrivateKeyPwd, signStr);
            // 转换为明文
            String reqPlainText = convertToPlainText(sign, signStr);
            // 加密数据
            String encData = ZTKXCryptoSignUtil.encrypt(ztkxEncPublicKey, reqPlainText);
            // 生成协议请求
            Map<String, String> reqMsg = convertToReq(encData);
            // 发送请求
            String encResMsg = WebUtils.doPost(null, null, requestUrl, reqMsg, connectTimeout, readTimeout);
            // 解密响应数据
            String decResMsg = ZTKXCryptoSignUtil.decrypt(sqbDecPrivateKey, sqbDecPrivateKeyPwd, encResMsg);
            // 验签
            ZTKXCryptoSignUtil.signCheck(ztkxVerSignPublicKey, decResMsg);
            // 构建返回结果
            resMsg = JsonUtil.jsonStringToObject(decResMsg, Map.class);
            return resMsg;
        } finally {
            log.info("中投科信接口请求地址: {}, 请求参数: {}, 返回结果: {}", requestUrl, JsonUtil.objectToJsonString(params), JsonUtil.objectToJsonString(resMsg));
        }
    }


    /**
     * 转换为协议请求参数
     *
     * @param encData
     * @return
     */
    private static Map<String, String> convertToReq(String encData) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put(ZTKXProtocolFields.FORMAT_TYPE, ZTKXProtocolFields.JSON);
        paramMap.put(ZTKXProtocolFields.CRYPT_TYPE, ZTKXProtocolFields.SM2);
        paramMap.put(ZTKXProtocolFields.SIGN_TYPE, ZTKXProtocolFields.SM2);
        paramMap.put(ZTKXProtocolFields.DATA, encData);
        return paramMap;
    }

    /**
     * 转换为明文
     *
     * @param signValue
     * @param requestStr
     * @return
     * @throws MpayException
     */
    private static String convertToPlainText(String signValue, String requestStr) throws MpayException {
        Map<String, String> message = new HashMap<>();
        message.put(ZTKXProtocolFields.SIGN, signValue);
        message.put(ZTKXProtocolFields.DATA, requestStr);
        Map<String, Map<String, String>> messageMap = new HashMap<>();
        messageMap.put(ZTKXProtocolFields.MESSAGE, message);
        return JsonUtil.objectToJsonString(messageMap);
    }
}
