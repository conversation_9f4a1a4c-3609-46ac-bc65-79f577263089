package com.wosai.mpay.api.ztkx.enums;

/**
 * 中投科信证件类型枚举
 * <AUTHOR>
 * @date 2025/2/26 22:35
 */
public enum ZTKXCertTypeEnum {
    ID_CARD("0", "身份证"),
    PASSPORT("2", "护照"),
    HK_MACAO_PASS("5", "港澳居民来往内地通行证(回乡证)"),
    TAIWAN_PASS("6", "台湾同胞来往内地通行证(台胞证)");

    private final String code;
    private final String desc;

    ZTKXCertTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ZTKXCertTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ZTKXCertTypeEnum type : ZTKXCertTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
