package com.wosai.mpay.api.ztkx.enums;


/**
 * 中投科信订单状态枚举
 */
public enum ZTKXRefundStatusEnum {
    PROCESSING("01", "商户申请成功"),
    SUCCESS("40", "退款成功"),
    FAILED("41", "退款失败");

    private final String code;
    private final String description;

    ZTKXRefundStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据code获取对应的枚举值
    public static ZTKXRefundStatusEnum getByCode(String code) {
        if (null == code) {
            return PROCESSING;
        }
        for (ZTKXRefundStatusEnum status : ZTKXRefundStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return PROCESSING;
    }

    // 成功
    public static boolean isSuccess(String status) {
        if (null == status) {
            return false;
        }
        if (status.equals(ZTKXRefundStatusEnum.SUCCESS.getCode())) {
            return true;
        }
        return false;
    }

    // 失败
    public static boolean isFailed(String status) {
        if (null == status) {
            return false;
        }
        if (status.equals(ZTKXRefundStatusEnum.FAILED.getCode())) {
            return true;
        }
        return false;
    }

    // 处理中
    public static boolean isProcessing(String status) {
        if (null == status) {
            return false;
        }
        if (status.equals(ZTKXRefundStatusEnum.PROCESSING.getCode())) {
            return true;
        }
        return false;
    }
}


