package com.wosai.mpay.api.ztkx.enums;

/**
 * 中投科信企业证件类型枚举
 * <AUTHOR>
 * @date 2025/2/26 22:40
 */
public enum ZTKXCustCertTypeEnum {
    BUSINESS_LICENSE("101", "营业执照"),
    REGISTRATION_CERTIFICATE("105", "登记证书");

    private final String code;
    private final String desc;

    ZTKXCustCertTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ZTKXCustCertTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ZTKXCustCertTypeEnum type : ZTKXCustCertTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
