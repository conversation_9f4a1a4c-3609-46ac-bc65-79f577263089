package com.wosai.mpay.api.ztkx;

import com.wosai.mpay.api.HttpResourceCallback;
import com.wosai.mpay.api.ztkx.enums.ZTKXTradeCodeEnum;
import com.wosai.mpay.api.ztkx.util.ZTKXCryptoSignUtil;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.AsyncClientUtil;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.misc.BASE64Decoder;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 中投科信支付接口异步客户端
 * 文档地址：https://docs.msfpay.com/docs/ztkx-cas/ztkx-cas-1el6m59r9rhum
 *
 * <AUTHOR>
 * @date 2025/2/26 13:43
 */
public class AsyncZTKXClient {
    public static final Logger log = LoggerFactory.getLogger(AsyncZTKXClient.class);

    SafeSimpleDateFormat dateFormat = new SafeSimpleDateFormat("yyyyMMddHHmmss");

    private int connectTimeout = 2000;
    private int readTimeout = 5000;
    private CloseableHttpAsyncClient httpClient;
    private RequestConfig requestConfig;

    public AsyncZTKXClient() {
        initClient();
    }


    public void initClient() {
        requestConfig = RequestConfig.custom().setConnectionRequestTimeout(connectTimeout).setConnectTimeout(connectTimeout).setSocketTimeout(readTimeout).build();
        httpClient = AsyncClientUtil.getCloseableHttpAsyncClient(null, null);
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    /**
     * 异步调用中投科信接口
     *
     * @param requestUrl           请求地址
     * @param params               请求参数
     * @param sqbSignPrivateKey    收钱吧签名私钥
     * @param sqbSignPrivateKeyPwd 收钱吧签名私钥密码
     * @param sqbDecPrivateKey     收钱吧解密私钥
     * @param sqbDecPrivateKeyPwd  收钱吧解密私钥密码
     * @param ztkxEncPublicKey     中投科信加密公钥
     * @param ztkxVerSignPublicKey 中投科信验签公钥
     * @param callback             回调函数
     */
    public void asyncCall(String requestUrl, Map<String, Object> params,
                          String sqbSignPrivateKey, String sqbSignPrivateKeyPwd,
                          String sqbDecPrivateKey, String sqbDecPrivateKeyPwd,
                          String ztkxEncPublicKey, String ztkxVerSignPublicKey,
                          HttpResourceCallback<Map> callback) {
        try {
            // 转换为签名字符串
            String signStr = JsonUtil.objectToJsonString(params);
            // 生成签名
            String sign = ZTKXCryptoSignUtil.getSign(sqbSignPrivateKey, sqbSignPrivateKeyPwd, signStr);
            // 转换为明文
            String reqPlainText = convertToPlainText(sign, signStr);
            // 加密数据
            String encData = ZTKXCryptoSignUtil.encrypt(ztkxEncPublicKey, reqPlainText);
            // 生成协议请求
            Map<String, String> reqMsg = convertToReq(encData);

            // 创建POST请求
            HttpPost httpPost = new HttpPost(requestUrl);
            List<NameValuePair> nvps = new ArrayList<>();
            for (Map.Entry<String, String> entry : reqMsg.entrySet()) {
                nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }
            httpPost.setEntity(new UrlEncodedFormEntity(nvps, StandardCharsets.UTF_8));
            httpPost.setConfig(requestConfig);
            // 异步执行请求
            httpClient.execute(httpPost, new FutureCallback<HttpResponse>() {
                @Override
                public void completed(HttpResponse response) {
                    String responseJson = "";
                    try {
                        HttpEntity entity = response.getEntity();
                        String encResMsg = EntityUtils.toString(entity, StandardCharsets.UTF_8);

                        // 解密响应数据
                        String decResMsg = ZTKXCryptoSignUtil.decrypt(sqbDecPrivateKey, sqbDecPrivateKeyPwd, encResMsg);
                        responseJson = decResMsg;
                        // 验签
                        ZTKXCryptoSignUtil.signCheck(ztkxVerSignPublicKey, decResMsg);
                        // 构建返回结果
                        Map<String, Object> resMsg = JsonUtil.jsonStringToObject(decResMsg, Map.class);
                        callback.onComplete(resMsg);
                    } catch (Exception e) {
                        log.error("request completed exception", e);
                        callback.onError(e);
                    } finally {
                        log.info("request url：{}, params：{},response: {}", requestUrl, JsonUtil.toJsonStr(params), responseJson);
                    }
                }

                @Override
                public void failed(Exception e) {
                    log.error("Request failed", e);
                    callback.onError(e);
                }

                @Override
                public void cancelled() {
                    log.warn("Request cancelled");
                    callback.onError(new MpayApiNetworkError("Request cancelled"));
                }
            });
        } catch (Exception e) {
            log.error("Request exception", e);
            callback.onError(e);
        }
    }

    /**
     * 同步调用中投科信接口
     *
     * @param requestUrl           请求地址
     * @param sqbSignPrivateKey    收钱吧签名私钥
     * @param sqbSignPrivateKeyPwd 收钱吧签名私钥密码
     * @param sqbDecPrivateKey     收钱吧解密私钥
     * @param sqbDecPrivateKeyPwd  收钱吧解密私钥密码
     * @param ztkxEncPublicKey     中投科信加密公钥
     * @param ztkxVerSignPublicKey 中投科信验签公钥
     * @return 响应结果
     * @throws MpayException
     * @throws MpayApiNetworkError
     */
    public void uploadFile(String requestUrl, String fileBase64, String platformMercId,
                           String sqbSignPrivateKey, String sqbSignPrivateKeyPwd,
                           String sqbDecPrivateKey, String sqbDecPrivateKeyPwd,
                           String ztkxEncPublicKey, String ztkxVerSignPublicKey,
                           HttpResourceCallback<Map> callback)
            throws MpayException, MpayApiNetworkError, InterruptedException {

        if (!checkImg(fileBase64)) {
            throw new UnsupportedOperationException("不支持的文件格式");
        }
        Map<String, String> paramMap = new HashMap<String, String>();
        paramMap.put(ZTKXBusinessFields.IMG, fileBase64.substring(fileBase64.length() - 50));
        paramMap.put(ZTKXProtocolFields.PLAT_MER_ID, platformMercId);
        paramMap.put(ZTKXProtocolFields.TRAN_CODE, ZTKXTradeCodeEnum.CMS003.getCode());
        paramMap.put(ZTKXBusinessFields.TRAN_TIME, dateFormat.format(new Date())); //时间
        String dataJson = JsonUtil.objectToJsonString(paramMap);

        String sign = ZTKXCryptoSignUtil.getSign(sqbSignPrivateKey, sqbSignPrivateKeyPwd, dataJson);
        Map<String, String> message = new HashMap<String, String>();
        message.put(ZTKXProtocolFields.SIGN, sign);
        message.put(ZTKXProtocolFields.DATA, dataJson);

        String messageStr = JsonUtil.objectToJsonString(message);
        String encData = ZTKXCryptoSignUtil.encrypt(ztkxEncPublicKey, messageStr);

        Map<String, String> params = new HashMap<>();
        params.put(ZTKXProtocolFields.FORMAT_TYPE, ZTKXProtocolFields.JSON);
        params.put(ZTKXProtocolFields.CRYPT_TYPE, ZTKXProtocolFields.SM2);
        params.put(ZTKXProtocolFields.SIGN_TYPE, ZTKXProtocolFields.SM2);
        params.put(ZTKXProtocolFields.DATA, encData);
        params.put(ZTKXBusinessFields.IMGS, fileBase64);


        // 创建POST请求
        HttpPost httpPost = new HttpPost(requestUrl);
        List<NameValuePair> nvps = new ArrayList<>();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
        }
        httpPost.setEntity(new UrlEncodedFormEntity(nvps, StandardCharsets.UTF_8));
        httpPost.setConfig(requestConfig);
        // 异步执行请求
        httpClient.execute(httpPost, new FutureCallback<HttpResponse>() {
            @Override
            public void completed(HttpResponse response) {
                try {
                    HttpEntity entity = response.getEntity();
                    String encResMsg = EntityUtils.toString(entity, StandardCharsets.UTF_8);

                    // 解密响应数据
                    String decResMsg = ZTKXCryptoSignUtil.decrypt(sqbDecPrivateKey, sqbDecPrivateKeyPwd, encResMsg);
                    // 构建返回结果
                    Map<String, Object> resMsg = JsonUtil.jsonStringToObject(decResMsg, Map.class);
                    callback.onComplete(resMsg);
                } catch (Exception e) {
                    log.error("request completed exception", e);
                    callback.onError(e);
                } finally {
                    log.info("request url：{}，params：{}", requestUrl, JsonUtil.toJsonStr(params));
                }
            }

            @Override
            public void failed(Exception e) {
                log.error("Request failed", e);
                callback.onError(e);
            }

            @Override
            public void cancelled() {
                log.warn("Request cancelled");
                callback.onError(new MpayApiNetworkError("Request cancelled"));
            }
        });

    }

    private static boolean checkImg(String base64Str) {
        boolean flag = false;
        try {
            byte[] b = new BASE64Decoder().decodeBuffer(base64Str);
            BufferedImage bufImg = ImageIO.read(new ByteArrayInputStream(b));
            if (null == bufImg) {
                return flag;
            }
            flag = true;
        } catch (Exception e) {
            log.error("checkImg fail", e);
        }
        return flag;
    }

    /**
     * 转换为协议请求参数
     *
     * @param encData 加密数据
     * @return 请求参数
     */
    private static Map<String, String> convertToReq(String encData) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put(ZTKXProtocolFields.FORMAT_TYPE, ZTKXProtocolFields.JSON);
        paramMap.put(ZTKXProtocolFields.CRYPT_TYPE, ZTKXProtocolFields.SM2);
        paramMap.put(ZTKXProtocolFields.SIGN_TYPE, ZTKXProtocolFields.SM2);
        paramMap.put(ZTKXProtocolFields.DATA, encData);
        return paramMap;
    }

    /**
     * 转换为明文
     *
     * @param signValue  签名值
     * @param requestStr 请求字符串
     * @return 明文
     * @throws MpayException
     */
    private static String convertToPlainText(String signValue, String requestStr) throws MpayException {
        Map<String, String> message = new HashMap<>();
        message.put(ZTKXProtocolFields.SIGN, signValue);
        message.put(ZTKXProtocolFields.DATA, requestStr);
        Map<String, Map<String, String>> messageMap = new HashMap<>();
        messageMap.put(ZTKXProtocolFields.MESSAGE, message);
        return JsonUtil.objectToJsonString(messageMap);
    }

    /**
     * 关闭HTTP客户端
     */
    public void close() {
        if (httpClient != null) {
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error("关闭HTTP客户端异常", e);
            }
        }
    }
}
