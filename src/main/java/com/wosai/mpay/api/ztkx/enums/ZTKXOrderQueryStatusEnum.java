package com.wosai.mpay.api.ztkx.enums;


/**
 * 中投科信订单状态枚举
 */
public enum ZTKXOrderQueryStatusEnum {
    SUCCESS("01", "支付成功"),
    PROCESSING("02", "交易处理中"),
    FAILED("03", "交易失败");

    private final String status;
    private final String desc;

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    ZTKXOrderQueryStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static ZTKXOrderQueryStatusEnum getByCode(String code) {
        if (null == code) {
            return PROCESSING;
        }
        for (ZTKXOrderQueryStatusEnum status : ZTKXOrderQueryStatusEnum.values()) {
            if (status.getStatus().equals(code)) {
                return status;
            }
        }
        return PROCESSING;
    }

    // 成功
    public static boolean isSuccess(String status) {
        if (null == status) {
            return false;
        }
        if (status.equals(ZTKXOrderQueryStatusEnum.SUCCESS.status)) {
            return true;
        }
        return false;
    }
    // 失败
    public static boolean isFailed(String status) {
        if (null == status) {
            return false;
        }
        if (status.equals(ZTKXOrderQueryStatusEnum.FAILED.status)) {
            return true;
        }
        return false;
    }

    // 处理中
    public static boolean isProcessing(String status) {
        if (null == status) {
            return false;
        }
        if (status.equals(ZTKXOrderQueryStatusEnum.PROCESSING.status)) {
            return true;
        }
        return false;
    }
}


