package com.wosai.mpay.api.ztkx.enums;

/**
 * <AUTHOR>
 * @description 中投科信查询会员提现-状态码
 * @date 2025-07-05
 */
public enum ZTKXQueryMemberWithdrawStateEnum {
    SUCCESS("00", "成功"),
    FAILED("01", "失败"),
    PROCESSING("02", "处理中");

    private final String code;
    private final String desc;

    ZTKXQueryMemberWithdrawStateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ZTKXQueryMemberWithdrawStateEnum of(String code) {
        if (null == code) {
            return PROCESSING;
        }
        for (ZTKXQueryMemberWithdrawStateEnum status : ZTKXQueryMemberWithdrawStateEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return PROCESSING;
    }
}
