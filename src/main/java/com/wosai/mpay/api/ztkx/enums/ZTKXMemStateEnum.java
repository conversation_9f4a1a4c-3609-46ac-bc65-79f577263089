package com.wosai.mpay.api.ztkx.enums;

/**
 * 中投科信会员签约状态枚举
 * <AUTHOR>
 * @date 2025/2/26 22:50
 */
public enum ZTKXMemStateEnum {
    NORMAL("00", "正常"),
    CANCELLED("01", "注销"),
    FROZEN("02", "冻结"),
    INITIAL("03", "初始"),
    LOCKED("04", "锁定");

    private final String code;
    private final String desc;

    ZTKXMemStateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ZTKXMemStateEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ZTKXMemStateEnum state : ZTKXMemStateEnum.values()) {
            if (state.getCode().equals(code)) {
                return state;
            }
        }
        return null;
    }
}
