package com.wosai.mpay.api.ztkx.util;

import cfca.sadk.algorithm.common.CBCParam;
import cfca.sadk.algorithm.common.Mechanism;
import cfca.sadk.algorithm.common.PKCSObjectIdentifiers;
import cfca.sadk.algorithm.common.PKIException;
import cfca.sadk.asn1.parser.ASN1Parser;
import cfca.sadk.lib.crypto.Session;
import cfca.sadk.lib.crypto.jni.JNISoftLib;
import cfca.sadk.org.bouncycastle.asn1.*;
import cfca.sadk.org.bouncycastle.asn1.cms.*;
import cfca.sadk.org.bouncycastle.asn1.x500.X500Name;
import cfca.sadk.org.bouncycastle.asn1.x509.AlgorithmIdentifier;
import cfca.sadk.org.bouncycastle.cms.CMSEnvelopedData;
import cfca.sadk.util.Base64;
import cfca.sadk.x509.certificate.X509Cert;

import java.math.BigInteger;

public class SM2EnvelopeUtil {
    private static byte[] IV_16 = new byte[]{50, 51, 52, 53, 54, 55, 56, 57, 56, 55, 54, 53, 52, 51, 50, 49};

    public SM2EnvelopeUtil() {
    }

    public static final byte[] envelopeMessage(byte[] sourceData, String symmetricAlgorithm, X509Cert[] receiverCerts, Session session) throws Exception {
        byte[] none64 = envelopMessage_None64(sourceData, symmetricAlgorithm, receiverCerts, session);
        return Base64.encode(none64);
    }

    private static byte[] envelopMessage_None64(byte[] sourceData, String symmetricAlgorithm, X509Cert[] receiverCerts, Session session) throws Exception {
        byte[] key = SM2AndItsCloseSymAlgUtil.generateSecretKey();
        IV_16 = SM2AndItsCloseSymAlgUtil.generateIV();
        ASN1EncodableVector recipientInfos = new ASN1EncodableVector();

        for(int i = 0; i < receiverCerts.length; ++i) {
            recipientInfos.add(toRecipientInfoOfIssuerAndSerialNumber(receiverCerts[i], key, session));
        }

        Mechanism contentEncryptionAlg;
        if (symmetricAlgorithm.indexOf("CBC") != -1) {
            CBCParam cbc = new CBCParam(IV_16);
            contentEncryptionAlg = new Mechanism(symmetricAlgorithm, cbc);
        } else {
            contentEncryptionAlg = new Mechanism(symmetricAlgorithm);
        }

        boolean useJNI = false;
        if (session != null && session instanceof JNISoftLib) {
            useJNI = true;
        }

        byte[] encryptedData = SM2AndItsCloseSymAlgUtil.crypto(useJNI, true, key, sourceData, contentEncryptionAlg);
        ASN1OctetString encryptedOctet = new BEROctetString(encryptedData);
        ASN1ObjectIdentifier tOID = (ASN1ObjectIdentifier)PKCS7EnvelopedData.MECH_OID.get(symmetricAlgorithm);
        AlgorithmIdentifier algId = getAlgorithmIdentifier(contentEncryptionAlg, tOID);
        ASN1Encodable parameters = algId.getParameters();
        EncryptedContentInfo encryptedContentInfo = new EncryptedContentInfo(PKCSObjectIdentifiers.sm2Data, algId, encryptedOctet);
        EnvelopedData envData = new EnvelopedData((OriginatorInfo)null, new DERSet(recipientInfos), encryptedContentInfo, ASN1Set.getInstance((Object)null));
        ContentInfo contentInfo = new ContentInfo(PKCSObjectIdentifiers.sm2EnvelopedData, envData);
        return ASN1Parser.parseDERObj2Bytes((new CMSEnvelopedData(contentInfo)).toASN1Structure());
    }

    private static AlgorithmIdentifier getAlgorithmIdentifier(Mechanism contentEncryptionAlg, ASN1ObjectIdentifier tOID) throws PKIException {
        AlgorithmIdentifier algorithmIdentifier = new AlgorithmIdentifier(tOID);
        if (contentEncryptionAlg.getMechanismType().toUpperCase().indexOf("CBC") != -1) {
            Object param = contentEncryptionAlg.getParam();
            if (param == null) {
                throw new PKIException(PKIException.NULL_P7_ENVELOP_CBC_ERR, PKIException.NULL_P7_ENVELOP_CBC_ERR_DES);
            } else {
                CBCParam cbcParam = (CBCParam)contentEncryptionAlg.getParam();
                DEROctetString doct = new DEROctetString(cbcParam.getIv());
                algorithmIdentifier = new AlgorithmIdentifier(tOID, doct);
                return algorithmIdentifier;
            }
        } else {
            return algorithmIdentifier;
        }
    }

    private static RecipientInfo toRecipientInfoOfIssuerAndSerialNumber(X509Cert cert, byte[] symmetricKey, Session session) throws Exception {

        byte[] encryptedKey = null;
        if (session != null && session instanceof JNISoftLib) {
            encryptedKey = SM2AndItsCloseSymAlgUtil.sm2EncryptByJNI(true, cert.getPublicKey(), symmetricKey);
        } else {
            encryptedKey = SM2AndItsCloseSymAlgUtil.sm2Encrypt(true, cert.getPublicKey(), symmetricKey);
        }

        ASN1OctetString encKey = new DEROctetString(encryptedKey);
        X500Name recipientIssuer = cert.getIssuerX500Name();
        BigInteger recipientSN = cert.getSerialNumber();
        IssuerAndSerialNumber issu = new IssuerAndSerialNumber(recipientIssuer, recipientSN);
        AlgorithmIdentifier keyEncAlg = new AlgorithmIdentifier(PKCSObjectIdentifiers.SM2_pubKey_encrypt, DERNull.INSTANCE);
        KeyTransRecipientInfo ktr = new KeyTransRecipientInfo(RecipientIdentifier.getInstance(issu), keyEncAlg, encKey);
        return new RecipientInfo(ktr);
    }
}