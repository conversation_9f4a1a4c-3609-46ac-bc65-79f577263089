package com.wosai.mpay.api.ztkx.enums;

/**
 * <AUTHOR>
 * @description 中投科信查询普通商户提现-状态码
 * @date 2025-07-05
 */
public enum ZTKXQueryNormalWithdrawStateEnum {
    SUCCESS("01", "交易成功"),
    PROCESSING("02", "交易处理中"),
    FAILED("03", "交易失败");

    private final String code;
    private final String desc;

    ZTKXQueryNormalWithdrawStateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ZTKXQueryNormalWithdrawStateEnum of(String code) {
        if (null == code) {
            return PROCESSING;
        }
        for (ZTKXQueryNormalWithdrawStateEnum status : ZTKXQueryNormalWithdrawStateEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return PROCESSING;
    }
}
