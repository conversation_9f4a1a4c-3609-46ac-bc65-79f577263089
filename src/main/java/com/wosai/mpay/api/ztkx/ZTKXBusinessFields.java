package com.wosai.mpay.api.ztkx;

/**
 * 中投科信业务字段
 *
 * <AUTHOR>
 * @date 2025/2/26 21:26
 */
public class ZTKXBusinessFields {
    /**
     * CAS016-条码支付业务字段
     */
    // 中投商户号
    public static final String MERID = "merid";

    // 支付方式常量定义
    public static final String PAY_TYPE = "paytype";

    // 交易金额
    public static final String TRAN_AMT = "tranamt";

    // 服务器回调地址
    public static final String SERVER_URL = "serverurl";

    // 平台手续费
    public static final String PAT_FEE = "patfee";

    // 分账类型
    public static final String PROFIT_TYPE = "profittype";

    // 分账信息
    public static final String PROFIT_LIST = "profitlist";

    // 商品信息
    public static final String PRODUCT_LIST = "productlist";

    // 商品名称
    public static final String PRODUCT_NAME = "productname";

    // 商品数量
    public static final String PRODUCT_NUM = "productnum";

    // 商品金额
    public static final String PRODUCT_AMT = "productamt";

    // Appid
    public static final String SUB_APPID = "subappid";

    // openid
    public static final String OPENID = "openid";

    // openId
    public static final String OPEN_ID = "openId";

    // 授权码
    public static final String AUTH_CODE = "authcode";

    // 备注
    public static final String REMARK = "remark";

    // 终端IP
    public static final String SPBILL_CREATE_IP = "spbillcreateip";

    // 设备信息
    public static final String DEVICE_INFO = "deviceinfo";

    // 指定支付方式
    public static final String LIMIT_PAY = "limitpay";

    // 订单超时时间
    public static final String ORDER_EXPIRE_TIME = "orderexpiretime";

    // 扩展字段
    public static final String EXTEND_FIELD = "extendfield";

    // 银行卡类型
    public static final String BANK_CARD_TYPE = "bankcardtype";

    // 订单优惠标识
    public static final String GOODSTAG = "goodstag";

    // 商品详情
    public static final String GOODS_DETAIL = "goodsdetail";

    // 花呗分期笔数
    public static final String INSTALLMENT_NUM = "installmentNum";

    // 商户手续费承担比例
    public static final String FEE_PERCENT = "feePercent";

    // 手续费补贴费率明细
    public static final String FEE_SUBSIDY_RULE_LIST = "feeSubsidyRuleList";

    // 终端信息
    public static final String TERMINAL_INFO = "terminalInfo";

    // 设备ID
    public static final String DEVICE_ID = "deviceId";

    // 设备IP
    public static final String DEVICE_IP = "deviceIp";

    // 前端成功通知地址
    public static final String FRONT_URL = "frontUrl";

    // 前端失败通知地址
    public static final String FRONT_FAIL_URL = "frontFailUrl";

    // 项目编号
    public static final String PRNO = "prno";

    // 语音播报控制
    public static final String VOICE_CONTROL = "voiceControl";

    // 平台补贴手续费
    public static final String PAT_SUBSIDY_FEE = "patsubsidyfee";

    // 支付信息
    public static final String PAY_INFO = "payinfo";

    // 手续费补贴总金额
    public static final String FEE_SUBSIDY_AMT_TOTAL = "feeSubsidyAmtTotal";

    // 手续费补贴明细
    public static final String FEE_SUBSIDY_DETAIL = "feeSubsidyDetail";

    /**
     * CAS008 交易状态查询业务字段
     */
    // 原交易交易码
    public static final String ORI_TRAN_CODE = "oritrancode";

    // 原交易订单号
    public static final String ORI_TRAN_FLOW = "oritranflow";

    // 查询选项
    public static final String QUERY_OPTION = "queryOption";

    // 原交易订单号
    public static final String ORI_TRANFLOW = "oritranflow";

    // 原交易状态
    public static final String ORI_TRANSTATUS = "oritranstatus";

    // 原响应码
    public static final String ORI_RESPCODE = "orirespcode";

    // 原响应信息
    public static final String ORI_RESPMSG = "orirespmsg";

    // 交易完成时间
    public static final String FINISHTIME = "finishtime";

    // 支付银行类型
    public static final String BANK_TYPE = "banktype";

    // 渠道订单号
    public static final String CHL_ORDER_ID = "chlOrderId";

    // 渠道完成时间
    public static final String CHL_FINISHTIME = "chlFinishTime";

    // 有效结算金额
    public static final String SETTLEA_MT = "settleAmt";

    // 优惠金额
    public static final String COUPON_AMT = "couponAmt";

    // 手续费
    public static final String FEE_AMT = "feeAmt";

    // 原交易交易码
    public static final String ORI_TRANCODE = "oritrancode";

    // 优惠详情
    public static final String PROMOTION_DETAIL = "promotionDetail";

    /**
     * CAS006 退款业务字段
     */
    // 退款金额
    public static final String REFUND_AMT = "refundamt";

    // 退款明细
    public static final String REFUND_LIST = "refundlist";

    // 退款是否支持现金退款
    public static final String ISSUPPORTCASH = "issupportcash";

    // 退款金额
    public static final String REFUNDAMT = "refundamt";

    // 退款状态，01：商户申请成功 40：退款成功 41：退款失败
    public static final String REFUND_STATUS = "status";

    /**
     * CAS059 银联userid查询
     */
    // 授权码
    public static final String USER_AUTH_CODE = "userAuthCode";

    // 银联支付标识
    public static final String APP_UP_IDENTIFIER = "appUpIdentifier";


    /**
     * CMS049-申请注册会员业务字段
     */
    // 签约手机号
    public static final String PHONE = "phone";
    // 会员名称
    public static final String CUST_NAME = "custname";
    // 会员类型
    public static final String USER_TYPE = "usertype";
    // 证件类型
    public static final String CUST_CERT_TYPE = "custcerttype";
    // 证件号码
    public static final String CUST_CERT_NO = "custcertno";
    // 证件照片
    public static final String BUS_PHOTO_URL = "busphotourl";
    // 经营地址省编码
    public static final String BUS_PRO_CODE = "busprocode";
    // 经营地址市编码
    public static final String BUS_CITY_CODE = "buscitycode";
    // 经营地址区县编码
    public static final String BUS_REGION_CODE = "busregioncode";
    // 经营地址详细地址
    public static final String BUS_ADD = "busadd";
    // 法人/经营者姓名
    public static final String NAME = "name";
    // 法人/经营者身份证正面照片
    public static final String ID_FRONT_IMG_URL = "idfrontimgurl";
    // 法人/经营者身份证反面照片
    public static final String ID_BACK_IMG_URL = "idbackimgurl";
    // 法人/经营者证件类型
    public static final String CERT_TYPE = "certtype";
    // 法人/经营者证件号码
    public static final String CERT_NO = "certno";
    // 法人/经营者证件有效期开始
    public static final String SIGN_DATE = "signdate";
    // 法人/经营者证件有效期截止
    public static final String CERT_DATE = "certdate";
    // 法人/经营者证件地址省编码
    public static final String PRO_CODE = "procode";
    // 法人/经营者证件地址市编码
    public static final String CITY_CODE = "citycode";
    // 法人/经营者证件地址区县编码
    public static final String REGION_CODE = "regioncode";
    // 法人/经营者证件地址详细地址
    public static final String ADDRESS = "address";
    // 是否默认卡
    public static final String DEF_CARD = "defcard";
    // 银行卡号
    public static final String BANK_CARD_NO = "bankcardno";
    // 银行卡户名
    public static final String BANK_CARD_NAME = "bankcardname";
    // 银行编码
    public static final String BANK_CODE = "bankcode";
    // 银行名称
    public static final String BANK_NAME = "bankname";
    // 预留手机号
    public static final String BANK_PRE_PHONE = "bankprephone";
    // 开户省编码
    public static final String OPEN_PRO_CODE = "openprocode";
    // 开户市编码
    public static final String OPEN_CITY_CODE = "opencitycode";
    // 银行账户开户证明文件/银行卡照片
    public static final String BANK_CARD_IMG_URL = "bankcardimgurl";
    // 开户声明文件
    public static final String DECLARE_IMG_URL = "declareimgurl";
    // 开户意愿视频
    public static final String WISH_VDO_URL = "wishvdourl";
    // 职业
    public static final String OCCUPATION = "occupation";
    // 常驻地址省编码
    public static final String RESIDENT_P_CODE = "residentPCode";
    // 常驻地址市编码
    public static final String RESIDENT_C_CODE = "residentCCode";
    // 常驻地址区县编码
    public static final String RESIDENT_D_CODE = "residentDCode";
    // 常驻地址详细地址
    public static final String RESIDENT_ADDRESS = "residentaddress";

    /**
     * CMS049-申请注册会员响应字段
     */
    // 会员号
    public static final String MEM_USER_ID = "memUserId";
    // 会员签约状态
    public static final String MEM_STATE = "memState";
    // 签约地址URL
    public static final String LINK_URL = "linkUrl";

    public static final String IMG = "img";
    public static final String TRAN_TIME = "trantime";
    public static final String IMGS = "imgs";

    //图片id
    public static final String PICTURE_ID = "pictureid";
    //上传图片返回状态码
    public static final String RESP_CODE = "respCode";

    //分账状态
    public static final String STATE = "state";

    //原交易码 用于分账 CAS048 分账 CAS060 分账回退 CAS061 会员提现
    public static final String TRAN_CODE = "trancode";

    /**
     * 提现相关字段
     */
    public static final String AMOUNT = "amount"; //结算金额
    public static final String CARD_NO = "cardNo";// 到账银行卡号
    public static final String RECEIVEAMT = "receiveamt";//实际到账金额
    public static final String WITHDRAW_FEE_AMT = "feeamt";//结算手续费
    public static final String BALANCE = "balance";//可提现余额
    public static final String TRANSITFUNDS = "transitfunds";//当日在途资金
}
