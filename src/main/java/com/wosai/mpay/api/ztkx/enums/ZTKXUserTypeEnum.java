package com.wosai.mpay.api.ztkx.enums;

/**
 * 中投科信会员类型枚举
 * <AUTHOR>
 * @date 2025/2/26 22:30
 */
public enum ZTKXUserTypeEnum {
    INDIVIDUAL_BUSINESS("1", "个体户"),
    ENTERPRISE("2", "企业"),
    PERSONAL("3", "个人");

    private final String code;
    private final String desc;

    ZTKXUserTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ZTKXUserTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ZTKXUserTypeEnum type : ZTKXUserTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
