package com.wosai.mpay.api.ztkx.util;

import cfca.sadk.algorithm.common.PKCSObjectIdentifiers;
import cfca.sadk.org.bouncycastle.asn1.ASN1ObjectIdentifier;

import java.util.Hashtable;

/**
 * <AUTHOR>
 * @date 2025/3/29 00:22
 */
public class PKCS7EnvelopedData {
    public static Hashtable MECH_OID = new Hashtable();
    public static Hashtable OID_MECH = new Hashtable();

    public PKCS7EnvelopedData() {
    }

    static {
        MECH_OID.put("DESede/CBC/PKCS7Padding", PKCSObjectIdentifiers.des3CBCEncryption);
        MECH_OID.put("DESede/ECB/PKCS7Padding", PKCSObjectIdentifiers.des3Encryption);
        MECH_OID.put("RSA/ECB/PKCS1PADDING", PKCSObjectIdentifiers.rsaEncryption);
        MECH_OID.put("AES/ECB/PKCS7Padding", new ASN1ObjectIdentifier("2.16.840.*********.1.1"));
        MECH_OID.put("AES/CBC/PKCS7Padding", new ASN1ObjectIdentifier("2.16.840.*********.1.2"));
        MECH_OID.put("RC4", PKCSObjectIdentifiers.rc4Encryption);
        MECH_OID.put("PBEWithMD5AndDES", PKCSObjectIdentifiers.pbeWithMD5AndDES_CBC);
        MECH_OID.put("PBEWithSHA1AndDES", PKCSObjectIdentifiers.pbeWithSHA1AndDES_CBC);
        MECH_OID.put("PBEWITHSHAAND2-KEYTRIPLEDES-CBC", PKCSObjectIdentifiers.pbeWithSHAAnd2DESCBC);
        MECH_OID.put("PBEWITHSHAAND3-KEYTRIPLEDES-CBC", PKCSObjectIdentifiers.pbeWithSHAAnd3DESCBC);
        MECH_OID.put("SM4/CBC/PKCS7Padding", PKCSObjectIdentifiers.SM4_CBC);
        MECH_OID.put("SM4/ECB/PKCS7Padding", PKCSObjectIdentifiers.SM4_ECB);
        OID_MECH.put(PKCSObjectIdentifiers.des3CBCEncryption, "DESede/CBC/PKCS7Padding");
        OID_MECH.put(PKCSObjectIdentifiers.des3Encryption, "DESede/ECB/PKCS7Padding");
        OID_MECH.put(PKCSObjectIdentifiers.rsaEncryption, "RSA/ECB/PKCS1PADDING");
        OID_MECH.put(PKCSObjectIdentifiers.rc4Encryption, "RC4");
        OID_MECH.put(PKCSObjectIdentifiers.pbeWithMD5AndDES_CBC, "PBEWithMD5AndDES");
        OID_MECH.put(PKCSObjectIdentifiers.pbeWithSHA1AndDES_CBC, "PBEWithSHA1AndDES");
        OID_MECH.put(PKCSObjectIdentifiers.pbeWithSHAAnd2DESCBC, "PBEWITHSHAAND2-KEYTRIPLEDES-CBC");
        OID_MECH.put(PKCSObjectIdentifiers.pbeWithSHAAnd3DESCBC, "PBEWITHSHAAND3-KEYTRIPLEDES-CBC");
        OID_MECH.put(PKCSObjectIdentifiers.SM4_CBC, "SM4/CBC/PKCS7Padding");
        OID_MECH.put(PKCSObjectIdentifiers.SM4_ECB, "SM4/ECB/PKCS7Padding");
    }
}
