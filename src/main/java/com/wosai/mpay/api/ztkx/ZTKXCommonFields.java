package com.wosai.mpay.api.ztkx;

/**
 * 中投科信业务字段
 *
 * <AUTHOR>
 * @date 2025/2/26 21:26
 */
public class ZTKXCommonFields {
    // 商户号
    public static final String PLATMERID = "platmerid";

    // 交易流水（交易订单号）
    public static final String TRANFLOW = "tranflow";

    // 交易编码
    public static final String TRANCODE = "trancode";

    // 客户端交易日期，YYYYMMDD格式的交易日期
    public static final String MERTRANDATE = "mertrandate";

    // 客户端交易时间，HHmmss格式的交易时间
    public static final String MERTRANTIME = "mertrantime";

    // 是否页面跳转，true:跳转页面; false:不跳转; 不输入默认为false
    public static final String ZTPAGE = "ztpage";

    // 交易返回码
    public static final String RESPCODE = "respcode";

    // 交易返回信息
    public static final String RESPMSG = "respmsg";
}
