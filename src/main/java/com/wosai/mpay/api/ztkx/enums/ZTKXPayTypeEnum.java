package com.wosai.mpay.api.ztkx.enums;


/**
 * 中投科信交易类型枚举
 */
public enum ZTKXPayTypeEnum {
    WECHAT_PUBLIC_MINI("05", "微信公众号/小程序"),
    ALIPAY_SCAN("06", "支付宝正扫"),
    ALIPAY_SERVICE_MINI("07", "支付宝服务窗/小程序"),
    WECHAT_SWIPE("08", "微信刷卡(付款码)"),
    ALIPAY_SWIPE("09", "支付宝刷卡(付款码)"),
    UNIONPAY_SCAN("12", "银联正扫"),
    UNIONPAY_REVERSE_SCAN("13", "银联反扫"),
    UNIONPAY_APP_PAYMENT("14", "银联APP支付"),
    FLOWERPAY_SCAN("15", "花呗分期正扫"),
    FLOWERPAY_REVERSE_SCAN("16", "花呗分期反扫"),
    UNIONPAY_JS_PAYMENT("22", "银联js支付"),
    CLOUD_MICRO_MINI_PAYMENT("24", "云微小程序支付");

    private final String code;
    private final String description;

    ZTKXPayTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}


