package com.wosai.mpay.api.ztkx;

/**
 * <AUTHOR>
 * @date 2025/2/26 22:06
 */
public class ZTKXProtocolFields {
    // 数据格式类型，此处为json
    public static final String FORMAT_TYPE = "formatType";
    // 加密类型，此处为sm2;
    public static final String CRYPT_TYPE = "cryptType";
    // 签名类型，此处为sm2
    public static final String SIGN_TYPE = "signType";
    // 请求头信息
    public static final String HEAD = "head";
    // 请求体信息
    public static final String BODY = "body";
    // 签名
    public static final String SIGN = "sign";
    // 数据内容
    public static final String DATA = "data";

    public static final String MESSAGE = "message";

    public static final String SM2 = "sm2";

    public static final String JSON = "json";


    // 响应码
    public static final String RESP_CODE = "respcode";
    // 响应消息
    public static final String RESP_MSG = "respmsg";
    // 商户交易日期
    public static final String MER_TRAN_DATE = "mertrandate";
    // 商户交易时间
    public static final String MER_TRAN_TIME = "mertrantime";
    // 平台商户号
    public static final String PLAT_MER_ID = "platmerid";
    // 交易码
    public static final String TRAN_CODE = "trancode";
    // 交易流水号
    public static final String TRAN_FLOW = "tranflow";
    // 中投页面标识
    public static final String ZT_PAGE = "ztpage";
}
