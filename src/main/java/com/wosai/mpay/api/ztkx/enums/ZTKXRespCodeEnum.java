package com.wosai.mpay.api.ztkx.enums;


/**
 * 中投科信响应码枚举
 */
public enum ZTKXRespCodeEnum {
    SUCCESS("C000000000", "成功"),
    PROCESSING("W000000000", "处理中");

    private final String code;
    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    ZTKXRespCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean isSuccess(String code) {
        if (null == code) {
            return false;
        }
        if (code.equals(ZTKXRespCodeEnum.SUCCESS.code)) {
            return true;
        }
        return false;
    }

    public static boolean isProcessing(String code) {
        if (null == code) {
            return false;
        }
        if (code.equals(ZTKXRespCodeEnum.PROCESSING.code)) {
            return true;
        }
        return false;
    }


    public static boolean isFailed(String code) {
        if (null == code) {
            return true;
        }
        if (code.equals(ZTKXRespCodeEnum.SUCCESS.code) || code.equals(ZTKXRespCodeEnum.PROCESSING.code)) {
            return false;
        }
        return true;
    }

}


