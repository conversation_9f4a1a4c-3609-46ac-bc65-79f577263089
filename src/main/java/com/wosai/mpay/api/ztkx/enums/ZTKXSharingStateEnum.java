package com.wosai.mpay.api.ztkx.enums;

/**
 * 中投科信分账状态枚举
 */
public enum ZTKXSharingStateEnum {
    SUCCESS("00", "成功"),
    FAILED("01", "失败"),
    PROCESSING("02", "处理中");

    private final String code;
    private final String description;

    ZTKXSharingStateEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据code获取对应的枚举值
    public static ZTKXSharingStateEnum getByCode(String code) {
        if (null == code) {
            return PROCESSING;
        }
        for (ZTKXSharingStateEnum status : ZTKXSharingStateEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return PROCESSING;
    }

    // 成功
    public static boolean isSuccess(String status) {
        if (null == status) {
            return false;
        }
        return status.equals(ZTKXSharingStateEnum.SUCCESS.getCode());
    }

    // 失败
    public static boolean isFailed(String status) {
        if (null == status) {
            return false;
        }
        return status.equals(ZTKXSharingStateEnum.FAILED.getCode());
    }

    // 处理中
    public static boolean isProcessing(String status) {
        if (null == status) {
            return false;
        }
        return status.equals(ZTKXSharingStateEnum.PROCESSING.getCode());
    }
}
