package com.wosai.mpay.api;

import java.util.HashSet;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/29.
 *
 */
public class SSLEnvFlag {

    public static final String ENV_MPAY_SDK_TURN_OFF_SSL = "mpay.sdk.turnOffSSL";
    public static final String ENV_MPAY_SDK_NOT_VERIFY_HOST_NAMES = "mpay.sdk.notVerifyHostNames";

    private static Boolean turnOffSSl = null;
    private static Set<String> notVerifyHostNames = null;

    public static final boolean turnOffSSl(){
        if(turnOffSSl == null){
            synchronized (SSLEnvFlag.class){
                if(turnOffSSl == null){
                    turnOffSSl = Boolean.valueOf(System.getProperty(ENV_MPAY_SDK_TURN_OFF_SSL, "false"));
                }
            }
        }
        return turnOffSSl;
    }


    public static Set<String> getNotVerifyHostNames(){
        if(notVerifyHostNames == null){
            synchronized (SSLEnvFlag.class){
                if(notVerifyHostNames == null){
                    String value = System.getProperty(ENV_MPAY_SDK_NOT_VERIFY_HOST_NAMES);
                    if(value == null || "".equals(value)){
                        notVerifyHostNames = new HashSet<String>();
                    }else{
                        String [] hostNames = value.split(",");
                        Set<String> temp = new HashSet<String>();
                        for (int i = 0; i < hostNames.length; i++) {
                            temp.add(hostNames[i]);
                        }
                        notVerifyHostNames = temp;
                    }
                }
            }
        }
        return notVerifyHostNames;
    }
}
