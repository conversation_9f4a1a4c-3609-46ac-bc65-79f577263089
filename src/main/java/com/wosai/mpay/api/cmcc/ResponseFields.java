package com.wosai.mpay.api.cmcc;

/**
 * Created by l<PERSON><PERSON><PERSON> on 17/12/8.
 */
public class ResponseFields {

    public static final String MERCHANT_ID = "merchantId";//商户编号 我方平台给商户分配的唯一标识
    public static final String REQUEST_ID = "requestId";//商户请求号 商户请求的交易流水号唯一
    public static final String SIGN_TYPE = "signType";//签名方式 只能是 MD5，RSA
    public static final String TYPE = "type";//签名方式 只能是 MD5，RSA
    public static final String VERSION = "version"; //版本号

    public static final String RETURN_CODE = "returnCode";//返回码 000000-成功。其他 信息提示码，提示各类相 关失败信息
    public static final String MESSAGE = "message"; //返回码信息提示

    public static final String SERVER_CERT = "serverCert";//服务器证书公钥 signType=RSA，此项必 输
    public static final String HMAC = "hmac";//签名数据 获得 hmac 的方法见签名 算法,参数顺序按照表格中从上到下的顺序,但不 包括本参数.

    public static final String ORDER_ID = "orderId";//商户系统订单号
    public static final String AMOUNT = "amount";//订单金额，以分为单位， 如 1 元表示为 100
    public static final String COUP_AMT = "coupAmt";//电子券消费金额 以分为单位，如1元表示为100
    public static final String VCH_AMT = "vchAmt";//代金券消费金额 以分为单位，如1元表示为100
    public static final String CASH_AMT = "cashAmt";//现金消费金额 以分为单位，如1元表示为100
    public static final String COUPON_AMT = "couponAmt";//优惠券金额 以元为单位
    public static final String POI_BON_AMT = "poiBonAmt";//积分消费金额 以元为单位
    public static final String RESERVED_1 = "reserved1"; //保留字段 1 交易返回时原样返回给商家网站，给商户备用
    public static final String RESERVED_2 = "reserved2"; //保留字段 2 交易返回时原样返回给商家网站，给商户备用

    public static final String PAY_NO = "payNo"; //手机平台返回的交易流水号
    public static final String AMT_ITEM = "amtItem";//支付金额明细.内容如下:CNY_AMT=xx# CMY_AMT=xx# RED_AMT=xx# VCH_AMT=xx# POT_CHG_AMT=xx 其中,xx 表示金额数字,分 为单位,#表示分割符。 CNY_AMT 表示现金金额 CMY_AMT 表示充值卡金 额
    //RED_AMT 表示红包金额 VCH_AMT 表示代金券金 额POT_CHG_AMT 表 示 积 分金额
    public static final String BANK_ABBR = "bankAbbr";//支付银行 用户使用哪个银行进行支 付的。详细信息请见后面 的银行代码对照表
    public static final String MOBILE = "mobile";//支付手机号 手机前三位+后四位，网关 支付时为空。
    public static final String PAY_DATE = "payDate";//支付时间 用户完成支付的时间 YYYYMMDDHHmmSS

    public static final String STATUS = "status";//支付结果 SUCCESS 表示支付成功 OVERDUE 表示交易过期 WFPAYMENT 表示等待支付 CLOSED 表示交易关闭 P-REFUND 表示部分退款 REFUND 表示退款成功 CANCLE 表示交易取消
    public static final String ORDER_DATE = "orderDate";//订单提交日期 商户发起请求的时间; 年年年年月月日日
    public static final String FEE = "fee";//费用 单位为分

    public static final String RESUTLE = "resutle";//商户处理结果 SUCCESS 代表成功
    public static final String MERCHANT_NAME = "merchantName";//商户简称
    public static final String MBL_NO = "mblNo";//用户手机号
    public static final String ORDER_STATUS = "ordSts";//交易状态
    public static final String CASH_AMOUNT = "cashAmount";//现金金额
    public static final String BON_AMOUNT = "bonAmount";//电子券金额
    public static final String SPL_AMOUNT = "splAmount";//银行卡补款 金额
    public static final String VCH_AMOUNT = "vchAmount";//代金券金额
    public static final String MER_OPR_NO = "merOprNo";//操作员编号
    public static final String STORE_ID = "storeId";//门店编号
    public static final String POS_ID = "posId";//终端编号


}
