package com.wosai.mpay.api.cmcc;

/**
 * Created by l<PERSON><PERSON><PERSON> on 17/12/8.
 */
public class ProtocolFields {

    public static final String CHARACTER_SET = "characterSet";//字符集

    public static final String SIGN_SP_ID = "1309312501";

    public static final String CHARSET_UTF8_INT = "1";//字符集的取值;1 UTF-8固定为1

    public static final String CHARSET_UTF8_STRING = "UTF-8";
    public static final String CHARSET_UTF8_TYPE = "02";

    public static final String CHARSET_GB2312_STRING = "GB2312";

    public static final String PAY_VER = "2.0.1";//版本号取值,目前是应为 2.0
    public static final String OTHER_VER = "2.0.0";//版本号取值,目前是应为 2.0
    public static final String SERVICE_VERSION_1_1 = "1.1";//接口版本号,退款的时候填1.1
    public static final String FEE_TYPE_RMB = "00";//现金支付币种，目前只支持人民币。1：人民币
    public static final String FEE_TYPE_CARD = "01";//现金支付币种，目前只支持人民币。1：人民币
    public static final String DATE_FORMAT = "yyyyMMdd";
    public static final String DATE_TIME_FORMAT = "yyyyMMddmmHHss";

    public static final String PERIOD_NUM = "5";
    public static final String PERIOD_UNIT = "00";


    public static final String FLAG_TRUE_STATUS = "0";//是否支持 0- 支持(默认) 1- 不支持
    public static final String FLAG_FALSE_STATUS = "0";//是否支持 0- 支持(默认) 1- 不支持

    /**
     * sign type
     **/
    public static final String SIGN_TYPE_MD5 = "MD5";
    public static final String SIGN_TYPE_RSA    = "RSA";
}
