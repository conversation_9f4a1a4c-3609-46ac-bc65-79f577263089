package com.wosai.mpay.api.cmcc;

import java.util.Arrays;
import java.util.List;

/**
 * Created by lih<PERSON><PERSON> on 17/12/8.
 */
public class CMCCWalletConstants {

    /**
     * 通讯/交易 结果状态
     **/
    public static final String RET_CODE_SUCCESS = "000000";//返回状态码，000000表示成功，其他未定义；返回非000000状态码时，请参考错误码类型。


    public static final String RET_CODE_NOT_ENOUGH_MONEY_IN_BANK_CARD_1 = "PWM11025";//您的银行卡余额不足    .合并相关银行卡或账户余额不足的报错，此类报错均为生成订单后，用户银行卡或账户余额不足导致交易失败。
    public static final String RET_CODE_NOT_ENOUGH_MONEY_IN_BANK_CARD_2 = "CPS01132";//账户余额不足，支付失败
    public static final String RET_CODE_NOT_ENOUGH_MONEY_IN_BANK_CARD_3 = "PDB00229";//银行卡余额不足
    public static final String RET_CODE_NOT_ENOUGH_MONEY_IN_BANK_CARD_4 = "PWM15013";//对不起，您的银行卡余额不足
    public static final String RET_CODE_NOT_ENOUGH = "PDB00228";//电子券余额不足，非实名用户无法使用账户余额支付 用户未实名，请完成实名后交易。（建议直接绑定银行卡）
    public static final String RET_CODE_MONEY_LIMIT = "PCC00225"; //条码支付额度超限
    public static final String RET_CODE_PAY_RISK = "PDB00226"; //交易存在风险，操作失败
    public static final String RET_CODE_BANK_CARD_EXPIRE_1 = "PWM18016";//快捷签约状态异常，需请解约后重试
    public static final String RET_CODE_BANK_CARD_EXPIRE_2 = "PWM11108";//您的银行卡状态异常，详询发卡银行
    public static final String RET_CODE_BANK_CARD_EXPIRE_3 = "PWM13082";//对不起，银行卡签约异常，请解约后重新签约
    public static final String RET_CODE_DATA_COM_ERROR = "SCM60002";//数据通讯超时，请稍后再试
    public static final String RET_CODE_SYSTEM_ERROR = "CPS02003";//对不起，系统开小差了，请稍后再试
    public static final String RET_CODE_INVALID_AUTH_CODE = "URM20851";//条码已失效
    public static final String RET_CODE_PAY_CODE_INVALID = "URM20856";//用户无有效条码
    public static final String RET_CODE_PAY_CODE_OUT = "URM20852";//条码已过期
    public static final String RET_CODE_PAY_CODE_ERROR = "URM20848";//该条码信息不存在
    public static final String RET_CODE_ILLEGAL_RELATIONSHIP = "CPS08000";//该商户没有开通此类交易权限
    public static final String RET_CODE_ORDER_IN_PROGRESS = "CPS01134";//订单支付中:云POS支付过程中,收银台返回支付不成功


    public static final String ORDER_STATUS_SUCCESS = "支付完成";
    public static final String ORDER_STATUS_PAY_AWAIT = "等待付款";
    public static final String ORDER_STATUS_CLOSE = "订单关闭";
    public static final String ORDER_STATUS_CANCEL_SUCCESS = "冲正成功";
    public static final String ORDER_STATUS_REFUND_SUCCESS = "退款成功";
    public static final String ORDER_STATUS_PRO_REFUND_SUCCESS = "部分退款成功";


    public static final String ORDER_STATUS_SUCCESS_QUERY = "SUCCESS";  //表示支付成功
    public static final String ORDER_STATUS_PAY_OVERDUE = "OVERDUE";  //表示交易过期
    public static final String ORDER_STATUS_WFPAYMENT = "WFPAYMENT";   //表示等待支付
    public static final String ORDER_STATUS_CLOSED = "CLOSED";     //表示交易关闭
    public static final String ORDER_STATUS_P_REFUND = "P-REFUND";  //表示部分退款
    public static final String ORDER_STATUS_REFUND = "REFUND";    //表示退款成功
    public static final String ORDER_STATUS_CANCLE = "CANCLE";    //表示交易取消


    public static final String SUCCESS = "SUCCESS";
    public static final String FAILED = "FAILED";

    public static final String REFUND_ORDER_NOT_EXISTS = "RPM22201";  //记录不存在，请重试
    public static final String CANCEL_ORDER_BEGIN_1 = "RPM35901";  //冲正已受理
    public static final String CANCEL_ORDER_BEGIN_2 = "CPT00090";   //冲正已受理
    public static final String QUERY_ORDER_NULL_1 = "CPS01003";  //无此订单记录
    public static final String QUERY_ORDER_NULL_2 = "CPS01300";   //没有此订单
    public static final String QUERY_ORDER_NULL_3 = "CPS13002";   //没有符合条件的记录(refundQuery)

    public static final String SIGN_ERROR = "IPS0009"; // 证书签名错误
    public static final String SIGN_DISCREPANCY = "IPS0008"; // 签名不符
    public static final String PAY_DATA_ILLEGAL = "IPS0009"; // [type|merchantId|version]数据非法
    public static final String QUERY_DATA_ILLEGAL = "IPS0002"; // [type|merchantId|version]数据非法
    public static final String TRANS_MAINTENANCE = "IPS0001"; // 交易正在维护,请稍后提交

    public static final String MERCHANT_NOT_FOUND = "IPS0006"; // 未找到商户[merchant_id]

    public static final String ORDER_CANNOT_PAY = "CPS50382"; // 当前订单状态不允许支付
    public static final String SYSTEM_BUSY = "CPS03001"; // 系统忙，请稍后再试
    public static final String BARCODE_IS_NULL = "CPS82052"; // 条码信息为空
    public static final String AMT_IS_ILLEGAL = "CPS01015"; // 订单金额小于等于0
    public static final String ORDER_ID_NOT_FOUND = "CPS08005"; // 未找到该订单号(refund)
    public static final String ORDER_ID_REPEAT = "CPS08014"; // 对不起，退款流水号重复，请重新输入(refund)
    public static final String REFUND_AMT_ILLEGAL = "CPS08009"; // 退款金额不能大于可退款金额(refund)
    public static final String REFUND_AMT_LESS_THAN_ZERO = "MCG01088"; // 退款订单数据有误，请重新退款(refund)


    public static final List<String> PAY_FAIL_ERR_CODE_LISTS = Arrays.asList(
            RET_CODE_NOT_ENOUGH_MONEY_IN_BANK_CARD_1, RET_CODE_NOT_ENOUGH_MONEY_IN_BANK_CARD_2, RET_CODE_NOT_ENOUGH_MONEY_IN_BANK_CARD_3,
            RET_CODE_NOT_ENOUGH_MONEY_IN_BANK_CARD_4, RET_CODE_NOT_ENOUGH, RET_CODE_MONEY_LIMIT, RET_CODE_PAY_RISK,
            RET_CODE_BANK_CARD_EXPIRE_1, RET_CODE_BANK_CARD_EXPIRE_2, RET_CODE_BANK_CARD_EXPIRE_3, RET_CODE_INVALID_AUTH_CODE,
            RET_CODE_PAY_CODE_INVALID, RET_CODE_PAY_CODE_OUT, RET_CODE_PAY_CODE_ERROR, RET_CODE_ILLEGAL_RELATIONSHIP,
            PAY_DATA_ILLEGAL, SIGN_ERROR, ORDER_CANNOT_PAY, SYSTEM_BUSY, BARCODE_IS_NULL, AMT_IS_ILLEGAL
    );

    public static final List<String> QUERY_FAIL_ERR_CODE_LISTS = Arrays.asList(
            QUERY_ORDER_NULL_1, QUERY_ORDER_NULL_2, QUERY_ORDER_NULL_3, QUERY_DATA_ILLEGAL, TRANS_MAINTENANCE, MERCHANT_NOT_FOUND
    );

    public static final List<String> REFUND_FAIL_ERR_CODE_LISTS = Arrays.asList(
            REFUND_ORDER_NOT_EXISTS, ORDER_ID_NOT_FOUND, ORDER_ID_REPEAT, REFUND_AMT_ILLEGAL, REFUND_AMT_LESS_THAN_ZERO
    );

}
