package com.wosai.mpay.api.cmcc;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.CMCCSignature;
import com.wosai.mpay.util.Hex;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.RsaSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by lihebin on 17/12/8.
 */
public class CMCCWalletClient {
    public static final Logger logger = LoggerFactory.getLogger(CMCCWalletClient.class);
    private int connectTimeout = 30000;
    private int readTimeout = 30000;

    private List<String> payColumns = Arrays.asList(
            ProtocolFields.CHARACTER_SET, BusinessFields.NOTIFY_URL, BusinessFields.MERCHANT_ID, BusinessFields.REQUEST_ID,
            BusinessFields.SIGN_TYPE, BusinessFields.TYPE, BusinessFields.VERSION, BusinessFields.AMOUNT,
            BusinessFields.CURRENCY, BusinessFields.ORDER_DATE, BusinessFields.ORDER_ID, BusinessFields.PERIOD,
            BusinessFields.PERIOD_UNIT, BusinessFields.PRODUCT_NAME, BusinessFields.RESERVED_1, BusinessFields.USER_TOKEN,
            BusinessFields.COU_FLAG, BusinessFields.VCH_FLAG, BusinessFields.CASH_FLAG, BusinessFields.PIK_FLAG, BusinessFields.OPR_ID
    );
    private List<String> cancelColumns = Arrays.asList(
            ProtocolFields.CHARACTER_SET, BusinessFields.MERCHANT_ID, BusinessFields.REQUEST_ID, BusinessFields.SIGN_TYPE,
            BusinessFields.TYPE, BusinessFields.VERSION, BusinessFields.OREQUEST_ID, BusinessFields.OORDER_DATE
    );
    private List<String> queryColumns = Arrays.asList(
            BusinessFields.MERCHANT_ID, BusinessFields.REQUEST_ID, BusinessFields.SIGN_TYPE, BusinessFields.TYPE,
            BusinessFields.VERSION, BusinessFields.ORDER_ID
    );
    private List<String> refundColumns = Arrays.asList(
            BusinessFields.MERCHANT_ID, BusinessFields.REQUEST_ID, BusinessFields.SIGN_TYPE, BusinessFields.TYPE,
            BusinessFields.VERSION, BusinessFields.ORDER_ID, BusinessFields.AMOUNT
    );
    private List<String> refundQueryColumns = Arrays.asList(
            BusinessFields.MERCHANT_ID, BusinessFields.SIGN_TYPE, BusinessFields.TYPE,
            BusinessFields.VERSION, BusinessFields.REQUEST_ID
    );

    public Map<String, Object> call(String serviceUrl,String signType, String key, Map<String, Object> request, String type) throws MpayException, MpayApiNetworkError, UnsupportedEncodingException {
        Map<String, String> params = new LinkedHashMap<>();
        StringBuilder signParam = new StringBuilder();
        com.wosai.mpay.api.cmcc.RequestBuilder paramsBuilder = new com.wosai.mpay.api.cmcc.RequestBuilder();
        if (type.equals("pay")) {
            for (String para : payColumns) {
                if (request.get(para) != null) {
                    params.put(para, request.get(para).toString());
                    signParam.append(request.get(para).toString());

                }
            }
        } else if (type.equals("cancel")) {
            for (String para : cancelColumns) {
                if (request.get(para) != null) {
                    params.put(para, request.get(para).toString());
                    signParam.append(request.get(para).toString());

                }
            }
        } else if (type.equals("query")) {
            for (String para : queryColumns) {
                if (request.get(para) != null) {
                    params.put(para, request.get(para).toString());
                    signParam.append(request.get(para).toString());

                }
            }
        } else if (type.equals("refund")) {
            for (String para : refundColumns) {
                if (request.get(para) != null) {
                    params.put(para, request.get(para).toString());
                    signParam.append(request.get(para).toString());
                }
            }
        } else if (type.equals("refundQuery")){
            for (String para : refundQueryColumns) {
                if (request.get(para) != null) {
                    params.put(para, request.get(para).toString());
                    signParam.append(request.get(para).toString());
                }
            }
        } else {
            throw new UnsupportedEncodingException("payment type is null.");
        }
        logger.debug("request {}", params);
        if (ProtocolFields.SIGN_TYPE_RSA.equals(signType)){
            paramsBuilder.set(BusinessFields.HMAC, RsaSignature.cmccSign(signParam.toString(), RsaSignature.SIG_ALG_NAME_SHA1_With_RSA, key,null));
        } else{
            paramsBuilder.set(BusinessFields.HMAC, CMCCSignature.MD5Sign(signParam.toString(), key));
        }
        for(Map.Entry<String, String> entry : params.entrySet()) {
            paramsBuilder.set(entry.getKey(), entry.getValue());
        }

        String response = HttpClientUtils.doPost(CMCCWalletClient.class.getName(), null, null, serviceUrl, "application/x-www-form-urlencoded", paramsBuilder.build(),  ProtocolFields.CHARSET_GB2312_STRING, connectTimeout, readTimeout);
        logger.info("response {}", response);
        return parseCMCCResponse(response);
    }


    /**
     * 解析和支付返回内容
     *
     * @param response
     * @return
     * @throws MpayException
     */
    private Map<String, Object> parseCMCCResponse(String response) throws MpayException {
        try {

            Map<String, Object> map = new HashMap<String, Object>();
            response = URLDecoder.decode(response, "UTF-8");
            String[] param = response.split("&");
            for (String keyvalue : param) {
                keyvalue = keyvalue.replaceAll("#", "=");
                String[] pair = keyvalue.split("=");
                if (pair.length == 2) {
                    map.put(pair[0], pair[1]);
                }else if (pair.length == 1) {
                    map.put(pair[0], "");
                }else {
                    Map amtItem = new HashMap();
                    for (int i = 1; i < pair.length; i += 2) {
                        amtItem.put(pair[i], pair[i + 1]);
                    }
                    map.put(pair[0], amtItem);
                }
            }
            return map;
        } catch (Exception e) {
            throw new MpayException("can not transfer response to map", e);
        }
    }

    public static void main(String[] args) throws IOException, MpayException {
        testPay();
//        testQuery2();
//        testRefund();
//        testRefundQuery();
    }


    public static void testPay() throws MpayException, IOException {
        CMCCWalletClient client = new CMCCWalletClient();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddhhmmss");
        com.wosai.mpay.api.cmcc.RequestBuilder builder = new com.wosai.mpay.api.cmcc.RequestBuilder();
        builder.set(ProtocolFields.CHARACTER_SET, "02");
        builder.set(BusinessFields.NOTIFY_URL, CMCCWalletConfig.NOTIFY_URL);
        builder.set(BusinessFields.MERCHANT_ID, "888009941110054");
        builder.set(BusinessFields.REQUEST_ID, "20490515134728");
        builder.set(BusinessFields.SIGN_TYPE, ProtocolFields.SIGN_TYPE_RSA);
        builder.set(BusinessFields.TYPE, "CloudQuickPay");
        builder.set(BusinessFields.VERSION, "2.0.1");
        builder.set(BusinessFields.AMOUNT, 10);
        builder.set(BusinessFields.CURRENCY, "00");
        builder.set(BusinessFields.ORDER_DATE, "20510705");
        builder.set(BusinessFields.ORDER_ID, "3990111a6604d965900006999960");
        builder.set(BusinessFields.PERIOD, 2);
        builder.set(BusinessFields.PERIOD_UNIT, "00");
        builder.set(BusinessFields.PRODUCT_NAME, "test");
        builder.set(BusinessFields.USER_TOKEN, "810086064234992712");
        builder.set(BusinessFields.COU_FLAG, "1");
        builder.set(BusinessFields.VCH_FLAG, "1");
        builder.set(BusinessFields.CASH_FLAG, "0");
        builder.set(BusinessFields.PIK_FLAG, "1");
        builder.set(BusinessFields.OPR_ID, "12345");
        builder.set(BusinessFields.POI_FLAG, "0");
        String privateKey = com.wosai.mpay.util.Base64.encode(Hex.decode(CMCCWalletConfig.privateKey_HEBAO));
        Map<String, Object> result = client.call(CMCCWalletConfig.PAY_URL_TEST,ProtocolFields.SIGN_TYPE_RSA, privateKey, builder.build(),"pay");
        System.out.println(result);

    }

    public static void testCancel() throws MpayException, MpayApiNetworkError, UnsupportedEncodingException {
        CMCCWalletClient client = new CMCCWalletClient();
        com.wosai.mpay.api.cmcc.RequestBuilder builder = new com.wosai.mpay.api.cmcc.RequestBuilder();
        builder.set(ProtocolFields.CHARACTER_SET, "02");
        builder.set(BusinessFields.MERCHANT_ID, CMCCWalletConfig.MERCHANT_ID);
        builder.set(BusinessFields.REQUEST_ID, "789425924537274481535");
        builder.set(BusinessFields.SIGN_TYPE, "MD5");
        builder.set(BusinessFields.TYPE, "CloudTxnCancel");
        builder.set(BusinessFields.VERSION, "2.0.0");
        builder.set(BusinessFields.OREQUEST_ID, "7894259245378962");
        builder.set(BusinessFields.OORDER_DATE, "20480630");
        Map<String, Object> result = client.call(CMCCWalletConfig.PAY_URL,"MD5", CMCCWalletConfig.MERCHANT_CERT, builder.build(),"cancel");
        System.out.println(result);
    }

    public static void testQuery() throws MpayException, MpayApiNetworkError, UnsupportedEncodingException {
        CMCCWalletClient client = new CMCCWalletClient();
        com.wosai.mpay.api.cmcc.RequestBuilder builder = new com.wosai.mpay.api.cmcc.RequestBuilder();
        builder.set(com.wosai.mpay.api.cmcc.ProtocolFields.CHARACTER_SET, "02");
        builder.set(BusinessFields.MERCHANT_ID, "888009941110054");
        builder.set(BusinessFields.REQUEST_ID, "20490515134725");
        builder.set(BusinessFields.SIGN_TYPE, "RSA");
        builder.set(BusinessFields.TYPE, "OrderQueryByYPOS");
        builder.set(BusinessFields.VERSION, "2.0.0");
        builder.set(BusinessFields.ORDER_DATE, "20510705");
        builder.set(BusinessFields.ORDER_ID, "3990111a6604d965900006999936");
        String privateKey = com.wosai.mpay.util.Base64.encode(Hex.decode(CMCCWalletConfig.privateKey_HEBAO));
        Map<String, Object> result = client.call(CMCCWalletConfig.PAY_URL,ProtocolFields.SIGN_TYPE_RSA, privateKey, builder.build(),"query");
        System.out.println(result);

    }

    public static void testQuery2() throws MpayException, MpayApiNetworkError, UnsupportedEncodingException {
        CMCCWalletClient client = new CMCCWalletClient();
        com.wosai.mpay.api.cmcc.RequestBuilder builder = new com.wosai.mpay.api.cmcc.RequestBuilder();
        builder.set(BusinessFields.MERCHANT_ID, "888009941110054");
        builder.set(BusinessFields.REQUEST_ID, "2049051513472580103");
        builder.set(BusinessFields.SIGN_TYPE, ProtocolFields.SIGN_TYPE_RSA);
        builder.set(BusinessFields.TYPE, "OrderQueryCPS");
        builder.set(BusinessFields.VERSION, "2.0.0");
        builder.set(BusinessFields.ORDER_ID, "7894259249971776");
        String privateKey = com.wosai.mpay.util.Base64.encode(Hex.decode(CMCCWalletConfig.privateKey_HEBAO));
        Map<String, Object> result = client.call(CMCCWalletConfig.PAY_URL_TEST, ProtocolFields.SIGN_TYPE_RSA, privateKey, builder.build(),"query");
        System.out.println(result);

    }

    public static void testRefund() throws MpayException, MpayApiNetworkError, UnsupportedEncodingException {
        CMCCWalletClient client = new CMCCWalletClient();
        com.wosai.mpay.api.cmcc.RequestBuilder builder = new com.wosai.mpay.api.cmcc.RequestBuilder();
        builder.set(BusinessFields.MERCHANT_ID, "888009941110054");
        builder.set(BusinessFields.REQUEST_ID, "2049051513472504");
        builder.set(BusinessFields.SIGN_TYPE, ProtocolFields.SIGN_TYPE_RSA);
        builder.set(BusinessFields.TYPE, "OrderRefundCPS");
        builder.set(BusinessFields.VERSION, "2.0.0");
        builder.set(BusinessFields.ORDER_ID, "3990111a6604d965900006999936");
        builder.set(BusinessFields.AMOUNT, -2);
        String privateKey = com.wosai.mpay.util.Base64.encode(Hex.decode(CMCCWalletConfig.privateKey_HEBAO));
        Map<String, Object> result = client.call(CMCCWalletConfig.PAY_URL_TEST,ProtocolFields.SIGN_TYPE_RSA, privateKey, builder.build(),"refund");
        System.out.println(result);
    }

    public static void testRefundQuery() throws MpayException, MpayApiNetworkError, UnsupportedEncodingException {
        CMCCWalletClient client = new CMCCWalletClient();
        com.wosai.mpay.api.cmcc.RequestBuilder builder = new com.wosai.mpay.api.cmcc.RequestBuilder();
        builder.set(BusinessFields.MERCHANT_ID, "888009941110054");
        builder.set(BusinessFields.SIGN_TYPE, ProtocolFields.SIGN_TYPE_RSA);
        builder.set(BusinessFields.TYPE, "CloudRfQuery");
        builder.set(BusinessFields.VERSION, "2.0.0");
        builder.set(BusinessFields.REQUEST_ID, "7894259249951272");
        String privateKey = com.wosai.mpay.util.Base64.encode(Hex.decode(CMCCWalletConfig.privateKey_HEBAO));
        Map<String, Object> result = client.call(CMCCWalletConfig.PAY_URL_TEST,ProtocolFields.SIGN_TYPE_RSA, privateKey, builder.build(),"refundQuery");
        System.out.println(result);
    }

    public static void testPrecreate() throws MpayException, MpayApiNetworkError {

    }


    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

}
