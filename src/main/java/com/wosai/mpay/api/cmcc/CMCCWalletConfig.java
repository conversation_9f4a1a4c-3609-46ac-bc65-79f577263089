package com.wosai.mpay.api.cmcc;

/**
 * Created by l<PERSON><PERSON><PERSON> on 17/12/8.
 */
public class CMCCWalletConfig {
    public static final String PAY_URL = "https://ipos.10086.cn/cps/cmpayService";

    public static final String PAY_URL_TEST = "http://***************/cps/cmpayService";

    /**
     * 支付异步通知地址
     **/
    public static final String NOTIFY_URL = "http://shouqianba.com";

    public static final String PAY_TYPE = "CloudQuickPay";          //云支付反扫条码支付
    public static final String CANCEL_TYPE = "CloudTxnCancel";      //云支付冲正接口
    public static final String QUERY_TYPE = "OrderQueryByYPOS";     //云支付订单结果轮询接口
    public static final String OLD_QUERY_TYPE = "OrderQueryCPS";    //云支付订单查询(单笔)
    public static final String REFUND_TYPE = "OrderRefundCPS";      //云支付退款(单笔)
    public static final String CLOUD_PAY_AMT = "CloudPayAmt";       //云支付订单二维码生成接口
    public static final String CLOUD_MERC_QUERY = "CloudMercQuery"; //云支付商户二维码查询接口
    public static final String CLOUD_RF_QUERY = "CloudRfQuery";     //云支付退款明细查询接口

    public static final String MERCHANT_ID = "888073115000276";
    public static final String MERCHANT_CERT = "";

    public static final  String privateKey = "";
    public static final  String privateKey_HEBAO = "";
}

