package com.wosai.mpay.api.cmcc;

/**
 * Created by l<PERSON><PERSON><PERSON> on 17/12/9.
 */
public class BusinessFields {


    public static final String NOTIFY_URL = "notifyUrl";//后台通知 URL
    public static final String MERCHANT_ID = "merchantId";//我方平台给商户分配的唯一标识
    public static final String REQUEST_ID = "requestId";//商户请求的交易流水号唯一
    public static final String SIGN_TYPE = "signType";//签名方式 只能是 MD5，RSA
    public static final String TYPE = "type";//接口类型 CloudQuickPay
    public static final String VERSION = "version";//版本号
    public static final String MERCHANT_CERT = "merchantCert";//商户证书公钥 不参与签名;如果 signType=RSA，此 项必输
    public static final String HMAC = "hmac";//获得 hmac 的方法见签名 算法,参数顺序按照表格 中从上到下的顺序,但不 包括本参数.

    public static final String AMOUNT = "amount";//订单金额，以分为单位， 如 1 元表示为 100
    public static final String CURRENCY = "currency";//币种 00-CNY 现金 01-CMY 充值卡 默认为:00
    public static final String ORDER_DATE = "orderDate";//订单提交日期 商户发起请求的时间; 年年年年月月日日
    public static final String ORDER_ID = "orderId";//商户系统订单号
    public static final String PERIOD = "period";//有效期数量 数字。订单申请后等待支付的有效时间，默认必须 小于 30 分钟，过期订单 自动关闭。
    public static final String PERIOD_UNIT = "periodUnit";//有效期单位 只能取以下枚举值 00-分
    public static final String PRODUCT_NAME = "productName"; //所购买商品的名称
    public static final String RESERVED_1 = "reserved1"; //保留字段 1 交易返回时原样返回给商家网站，给商户备用
    public static final String RESERVED_2 = "reserved2"; //保留字段 2 交易返回时原样返回给商家网站，给商户备用

    public static final String USER_TOKEN = "userToken";//用户扫描码 18 位条码
    public static final String COU_FLAG = "couFlag";//是否支持电子券 0- 支持(默认) 1- 不支持
    public static final String VCH_FLAG = "vchFlag";//是否支持代金券 0- 支持(默认) 1- 不支持
    public static final String CASH_FLAG = "cashFlag";//是否支持账户现金 0- 支持(默认) 1- 不支持
    public static final String PIK_FLAG = "pikFlag";//是否支持订单捡起 0- 支持(默认) 1- 不支持
    public static final String POI_FLAG = "poiFlag";//是否支持积分 0- 支持(默认) 1- 不支持
    public static final String OPR_ID = "oprId";//商户操作员 编号

    public static final String OREQUEST_ID = "orequestId";//原交易商户请求号 需冲正的商户交易流水号
    public static final String OORDER_DATE = "oorderDate";//原交易订单提交日期 商户发起请求的时间; 年年年年月月日日

}
