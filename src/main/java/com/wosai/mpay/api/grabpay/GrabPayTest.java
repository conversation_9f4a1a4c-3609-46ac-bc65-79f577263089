package com.wosai.mpay.api.grabpay;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/***
 * @ClassName: GrabPayTest
 * @Description:
 * @Auther: dabuff
 * @Date: 2022/2/9 4:59 PM
 */
public class GrabPayTest {

    private static final String BASE_URL = "https://partner-api.stg-myteksi.com";
    private static final String PAY_URL = BASE_URL + "/grabpay/partner/v1/terminal/transaction/perform";
    private static final String QUERY_URL = BASE_URL + "/grabpay/partner/v1/terminal/transaction/";
    private static final String CANCEL_URL = BASE_URL + "/grabpay/partner/v1/terminal/transaction/";
    private static final String REFUND_URL = BASE_URL + "/grabpay/partner/v1/terminal/transaction/";


    private static final String partner_id = "8b3f30d2-b0fa-42d9-bd34-55ef38d3eec4";
    private static final String private_key = "";
    private static final String merchant_id = "34031f5d-ad2d-458b-a085-e203a1225527";
    private static final String terminal_id = "795b94f4cd85795fb4ab944f5";


    private static void payTest() throws Exception {
        GrabPayRequestBuilder requestBuilder = new GrabPayRequestBuilder();
        requestBuilder.bodySet(GrabPayProtocolFields.MSG_ID, UUID.randomUUID().toString().replaceAll("-", ""));
        requestBuilder.bodySet(GrabPayBusinessFields.GRAB_ID, merchant_id);
        requestBuilder.bodySet(GrabPayBusinessFields.TERMINAL_ID, terminal_id);
        requestBuilder.bodySet(GrabPayBusinessFields.CURRENCY, "MYR");
        requestBuilder.bodySet(GrabPayBusinessFields.AMOUNT, 1);
        requestBuilder.bodySet(GrabPayBusinessFields.PARTNER_TX_ID, "7894259280895276");
        requestBuilder.bodySet(GrabPayBusinessFields.CODE, "657192433020506662");
        requestBuilder.bodySet(GrabPayBusinessFields.ADDITIONAL_INFO, Collections.singletonList("amountBreakdown"));

        requestBuilder.headSet(GrabPayProtocolFields.CONTENT_TYPE, GrabPayConstants.CONTENT_TYPE);
        requestBuilder.headSet(GrabPayProtocolFields.DATE, getGMTDateTime());

        GrabPayClient client = new GrabPayClient();
        Map<String, Object> result = client.call(PAY_URL, GrabPayConstants.POST, requestBuilder.build(), partner_id, private_key);

        System.out.println(result);
    }

    private static void queryTest() throws Exception {
        GrabPayRequestBuilder requestBuilder = new GrabPayRequestBuilder();
        requestBuilder.bodySet(GrabPayProtocolFields.MSG_ID, UUID.randomUUID().toString().replaceAll("-", ""));
        requestBuilder.bodySet(GrabPayBusinessFields.GRAB_ID, merchant_id);
        requestBuilder.bodySet(GrabPayBusinessFields.TERMINAL_ID, terminal_id);
        requestBuilder.bodySet(GrabPayBusinessFields.CURRENCY, "MYR");
        requestBuilder.bodySet(GrabPayBusinessFields.TX_TYPE, "P2M");
        requestBuilder.bodySet(GrabPayBusinessFields.PARTNER_TX_ID, "partner-1b254f3185d0b083d1f6f116");
        requestBuilder.bodySet(GrabPayBusinessFields.ADDITIONAL_INFO, "amountBreakdown");
        requestBuilder.headSet(GrabPayProtocolFields.DATE, getGMTDateTime());
        requestBuilder.headSet(GrabPayProtocolFields.CONTENT_TYPE, GrabPayConstants.CONTENT_TYPE_GET);


        String url = QUERY_URL + "partner-1b254f3185d0b083d1f6f116";
        GrabPayClient client = new GrabPayClient();
        Map<String, Object> result = client.call(url, GrabPayConstants.GET, requestBuilder.build(), partner_id, private_key);
//        Map<String, Object> result = client.call("https://en0djilumnnmfe.x.pipedream.net", GrabPayConstants.GET, requestBuilder.build(), private_key);

        System.out.println(result);
    }

    private static void refundTest() throws Exception {
        GrabPayRequestBuilder requestBuilder = new GrabPayRequestBuilder();
        requestBuilder.bodySet(GrabPayProtocolFields.MSG_ID, UUID.randomUUID().toString().replaceAll("-", ""));
        requestBuilder.bodySet(GrabPayBusinessFields.GRAB_ID, merchant_id);
        requestBuilder.bodySet(GrabPayBusinessFields.TERMINAL_ID, terminal_id);
        requestBuilder.bodySet(GrabPayBusinessFields.CURRENCY, "MYR");
        requestBuilder.bodySet(GrabPayBusinessFields.AMOUNT, 1);
        requestBuilder.bodySet(GrabPayBusinessFields.PARTNER_TX_ID, "refund--1b254f3185d0b083d1f6fc29");
        requestBuilder.headSet(GrabPayProtocolFields.CONTENT_TYPE, GrabPayConstants.CONTENT_TYPE);
        requestBuilder.headSet(GrabPayProtocolFields.DATE, getGMTDateTime());
        String url = REFUND_URL + "7894259280895276" + "/refund";
        GrabPayClient client = new GrabPayClient();
        Map<String, Object> result = client.call(url, GrabPayConstants.PUT, requestBuilder.build(), partner_id, private_key);

        System.out.println(result);
    }

    private static void cancelTest() throws Exception {
        GrabPayRequestBuilder requestBuilder = new GrabPayRequestBuilder();
        requestBuilder.bodySet(GrabPayProtocolFields.MSG_ID, UUID.randomUUID().toString().replaceAll("-", ""));
        requestBuilder.bodySet(GrabPayBusinessFields.GRAB_ID, merchant_id);
        requestBuilder.bodySet(GrabPayBusinessFields.TERMINAL_ID, terminal_id);
        requestBuilder.bodySet(GrabPayBusinessFields.CURRENCY, "MYR");
        requestBuilder.bodySet(GrabPayBusinessFields.ORIGINAL_PARTNER_TX_ID, "partner-1b254f3185d0b083d1f6f105");
        requestBuilder.headSet(GrabPayProtocolFields.CONTENT_TYPE, GrabPayConstants.CONTENT_TYPE);
        requestBuilder.headSet(GrabPayProtocolFields.DATE, getGMTDateTime());


        String url = CANCEL_URL + "partner-1b254f3185d0b083d1f6f105" + "/cancel";
        GrabPayClient client = new GrabPayClient();
        Map<String, Object> result = client.call(url, GrabPayConstants.PUT, requestBuilder.build(), partner_id, private_key);

        System.out.println(result);
    }


    public static void main(String[] args) throws Exception {
//        payTest();
//        queryTest();
        refundTest();
//        cancelTest();

    }

    public static String getGMTDateTime() {
        DateFormat dateFormat = new SimpleDateFormat("E, dd MMM yyyy HH:mm:ss z", Locale.ENGLISH);
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        Date now = new Date(System.currentTimeMillis());
        return dateFormat.format(now);
    }
}
