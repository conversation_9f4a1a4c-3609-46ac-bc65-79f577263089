package com.wosai.mpay.api.grabpay;

/***
 * @ClassName: GrabPayBusinessFields
 * @Description:
 * @Auther: dabuff
 * @Date: 2022/2/9 5:18 PM
 */
public class GrabPayBusinessFields {

    public static final String GRAB_ID = "grabID"; //商户门店的唯一ID，同merchant_id， grabpay 提供
    public static final String TERMINAL_ID = "terminalID"; //终端的唯一ID，grabpay 提供
    public static final String CURRENCY = "currency"; //币种

    public static final String PARTNER_TX_ID = "partnerTxID"; //交易流水号/退款流水号
    public static final String AMOUNT = "amount"; //交易金额/退款金额

    /* 被扫支付 */
    public static final String CODE = "code"; //条形码
    public static final String ADDITIONAL_INFO = "additionalInfo"; //额外信息

    /* 查询 */
    public static final String TX_TYPE= "txType"; //流水类型

    /* 撤单 */
    public static final String ORIGINAL_PARTNER_TX_ID= "origPartnerTxID"; //原交易流水号

    /* 退款 */
    public static final String REASON = "reason"; //退款原因

}
