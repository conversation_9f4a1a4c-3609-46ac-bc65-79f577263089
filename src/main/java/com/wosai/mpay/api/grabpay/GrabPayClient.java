package com.wosai.mpay.api.grabpay;

import com.wosai.mpay.exception.HttpFailException;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.util.*;
import com.wosai.pantheon.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.util.Map;

/***
 * @ClassName: GrabPayClient
 * @Description:
 * @Auther: dabuff
 * @Date: 2022/2/9 3:52 PM
 */
public class GrabPayClient {
    public static final Logger logger = LoggerFactory.getLogger(GrabPayClient.class);

    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public GrabPayClient() {
    }

    public GrabPayClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public Map<String, Object> call(String serviceUrl, String httpMethod, Map<String, Object> request, String partnerId, String signKey) throws Exception {

        Map<String, String> head = MapUtil.getMap(request, GrabPayProtocolFields.HEAD);
        Map body = MapUtil.getMap(request, GrabPayProtocolFields.BODY);

        if (GrabPayConstants.GET.equals(httpMethod)){
            serviceUrl = WebUtils.buildGetUrl(serviceUrl, WebUtils.buildQuery(body, "UTF-8")).toString();
        }
        URL url = new URL(serviceUrl);
        String gmtDateTime = MapUtil.getString(head, GrabPayProtocolFields.DATE);
        String contentType = MapUtil.getString(head, GrabPayProtocolFields.CONTENT_TYPE);
        String hmacResult = GrabPaySignature.sign(httpMethod,
                contentType,
                gmtDateTime,
                url.getFile(),
                GrabPayConstants.GET.equals(httpMethod) ? StringUtils.EMPTY : JsonUtil.objectToJsonString(body),
                signKey);

        String authorization = partnerId + ":" + hmacResult;
        head.put(GrabPayProtocolFields.AUTHORIZATION, authorization);

        String bodyStr = JsonUtil.objectToJsonString(body);

        logger.info("original request params is {}", bodyStr);
        logger.info("original request headers is {}", JsonUtil.objectToJsonString(head));

        String responseStr;
        int responseCode = -1;
        switch (httpMethod) {
            case GrabPayConstants.POST:
                try {
                    responseStr = HttpClientUtils.doPost(GrabPayClient.class.getName(), null, null, serviceUrl,
                            GrabPayConstants.CONTENT_TYPE_WITHOUT_CHARSET, bodyStr, head, GrabPayConstants.CHARSET_UTF8, connectTimeout, readTimeout);
                } catch (HttpFailException e){
                    responseCode = e.getCode();
                    responseStr = e.getBody();
                }
                break;
            case GrabPayConstants.GET:
                try {
                    Map<String, Object> getResult = HttpClientUtils.doGet(GrabPayClient.class.getName(), null, null, serviceUrl, null, head, GrabPayConstants.CHARSET_UTF8, connectTimeout, readTimeout);
                    responseStr = MapUtils.getString(getResult, HttpClientUtils.BODY_RESULT_FIELD);
                } catch (HttpFailException e){
                    responseCode = e.getCode();
                    responseStr = e.getBody();
                }
                break;
            case GrabPayConstants.PUT:
                try {
                    responseStr = HttpClientUtils.doPut(GrabPayClient.class.getName(), null, null, serviceUrl,
                            GrabPayConstants.CONTENT_TYPE_WITHOUT_CHARSET, bodyStr, head, GrabPayConstants.CHARSET_UTF8, connectTimeout, readTimeout);
                } catch (HttpFailException e) {
                    responseCode = e.getCode();
                    responseStr = e.getBody();
                }

                break;
            default:
                throw new Exception("不支持的请求方法: " + httpMethod);
        }

        logger.info("response {}", responseStr);
        try {
            Map<String, Object> result = JsonUtil.jsonStrToObject(responseStr, Map.class);
            if (responseCode != -1){
                result.put("respCode", String.valueOf(responseCode));
            }
            return result;
        } catch (Exception e) {
            throw new MpayApiUnknownResponse("parse response error: " + e.getMessage(), e);
        }
    }
}