package com.wosai.mpay.api.grabpay;

/***
 * @ClassName: GrabPayResponseFields
 * @Description:
 * @Auther: dabuff
 * @Date: 2022/2/10 6:29 PM
 */
public class GrabPayResponseFields {

    public static final String RESPONSE_CODE = "respCode"; //http code
    public static final String CODE = "code"; //error code
    public static final String ARG = "arg"; //error arg
    public static final String MESSAGE = "message"; //error message
    public static final String REASON = "reason"; //error reason
    public static final String DEV_MESSAGE = "devMessage"; //error dev message
    public static final String ERR_MSG = "errMsg"; //失败交易的错误信息
    public static final String TX_ID = "txID"; //grabpay 侧的交易流水号
    public static final String STATUS = "status"; //交易状态
    public static final String CURRENCY = "currency"; //币种
    public static final String AMOUNT = "amount"; //交易金额/退款金额
    public static final String UPDATED = "updated"; //交易更新时间  Unix timestamp
    public static final String ADDITIONAL_INFO = "additionalInfo"; //额外信息
    public static final String ORIGINAL_TX_ID = "originTxID"; //grabpay 侧的原交易流水号 （退款返回）
    public static final String DESCRIPTION = "description"; //status = success 时的响应信息


}
