package com.wosai.mpay.api.grabpay;

/***
 * @ClassName: GrabPayConstants
 * @Description:
 * @Auther: dabuff
 * @Date: 2022/2/9 4:53 PM
 */
public class GrabPayConstants {

    public static final String CONTENT_TYPE = "application/json; charset=UTF-8";
    public static final String CONTENT_TYPE_GET = "application/x-www-form-urlencoded";
    public static final String CONTENT_TYPE_WITHOUT_CHARSET = "application/json";
    public static final String CHARSET_UTF8 = "UTF-8";
    public static final String POST = "POST";
    public static final String PUT = "PUT";
    public static final String GET = "GET";
    public static final String REFUND = "Refund"; //退款查询
    public static final String P2M = "P2M"; //交易查询
    public static final String AMOUNT_BREAKDOWN = "amountBreakdown"; //金额明细

    /* status */
    public static final String STATUS_SUCCESS = "success";
    public static final String STATUS_FAILED = "failed";
    public static final String STATUS_UNKNOWN = "unknown";
    public static final String STATUS_PENDING = "pending";
    public static final String STATUS_BAD_DEBT = "bad_debt";

    /* http resp code */
    public static final String CODE_SUCCESS = "200";
    public static final String CODE_BAD_REQUEST = "400";
    public static final String CODE_UNAUTHORIZED = "401";
    public static final String CODE_FORBIDDEN = "403";
    public static final String CODE_NOT_FOUND = "404";
    public static final String CODE_CONFLICT = "409";
    public static final String CODE_INTERNAL_SERVER_ERROR = "500";
    public static final String CODE_SERVICE_UNAVAILABLE = "503";

    public static final String CODE_CANCEL_NOT_SUPPORTED = "40011";
    public static final String MSG_CANCEL_NOT_SUPPORTED = "transaction status is not supported";

}