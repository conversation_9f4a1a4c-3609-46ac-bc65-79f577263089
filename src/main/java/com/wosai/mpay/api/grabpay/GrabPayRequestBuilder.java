package com.wosai.mpay.api.grabpay;

import java.util.HashMap;
import java.util.Map;

/***
 * @ClassName: GrabPayRequestBuilder
 * @Description:
 * @Auther: dabuff
 * @Date: 2022/2/9 5:10 PM
 */
public class GrabPayRequestBuilder {
    Map<String, Object> request;
    Map<String, Object> body;
    Map<String, String> head;

    public GrabPayRequestBuilder() {
        request = new HashMap<>();
        body = new HashMap<>();
        head = new HashMap<>();
    }

    public void requestSet(String field, Object value) {
        request.put(field, value);
    }

    public void headSet(String field, String value) {
        head.put(field, value);
    }

    public void bodySet(String field, Object value) {
        body.put(field, value);
    }

    public Map<String, Object> build() {
        requestSet(GrabPayProtocolFields.HEAD, head);
        requestSet(GrabPayProtocolFields.BODY, body);

        return request;
    }
}