package com.wosai.mpay.api.unionqrcode;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/3/4.
 */
public class ResponseFields {

    public static final String RESP_CODE = "respCode";// 应答码
    public static final String RESP_MSG = "respMsg";// 响应信息
    public static final String REDIRECTURL="redirectUrl";
    public static final String ORIG_RESP_CODE = "origRespCode";// 原交易应答码
    public static final String ORIG_RESP_MSG = "origRespMsg";// 原交易响应信息
    public static final String SIGNATURE = "signature";// 签名串

    public static final String COUPON_INFO = "couponInfo";// 优惠信息
    public static final String COUPON_INFO_ID = "id";                   // COUPON_INFO
    public static final String COUPON_INFO_DESC = "desc";               // COUPON_INFO
    public static final String COUPON_INFO_TYPE = "type";               // COUPON_INFO
    public static final String COUPON_INFO_OFFST_AMT = "offstAmt";      // COUPON_INFO
    public static final String COUPON_INFO_SPNSR_ID = "spnsrId";        // COUPON_INFO
    public static final String COUPON_INFO_ADDN_INFO = "addnInfo";      // COUPON_INFO

    public static final String CURRENCY_CODE = "currencyCode";// 交易币种
    public static final String DCT_GOODS_INFO = "dctGoodsInfo";// 单品优惠信息
    public static final String ORDER_NO = "orderNo";// 订单号
    public static final String ORIG_ORDER_NO = "origOrderOo";// 原订单号

    public static final String PAYER_INFO = "payerInfo";// 付款方信息
    public static final String PAYER_INFO_BUYER_ID = "buyerId";             // PAYER_INFO
    public static final String PAYER_INFO_BUYER_ACCOUNT = "buyerAccount";   // PAYER_INFO
    public static final String PAYER_INFO_CARD_ATTR = "cardAttr";           // PAYER_INFO
    public static final String PAYER_INFO_ACC_NO = "accNo";                 // PAYER_INFO
    public static final String PAYER_INFO_NAME = "name";                    // PAYER_INFO

    public static final String PAY_AMT = "payAmt";// 买家付款金额
    public static final String PAY_TIME = "payTime";// 交易支付时间  退款成功时间
    public static final String QR_CODE = "qrCode";// 二维码
    public static final String SETTLEMENT_AMT = "settlementAmt";// 应结订单金额
    public static final String TXN_AMT = "txnAmt";// 交易金额
    public static final String MER_TRANS_INDEX="merTransIndex"; //商户交易索引
    public static final String ORIG_TXN_AMT="origTxnAmt";//原始金额

    public static final String VOUCHER_NUM="voucherNum"; //付款凭证号


}
