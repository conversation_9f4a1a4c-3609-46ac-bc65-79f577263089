package com.wosai.mpay.api.unionqrcode;


import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/3/4.
 */
public class UnionPayQRCodeConstants {

    public static final String DEFAULT_VERSION = "1.0.0";
    public static final String DEFAULT_SIGN_TYPE = "03";

    /** UTF-8字符集 **/
    public static final String CHARSET_UTF8 = "UTF-8";

    public static String ORDER_TIME_DATE_TIME_FORMAT = "yyyyMMddHHmmss"; //orderTime 20180702142900
    public static String PAY_TIME_DATE_TIME_FORMAT = "yyyyMMddHHmmss"; //payTime 20180629112830

    public static final String ORDER_TYPE_UNIONPAY = "unionpay"; //银联二维码

    public static final String REQ_TYPE_BARCODE="0310000903";
    public static final String REQ_TYPE_QRCODE="0510000903";
    public static final String REQ_TYPE_WAP="0630001903";

    public static final String REQ_TYPE_BARCODE_CANCEL="0330000903";
    public static final String REQ_TYPE_QRCODE_WAP_CANCEL="0170000903";

    public static final String REQ_TYPE_BARCODE_QUERY="0350000903";
    public static final String REQ_TYPE_QRCODE_WAP_QUERY="0540000903";

    public static final String REQ_TYPE_BARCODE_REFUND="0340000903";
    public static final String REQ_TYPE_QRCODE_WAP_REFUND="0150000903";
    public static final String REQ_TYPE_QRCODE_QUERY_USER_ID="0460000903";


    public static final String AREA_INFO_SH="1562912";

    public static final String CURRENCY_CODE_CNY = "156"; //人民币-156

    public static final String COUPON_INFO_SPNSR_ID_UNIONPAY = "00010000"; //银联出资方
    public static final String COUPON_INFO_TYPE_DD01 = "DD01"; //随机立减
    public static final String COUPON_INFO_TYPE_CP01 = "CP01"; //抵金券 1:无需领取，交易时直接适配并承兑的优惠券
    public static final String COUPON_INFO_TYPE_CP02 = "CP02"; //抵金券 2:事前领取，交易时上送银联并承兑的优惠券

    public static final String CARD_ATTR_DEBIT_CARD = "01"; // 借记卡
    public static final String CARD_ATTR_CREDIT_CARD = "02"; // 贷记卡

    public static final String ORDER_TYPE_COMMON_CONSUME="10";

    public static final String DEVICE_TYPE_11 = "11"; //条码支付辅助受理终端

    public static final String QRCODE_TYPE_DYNAMIC = "0"; //二维码类型 动态码
    public static final String QRCODE_TYPE_STATIC = "1"; //二维码类型 静态码

    /** 退款查询交易原交易应答码 **/
    public static final String REFUND_QUERY_ORIG_RESP_CODE_SUCCESS = "00"; //退款成功
    public static final String REFUND_QUERY_ORIG_RESP_CODE_FAIL = "01"; //退款失败
    public static final String REFUND_QUERY_ORIG_RESP_CODE_IN_PROG = "02"; //退款处理中

    /** 应答码 **/
    public static final String RESP_CODE_SUCCESS = "00"; //成功
    public static final String RESP_CODE_SYSTEM_ERROR = "01"; //系统错误，交易失败
    public static final String RESP_CODE_ORDER_UNKNOWN  = "04"; // 交易状态未明
    public static final String RESP_CODE_BUSY_RETRY_LATER  = "06"; // 系统繁忙，请稍后再试
    public static final String RESP_CODE_INVALID_REQUEST_FORMAT  = "10"; // 报文格式错误
    public static final String RESP_CODE_INVALID_SIGN  = "11"; // 验证签名失败
    public static final String RESP_CODE_DUPLICATED_TRADE  = "12"; // 重复交易
    public static final String RESP_CODE_LACK_PARAMS  = "13"; // 报文交易要素缺失
    public static final String RESP_CODE_AUTH_CODE_EXPIRE  = "20"; // 二维码已失效
    public static final String RESP_CODE_TRADE_COUNT_LIMITED  = "21"; // 支付次数超限
    public static final String RESP_CODE_AUTH_CODE_STATUS_INVALID  = "22"; // 二维码状态错误
    public static final String RESP_CODE_AUTH_CODE_NOT_EXISTS  = "23"; // 无此二维码
    public static final String RESP_CODE_MERCHANT_OR_AGENT_STATUS_INVALID  = "31"; // 商户或机构状态不正确
    public static final String RESP_CODE_AUTHORITY_DENY  = "32"; // 无此交易权限
    public static final String RESP_CODE_AMOUNT_LIMIT  = "33"; // 交易金额超限
    public static final String RESP_CODE_TRADE_NOT_EXIST  = "34"; // 交易不存在
    public static final String RESP_CODE_TRADE_NOT_EXIST_OR_ERROR = "35"; //交易不存在或状态错误
    public static final String RESP_CODE_TRADE_INFO_ERROR  = "36"; //与原交易不符
    public static final String RESP_CODE_TRADE_OPERATE_LIMIT  = "37"; //已超过最大查询次数或操作过于频繁
    public static final String RESP_CODE_TRADE_RISK_FAIL="38"; //基于风险控制原因失败
    public static final String RESP_CODE_NOT_IN_BUSINESS_TIME="39"; //交易 不在受理时间范围内
    public static final String RESP_CODE_AUTH_FAIL="43"; //无此权限
    public static final String RESP_CODE_CARD_FAIL="60"; //交易失败请咨询发卡行
    public static final String RESP_CODE_CARD_NO_NOT_VALID="61"; //输入的卡号无效，请确认后输入
    public static final String RESP_CODE_BANK_NOT_SUPPORT="62"; //交易失败，发卡银行不支持该商户，请更换其他银行卡
    public static final String RESP_CODE_CARD_STATUS_ERROR="63"; //卡状态不正确
    public static final String RESP_CODE_CARD_MONEY_NOT_ENOUGH ="64"; //卡余额不足
    public static final String RESP_CODE_CARD_PASSWORD_ERROR="65"; //密码错误 或者有效期有误
    public static final String RESP_CODE_CARD_HOLDER_IDENTIFY_FAIL="66"; //持卡人身份验证失败
    public static final String RESP_CODE_PASSWORD_LIMIT="67"; //密码输入超限制
    public static final String RESP_CODE_CARD_NOT_SUPPORT="68"; //您的银行卡暂不支持该业务
    public static final String RESP_CODE_INPUT_TIMEOUT="69"; //输入超限制
    public static final String RESP_CODE_CARD_EXPIRE ="73"; //支付卡已超有效期
    public static final String RESP_CODE_CARD_NOT_OPEN="77"; //银行卡未开通认证支付
    public static final String RESP_CODE_CARD_PERMISSION_LIMIT ="78"; //权限首先
    public static final String RESP_CODE_TRADE_LIMIT="81"; //月累计交易笔数或金额超限
    public static final String RESP_CODE_TRADE_NOT_MATCH_RULE="85"; //交易失败 不满足规则


    public static final Set<String> PAY_RESP_CODE_FAIL_SET = new HashSet<String>(){{
        List<String> codes = Arrays.asList(
                RESP_CODE_SYSTEM_ERROR, RESP_CODE_TRADE_INFO_ERROR,
                RESP_CODE_TRADE_RISK_FAIL, RESP_CODE_AUTH_FAIL, RESP_CODE_NOT_IN_BUSINESS_TIME, RESP_CODE_CARD_FAIL,
                RESP_CODE_CARD_NO_NOT_VALID, RESP_CODE_BANK_NOT_SUPPORT, RESP_CODE_CARD_STATUS_ERROR, RESP_CODE_CARD_MONEY_NOT_ENOUGH,
                RESP_CODE_CARD_PASSWORD_ERROR, RESP_CODE_CARD_HOLDER_IDENTIFY_FAIL, RESP_CODE_PASSWORD_LIMIT, RESP_CODE_CARD_NOT_SUPPORT,
                RESP_CODE_INPUT_TIMEOUT, RESP_CODE_CARD_EXPIRE, RESP_CODE_CARD_NOT_OPEN, RESP_CODE_CARD_PERMISSION_LIMIT,
                RESP_CODE_TRADE_LIMIT, RESP_CODE_TRADE_NOT_MATCH_RULE,
                RESP_CODE_INVALID_REQUEST_FORMAT, RESP_CODE_INVALID_SIGN, RESP_CODE_DUPLICATED_TRADE,
                RESP_CODE_LACK_PARAMS, RESP_CODE_AUTH_CODE_EXPIRE,
                RESP_CODE_TRADE_COUNT_LIMITED, RESP_CODE_AUTH_CODE_STATUS_INVALID, RESP_CODE_AUTH_CODE_NOT_EXISTS,
                RESP_CODE_MERCHANT_OR_AGENT_STATUS_INVALID, RESP_CODE_AUTHORITY_DENY, RESP_CODE_AMOUNT_LIMIT
                );
        addAll(codes);
    }};


    /** 回调支付通知返回 **/
    public static final String NOTIFY_SUCCESS = "success";
    public static final String NOTIFY_FAIL = "fail";

    /**
     * pay接口所允许的字段
     */
    public static final Set<String> PAY_ALLOWED_FIELDS = new HashSet<String>(Arrays.asList(
            "merId", "merCatCode","merName","merNameEng","termId","qrNo","specFeeInfo","currencyCode","txnAmt","orderNo","orderTime","areaInfo","merTransIndex","termInfo","acqAddnData","serProvId","subMerInfo","pnrInsIdCd","discountCode", "idCheckIn", "identityInfo", "idCheckUrl", "encryptCertId"
    ));

    /**
     * wap接口所允许的字段
     */
    public static final Set<String> WAP_ALLOWED_FIELDS = new HashSet<String>(Arrays.asList(
            "orderNo", "orderTime","orderType","payeeInfo","paymentValidTime","orderTimeOut","orderDesc","customerIp","encryptCertId","backUrl","areaInfo","txnAmt","qrCodeType","frontUrl","frontFailUrl","merTransIndex","specFeeInfo","currencyCode","reqReserved","termInfo","userId","acqAddnData","idCheckIn","identityInfo","idCheckUrl","qrCode","pnrInsIdCd"

    ));

    public static final String PAY_CHANNEL_01 = "01"; // 基于小程序模式
    public static final String  MICRO_MESSENGER="MicroMessenger"; //MicroMessenger

}
