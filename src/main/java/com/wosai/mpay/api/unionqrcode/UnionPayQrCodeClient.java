package com.wosai.mpay.api.unionqrcode;

import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/3/4.
 */
public class UnionPayQrCodeClient {

    public static final Logger logger = LoggerFactory.getLogger(UnionPayQrCodeClient.class);
    private static final String CONTENT_TYPE = "application/x-www-form-urlencoded";

    public static final String SIGN_TYPE_UNIONPAY = "unionpay";
    public static final String SIGN_TYPE_HAIKE = "haike";

    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public UnionPayQrCodeClient() {
    }

    public UnionPayQrCodeClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public Map<String,Object> call(String gateway, String signKey, Map request) throws MpayException, IOException {
        return call(gateway, SIGN_TYPE_UNIONPAY, signKey, request);
    }

    @SuppressWarnings("unchecked")
    public Map<String,Object> call(String gateway, String signType, String signKey, Map request) throws MpayException, IOException {
        preProcess(signType, signKey, request);
        String requestStr = JsonUtil.toJsonStr(request);
        logger.debug("request {}", requestStr);
        String responseStr = WebUtils.doPost(null, null, gateway,request, connectTimeout, readTimeout);
        logger.debug("response {}", responseStr);
        try{
            Map<String,Object> result = JsonUtil.jsonStringToObject(responseStr, Map.class);
            processSomeJsonString(result);
            logger.debug("parse response is {}", JsonUtil.objectToJsonString(result));
            return result;
        }catch (Exception e){
            throw new MpayApiUnknownResponse("parse response error: " + e.getMessage(), e);
        }
    }

    public static void preProcess(String signType, String signKey, Map<String,Object> request) throws MpayException {
        request.remove(com.wosai.mpay.api.weixin.ProtocolFields.SIGN);
        String sign = null;
        if (SIGN_TYPE_UNIONPAY.equals(signType)) {
            sign = RsaSignature.sign(request, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, signKey);
            request.put(ProtocolFields.SIGN, sign);
        } else if (SIGN_TYPE_HAIKE.equals(signType)) {
            //海科走前置机做加签, 故 sign 上送 空字符串
            sign = StringUtils.EMPTY;
        }

        request.put(ProtocolFields.SIGN, sign);
    }

    /**
     * 银联开放平台返回的字段里面，有些值是json格式的字符串，需要把这些值转换为对象
     */
    private void processSomeJsonString(Map<String, Object> result) throws MpayException {
        if(result != null && result.containsKey(ResponseFields.PAYER_INFO) && result.get(ResponseFields.PAYER_INFO) instanceof String){
            String payerInfoStr = (String) result.get(ResponseFields.PAYER_INFO);
            result.put(ResponseFields.PAYER_INFO, parseKeyValue(decodeStr(payerInfoStr)));
        }
        if(result != null && result.containsKey(ResponseFields.COUPON_INFO) && result.get(ResponseFields.COUPON_INFO) instanceof String){
            String couponInfoStr = (String) result.get(ResponseFields.COUPON_INFO);
            result.put(ResponseFields.COUPON_INFO, JsonUtil.jsonStringToObject(decodeStr(couponInfoStr), List.class));
        }
    }
    
    private String decodeStr(String str) {
        if(!StringUtils.empty(str)) {
            try {
                return new String(Base64.decode(str));
            }catch (Exception e) {
                logger.info("error parse str:"+ str, e);
            }
        }
        return null;
    }

    private Map<String,Object> parseKeyValue(String content) {
        if (StringUtils.empty(content)) return new HashMap<>();
        if (content.startsWith("{") && content.endsWith("}")) {
            content = content.substring(1, content.length() - 1);
        }
        Map<String, Object> result = new HashMap<>();
        for (String kvStr : content.split("&")) {
            String[] kv = kvStr.split("=");
            if (kv.length == 2) {
                result.put(kv[0], kv[1]);
            }
        }
        return result;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
}
