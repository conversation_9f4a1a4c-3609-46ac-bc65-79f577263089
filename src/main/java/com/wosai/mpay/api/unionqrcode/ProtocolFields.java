package com.wosai.mpay.api.unionqrcode;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/3/4.
 */
public class ProtocolFields {

    public static final String VERSION = "version"; //接口版本号
    public static final String SIGN_TYPE = "signType"; //算法类型
    public static final String SIGN = "sign"; //签名串
    public static final String SIGNATURE = "signature"; //签名串
    public static final String CERT_ID = "certId"; //证书id
    public static final String ENCRYPT_CERT_ID = "encryptCertId"; //加密证书编号, 用于加密的公钥证书的序列号
    public static final String MER_ID = "merId"; //商户号
    public static final String ACQ_CODE="acqCode";//受理机构代码
    public static final String TERM_ID = "termId"; //termId
    public static final String NAME = "name";
    public static final String MER_CAT_CODE="merCatCode";
    public static final String MER_NAME="merName";
    public static final String ID="id";
    public static final String PRN_INS_ID_CD="pnrInsIdCd";

}
