package com.wosai.mpay.api.techtrans;

import com.wosai.mpay.util.SafeSimpleDateFormat;

import java.util.Date;
import java.util.UUID;

public class TechTransUtil {

    public static final SafeSimpleDateFormat DATE_FORMAT_YYYYMMDD = new SafeSimpleDateFormat("yyyyMMdd");
    public static final SafeSimpleDateFormat HHMMSS = new SafeSimpleDateFormat("HHmmss");


    /**
     * Generate a unique CRC operation code
     * 生成唯一的CRC操作码
     *
     * 每次操作（并非每次调用），需要生成对应该接口的独立且唯一的操作码CRC，
     * 服务端将根据接口名与CRC验证是否重复操作
     *
     * @return CRC operation code
     */
    public static String generateCRC() {
        return UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }

    /**
     * Format current date for request
     *
     * @return Formatted date string (yyyyMMdd)
     */
    public static String getCurrentDate() {
        return DATE_FORMAT_YYYYMMDD.format(new Date());
    }

    /**
     * Format current time for request
     *
     * @return Formatted time string (HHmmss)
     */
    public static String getCurrentTime() {
        return HHMMSS.format(new Date());
    }
}
