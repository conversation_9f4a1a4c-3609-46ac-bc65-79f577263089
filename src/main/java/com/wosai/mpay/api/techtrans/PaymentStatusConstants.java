package com.wosai.mpay.api.techtrans;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/4/22.
 * 支付状态常量类
 */
public class PaymentStatusConstants {

    /**
     * 支付成功
     */
    public static final String SUCCESS = "SUCCESS";

    /**
     * 转入退款
     */
    public static final String REFUND = "REFUND";

    /**
     * 未支付
     */
    public static final String NOTPAY = "NOTPAY";

    /**
     * 已关闭
     */
    public static final String CLOSED = "CLOSED";

    /**
     * 用户支付中
     */
    public static final String USERPAYING = "USERPAYING";

    /**
     * 支付失败(其他原因，如银行返回失败)
     */
    public static final String PAYERROR = "PAYERROR";

    /**
     * 判断支付是否已完成（成功、退款、关闭或失败）
     * @param status 支付状态
     * @return 如果支付已完成则返回true，否则返回false
     */
    public static boolean isPaymentCompleted(String status) {
        return SUCCESS.equals(status) || REFUND.equals(status) ||
                CLOSED.equals(status) || PAYERROR.equals(status);
    }

    /**
     * 判断支付是否成功
     * @param status 支付状态
     * @return 如果支付成功则返回true，否则返回false
     */
    public static boolean isPaymentSuccessful(String status) {
        return SUCCESS.equals(status);
    }

    /**
     * 判断支付是否处于进行中状态
     * @param status 支付状态
     * @return 如果支付正在进行中则返回true，否则返回false
     */
    public static boolean isPaymentInProgress(String status) {
        return USERPAYING.equals(status) || NOTPAY.equals(status);
    }


}
