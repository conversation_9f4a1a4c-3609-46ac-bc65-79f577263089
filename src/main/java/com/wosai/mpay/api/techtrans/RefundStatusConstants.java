package com.wosai.mpay.api.techtrans;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/4/22.
 */
public class RefundStatusConstants {
    /**
     * 退款处理中
     */
    public static final String PROCESSING = "PROCESSING";

    /**
     * 退款成功
     */
    public static final String SUCCESS = "SUCCESS";

    /**
     * 退款关闭
     */
    public static final String REFUNDCLOSE = "REFUNDCLOSE";

    /**
     * 退款异常
     */
    public static final String CHANGE = "CHANGE";



    /**
     * 检查退款是否已完成（成功或关闭）
     */
    public static boolean isRefundCompleted(String status) {
        return SUCCESS.equals(status) || REFUNDCLOSE.equals(status);
    }

    /**
     * 检查退款是否成功
     */
    public static boolean isRefundSuccessful(String status) {
        return SUCCESS.equals(status);
    }

    /**
     * 检查退款是否正在处理中
     */
    public static boolean isRefundInProgress(String status) {
        return PROCESSING.equals(status);
    }

}
