package com.wosai.mpay.api.techtrans;

/**
 * TechTrans API Constants for Beijing Daxing Airport
 * 基于科传(TechTrans)提供的大兴机场支付接口文档
 */
public class TechTransConstant {

    // Content type - 内容类型
    public static final String CONTENT_TYPE = "application/json"; // 请求内容类型
    public static final String CHARSET = "utf-8"; // 字符编码

    // API methods - 接口方法
    public static final String METHOD_WX_MINI_ORDER_QUERY = "WxMiniOrderQuery"; // 查询微信小程序订单
    public static final String METHOD_WX_MINI_ORDER_REFUND = "WxMiniOrderRefund"; // 退款申请
    public static final String METHOD_WX_MINI_REFUND_QUERY = "WxMiniRefundQuery"; // 退款查询
    public static final String METHOD_WX_MINI_ORDER_CLOSE = "WxMiniOrderClose"; // 关闭订单

    // Payment types - 支付方式
    public static final String PAY_TYPE_ALIPAY = "Alipay"; // 支付宝
    public static final String PAY_TYPE_WECHAT = "Wechat"; // 微信


    // Response status - 响应状态
    public static final String RESPONSE_STATUS_SUCCESS = "0"; // 接口调用成功状态码
    public static final String CALLBACK_SUCCESS_RESPONSE = "SUCCESS"; // 回调通知成功响应(启用多次通知时需返回此值)

    // Field order lists for API requests - API请求字段顺序列表

    // WxMiniOrderQuery field order - 查询微信小程序订单字段顺序
    public static final String[] WX_MINI_ORDER_QUERY_FIELDS = {
        TechTransRequestFields.SELLER_ID,
        TechTransRequestFields.STORE_CODE,
        TechTransRequestFields.MALL_ID,
        TechTransRequestFields.OUT_ID,
        TechTransRequestFields.TRADE_NO
    };

    // WxMiniOrderRefund field order - 退款申请字段顺序
    public static final String[] WX_MINI_ORDER_REFUND_FIELDS = {
        TechTransRequestFields.SELLER_ID,
        TechTransRequestFields.STORE_CODE,
        TechTransRequestFields.MALL_ID,
        TechTransRequestFields.BS_STORE_CODE,
        TechTransRequestFields.REFUND_OUT_ID,
        TechTransRequestFields.OUT_ID,
        TechTransRequestFields.TRADE_NO,
        TechTransRequestFields.REFUND_AMOUNT,
        TechTransRequestFields.TOTAL_FEE
    };

    // WxMiniRefundQuery field order - 退款查询字段顺序
    public static final String[] WX_MINI_REFUND_QUERY_FIELDS = {
        TechTransRequestFields.SELLER_ID,
        TechTransRequestFields.STORE_CODE,
        TechTransRequestFields.MALL_ID,
        TechTransRequestFields.REFUND_OUT_ID,
        TechTransRequestFields.REFUND_TRADE_NO
    };

    // WxMiniOrderClose field order - 关闭订单字段顺序
    public static final String[] WX_MINI_ORDER_CLOSE_FIELDS = {
        TechTransRequestFields.SELLER_ID,
        TechTransRequestFields.STORE_CODE,
        TechTransRequestFields.MALL_ID,
        TechTransRequestFields.OUT_ID
    };

    // Callback notification field order lists - 回调通知字段顺序列表

    // Payment result notification field order - 支付结果通知字段顺序
    public static final String[] PAYMENT_NOTIFICATION_FIELDS = {
        TechTransResponseFields.TOTAL_FEE,
        TechTransResponseFields.ACTUAL_FEE,
        TechTransResponseFields.COUPON_FEE,
        TechTransResponseFields.DISCOUNT_AMT,
        TechTransResponseFields.TRADE_NO,
        TechTransResponseFields.OUT_ID,
        TechTransResponseFields.BUYER_ID,
        TechTransResponseFields.PAY_TIME
    };

    // Refund result notification field order - 退款结果通知字段顺序
    public static final String[] REFUND_NOTIFICATION_FIELDS = {
        TechTransResponseFields.TOTAL_FEE,
        TechTransResponseFields.ACTUAL_FEE,
        TechTransResponseFields.TRADE_NO,
        TechTransResponseFields.OUT_ID,
        TechTransResponseFields.REFUND_OUT_ID,
        TechTransResponseFields.REFUND_TRADE_NO,
        TechTransResponseFields.REFUND_FEE,
        TechTransResponseFields.ACTUAL_REFUND_FEE,
        TechTransResponseFields.REFUND_STATUS,
        TechTransResponseFields.SUCCESS_TIME
    };

}
