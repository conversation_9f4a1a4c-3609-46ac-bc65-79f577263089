package com.wosai.mpay.api.techtrans;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static com.wosai.mpay.api.techtrans.TechTransUtil.*;

/**
 * TechTrans API Client for Beijing Daxing Airport
 * 大兴机场科传支付接口客户端
 *
 * 基于科传(TechTrans)提供的大兴机场支付接口文档实现
 * 支持统一订单查询、统一退款接口、统一退款查询等功能
 * 支持支付宝和微信支付方式
 */
public class TechTransClient {
    private static final Logger logger = LoggerFactory.getLogger(TechTransClient.class);

    private int connectTimeout = 1000;
    private int readTimeout = 5000;


    public TechTransClient() {
    }

    public TechTransClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    /**
     * Call TechTrans API with ordered fields
     * 使用有序字段调用科传API
     *
     * 所有接口都是以POST方式调用，请勿在url中携带请求参数
     *
     * @param method API method name 接口方法名
     * @param url API url
     * @param data Request data parameters 请求数据参数
     * @param user API user 请求用户
     * @param signKey Signature key 签名密钥
     * @param orderedFields Array of field names in the order they should be processed 按顺序处理的字段名称数组
     * @return API response as Map 以Map形式返回API响应
     * @throws MpayException if request fails 如果请求失败
     * @throws MpayApiNetworkError if network error occurs 如果发生网络错误
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> call(String method, String url, Map<String, Object> data, String user, String signKey, String[] orderedFields)
            throws MpayException, MpayApiNetworkError {
        // Prepare request parameters
        String crc = generateCRC();
        String reqDate = getCurrentDate();
        String reqTime = getCurrentTime();

        // Create a new LinkedHashMap to preserve the specified order
        Map<String, Object> orderedData = new LinkedHashMap<>();

        // First add fields in the specified order
        for (String field : orderedFields) {
            if (data.containsKey(field)) {
                orderedData.put(field, data.get(field));
            }
        }

        // Build request with LinkedHashMap to ensure ordered keys
        Map<String, Object> request = new LinkedHashMap<>();
        request.put(TechTransRequestFields.REQ_DATE, reqDate);
        request.put(TechTransRequestFields.REQ_TIME, reqTime);
        request.put(TechTransRequestFields.METHOD, method);
        request.put(TechTransRequestFields.USER, user);
        request.put(TechTransRequestFields.CRC, crc);

        // Generate signature
        String sign = TechTransSignature.signWithOrderedFields(method, orderedData, crc, signKey, orderedFields);
        request.put(TechTransRequestFields.SIGN, sign);
        request.put(TechTransRequestFields.DATA, orderedData);


        // Convert request to JSON using ordered serialization
        String requestJson = JsonUtil.objectToJsonString(request);
        logger.debug("request {}", requestJson);
        // Call API
        Map<String, String> headers = new LinkedHashMap<>();
        headers.put("Content-Type", TechTransConstant.CONTENT_TYPE);
        String charset = TechTransConstant.CHARSET;
        String responseJson = HttpClientUtils.doPost(TechTransClient.class.getName(), null, null, url, TechTransConstant.CONTENT_TYPE, requestJson, headers, charset, connectTimeout, readTimeout);
        logger.debug("response {}", responseJson);

        // Parse response
        Map<String, Object> response;
        try {
            response = JsonUtil.jsonStringToObject(responseJson, Map.class);
        } catch (Exception e) {
            throw new MpayApiUnknownResponse("Parse response error: " + e.getMessage(), e);
        }
        // 注意, 如果没有错误的话，响应结果中包含 data, 则将其展开
        Map responseData = MapUtils.getMap(response, TechTransResponseFields.DATA);
        if (responseData != null){
            response.putAll(responseData);
            response.remove(TechTransResponseFields.DATA);
        }
        return response;
    }


    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }


}
