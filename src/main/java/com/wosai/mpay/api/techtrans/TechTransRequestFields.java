package com.wosai.mpay.api.techtrans;

/**
 * TechTrans API Request Fields for Beijing Daxing Airport
 * 大兴机场科传支付接口请求字段
 */
public class TechTransRequestFields {
    // Common request fields - 公共请求字段
    public static final String REQ_DATE = "reqdate"; // 请求日期，格式：yyyyMMdd
    public static final String REQ_TIME = "reqtime"; // 请求时间，格式：HHmmss
    public static final String METHOD = "method"; // 调用的接口名
    public static final String USER = "user"; // 请求用户
    public static final String CRC = "crc"; // 操作码，每次操作需要生成唯一的操作码
    public static final String SIGN = "sign"; // 签名MD5串，不区分大小写
    public static final String DATA = "data"; // 请求的参数的JSON对象

    // Order creation fields - 订单创建字段
    public static final String SELLER_ID = "SellerID"; // 收款账户
    public static final String ORDER_TITLE = "OrderTitle"; // 订单标题
    public static final String OUT_ID = "OutID"; // 商家订单号，长度不超过20位
    public static final String TOTAL_FEE = "TotalFee"; // 订单金额，单位分
    public static final String MALL_ID = "MallID"; // 商场号
    public static final String STORE_CODE = "StoreCode"; // 店铺号
    public static final String BS_STORE_CODE = "BsStoreCode"; // 合同店铺号(未使用到则直接填写为店铺号)
    public static final String RETURN_URL = "ReturnUrl"; // 支付成功后跳转到的url地址
    public static final String NEED_INVOICE = "NeedInvoice"; // 是否开票(true/false)
    public static final String EX_PARAM = "ExParam"; // 扩展参数，按需填写，格式为JSON格式字符串
    public static final String CALLBACK_URL = "CallbackUrl"; // 回调地址(不填则不回调)
    public static final String NO_OUT_ID = "NoOutID"; // 值为1时跳转ReturnUrl时不追加OutID
    public static final String NOTIFY_AGAIN = "NotifyAgain"; // 启用多次支付结果通知(true/false)

    // Order query fields - 订单查询字段
    public static final String TRADE_NO = "TradeNO"; // 收单行交易流水号
    public static final String PAY_TYPE = "PayType"; // 支付方式，Alipay-支付宝，Wechat-微信

    // Refund fields - 退款字段
    public static final String REFUND_OUT_ID = "RefundOutID"; // 商家退款单号
    public static final String REFUND_FEE = "RefundFee"; // 退款金额，单位分
    public static final String REFUND_TRADE_NO = "RefundTradeNO"; // 退款收单行交易流水号
    public static final String REFUND_AMOUNT = "RefundAmount"; // 当次要求退款金额，单位是RMB分

    // Invoice fields (for ExParam) - 发票字段(用于ExParam)
    public static final String INVOICE_MERCHANT_NAME = "invoice_merchant_name"; // 品牌简称|商户简称 不能带空格,|必须要英文竖线
    public static final String INVOICE_TAX_NUM = "invoice_tax_num"; // 税号
}
