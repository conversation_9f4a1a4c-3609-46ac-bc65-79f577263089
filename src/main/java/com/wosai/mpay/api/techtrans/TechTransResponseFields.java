package com.wosai.mpay.api.techtrans;

/**
 * TechTrans API Response Fields for Beijing Daxing Airport
 * 大兴机场科传支付接口响应字段
 */
public class TechTransResponseFields {
    // Common response fields - 公共响应字段
    public static final String RES_DATE = "resdate"; // 响应日期
    public static final String RES_TIME = "restime"; // 响应时间
    public static final String METHOD = "method"; // 调用的接口名
    public static final String STATUS = "status"; // 状态码，0表示成功
    public static final String MSG = "msg"; // 异常消息
    public static final String DATA = "data"; // 返回结果，格式由各接口定义

    // Order query response fields - 订单查询响应字段
    public static final String TOTAL_FEE = "TotalFee"; // 总金额，单位是RMB分
    public static final String ACTUAL_FEE = "ActualFee"; // 实际金额，单位是RMB分
    public static final String COUPON_FEE = "CouponFee"; // 优惠券金额，单位是RMB分
    public static final String DISCOUNT_AMT = "DiscountAmt"; // 折扣金额，单位是RMB分
    public static final String TRADE_NO = "TradeNO"; // 收单行交易流水号
    public static final String OUT_ID = "OutID"; // 外部订单号
    public static final String BUYER_ID = "BuyerID"; // 买家ID
    public static final String ORDER_STATUS = "OrderStatus"; // 交易状态，10—待支付，30—支付成功，70—已取消，90—已关闭
    public static final String TIME_END = "TimeEnd"; // 支付完成时间，yyyy-MM-dd HH:mm:ss

    // Refund response fields - 退款响应字段
    public static final String REFUND_OUT_ID = "RefundOutID"; // 退货外部订单号
    public static final String REFUND_TRADE_NO = "RefundTradeNO"; // 退货收单行交易流水号
    public static final String REFUND_FEE = "RefundFee"; // 申请退款金额
    public static final String ACTUAL_REFUND_FEE = "ActualRefundFee"; // 退款金额
    public static final String REFUND_STATUS = "RefundStatus"; // 退款状态，SUCCESS-退款成功，CHANGE-退款异常，REFUNDCLOSE—退款关闭

    // Callback notification fields - 回调通知字段
    public static final String SELLER_ID = "SellerID"; // 收款账户
    public static final String PAY_TIME = "PayTime"; // 支付完成时间，yyyy-MM-dd HH:mm:ss
    public static final String SUCCESS_TIME = "SuccessTime"; // 退款成功时间，yyyy-MM-dd HH:mm:ss
}
