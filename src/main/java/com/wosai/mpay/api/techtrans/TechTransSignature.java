package com.wosai.mpay.api.techtrans;

import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Digest;

import java.util.Map;

/**
 * TechTrans API Signature Generator for Beijing Daxing Airport
 * 大兴机场科传支付接口签名生成器
 *
 * 签名方式：
 * 1：按以下方式拼接获得字符串str,将data的属性和值拼接成dstr：(参数名1+参数值1+参数名2+参数值2…)
 * 2：拼接原始字符串：接口名+dstr+crc+签名秘钥
 * 3：将得到的原始字符串进行MD5运算，获得32位小写的md5的sign字符串
 */
public class TechTransSignature {
    private static final String CHARSET = "UTF-8";


    /**
     * Generate signature for TechTrans API using ordered fields
     *
     * 1. Concatenate data parameters in the specified order: (param1+value1+param2+value2...)
     * 2. Concatenate original string: method+dataStr+crc+signKey
     * 3. Calculate MD5 hash of the original string
     *
     * @param method API method name
     * @param data Request data parameters
     * @param crc Operation code
     * @param signKey Signature key
     * @param orderedFields Array of field names in the order they should be processed
     * @return MD5 signature
     * @throws MpayException if signature generation fails
     */
    public static String signWithOrderedFields(String method, Map<String, Object> data, String crc, String signKey, String[] orderedFields) throws MpayException {
        try {
            // Step 1: Concatenate data parameters in the specified order
            StringBuilder dataStr = new StringBuilder();
            for (String fieldName : orderedFields) {
                Object value = data.get(fieldName);
                if (value != null) {
                    dataStr.append(fieldName).append(value);
                }
            }

            // Step 2: Concatenate original string
            String originalStr = method + dataStr.toString() + crc + signKey;
            // Step 3: Calculate MD5 hash
            return Digest.md5(originalStr.getBytes(CHARSET)).toLowerCase();
        } catch (Exception e) {
            throw new MpayException("Failed to generate TechTrans signature with ordered fields", e);
        }
    }

    /**
     * Verify signature from TechTrans API response
     *
     * @param method API method name
     * @param data Response data parameters
     * @param crc Operation code
     * @param signKey Signature key
     * @param orderedFields Array of field names in the order they should be processed
     * @param signature Signature to verify
     * @return true if signature is valid, false otherwise
     * @throws MpayException if signature verification fails
     */
    public static boolean verify(String method, Map<String, Object> data, String crc, String signKey, String[] orderedFields, String signature) throws MpayException {
        String calculatedSignature = signWithOrderedFields(method, data, crc, signKey, orderedFields);
        return calculatedSignature.equalsIgnoreCase(signature);
    }
}
