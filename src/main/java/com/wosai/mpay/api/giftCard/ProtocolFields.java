package com.wosai.mpay.api.giftCard;

public class ProtocolFields {

    public static final String APP_ID = "app_id";
    public static final String METHOD = "method";
    public static final String FORMAT = "format";
    public static final String CHARSET = "charset";
    public static final String SIGN_TYPE = "sign_type";
    public static final String SIGN = "sign";
    public static final String TIMESTAMP = "timestamp";
    public static final String VERSION = "version";
    public static final String NOTIFY_URL="notify_url";
    public static final String BIZ_CONTENT = "biz_content";

    public static final String GIFTCARD_REDEEM_RESPONSE = "giftcard_redeem_response";
    public static final String GIFTCARD_REDEEM_QUERY_RESPONSE = "giftcard_redeem_query_response";
    public static final String GIFTCARD_REFUND_RESPONSE = "giftcard_refund_response";
    public static final String GIFTCARD_REFUND_QUERY_RESPONSE = "giftcard_refund_query_response";
    public static final String GIFTCARD_REDEEM_CANCEL_RESPONSE = "giftcard_redeem_cancel_response";
}
