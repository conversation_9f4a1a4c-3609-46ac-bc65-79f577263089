package com.wosai.mpay.api.giftCard;

public class BusinessFields {

    public static final String REFUND_CURRENCY = "refund_currency";
    public static final String OUT_TRADE_NO = "out_trade_no";
    public static final String SCENE = "scene";
    public static final String AUTH_CODE = "auth_code";
    public static final String AUTH_NO = "auth_no"; //资金授权单号
    public static final String PRODUCT_CODE = "product_code";
    public static final String SELLER_ID = "seller_id";
    public static final String TOTAL_AMOUNT = "total_amount";
    public static final String TRANS_CURRENCY = "trans_currency";    //标价币种, total_amount 对应的币种单位。目前仅支持 人民币:CNY
    public static final String SETTLE_CURRENCY = "settle_currency";   //商户指定的结算币种，目前仅 支持人民币:CNY
    public static final String SETTLE_AMOUNT = "settle_amount";   //结算币种订单金额
    public static final String DISCOUTABLE_AMOUNT = "discountable_amount";
    public static final String UNDISCOUNTABLE_AMOUNT = "undiscountable_amount";
    public static final String SUBJECT = "subject";
    public static final String BODY = "body";
    public static final String GOODS_DETAIL = "goods_detail";
    public static final String OPERATOR_ID = "operator_id";
    public static final String STORE_ID = "store_id";
    public static final String ALIPAY_STORE_ID = "alipay_store_id";
    public static final String TERMINAL_ID = "terminal_id";
    public static final String EXTEND_PARAMS = "extend_params";
    public static final String TIME_EXPIRE = "time_expire";
    public static final String TIMEOUT_EXPRESS = "timeout_express"; //该笔订单允许的最晚付款时间，逾期将关闭交易。取值范围：1m～15d。m-分钟，h-小时，d-天，1c-当天（1c-当天的情况下，无论交易何时创建，都在0点关闭）。 该参数数值不接受小数点， 如 1.5h，可转换为 90m。
    public static final String AGREEMENT_PARAMS = "agreement_params";  // 代扣业务需要传入协议相关 信息
    public static final String QR_CODE_TIMEOUT_EXPRESS = "qr_code_timeout_express"; //该笔订单允许的最晚付款时间，逾期将关闭交易。取值范围：1m～15d。m-分钟，h-小时，d-天，1c-当天（1c-当天的情况下，无论交易何时创建，都在0点关闭）。 该参数数值不接受小数点， 如 1.5h，可转换为 90m。
    public static final String DISABLE_PAY_CHANNELS = "disable_pay_channels"; //禁用渠道，用户不可用指定渠道支付当有多个渠道时用“,”分隔注，与enable_pay_channels互斥
    public static final String ENABLE_PAY_CHANNELS = "enable_pay_channels"; //可用渠道，用户只能在指定渠道范围内支付当有多个渠道时用“,”分隔注，与disable_pay_channels互斥
    public static final String MERCHANT_ORDER_NO = "merchant_order_no";   //商户的原始订单号
    public static final String EXT_USER_INFO = "ext_user_info";     //外部指定买家
    public static final String AUTH_CONNFIRM_MODE = "auth_confirm_mode";     //预授权确认模式，授权转交易 请求中传入，适用于预授权转 交易业务使用目前只支持 PRE_AUTH(预授权产品码) COMPLETE:转交易支付完成 结束 预 授 权 ， 解 冻 剩 余 金 额 ;NOT_COMPLETE: 转交易支付完成不结束预授 权，不解冻剩余金额
    public static final String TERMINAL_PARAMS = "terminal_params";    //json 格式;商户传入终端设 备。
    public static final String BUSINESS_PARAMS = "business_params";    //商户传入业务信息，应用于安 全，营销等参数直传场景，格 式为 json 格式。
    public static final String ROYALTY_INFO = "royalty_info";
    public static final String QR_CODE = "qr_code";
    public static final String TRADE_NO = "trade_no";
    public static final String OPEN_ID = "open_id";
    public static final String BUYER_LOGON_ID = "buyer_logon_id";
    public static final String BUYER_USER_ID = "buyer_user_id";
    public static final String BUYER_ID = "buyer_id";
    public static final String TRADE_STATUS = "trade_status";
    public static final String RECEIPT_AMOUNT = "receipt_amount";
    public static final String INVOICE_AMOUNT = "invoice_amount";
    public static final String BUYER_PAY_AMOUNT = "buyer_pay_amount";
    public static final String POINT_AMOUNT = "point_amount";
    public static final String SEND_PAY_DATE = "send_pay_date";
    public static final String GMT_PAYMENT = "gmt_payment";
    public static final String STORE_NAME = "store_name";
    public static final String FUND_BILL_LIST = "fund_bill_list";
    public static final String FUND_CHANNEL = "fund_channel";
    public static final String FUNDCHANNEL = "fundChannel";
    public static final String FUNDTYPE = "fundType";
    public static final String FUND_TYPE = "fund_type";
    public static final String AMOUNT = "amount";
    public static final String RETRY_FLAG = "retry_flag";
    public static final String ACTION = "action";
    public static final String OUT_REQUEST_NO = "out_request_no";
    public static final String REFUND_AMOUNT = "refund_amount";
    public static final String REFUND_REASON = "refund_reason";
    public static final String FUND_CHANGE = "fund_change";
    public static final String REFUND_FEE = "refund_fee";
    public static final String GMT_REFUND_PAY = "gmt_refund_pay";
    public static final String REFUND_DETAIL_ITEM_LIST = "refund_detail_item_list";

    public static final String IMAGE_ID = "image_id";
    public static final String STATUS_CODE = "status_code";
    public static final String STATUS_DESC = "status_desc";
    public static final String AUDIT_STATUS = "audit_status";
    public static final String AUDIT_DESC = "audit_desc";
    public static final String IS_ONLINE = "is_online";

    public static final String SHOP_ID = "shop_id";
    public static final String SHOP_ID_C = "shopId";
    public static final String STORE_ID_C = "storeId";
    public static final String CATEGORY_ID = "categoryId";
    public static final String CATEGORY_ID_C = "categoryId";
    public static final String BRAND_NAME = "brandName";
    public static final String BRAND_NAME_C = "brandName";
    public static final String BRAND_LOGO = "brandLogo";
    public static final String BRAND_LOGO_C = "brandLogo";
    public static final String SLOGAN = "slogan";
    public static final String MAIN_SHOP_NAME = "mainShopName";
    public static final String MAIN_SHOP_NAME_C = "mainShopName";
    public static final String BRANCH_SHOP_NAME = "branchShopName";
    public static final String BRANCH_SHOP_NAME_C = "branchShopName";
    public static final String PROVINCE_CODE = "provinceCode";
    public static final String PROVINCE_CODE_C = "provinceCode";
    public static final String CITY_CODE = "cityCode";
    public static final String CITY_CODE_C = "cityCode";
    public static final String DISTRICT_CODE = "districtCode";
    public static final String DISTRICT_CODE_C = "districtCode";
    public static final String ADDRESS = "address";
    public static final String LONGITUDE = "longitude";
    public static final String LATITUDE = "latitude";
    public static final String PHONE = "phone";
    public static final String MOBILE = "mobile";
    public static final String MAIN_IMAGE = "mainImage";
    public static final String MAIN_IMAGE_C = "mainImage";
    public static final String AUDIT_IMAGES = "auditImages";
    public static final String AUDIT_IMAGES_C = "auditImages";
    public static final String OPENING_HOURS = "openingHours";
    public static final String OPENING_HOURS_C = "openingHours";
    public static final String WIFI = "wifi";
    public static final String PARKING = "parking";
    public static final String VALUE_ADDED = "valueAdded";
    public static final String VALUE_ADDED_C = "valueAdded";
    public static final String AVERAGE_PRICE = "averagePrice";
    public static final String AVERAGE_PRICE_C = "averagePrice";
    public static final String ISV_PID = "isvPid";
    public static final String ISV_PID_C = "isvPid";
    public static final String LICENCE = "licence";
    public static final String LICENCE_CODE = "licenceCode";
    public static final String LICENCE_CODE_C = "licenceCode";
    public static final String LICENCE_NAME = "licenceName";
    public static final String LICENCE_NAME_C = "licenceName";
    public static final String AUTH_LETTER = "authLetter";
    public static final String AUTH_LETTER_C = "authLetter";
    public static final String IS_OPERATING_ONLINE = "isOperatingOnline";
    public static final String IS_OPERATING_ONLINE_C = "isOperatingOnline";
    public static final String ONLINE_IMAGE = "onlineImage";
    public static final String ONLINE_IMAGE_C = "onlineImage";
    public static final String NOTIFY_URL = "notifyUrl";
    public static final String IS_SHOW = "is_show";
    public static final String IS_SHOW_C = "isShow";


    public static final String CODE = "code";
    public static final String MSG = "msg";
    public static final String SUB_CODE = "sub_code";
    public static final String SUB_MSG = "sub_msg";

    public static final String NOTIFY_ACTION_TYPE = "notify_action_type";

    public static final String SUB_MERCHANT = "sub_merchant";
    public static final String MERCHANT_ID = "merchant_id";

    public static final String ACCOUNT_ID = "account_id";


}