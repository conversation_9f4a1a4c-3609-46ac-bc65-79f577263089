package com.wosai.mpay.api.giftCard;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.BuilderException;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;


public class RequestBuilder {
    private static ObjectMapper om = new ObjectMapper();
    private Map<String, String> request;
    private Map<String, Object> bizContent;

    public RequestBuilder() {
        request = new TreeMap<String, String>();
        bizContent = new LinkedHashMap<String, Object> ();
        DateFormat df = new SimpleDateFormat(GiftCardConstant.DATE_TIME_FORMAT);
        df.setTimeZone(TimeZone.getTimeZone(GiftCardConstant.DATE_TIMEZONE));
        request.put(ProtocolFields.TIMESTAMP, df.format(new Date()));
        request.put(ProtocolFields.VERSION, "1.0");
    }

    public Map<String, String> build() throws BuilderException {
        if (!request.containsKey(ProtocolFields.METHOD)) {
            throw new BuilderException();
        }
        if (!bizContent.isEmpty()) {
            try {
                request.put(ProtocolFields.BIZ_CONTENT, om.writeValueAsString(bizContent));
            }
            catch (JsonProcessingException e) {
                throw new BuilderException("unable to serialize bizContent in JSON format.", e);
            }
        }
        return request;
    }
    
    public void set(String field, String value) {
        request.put(field,  value);
    }
    
    public void bizSet(String field, Object value) {
        bizContent.put(field, value);
    }
}
