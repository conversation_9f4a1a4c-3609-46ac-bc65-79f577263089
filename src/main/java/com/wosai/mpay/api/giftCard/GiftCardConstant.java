package com.wosai.mpay.api.giftCard;

import java.util.Arrays;
import java.util.List;

public class GiftCardConstant {

    /** 默认时间格式 **/
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**  Date默认时区 **/
    public static final String DATE_TIMEZONE    = "GMT+8";

    /** 默认编码格式 **/
    public static final String CHARSET_UTF8 = "utf-8";

    /** 条码支付 **/
    public static final String SCENE_BAR_CODE = "bar_code";


    /**礼品卡支付结果返回码*/
    public static final String RETURN_CODE_SUCCESS = "10000";//业务全部处理成功
    public static final String RETURN_CODE_INPROG = "10003";//支付中
    public static final String RETURN_CODE_UNKNOW ="20000";//业务出现未知错误或者系统异常,如果支付接口返回，需要调用查询接口确认订单状态或者发起撤销
    public static final String RETURN_CODE_MISSING_PARAM = "40001";//缺少参数
    public static final String RETURN_CODE_ILLEGAL_PARAM = "40002";//非法参数
    public static final String RETURN_CODE_FAIL = "40004";//业务处理失败

    /**交易状态*/
    public static final String TRADE_STATUS_WAIT_BUYER_PAY = "WAIT_BUYER_PAY";
    public static final String TRADE_STATUS_TRADE_CLOSED = "TRADE_CLOSED";
    public static final String TRADE_STATUS_TRADE_SUCCESS = "TRADE_SUCCESS";
    public static final String TRADE_STATUS_TRADE_FINISHED = "TRADE_FINISHED";
    public static final String TRADE_STATUS_TRADE_PENDING = "TRADE_PENDING";

    /**subCode*/
    public static final String RESULT_CODE_QUERY_TRADE_NOT_EXIST = "ACQ.TRADE_NOT_EXIST";
    public static final String RESULT_CODE_INVALID_PARAMETER = "ACQ.INVALID_PARAMETER";
    public static final String RESULT_CODE_SYSTEM_ERROR = "ACQ.SYSTEM_ERROR";
    public static final String RESULT_CODE_ACCESS_FORBIDDEN = "ACQ.ACCESS_FORBIDDEN";
    public static final String RESULT_CODE_TOTAL_FEE_EXCEED = "ACQ.TOTAL_FEE_EXCEED";
    public static final String RESULT_CODE_PAYMENT_AUTH_CODE_INVALID = "ACQ.PAYMENT_AUTH_CODE_INVALID";
    public static final String RESULT_CODE_TRADE_HAS_SUCCESS = "ACQ.TRADE_HAS_SUCCESS";
    public static final String RESULT_CODE_TRADE_HAS_CLOSE = "ACQ.TRADE_HAS_CLOSE";
    public static final String RESULT_CODE_BUYER_ENABLE_STATUS_FORBID = "ACQ.BUYER_ENABLE_STATUS_FORBID";
    public static final String RESULT_CODE_PAYMENT_FAIL = "ACQ.PAYMENT_FAIL";
    public static final String RESULT_CODE_SELLER_BEEN_BLOCKED = "ACQ.SELLER_BEEN_BLOCKED";
    public static final String RESULT_CODE_INVALID_STORE_ID = "ACQ.INVALID_STORE_ID";
    public static final String RESULT_CODE_AMOUNT_OR_CURRENCY_ERROR = "ACQ.AMOUNT_OR_CURRENCY_ERROR";
    public static final String RESULT_CODE_CURRENCY_NOT_SUPPORT = "ACQ.AMOUNT_OR_CURRENCY_ERROR";

    public static final String RESULT_CODE_REFUND_AMT_NOT_EQUAL_TOTAL = "ACQ.REFUND_AMT_NOT_EQUAL_TOTAL";
    public static final String RESULT_CODE_REASON_TRADE_BEEN_FREEZEN = "ACQ.REASON_TRADE_BEEN_FREEZEN";
    public static final String RESULT_CODE_TRADE_NOT_EXIST = "ACQ.TRADE_NOT_EXIST";
    public static final String RESULT_CODE_TRADE_HAS_FINISHED = "ACQ.TRADE_HAS_FINISHED";
    public static final String RESULT_CODE_TRADE_STATUS_ERROR = "ACQ.TRADE_STATUS_ERROR";
    public static final String RESULT_CODE_DISCORDANT_REPEAT_REQUEST = "ACQ.TRADE_STATUS_ERROR";
    public static final String RESULT_CODE_REASON_TRADE_REFUND_FEE_ERR = "ACQ.REASON_TRADE_REFUND_FEE_ERR";
    public static final String RESULT_CODE_REASON_TRADE_NOT_ALLOW_REFUND = "ACQ.TRADE_NOT_ALLOW_REFUND";
    public static final String RESULT_CODE_REASON_REFUND_FEE_ERROR = "ACQ.TRADE_NOT_ALLOW_REFUND";


    /** 礼品卡错误码**/
    public static final String RETURN_CODE_MISSING_METHOD = "isv.missing-method";
    public static final String RETURN_CODE_MISSING_SIGN = "isv.missing-signature";
    public static final String RETURN_CODE_MISSING_SIGN_TYPE = "isv.missing-signature-type";
    public static final String RETURN_CODE_MISSING_SIGN_KEY = "isv.missing-signature-key";
    public static final String RETURN_CODE_MISSING_APP_ID = "isv.missing-app-id";
    public static final String RETURN_CODE_MISSING_TIMESTAMP = "isv.missing-timestamp";
    public static final String RETURN_CODE_MISSING_VERSION = "isv.missing-version";
    public static final String RETURN_CODE_MISSING_ENCRPT_TYPE = "isv.decryption-error-missing-encrypt-type";

    public static final String RETURN_CODE_INVALID_PARAM = "isv.invalid-parameter";
    public static final String RETURN_CODE_INVALID_METHOD = "isv.invalid-method";
    public static final String RETURN_CODE_INVALID_FORMAT = "isv.invalid-format";
    public static final String RETURN_CODE_INVALID_SIGN_TYPE = "isv.invalid-signature-type";
    public static final String RETURN_CODE_INVALID_SIGN = "isv.invalid-signature";
    public static final String RETURN_CODE_INVALID_ENCRYPT_TYPE = "isv.invalid-encrypt-type";
    public static final String RETURN_CODE_INVALID_ENCRYPT = "isv.invalid-encrypt";
    public static final String RETURN_CODE_INVALID_APP_ID = "isv.invalid-app-id";
    public static final String RETURN_CODE_INVALID_TIMESTAMP = "isv.invalid-timestamp";
    public static final String RETURN_CODE_INVALID_CHARSET = "isv.invalid-charset";
    public static final String RETURN_CODE_DECRYPTION_ERROR_NOT_VAILD_ENCRYPT_TYPE = "isv.decryption-error-not-valid-encrypt-type";
    public static final String RETURN_CODE_DECRYPTION_ERROR_NOT_VAILD_ENCRYPT_KEY = "isv.decryption-error-not-valid-encrypt-key";
    public static final String RETURN_CODE_DECRYPTION_ERROR_UNKNOWN = "isv.decryption-error-unknown";
    public static final String RETURN_CODE_MISSING_SIGN_CONFIG= "isv.missing-signature-config";

    public static final String ACTION_CLOSE = "close";
    public static final String ACTION_REFUND = "refund";

    public static final List<String> PAY_FAIL_ERR_CODE_LISTS = Arrays.asList(RESULT_CODE_INVALID_PARAMETER, RESULT_CODE_ACCESS_FORBIDDEN
            , RESULT_CODE_TOTAL_FEE_EXCEED, RESULT_CODE_PAYMENT_AUTH_CODE_INVALID, RESULT_CODE_TRADE_HAS_CLOSE
            , RESULT_CODE_BUYER_ENABLE_STATUS_FORBID, RESULT_CODE_PAYMENT_FAIL, RESULT_CODE_SELLER_BEEN_BLOCKED
            , RESULT_CODE_INVALID_STORE_ID, RESULT_CODE_AMOUNT_OR_CURRENCY_ERROR, RESULT_CODE_CURRENCY_NOT_SUPPORT);

}
