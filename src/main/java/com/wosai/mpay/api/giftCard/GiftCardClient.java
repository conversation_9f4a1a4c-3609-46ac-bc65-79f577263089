package com.wosai.mpay.api.giftCard;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.RsaSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class GiftCardClient {

    private static final Logger logger = LoggerFactory.getLogger(GiftCardClient.class);

    protected static final ObjectMapper om = new ObjectMapper();

    protected int connectTimeout = 1000;
    protected int readTimeout = 5000;

    private static final String SIGN_TYPE_RSA2 = "RSA2";
    private static final String SIGN_TYPE_RSA = "RSA";

    public Map<String,Object> call(String gatewayUrl, String privateKey, Map<String, String> request) throws MpayException, MpayApiNetworkError {
        request.remove(ProtocolFields.SIGN);
        for (String key:request.keySet().toArray(new String[0])){
            if(request.get(key) == null || "".equals(request.get(key))){
                request.remove(key);
            }
        }
        String sign = null;
        String signType = request.get(ProtocolFields.SIGN_TYPE);
        if (SIGN_TYPE_RSA.equals(signType)) {
            sign = RsaSignature.sign(request, RsaSignature.SIG_ALG_NAME_SHA1_With_RSA, privateKey);
        } else if (SIGN_TYPE_RSA2.equals(signType)) {
            sign = RsaSignature.sign(request, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, privateKey);
        }
        request.put(ProtocolFields.SIGN, sign);
        logger.debug("request {}", request);
        try {
            String resp = HttpClientUtils.doPost(GiftCardClient.class.getName(), null, null, gatewayUrl, request, GiftCardConstant.CHARSET_UTF8, connectTimeout, readTimeout);
            logger.debug("response:{}",resp);
            return  om.readValue(resp, new TypeReference<Map<String,Object>>() {});
        } catch (Exception e) {
            throw new MpayApiUnknownResponse("error invoking giftCard api", e);
        }
    }
}
