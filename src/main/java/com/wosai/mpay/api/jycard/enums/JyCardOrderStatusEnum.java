package com.wosai.mpay.api.jycard.enums;


/**
 * <AUTHOR>
 * @description 锦医一卡通-身份标识类型
 * @date 2024-09-04
 */
public enum JyCardOrderStatusEnum {
    PROCESSING("20","正在处理"),
    SUCCESS("60", "支付成功"),
    FAILED("77","支付失败"),
    ORDER_NOT_EXIST("78","订单不存在"),
    REFUNDED("80","转入退款");

    private final String status;
    private final String desc;

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    JyCardOrderStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static JyCardOrderStatusEnum of(String status) {
        if (null == status) {
            return null;
        }
        for (JyCardOrderStatusEnum e : JyCardOrderStatusEnum.values()) {
            if (e.status.equals(status)) {
                return e;
            }
        }
        return null;
    }
}


