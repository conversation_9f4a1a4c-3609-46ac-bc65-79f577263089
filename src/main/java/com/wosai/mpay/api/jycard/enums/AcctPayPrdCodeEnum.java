package com.wosai.mpay.api.jycard.enums;


/**
 * <AUTHOR>
 * @description 锦医一卡通-支付渠道代码枚举
 * @date 2024-09-04
 */
public enum AcctPayPrdCodeEnum {
    ACCT("$$$","账户"),
    ACCT_PRE("$01","账户(预支付)"),
    CASH_BASE("A01","现金"),
    BANK_BOC("A31","中国银行"),
    BANK_ICBC("A32","工商银行"),
    BANK_UNIONPAY("A36","银联"),
    BANK_ALIPAY("A37","支付宝"),
    BANK_WXPAY("A38","微信"),
    BANK_SHOUQIANBA("A40","收钱吧"),
    BANK_DUOLABAO("A41","哆啦宝"),
    BANK_TIANYI("A42","重庆大学缴费平台"),
    BANK_UNIONPAYSF("A43","银联商服");
    
    private final String code;
    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    AcctPayPrdCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AcctPayPrdCodeEnum of(String code) {
        if (null == code) {
            return null;
        }
        for (AcctPayPrdCodeEnum e : AcctPayPrdCodeEnum.values()) {
            if (code.equals(e.code)) {
                return e;
            }
        }
        return null;
    }
}


