package com.wosai.mpay.api.jycard.enums;


/**
 * <AUTHOR>
 * @description 锦医一卡通-终端类型枚举
 * @date 2024-09-04
 */
public enum DevIdEnum {
    DEV_ID_APP("APP", "ticket登录"),
    DEV_ID_API("API","api信任登录"),
    DEV_ID_H5("WAP","h5前端"),
    DEV_ID_WEB("WEB","web前端"),
    DEV_ID_BARCODE("BARCODE","二维码"),
    DEV_ID_ALIPAY("ALIPAY","支付宝客户端"),
    DEV_ID_WECHAT("WECHAT","微信客户端"),
    DEV_ID_MINI_PROGRAM("MINI_PROGRAM","小程序");

    private final String type;
    private final String desc;

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    DevIdEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static DevIdEnum of(String type) {
        if (null == type) {
            return null;
        }
        for (DevIdEnum e : DevIdEnum.values()) {
            if (type.equals(e.type)) {
                return e;
            }
        }
        return null;
    }
}


