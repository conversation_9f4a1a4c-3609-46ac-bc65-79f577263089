package com.wosai.mpay.api.jycard;

import com.wosai.mpay.api.jycard.enums.IdTypeEnum;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static com.wosai.mpay.api.jycard.JyCardConstants.*;
import static com.wosai.mpay.api.jycard.JyCardProtocolFields.*;

/**
 * <AUTHOR>
 * @description 锦医一卡通-测试类
 * @date 2024/9/2
 */
public class JyCardTest {
    public static final Logger logger = LoggerFactory.getLogger(JyCardTest.class);

    public JyCardTest() {}

    private static final SafeSimpleDateFormat dateSimpleFormat = new SafeSimpleDateFormat(JyCardConstants.DATE_SIMPLE_FORMAT);

    //公钥
    private static final String publicKey = "";
    //私钥
    private static final String privateKey = "";
    //测试环境访问地址
    private static final String REQUEST_URL = "http://weixin.ykt.jzmu.edu.cn:8081/onecard/uap/gateway.action";
    //appKey
    private static final String appKey = "094ECE9B552DD46349B2827897297AD4";
    //秘钥
    private static final String appSecret = "GBlyrVq70bhQuMAp+eussbezN3FAqgBk";
    //默认账户类型: 000是电子账户类型
    private static final String accType = "000";
    //学工号
    private static final String sno = "fanchao";
    //校园卡密码
    private static final String password = "888888";
    //测试商户号
    private static final String mercacc = "1000000";
    //一卡通账号
    private static final String account = "16736";
    //身份标识
    private static final String identityId = "************";

    /**
     * 查询token
     *
     * @throws Exception
     */
    private static void queryToken() throws Exception {
        JyCardRequestBuilder requestBuilder = new JyCardRequestBuilder();
        //构建系统级请求参数
        requestBuilder.set(JyCardProtocolFields.TIMESTAMP, dateSimpleFormat.format(new Date()));
        requestBuilder.set(JyCardProtocolFields.FORMAT, DEFAULT_APPLICATION_FORMAT);
        requestBuilder.set(JyCardProtocolFields.APP_KEY, appKey);
        requestBuilder.set(JyCardProtocolFields.ACCESS_TOKEN, DEFAULT_ACCESS_TOKEN);
        requestBuilder.set(JyCardProtocolFields.API_VERSION, DEFAULT_API_VERSION);
        requestBuilder.set(JyCardProtocolFields.SIGN_METHOD, DEFAULT_SIGN_METHOD);

        //构建应用级请求参数
        requestBuilder.set(APPLICATION_PARAMS_KEY, new HashMap<>());

        Map<String, Object> result = new JyCardClient().call(REQUEST_URL, METHOD_QUERY_TOKEN,  requestBuilder.build(), appSecret, privateKey, publicKey);
        System.out.println(result);
    }

    /**
     * 校验密码
     *
     * @throws Exception
     */
    private static void checkPassword() throws Exception {
        JyCardRequestBuilder requestBuilder = new JyCardRequestBuilder();
        //构建系统级请求参数
        requestBuilder.set(JyCardProtocolFields.TIMESTAMP, dateSimpleFormat.format(new Date()));
        requestBuilder.set(JyCardProtocolFields.FORMAT, DEFAULT_APPLICATION_FORMAT);
        requestBuilder.set(JyCardProtocolFields.APP_KEY, appKey);
        requestBuilder.set(JyCardProtocolFields.ACCESS_TOKEN, getAccessToken());
        requestBuilder.set(JyCardProtocolFields.API_VERSION, DEFAULT_API_VERSION);
        requestBuilder.set(JyCardProtocolFields.SIGN_METHOD, DEFAULT_SIGN_METHOD);

        //构建应用级请求参数
        Map<String, Object> pwdParams = new HashMap<>();
        pwdParams.put(JyCardBusinessFields.ID_TYPE, IdTypeEnum.ID_SNO.getType());
        pwdParams.put(JyCardBusinessFields.ID, sno);
        pwdParams.put(JyCardBusinessFields.PWD, password);
        Map<String, Object> applicationRequest = new HashMap<>();
        applicationRequest.put(JyCardBusinessFields.REQUEST_OBJ_CHECK_PWD, pwdParams);
        //设置应用级请求参数
        requestBuilder.set(APPLICATION_PARAMS_KEY, applicationRequest);

        Map<String, Object> result = new JyCardClient().call(REQUEST_URL, METHOD_CHECK_PASSWORD,  requestBuilder.build(), appSecret, privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));
    }

    /**
     * 支付
     *
     * @throws Exception
     */
    private static void pay() throws Exception {
        JyCardRequestBuilder requestBuilder = new JyCardRequestBuilder();
        //构建系统级请求参数
        requestBuilder.set(JyCardProtocolFields.TIMESTAMP, dateSimpleFormat.format(new Date()));
        requestBuilder.set(JyCardProtocolFields.FORMAT, DEFAULT_APPLICATION_FORMAT);
        requestBuilder.set(JyCardProtocolFields.APP_KEY, appKey);
        requestBuilder.set(JyCardProtocolFields.ACCESS_TOKEN, getAccessToken());
        requestBuilder.set(JyCardProtocolFields.API_VERSION, DEFAULT_API_VERSION);
        requestBuilder.set(JyCardProtocolFields.SIGN_METHOD, DEFAULT_SIGN_METHOD);

        //构建应用级请求参数
        Map<String, Object> payParams = new HashMap<>();
        payParams.put(JyCardBusinessFields.ACCOUNT, account);
        payParams.put(JyCardBusinessFields.TRANAMT, "0.01");
        payParams.put(JyCardBusinessFields.ACCTYPE, accType);
        payParams.put(JyCardBusinessFields.MERCACC, mercacc);
        payParams.put(JyCardBusinessFields.ORDER_NO, "201602020000000299");
        Map<String, Object> applicationRequest = new HashMap<>();
        applicationRequest.put(JyCardBusinessFields.REQUEST_OBJ_PAY, payParams);
        //设置应用级请求参数
        requestBuilder.set(APPLICATION_PARAMS_KEY, applicationRequest);

        Map<String, Object> result = new JyCardClient().call(REQUEST_URL, METHOD_PAY,  requestBuilder.build(), appSecret, privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));
    }

    /**
     * 退款
     *
     * @throws Exception
     */
    private static void refund() throws Exception {
        JyCardRequestBuilder requestBuilder = new JyCardRequestBuilder();
        //构建系统级请求参数
        requestBuilder.set(JyCardProtocolFields.TIMESTAMP, dateSimpleFormat.format(new Date()));
        requestBuilder.set(JyCardProtocolFields.FORMAT, DEFAULT_APPLICATION_FORMAT);
        requestBuilder.set(JyCardProtocolFields.APP_KEY, appKey);
        requestBuilder.set(JyCardProtocolFields.ACCESS_TOKEN, getAccessToken());
        requestBuilder.set(JyCardProtocolFields.API_VERSION, DEFAULT_API_VERSION);
        requestBuilder.set(JyCardProtocolFields.SIGN_METHOD, DEFAULT_SIGN_METHOD);

        //构建应用级请求参数
        Map<String, Object> refundParams = new HashMap<>();
        refundParams.put(JyCardBusinessFields.ACCOUNT, account);
        refundParams.put(JyCardBusinessFields.TRANAMT, "0.01");
        refundParams.put(JyCardBusinessFields.ACCTYPE, accType);
        refundParams.put(JyCardBusinessFields.MERCACC, mercacc);
        refundParams.put(JyCardBusinessFields.ORDER_NO, "101602020000000102");
        refundParams.put(JyCardBusinessFields.REFUND_ORDER_NO, "201602020000000299");
        Map<String, Object> applicationRequest = new HashMap<>();
        applicationRequest.put(JyCardBusinessFields.REQUEST_OBJ_REFUND, refundParams);
        //设置应用级请求参数
        requestBuilder.set(APPLICATION_PARAMS_KEY, applicationRequest);

        Map<String, Object> result = new JyCardClient().call(REQUEST_URL, METHOD_REFUND,  requestBuilder.build(), appSecret, privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));
    }

    /**
     * 查询交易
     *
     * @throws Exception
     */
    private static void queryOrder() throws Exception {
        JyCardRequestBuilder requestBuilder = new JyCardRequestBuilder();
        //构建系统级请求参数
        requestBuilder.set(JyCardProtocolFields.TIMESTAMP, dateSimpleFormat.format(new Date()));
        requestBuilder.set(JyCardProtocolFields.FORMAT, DEFAULT_APPLICATION_FORMAT);
        requestBuilder.set(JyCardProtocolFields.APP_KEY, appKey);
        requestBuilder.set(JyCardProtocolFields.ACCESS_TOKEN, getAccessToken());
        requestBuilder.set(JyCardProtocolFields.API_VERSION, DEFAULT_API_VERSION);
        requestBuilder.set(JyCardProtocolFields.SIGN_METHOD, DEFAULT_SIGN_METHOD);

        //构建应用级请求参数
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put(JyCardBusinessFields.ACCOUNT, account);
        queryParam.put(JyCardBusinessFields.ACCTYPE, accType);
        queryParam.put(JyCardBusinessFields.ORDER_NO, "101602020000000102");
        List<Map<String, Object>> queryParams = new ArrayList<>();
        queryParams.add(queryParam);
        Map<String, Object> queryParamData = new HashMap<>();
        queryParamData.put(JyCardBusinessFields.REQUEST_OBJ_ORDER_QUERY_LIST, queryParams);
        Map<String, Object> applicationRequest = new HashMap<>();
        applicationRequest.put(JyCardBusinessFields.REQUEST_OBJ_ORDER_QUERY, queryParamData);
        //设置应用级请求参数
        requestBuilder.set(APPLICATION_PARAMS_KEY, applicationRequest);

        Map<String, Object> result = new JyCardClient().call(REQUEST_URL, METHOD_QUERY_ORDER,  requestBuilder.build(), appSecret, privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));
    }

    private static String getAccessToken() {
        return "CDDDCDB90E9643601ADAB92610BB34EDBE57C6D366ABFFC4175153727106C43FB576FF5901FF7000F2970D076E6641694E45D0839AC524C84BBE7678E4236EF9";
    }

    public static void main(String[] args) throws Exception {
        try {
            //queryToken();
            //checkPassword();
            //pay();
            //refund();
            queryOrder();
        } catch (Exception e) {
            System.out.println("执行失败, 错误如下:");
            e.printStackTrace();
        }
    }
}
