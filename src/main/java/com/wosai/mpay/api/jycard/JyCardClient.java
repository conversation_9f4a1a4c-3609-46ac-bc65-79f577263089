package com.wosai.mpay.api.jycard;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.mpay.api.jycard.enums.JyCardSystemErrorEnum;
import com.wosai.mpay.api.jycard.security.JyCardUtil;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;

import static com.wosai.mpay.util.MapEncryptUtil.resetValueAndReturnNewMap;

/**
 * <AUTHOR>
 * @description 锦医一卡通-客户端封装
 * @date 2024/9/2
 */
public class JyCardClient {
    public static final Logger log = LoggerFactory.getLogger(JyCardClient.class);
    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    //打印日志时需要加密的字段
    private static final List<String> NEED_ENCRYPT_FIELDS_WHEN_LOG = Arrays.asList(
            JyCardProtocolFields.ACCESS_TOKEN, JyCardBusinessFields.ACCOUNT, JyCardBusinessFields.PWD);

    //默认加密后的值
    private static final String DEFAULT_ENCRYPT_VALUE = "*";

    /**
     * 请求接口
     *
     * @param requestUrl   请求地址
     * @param method       请求方法
     * @param params       请求参数
     * @param appSecretKey 密钥
     * @param privateKey   私钥
     * @param publicKey    公钥
     * @return
     * @throws MpayException
     * @throws MpayApiNetworkError
     */
    public Map<String, Object> call(String requestUrl, String method, Map<String, Object> params, String appSecretKey, String privateKey, String publicKey)
            throws MpayException, MpayApiNetworkError {

        //将方法添加到参数中
        params.put(JyCardProtocolFields.METHOD, method);

        log.info("request {}", toJsonStringWithEncryptFields(params));

        //加密应用级参数
        encryptApplicationRequestParams(params, appSecretKey);

        TreeMap<String, Object> requestMap = new TreeMap<>(params);
        
        //加签
        String signValue = getSignValue(requestMap, privateKey);
        requestMap.put(JyCardProtocolFields.SIGN, signValue);

        String response = HttpClientUtils.doPost(JyCardClient.class.getName(), null, null,
                requestUrl, requestMap, WebUtils.DEFAULT_CHARSET_UTF8, connectTimeout, readTimeout);

        return decryptResponse(method, response, publicKey, appSecretKey);
    }

    /**
     * 加密应用级参数
     *
     * @param params
     * @throws MpayException
     */
    private void encryptApplicationRequestParams(Map<String, Object> params, String appSecretKey) throws MpayException {
        Map applicationRequestParams = MapUtils.getMap(params, JyCardProtocolFields.APPLICATION_PARAMS_KEY);
        if (null == applicationRequestParams) {
            log.error("加密应用级参数: 未查到应用级参数, method={}", params.get(JyCardProtocolFields.METHOD));
            throw new MpayException("加密应用级参数: 未查到应用级参数");
        }

        //将参数转换为字符串
        String paramsString = JsonUtil.objectToJsonString(applicationRequestParams);

        //加密
        String encryptValue = JyCardUtil.encrypt(paramsString, appSecretKey, JyCardProtocolFields.DEFAULT_ENCRYPT_ALGORITHM);

        //用加密后的数据覆盖原有的值
        params.put(JyCardProtocolFields.APPLICATION_PARAMS_KEY, encryptValue);
    }

    /**
     * 获取加签值
     *
     * @param params
     * @param privateKey
     * @return
     */
    private String getSignValue(TreeMap<String, Object> params, String privateKey) throws MpayException {
        StringBuilder paramsString = new StringBuilder(1024);
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            paramsString.append(entry.getKey()).append(entry.getValue());
        }

        return RsaSignature.sign(paramsString.toString(), JyCardProtocolFields.DEFAULT_SIGN_ALGORITHM, privateKey);
    }

    /**
     * 解密响应数据
     *
     * @param method
     * @param response
     * @param publicKey
     * @param appSecretKey
     * @return
     * @throws MpayException
     */
    private Map<String, Object> decryptResponse(String method, String response, String publicKey, String appSecretKey) throws MpayException {
        TreeMap<String, String> responseMap = parseFormData(method, response);

        String systemErrorCode = responseMap.get(JyCardResponseFields.SYSTEM_ERROR_CODE);
        if (!JyCardResponseUtil.isSuccess(systemErrorCode)) {
            log.info("response {}", response);
            Map<String, Object> result = new HashMap<>();
            result.put(JyCardResponseFields.SYSTEM_ERROR_CODE, systemErrorCode);
            result.put(JyCardResponseFields.BIZ_ERROR_MSG, JyCardSystemErrorEnum.of(systemErrorCode).getMsg());
            return result;
        }

        //验签
        checkSign(method, responseMap, publicKey);

        //解密响应结果
        String applicationRequestParams = MapUtils.getString(responseMap, JyCardProtocolFields.APPLICATION_PARAMS_KEY);
        String resultString = JyCardUtil.decrypt(applicationRequestParams, appSecretKey, JyCardProtocolFields.DEFAULT_ENCRYPT_ALGORITHM);
        Map<String, Object> resultMap = JsonUtil.jsonStringToObject(resultString, new TypeReference<Map<String, Object>>() {});

        log.info("response {}", toJsonStringWithEncryptFields(resultMap));
        resultMap.put(JyCardResponseFields.SYSTEM_ERROR_CODE, systemErrorCode);
        return resultMap;
    }

    /**
     * 解析form格式的数据
     *
     * @param method
     * @param formData
     * @return
     * @throws MpayException
     */
    private TreeMap<String, String> parseFormData(String method, String formData) throws MpayException {
        TreeMap<String, String> resultMap = new TreeMap<>();
        try {
            for (String string : formData.split("&")) {
                int index = string.indexOf("=");
                if (index > 0 && index <= string.length() - 1) {
                    resultMap.put(string.substring(0, index), URLDecoder.decode(string.substring(index + 1), "utf-8"));
                }
            }
            return resultMap;
        } catch (UnsupportedEncodingException e) {
            log.error("解析响应数据异常, method={}, formData={}, error={}", method, formData, e.getMessage(), e);
            throw new MpayException("解析响应数据异常, method=" + method);
        }
    }

    /**
     * 检查签名
     *
     * @param method
     * @param resultMap
     * @param publicKey
     * @throws MpayException
     */
    private void checkSign(String method, TreeMap<String, String> resultMap, String publicKey) throws MpayException {
        StringBuilder text = new StringBuilder();
        String signValue = null;

        for (Map.Entry<String, String> entry : resultMap.entrySet()) {
            if (JyCardProtocolFields.SIGN.equals(entry.getKey())) {
                signValue = entry.getValue();
                continue;
            }
            text.append(entry.getKey()).append(entry.getValue());
        }

        if (!RsaSignature.validateSign(text.toString().getBytes(), signValue, JyCardProtocolFields.DEFAULT_SIGN_ALGORITHM, publicKey)) {
            log.warn("响应数据验签失败, method={}, resultMap={}", method, JsonUtil.objectToJsonString(resultMap));
            throw new MpayException("响应数据验签失败, method=" + method);
        }
    }

    /**
     * 给指定需要脱敏的字段进行脱敏，并返回Json String
     *
     * @param data
     * @return
     * @throws MpayException
     */
    private static String toJsonStringWithEncryptFields(Map<String, Object> data) throws MpayException {
        Map<String, Object> newMap = resetValueAndReturnNewMap(data, NEED_ENCRYPT_FIELDS_WHEN_LOG, DEFAULT_ENCRYPT_VALUE);
        return JsonUtil.objectToJsonString(newMap);
    }

}
