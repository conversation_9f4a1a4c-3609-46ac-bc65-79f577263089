package com.wosai.mpay.api.jycard.security;

import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import java.security.GeneralSecurityException;

/**
 * 锦医一卡通-工具类
 */
public class JyCardUtil {
    public static final Logger log = LoggerFactory.getLogger(JyCardUtil.class);

    /**
     * 加密
     */
    public static String encrypt(String text, String key, String algorithm) throws MpayException {
        byte[] bytes = text.getBytes(); // 待加/解密的数据
        byte[] keyData = Base64.decode(key); // 密钥数据
        try {
            byte[] cipherBytes = SymmtricCryptoUtil.symmtricCrypto(bytes,
                    keyData, algorithm, Cipher.ENCRYPT_MODE);
            return Base64.encode(cipherBytes);
        } catch (GeneralSecurityException e) {
            log.error("加密错误: key={}, algorithm={}, text={}, error={}", key, algorithm, text, e.getMessage(), e);
            throw new MpayException("加密错误", e);
        }
    }

    /**
     * 解密
     */
    public static String decrypt(String text, String key, String algorithm) throws MpayException {
        byte[] bytes = Base64.decode(text); // 待加/解密的数据
        byte[] keyData = Base64.decode(key); // 密钥数据
        try {
            byte[] cipherBytes = SymmtricCryptoUtil.symmtricCrypto(bytes,
                    keyData, algorithm, Cipher.DECRYPT_MODE);
            return new String(cipherBytes);
        } catch (GeneralSecurityException e) {
            log.error("解密错误: key={}, algorithm={}, text={}, error={}", key, algorithm, text, e.getMessage(), e);
            throw new MpayException("解密错误", e);
        }
    }
}
