package com.wosai.mpay.api.jycard.enums;


/**
 * <AUTHOR>
 * @description 锦医一卡通-系统错误码定义
 * @date 2024-09-04
 */
public enum JyCardSystemErrorEnum {
    UNKNOWN("999999", "未知错误"),
    SUCCESS("0", "表示成功"),
    SYSTEM_BUSY("-1", "系统繁忙"),
    SYSTEM_ERROR("-2", "系统内部错误"),
    PAYLOAD_INVALID("40001", "不合法的数据报文"),
    APP_KEY_INVALID("40002", " 不支持的appkey"),
    SIGN_ALGORITHM_INVALID("40003", "不支持的签名算法"),
    SIGN_CHECK_FAILED("40004", "签名校验失败"),
    SYSTEM_AUTH_EXPIRED("40005", "系统授权过期"),
    API_AUTO_EXPIRED("40006", " API授权过期"),
    ACCESS_SYSTEM_AUTH_EXPIRED("40007", "接入系统授权过期"),
    SYSTEM_UNAUTHORIZED("40008", "系统未授权"),
    API_UNAUTHORIZED("40009", " API未授权"),
    ACCESS_SYSTEM_UNAUTHORIZED("40010", "接入系统未授权"),
    ACCESS_SYSTEM_CLOSED("40011", "接入系统未启用"),
    UNSUPPORTED_API("40012", "不支持的API"),
    API_CLOSED("40013", " API未启用"),
    CALL_TIMES_LIMIT("40014", "调用次数超限"),
    DATA_FORMAT_ERROR("40015", "数据格式错误"),
    PROTOCOL_VERSION_UNSUPPORTED("40016", "协议版本不支持"),
    ACCESS_TOKEN_INVALID("40017", "access_token无效"),
    TIMESTAMP_INVALID("40018", "时间戳无效");

    private final String code;
    private final String msg;

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    JyCardSystemErrorEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static JyCardSystemErrorEnum of(String code) {
        if (null == code) {
            return UNKNOWN;
        }
        for (JyCardSystemErrorEnum e : JyCardSystemErrorEnum.values()) {
            if (code.equals(e.code)) {
                return e;
            }
        }
        return UNKNOWN;
    }
}


