package com.wosai.mpay.api.jycard;

/**
 * <AUTHOR>
 * @description
 * @date 2024/9/4
 */
public class JyCardBusinessFields {
    //查询token
    public static final String REQUEST_OBJ_ACCESS_TOKEN = "authorize_access_token";

    //校验密码
    public static final String REQUEST_OBJ_CHECK_PWD = "check_pwd";
    public static final String ID_TYPE = "idtype"; //身份标识类型, 见IdTypeEnum
    public static final String ID = "id";   //身份标识, 见IdTypeEnum
    public static final String PWD = "pwd"; //校园卡查询密码, 见IdTypeEnum
    public static final String SNO = "sno"; //用户名

    //支付
    public static final String REQUEST_OBJ_PAY = "gdccard_pay";
    public static final String ACCOUNT = "account";//一卡通账户。如果一个身份标识对应多张卡时，此账号为查询密码匹配的第一张卡的账号
    public static final String IDENTITY_ID = "identityid";//身份标识
    public static final String TRANAMT = "tranamt";//支付金额
    public static final String ACCTYPE = "acctype";//账户类型
    public static final String MERCACC = "mercacc";//商户号
    public static final String REMARK = "remark";//费用说明
    public static final String ATTACH = "attach";//附加数据,对账文件里返回
    public static final String ORDER_NO = "order_no";//订单号。收钱吧上送的订单号
    public static final String TRADE_NO = "out_id";//订单号。锦医一卡通的交易单号

    //退款
    public static final String REQUEST_OBJ_REFUND = "gdccard_refund";
    public static final String REFUND_ORDER_NO = "refund_order_no";//原支付订单号

    //查询订单
    public static final String REQUEST_OBJ_ORDER_QUERY = "pay_query";
    public static final String REQUEST_OBJ_ORDER_QUERY_LIST = "pay_querys";
    public static final String QUERYS = "querys";
    public static final String ORDER_STATUS = "order_status";
}
