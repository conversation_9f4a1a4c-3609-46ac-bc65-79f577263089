package com.wosai.mpay.api.jycard;

/**
 * <AUTHOR>
 * @description 锦医一卡通-常量定义
 * @date 2024/9/2
 */
public class JyCardConstants {
    /**
     * 请求方法定义
     */
    public static final String METHOD_QUERY_TOKEN = "synjones.authorize.access_token"; //获取访问令牌
    public static final String METHOD_CHECK_PASSWORD = "synjones.onecard.check.pwd";   //校验用户名和密码
    public static final String METHOD_PAY = "synjones.onecard.gdccard.pay"; //支付
    public static final String METHOD_REFUND = "synjones.onecard.gdccard.refund"; //退款
    public static final String METHOD_QUERY_ORDER = "synjones.onecard.pay.query"; //交易查询

    /**
     * 日期格式
     */
    public static final String DATE_SIMPLE_FORMAT = "yyyy-MM-dd HH:mm:ss";

}
