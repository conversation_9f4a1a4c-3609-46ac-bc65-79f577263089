package com.wosai.mpay.api.uepay;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Date: 2020/6/10 Time: 11:03 上午
 */
public class RequestBuilder {

    Map<String, Object> request;
    Map<String, String> bizContent;

    public RequestBuilder() {
        request = new HashMap<>();
        bizContent = new HashMap<>();

        request.put(ProtocolFields.APP_VERSION, UepayConstants.APP_VERSION);
        request.put(ProtocolFields.APP_SOURCE, UepayConstants.APP_SOURCE_SERVER);
        request.put(ProtocolFields.TRADE_TYPE, UepayConstants.TRADE_TYPE);
    }

    public Map<String, Object> build() {
        request.put(ProtocolFields.ARGUMENTS, bizContent);
        return request;
    }

    public void baseSet(String field, Object value) {
        request.put(field, value);
    }

    public void bizSet(String field, String value) {
        bizContent.put(field, value);
    }

    public void baseSet(Map<String, Object> baseParams) {
        request.putAll(baseParams);
    }

    public void bizSet(Map<String, String> bizParams) {
        bizContent.putAll(bizParams);
    }

    public Map<String, Object> getRequest() {
        return request;
    }

    public Map<String, String> getBizContent() {
        return bizContent;
    }
}
