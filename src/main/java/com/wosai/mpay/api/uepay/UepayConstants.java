package com.wosai.mpay.api.uepay;

/**
 * <AUTHOR> Date: 2020/6/10 Time: 11:04 上午
 */
public class UepayConstants {

    public static final String TRADE_STATE_SUCCESS = "SUCCESS"; //支付成功
    public static final String TRADE_STATE_USERPAYING = "USERPAYING"; //用戶支付中
    public static final String TRADE_STATE_PAYERROR = "PAYERROR"; //支付失敗
    public static final String TRADE_STATE_BACKOUT = "BACKOUT"; //已撤銷
    public static final String TRADE_STATE_REFUND = "REFUND"; //退款申請中
    public static final String TRADE_STATE_REFUNDNO = "REFUNDNO"; //退款申請被駁回
    public static final String TRADE_STATE_REFUNDYES = "REFUNDYES"; //退款成功

    public static final String REQUEST_TYPE_MICROPAY = "MICROPAY"; //掃碼支付
    public static final String REQUEST_TYPE_REVOKE = "REVOKE"; //交易撤銷
    public static final String REQUEST_TYPE_REFUND = "REFUND"; //退款
    public static final String REQUEST_TYPE_QUERY = "QUERY"; //交易查詢

    public static final String APP_VERSION = "1.3"; //版本号, 固定值

    public static final String APP_SOURCE_POS = "1"; //来源-pos终端
    public static final String APP_SOURCE_SERVER = "2"; //来源-server服务器
    public static final String APP_SOURCE_WEB = "3"; //来源-web网页
    public static final String APP_SOURCE_OTHER = "4"; //来源-其他

    public static final String TRADE_TYPE = "MICROPAY"; //交易类型, 固定值

}
