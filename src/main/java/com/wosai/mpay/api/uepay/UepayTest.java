package com.wosai.mpay.api.uepay;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Date: 2020/6/10 Time: 11:04 上午
 */
public class UepayTest {


    private static String gateway = "https://fat.uepay.mo/payment/gateway";
    private static String merchantNo = "000030053310001";
    private static String secret = "";

    public static void main(String[] args) throws Throwable {
//        pay();
        query();
//        revoke();
//        refund();
    }

    private static void refund() throws Throwable {
        String orderNo = "1592190560999";
        String refundAmt = "2";
        String refundOrderNo = System.currentTimeMillis() + "";
        Map<String, String> bizParams = new HashMap<>();
        bizParams.put("orderNo", orderNo);
        bizParams.put("refundAmt", refundAmt);
        bizParams.put("refundOrderNo", refundOrderNo);

        UepayClient uepayClient = new UepayClient();
        uepayClient.call(gateway, bizParams, merchantNo, "REFUND", secret);
    }

    private static void revoke() throws Throwable {
        String orderNo = "7894259244316975";
        Map<String, String> bizParams = new HashMap<>();
        bizParams.put("orderNo", orderNo);

        UepayClient uepayClient = new UepayClient();
        uepayClient.call(gateway, bizParams, merchantNo, "REVOKE", secret);
    }



    private static void query() throws Throwable {
        String orderNo = "7894259244720938";

        Map<String, String> bizParams = new HashMap<>();
        bizParams.put("orderNo", orderNo);

        UepayClient uepayClient = new UepayClient();
        uepayClient.call(gateway, bizParams, merchantNo, "QUERY", secret);
    }

    private static void pay() throws Throwable {
        String storeNo = "test1";
        String terminalNo = "124896357";

        Map<String, String> bizParams = new HashMap<>();
        bizParams.put("storeCode", storeNo);
        bizParams.put("terminal", terminalNo);
        bizParams.put("qrCode", "281393605933646372");
        bizParams.put("orderNo", System.currentTimeMillis() + "");
        bizParams.put("amt", "2");



        UepayClient uepayClient = new UepayClient();
        uepayClient.call(gateway, bizParams, merchantNo, "MICROPAY", secret);
    }

}
