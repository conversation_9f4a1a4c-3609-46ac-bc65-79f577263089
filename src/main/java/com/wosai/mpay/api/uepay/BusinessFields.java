package com.wosai.mpay.api.uepay;

/**
 * <AUTHOR> Date: 2020/6/10 Time: 11:04 上午
 */
public class BusinessFields {

    public static final String QR_CODE = "qrCode"; //二維碼
    public static final String ORDER_NO = "orderNo"; //訂單號,商戶訂單號
    public static final String AMT = "amt"; //訂單金額,以分為單位
    public static final String ATTACH = "attach"; //附加數據,異步通知介面原樣返回
    public static final String STORE_CODE = "storeCode"; //門店編號,極易付系統提供的商戶門店編號
    public static final String TERMINAL = "terminal"; //終端號,終端編號(唯一標識/SN號)
    public static final String CASHIER = "cashier"; //收銀員,收銀員編號或名稱
    public static final String TRAN_NO = "tranNo"; //支付流水號,極易付流水號
    public static final String REFUND_ORDER_NO = "refundOrderNo"; //退款訂單號,商戶退款訂單號
    public static final String REFUND_AMT = "refundAmt"; //退款金額單位:分

}
