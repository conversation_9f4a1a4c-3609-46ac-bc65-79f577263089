package com.wosai.mpay.api.uepay;

import com.wosai.mpay.api.chinaums.ChinaumsConstants;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.UepaySignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR> Date: 2020/6/9 Time: 10:58 上午
 */
public class UepayClient {
    public static final Logger logger = LoggerFactory.getLogger(UepayClient.class);

    private static final String CONTENT_TYPE = "application/json;charset=utf-8";

    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map<String,Object> call(String gateway, Map<String, String> bizParams, String merchantNo, String requestType, String secret) throws MpayException, MpayApiNetworkError {

        String signature = UepaySignature.sign(bizParams, merchantNo, requestType
                , UepayConstants.APP_VERSION, UepayConstants.APP_SOURCE_SERVER, UepayConstants.TRADE_TYPE, secret);

        RequestBuilder builder = new RequestBuilder();
        builder.baseSet(ProtocolFields.REQUEST_TYPE, requestType);
        builder.baseSet(ProtocolFields.MERCHANT_NO, merchantNo);
        builder.baseSet(ProtocolFields.CLIENT_SIGN, signature);
        builder.bizSet(bizParams);

        String requestStr = JsonUtil.objectToJsonString(builder.build());
        logger.info("UepayClient request: {}", requestStr);

        String resultStr = HttpClientUtils.doPost(UepayClient.class.getName(), null, null, gateway, CONTENT_TYPE, requestStr, ChinaumsConstants.CHARSET_UTF8, connectTimeout, readTimeout);

        logger.info("UepayClient result: {}", resultStr);

        try{
            Map<String,Object> result = JsonUtil.jsonStringToObject(resultStr, Map.class);
            return result;
        }catch (Exception e){
            throw new MpayApiUnknownResponse("parse response error: " + e.getMessage(), e);
        }
    }

}
