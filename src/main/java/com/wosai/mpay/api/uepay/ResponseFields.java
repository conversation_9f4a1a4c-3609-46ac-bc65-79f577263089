package com.wosai.mpay.api.uepay;

/**
 * <AUTHOR> Date: 2020/6/10 Time: 11:03 上午
 */
public class ResponseFields {

    public static final String REQUEST_TYPE = "requestType"; //交易代碼
    public static final String MESSAGE = "message"; //返回結果描述
    public static final String EN_MESSAGE = "enMessage"; //返回結果英文描述
    public static final String RET_CODE = "retCode"; //返回結果描述對應編碼
    public static final String RESULT = "result"; //結果, false : 失敗 true :成功
    public static final String RESULTS = "results"; //返回參數, 與具體交易相關的報文體, 根據交易的不同, 包含的元素內容有差別
    public static final String SERVER_SIGN = "serverSign"; //簽名, 服務器響應數據簽名(當參數result值等於true,才需要校驗服務簽名)


    public static final String TRADE_STATE = "tradeState"; //交易狀態
    public static final String TRAN_NO = "tranNo"; //支付流水號, 極易付流水號
    public static final String ORDER_NO = "orderNo"; //訂單號, 商戶上送訂單號
    public static final String TRADE_TIME = "tradeTime"; //支付時間, yyyy-MM-dd HH:mm:ss
    public static final String MOP_AMT = "mopAmt"; //客戶實際扣款金額(澳門元)單位:分
    public static final String HKD_AMT = "hkdAmt"; //客戶實際扣款金額(港幣)單位:分
    public static final String RMB_AMT = "rmbAmt"; //客戶實際扣款金額(人⺠幣)單位:分
    public static final String WX_BARCODE_NO = "wxBarcodeNo"; //用戶付款記錄條形碼
    public static final String OPENID = "openid"; //微信返回的openid，如果需要，需要在商戶入住時，向極易付申請。
    public static final String WX_NO = "wxNo"; //微信流水號(衹有微信支付時才返回)
    public static final String TRAN_TYPE = "tranType"; //訂單类型:"1":消費單 "5":退款單
    public static final String TOTAL_AMOUNT = "totalAmount"; //客戶實際总金額單位:分。
    public static final String REFUNDABLE_AMT = "refundableAmt"; //訂單剩餘可退款金額,(澳門元)單位:分
    public static final String PAY_METHOD = "payMethod"; //支付方式,uepay:钱包支付,wechat:微信支付,alipay:支付宝支付
    public static final String PAY_LOG = "payLog"; //支付結果
    public static final String TERMINAL = "terminal"; //終端號,交易使用的終端編號(如果交易时上送则有,否则无)

}
