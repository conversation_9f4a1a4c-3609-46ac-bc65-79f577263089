package com.wosai.mpay.api.haikeduo;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * HaikeDuo API Client for 嗨客多 Payment Service
 * 嗨客多支付接口客户端
 *
 * 基于嗨客多官方API文档实现
 * 支持仿Native支付、订单查询、订单退款、退款查询等功能
 * 支持微信、支付宝、银联等多种支付方式
 */
public class HaikeDuoClient {
    private static final Logger logger = LoggerFactory.getLogger(HaikeDuoClient.class);

    private int connectTimeout = HaikeDuoConstants.DEFAULT_CONNECT_TIMEOUT;
    private int readTimeout = HaikeDuoConstants.DEFAULT_READ_TIMEOUT;

    public HaikeDuoClient() {
    }

    public HaikeDuoClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    /**
     * Call HaikeDuo API
     * 调用嗨客多API
     *
     * @param url API endpoint URL API接口地址
     * @param data Request data parameters 请求数据参数
     * @param appId Application ID 应用ID
     * @param merchantNum Merchant number 商户编号
     * @param appKey Application key (secret) 应用密钥
     * @return API response as Map 以Map形式返回API响应
     * @throws MpayException if request fails 如果请求失败
     * @throws MpayApiNetworkError if network error occurs 如果发生网络错误
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> call(String url, Map<String, Object> data, String appId, String merchantNum, String appKey)
            throws MpayException, MpayApiNetworkError {
        
        // Validate input parameters
        validateCallParameters(url, data, appId, merchantNum, appKey);

        // Prepare request parameters with authentication
        Map<String, Object> request = new LinkedHashMap<>();
        request.put(HaikeDuoRequestFields.APPID, appId);
        request.put(HaikeDuoRequestFields.TIMESTAMP, System.currentTimeMillis() / 1000);
        request.put(HaikeDuoRequestFields.MERCHANT_NUM, merchantNum);
        
        // Add business data
        if (data != null) {
            request.putAll(data);
        }

        // Generate signature
        String sign = HaikeDuoSignature.signWithValidation(request, appKey);
        request.put(HaikeDuoRequestFields.SIGN, sign);

        // Convert request to JSON
        String requestJson = JsonUtil.objectToJsonString(request);
        logger.info("HaikeDuo request: {}", requestJson);

        // Prepare headers
        Map<String, String> headers = new LinkedHashMap<>();
        headers.put(HaikeDuoProtocolFields.HEADER_CONTENT_TYPE, HaikeDuoConstants.CONTENT_TYPE);

        // Call API
        String responseJson = HttpClientUtils.doPost(
            HaikeDuoClient.class.getName(),
            null,
            null,
            url,
            HaikeDuoConstants.CONTENT_TYPE,
            requestJson,
            headers,
            HaikeDuoConstants.CHARSET,
            connectTimeout,
            readTimeout
        );
        
        logger.info("HaikeDuo response: {}", responseJson);

        // Parse response
        Map<String, Object> response;
        try {
            response = JsonUtil.jsonStringToObject(responseJson, Map.class);
        } catch (Exception e) {
            throw new MpayApiUnknownResponse("Parse response error: " + e.getMessage(), e);
        }

        // Validate response format
        validateResponseFormat(response);


        return response;
    }

    /**
     * Pay QRCode - 仿Native支付
     * 用户在微信、支付宝内跳转到支付链接，或使用微信、支付宝扫描支付链接生成的二维码，调起完成支付
     *
     * @param baseUrl Base URL of HaikeDuo API 嗨客多API基础地址
     * @param data Request data 请求数据
     * @param appId Application ID 应用ID
     * @param merchantNum Merchant number 商户编号
     * @param appKey Application key 应用密钥
     * @return Payment response 支付响应
     * @throws MpayException if request fails 如果请求失败
     * @throws MpayApiNetworkError if network error occurs 如果发生网络错误
     */
    public Map<String, Object> payQrcode(String baseUrl, Map<String, Object> data, String appId, String merchantNum, String appKey)
            throws MpayException, MpayApiNetworkError {
        String url = baseUrl;
        return call(url, data, appId, merchantNum, appKey);
    }

    /**
     * Order Query - 订单查询
     * 用于获取订单详情，包括订单支付结果
     *
     * @param baseUrl Base URL of HaikeDuo API 嗨客多API基础地址
     * @param data Request data 请求数据
     * @param appId Application ID 应用ID
     * @param merchantNum Merchant number 商户编号
     * @param appKey Application key 应用密钥
     * @return Order query response 订单查询响应
     * @throws MpayException if request fails 如果请求失败
     * @throws MpayApiNetworkError if network error occurs 如果发生网络错误
     */
    public Map<String, Object> orderQuery(String baseUrl, Map<String, Object> data, String appId, String merchantNum, String appKey)
            throws MpayException, MpayApiNetworkError {
        String url = baseUrl;
        return call(url, data, appId, merchantNum, appKey);
    }

    /**
     * Order Refund - 订单退款
     * 用于对已交易成功的订单执行退款操作
     *
     * @param baseUrl Base URL of HaikeDuo API 嗨客多API基础地址
     * @param data Request data 请求数据
     * @param appId Application ID 应用ID
     * @param merchantNum Merchant number 商户编号
     * @param appKey Application key 应用密钥
     * @return Refund response 退款响应
     * @throws MpayException if request fails 如果请求失败
     * @throws MpayApiNetworkError if network error occurs 如果发生网络错误
     */
    public Map<String, Object> orderRefund(String baseUrl, Map<String, Object> data, String appId, String merchantNum, String appKey)
            throws MpayException, MpayApiNetworkError {
        String url = baseUrl;
        return call(url, data, appId, merchantNum, appKey);
    }

    /**
     * Refund Query - 退款查询
     * 用于查询退款状态
     *
     * @param baseUrl Base URL of HaikeDuo API 嗨客多API基础地址
     * @param data Request data 请求数据
     * @param appId Application ID 应用ID
     * @param merchantNum Merchant number 商户编号
     * @param appKey Application key 应用密钥
     * @return Refund query response 退款查询响应
     * @throws MpayException if request fails 如果请求失败
     * @throws MpayApiNetworkError if network error occurs 如果发生网络错误
     */
    public Map<String, Object> refundQuery(String baseUrl, Map<String, Object> data, String appId, String merchantNum, String appKey)
            throws MpayException, MpayApiNetworkError {
        String url = baseUrl;
        return call(url, data, appId, merchantNum, appKey);
    }

    /**
     * Validate call parameters
     * 验证调用参数
     */
    private void validateCallParameters(String url, Map<String, Object> data, String appId, String merchantNum, String appKey)
            throws MpayException {
        if (url == null || url.trim().isEmpty()) {
            throw new MpayException("URL cannot be null or empty");
        }
        if (appId == null || appId.trim().isEmpty()) {
            throw new MpayException("App ID cannot be null or empty");
        }
        if (merchantNum == null || merchantNum.trim().isEmpty()) {
            throw new MpayException("Merchant number cannot be null or empty");
        }
        if (appKey == null || appKey.trim().isEmpty()) {
            throw new MpayException("App key cannot be null or empty");
        }
    }

    /**
     * Validate response format
     * 验证响应格式
     */
    private void validateResponseFormat(Map<String, Object> response) throws MpayApiUnknownResponse {
        if (response == null) {
            throw new MpayApiUnknownResponse("Response is null");
        }
        if (!response.containsKey(HaikeDuoResponseFields.CODE)) {
            throw new MpayApiUnknownResponse("Response missing required field: code");
        }
    }

    // Getters and setters
    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
}
