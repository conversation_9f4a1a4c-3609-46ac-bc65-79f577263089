package com.wosai.mpay.api.haikeduo;

/**
 * HaikeDuo Protocol Fields for 嗨客多 Payment Service
 * 嗨客多支付接口协议字段
 * 
 * 定义协议层面的字段，包括HTTP头部、认证信息等
 */
public class HaikeDuoProtocolFields {

    // HTTP Headers - HTTP头部字段
    public static final String HEADER_CONTENT_TYPE = "Content-Type";
    public static final String HEADER_ACCEPT = "Accept";
    public static final String HEADER_USER_AGENT = "User-Agent";
    public static final String HEADER_REQUEST_ID = "Request-Id";
    
    // Rate limiting headers - 流控相关头部
    public static final String HEADER_RATE_LIMIT = "X-RateLimit-Limit";
    public static final String HEADER_RATE_REMAINING = "X-RateLimit-Remaining";
    public static final String HEADER_RETRY_AFTER = "Retry-After";

    // Content type values - 内容类型值
    public static final String CONTENT_TYPE_JSON = "application/json";
    public static final String CONTENT_TYPE_JSON_UTF8 = "application/json;charset=utf-8";
    public static final String CONTENT_TYPE_FORM = "application/x-www-form-urlencoded";

    // Authentication fields - 认证字段
    public static final String AUTH_APPID = "appid";
    public static final String AUTH_TIMESTAMP = "timestamp";
    public static final String AUTH_MERCHANT_NUM = "merchant_num";
    public static final String AUTH_SIGN = "sign";

    // Signature related fields - 签名相关字段
    public static final String SIGNATURE_KEY_PARAM = "key";
    public static final String SIGNATURE_ALGORITHM = "MD5";
    public static final String SIGNATURE_CHARSET = "UTF-8";
    public static final String SIGNATURE_SEPARATOR = "&";
    public static final String SIGNATURE_EQUALS = "=";

    // Request validation - 请求验证
    public static final int TIMESTAMP_TOLERANCE_SECONDS = 30; // 时间戳容差30秒
    public static final String HTTP_METHOD = "POST"; // 统一使用POST方法

    // Response validation - 响应验证
    public static final String SUCCESS_CODE = "000000";
    public static final String SUCCESS_MESSAGE = "请求成功";

    // Connection settings - 连接设置
    public static final int DEFAULT_CONNECT_TIMEOUT = 3000; // 默认连接超时3秒
    public static final int DEFAULT_READ_TIMEOUT = 30000; // 默认读取超时30秒
    public static final String DEFAULT_CHARSET = "UTF-8"; // 默认字符编码

    // User agent - 用户代理
    public static final String USER_AGENT_PREFIX = "HaikeDuo-Java-SDK";
    public static final String USER_AGENT_VERSION = "1.0.0";
    public static final String DEFAULT_USER_AGENT = USER_AGENT_PREFIX + "/" + USER_AGENT_VERSION;

    // Request ID generation - 请求ID生成
    public static final String REQUEST_ID_PREFIX = "hkd_";
    public static final int REQUEST_ID_LENGTH = 32;

    // Error handling - 错误处理
    public static final String ERROR_NETWORK = "NETWORK_ERROR";
    public static final String ERROR_TIMEOUT = "TIMEOUT_ERROR";
    public static final String ERROR_PARSE = "PARSE_ERROR";
    public static final String ERROR_SIGNATURE = "SIGNATURE_ERROR";
    public static final String ERROR_VALIDATION = "VALIDATION_ERROR";

    // Retry settings - 重试设置
    public static final int MAX_RETRY_ATTEMPTS = 3;
    public static final int RETRY_DELAY_MS = 1000;
    public static final double RETRY_BACKOFF_MULTIPLIER = 2.0;

    // Logging - 日志
    public static final String LOG_REQUEST_PREFIX = "HaikeDuo Request: ";
    public static final String LOG_RESPONSE_PREFIX = "HaikeDuo Response: ";
    public static final String LOG_ERROR_PREFIX = "HaikeDuo Error: ";

    // Field exclusions for signature - 签名排除字段
    public static final String[] SIGNATURE_EXCLUDE_FIELDS = {
        AUTH_SIGN, // sign字段不参与签名
        // 对象和数组字段不参与签名（根据文档规定）
    };

    // Required fields validation - 必填字段验证
    public static final String[] REQUIRED_AUTH_FIELDS = {
        AUTH_APPID,
        AUTH_TIMESTAMP,
        AUTH_MERCHANT_NUM,
        AUTH_SIGN
    };

    // Field types for validation - 字段类型验证
    public static final String FIELD_TYPE_STRING = "string";
    public static final String FIELD_TYPE_INTEGER = "integer";
    public static final String FIELD_TYPE_OBJECT = "object";
    public static final String FIELD_TYPE_ARRAY = "array";
    public static final String FIELD_TYPE_DECIMAL = "decimal";

    // Protocol validation methods - 协议验证方法
    public static class Validation {
        
        /**
         * Validate timestamp tolerance
         * 验证时间戳容差
         */
        public static boolean isValidTimestamp(long timestamp) {
            long currentTime = System.currentTimeMillis() / 1000;
            long diff = Math.abs(currentTime - timestamp);
            return diff <= TIMESTAMP_TOLERANCE_SECONDS;
        }
        
        /**
         * Check if field should be excluded from signature
         * 检查字段是否应从签名中排除
         */
        public static boolean shouldExcludeFromSignature(String fieldName) {
            for (String excludeField : SIGNATURE_EXCLUDE_FIELDS) {
                if (excludeField.equals(fieldName)) {
                    return true;
                }
            }
            return false;
        }
        
        /**
         * Validate required authentication fields
         * 验证必需的认证字段
         */
        public static boolean hasRequiredAuthFields(java.util.Map<String, Object> params) {
            for (String requiredField : REQUIRED_AUTH_FIELDS) {
                if (!params.containsKey(requiredField) || params.get(requiredField) == null) {
                    return false;
                }
            }
            return true;
        }
        
        /**
         * Check if response is successful
         * 检查响应是否成功
         */
        public static boolean isSuccessResponse(String code) {
            return SUCCESS_CODE.equals(code);
        }
    }

    // HTTP status codes - HTTP状态码
    public static final int HTTP_OK = 200;
    public static final int HTTP_BAD_REQUEST = 400;
    public static final int HTTP_UNAUTHORIZED = 401;
    public static final int HTTP_FORBIDDEN = 403;
    public static final int HTTP_NOT_FOUND = 404;
    public static final int HTTP_TOO_MANY_REQUESTS = 429;
    public static final int HTTP_INTERNAL_SERVER_ERROR = 500;
    public static final int HTTP_BAD_GATEWAY = 502;
    public static final int HTTP_SERVICE_UNAVAILABLE = 503;
    public static final int HTTP_GATEWAY_TIMEOUT = 504;

    // Environment settings - 环境设置
    public static final String ENV_PRODUCTION = "production";
    public static final String ENV_SANDBOX = "sandbox";
    public static final String ENV_DEVELOPMENT = "development";

    // API versioning - API版本
    public static final String API_VERSION = "v2";
    public static final String API_VERSION_HEADER = "API-Version";

    // Security settings - 安全设置
    public static final boolean ENABLE_SSL_VERIFICATION = true;
    public static final String[] SUPPORTED_TLS_VERSIONS = {"TLSv1.2", "TLSv1.3"};
    public static final String[] SUPPORTED_CIPHER_SUITES = {
        "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
        "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256",
        "TLS_DHE_RSA_WITH_AES_256_GCM_SHA384",
        "TLS_DHE_RSA_WITH_AES_128_GCM_SHA256"
    };
}
