package com.wosai.mpay.api.haikeduo;

/**
 * HaikeDuo API Constants for 嗨客多 Payment Service
 * 嗨客多支付接口常量类
 * 
 * 基于嗨客多官方API文档实现
 * 支持仿Native支付、订单查询、订单退款、退款查询等功能
 */
public class HaikeDuoConstants {

    // Content type and charset - 内容类型和字符编码
    public static final String CONTENT_TYPE = "application/json"; // 请求内容类型
    public static final String CHARSET = "utf-8"; // 字符编码

    // Payment software types - 支付软件类型
    public static final String SOFTWARE_WEIXIN = "weixin"; // 微信
    public static final String SOFTWARE_ALIPAY = "alipay"; // 支付宝
    public static final String SOFTWARE_UNIONPAY = "unionpay"; // 银联

    // Payment channels - 支付渠道
    public static final int CHANNEL_WEIXIN = 1; // 微信
    public static final int CHANNEL_ALIPAY = 2; // 支付宝
    public static final int CHANNEL_UNIONPAY = 5; // 银联二维码
    public static final int CHANNEL_SANXIAFU = 6; // 三峡付
    public static final int CHANNEL_HUABEI = 7; // 花呗分期
    public static final int CHANNEL_JINGDONG = 8; // 京东支付

    // WeChat Mini Program redirect flag - 微信小程序跳转标识
    public static final int REDIRECT_XCX_YES = 1; // 跳转小程序
    public static final int REDIRECT_XCX_NO = 0; // 不跳转小程序

    // Separate account methods - 分账方式
    public static final String SEPARATE_METHOD_SYNC = "sync"; // 同步分账
    public static final String SEPARATE_METHOD_ASYNC = "async"; // 异步分账
    public static final String SEPARATE_METHOD_MANUAL = "manual"; // 手动分账

    // Separate account flags - 分账标识
    public static final int SEPARATE_YES = 1; // 分账
    public static final int SEPARATE_NO = 0; // 不分账

    // Response codes - 返回码
    public static final String RESPONSE_CODE_SUCCESS = "000000"; // 业务处理成功

    // Request error codes - 请求类错误码
    public static final String ERROR_CODE_UNSUPPORTED_METHOD = "100000"; // 不支持的HTTP请求方法
    public static final String ERROR_CODE_PARAM_VALIDATION = "100001"; // 请求参数验证不通过
    public static final String ERROR_CODE_MISSING_APPID = "100002"; // 缺少APPID传参或应用不存在
    public static final String ERROR_CODE_RATE_LIMIT = "100003"; // 请求频率限制
    public static final String ERROR_CODE_MISSING_SIGN = "100004"; // 签名不存在
    public static final String ERROR_CODE_INVALID_SIGN = "100005"; // 签名不正确

    // Permission error codes - 权限类错误码
    public static final String ERROR_CODE_NO_PERMISSION = "200000"; // 没有操作权限

    // System error codes - 系统类错误码
    public static final String ERROR_CODE_SYSTEM_ERROR = "300000"; // 系统错误
    public static final String ERROR_CODE_INTERNAL_ERROR = "300001"; // 内部其他平台错误

    // Business error codes - 业务处理类错误码
    public static final String ERROR_CODE_DATA_NOT_EXIST = "400000"; // 数据不存在
    public static final String ERROR_CODE_BUSINESS_CONDITION = "400001"; // 业务条件不满足

    // Signature algorithm - 签名算法
    public static final String SIGNATURE_ALGORITHM = "MD5"; // MD5签名算法
    public static final String SIGNATURE_CHARSET = "UTF-8"; // 签名字符编码

    // Time format - 时间格式
    public static final String TIME_FORMAT_UNIX = "unix"; // Unix时间戳格式

    // Default timeout settings - 默认超时设置
    public static final int DEFAULT_CONNECT_TIMEOUT = 3000; // 默认连接超时时间(毫秒)
    public static final int DEFAULT_READ_TIMEOUT = 30000; // 默认读取超时时间(毫秒)

    // HTTP headers - HTTP头部
    public static final String HEADER_CONTENT_TYPE = "Content-Type";
    public static final String HEADER_REQUEST_ID = "Request-Id";
    public static final String HEADER_RATE_LIMIT = "X-RateLimit-Limit";
    public static final String HEADER_RATE_REMAINING = "X-RateLimit-Remaining";
    public static final String HEADER_RETRY_AFTER = "Retry-After";

    // Field validation lengths - 字段验证长度
    public static final int MAX_LENGTH_STORE_NUM = 7; // 门店编号最大长度
    public static final int MAX_LENGTH_STAFF_NUM = 8; // 收银员编号最大长度
    public static final int MAX_LENGTH_MERCHANT_ORDER_NO = 64; // 商户订单号最大长度
    public static final int MAX_LENGTH_ORDER_NUMBER = 32; // 平台订单号最大长度
    public static final int MAX_LENGTH_TRADE_NO = 64; // 收单机构流水号最大长度
    public static final int MAX_LENGTH_MCH_REMARKS = 128; // 交易附加信息最大长度
    public static final int MAX_LENGTH_REFUND_NO = 32; // 退款单号最大长度
    public static final int MAX_LENGTH_REMARK = 20; // 备注最大长度
    public static final int MAX_LENGTH_GOODS_ID = 32; // 商品编号最大长度
    public static final int MAX_LENGTH_GOODS_NAME = 32; // 商品名称最大长度
    public static final int MAX_LENGTH_INVOICE_ID = 32; // 小票ID最大长度

    // Decimal precision - 小数精度
    public static final String DECIMAL_FORMAT_AMOUNT = "decimal(7,2)"; // 金额格式
    public static final String DECIMAL_FORMAT_LARGE_AMOUNT = "decimal(10,2)"; // 大金额格式
    public static final String DECIMAL_FORMAT_RATIO = "decimal(4,2)"; // 比例格式

    // WeChat Mini Program info - 微信小程序信息
    public static final String XCX_PAGE_PATH = "/pages/subPackage2/pay/index"; // 嗨客小程序支付页面地址

    public static final String STAFF_NUM_DEFAULT_VALUE = "60000000";  // 收银员编号默认值
    public static final String STORE_NUM_DEFAULT_VALUE = "1000000";  // store_num编号默认值
}
