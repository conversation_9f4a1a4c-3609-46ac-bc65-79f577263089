package com.wosai.mpay.api.haikeduo;

import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Digest;

import java.util.*;

/**
 * HaikeDuo API Signature Generator for 嗨客多 Payment Service
 * 嗨客多支付接口签名生成器
 *
 * 签名方式（基于官方文档）：
 * 1. 参与签名的参数：接口的所有标量参数。sign字段、对象和数组不参与签名，其他字段不管是否为空都要参与签名。
 * 2. 所有参数以ASCII字典升序排列
 * 3. 拼接待签名字符串：以key1=value1&key2=value2形式将参与签名的参数，拼接成待签名字符串。并在待签名字符串最后拼接上"&key=APPKEY"
 * 4. MD5摘要：使用MD5摘要算法对带签名字符串做签名，以得到的32个字符十六进制小写字符串作为签名字段sign的值
 */
public class HaikeDuoSignature {
    private static final String CHARSET = "UTF-8";

    /**
     * Generate signature for HaikeDuo API request
     * 为嗨客多API请求生成签名
     *
     * @param params Request parameters 请求参数
     * @param appKey Application key (secret) 应用密钥
     * @return MD5 signature MD5签名
     * @throws MpayException if signature generation fails 如果签名生成失败
     */
    public static String sign(Map<String, Object> params, String appKey) throws MpayException {
        try {
            // 1. Remove sign field and filter out objects/arrays
            // 1. 移除sign字段并过滤掉对象和数组
            Map<String, Object> filteredParams = filterSignatureParams(params);

            // 2. Sort parameters by ASCII dictionary order
            // 2. 按ASCII字典序排序参数
            Map<String, Object> sortedParams = new TreeMap<>(filteredParams);

            // 3. Build signature string
            // 3. 构建签名字符串
            StringBuilder signatureString = new StringBuilder();
            boolean first = true;
            for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
                if (!first) {
                    signatureString.append("&");
                }
                signatureString.append(entry.getKey()).append("=").append(entry.getValue());
                first = false;
            }

            // 4. Append app key
            // 4. 追加应用密钥
            signatureString.append("&key=").append(appKey);

            // 5. Calculate MD5 hash
            // 5. 计算MD5哈希
            String signature = Digest.md5(signatureString.toString().getBytes(CHARSET));
            return signature.toLowerCase();

        } catch (Exception e) {
            throw new MpayException("Failed to generate signature: " + e.getMessage(), e);
        }
    }

    /**
     * Verify signature from HaikeDuo API response
     * 验证嗨客多API响应的签名
     *
     * @param params Response parameters 响应参数
     * @param appKey Application key (secret) 应用密钥
     * @param signature Signature to verify 待验证的签名
     * @return true if signature is valid, false otherwise 签名有效返回true，否则返回false
     * @throws MpayException if signature verification fails 如果签名验证失败
     */
    public static boolean verify(Map<String, Object> params, String appKey, String signature) throws MpayException {
        if (signature == null || signature.trim().isEmpty()) {
            return false;
        }

        // Add appid to response parameters for verification (as per documentation)
        // 将appid添加到响应参数中进行验证（根据文档要求）
        Map<String, Object> verifyParams = new HashMap<>(params);
        
        String calculatedSignature = sign(verifyParams, appKey);
        return calculatedSignature.equalsIgnoreCase(signature.trim());
    }

    /**
     * Filter parameters for signature generation
     * 过滤用于签名生成的参数
     * 
     * 根据文档：sign字段、对象和数组不参与签名，其他字段不管是否为空都要参与签名
     *
     * @param params Original parameters 原始参数
     * @return Filtered parameters 过滤后的参数
     */
    private static Map<String, Object> filterSignatureParams(Map<String, Object> params) {
        Map<String, Object> filteredParams = new HashMap<>();
        
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            // Skip sign field
            // 跳过sign字段
            if (HaikeDuoRequestFields.SIGN.equals(key)) {
                continue;
            }
            
            // Skip objects and arrays (but include null and empty strings as per documentation)
            // 跳过对象和数组（但根据文档包含null和空字符串）
            if (value instanceof Map || value instanceof List || value instanceof Object[]) {
                continue;
            }
            
            // Include all scalar values (including null and empty strings)
            // 包含所有标量值（包括null和空字符串）
            filteredParams.put(key, value == null ? "" : value.toString());
        }
        
        return filteredParams;
    }

    /**
     * Build query string from parameters (for debugging)
     * 从参数构建查询字符串（用于调试）
     *
     * @param params Parameters 参数
     * @return Query string 查询字符串
     */
    public static String buildQueryString(Map<String, Object> params) {
        Map<String, Object> filteredParams = filterSignatureParams(params);
        Map<String, Object> sortedParams = new TreeMap<>(filteredParams);
        
        StringBuilder queryString = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
            if (!first) {
                queryString.append("&");
            }
            queryString.append(entry.getKey()).append("=").append(entry.getValue());
            first = false;
        }
        
        return queryString.toString();
    }

    /**
     * Build signature string with app key (for debugging)
     * 构建包含应用密钥的签名字符串（用于调试）
     *
     * @param params Parameters 参数
     * @param appKey Application key 应用密钥
     * @return Signature string 签名字符串
     */
    public static String buildSignatureString(Map<String, Object> params, String appKey) {
        String queryString = buildQueryString(params);
        return queryString + "&key=" + appKey;
    }

    /**
     * Validate signature parameters
     * 验证签名参数
     *
     * @param params Parameters to validate 待验证的参数
     * @param appKey Application key 应用密钥
     * @throws MpayException if validation fails 如果验证失败
     */
    public static void validateSignatureParams(Map<String, Object> params, String appKey) throws MpayException {
        if (params == null || params.isEmpty()) {
            throw new MpayException("Parameters cannot be null or empty for signature generation");
        }
        
        if (appKey == null || appKey.trim().isEmpty()) {
            throw new MpayException("App key cannot be null or empty for signature generation");
        }
        
        // Check required authentication fields
        // 检查必需的认证字段
        String[] requiredFields = {
            HaikeDuoRequestFields.APPID,
            HaikeDuoRequestFields.TIMESTAMP,
            HaikeDuoRequestFields.MERCHANT_NUM
        };
        
        for (String field : requiredFields) {
            if (!params.containsKey(field) || params.get(field) == null) {
                throw new MpayException("Required field missing for signature: " + field);
            }
        }
    }

    /**
     * Generate signature with validation
     * 生成带验证的签名
     *
     * @param params Request parameters 请求参数
     * @param appKey Application key 应用密钥
     * @return MD5 signature MD5签名
     * @throws MpayException if signature generation fails 如果签名生成失败
     */
    public static String signWithValidation(Map<String, Object> params, String appKey) throws MpayException {
        validateSignatureParams(params, appKey);
        return sign(params, appKey);
    }
}
