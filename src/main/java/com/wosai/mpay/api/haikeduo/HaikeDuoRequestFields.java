package com.wosai.mpay.api.haikeduo;

/**
 * HaikeDuo API Request Fields for 嗨客多 Payment Service
 * 嗨客多支付接口请求字段
 * 
 * 基于嗨客多官方API文档定义的请求字段名称
 * 所有字段名称严格按照API文档规范
 */
public class HaikeDuoRequestFields {

    // Common request fields - 公共请求字段
    public static final String APPID = "appid"; // 应用ID
    public static final String TIMESTAMP = "timestamp"; // 请求Unix时间戳【秒】
    public static final String MERCHANT_NUM = "merchant_num"; // 商户编号
    public static final String SIGN = "sign"; // 签名字符串

    // Store and staff fields - 门店和员工字段
    public static final String STORE_NUM = "store_num"; // 门店编号
    public static final String STAFF_NUM = "staff_num"; // 收银员编号

    // Order fields - 订单字段
    public static final String MERCHANT_ORDER_NO = "merchant_order_no"; // 商家订单号
    public static final String ORDER_NUMBER = "order_number"; // 平台订单编号
    public static final String TRADE_NO = "trade_no"; // 收单机构交易流水号
    public static final String MERCHANT_TRADE_NO = "merchant_trade_no"; // 扫码交易流水号

    // Payment fields - 支付字段
    public static final String AMOUNT = "amount"; // 支付金额
    public static final String SOFTWARE = "software"; // 支付软件
    public static final String IS_REDIRECT_XCX = "is_redirect_xcx"; // 是否跳转小程序支付
    public static final String MCH_REMARKS = "mch_remarks"; // 交易附加信息

    // Refund fields - 退款字段
    public static final String MERCHANT_REFUND_NO = "merchant_refund_no"; // 商家退款单号
    public static final String REFUND_AMOUNT = "amount"; // 退款金额
    public static final String REMARK = "remark"; // 退款备注

    // Separate account fields - 分账字段
    public static final String IS_SEPARATE = "is_separate"; // 是否分账
    public static final String SEPARATE_METHOD = "separate_method"; // 分账方式
    public static final String SEPARATE_DETAIL = "separate_detail"; // 分账关系

    // Invoice fields - 发票字段
    public static final String INVOICE = "invoice"; // 商家小票商品信息
    public static final String INVOICE_ID = "invoice.id"; // 小票ID
    public static final String INVOICE_COST_PRICE = "invoice.cost_price"; // 小票原价
    public static final String INVOICE_DETAILS = "invoice.details"; // 商品详情列表

    // Invoice detail fields - 发票详情字段
    public static final String INVOICE_DETAILS_GOODS_ID = "invoice.details.goods_id"; // 商品编号
    public static final String INVOICE_DETAILS_GOODS_NAME = "invoice.details.goods_name"; // 商品名称
    public static final String INVOICE_DETAILS_QUANTITY = "invoice.details.quantity"; // 商品数量
    public static final String INVOICE_DETAILS_PRICE = "invoice.details.price"; // 商品价格

    // Nested invoice fields (for object structure) - 嵌套发票字段
    public static final String INVOICE_NESTED_ID = "id"; // 小票ID (在invoice对象内)
    public static final String INVOICE_NESTED_COST_PRICE = "cost_price"; // 小票原价 (在invoice对象内)
    public static final String INVOICE_NESTED_DETAILS = "details"; // 商品详情列表 (在invoice对象内)

    // Nested invoice detail fields (for array structure) - 嵌套发票详情字段
    public static final String INVOICE_DETAIL_GOODS_ID = "goods_id"; // 商品编号 (在details数组内)
    public static final String INVOICE_DETAIL_GOODS_NAME = "goods_name"; // 商品名称 (在details数组内)
    public static final String INVOICE_DETAIL_QUANTITY = "quantity"; // 商品数量 (在details数组内)
    public static final String INVOICE_DETAIL_PRICE = "price"; // 商品价格 (在details数组内)

    // Separate detail fields - 分账详情字段
    public static final String SEPARATE_DETAIL_DIVIDED_AMOUNT = "divided_amount"; // 分账金额
    public static final String SEPARATE_DETAIL_ORDER_AMOUNT = "order_amount"; // 订单金额
    public static final String SEPARATE_DETAIL_MERCHANT_NAME = "merchant_name"; // 商户名称
    public static final String SEPARATE_DETAIL_ORDER_ID = "order_id"; // 订单编号
    public static final String SEPARATE_DETAIL_RATIO = "ratio"; // 分账比例
    public static final String SEPARATE_DETAIL_CREATED_AT = "created_at"; // 创建时间

    // Query specific fields - 查询专用字段
    // Note: For order query, either order_number or merchant_order_no must be provided
    // 注意：订单查询时，order_number 与 merchant_order_no 二者必传其一

    // Refund specific fields - 退款专用字段
    // Note: For refund, one of order_number, merchant_order_no, trade_no, or merchant_trade_no must be provided
    // 注意：退款时，order_number、merchant_order_no、trade_no、merchant_trade_no 四者必传其一

    // Field requirement indicators - 字段必填标识
    public static final String REQUIRED_TRUE = "true"; // 必填
    public static final String REQUIRED_FALSE = "false"; // 非必填
    public static final String REQUIRED_CONDITIONAL = "C"; // 条件必填

    // Data type indicators - 数据类型标识
    public static final String TYPE_STRING = "string"; // 字符串类型
    public static final String TYPE_INT = "int"; // 整数类型
    public static final String TYPE_OBJECT = "object"; // 对象类型
    public static final String TYPE_ARRAY = "[]"; // 数组类型
    public static final String TYPE_ARRAY_OBJECT = "[]object"; // 对象数组类型
    public static final String TYPE_DECIMAL = "decimal"; // 小数类型
}
