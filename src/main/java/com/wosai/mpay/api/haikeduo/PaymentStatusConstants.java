package com.wosai.mpay.api.haikeduo;

/**
 * HaikeDuo Payment Status Constants for 嗨客多 Payment Service
 * 嗨客多支付状态常量类
 * 
 * 基于嗨客多官方API文档定义的支付状态枚举
 * 支付状态：0-等待支付，1-支付成功，2-支付已撤销/已关闭，3-全额退款，4-支付失败，5-交易结束，不可退款，6-部分退款
 */
public class PaymentStatusConstants {

    /**
     * 等待支付
     * 订单已创建，等待用户支付
     */
    public static final int WAITING_PAYMENT = 0;

    /**
     * 支付成功
     * 用户已完成支付，交易成功
     */
    public static final int PAYMENT_SUCCESS = 1;

    /**
     * 支付已撤销/已关闭
     * 订单被撤销或关闭，无法继续支付
     */
    public static final int PAYMENT_CANCELLED_OR_CLOSED = 2;

    /**
     * 全额退款
     * 订单已全额退款
     */
    public static final int FULL_REFUND = 3;

    /**
     * 支付失败
     * 支付过程中发生错误，支付失败
     */
    public static final int PAYMENT_FAILED = 4;

    /**
     * 交易结束，不可退款
     * 交易已结束，不允许退款操作
     */
    public static final int TRANSACTION_FINISHED = 5;

    /**
     * 部分退款
     * 订单已部分退款，但未全额退款
     */
    public static final int PARTIAL_REFUND = 6;

    // Status descriptions - 状态描述
    public static final String DESC_WAITING_PAYMENT = "等待支付";
    public static final String DESC_PAYMENT_SUCCESS = "支付成功";
    public static final String DESC_PAYMENT_CANCELLED_OR_CLOSED = "支付已撤销/已关闭";
    public static final String DESC_FULL_REFUND = "全额退款";
    public static final String DESC_PAYMENT_FAILED = "支付失败";
    public static final String DESC_TRANSACTION_FINISHED = "交易结束，不可退款";
    public static final String DESC_PARTIAL_REFUND = "部分退款";

    /**
     * Get payment status description
     * 获取支付状态描述
     *
     * @param status 支付状态码
     * @return 状态描述
     */
    public static String getStatusDescription(int status) {
        switch (status) {
            case WAITING_PAYMENT:
                return DESC_WAITING_PAYMENT;
            case PAYMENT_SUCCESS:
                return DESC_PAYMENT_SUCCESS;
            case PAYMENT_CANCELLED_OR_CLOSED:
                return DESC_PAYMENT_CANCELLED_OR_CLOSED;
            case FULL_REFUND:
                return DESC_FULL_REFUND;
            case PAYMENT_FAILED:
                return DESC_PAYMENT_FAILED;
            case TRANSACTION_FINISHED:
                return DESC_TRANSACTION_FINISHED;
            case PARTIAL_REFUND:
                return DESC_PARTIAL_REFUND;
            default:
                return "未知状态";
        }
    }

    /**
     * Check if payment is successful
     * 检查支付是否成功
     *
     * @param status 支付状态码
     * @return true if payment is successful, false otherwise
     */
    public static boolean isPaymentSuccessful(int status) {
        return status == PAYMENT_SUCCESS;
    }

    /**
     * Check if payment is pending
     * 检查支付是否待处理
     *
     * @param status 支付状态码
     * @return true if payment is pending, false otherwise
     */
    public static boolean isPaymentPending(int status) {
        return status == WAITING_PAYMENT;
    }

    /**
     * Check if payment is failed or cancelled
     * 检查支付是否失败或取消
     *
     * @param status 支付状态码
     * @return true if payment is failed or cancelled, false otherwise
     */
    public static boolean isPaymentFailedOrCancelled(int status) {
        return status == PAYMENT_FAILED || status == PAYMENT_CANCELLED_OR_CLOSED;
    }

    /**
     * Check if order has refund
     * 检查订单是否有退款
     *
     * @param status 支付状态码
     * @return true if order has refund, false otherwise
     */
    public static boolean hasRefund(int status) {
        return status == FULL_REFUND || status == PARTIAL_REFUND;
    }

    /**
     * Check if order can be refunded
     * 检查订单是否可以退款
     *
     * @param status 支付状态码
     * @return true if order can be refunded, false otherwise
     */
    public static boolean canRefund(int status) {
        return status == PAYMENT_SUCCESS || status == PARTIAL_REFUND;
    }

    /**
     * Check if order is finished
     * 检查订单是否已结束
     *
     * @param status 支付状态码
     * @return true if order is finished, false otherwise
     */
    public static boolean isFinished(int status) {
        return status == TRANSACTION_FINISHED || status == FULL_REFUND || 
               status == PAYMENT_CANCELLED_OR_CLOSED || status == PAYMENT_FAILED;
    }
}
