package com.wosai.mpay.api.haikeduo;

/**
 * HaikeDuo API Response Fields for 嗨客多 Payment Service
 * 嗨客多支付接口响应字段
 * 
 * 基于嗨客多官方API文档定义的响应字段名称
 * 所有字段名称严格按照API文档规范
 */
public class HaikeDuoResponseFields {

    // Common response fields - 公共响应字段
    public static final String CODE = "code"; // 返回码
    public static final String MESSAGE = "message"; // 返回码说明
    public static final String DATA = "data"; // 返回数据
    public static final String SIGN = "sign"; // 签名字符串

    // Pay QRCode response fields - 仿Native支付响应字段
    public static final String ORDER_NUMBER = "order_number"; // 平台单号
    public static final String URL = "url"; // 支付地址
    public static final String CODE_TEMP = "code"; // 临时code码 (微信小程序跳转需要)
    public static final String XCX_ORIGIN_ID = "xcx_origin_id"; // 微信小程序原始ID
    public static final String XCX_APP_ID = "xcx_app_id"; // 微信小程序APPID

    // Order query response fields - 订单查询响应字段
    public static final String TRADE_NO = "trade_no"; // 收单机构交易流水号
    public static final String MERCHANT_TRADE_NO = "merchant_trade_no"; // 扫码交易流水号
    public static final String PAY_STATUS = "pay_status"; // 支付状态
    public static final String PAY_AT = "pay_at"; // 交易完成时间
    public static final String DEAL_MOUNT = "deal_mount"; // 实际交易金额
    public static final String REFUND_AMOUNT = "refund_amount"; // 订单累计退款金额
    public static final String COMMISSION_CHARGE = "commission_charge"; // 订单手续费
    public static final String ARRIVE_MOUNT = "arrive_mount"; // 结算金额
    public static final String REMARKS = "remarks"; // 消费者付款备注
    public static final String MCH_REMARKS = "mch_remarks"; // 交易附加信息
    public static final String MERCHANT_ORDER_NO = "merchant_order_no"; // 商户订单号
    public static final String C_CHANNEL = "c_channel"; // 支付方式
    public static final String CHANNEL_TYPE = "channel_type"; // 支付类型
    public static final String PAY_RESOURCE = "pay_resource"; // 支付来源

    // Separate details fields - 分账明细字段
    public static final String SEPARATE_DETAILS = "separate_details"; // 分账明细
    public static final String SEPARATE_DETAILS_DIVIDED_AMOUNT = "separate_details.divided_amount"; // 分账金额
    public static final String SEPARATE_DETAILS_ORDER_AMOUNT = "separate_details.order_amount"; // 订单金额
    public static final String SEPARATE_DETAILS_MERCHANT_NAME = "separate_details.merchant_name"; // 商户名称
    public static final String SEPARATE_DETAILS_ORDER_ID = "separate_details.order_id"; // 订单编号
    public static final String SEPARATE_DETAILS_RATIO = "separate_details.ratio"; // 分账比例
    public static final String SEPARATE_DETAILS_CREATED_AT = "separate_details.created_at"; // 创建时间

    // Promotion fields - 优惠信息字段
    public static final String PROMOTION = "promotion"; // 优惠信息
    public static final String PROMOTION_TOTAL_AMOUNT = "promotion.total_amount"; // 总优惠金额
    public static final String PROMOTION_COUNT = "promotion.count"; // 优惠券数量
    public static final String PROMOTION_DETAILS = "promotion.details"; // 优惠详细信息列表

    // Promotion detail fields - 优惠详情字段
    public static final String PROMOTION_DETAILS_FUNDER = "promotion.details.funder"; // 出资方
    public static final String PROMOTION_DETAILS_AMOUNT = "promotion.details.amount"; // 出资金额
    public static final String PROMOTION_DETAILS_NAME = "promotion.details.name"; // 活动名称或优惠描述
    public static final String PROMOTION_DETAILS_COUPON_ID = "promotion.details.coupon_id"; // 券ID
    public static final String PROMOTION_DETAILS_ACTIVITY_ID = "promotion.details.activity_id"; // 活动ID
    public static final String PROMOTION_DETAILS_SCOPE = "promotion.details.scope"; // 适用范围
    public static final String PROMOTION_DETAILS_TYPE = "promotion.details.type"; // 券或优惠类别

    // Refund response fields - 退款响应字段
    public static final String REFUND_NO = "refund_no"; // 平台退款单号
    public static final String REFUND_AT = "refund_at"; // 退款成功时间戳
    public static final String REFUND_STATUS = "refund_status"; // 退款状态
    public static final String FAIL_REASON = "fail_reason"; // 退款失败原因
    public static final String MERCHANT_REFUND_NO = "merchant_refund_no"; // 商家退款单号

    // Nested object field names (for object structure) - 嵌套对象字段名称
    public static final String NESTED_DIVIDED_AMOUNT = "divided_amount"; // 分账金额 (在separate_details对象内)
    public static final String NESTED_ORDER_AMOUNT = "order_amount"; // 订单金额 (在separate_details对象内)
    public static final String NESTED_MERCHANT_NAME = "merchant_name"; // 商户名称 (在separate_details对象内)
    public static final String NESTED_ORDER_ID = "order_id"; // 订单编号 (在separate_details对象内)
    public static final String NESTED_RATIO = "ratio"; // 分账比例 (在separate_details对象内)
    public static final String NESTED_CREATED_AT = "created_at"; // 创建时间 (在separate_details对象内)

    public static final String NESTED_TOTAL_AMOUNT = "total_amount"; // 总优惠金额 (在promotion对象内)
    public static final String NESTED_COUNT = "count"; // 优惠券数量 (在promotion对象内)
    public static final String NESTED_DETAILS = "details"; // 优惠详细信息列表 (在promotion对象内)

    public static final String NESTED_FUNDER = "funder"; // 出资方 (在promotion.details对象内)
    public static final String NESTED_AMOUNT = "amount"; // 出资金额 (在promotion.details对象内)
    public static final String NESTED_NAME = "name"; // 活动名称 (在promotion.details对象内)
    public static final String NESTED_COUPON_ID = "coupon_id"; // 券ID (在promotion.details对象内)
    public static final String NESTED_ACTIVITY_ID = "activity_id"; // 活动ID (在promotion.details对象内)
    public static final String NESTED_SCOPE = "scope"; // 适用范围 (在promotion.details对象内)
    public static final String NESTED_TYPE = "type"; // 券或优惠类别 (在promotion.details对象内)

    // Promotion scope values - 优惠适用范围值
    public static final String SCOPE_SINGLE = "SINGLE"; // 单品优惠
    public static final String SCOPE_GLOBAL = "GLOBAL"; // 全场代金券
    public static final String SCOPE_DISCOUNT = "DISCOUNT"; // 折扣券

    // Promotion type values - 优惠类别值
    public static final String TYPE_COUPON = "COUPON"; // 代金券/随行付-预充值优惠券
    public static final String TYPE_DISCOUNT = "DISCOUNT"; // 优惠券/随行付-免充值优惠券
    public static final String TYPE_UNKNOWN = "UNKNOWN"; // 未知
    public static final String TYPE_CASH = "CASH"; // 充值代金券
    public static final String TYPE_NO_CASH = "NO_CASH"; // 免充值代金券
    public static final String TYPE_ALIPAY_FIX_VOUCHER = "ALIPAY_FIX_VOUCHER"; // 全场代金券
    public static final String TYPE_ALIPAY_DISCOUNT_VOUCHER = "ALIPAY_DISCOUNT_VOUCHER"; // 折扣券
    public static final String TYPE_ALIPAY_ITEM_VOUCHER = "ALIPAY_ITEM_VOUCHER"; // 单品优惠券
    public static final String TYPE_ALIPAY_CASH_VOUCHER = "ALIPAY_CASH_VOUCHER"; // 现金抵价券
    public static final String TYPE_ALIPAY_BIZ_VOUCHER = "ALIPAY_BIZ_VOUCHER"; // 商家全场券
    public static final String TYPE_SXF00001 = "SXF00001"; // 随行付随机立减
    public static final String TYPE_DD01 = "DD01"; // 银联优惠，随机立减
    public static final String TYPE_CP01 = "CP01"; // 银联优惠代金券，无需领取
    public static final String TYPE_CP02 = "CP02"; // 银联优惠代金券，需事前领取

    // Funder values - 出资方值
    public static final String FUNDER_MERCHANT = "商户"; // 商户
    public static final String FUNDER_CHANNEL = "通道"; // 通道
    public static final String FUNDER_WEIXIN = "微信"; // 微信
    public static final String FUNDER_ALIPAY = "支付宝"; // 支付宝
    public static final String FUNDER_UNIONPAY = "银联"; // 银联
    public static final String FUNDER_BANK = "银行"; // 银行
    public static final String FUNDER_OTHER = "其他"; // 其他

    // Data type indicators - 数据类型标识
    public static final String TYPE_STRING = "string"; // 字符串类型
    public static final String TYPE_INT = "int"; // 整数类型
    public static final String TYPE_OBJECT = "object"; // 对象类型
    public static final String TYPE_ARRAY = "[]"; // 数组类型
    public static final String TYPE_ARRAY_OBJECT = "[]object"; // 对象数组类型
    public static final String TYPE_DECIMAL = "decimal"; // 小数类型
    public static final String TYPE_MIXED = "mixed"; // 混合类型

    public static final String status = "status"; // 状态
}
