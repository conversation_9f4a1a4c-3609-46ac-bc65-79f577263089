package com.wosai.mpay.api.haikeduo;

/**
 * HaikeDuo Refund Status Constants for 嗨客多 Payment Service
 * 嗨客多退款状态常量类
 * 
 * 基于嗨客多官方API文档定义的退款状态枚举
 * 退款状态：0-退款中，1-退款成功，2-退款失败
 */
public class RefundStatusConstants {

    /**
     * 退款中
     * 退款申请已提交，正在处理中
     */
    public static final int REFUNDING = 0;

    /**
     * 退款成功
     * 退款已成功完成
     */
    public static final int REFUND_SUCCESS = 1;

    /**
     * 退款失败
     * 退款处理失败
     */
    public static final int REFUND_FAILED = 2;

    // Status descriptions - 状态描述
    public static final String DESC_REFUNDING = "退款中";
    public static final String DESC_REFUND_SUCCESS = "退款成功";
    public static final String DESC_REFUND_FAILED = "退款失败";

    /**
     * Get refund status description
     * 获取退款状态描述
     *
     * @param status 退款状态码
     * @return 状态描述
     */
    public static String getStatusDescription(int status) {
        switch (status) {
            case REFUNDING:
                return DESC_REFUNDING;
            case REFUND_SUCCESS:
                return DESC_REFUND_SUCCESS;
            case REFUND_FAILED:
                return DESC_REFUND_FAILED;
            default:
                return "未知状态";
        }
    }

    /**
     * Check if refund is successful
     * 检查退款是否成功
     *
     * @param status 退款状态码
     * @return true if refund is successful, false otherwise
     */
    public static boolean isRefundSuccessful(int status) {
        return status == REFUND_SUCCESS;
    }

    /**
     * Check if refund is pending
     * 检查退款是否处理中
     *
     * @param status 退款状态码
     * @return true if refund is pending, false otherwise
     */
    public static boolean isRefundPending(int status) {
        return status == REFUNDING;
    }

    /**
     * Check if refund is failed
     * 检查退款是否失败
     *
     * @param status 退款状态码
     * @return true if refund is failed, false otherwise
     */
    public static boolean isRefundFailed(int status) {
        return status == REFUND_FAILED;
    }

    /**
     * Check if refund is completed (either success or failed)
     * 检查退款是否已完成（成功或失败）
     *
     * @param status 退款状态码
     * @return true if refund is completed, false otherwise
     */
    public static boolean isRefundCompleted(int status) {
        return status == REFUND_SUCCESS || status == REFUND_FAILED;
    }

    /**
     * Check if refund can be queried again
     * 检查是否可以再次查询退款状态
     * 
     * 根据文档：当 refund_status = 0 时，可调用退款查询接口
     *
     * @param status 退款状态码
     * @return true if refund can be queried again, false otherwise
     */
    public static boolean canQueryAgain(int status) {
        return status == REFUNDING;
    }
}
