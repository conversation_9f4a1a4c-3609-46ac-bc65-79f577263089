package com.wosai.mpay.api.hxbank;

/**
 * <AUTHOR>
 * @Description HXBankProtocolFields
 * @Date 2021/8/20 5:41 PM
 */
public class HXBankProtocolFields {
    public static final String VERSION = "version";         //接口版本号
    public static final String APP_ID = "appid";            //开发者ID,银行分配
    public static final String SIGNATURE = "signature";     //签名摘要,对body进行签名作业之后获取的摘要信息

    public static final String BODY = "body";
    public static final String PSC_DATE = "pcsDate";        //请求时间

}
