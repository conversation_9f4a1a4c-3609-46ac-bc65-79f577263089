package com.wosai.mpay.api.hxbank;

import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import com.wosai.mpay.util.hxbank.EnvApplication;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description HXBankClient
 * @Date 2021/8/20 5:38 PM
 */
public class HXBankClient {
    public static final Logger log = LoggerFactory.getLogger(HXBankClient.class);

    private static final String CONTENT_TYPE = "application/json;charset=utf-8";

    private int connectTimeout = 3000;
    private int readTimeout = 30000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
    private static EnvApplication envApplication = new EnvApplication();


    public Map<String, Object> call(String serviceUrl, Map<String, Object> request, String secretKey, String cert) throws Exception {
        log.info("original request is {}" , JsonUtil.objectToJsonString(request));

        String retSigned = null;
        try {
            retSigned = RsaSignature.hxbankSign(JsonUtil.objectToJsonString(MapUtils.getNestedProperty(request, HXBankProtocolFields.BODY)), RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, secretKey);
        } catch (Exception e) {
            log.error("加签异常", e);
        }

        // 加密:商户使用综合支付的公钥进行加密
        // 参数说明：(1)银行公钥证书；(2)对称算法AES(固定)；(3)待加密内容
        String sRetBody = "";
        try {
            sRetBody = envApplication.makeEnvelope(cert, AesUtil.AES, JsonUtil.objectToJsonString(MapUtils.getNestedProperty(request, HXBankProtocolFields.BODY)).getBytes(StandardCharsets.UTF_8));
        } catch (MpayException e) {
            log.error("公钥加密异常", e);
        }

        request.put(HXBankProtocolFields.SIGNATURE, retSigned);
        request.put(HXBankProtocolFields.BODY, sRetBody);
        log.info("encrypt request is {}", JsonUtil.objectToJsonString(request));
        String requestStr = JsonUtil.objectToJsonString(request);
        // 使用 "application/json" contentType发起请求
        String response = HttpClientUtils.doPost(HXBankClient.class.getName(), null, null, serviceUrl, CONTENT_TYPE, requestStr, "utf-8", connectTimeout, readTimeout);

        Map<String, Object> jsonResponse = JsonUtil.jsonStrToObject(response, Map.class);
        log.info("original response is {}", response);

        if (StringUtils.isEmpty(MapUtils.getString(jsonResponse, HXBankProtocolFields.BODY))){
            throw new MpayApiUnknownResponse("fetch response error:" + MapUtils.getString(jsonResponse, HXBankResponseFields.DATA));
        }
        // 解密：商户使用自己的私钥进行解密
        // 参数说明：(1)商户私钥(jks文件)；(2)密钥口令；(3)待解密内容(请求body内容)
        byte[] decryption = envApplication.openEnvelope(Base64.decode(secretKey), String.valueOf(jsonResponse.get(HXBankProtocolFields.BODY)));
        String body = new String(decryption, StandardCharsets.UTF_8);
        log.info("decrypt response is {}",body);

        return JsonUtil.jsonStrToObject(body, Map.class);
    }
}
