package com.wosai.mpay.api.hxbank;

import com.wosai.mpay.util.SafeSimpleDateFormat;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description HXBankTest
 * @Date 2021/8/23 1:53 PM
 */
public class HXBankTest {

    private static final String BASE_URL = "https://paytest.95577.com.cn/";
    private static final String PAY_URL = BASE_URL + "trans/OPC103040202000100002500";
    private static final String QUERY_USERID_URL = BASE_URL + "trans/OPC103040202000100004200";
    private static final String QUERY_URL = BASE_URL + "trans/OPC103040202000100003100";
    private static final String REFUND_URL = BASE_URL + "trans/OPC103040202000100002900";
    private static final String CANCEL_URL = BASE_URL + "trans/OPC103040202000100003000";
    private static final String ALI_PRECREATE_URL = BASE_URL + "trans/OPC103040202000100003300";
    private static final String WX_PRECREATE_URL = BASE_URL + "trans/OPC103040202000100002800";


    private static final String APP_ID = "38536434345161657444667144747A76675642325762374234796B3D";
    private static final String MERCHANT_NO = "***************";

    private static SafeSimpleDateFormat df = new SafeSimpleDateFormat("yyyyMMddHHmmss");

    //商户私钥
    private static final String skey = "";
    //银行公钥证书
    private static final String hxkey  = "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tDQpNSUlEYXpDQ0FsT2dBd0lCQWdJRVhXZElWakFO\n" +
            "QmdrcWhraUc5dzBCQVFzRkFEQndNUXN3Q1FZRFZRUUdFd0pEVGpFUU1BNEdBMVVFQnhNSFFtVnBh\n" +
            "bWx1WnpFWE1CVUdBMVVFQ2hNT2QzZDNMbWg0WWk1amIyMHVZMjR4RURBT0JnTlZCQWdUQjBKbGFX\n" +
            "cHBibWN4Q3pBSkJnTlZCQXNUQWtsVU1SY3dGUVlEVlFRREV3NTNkM2N1YUhoaUxtTnZiUzVqYmpB\n" +
            "ZUZ3MHhPVEE0TWprd016TTJOVFJhRncweU9UQTRNall3TXpNMk5UUmFNRnd4Q3pBSkJnTlZCQVlU\n" +
            "QWtOT01RMHdDd1lEVlFRS0RBUjZhSHBtTVJBd0RnWURWUVFIREFkQ1pXbHFhVzVuTVJBd0RnWURW\n" +
            "UVFJREFkQ1pXbHFhVzVuTVFzd0NRWURWUVFMREFKSlZERU5NQXNHQTFVRUF3d0VlbWg2WmpDQ0FT\n" +
            "SXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBSnBEb3JPVG9DUnRUbmhwQWw3\n" +
            "Yk9IajJjc0ZjeVhTTzVKMTRraXFiZUttbVlRYXVXOHBFNy84SVEwbGFxRGdWelFhMEdxcWJXdWts\n" +
            "Tzk0b1ZtSkNzc2NCSjFjaWVHUjU1a1lNK1dUamZudHJ0NkVKV3N5eklxZEx4aitiVXVEOEgrb1lC\n" +
            "bEMveUVNakZKS0RuaHBaY1BIc1VVb0owbDdodXUwNE5oWHBYbFUwQVVqNzFERHJScnVNRXBQbE1P\n" +
            "QVBhckJxQlBIWkQ3aXFVZ1lTNWVSUXlrQ0VQM25oZnFtQUNpSyswTHBnejJWNUc5cmUzSmpLZ3h4\n" +
            "NGlKQVFxQmRBeExUbU0zY2NjcmdKVUl5QUpoeXBOVnlRK29rakptT3hsbUJkcSsvYlNsUmhoVC9p\n" +
            "UzBUVDkyRG1kMkloSmJqVHhUSWZYNVZzWkVGZUt0QnExZ3VhcmswQ0F3RUFBYU1oTUI4d0hRWURW\n" +
            "UjBPQkJZRUZEMDkwNzB3d0xlM09pUzZSR2hSQWpTUXpWUE9NQTBHQ1NxR1NJYjNEUUVCQ3dVQUE0\n" +
            "SUJBUUEweUVxRXBWaXl1WXVGb2p1SmNRUlV4VW5iaC9KYzVMRy9qSWVud2JuSm5lSDlQdmErMSta\n" +
            "dlBObE9sa3dmSVh3YVBGL2FnUWpMZEhhd1ZRdEdEWldtZlBQK3c5ODEzL0s0SE1VOFZJZ2VqaTZL\n" +
            "Z2NoQzJMcTNQdWhBSzRhOGJ6emEyQWcwM29tSVBKQjBVd2VsalBqK0d1eDFmK1VsTXRVY21GZ01P\n" +
            "WkhFbEdybEJRbU44bnFjd2YzOTFXNUFqcGdEZk1hdTlqNVhIVVBBWWVDVFk2bWhVaWRoS2sxQmZD\n" +
            "dWdnQnFVYmJod1JJcitGdHhLZXhQSUNUUG9YekgycXdMb1BPNFpIa2lxTEZqWkt4RkFlcTczeWhG\n" +
            "bkFaM0ZMc1hOSFdIR2F5R2MvUTNxckRkRE16Z1VGMmpYWUdGbDRrUHE3MjNrVzJON2M0QkVEdERx\n" +
            "RVdiTg0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQ==";

    private static void queryUserIdTest() throws Exception {
        HXBankRequestBuilder requestBuilder = new HXBankRequestBuilder();

//        {"@timestamp":"2023-02-16T19:28:02.503+08:00","@version":1,"message":"original request is {\"appid\":\"336532462F337459627746326836382F363455624367737A5446413D\",\"body\":{\"appUpIdentifier\":\"UnionPay/1.0 CloudPay\",\"merchantId\":\"**********\",\"merchantNo\":\"***************\",\"orderNo\":\"****************\",\"pcsDate\":\"**************\",\"userAuthCode\":\"0622fc842002003c1T79ltwa\"},\"version\":\"1.0\"}","logger_name":"com.wosai.mpay.api.hxbank.HXBankClient","thread_name":"qtp436532993-14","level":"INFO","level_value":20000,"trace_id_key":"625ee37713756ff2"}


        requestBuilder.bizSet(HXBankProtocolFields.APP_ID, "336532462F337459627746326836382F363455624367737A5446413D");


        requestBuilder.bizBodySet(HXBankProtocolFields.PSC_DATE, df.format(new Date()));
        requestBuilder.bizBodySet(HXBankBusinessFields.MERCHANT_ID, "**********");
        requestBuilder.bizBodySet(HXBankBusinessFields.MERCHANT_NO, "***************");
        requestBuilder.bizBodySet(HXBankBusinessFields.ORDER_NO, System.currentTimeMillis() + "");
        requestBuilder.bizBodySet("appUpIdentifier", "UnionPay/1.0 CloudPay");
        requestBuilder.bizBodySet("userAuthCode", "0622fc842002003c1T79ltwa");


        HXBankClient hxBankClient = new HXBankClient();
        Map<String, Object> result = hxBankClient.call(QUERY_USERID_URL, requestBuilder.build(), skey, hxkey);


        System.out.println(result);
    }

    private static void payTest() throws Exception {
        HXBankRequestBuilder requestBuilder = new HXBankRequestBuilder();
        requestBuilder.bizSet(HXBankProtocolFields.APP_ID, APP_ID);


        requestBuilder.bizBodySet(HXBankProtocolFields.PSC_DATE, df.format(new Date()));
        requestBuilder.bizBodySet(HXBankBusinessFields.MERCHANT_NO, MERCHANT_NO);
        requestBuilder.bizBodySet(HXBankBusinessFields.ORDER_NO, System.currentTimeMillis() + "");
        requestBuilder.bizBodySet(HXBankBusinessFields.AUTH_CODE, "280227724059102205");
        requestBuilder.bizBodySet(HXBankBusinessFields.AMOUNT, "0.01");
        requestBuilder.bizBodySet(HXBankBusinessFields.SUBJECT, "TEST-003");


        HXBankClient hxBankClient = new HXBankClient();
        Map<String, Object> result = hxBankClient.call(PAY_URL, requestBuilder.build(), skey, hxkey);


        System.out.println(result);
    }

    private static void queryTest() throws Exception {
        HXBankRequestBuilder requestBuilder = new HXBankRequestBuilder();
        requestBuilder.bizSet(HXBankProtocolFields.APP_ID, APP_ID);

        requestBuilder.bizBodySet(HXBankProtocolFields.PSC_DATE, df.format(new Date()));
        requestBuilder.bizBodySet(HXBankBusinessFields.MERCHANT_NO, MERCHANT_NO);
        requestBuilder.bizBodySet(HXBankBusinessFields.ORDER_NO, "*************");
        requestBuilder.bizBodySet(HXBankBusinessFields.ORG_PCS_DATE, "********");
//        requestBuilder.bizBodySet(HXBankBusinessFields.TRADE_TYPE, "2");

        HXBankClient hxBankClient = new HXBankClient();
        Map<String, Object> result = hxBankClient.call(QUERY_URL, requestBuilder.build(), skey, hxkey);


        System.out.println(result);
    }


    private static void cancelTest() throws Exception {
        HXBankRequestBuilder requestBuilder = new HXBankRequestBuilder();
        requestBuilder.bizSet(HXBankProtocolFields.APP_ID, APP_ID);

        requestBuilder.bizBodySet(HXBankProtocolFields.PSC_DATE, df.format(new Date()));
        requestBuilder.bizBodySet(HXBankBusinessFields.MERCHANT_NO, MERCHANT_NO);
        requestBuilder.bizBodySet(HXBankBusinessFields.ORDER_NO, System.currentTimeMillis() + "");
        requestBuilder.bizBodySet(HXBankBusinessFields.ORIG_ORDER_NO, "**************");
        requestBuilder.bizBodySet(HXBankBusinessFields.ORG_PCS_DATE, "********");


        HXBankClient hxBankClient = new HXBankClient();
        Map<String, Object> result = hxBankClient.call(CANCEL_URL, requestBuilder.build(), skey, hxkey);


        System.out.println(result);
    }

    private static void refundTest() throws Exception {
        HXBankRequestBuilder requestBuilder = new HXBankRequestBuilder();
        requestBuilder.bizSet(HXBankProtocolFields.APP_ID, APP_ID);

        requestBuilder.bizBodySet(HXBankProtocolFields.PSC_DATE, df.format(new Date()));
        requestBuilder.bizBodySet(HXBankBusinessFields.MERCHANT_NO, MERCHANT_NO);
        requestBuilder.bizBodySet(HXBankBusinessFields.ORIG_ORDER_NO, "*************");
        requestBuilder.bizBodySet(HXBankBusinessFields.REFUND_ORDER_NO, System.currentTimeMillis() + "99");
        requestBuilder.bizBodySet(HXBankBusinessFields.AMOUNT, "0.01");
        requestBuilder.bizBodySet(HXBankBusinessFields.ORG_PCS_DATE, "********");


        HXBankClient hxBankClient = new HXBankClient();
        Map<String, Object> result = hxBankClient.call(REFUND_URL, requestBuilder.build(), skey, hxkey);


        System.out.println(result);
    }

    private static void preCreateTest() throws Exception {
        HXBankRequestBuilder requestBuilder = new HXBankRequestBuilder();
        requestBuilder.bizSet(HXBankProtocolFields.APP_ID, APP_ID);


        requestBuilder.bizBodySet(HXBankProtocolFields.PSC_DATE, df.format(new Date()));
        requestBuilder.bizBodySet(HXBankBusinessFields.MERCHANT_NO, MERCHANT_NO);
        requestBuilder.bizBodySet(HXBankBusinessFields.ORDER_NO, System.currentTimeMillis() + "");
        requestBuilder.bizBodySet(HXBankBusinessFields.AMOUNT, "0.01");
        requestBuilder.bizBodySet(HXBankBusinessFields.SUBJECT, "TEST-001");
        requestBuilder.bizBodySet(HXBankBusinessFields.BUYER_ID, "****************");
        requestBuilder.bizBodySet(HXBankBusinessFields.NOTIFY_URL, "https://shouqianba.com");




        HXBankClient hxBankClient = new HXBankClient();
        Map<String, Object> result = hxBankClient.call(ALI_PRECREATE_URL, requestBuilder.build(), skey, hxkey);


        System.out.println(result);
    }


    public static void main(String[] args) throws Exception {
        queryUserIdTest();
//        payTest();
//        refundTest();
//        cancelTest();
//        queryTest();
//        preCreateTest();
    }
}
