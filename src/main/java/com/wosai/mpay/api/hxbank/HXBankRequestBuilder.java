package com.wosai.mpay.api.hxbank;

import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @Description HXBankRequestBuilder
 * @Date 2021/8/23 1:45 PM
 */
public class HXBankRequestBuilder {

    Map<String, Object> request;
    Map<String, Object> bizBody;

    public HXBankRequestBuilder() {
        request = new TreeMap<>();
        bizBody = new TreeMap<>();

        request.put(HXBankProtocolFields.VERSION, HXBankConstants.VERSION_1);  //版本号，华夏固定传1.0
    }

    public Map<String, Object> build() {
        request.put(HXBankProtocolFields.BODY, bizBody);
        return request;
    }

    public void bizSet(String field, Object value) {
        request.put(field,  value);
    }

    public void bizBodySet(String field, Object value) {
        bizBody.put(field,  value);
    }

    public Map<String,Object> getRequest(){
        return request;
    }

    public Map<String,Object> getRequestBody(){
        return bizBody;
    }
}
