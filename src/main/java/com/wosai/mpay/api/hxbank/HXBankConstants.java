package com.wosai.mpay.api.hxbank;

/**
 * <AUTHOR>
 * @Description HXBankConstants
 * @Date 2021/8/23 11:10 AM
 */
public class HXBankConstants {

    //接口版本号
    public static final String VERSION_1 = "1.0";
    public static final String VERSION_2 = "2.0";

    /** 默认时间格式 **/
    public static final String DATE_TIME_FORMAT = "yyyyMMddHHmmss";

    public static final String LIMIT_PAY_NO_CREDIT = "no_credit"; //不能使用信用卡支付

    /*交易方式*/
    public static final String PAY_METHOD_ALI = "ALIPAY"; //交易方式-支付宝
    public static final String PAY_METHOD_WX = "WXPAY"; //交易方式-微信
    public static final String PAY_METHOD_YL = "YLPAY"; //交易方式-银联云闪付

    /*返回码*/
    public static final String RESP_CODE_SUCCESS = "000000"; //交易成功
    public static final String RESP_CODE_PROCESSING = "10400"; //订单处理中，请稍后查询交易结果
    public static final String RESP_CODE_LACK_OF_PARAMS = "9003"; //请求参数错误:缺少重要字段，例如merchantNo、pcsDate、subject、amount、orderNo、authCode

    public static final String RESP_CODE_NO_TRANSACTION = "2003"; //交易信息不存在:原流水信息不存在(退款/撤销：1、origOrderNo不存在 2、origOrderNo正确，orgPcsDate不正确)
    public static final String RESP_CODE_CANCEL_ERROR = "2005"; //不能撤销的流水(1、原交易已撤销 2、原交易已退款，包括部分退款)
    public static final String RESP_MSG_REPEAT_CANCEL = "不能撤销的流水:原交易已撤销"; //不能撤销的流水:原交易已撤销
    public static final String RESP_MSG_CANCEL_ERROR_CAUSE_REFUND = "不能撤销的流水:原交易已退款"; //不能撤销的流水:原交易已退款(包括部分退款)
    public static final String RESP_MSG_CANCEL_ERROR_CAUSE_NOT_PAY = "不能撤销的流水:原交易状态有误，不支持撤销操作"; //不能撤销的流水:原交易状态有误，不支持撤销操作
    public static final String RESP_MSG_CANCEL_ERROR_CAUSE_PRECREATE = "不能撤销的流水:原交易不是被扫交易，不支持撤销操作"; //只有被扫交易才可以撤单
    public static final String RESP_CODE_NO_CROSS_DAY_CANCEL = "4046"; //只能撤销当日的交易:只能撤销当日的交易

    public static final String RESP_CODE_ERROR_ORIG_ORDER_NO_REFUND = "2010"; //不能退款的流水:该笔流水未支付成功,不能退款
    public static final String RESP_CODE_ERROR_REFUND_AMOUNT = "2010"; //错误的退款金额 (1、此交易的金额已全部返还，无法再次退款 2、退款金额超额）


    public static final String RESP_CODE_NO_TRANSACTION_QUERY = "2023"; //原交易流水不存在:查询流水信息信息不存在


    /*支付状态响应码*/
    public static final String STATUS_CODE_OO = "OO"; //成功
    public static final String STATUS_CODE_WI = "WI"; //需要轮询
    public static final String STATUS_CODE_FL = "FL"; //失败

    /*交易类型*/
    public static final String TRADE_TYPE_PAY = "1"; //支付
    public static final String TRADE_TYPE_REFUND_OR_CANCEL = "2"; //退款/撤销


    /*异步通知交易状态*/
    public static final String NOTIFY_STATUS_SUCCESS = "100"; //支付成功（支付订单）或退款成功（退款订单）
    public static final String NOTIFY_STATUS_WAIT_PAY = "101"; //待支付
    public static final String NOTIFY_STATUS_FAIL = "102"; //支付失败（支付订单）或退款失败（退款订单）
    public static final String NOTIFY_STATUS_PROCESSING = "103"; //订单处理中
    public static final String NOTIFY_STATUS_CANCELED = "104"; //已撤销

    //aliVoucherDetailList type 优惠券类型
    public static final String ALIPAY_FIX_VOUCHER = "ALIPAY_FIX_VOUCHER";          //全场代金券
    public static final String ALIPAY_DISCOUNT_VOUCHER = "ALIPAY_DISCOUNT_VOUCHER";//折扣券
    public static final String ALIPAY_ITEM_VOUCHER = "ALIPAY_ITEM_VOUCHER";        //单品优惠券
    public static final String ALIPAY_CASH_VOUCHER = "ALIPAY_CASH_VOUCHER";        //现金抵价券
    public static final String ALIPAY_BIZ_VOUCHER = "ALIPAY_BIZ_VOUCHER";          //商家全场券

    public static final String NOTIFY_SUCCESS = "SUCCESS";          //异步通知返回成功应答

    public static final String TERM_INFO_TERMINAL_TYPE = "11"; //条码支付辅助受理终端

    /*主扫场景下二维码的类型*/
    public static final String QR_CODE_TYPE_DYNAMIC = "0"; //0-动态码
    public static final String QR_CODE_TYPE_STATIC = "1"; //1-静态码

    //业务种类编码
    public static final String SVC_SYS_CODE_CONSUME = "D203"; //D203 – 消费
}
