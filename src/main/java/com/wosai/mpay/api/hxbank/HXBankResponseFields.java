package com.wosai.mpay.api.hxbank;

/**
 * <AUTHOR>
 * @Description HXBankResponseFields
 * @Date 2021/8/23 9:09 AM
 */
public class HXBankResponseFields {
    public static final String RESP_CODE = "respCode"; //返回码
    public static final String RESP_MSG = "respMsg"; //返回信息
    public static final String STATUS_CODE = "statusCode"; //支付状态响应码  OO:成功 WI:需要轮询 FL:失败
    public static final String STATUS_MSG = "statusMsg"; //支付状态响应信息
    public static final String CURRENCY = "currency"; //支付货币类型
    public static final String AMOUNT = "amount"; //订单金额  单位为元，两位小数
    public static final String REFUND_AMOUNT = "refundAmount "; //退款金额  tradeType为2-退款/撤销时必返
    public static final String PAY_METHOD = "payMethod"; //交易方式  支付宝--ALIPAY,银联云闪付-YLPAY,微信-WXPAY
    public static final String TRADE_TYPE = "tradeType"; //交易类型  1 -支付， 2-退款/撤销
    public static final String AUTH_CODE = "authCode"; //付款码

    public static final String ORDER_NO = "orderNo"; //商户流水号  合作方上送的流水号
    public static final String QRCODE = "qrcode"; //二维码链接

    public static final String TRADE_NO = "tradeNo"; //平台流水号  微信、支付宝、银联等平台返回的订单号
    public static final String CHANNEL_NO = "channelNo"; //商户通道交易订单号  银行系统生成，发往微信支付宝银联通道的通道交易订单号
    public static final String BANK_TYPE = "bankType"; //付款银行  银行类型，采用字符串类型的银行标识
    public static final String BUYER_ID = "buyerId"; //付款用户  付款人编号
    public static final String SUB_OPEN_ID = "subOpenId"; //微信用户子标识  微信交易子商户appid下用户唯一标识
    public static final String BUYER_USER_ID = "buyerUserId"; //买家在支付宝的用户id
    public static final String SUCCESS_TIME = "successTime"; //支付成功时间  yyyyMMddHHmmss
    public static final String YL_ECT_DETAIL = "ylDctDetail"; //银联-商品优惠明细内容
    public static final String YL_COUPON_INFO = "ylCouponInfo"; //银联-优惠信息
    public static final String YL_COUPON_INFO_DETAIL_ID = "id"; // 项目编号 票券编号、活动编号等
    public static final String YL_COUPON_INFO_DETAIL_TYPE = "type"; // 项目类型  DD01-随机立减 CP01-抵金券1：无需领取，交易时直接适配并承兑的优惠券  CP02-抵金券2：事前领取，交易时上送银联并承兑的优惠券
    public static final String YL_COUPON_INFO_DETAIL_SPNSR_ID = "spnsrId"; // 出资方  银联作为出资方：固定填写00010000  付款方作为出资：填写8位付款方机构代码 商户作为出资方：填写15位商户代码。
    public static final String YL_COUPON_INFO_DETAIL_OFFST_AMT = "offstAmt"; // 抵消交易金额  不能全为0，单位为元，两位小数
    public static final String YL_COUPON_INFO_DETAIL_DESC = "desc"; // 项目简称  优惠活动简称，可用于展示、打单等
    public static final String YL_COUPON_INFO_DETAIL_ADDNINFO = "addnInfo"; // 附加信息

    public static final String USER_PAY_AMT = "userPayAmt"; //实际付款金额  消费者实际付款金额。tradeType=1时，通道返回，则返回。

    public static final String WX_PROMOTION_DETAIL = "wxPromotionDetail"; //微信-优惠功能

    public static final String CHARGE_FLAGS = "chargeFlags"; //支付宝-费率活动标识  支付宝-费率活动标识，当交易享受活动优惠费率时，返回该活动的标识；(1)蓝海活动优惠费率0，值为bluesea_1;(2)特殊行业优惠费率0，值为industry_special_00;(3)特殊行业优惠费率千一，值为industry_special_01
    public static final String ALI_VOUCHER_DETAIL_LIST = "aliVoucherDetailList"; //支付宝-优惠券信息
    public static final String ALI_DISCOUNT_GOODS_DETAIL = "aliDiscountGoodsDetail"; //支付宝-商品优惠明细内容


    public static final String WX_PAY_INFO = "wxPayInfo"; //微信支付信息
    public static final String MER_ID = "merId"; //商户号  银联商户号
    public static final String APP_ID = "appid"; //公众账号ID  微信返回的appid
    public static final String SUB_APP_ID = "subAppid"; //子公众账号ID  微信返回的子商户公众账号ID
    public static final String MCH_ID = "mchId"; //商户号  微信支付分配的商户号


    public static final String CHANNEL_ORDER_NO = "channelOrderNo"; //商户通道退款订单号  银行系统生成，发往微信支付宝银联通道的通道退款交易订单号
    public static final String ORIG_CHANNEL_NO = "origChannelNo"; //原商户通道交易订单号  需要退款的原商户通道交易订单号
    public static final String WX_SETTLEMENT_REFUND_FEE = "wxSettlementRefundFee"; //微信-退款金额  微信-去掉非充值代金券退款金额后的退款金额，退款金额=申请退款金额-非充值代金券退款金额，退款金额<=申请退款金额
    public static final String WX_CASH_REFUND_FEE = "wxCashRefundFee"; //微信-现金退款金额  单位-元
    public static final String WX_COUPON_REFUND_FEE = "wxCouponRefundFee"; //微信-代金券退款总金额  微信-代金券退款金额<=退款金额，退款金额-代金券或立减优惠退款金额为现金
    public static final String WX_REFUND_DETAIL = "wxRefundDetail"; //微信-优惠退款详情
    public static final String ALI_PRESENT_REFUND_BUYER_AMOUNT = "aliPresentRefundBuyerAmount"; //支付宝-买家退款金额  支付宝-本次退款金额中买家退款金额
    public static final String ALI_PRESENT_REFUND_DISCOUNT_AMOUNT = "aliPresentRefundDiscountAmount"; //支付宝-平台优惠退款金额  支付宝-本次退款金额中平台优惠退款金额
    public static final String ALI_PRESENT_REFUND_M_DISCOUNT_AMOUNT = "aliPresentRefundMdiscountAmount"; //支付宝-商家优惠退款金额  支付宝-本次退款金额中商家优惠退款金额

    public static final String STATUS = "status"; //交易状态
    public static final String DATA = "data"; //返回数据（用于异常情况下，例如：验签失败）


    //aliVoucherDetailList支付宝-优惠券信息
    public static final String VOUCHER_ID = "voucherId";                                    //券id
    public static final String VOUCHER_NAME = "name";                                       //券名称
    public static final String VOUCHER_TYPE = "type";                                       //优惠券类型  ALIPAY_FIX_VOUCHER - 全场代金券ALIPAY_DISCOUNT_VOUCHER - 折扣券 ALIPAY_ITEM_VOUCHER - 单品优惠券  ALIPAY_CASH_VOUCHER - 现金抵价券  ALIPAY_BIZ_VOUCHER - 商家全场券
    public static final String VOUCHER_AMOUNT = "amount";                                   //优惠券面额 它应该会等于商家出资加上其他出资方出资
    public static final String VOUCHER_MERCHANT_CONTRIBUTE = "merchantContribute";         //商家出资（特指发起交易的商家出资金额）
    public static final String VOUCHER_OTHER_CONTRIBUTE = "otherContribute";               //其他出资方出资金额，可能是支付宝，可能是品牌商，或者其他方，也可能是他们的一起出资
    public static final String MEMO = "memo";                                               //优惠券备注信息 如：学生专用优惠
    public static final String TEMPLATE_ID = "template_id";                                 //券模板id
    public static final String OTHER_CONTRIBUTE_DETAIL = "other_contribute_detail";         //优惠券的其他出资方明细
    public static final String PURCHASE_BUYER_CONTRIBUTE = "purchase_buyer_contribute";     //如果使用的这张券是用户购买的，则该字段代表用户在购买这张券时用户实际付款的金额
    public static final String PURCHASE_MERCHANT_CONTRIBUTE ="purchase_merchant_contribute";//如果使用的这张券是用户购买的，则该字段代表用户在购买这张券时商户优惠的金额
    public static final String PURCHASE_ANT_CONTRIBUTE = "purchase_ant_contribute";         //如果使用的这张券是用户购买的，则该字段代表用户在购买这张券时平台优惠的金额
    //other_contribute_detail字段说明
    public static final String CONTRIBUTE_TYPE = "contribute_type";                         //出资方类型 如品牌商出资、支付宝平台出资等
    public static final String CONTRIBUTE_AMOUNT = "contribute_amount";                     //出资方金额

    //wxPromotionDetail微信-优惠功能
    public static final String PROMOTION_ID = "promotion_id";                           //券id 券或者立减优惠id
    public static final String PROMOTION_NAME = "name";                                 //优惠名称
    public static final String PROMOTION_SCOPE = "scope";                               //优惠范围 GLOBAL- 全场代金券 SINGLE- 单品优惠
    public static final String PROMOTION_TYPE = "type";                                 //优惠类型 COUPON- 代金券，需要走结算资金的充值型代金券,（境外商户券币种与支付币种一致） DISCOUNT- 优惠券，不走结算资金的免充值型优惠券，（境外商户券币种与标价币种一致
    public static final String PROMOTION_AMOUNT = "amount";                             //优惠券面额  用户享受优惠的金额（优惠券面额=微信出资金额+商家出资金额+其他出资方金额）
    public static final String PROMOTION_ACTIVITY_ID = "activity_id";                   //活动ID  在微信商户后台配置的批次ID
    public static final String PROMOTION_WXPAY_CONTRIBUTE = "wxpay_contribute";         //微信出资  特指由微信支付商户平台创建的优惠，出资金额等于本项优惠总金额，单位为元
    public static final String PROMOTION_MERCHANT_CONTRIBUTE = "merchant_contribute";   //商户出资  特指商户自己创建的优惠，出资金额等于本项优惠总金额，单位为元
    public static final String PROMOTION_OTHER_CONTRIBUTE = "other_contribute";         //其他出资  其他出资方出资金额，单位为元
    public static final String PROMOTION_GOODS_DETAIL = "goods_detail";                 //单品列表  单品信息，使用Json格式
    //goods_detail
    public static final String DETAIL_GOODS_ID = "goods_id";                            //商品编码 由半角的大小写字母、数字、中划线、下划线中的一种或几种组成
    public static final String DETAIL_GOODS_REMARK = "goods_remark";                    //商品备注 goodsRemark为备注字段，按照配置原样返回，字段内容在微信后台配置券时进行设置。
    public static final String DETAIL_DISCOUNT_AMOUNT = "discount_amount";              //商品优惠金额  单品的总优惠金额，单位为：元
    public static final String DETAIL_QUANTITY = "quantity";                            //商品数量  用户购买的数量，正整数。
    public static final String DETAIL_PRICE = "price";                                  //商品价格  单位为：元。如果商户有优惠，需传输商户优惠后的单价(例如：用户对一笔100元的订单使用了商场发的纸质优惠券100-50，则活动商品的单价应为原单价-50)

    //ylCouponInfo银联-优惠信息
    public static final String COUPON_ID = "id";                //项目编号  票券编号、活动编号等，格式自定义
    public static final String COUPON_desc = "desc";            //项目简称  优惠活动简称，可用于展示、打单等
    public static final String COUPON_ADDN_INFO = "addnInfo";   //附加信息  打印信息-内容自定义
    public static final String COUPON_TYPE = "type";            //项目类型  DD01-随机立减 CP01-抵金券1：无需领取，交易时直接适配并承兑的优惠券 CP02-抵金券2：事前领取，交易时上送银联并承兑的优惠券
    public static final String COUPON_OFFST_AMT = "offstAmt";   //抵消交易金额  不能全为0，单位为元，两位小数
    public static final String COUPON_SPNSR_ID = "spnsrId";     //出资方  银联作为出资方：固定填写00010000  付款方作为出资：填写8位付款方机构代码  商户作为出资方：填写15位商户代码。
}
