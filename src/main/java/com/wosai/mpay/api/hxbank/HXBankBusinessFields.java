package com.wosai.mpay.api.hxbank;

/**
 * <AUTHOR>
 * @Description HXBankBusinessFields
 * @Date 2021/8/20 5:40 PM
 */
public class HXBankBusinessFields {

    public static final String MAC = "mac"; //MAC地址  交易设备为PC电脑等网络设备时必输格式:xx-xx-xx-xx-xx-xx
    public static final String OCCUR_ADD = "occurAdd"; //网络地址(终端IP)  交易设备为PC电脑等网络设备时必输;Ipv4格式: xx.xx.xx.xx, Ipv6格式: xx:xx:xx:xx:xx:xx:xx:xx
    public static final String PHONE_NUB = "phoneNub"; //手机号  交易设备为手机时必输,填写接入网络的11位手机号码
    public static final String ID = "ID"; //交易设备ID  交易设备为手机时必输,建议安卓设备采集IMEI,IOS设备采集IDFV
    public static final String IMSI = "IMSI"; //交易设备IMSI  国际移动客户标识码,总长度不超过15位,存储在SIM卡中,例如:***************
    public static final String ICCD = "ICCD"; //交易设备ICCID  IC卡的唯一识别码,由20位数字组成,固化在手机SIM卡中,例如:89014104243308224563
    public static final String GPS = "GPS"; //交易设备GPS  用于标识交易设备的地理位置信息,格式为“经度，纬度”。

    public static final String MERCHANT_ID = "merchantId"; //服务商商户编号  提供给合作方的商户唯一标识
    public static final String MERCHANT_NO = "merchantNo"; //商户号  提供给合作方的商户唯一标识
    public static final String ORDER_NO = "orderNo"; //商户流水号  合作方自定义流水号，唯一标识一笔交易
    public static final String AUTH_CODE = "authCode"; //授权码  微信付款码：18位纯数字，以10、11、12、13、14、15开头。支付宝付款码：25-30开头的长度为16-24位的数字。银联：62开头的19位数字。

    public static final String AMOUNT = "amount"; //订单金额  单位为元，两位小数 （退款时，金额不能大于订单金额）
    public static final String SUBJECT = "subject"; //订单标题  即购买的商品
    public static final String DESC = "desc"; //订单描述
    public static final String NOTIFY_URL = "notifyUrl"; //通知地址  平台异步返回支付结果给商户的地址,最大长度不能超过200个字符
    public static final String TERM_ID = "termId"; //终端编号  当发起银联云闪付交易时，该字段为必传

    public static final String SUB_APP_ID = "subAppid"; //合作方微信公众账号ID  必传。用于识别上送商户的公众号标识
    public static final String SUB_OPEN_ID = "subOpenId"; //微信用户子标识  必传。该字段为消费者与合作方微信公众账号的唯一对应关
    public static final String BUYER_LOGON_ID = "buyerLogonId"; //买家支付宝账号  买家支付宝账号，和buyerId不能同时为空
    public static final String BUYER_ID = "buyerId"; //买家的支付宝唯一用户号  买家的支付宝唯一用户号（2088开头的16位纯数字）,和buyerLogonId不能同时为空


    public static final String YL_GOODS_INFO = "ylGoodsInfo"; //银联-商品信息  银联被扫交易时,商户可上送
    /*ylGoodsInfo*/
    public static final String GOODS_TAG = "goodsTag"; //微信-订单优惠标记  订单优惠标记，代金券或立减优惠功
    public static final String WX_GOODS_DETAIL = "wxGoodsDetail"; //微信-单品列表  微信单品优惠功能字段，当发起微信被扫时,商户可上送

    public static final String ALI_DISCOUNTABLE_AMOUNT = "aliDiscountableAmount"; //支付宝-可打折金额  支付宝-参与优惠计算的金额。当发起支付宝被扫时,商户可上送。
    public static final String LIMIT_PAY = "limitPay"; //借贷标识  no_credit--指定不能使用信用卡支付

    public static final String EXPIRE_TIME = "expireTime"; //订单有效时间  指定订单的支付有效时间（分钟数）， 超过有效时间用户将无法支付。若不指定该参数则系统默认设置有效时间。参数允许设置范围：5-1440区间的整数值最短失效时间间隔必须大于5分钟，默认订单有效时间5分钟(微信支付宝通道可用该字段)

    public static final String REFUND_ORDER_NO = "refundOrderNo"; //商户退款流水号  合作方自定义流水号，唯一标识一笔交易
    public static final String ORIG_ORDER_NO = "origOrderNo"; //原交易流水号  需要退款的原交易流水号
    public static final String ORG_PCS_DATE = "orgPcsDate"; //原交易日期  yyyyMMdd

    public static final String TRADE_TYPE = "tradeType"; //交易类型  1-支付，2-退款/撤销。为空时默认查询支付订单

    public static final String TERMINAL_ID = "terminalId"; //终端设备号
    public static final String TERMINAL_TYPE = "terminalType"; //设备类型
    public static final String SERIAL_NUM = "serialNum"; //终端序列号

    public static final String ENCRYPT_RAND_NUM = "encryptRandNum"; //加密随机因子

    public static final String SECRET_TEXT = "secretText"; //终端硬件序列号密文数据


    public static final String CUSTOMER_IP = "customerIp"; //持卡人ip
    public static final String QR_CODE_TYPE = "qrCodeType"; //二维码类型 , 0-动态码  1-静态码
    public static final String USER_AUTH_CODE = "userAuthCode"; //授权码  付款方返回的临时授权码，一次有效
    public static final String APP_UP_IDENTIFIER = "appUpIdentifier"; //银联支付标识  收款方识别HTTP请求User Agent中包含银联支付标识，格式为“UnionPay/<版本号> <App标识>”，注意APP标识仅支持字母和数字。 示例：UnionPay/1.0 ICBCeLife

    public static final String SVC_SYS_CODE = "svcSysCode"; //svcSysCode	业务类型编码（数字货币交易必输）
    public static final String BUS_CTGTY = "busCtgty"; //busCtgty	业务种类编码（数字货币交易必输）


    public static final String QRCODE = "qrCode"; // 二维码


}
