package com.wosai.mpay.api.cibbank;

/**
 * Created by jian<PERSON> on 16/6/16.
 */
public class ProtocolFields {
    public static final String SERVICE = "service";//接口类型:unified.trade.micropay
    public static final String VERSION = "version";//版本号,version 默认值是 1.0
    public static final String CHARSET = "charset";//字符集 可选值 UTF-8 ,默认为 UTF-8
    public static final String SIGN = "sign";//签名 MD5 签名结果，详见“第 4 章 MD5 签名规则”
    public static final String SIGN_TYPE  = "sign_type";//签名类型,取值:MD5 默认:MD5
    public static final String SIGN_AGENT_NO = "sign_agentno";//渠道编号
    public static final String GROUP_NO = "groupno";//大商户编号
    public static final String NONCE_STR = "nonce_str";//随机字符串，不长于 32 位
    public static final String MCH_ID = "mch_id";//商户号，由威富通分配


}
