package com.wosai.mpay.api.cibbank;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by jianfree on 16/6/16.
 */
public class CIBBankConstants {

    /** UTF-8字符集 **/
    public static final String CHARSET_UTF8     = "UTF-8";

    /** 默认版本号 **/
    public static final String VERSION = "1.0";


    public static String  DATE_TIME_FORMAT = "yyyyMMddHHmmss";

    /** sign type **/
    public static String SIGN_TYPE_MD5 = "MD5";
    public static String SIGN_TYPE_SHA1RSA = "RSA_1_1";
    public static String SIGN_TYPE_SHA256RSA = "RSA_1_256";

    /** FEE_TYPE 货币类型**/
    public static final String FEE_TYPE_CNY = "CNY"; //人民币


    public static final String STATUS_SUCCESS = "0";
    public static final String RESULT_CODE_SUCCESS = "0";
    public static final String PAY_RESULT_SUCCESS = "0";

    public static final String IS_RAW_YES = "1";
    public static final String IS_RAW_NO = "0";

    public static final String NEED_QUERY_YES = "Y";
    public static final String NEED_QUERY_NO = "N";

    /** 交易状态 **/
    public static final String TRADE_STATE_SUCCESS = "SUCCESS";//支付成功
    public static final String TRADE_STATE_REFUND = "REFUND";//转入退款
    public static final String TRADE_STATE_NOTPAY = "NOTPAY";//未支付
    public static final String TRADE_STATE_CLOSED = "CLOSED";//已关闭
    public static final String TRADE_STATE_REVOKED = "REVOKED";//已撤销（刷卡支付）
    public static final String TRADE_STATE_USERPAYING = "USERPAYING";//用户支付中
    public static final String TRADE_STATE_PAYERROR = "PAYERROR";//支付失败


    /** service **/
    /** 小额支付(b2c) **/
    public static final String SERVICE_UNIFIED_TRADE_MICROPAY = "unified.trade.micropay";
    /** 小额支付(b2c) 扫码支付(c2b) 冲正关单 **/
    public static final String SERVICE_UNIFIED_MICROPAY_REVERSE = "unified.micropay.reverse";
    /** 查询订单 **/
    public static final String SERVICE_UNIFIED_TRADE_QUERY = "unified.trade.query";
    /** 退款 **/
    public static final String SERVICE_UNIFIED_TRADE_REFUND = "unified.trade.refund";
    /** 退款查询 **/
    public static final String SERVICE_UNIFIED_TRADE_REFUND_QUERY = "unified.trade.refundquery";

    /** 扫码支付(c2b) 微信 **/
    public static final String SERVICE_PAY_WEIXIN_NATIVE = "pay.weixin.native";
    /** wap支付 微信公众号 **/
    public static final String SERVICE_PAY_WEIXIN_JSPAY = "pay.weixin.jspay";
    /** 扫码支付(c2b) 支付宝 **/
    public static final String SERVICE_PAY_ALIPAY_NATIVE = "pay.alipay.nativev3";
    /** wap 支付 支付宝 **/
    public static final String SERVICE_PAY_ALIPAY_JSPAY = "pay.alipay.jspay";
    /** 扫码支付(c2b) qq钱包 **/
    public static final String SERVICE_PAY_QQ_NATIVE = "pay.tenpay.proxy.native";
    /** wap 支付 qq钱包 **/
    public static final String SERVICE_PAY_QQ_JSPAY = "pay.tenpay.jspay";
    /** 扫码支付(c2b) 京东钱包 **/
    public static final String SERVICE_PAY_JD_NATIVE = "pay.jdpay.native";
    /** wap 支付 京东钱包 **/
    public static final String SERVICE_PAY_JD_JSPAY = "pay.jdpay.jspay";
    /** wap 支付 翼支付 **/
    public static final String SERVICE_PAY_BESTPAY_JSPAY = "pay.bestpay.jspay";



    /** 对账单 下载**/
    public static final String SERVICE_PAY_BILL_AGENT = "pay.bill.agent";
    public static final String BILL_TYPE_ALL = "ALL";
    public static final String BILL_TYPE_SUCCESS = "SUCCESS";
    public static final String BILL_TYPE_REFUND = "REFUND";

    public  static final String ERR_CODE_SWIFT_PASS_ORDER_NOT_EXISTS = "Order not exists";
    public  static final String ERR_CODE_SWIFT_PASS_INTERNAL_ERROR = "Internal error";
    public  static final String ERR_CODE_SWIFT_PASS_SYSTEM_ERROR = "System error";
    public  static final String ERR_CODE_SWIFT_PASS_AUTH_CODE_INVALID = "Auth code invalid";



    public static final String ERR_CODE_WEIXIN_SYSTEM_ERROR = "SYSTEMERROR"; //接口返回错误
    public static final String ERR_CODE_WEIXIN_PARAM_ERROR = "PARAM_ERROR"; //参数错误
    public static final String ERR_CODE_WEIXIN_ORDER_PAID = "ORDERPAID"; //  订单已支付
    public static final String ERR_CODE_WEIXIN_NOAUTH = "NOAUTH"; // 商户无权限
    public static final String ERR_CODE_WEIXIN_AUTH_CODE_EXPIRE = "AUTHCODEEXPIRE"; // 二维码已过期，请用户在微信上刷新后再试
    public static final String ERR_CODE_WEIXIN_NOT_ENOUGH = "NOTENOUGH"; //  余额不足
    public static final String ERR_CODE_WEIXIN_NOT_SUPORTCARD = "NOTSUPORTCARD"; //  不支持卡类型
    public static final String ERR_CODE_WEIXIN_ORDER_CLOSED = "ORDERCLOSED"; //订单已关闭
    public static final String ERR_CODE_WEIXIN_ORDER_REVERSED = "ORDERREVERSED"; //  订单已撤销
    public static final String ERR_CODE_WEIXIN_BANK_ERROR = "BANKERROR"; //  银行系统异常
    public static final String ERR_CODE_WEIXIN_USER_PAYING = "USERPAYING"; // 用户支付中，需要输入密码
    public static final String ERR_CODE_WEIXIN_AUTH_CODE_ERROR = "AUTH_CODE_ERROR"; //授权码参数错误
    public static final String ERR_CODE_WEIXIN_AUTH_CODE_INVALID = "AUTH_CODE_INVALID"; //  授权码检验错误
    public static final String ERR_CODE_WEIXIN_XML_FORMAT_ERROR = "XML_FORMAT_ERROR"; //   XML格式错误
    public static final String ERR_CODE_WEIXIN_REQUIRE_POST_METHOD = "REQUIRE_POST_METHOD"; //请使用post方法
    public static final String ERR_CODE_WEIXIN_SIGN_ERROR = "SIGNERROR"; //  签名错误
    public static final String ERR_CODE_WEIXIN_LACK_PARAMS = "LACK_PARAMS"; //缺少参数
    public static final String ERR_CODE_WEIXIN_POST_DATA_EMPTY = "POST_DATA_EMPTY"; //post数据为空
    public static final String ERR_CODE_WEIXIN_NOT_UTF8 = "NOT_UTF8"; //   编码格式错误
    public static final String ERR_CODE_WEIXIN_BUYER_MISMATCH = "BUYER_MISMATCH"; // 支付帐号错误
    public static final String ERR_CODE_WEIXIN_APPID_NOT_EXIST = "APPID_NOT_EXIST"; //APPID不存在
    public static final String ERR_CODE_WEIXIN_MCHID_NOT_EXIST = "MCHID_NOT_EXIST"; //MCHID不存在
    public static final String ERR_CODE_WEIXIN_OUT_TRADE_NO_USED = "OUT_TRADE_NO_USED"; //  商户订单号重复
    public static final String ERR_CODE_WEIXIN_APPID_MCHID_NOT_MATCH = "APPID_MCHID_NOT_MATCH"; //  appid和mch_id不匹配
    public static final String ERR_CODE_WEIXIN_ORDER_NOT_EXIST = "ORDERNOTEXIST"; //  此交易订单号不存在
    public static final String ERR_CODE_WEIXIN_TRANSACTION_ID_INVALID = "TRANSACTION_ID_INVALID"; //  订单号非法
    public static final String ERR_CODE_WEIXIN_REFUND_FEE_INVALID = "REFUND_FEE_INVALID"; //  退款金额大于支付金额
    public static final String ERR_CODE_WEIXIN_ORDERREFUND = "ORDERREFUND"; //  订单撤销失败（已经退款的订单不允许撤销）
    public static final String ERR_CODE_WEIXIN_TRADE_STATE_ERROR = "TRADE_STATE_ERROR"; //  订单退款失败（订单已经退完款后，不能再退款）
    public static final String ERR_CODE_WEIXIN_REFUNDNOTEXIST = "REFUNDNOTEXIST"; //  订单退款查询（退款不存在）
    public static final String ERR_CODE_WEIXIN_ORDERSTATUSERROR = "ORDERSTATUSERROR"; //  订单状态错误


    public static final String ERR_CODE_QQ_TRADE_STATE_USERPAYING = "66227005";//提示用户输入支付密码，如果用户手机无网络信号，请换用其他收款方式。
    public static final String ERR_CODE_QQ_ILLEGAL_SIGN = "66227001";//商户签名校验失败	确认签名规则是否符合接口文档中的描述。
    public static final String ERR_CODE_QQ_NO_PRIVILEGE = "66227002";//商户没有手Q支付权限	请联系手Q相关同事，确认商户号的权限。
    public static final String ERR_CODE_QQ_CHARSET_CONVERION_ERROR = "66227004";//字符集转换失败（请使用在GBK字符集之内的utf8字符）	请确认请求的实际编码与请求中的charset参数是否一致。
    public static final String ERR_CODE_QQ_INVALID_AUTH_CODE = "66227006";//用户授权码无效	提示用户关闭付款码，然后重新开通。
    public static final String ERR_CODE_QQ_ILLEGAL_CERTIFICATE = "66227007";//用户证书非法	提示用户关闭付款码，然后重新开通。
    public static final String ERR_CODE_QQ_NOT_ENOUGH = "66227008";//余额不足	用户的财付通余额资金不足，请更换为银行卡支付。
    public static final String ERR_CODE_QQ_TRADE_CLOSED_CANCEL_FAIL = "66227009";//撤单失败，订单已撤单	订单已经成功关闭，请勿重复提交 关闭订单 请求。
    public static final String ERR_CODE_QQ_CANCEL_FAIL = "66227010";//撤单失败，订单已支付成功	订单已经支付成功，此时无法完成关闭订单操作。可以调用 撤销订单（交易冲正）接口，或者 按照支付成功，给用户发货。
    public static final String ERR_CODE_QQ_TRADE_REFUNDED_CANCEL_FAIL = "66227011";//撤单失败，订单已退款	订单已经在退款流程中，请勿再次提交 关闭订单 请求。可以请用户重新支付。
    public static final String ERR_CODE_QQ_TRADE_NOT_EXIST = "********";//订单不存在	该错误码通常是在调用查询订单接口返回。收到该错误码时，表示订单还没有完成支付，可以再次查询，直到得到订单的最终状态，或者是调用撤销订单（交易冲正）接口接口。
    public static final String ERR_CODE_QQ_TRADE_CLOSED = "********";//订单已经关闭。	订单已经被关闭，不能尝试支付。
    public static final String ERR_CODE_QQ_NOT_ENOUGH_MONEY_IN_BANK_CARD = "********";//银行卡可用余额不足	可以提醒用户更换 其它银行卡或者财付通余额进行支付。
    public static final String ERR_CODE_QQ_TRADE_WAITED_TO_CONFIRM = "*********";//您的操作已提交，请确认是否已生效
    public static final String ERR_CODE_QQ_BANK_CARD_LOCKED = "********";//银行卡被锁定
    public static final String ERR_CODE_QQ_ILLEGAL_BANK_CARD = "********";//银行卡状态异常
    public static final String ERR_CODE_QQ_UNMATCHED_PHONE_NUMBER = "********";//您的银行卡预留手机不符，请核对后再试。若您的银行卡预留手机号已变更，请在当前应用或网站重新绑定银行卡。
    public static final String ERR_CODE_QQ_BANK_CARD_EXPIRE = "********";//银行卡快捷签约状态不存在或者已过期
    public static final String ERR_CODE_QQ_TRADE_CLOSED_CANNOT_RETRY = "1000********";//订单已经被关闭，请勿尝试支付	创建新的订单，引导用户支付。
    public static final String ERR_CODE_QQ_TRADE_CLOSE_CANNOT_RETRY = "********";//订单已经被关闭，请勿尝试支付	创建新的订单，引导用户支付。
    public static final String ERR_CODE_QQ_ILLEGAL_RELATIONSHIP = "********";//商户号之间不存在有效的受理关系。	请向手Q支付申请受理关系。

    public static final String ERR_CODE_ALIPAY2_SYSTEM_ERROR = "ACQ.SYSTEM_ERROR";//接口返回错误
    public static final String ERR_CODE_ALIPAY2_INVALID_PARAMETER = "ACQ.INVALID_PARAMETER";//参数无效
    public static final String ERR_CODE_ALIPAY2_PAY_ACCESS_FORBIDDEN = "ACQ.ACCESS_FORBIDDEN";//无权限使用接口
    public static final String ERR_CODE_ALIPAY2_PAY_EXIST_FORBIDDEN_WORD = "ACQ.EXIST_FORBIDDEN_WORD";//订单信息中包含违禁词
    public static final String ERR_CODE_ALIPAY2_PAY_PARTNER_ERROR = "ACQ.PARTNER_ERROR";//应用APP_ID填写错误
    public static final String ERR_CODE_ALIPAY2_PAY_TOTAL_FEE_EXCEED = "ACQ.TOTAL_FEE_EXCEED";//订单总金额超过限额
    public static final String ERR_CODE_ALIPAY2_PAY_PAYMENT_AUTH_CODE_INVALID = "ACQ.PAYMENT_AUTH_CODE_INVALID";//支付授权码无效
    public static final String ERR_CODE_ALIPAY2_PAY_CONTEXT_INCONSISTENT = "ACQ.CONTEXT_INCONSISTENT";//交易信息被篡改
    public static final String ERR_CODE_ALIPAY2_PAY_TRADE_HAS_SUCCESS = "ACQ.TRADE_HAS_SUCCESS";//交易已被支付
    public static final String ERR_CODE_ALIPAY2_PAY_TRADE_HAS_CLOSE = "ACQ.TRADE_HAS_CLOSE";//交易已经关闭
    public static final String ERR_CODE_ALIPAY2_PAY_BUYER_BALANCE_NOT_ENOUGH = "ACQ.BUYER_BALANCE_NOT_ENOUGH";//买家余额不足
    public static final String ERR_CODE_ALIPAY2_PAY_BUYER_BANKCARD_BALANCE_NOT_ENOUGH = "ACQ.BUYER_BANKCARD_BALANCE_NOT_ENOUGH";//用户银行卡余额不足
    public static final String ERR_CODE_ALIPAY2_PAY_ERROR_BALANCE_PAYMENT_DISABLE = "ACQ.ERROR_BALANCE_PAYMENT_DISABLE";//余额支付功能关闭
    public static final String ERR_CODE_ALIPAY2_PAY_BUYER_SELLER_EQUAL = "ACQ.BUYER_SELLER_EQUAL";//买卖家不能相同
    public static final String ERR_CODE_ALIPAY2_PAY_TRADE_BUYER_NOT_MATCH = "ACQ.TRADE_BUYER_NOT_MATCH";//交易买家不匹配
    public static final String ERR_CODE_ALIPAY2_PAY_BUYER_ENABLE_STATUS_FORBID = "ACQ.BUYER_ENABLE_STATUS_FORBID";//买家状态非法
    public static final String ERR_CODE_ALIPAY2_PAY_PULL_MOBILE_CASHIER_FAIL = "ACQ.PULL_MOBILE_CASHIER_FAIL";//唤起移动收银台失败
    public static final String ERR_CODE_ALIPAY2_PAY_MOBILE_PAYMENT_SWITCH_OFF = "ACQ.MOBILE_PAYMENT_SWITCH_OFF";//用户的无线支付开关关闭
    public static final String ERR_CODE_ALIPAY2_PAY_PAYMENT_FAIL = "ACQ.PAYMENT_FAIL";//支付失败
    public static final String ERR_CODE_ALIPAY2_PAY_BUYER_PAYMENT_AMOUNT_DAY_LIMIT_ERROR = "ACQ.BUYER_PAYMENT_AMOUNT_DAY_LIMIT_ERROR";//买家付款日限额超限
    public static final String ERR_CODE_ALIPAY2_PAY_BEYOND_PAY_RESTRICTION = "ACQ.BEYOND_PAY_RESTRICTION";//商户收款额度超限
    public static final String ERR_CODE_ALIPAY2_PAY_BEYOND_PER_RECEIPT_RESTRICTION = "ACQ.BEYOND_PER_RECEIPT_RESTRICTION";//商户收款金额超过月限额
    public static final String ERR_CODE_ALIPAY2_PAY_BUYER_PAYMENT_AMOUNT_MONTH_LIMIT_ERROR = "ACQ.BUYER_PAYMENT_AMOUNT_MONTH_LIMIT_ERROR";//买家付款月额度超限
    public static final String ERR_CODE_ALIPAY2_PAY_SELLER_BEEN_BLOCKED = "ACQ.SELLER_BEEN_BLOCKED";//商家账号被冻结
    public static final String ERR_CODE_ALIPAY2_PAY_ERROR_BUYER_CERTIFY_LEVEL_LIMIT = "ACQ.ERROR_BUYER_CERTIFY_LEVEL_LIMIT";//买家未通过人行认证
    public static final String ERR_CODE_ALIPAY2_PAY_PAYMENT_REQUEST_HAS_RISK = "ACQ.PAYMENT_REQUEST_HAS_RISK";//支付有风险
    public static final String ERR_CODE_ALIPAY2_PAY_NO_PAYMENT_INSTRUMENTS_AVAILABLE = "ACQ.NO_PAYMENT_INSTRUMENTS_AVAILABLE";//没用可用的支付工具
    public static final String ERR_CODE_ALIPAY2_PAY_USER_FACE_PAYMENT_SWITCH_OFF="USER_FACE_PAYMENT_SWITCH_OFF";	//用户当面付付款开关关闭
    public static final String ERR_CODE_ALIPAY2_PAY_SOUNDWAVE_PARSER_FAIL = "SOUNDWAVE_PARSER_FAIL"; //顾客付款码无效
    public static final String ERR_CODE_ALIPAY2_QUERY_TRADE_NOT_EXIST="ACQ.TRADE_NOT_EXIST";	//交易不存在
    public static final String ERR_CODE_ALIPAY2_CANCEL_SELLER_BALANCE_NOT_ENOUGH = "ACQ.SELLER_BALANCE_NOT_ENOUGH";//卖家余额不足
    public static final String ERR_CODE_ALIPAY2_CANCEL_REASON_TRADE_BEEN_FREEZEN = "ACQ.REASON_TRADE_BEEN_FREEZEN";//交易被冻结
    public static final String ERR_CODE_ALIPAY2_REFUND_SELLER_BALANCE_NOT_ENOUGH = "ACQ.SELLER_BALANCE_NOT_ENOUGH";//卖家余额不足
    public static final String ERR_CODE_ALIPAY2_REFUND_REFUND_AMT_NOT_EQUAL_TOTAL = "ACQ.REFUND_AMT_NOT_EQUAL_TOTAL";//退款金额超限
    public static final String ERR_CODE_ALIPAY2_REFUND_REASON_TRADE_BEEN_FREEZEN = "ACQ.REASON_TRADE_BEEN_FREEZEN";//请求退款的交易被冻结
    public static final String ERR_CODE_ALIPAY2_REFUND_TRADE_NOT_EXIST = "ACQ.TRADE_NOT_EXIST";//交易不存在
    public static final String ERR_CODE_ALIPAY2_REFUND_TRADE_HAS_FINISHED = "ACQ.TRADE_HAS_FINISHED";//交易已完结
    public static final String ERR_CODE_ALIPAY2_REFUND_TRADE_STATUS_ERROR = "ACQ.TRADE_STATUS_ERROR";//交易状态非法
    public static final String ERR_CODE_ALIPAY2_REFUND_DISCORDANT_REPEAT_REQUEST = "ACQ.DISCORDANT_REPEAT_REQUEST";//不一致的请求
    public static final String ERR_CODE_ALIPAY2_REFUND_REASON_TRADE_REFUND_FEE_ERR = "ACQ.REASON_TRADE_REFUND_FEE_ERR";//退款金额无效


    public static final String ERR_CODE_JD_SUCCESS = "000000"; // 成功
    public static final String ERR_CODE_JD_INVALID_PARAMETER = "000101";//参数异常
    public static final String ERR_CODE_JD_INVALID_AMOUNT_FORMAT = "000102";//金额格式不对
    public static final String ERR_CODE_JD_INVALID_NOTIFY_URL = "000103";//通知回掉地址不能为空
    public static final String ERR_CODE_JD_MERCHANT_NOT_EXISTS = "000104";//商户不存在
    public static final String ERR_CODE_JD_ORDER_NOT_EXISTS = "000105";//订单不存在
    public static final String ERR_CODE_JD_ORDER_REFUNDED = "000106";//订单已经退款成功
    public static final String ERR_CODE_JD_DUPLICATE_REFUND_REQUEST = "000107";//退单请求重复
    public static final String ERR_CODE_JD_REFUND_ERROR_WITH_ABNORMAL_ORDER_STATUS = "000108";//订单状态不正确,不能退单
    public static final String ERR_CODE_JD_ILLEGAL_SIGN = "000109";//签名验证不正确
    public static final String ERR_CODE_JD_AMOUNT_ERROR = "000110";//订单金额不对
    public static final String ERR_CODE_JD_CREATE_ORDER_FAIL = "000114";//订单创建失败
    public static final String ERR_CODE_JD_INVALID_AUTH_CODE = "000119";//无效二维码
    public static final String ERR_CODE_JD_PAY_FAIL = "000120";//支付失败
    public static final String ERR_CODE_JD_INVALID_EXPIRE_TIME_FORMAT = "000125";//过期时间格式不正确
    public static final String ERR_CODE_JD_INVALID_ORDER_SN_LENGTH = "000128";//订单号长度不能超过32个字符
    public static final String ERR_CODE_JD_INVALID_URL_LENGTH = "000129";//url地址不能超过1024个字符
    public static final String ERR_CODE_JD_INVALID_TRADE_NAME_LENGTH = "000130";//交易名称不能超过64个字符
    public static final String ERR_CODE_JD_INVALID_TRADE_DESC_LENGTH = "000131";//交易描述不能超过256个字符
    public static final String ERR_CODE_JD_REFUNDABLE_AMOUNT_NOT_ENOUGH = "000132";//该订单可退金额不足
    public static final String ERR_CODE_JD_REFUND_NO_IS_EMPTY = "000133";//部分退单退单号不能为空
    public static final String ERR_CODE_JD_INVALID_REFUND_NO_LENGTH = "000135";//退单号长度不能超过32个字符
    public static final String ERR_CODE_JD_REFUND_AMOUNT_IS_EMPTY = "000136";//退款金额不能为空
    public static final String ERR_CODE_JD_INVALID_NOTE_LENGTH = "000137";//备注长度不能超过100个字符
    public static final String ERR_CODE_JD_ORDER_CANCELED = "000138";//订单已撤销成功
    public static final String ERR_CODE_JD_CANCEL_FAIL_WITH_ABNORMAL_ORDER_STATUS = "000139";//订单状态不正确，不能撤单
    public static final String ERR_CODE_JD_CANCEL_NO_IS_EMPTY = "000140";//撤单号不能为空
    public static final String ERR_CODE_JD_INVALID_CANCEL_NO_LENGTH = "000141";//撤单号长度不能超过32个字符
    public static final String ERR_CODE_JD_DUPLICATE_CANCEL_NO = "000142";//撤单号重复
    public static final String ERR_CODE_JD_INVALID_MERCHANT_NO_LENGTH = "000143";//门店号不能超过20个字符
    public static final String ERR_CODE_JD_INVALID_TERMINAL_NO_LENGTH = "000144";//机具号不能超过20个字符
    public static final String ERR_CODE_JD_INVALID_EXTRA_INFO_LENGTH = "000145";//额外信息不能超过512个字符
    public static final String ERR_CODE_JD_AMOUNT_IS_EMPTY = "000146";//金额不能为空
    public static final String ERR_CODE_JD_SYSTEM_ERROR = "000999";//服务器内部错误
    public static final String ERR_CODE_JD_ACCOUNT_FREEZED = "A00001";//账户冻结
    public static final String ERR_CODE_JD_ACCOUNT_FREEZED_FORBIDDEN_INCOME = "A00002";//账户冻结止入
    public static final String ERR_CODE_JD_ACCOUNT_FREEZED_FORBIDDEN_OUTLAY = "A00003";//账户冻结止出
    public static final String ERR_CODE_JD_ACCOUNT_LOGOUTED = "A00004";//账户注销
    public static final String ERR_CODE_JD_ACCOUNT_AMOUNT_NOT_ENOUGH = "A00005";//账户可用金额不足
    public static final String ERR_CODE_JD_BANKCARD_PAY_AMOUNT_LIMIT = "B00001";//超过银行卡支付限额
    public static final String ERR_CODE_JD_BANKCARD_PAY_COUNT_LIMIT = "B00002";//超过银行卡可支付笔数
    public static final String ERR_CODE_JD_BANKCARD_DAILY_PAY_COUNT_LIMIT = "B00003";//超过银行卡当日可支付笔数
    public static final String ERR_CODE_JD_BANKCARD_DAILY_PAY_AMOUNT_LIMIT = "B00004";//超过银行卡当日订单总额
    public static final String ERR_CODE_JD_BANKCARD_INFO_CHECK_FAIL = "B00005";//银行卡信息校验失败
    public static final String ERR_CODE_JD_BANKCARD_AMOUNT_NOT_ENOUGH = "B00006";//银行卡余额不足
    public static final String ERR_CODE_JD_BANKCARD_LOGOUTED = "B00007";//银行卡已销户
    public static final String ERR_CODE_JD_BANKCARD_LOSSED = "B00008";//银行卡已挂失
    public static final String ERR_CODE_JD_BANKCARD_LOCKED = "B00009";//银行卡已锁定
    public static final String ERR_CODE_JD_BANKCARD_STATUS_ABNORMAL = "B00010";//银行卡状态异常
    public static final String ERR_CODE_JD_BANKCARD_REFUND_AMOUNT_LIMIT = "B00011";//退款金额超出可退额度
    public static final String ERR_CODE_JD_BANKCARD_QUICK_CLOSED = "B00012";//银行卡快捷业务已经关闭
    public static final String ERR_CODE_JD_BANKCARD_MONTHLY_PAY_COUNT_LIMIT = "B00013";//超过银行卡当月可支付笔数
    public static final String ERR_CODE_JD_BANKCARD_MONTHLY_PAY_AMOUNT_LIMIT = "B00014";//超过银行卡当月订单总额
    public static final String ERR_CODE_JD_BANKCARD_QUICK_NOT_OPEN = "B00015";//银行卡快捷业务未开通
    public static final String ERR_CODE_JD_ACCOUNT_PAY_LIMIT = "R00001";//账号支付受限
    public static final String ERR_CODE_JD_ACCOUNT_PAY_AMOUNT_LIMIT = "R00002";//超过账户支付限额
    public static final String ERR_CODE_JD_ACCOUNT_PAY_COUNT_LIMIT = "R00003";//超过账户支付次数
    public static final String ERR_CODE_JD_TRADE_PAID = "T00001";//该订单已支付
    public static final String ERR_CODE_JD_ORDER_NOT_EXISTS_TRADE = "T00002";//无此订单信息
    public static final String ERR_CODE_JD_ORDER_CLOSED = "T00003";//订单已关闭
    public static final String ERR_CODE_JD_ORDER_TIMEOUT = "T00004";//订单已过期
    public static final String ERR_CODE_JD_ORDER_PAY_FREQUENCY = "T00005";//支付操作频繁
    public static final String ERR_CODE_JD_ORDER_REFUNDED_TRADE = "T00006";//订单已经退款
    public static final String ERR_CODE_JD_ORDER_REFUND_IN_PROGRESS = "T00007";//订单退款处理中
    public static final String ERR_CODE_JD_ORDER_REFUND_ERROR = "T00008";//退款失败
    public static final String ERR_CODE_JD_ORDER_REFUNDABLE_AMOUNT_NOT_ENOUGH = "T00009";//退款金额超过可退额度
    public static final String ERR_CODE_JD_ORDER_REFUND_FAIL = "T00010";//原订单状态不允许退款
    public static final String ERR_CODE_JD_ORDER_USER_BINDED = "T00011";//本订单已经绑定支付人,请重新生成订单号
    public static final String ERR_CODE_JD_MERCHANT_BIZ_NOT_SUPPORT = "TAQ2000401";//商户未开通此项服务
    public static final String ERR_CODE_JD_EXPIRED_AUTH_CODE = "AKS9920306";//付款码超时
    public static final String ERR_CODE_JD_BALANCE_NOT_ENOUGH = "BAC4000023";//账户余额不足


    public static final String ERR_CODE_BESTPAY_ILLEGAL_SIGN = "-301"; // 订单 MAC 域验证失败
    public static final String ERR_CODE_BESTPAY_ORDER_VALID_ERROR = "-302"; // 校验订单出错 检查订单金额
    public static final String ERR_CODE_BESTPAY_MECHANT_NO_AUTH = "-304"; // 商户未配置此交易权限
    public static final String ERR_CODE_BESTPAY_LEDGER_ACCOUNT_NOT_EXISTS = "-3061"; // 分账商户不存在
    public static final String ERR_CODE_BESTPAY_LEDGER_AMOUNT_ERROR = "-3062"; // 分账金额有误
    public static final String ERR_CODE_BESTPAY_LEDGER_AMOUNT_ORDER_AMOUNT_NOT_MATCH = "-3063"; // 分账金额总和不等于订单金额
    public static final String ERR_CODE_BESTPAY_LEDGER_PARENT_MERCHANT_ERROR = "-3064"; // 分账商户父商户有误
    public static final String ERR_CODE_BESTPAY_LEDGER_DUAL_MERCHANT_NOT_ALLOW = "-3065"; // 分账信息不允许有两个相同商户
    public static final String ERR_CODE_BESTPAY_PASSWORD_ERROR = "-309"; //  校验商户调用密码出错
    public static final String ERR_CODE_BESTPAY_MERCHANT_NOT_ENROLL = "1001"; // 商户未注册
    public static final String ERR_CODE_BESTPAY_MERCHANT_KEY_NOT_CONFIG = "1002"; // 商户未配置密钥信息
    public static final String ERR_CODE_BESTPAY_MERCHANT_BANK_NOT_OPEN = "1003"; // 商户未开通银行
    public static final String ERR_CODE_BESTPAY_MERCHANT_BANK_NOT_EXISTS = "4008"; // 绑卡信息不存在
    public static final String ERR_CODE_BESTPAY_QUERY_BANK_ERROR = "5146"; // 查询绑卡信息出错
    public static final String ERR_CODE_BESTPAY_BANK_DECODE_FAIL = "849"; // 卡信息解密失败
    public static final String ERR_CODE_BESTPAY_BARCODE_STORE_CODE_IS_EMPTY = "BARCODE_STORE_CODE_IS_EMPTY"; // 条码门店号不能为空
    public static final String ERR_CODE_BESTPAY_BARCODE_VALIDATE_ERROR = "BARCODE_VALIDATE_ERROR"; // 条形码验证异常
    public static final String ERR_CODE_BESTPAY_BE100002 = "BE100002"; //  受理机构代码为空
    public static final String ERR_CODE_BESTPAY_BE110028 = "BE110028"; //  分账信息有误
    public static final String ERR_CODE_BESTPAY_ORDER_NOT_EXISTS = "BE110062"; //  没有找到符合条件的记录
    public static final String ERR_CODE_BESTPAY_ORDER_NOT_EXISTS_OR_OUT_OF_REFUND_TIME_LIMIT = "BE110078"; // 原交易不存在或超过退款限制时间
    public static final String ERR_CODE_BESTPAY_INVALID_PARAMS = "BE199999"; //  请求参数有误
    public static final String ERR_CODE_BESTPAY_BE300000 = "BE300000"; //  商户不存在
    public static final String ERR_CODE_BESTPAY_BE300001 = "BE300001"; //  订单 MAC 域验证失败
    public static final String ERR_CODE_BESTPAY_BE300006 = "BE300006"; //  受理机构未配置此交易权限
    public static final String ERR_CODE_BESTPAY_BE300007 = "BE300007"; //  商户未配置此交易权限
    public static final String ERR_CODE_BESTPAY_BE300012 = "BE300012"; //  分账金额总和不等于订单总金额
    public static final String ERR_CODE_BESTPAY_BE300013 = "BE300013"; //  商户 IP 验证异常
    public static final String ERR_CODE_BESTPAY_ORDER_PARTIAL_REFUND_NOT_SUPPORT = "BE300018"; //  订单不支持部分扣款
    public static final String ERR_CODE_BESTPAY_BE301001 = "BE301001"; //  订单金额出现异常，交易失败
    public static final String ERR_CODE_BESTPAY_BATCH_ORDER_NOT_SUPPORT_REFUND = "BE301007"; // 不支持批量支付订单的退款，退款失 败
    public static final String ERR_CODE_BESTPAY_ORDER_NOT_SUPPORT_REFUND = "BE301008"; // 订单的业务类型不支持退款，退款失 败
    public static final String ERR_CODE_BESTPAY_ORDER_NOT_SUCCESS = "BE301009"; // 订单状态不为成功，退款失败
    public static final String ERR_CODE_BESTPAY_ORDER_REFUND_INPROGRESS = "BE301010"; // 退款订单受理中，退款失败
    public static final String ERR_CODE_BESTPAY_ORDER_REFUNDED_OR_CANCELED = "BE301011"; // 订单已退款或冲正，退款失败
    public static final String ERR_CODE_BESTPAY_ORDER_NOT_REFUNDED_CANCEL_FAIL = "BE301012"; // 订单不为未退款，冲正失败
    public static final String ERR_CODE_BESTPAY_ORDER_CLOSED = "BE301013"; // 订单状态为作废，冲正失败
    public static final String ERR_CODE_BESTPAY_ORDER_NOT_TODAY_ORDER_CANCEL_FAIL = "BE301014"; // 原订单不为当天订单，冲正失败
    public static final String ERR_CODE_BESTPAY_SYSTEM_ERROR = "BE999999"; // 系统繁忙，请稍后再试
    public static final String MESSAGE_BESTPAY_MERCHANT_FREEZE = "商户被冻结"; // 商户被冻结
    public static final String STATUS_BESTPAY_MERCHANT_FREEZE = "400"; // 商户被冻结

    public static final int NO_CREDIT = 1;//交易禁用信用卡


    //todo 对接支付宝微信时，把下面的各自内容补上

    /** 失败 业务错误 **/
    public static final Set  FAIL_ERR_CODE_LISTS = new HashSet(){
        {addAll(
                Arrays.asList(
                        ERR_CODE_SWIFT_PASS_AUTH_CODE_INVALID,

                        ERR_CODE_WEIXIN_AUTH_CODE_EXPIRE,ERR_CODE_WEIXIN_NOT_ENOUGH,
                        ERR_CODE_WEIXIN_NOT_SUPORTCARD,ERR_CODE_WEIXIN_AUTH_CODE_ERROR,
                        ERR_CODE_WEIXIN_AUTH_CODE_INVALID,ERR_CODE_WEIXIN_BUYER_MISMATCH,
                        ERR_CODE_WEIXIN_OUT_TRADE_NO_USED,ERR_CODE_WEIXIN_APPID_MCHID_NOT_MATCH,
                        ERR_CODE_WEIXIN_ORDER_NOT_EXIST,ERR_CODE_WEIXIN_TRANSACTION_ID_INVALID,

                        ERR_CODE_ALIPAY2_PAY_TOTAL_FEE_EXCEED, ERR_CODE_ALIPAY2_PAY_PAYMENT_AUTH_CODE_INVALID,
                        ERR_CODE_ALIPAY2_PAY_BUYER_BALANCE_NOT_ENOUGH, ERR_CODE_ALIPAY2_PAY_BUYER_BANKCARD_BALANCE_NOT_ENOUGH,
                        ERR_CODE_ALIPAY2_PAY_ERROR_BALANCE_PAYMENT_DISABLE, ERR_CODE_ALIPAY2_PAY_BUYER_SELLER_EQUAL,
                        ERR_CODE_ALIPAY2_PAY_TRADE_BUYER_NOT_MATCH, ERR_CODE_ALIPAY2_PAY_BUYER_ENABLE_STATUS_FORBID,
                        ERR_CODE_ALIPAY2_PAY_PULL_MOBILE_CASHIER_FAIL, ERR_CODE_ALIPAY2_PAY_MOBILE_PAYMENT_SWITCH_OFF,
                        ERR_CODE_ALIPAY2_PAY_PAYMENT_FAIL, ERR_CODE_ALIPAY2_PAY_BUYER_PAYMENT_AMOUNT_DAY_LIMIT_ERROR,
                        ERR_CODE_ALIPAY2_PAY_BEYOND_PAY_RESTRICTION, ERR_CODE_ALIPAY2_PAY_BEYOND_PER_RECEIPT_RESTRICTION,
                        ERR_CODE_ALIPAY2_PAY_BUYER_PAYMENT_AMOUNT_MONTH_LIMIT_ERROR, ERR_CODE_ALIPAY2_PAY_SELLER_BEEN_BLOCKED,
                        ERR_CODE_ALIPAY2_PAY_ERROR_BUYER_CERTIFY_LEVEL_LIMIT, ERR_CODE_ALIPAY2_PAY_PAYMENT_REQUEST_HAS_RISK,
                        ERR_CODE_ALIPAY2_PAY_NO_PAYMENT_INSTRUMENTS_AVAILABLE, ERR_CODE_ALIPAY2_PAY_USER_FACE_PAYMENT_SWITCH_OFF,
                        ERR_CODE_ALIPAY2_QUERY_TRADE_NOT_EXIST, ERR_CODE_ALIPAY2_CANCEL_SELLER_BALANCE_NOT_ENOUGH,
                        ERR_CODE_ALIPAY2_CANCEL_REASON_TRADE_BEEN_FREEZEN, ERR_CODE_ALIPAY2_REFUND_SELLER_BALANCE_NOT_ENOUGH,
                        ERR_CODE_ALIPAY2_REFUND_REFUND_AMT_NOT_EQUAL_TOTAL, ERR_CODE_ALIPAY2_REFUND_REASON_TRADE_BEEN_FREEZEN,
                        ERR_CODE_ALIPAY2_REFUND_TRADE_NOT_EXIST, ERR_CODE_ALIPAY2_REFUND_DISCORDANT_REPEAT_REQUEST,
                        ERR_CODE_ALIPAY2_REFUND_REASON_TRADE_REFUND_FEE_ERR,ERR_CODE_ALIPAY2_PAY_SOUNDWAVE_PARSER_FAIL,

                        ERR_CODE_QQ_INVALID_AUTH_CODE, ERR_CODE_QQ_ILLEGAL_CERTIFICATE,ERR_CODE_QQ_NOT_ENOUGH,
                        ERR_CODE_QQ_TRADE_NOT_EXIST,ERR_CODE_QQ_TRADE_CLOSED,ERR_CODE_QQ_NOT_ENOUGH_MONEY_IN_BANK_CARD,
                        ERR_CODE_QQ_TRADE_WAITED_TO_CONFIRM,ERR_CODE_QQ_BANK_CARD_LOCKED,ERR_CODE_QQ_ILLEGAL_BANK_CARD,
                        ERR_CODE_QQ_UNMATCHED_PHONE_NUMBER, ERR_CODE_QQ_BANK_CARD_EXPIRE,

                        ERR_CODE_JD_MERCHANT_NOT_EXISTS,ERR_CODE_JD_DUPLICATE_REFUND_REQUEST,ERR_CODE_JD_REFUND_ERROR_WITH_ABNORMAL_ORDER_STATUS,
                        ERR_CODE_JD_CREATE_ORDER_FAIL, ERR_CODE_JD_INVALID_AUTH_CODE, ERR_CODE_JD_PAY_FAIL,
                        ERR_CODE_JD_REFUNDABLE_AMOUNT_NOT_ENOUGH, ERR_CODE_JD_INVALID_REFUND_NO_LENGTH, ERR_CODE_JD_CANCEL_FAIL_WITH_ABNORMAL_ORDER_STATUS,
                        ERR_CODE_JD_SYSTEM_ERROR, ERR_CODE_JD_ACCOUNT_FREEZED, ERR_CODE_JD_ACCOUNT_FREEZED_FORBIDDEN_INCOME,
                        ERR_CODE_JD_ACCOUNT_FREEZED_FORBIDDEN_OUTLAY, ERR_CODE_JD_ACCOUNT_LOGOUTED, ERR_CODE_JD_ACCOUNT_AMOUNT_NOT_ENOUGH,
                        ERR_CODE_JD_BANKCARD_PAY_AMOUNT_LIMIT, ERR_CODE_JD_BANKCARD_PAY_COUNT_LIMIT,
                        ERR_CODE_JD_BANKCARD_DAILY_PAY_COUNT_LIMIT, ERR_CODE_JD_BANKCARD_DAILY_PAY_AMOUNT_LIMIT,
                        ERR_CODE_JD_BANKCARD_INFO_CHECK_FAIL, ERR_CODE_JD_BANKCARD_AMOUNT_NOT_ENOUGH,
                        ERR_CODE_JD_BANKCARD_LOGOUTED, ERR_CODE_JD_BANKCARD_LOSSED, ERR_CODE_JD_BANKCARD_LOCKED,
                        ERR_CODE_JD_BANKCARD_STATUS_ABNORMAL, ERR_CODE_JD_BANKCARD_REFUND_AMOUNT_LIMIT,
                        ERR_CODE_JD_BANKCARD_QUICK_CLOSED, ERR_CODE_JD_BANKCARD_MONTHLY_PAY_COUNT_LIMIT,
                        ERR_CODE_JD_BANKCARD_MONTHLY_PAY_AMOUNT_LIMIT, ERR_CODE_JD_BANKCARD_QUICK_NOT_OPEN,
                        ERR_CODE_JD_ACCOUNT_PAY_LIMIT, ERR_CODE_JD_ACCOUNT_PAY_AMOUNT_LIMIT, ERR_CODE_JD_ACCOUNT_PAY_COUNT_LIMIT,
                        ERR_CODE_JD_ORDER_PAY_FREQUENCY, ERR_CODE_JD_ORDER_REFUND_ERROR, ERR_CODE_JD_ORDER_REFUNDABLE_AMOUNT_NOT_ENOUGH,
                        ERR_CODE_JD_ORDER_REFUND_FAIL, ERR_CODE_JD_ORDER_USER_BINDED, ERR_CODE_JD_ORDER_TIMEOUT,
                        ERR_CODE_JD_ORDER_NOT_EXISTS, ERR_CODE_JD_ORDER_REFUNDED,
                        ERR_CODE_JD_ORDER_CANCELED, ERR_CODE_JD_TRADE_PAID,
                        ERR_CODE_JD_ORDER_NOT_EXISTS_TRADE, ERR_CODE_JD_ORDER_CLOSED,
                        ERR_CODE_JD_ORDER_REFUNDED_TRADE, ERR_CODE_JD_ORDER_REFUND_IN_PROGRESS, ERR_CODE_JD_MERCHANT_BIZ_NOT_SUPPORT,
                        ERR_CODE_JD_EXPIRED_AUTH_CODE, ERR_CODE_JD_BALANCE_NOT_ENOUGH,

                        ERR_CODE_BESTPAY_ORDER_VALID_ERROR,
                        ERR_CODE_BESTPAY_MECHANT_NO_AUTH,
                        ERR_CODE_BESTPAY_LEDGER_ACCOUNT_NOT_EXISTS,
                        ERR_CODE_BESTPAY_LEDGER_AMOUNT_ERROR,
                        ERR_CODE_BESTPAY_LEDGER_AMOUNT_ORDER_AMOUNT_NOT_MATCH,
                        ERR_CODE_BESTPAY_LEDGER_PARENT_MERCHANT_ERROR,
                        ERR_CODE_BESTPAY_LEDGER_DUAL_MERCHANT_NOT_ALLOW,
                        ERR_CODE_BESTPAY_MERCHANT_BANK_NOT_EXISTS,
                        ERR_CODE_BESTPAY_QUERY_BANK_ERROR,
                        ERR_CODE_BESTPAY_BANK_DECODE_FAIL,
                        ERR_CODE_BESTPAY_BARCODE_VALIDATE_ERROR,
                        ERR_CODE_BESTPAY_BE110028,
                        ERR_CODE_BESTPAY_ORDER_NOT_EXISTS,
                        ERR_CODE_BESTPAY_ORDER_NOT_EXISTS_OR_OUT_OF_REFUND_TIME_LIMIT,
                        ERR_CODE_BESTPAY_BE300012,
                        ERR_CODE_BESTPAY_BE300013,
                        ERR_CODE_BESTPAY_ORDER_PARTIAL_REFUND_NOT_SUPPORT,
                        ERR_CODE_BESTPAY_BE301001,
                        ERR_CODE_BESTPAY_BATCH_ORDER_NOT_SUPPORT_REFUND,
                        ERR_CODE_BESTPAY_ORDER_NOT_SUPPORT_REFUND,
                        ERR_CODE_BESTPAY_ORDER_NOT_SUCCESS,
                        ERR_CODE_BESTPAY_ORDER_REFUND_INPROGRESS,
                        ERR_CODE_BESTPAY_ORDER_REFUNDED_OR_CANCELED,
                        ERR_CODE_BESTPAY_ORDER_NOT_REFUNDED_CANCEL_FAIL,
                        ERR_CODE_BESTPAY_ORDER_CLOSED,
                        ERR_CODE_BESTPAY_ORDER_NOT_TODAY_ORDER_CANCEL_FAIL,
                        ERR_CODE_BESTPAY_SYSTEM_ERROR


                        )
        );}

    };


    /** 失败 协议错误 **/

    public static final Set PROTOCAL_ERR_CODE_LISTS = new HashSet(){
        {
            addAll(
                    Arrays.asList(
                            ERR_CODE_WEIXIN_NOAUTH,ERR_CODE_WEIXIN_XML_FORMAT_ERROR,
                            ERR_CODE_WEIXIN_REQUIRE_POST_METHOD,ERR_CODE_WEIXIN_SIGN_ERROR,
                            ERR_CODE_WEIXIN_LACK_PARAMS,ERR_CODE_WEIXIN_POST_DATA_EMPTY,
                            ERR_CODE_WEIXIN_NOT_UTF8,ERR_CODE_WEIXIN_APPID_NOT_EXIST,
                            ERR_CODE_WEIXIN_MCHID_NOT_EXIST,

                            ERR_CODE_ALIPAY2_INVALID_PARAMETER, ERR_CODE_ALIPAY2_PAY_ACCESS_FORBIDDEN,
                            ERR_CODE_ALIPAY2_PAY_EXIST_FORBIDDEN_WORD, ERR_CODE_ALIPAY2_PAY_PARTNER_ERROR,
                            ERR_CODE_ALIPAY2_PAY_CONTEXT_INCONSISTENT,

                            ERR_CODE_QQ_ILLEGAL_SIGN, ERR_CODE_QQ_NO_PRIVILEGE,
                            ERR_CODE_QQ_CHARSET_CONVERION_ERROR,ERR_CODE_QQ_ILLEGAL_RELATIONSHIP,

                            ERR_CODE_JD_INVALID_PARAMETER,ERR_CODE_JD_INVALID_AMOUNT_FORMAT,ERR_CODE_JD_INVALID_NOTIFY_URL,
                            ERR_CODE_JD_ILLEGAL_SIGN, ERR_CODE_JD_AMOUNT_ERROR, ERR_CODE_JD_INVALID_EXPIRE_TIME_FORMAT,
                            ERR_CODE_JD_INVALID_ORDER_SN_LENGTH,  ERR_CODE_JD_INVALID_URL_LENGTH, ERR_CODE_JD_INVALID_TRADE_NAME_LENGTH,
                            ERR_CODE_JD_INVALID_TRADE_DESC_LENGTH, ERR_CODE_JD_REFUND_NO_IS_EMPTY, ERR_CODE_JD_REFUND_AMOUNT_IS_EMPTY,
                            ERR_CODE_JD_INVALID_NOTE_LENGTH, ERR_CODE_JD_CANCEL_NO_IS_EMPTY, ERR_CODE_JD_INVALID_CANCEL_NO_LENGTH,
                            ERR_CODE_JD_DUPLICATE_CANCEL_NO, ERR_CODE_JD_INVALID_MERCHANT_NO_LENGTH,
                            ERR_CODE_JD_INVALID_TERMINAL_NO_LENGTH, ERR_CODE_JD_INVALID_EXTRA_INFO_LENGTH, ERR_CODE_JD_AMOUNT_IS_EMPTY,


                            ERR_CODE_BESTPAY_PASSWORD_ERROR,
                            ERR_CODE_BESTPAY_ILLEGAL_SIGN,
                            ERR_CODE_BESTPAY_MERCHANT_NOT_ENROLL,
                            ERR_CODE_BESTPAY_MERCHANT_KEY_NOT_CONFIG,
                            ERR_CODE_BESTPAY_MERCHANT_BANK_NOT_OPEN,
                            ERR_CODE_BESTPAY_BARCODE_STORE_CODE_IS_EMPTY,
                            ERR_CODE_BESTPAY_BE100002,
                            ERR_CODE_BESTPAY_INVALID_PARAMS,
                            ERR_CODE_BESTPAY_BE300000,
                            ERR_CODE_BESTPAY_BE300001,
                            ERR_CODE_BESTPAY_BE300006,
                            ERR_CODE_BESTPAY_BE300007

                    )
            );
        }
    };



}
