package com.wosai.mpay.api.cibbank;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Random;

/**
 * Created by jianfree on 2/8/17.
 */
public class CIBBankTest {
    public static final Logger logger = LoggerFactory.getLogger(CIBBankTest.class);

    public static void main(String[] args) throws MpayException, MpayApiNetworkError {
//        testPay();
//        testQuery();
//        testRefund();
//        testRefundQuery();
        testReverse();
//        testPrecreate();
//        testWapPrecreate();
//        testBillDownload();

    }


    public static void testPay() throws MpayException, MpayApiNetworkError {
        CIBBankClient client = new CIBBankClient();
        RequestBuilder builder = getDefaultRequestBuilder(CIBBankConstants.SERVICE_UNIFIED_TRADE_MICROPAY);
        builder.set(ProtocolFields.MCH_ID, CIBBankConfig.CIBBANK_MCH_ID);
        builder.set(BusinessFields.OUT_TRADE_NO, generateTradeNo());
//        builder.set(BusinessFields.OUT_TRADE_NO, "*****************");
        builder.set(BusinessFields.BODY, "喔噻测试商品apple");
        builder.set(BusinessFields.ATTACH, "hahahahah");
        builder.set(BusinessFields.TOTAL_FEE, "1");
        builder.set(BusinessFields.AUTH_CODE, "510738054591343782");
        builder.set(BusinessFields.MCH_CREATE_IP, "127.0.0.1");
        Map<String,Object> response =   client.call(CIBBankConfig.GATEWAY, CIBBankConfig.CIBBANK_MCH_KEY, builder.build());
    }

    public static void testQuery() throws MpayException, MpayApiNetworkError {
        CIBBankClient client = new CIBBankClient();
        RequestBuilder builder = getDefaultRequestBuilder(CIBBankConstants.SERVICE_UNIFIED_TRADE_QUERY);
        builder.set(ProtocolFields.MCH_ID, CIBBankConfig.CIBBANK_MCH_ID);
        builder.set(BusinessFields.OUT_TRADE_NO, "*****************");
        Map<String, Object> response =   client.call(CIBBankConfig.GATEWAY, CIBBankConfig.CIBBANK_MCH_KEY, builder.build());
    }

    public static void testRefund() throws MpayException, MpayApiNetworkError {
        CIBBankClient client = new CIBBankClient();
        RequestBuilder builder = getDefaultRequestBuilder(CIBBankConstants.SERVICE_UNIFIED_TRADE_REFUND);
        builder.set(ProtocolFields.MCH_ID, CIBBankConfig.CIBBANK_MCH_ID);
        builder.set(BusinessFields.OUT_TRADE_NO, "Test15016568063417406");
        builder.set(BusinessFields.OUT_REFUND_NO, "Test15016568063417406-2");
        builder.set(BusinessFields.TOTAL_FEE,  "1");
        builder.set(BusinessFields.REFUND_FEE, "1");
        builder.set(BusinessFields.OP_USER_ID, "wjw");
        Map<String, Object> response =   client.call(CIBBankConfig.GATEWAY, CIBBankConfig.CIBBANK_MCH_KEY, builder.build());
    }

    public static void testRefundQuery() throws MpayException, MpayApiNetworkError {
        CIBBankClient client = new CIBBankClient();
        RequestBuilder builder = getDefaultRequestBuilder(CIBBankConstants.SERVICE_UNIFIED_TRADE_REFUND_QUERY);
        builder.set(ProtocolFields.MCH_ID, CIBBankConfig.CIBBANK_MCH_ID);
        builder.set(BusinessFields.OUT_TRADE_NO, "*****************");
        builder.set(BusinessFields.OP_USER_ID, CIBBankConfig.CIBBANK_MCH_ID);
        Map<String, Object> response =   client.call(CIBBankConfig.GATEWAY, CIBBankConfig.CIBBANK_SIGN_AGENT_KEY, builder.build());
    }

    public static void testReverse() throws MpayException, MpayApiNetworkError {
        CIBBankClient client = new CIBBankClient();
        RequestBuilder builder = getDefaultRequestBuilder(CIBBankConstants.SERVICE_UNIFIED_MICROPAY_REVERSE);
        builder.set(ProtocolFields.MCH_ID, CIBBankConfig.CIBBANK_MCH_ID);
        builder.set(BusinessFields.OUT_TRADE_NO, "Test15016634592669979");
        Map<String, Object> response =   client.call(CIBBankConfig.GATEWAY, CIBBankConfig.CIBBANK_MCH_KEY, builder.build());
    }

    public static void testPrecreate() throws MpayException, MpayApiNetworkError {
        CIBBankClient client = new CIBBankClient();
        RequestBuilder builder = getDefaultRequestBuilder(CIBBankConstants.SERVICE_PAY_BESTPAY_JSPAY);
        builder.set(ProtocolFields.MCH_ID, "************");
        builder.set(BusinessFields.OUT_TRADE_NO, generateTradeNo());
        builder.set(BusinessFields.BODY, "喔噻测试商品apple");
        builder.set(BusinessFields.ATTACH, "hahahahah");
        builder.set(BusinessFields.TOTAL_FEE, "1");
        builder.set(BusinessFields.AUTH_CODE, "xxxx");
        builder.set(BusinessFields.MCH_CREATE_IP, "127.0.0.1");
        builder.set(BusinessFields.NOTIFY_URL, "http://www.test.com");
        builder.set(BusinessFields.TIME_EXPIRE, getTenMinitesExpireTime());
        builder.set(BusinessFields.GOODS_TAG, "xxxx");
        Map<String, Object> response =   client.call(CIBBankConfig.GATEWAY, "8a4199115aa15cd81e064c796a4da1a6", builder.build());
        System.out.println(response);
    }

    public static void testWapPrecreate() throws MpayException, MpayApiNetworkError {
        CIBBankClient client = new CIBBankClient();
        RequestBuilder builder = getDefaultRequestBuilder(CIBBankConstants.SERVICE_PAY_BESTPAY_JSPAY);
        builder.set(ProtocolFields.MCH_ID, "************");
        builder.set(BusinessFields.OUT_TRADE_NO, generateTradeNo());
        builder.set(BusinessFields.BODY, "喔噻测试商品apple");
        builder.set(BusinessFields.ATTACH, "hahahahah");
        builder.set(BusinessFields.TOTAL_FEE, "1");
        builder.set(BusinessFields.AUTH_CODE, "xxxx");
        builder.set(BusinessFields.MCH_CREATE_IP, "127.0.0.1");
        builder.set(BusinessFields.NOTIFY_URL, "http://www.test.com");
        builder.set(BusinessFields.TIME_EXPIRE, getTenMinitesExpireTime());
        builder.set(BusinessFields.GOODS_TAG, "xxxx");
        Map<String, Object> response =   client.call(CIBBankConfig.GATEWAY, "8a4199115aa15cd81e064c796a4da1a6", builder.build());
    }

    public static void testBillDownload() throws MpayException, MpayApiNetworkError {
        CIBBankClient client = new CIBBankClient();
        RequestBuilder builder = getDefaultRequestBuilder(CIBBankConstants.SERVICE_PAY_BILL_AGENT);
        builder.set(ProtocolFields.MCH_ID, CIBBankConfig.CIBBANK_SIGN_AGENT_NO);
        builder.set(BusinessFields.BILL_DATE, "********");
        builder.set(BusinessFields.BILL_TYPE, CIBBankConstants.BILL_TYPE_ALL);
        String response =   client.downloadBill(CIBBankConfig.BILL_GATEWAY, CIBBankConfig.CIBBANK_SIGN_AGENT_KEY, builder.build());

    }

    public static RequestBuilder getDefaultRequestBuilder(String service){
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.CHARSET, CIBBankConstants.CHARSET_UTF8);
        builder.set(ProtocolFields.MCH_ID, "************");
        builder.set(ProtocolFields.SIGN_TYPE, CIBBankConstants.SIGN_TYPE_MD5);
        builder.set(ProtocolFields.VERSION, CIBBankConstants.VERSION);
        builder.set(ProtocolFields.SERVICE, service);
        return builder;
    }


    /**
     * 获取过期时间
     * @return
     */
    public static String getTenMinitesExpireTime(){
        return new SimpleDateFormat(CIBBankConstants.DATE_TIME_FORMAT).format(new Date(System.currentTimeMillis() + 1000 * 60 * 10));
    }

    private static String generateTradeNo() {
        Random random = new Random();
        return String.format("Test%d%d", System.currentTimeMillis(), random.nextInt(10000));
    }
}
