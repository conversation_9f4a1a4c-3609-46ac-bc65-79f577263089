package com.wosai.mpay.api.cibbank;

/**
 * Created by jian<PERSON> on 16/6/16.
 */
public class ResponseFields {

    public static final String ATTACH = "attach";//附加信息 商家数据包，原样返回
    public static final String BANK_BILLNO = "bank_billno";//银行订单号，若为微信支付则为空
    public static final String BANK_TYPE = "bank_type";//付款银行 银行类型
    public static final String CHARSET = "charset";//字符集 可选值 UTF-8 ，默认为 UTF-8。
    public static final String COUPON_FEE = "coupon_fee";//现金券金额 现金券支付金额<=订单总金额， 订单总金额  -现金券金额为现金支付金额
    public static final String COUPON_REFUND_FEE = "coupon_refund_fee";//现 金券 退 款 金
    public static final String DEVICE_INFO = "device_info";//设备号 威富通支付分配的终端设备号
    public static final String ERR_CODE = "err_code";//错误代码 具体错误码请看文档最后错误码列表
    public static final String ERR_MSG = "err_msg";//错误代码描述 结果信息描述
    public static final String FEE_TYPE = "fee_type";//货币种类 货币类型，符合 ISO 4217 标准的三位字母 代码，默认人民币：CNY
    public static final String IS_SUBSCRIBE = "is_subscribe";//是否关注公众账 号 用户是否关注公众账号，0-关注，1-未关注， 仅在公众 账号类型支付有效
    public static final String MCH_ID = "mch_id";//商户号，由威富通分配
    public static final String MESSAGE = "message";//返回信息，如非空，为错误原因签名失败参 数格式校验错误
    public static final String NEED_QUERY = "need_query";//查询标识 Y:需要查询，N：不需要查询
    public static final String NONCE_STR = "nonce_str";//随机字符串，不长于 32 位
    public static final String OPENID = "openid";//用户标识 用户在商户 appid 下的唯一标识
    public static final String SUB_OPENID = "sub_openid";//用户标识 用户在商户 appid 下的唯一标识
    public static final String OUT_REFUND_NO = "out_refund_no";//商户退款单号
    public static final String OUT_TRADE_NO = "out_trade_no";//商户订单号 商户系统内部的订单号
    public static final String OUT_TRANSACTION_ID = "out_transaction_id";//第三方订单号
    public static final String PAY_INFO = "pay_info";//支付结果信息，支付成功时为空
    public static final String PAY_RESULT = "pay_result";//支付结果：0—成功；其它—失败
    public static final String REFUND_CHANNEL = "refund_channel";//退款渠道 ORIGINAL—原路退款，默认 BALANCE—退回到余额
    public static final String REFUND_COUNT = "refund_count";//退款笔数 退款记录数
    public static final String REFUND_FEE = "refund_fee";//退款金额 退款总金额,单位为分,可以做部分退款
    public static final String REFUND_ID = "refund_id";//威富通退款单
    public static final String RESULT_CODE = "result_code";//业务结果 0 表示成功非 0 表示失败
    public static final String SIGN = "sign";//签名 MD5 签名结果，详见“第 4 章 MD5 签名规则”
    public static final String SIGN_AGENTNO = "sign_agentno";//授权渠道编号 如果不为空，则用授权渠道的密钥进行签名
    public static final String SIGN_TYPE = "sign_type";//签名方式 签名类型，取值：MD5 默认：MD5
    public static final String STATUS = "status";//返回状态码 0 表示成功非 0 表示失败
    public static final String SUB_APPID = "sub_appid";//子商户 appid
    public static final String SUB_IS_SUBSCRIBE = "sub_is_subscribe";//子商户是否关注 用户是否关注子公众账号，0-关注，1-未关 注，仅在公众 账号类型支付有效
    public static final String TIME_END = "time_end";//支付完成时间，格式为 yyyyMMddhhmmss，如  2009 年 12 月 27 日 9 点 10 分 10 秒表示为  20091227091010。时区为 GMT+8 beijing。该 时间取自威富通服务器
    public static final String TOTAL_FEE = "total_fee";//总金额，以分为单位，不允许包含任何字、  符号
    public static final String TRADE_TYPE = "trade_type";//交易类型
    public static final String TRANSACTION_ID = "transaction_id";//威富通订单号 威富通交易号。
    public static final String TRADE_STATE = "trade_state";//交易状态
    public static final String CODE_URL = "code_url"; //二维码链接
    public static final String TOKEN_ID = "token_id"; //动态口令 威富通成的预支付 ID,用于后续接口调用中 使用
    public static final String BUYER_LOGON_ID = "buyer_logon_id";//买家支付宝账号
    public static final String BUYER_USER_ID = "buyer_user_id";
    public static final String BUYER_ID = "buyer_id";//买家支付宝用户 ID
    public static final String FUND_BILL_LIST = "fund_bill_list";
    public static final String PAY_URL = "pay_url";



}
