package com.wosai.mpay.api.cibbank;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.*;
import com.wosai.pantheon.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.util.Map;
import java.util.Random;

/**
 * Created by jianfree on 16/6/16.
 */
public class CIBBankClient {
    public static final Logger logger = LoggerFactory.getLogger(CIBBankClient.class);
    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public Map<String,Object> call(String serviceUrl, String signKey,  Map<String,Object> request) throws MpayException, MpayApiNetworkError {
        request.put(ProtocolFields.NONCE_STR, getNonceStr());
        for(Object mapKey: request.keySet().toArray()){
            if(request.get(mapKey) == null){
                request.remove(mapKey);
            }
        }
        request.remove(ProtocolFields.SIGN);
        String signType = MapUtil.getString(request, ProtocolFields.SIGN_TYPE);
        String sign;
        if(CIBBankConstants.SIGN_TYPE_MD5.equals(signType)){
            sign = WeixinSignature.getSign(request, signKey, CIBBankConstants.CHARSET_UTF8);
        }else if(CIBBankConstants.SIGN_TYPE_SHA256RSA.equals(signType)){
            sign = RsaSignature.sign(request, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, signKey);
        }else if(CIBBankConstants.SIGN_TYPE_SHA1RSA.equals(signType)){
            sign = RsaSignature.sign(request, RsaSignature.SIG_ALG_NAME_SHA1_With_RSA, signKey);
        }else{
            sign = WeixinSignature.getSign(request, signKey, CIBBankConstants.CHARSET_UTF8);
        }
        request.put(ProtocolFields.SIGN, sign);
        String requestXml = XmlUtils.map2XmlString(request, "xml");
        logger.debug("request {}", requestXml);
        String response = HttpClientUtils.doPost(CIBBankClient.class.getName(), null, null, serviceUrl, "text/xml", requestXml,CIBBankConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        logger.debug("response {}",response.replaceAll("\\n", ""));
        Map<String, Object> result = XmlUtils.parse(response);
        return result;
    }



    public String downloadBill(String serviceUrl,String signKey,  Map<String,Object> request) throws MpayException, MpayApiNetworkError {
        request.put(ProtocolFields.NONCE_STR, getNonceStr());
        for(Object mapKey: request.keySet().toArray()){
            if(request.get(mapKey) == null){
                request.remove(mapKey);
            }
        }
        request.remove(ProtocolFields.SIGN);
        request.put(ProtocolFields.SIGN, WeixinSignature.getSign(request, signKey, CIBBankConstants.CHARSET_UTF8));
        String requestXml = XmlUtils.map2XmlString(request, "xml");
        logger.debug("request {}", requestXml);
        String response = WebUtils.doPost(null, null, serviceUrl, "text/xml", getBytes(requestXml, CIBBankConstants.CHARSET_UTF8), connectTimeout, readTimeout);
        logger.debug("response {}", response);
        return response;
    }



    private String getNonceStr(){
        return new Random().nextLong() + "";
    }



    private byte[] getBytes(String string , String charset) throws MpayException {
        if(string == null){
            return null;
        }else {
            try {
                return string.getBytes(charset);
            } catch (UnsupportedEncodingException e) {
                throw new MpayException("UnsupportedEncodingException :", e);
            }
        }
    }



    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
}
