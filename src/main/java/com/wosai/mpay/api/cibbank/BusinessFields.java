package com.wosai.mpay.api.cibbank;

/**
 * Created by jian<PERSON> on 16/6/16.
 */
public class BusinessFields {
    public static final String ATTACH = "attach";//附加信息 商户附加信息，可做扩展参数，255 字符内
    public static final String ATTACH_BANK_MCH_NAME = "bank_mch_name";
    public static final String ATTACH_BANK_MCH_ID = "bank_mch_id";
    public static final String AUTH_CODE = "auth_code";//授权码 扫码支付授权码， 设备读取用户展示的条码 或者二维码信息
    public static final String BODY = "body";//商品描述
    public static final String DEVICE_INFO = "device_info";//设备号 威富通支付分配的终端设备号
    public static final String GOODS_TAG = "goods_tag";//商品标记
    public static final String MCH_CREATE_IP = "mch_create_ip";//终端 IP 订单生成的机器 IP
    public static final String NOTIFY_URL = "notify_url";//接收威富通通知的 URL
    public static final String OP_DEVICE_ID = "op_device_id";//设备编号
    public static final String OP_SHOP_ID = "op_shop_id";//门店编号
    public static final String OP_USER_ID = "op_user_id";//操作员帐号,默认为商户号
    public static final String OUT_REFUND_NO = "out_refund_no";//商户退款单号，32 个字符内、可包含字母,确保 在商户系统唯一。
    public static final String OUT_TRADE_NO = "out_trade_no";//商户订单号 商户系统内部的订单号 ,32 个字符内、 可包含字母,确保 在商户系统唯一
    public static final String REFUND_CHANNEL = "refund_channel";//退款渠道 ORIGINAL-原路退款，默认 BALANCE-余额
    public static final String REFUND_FEE = "refund_fee";//退款金额 退款总金额,单位为分,可以做部分退款
    public static final String REFUND_ID = "refund_id";//威 富通 退 款 单
    public static final String QR_CODE_TIMEOUT_EXPRESS = "qr_code_timeout_express"; //该笔订单允许的最晚付款时间，逾期将关闭交易。取值范围：1m～15d。m-分钟，h-小时，d-天，1c-当天（1c-当天的情况下，无论交易何时创建，都在0点关闭）。 该参数数值不接受小数点， 如 1.5h，可转换为 90m。
    public static final String TIME_EXPIRE = "time_expire";//订单超时时间 订单失效时间，格式为 yyyymmddhhmmss，如  2009 年 12 月 27 日 9 点 10 分 10 秒表示为 20091227091010。时区为 GMT+8 beijing。该 时间取自商户服务器
    public static final String TIME_START = "time_start";//订单生成时间，格式为 yyyymmddhhmmss，如  2009 年 12 月 25 日 9 点 10 分 10 秒表示为 20091225091010。时区为 GMT+8 beijing。该 时间取自商户服务器
    public static final String TOTAL_FEE = "total_fee";//总金额 订单总金额，单位为分
    public static final String TRANSACTION_ID = "transaction_id";//威富通订单号 威富通单号, out_trade_no 和 transaction_id 至少 一个必填，同时存在时 transaction_id 优先
    public static final String SUB_OPENID = "sub_openid";//用户标识 用户在商户 appid 下的唯一标识
    public static final String SUB_APPID = "sub_appid";//当发起公众号支付时，值是微信公众平台基本配置中的AppID(应用ID)；当发起小程序支付时，值是对应小程序的AppID
    public static final String IS_RAW = "is_raw"; //是否原生态
    public static final String BILL_DATE = "bill_date"; //账单日期 格式:yyyyMMdd(如:20150101)
    public static final String BILL_TYPE = "bill_type"; //账单类型
    public static final String BUYER_LOGON_ID = "buyer_logon_id";//买家支付宝账号
    public static final String BUYER_USER_ID = "buyer_user_id";
    public static final String BUYER_ID = "buyer_id";//买家支付宝用户 ID
    public static final String SYS_PROVIDER_ID = "sys_provider_id"; //服务商编号
    public static final String IS_MINIPG = "is_minipg"; // 是否小程序支付 值为1，表示小程序支付；不传或值不为1，表示公众账号内支付
    public static final String LIMIT_CREDIT_PAY = "limit_credit_pay";//限定用户使用时能否使用信用卡，值为1，禁用信用卡；值为0或者不传此参数则不禁用

}
