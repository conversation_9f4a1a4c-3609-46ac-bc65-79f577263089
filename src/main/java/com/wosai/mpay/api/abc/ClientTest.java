package com.wosai.mpay.api.abc;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/1/15、17:15
 **/

public class ClientTest {

    public static String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2iCJ36duFr0Ke5M37zZzZJ4kK\n" +
            "mRApr42H8rG1mpuCQNvNmgSKl3L+0P1mqQf3/0RrseB9G8BKNwlqBQSz28L9ynWQ\n" +
            "v4Hq1LTgHtEjQc0w73ZJ32JeOmfq8oor84YxjNDNJu3RHNNkuOyM7lFr3BtYkaIa\n" +
            "IAvLc8LeO2tx4nvRTwIDAQAB";

    public static void main(String[] args) throws Exception {
        payTest();
//        queryTest();
//        refundTest();

    }

    public static void payTest() throws Exception {
        Map<String, String> HEADERS = new HashMap<>();
        HEADERS.put("Content-Type", "application/x-www-form-urlencoded");

        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.set(ABCRequestFields.PAYMODE, "2");
        requestBuilder.set(ABCRequestFields.TRANSTYPE, "1");
        requestBuilder.set(ABCRequestFields.COUNTNO, "950030000001450");
        requestBuilder.set(ABCRequestFields.TERMINAL_SERIALNO, "121546");
        requestBuilder.set(ABCRequestFields.AMOUNT, "0.01");
        requestBuilder.set(ABCRequestFields.AUTH_CODE, "285465438819083314");

        requestBuilder.setPlatformField("reqDate", getCurrentDate("yyyyMMdd"));
        requestBuilder.setPlatformField("reqTime", getCurrentDate("HHmmss"));
        requestBuilder.setPlatformField("provCode", "50");
        requestBuilder.setPlatformField("srcAreaCode", "50");
        requestBuilder.setPlatformField("accessType", "Application/json");


        String gateway = "https://bjuat.echase.cn/gateway/bmpapi/postrans";
//        String gateway = "https://enpnj61i9dkq.x.pipedream.net";

        ABCClient abcClient = new ABCClient();
        Map<String, Object> result = abcClient.call(requestBuilder, gateway, publicKey, HEADERS);
        System.out.println(result);

    }

    //{paymode=2, transtype=1, channelno=1, terminal_serialno=1212, respcode=NP, result=订单待支付, transname=条码支付, merchant_code=113132103001301, terminal_code=00001450, countno=950030000001450, orderid=19500300000014502401222129037652, amount=0.01, payamount=0.00, macdata=fgotGAIGAMEplB0F+5qLhDrkJAJngl96yvDFk6QRzDbwwzyH34QTR79sSxiNGWYKHvnFZ3WwTZThRgQ/5qPQ7cguKGffYsOozQx/cNfVei1QnxRNwoa8XOawdu4qOgu1bLmmJPWhIa79X/1oRA6JbqIshTpE1Rv8ZORuiBv1SKo=}

    public static void queryTest() throws Exception {

        Map<String, String> HEADERS = new HashMap<>();
        HEADERS.put("Content-Type", "application/x-www-form-urlencoded");

        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.set(ABCRequestFields.PAYMODE, "2");
        requestBuilder.set(ABCRequestFields.TRANSTYPE, "5");
        requestBuilder.set(ABCRequestFields.COUNTNO, "950030000001450");
        requestBuilder.set(ABCRequestFields.TERMINAL_SERIALNO, "1214");
        requestBuilder.set(ABCRequestFields.AMOUNT, "0.01");
//        requestBuilder.set(AbcRequestConstant.AUTH_CODE, "281806964250627467");

        requestBuilder.setPlatformField("reqDate", getCurrentDate("yyyyMMdd"));
        requestBuilder.setPlatformField("reqTime", getCurrentDate("HHmmss"));
        requestBuilder.setPlatformField("provCode", "09");
        requestBuilder.setPlatformField("srcAreaCode", "09");
        requestBuilder.setPlatformField("accessType", "Application/json");


        String gateway = "https://bjuat.echase.cn/gateway/bmpapi/postrans";

        ABCClient abcClient = new ABCClient();
        Map<String, Object> result = abcClient.call(requestBuilder, gateway, publicKey, HEADERS);

        System.out.println(result);
    }


    public static void refundTest() throws Exception {

        Map<String, String> HEADERS = new HashMap<>();
        HEADERS.put("Content-Type", "application/x-www-form-urlencoded");

        RequestBuilder requestBuilder = new RequestBuilder();
        requestBuilder.set(ABCRequestFields.PAYMODE, "2");
        requestBuilder.set(ABCRequestFields.TRANSTYPE, "3");
        requestBuilder.set(ABCRequestFields.COUNTNO, "950030000001450");
        //流水号 每次都要不一样
        requestBuilder.set(ABCRequestFields.TERMINAL_SERIALNO, "3");
        //原交易流水
        requestBuilder.set("orig_terminal_serialno", "1214");
        requestBuilder.set(ABCRequestFields.AMOUNT, "0.01");
//        requestBuilder.set(AbcRequestConstant.AUTH_CODE, "281806964250627467");

        requestBuilder.setPlatformField("reqDate", getCurrentDate("yyyyMMdd"));
        requestBuilder.setPlatformField("reqTime", getCurrentDate("HHmmss"));
        requestBuilder.setPlatformField("provCode", "09");
        requestBuilder.setPlatformField("srcAreaCode", "09");
        requestBuilder.setPlatformField("accessType", "Application/json");


        String gateway = "https://bjuat.echase.cn/gateway/bmpapi/postrans";

        ABCClient abcClient = new ABCClient();
        Map<String, Object> result = abcClient.call(requestBuilder, gateway, publicKey, HEADERS);
        System.out.println(result);
    }


    public static String getCurrentDate(String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date());
    }
}
