package com.wosai.mpay.api.abc;

/**
 * <AUTHOR>
 * @Date 2024/1/12、16:42
 **/

import java.nio.charset.StandardCharsets;


import com.wosai.mpay.util.RsaSignature;
import org.apache.commons.codec.binary.Base64;

public class RSAEncrypt {

    public static String encrypt(String str, String publicKey) throws Exception {
        return Base64.encodeBase64String(RsaSignature.encryptByPublicKey(str.getBytes(StandardCharsets.UTF_8), publicKey));
    }

    /**
     *
     * @param str base64 decode string
     * @param privateKey
     * @return
     * @throws Exception
     */
    public static String decrypt(String str, String privateKey) throws Exception {
        return new String(RsaSignature.decryptByPrivateKey(com.wosai.mpay.util.Base64.decode(str), privateKey));
    }

    /**
     * 用私钥对信息生成数字签名
     */
    public static String sign(byte[] data, String privateKey) throws Exception {
        return RsaSignature.sign(data, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, privateKey);
    }


    /**
     * 校验数字签名
     */
    public static boolean verify(byte[] data, String publicKey, String sign) throws Exception {
        return RsaSignature.validateSign(data, sign, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, publicKey);
    }
}

