package com.wosai.mpay.api.abc;

/**
 * <AUTHOR>
 * @Date 2024/1/17、18:47
 * 农行通道返回key
 **/

public class ABCResponseFields {
    // 交易模式
    public static final String PAYMODE = "paymode";
    // 交易类型
    public static final String TRANSTYPE = "transtype";
    // 条码支付渠道标识
    public static final String CHANNELNO = "channelno";
    // 流水号
    public static final String TERMINAL_SERIALNO = "terminal_serialno";
    // 响应码
    public static final String RESPCODE = "respcode";
    // 响应描述
    public static final String RESULT = "result";
    // 交易名称
    public static final String TRANSNAME = "transname";
    // 商户号
    public static final String MERCHANT_CODE = "merchant_code";
    // 终端号
    public static final String TERMINAL_CODE = "terminal_code";
    // 交易日期
    public static final String TRANSDATE = "transdate";
    // 交易时间
    public static final String TRANSTIME = "transtime";
    // 农行流水号
    public static final String ABCTRACENO = "abctraceno";
    // 农行批次号
    public static final String ABCBATCHNO = "abcbatchno";
    // 农行凭证号
    public static final String ABCVOUCHERNO = "abcvoucherno";
    // 系统参考号
    public static final String RRN = "rrn";
    // 订单号
    public static final String ORDERID = "orderid";
    // 三方订单号
    public static final String TRANSACTION_ID = "transaction_id";
    // 交易金额
    public static final String AMOUNT = "amount";
    // 实付金额
    public static final String PAYAMOUNT = "payamount";
    // 微信用户子标识
    public static final String SUB_OPENID = "sub_openid";
    // 用户标识
    public static final String USER_ID = "user_id";
    // 验签字符串
    public static final String MACDATA = "macdata";

    public static final String ORIGORDERID = "origorderId";


}
