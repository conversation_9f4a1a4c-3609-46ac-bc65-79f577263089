package com.wosai.mpay.api.abc;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.api.ccb.CcbClient;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Base64;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.XmlUtils;
import com.wosai.pantheon.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/1/15、15:37
 * 农业银行第三方通道
 **/

public class ABCClient {
    public static final Logger logger = LoggerFactory.getLogger(ABCClient.class);
    private ObjectMapper objectMapper = new ObjectMapper();

    private int connectTimeout = 3000;
    private int readTimeout = 15000;
    private String charset = "utf-8";

    private String GBK = "gbk";

    private String PARAMS_PREFIX = "0004";

    public ABCClient() {
    }

    public ABCClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public ABCClient(int connectTimeout, int readTimeout, String charset) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
        this.charset = charset;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public Map<String, Object> call(RequestBuilder requestBuilder, String gateway
            , String publicKey) throws MpayException, MpayApiNetworkError {
        return call(requestBuilder, gateway, publicKey, null);
    }

    public Map<String, Object> call(RequestBuilder requestBuilder, String gateway
            , String publicKey, Map<String, String> headers)
            throws MpayException, MpayApiNetworkError {


        Map<String, String> request = requestBuilder.getRequest();
        String transtype = MapUtil.getString(request, ABCRequestFields.TRANSTYPE);
        String amount = MapUtil.getString(request, ABCRequestFields.AMOUNT);
        String terminalSerialno = MapUtil.getString(request, ABCRequestFields.TERMINAL_SERIALNO);

        String macdata = null;
        try {
            macdata = RSAEncrypt.encrypt(transtype + amount + terminalSerialno, publicKey);
        } catch (Exception e) {
            throw new RuntimeException("农行加签异常");
        }
        request.put(ABCRequestFields.MACDATA, macdata);
        String jsonStr = JsonUtil.toJsonStr(request);
        jsonStr = PARAMS_PREFIX + jsonStr;
        macdata = getBase64Data(jsonStr, GBK);

        String platformParams = requestBuilder.genPlatformFieldParams();

        String url = gateway + "?" + platformParams + "&sendPackage=" + macdata;
        logger.debug("request {}, oriParams: {}", url, jsonStr);

        Map<String, Object> result = HttpClientUtils.doGet(ABCClient.class.getName()
                , null, null
                , url, null, headers, charset, connectTimeout, readTimeout);

        try {
            String body = MapUtil.getString(result, HttpClientUtils.BODY_RESULT_FIELD);
            Map<String, Object> parse = XmlUtils.parse(body);
            String recvPackage = MapUtil.getString(parse, ABCRequestFields.RECV_PACKAGE);
            String resultStr = getDecodeBase64(recvPackage, GBK);
            resultStr = resultStr.replace(PARAMS_PREFIX, "");
            Map map = JsonUtil.jsonStringToObject(resultStr, Map.class);
            logger.debug("response {}", resultStr);
            return map;
        } catch (Exception e) {
            throw new MpayApiUnknownResponse("parse response error: " + e.getMessage(), e);
        }
    }


    public String getBase64Data(String str, String charset) throws MpayException {
        try {
            String data = Base64.encode(str.getBytes(charset));
            return data;
        } catch (Exception e) {
            throw new MpayException("Base64 encode error: ", e);
        }
    }

    public String getDecodeBase64(String content, String charset) throws MpayException {
        try {
            return new String(Base64.decode(content), charset);
        } catch (Exception e) {
            throw new MpayException("Base64 decode error: ", e);
        }
    }

}



