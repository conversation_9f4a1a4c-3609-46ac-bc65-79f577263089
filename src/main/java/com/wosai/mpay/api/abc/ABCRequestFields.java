package com.wosai.mpay.api.abc;

/**
 * <AUTHOR>
 * @Date 2024/1/15、15:04
 **/

public class ABCRequestFields {
    // 交易模式
    public static final String PAYMODE = "paymode";
    // 交易类型
    public static final String TRANSTYPE = "transtype";
    // 款台号
    public static final String COUNTNO = "countno";
    // 款台序列号
    public static final String COUNTSEQ = "countseq";
    // 流水号
    public static final String TERMINAL_SERIALNO = "terminal_serialno";
    // 交易金额
    public static final String AMOUNT = "amount";
    // 操作员号
    public static final String OPERID = "operid";
    // 付款码
    public static final String AUTH_CODE = "auth_code";
    // 商品名称
    public static final String BODY = "body";
    // 订单备注
    public static final String REMARKS = "remarks";
    // 微信公众账号 ID
    public static final String SUB_APPID = "sub_appid";
    // 终端实时经纬度信息
    public static final String POS_GA = "pos_ga";
    // 备用字段
    public static final String TEMPFIELD = "tempfield";
    // 场景业态类型编码
    public static final String BUSINESS_FORMAT = "business_format";
    // 业务订单字段集
    public static final String EXT_ORDER_INFO = "ext_order_info";
    // 分账信息字段集
    public static final String SPLIT_AMT_INFO = "split_amt_info";
    // 验签字符串
    public static final String MACDATA = "macdata";
    // 订单号
    public static final String ORDERID = "orderid";
    // 原请求流水号
    public static final String ORIG_TERMINAL_SERIALNO = "orig_terminal_serialno";

    public static final String RECV_PACKAGE = "recvPackage";

    public static final String OS = "os";
    public static final String APP_VERSION = "appVersion";
    public static final String REQ_DATE = "reqDate";
    public static final String REQ_TIME = "reqTime";
    public static final String PROV_CODE = "provCode";
    public static final String SRC_AREA_CODE = "srcAreaCode";
    public static final String SYS_ID = "sysId";
    public static final String ACCESS_TYPE = "accessType";

}
