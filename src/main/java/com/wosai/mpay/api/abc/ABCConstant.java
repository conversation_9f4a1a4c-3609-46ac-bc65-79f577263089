package com.wosai.mpay.api.abc;


import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/1/17、19:43
 **/

public class ABCConstant {

    //请求返回成功，不一定是业务返回成功
    public static final String RESP_SUCCESS = "00";

    public static final String QRCODE = "2";

    public static final String B2C = "2";

    public static final String REFUND = "3";

    public static final String QUERY = "5";

    public static final String PAY = "1";


    public static final String NAME = "provider.abc";


    public static Map<String, String> HEADERS = new HashMap() {
        {
            put("Content-Type", "application/x-www-form-urlencoded");
        }
    };

    public static final String DATE_SIMPLE_FORMAT = "yyyyMMdd";

    public static final String DATE_TIME_SIMPLE_FORMAT = "HHmmss";

    public static final String ACCESS_TYPE = "Application/json";

    //地区码 一期先写死50
    public static final String AREA_CODE = "50";

    public static Set<String> NEED_QUERY = new HashSet(){
        {
            add("NP");
            add("RB");
        }
    };



}
