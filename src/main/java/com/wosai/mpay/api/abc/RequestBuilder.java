package com.wosai.mpay.api.abc;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/1/15、15:43
 **/
public class RequestBuilder {

    private final Map<String, String> platformField;

    private final Map<String, String> request;

    public RequestBuilder() {
        request = new HashMap<String, String>();
        platformField = new HashMap<String, String>();
    }

    public Map<String, String> getRequest() {
        return request;
    }

    public Map<String, String> getPlatformField() {
        return platformField;
    }

    public RequestBuilder set(String field, String value) {
        request.put(field, value);
        return this;
    }

    public RequestBuilder setPlatformField(String field, String value) {
        platformField.put(field, value);
        return this;
    }

    public String genRequestParams() {
        StringBuilder temp = new StringBuilder();
        request.forEach((k, v) -> {
            if (temp.length() > 0) {
                temp.append("&");
            }
            temp.append(k).append("=").append(v);
        });
        return temp.toString();
    }

    public String genPlatformFieldParams() {
        StringBuilder temp = new StringBuilder();
        platformField.forEach((k, v) -> {
            if (temp.length() > 0) {
                temp.append("&");
            }
            temp.append(k).append("=").append(v);
        });
        return temp.toString();
    }
}
