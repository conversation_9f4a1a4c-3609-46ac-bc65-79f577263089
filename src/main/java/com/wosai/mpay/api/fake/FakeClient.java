package com.wosai.mpay.api.fake;

import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.XmlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/***
 * @ClassName: fakeClient
 * @Description:
 * @Auther: dabuff
 * @Date: 2024/9/29 14:56
 */
public class FakeClient {

    public static final Logger logger = LoggerFactory.getLogger(FakeClient.class);

    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map<String, Object> call(String gatewayUrl, Map<String, Object> request, String contentType, String requestFormat) {
        try {
            String requestStr = processRequest(request, requestFormat);
            logger.info("request is {}", requestStr);
            String responseStr = HttpClientUtils.doPost(FakeClient.class.getName(), null, null, gatewayUrl, contentType, requestStr, FakeConstant.CHARSET_UTF8, connectTimeout, readTimeout);
            logger.info("response is {}",  responseStr);

            return processResponse(responseStr, requestFormat);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    private String processRequest(Map<String, Object> request, String requestFormat) throws MpayException {

        if (FakeConstant.JSON_FORMAT.equals(requestFormat)) {
            return JsonUtil.objectToJsonString(request);
        } else if (FakeConstant.XML_FORMAT.equals(requestFormat)) {
            return XmlUtils.map2XmlString(request, FakeConstant.XML_FORMAT);
        }

        return null;
    }


    private Map<String, Object> processResponse(String responseStr, String requestFormat) throws MpayException {

        if (FakeConstant.JSON_FORMAT.equals(requestFormat)) {
            return JsonUtil.jsonStringToObject(responseStr, Map.class);
        } else if (FakeConstant.XML_FORMAT.equals(requestFormat)) {
            return XmlUtils.parse(responseStr);
        }

        return null;
    }

}
