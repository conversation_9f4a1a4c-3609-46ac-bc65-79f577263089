package com.wosai.mpay.api.spdb.security;

import java.io.IOException;
import java.math.BigInteger;

import org.bouncycastle.math.ec.ECPoint;

public class SM2EnDecryptor {
    public SM2EnDecryptor() {
    }

    public static String encrypt(byte[] hexStrPub, byte[] data) {
        byte[] source = new byte[data.length];
        System.arraycopy(data, 0, source, 0, data.length);
        SM2Cipher cipher2sm2 = new SM2Cipher();
        SM2Factory sm2Factory = SM2Factory.getInstance();
        byte[] formatedPubKey;
        if (hexStrPub.length == 64) {
            formatedPubKey = new byte[65];
            formatedPubKey[0] = 4;
            System.arraycopy(hexStrPub, 0, formatedPubKey, 1, hexStrPub.length);
        } else {
            formatedPubKey = hexStrPub;
        }

        ECPoint ecPoint = sm2Factory.ecc_curve.decodePoint(formatedPubKey);
        ECPoint c1 = cipher2sm2.Init_enc(sm2Factory, ecPoint);
        cipher2sm2.Encrypt(source);
        byte[] c3 = new byte[32];
        cipher2sm2.Dofinal(c3);
        return SM2Util.byteToHex(c1.getEncoded(false)) + SM2Util.byteToHex(source) + SM2Util.byteToHex(c3);
    }

    public static byte[] decrypt(byte[] privateKey, byte[] encryptedData) throws IOException {
        if (privateKey != null && privateKey.length != 0) {
            if (encryptedData != null && encryptedData.length != 0) {
                String data = SM2Util.byteToHex(encryptedData);
                byte[] c1Bytes = SM2Util.hexToByte(data.substring(0, 130));
                int c2Len = encryptedData.length - 97;
                byte[] c2 = SM2Util.hexToByte(data.substring(130, 130 + 2 * c2Len));
                byte[] c3 = SM2Util.hexToByte(data.substring(130 + 2 * c2Len, 194 + 2 * c2Len));
                SM2Factory sm2 = SM2Factory.getInstance();
                BigInteger userD = new BigInteger(1, privateKey);
                ECPoint c1 = sm2.ecc_curve.decodePoint(c1Bytes);
                SM2Cipher cipher = new SM2Cipher();
                cipher.Init_dec(userD, c1);
                cipher.Decrypt(c2);
                cipher.Dofinal(c3);
                return c2;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }
}
