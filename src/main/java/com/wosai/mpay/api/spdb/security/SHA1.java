package com.wosai.mpay.api.spdb.security;

import java.security.MessageDigest;
import javax.xml.bind.DatatypeConverter;

public class SHA1 {
    public SHA1() {
    }

    public static String digest(String content) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-1");
            messageDigest.update(content.getBytes("UTF-8"));
            byte[] digestBytes = messageDigest.digest();
            return DatatypeConverter.printBase64Binary(digestBytes);
        } catch (Exception var3) {
            var3.printStackTrace();
            return null;
        }
    }
}