package com.wosai.mpay.api.spdb;

public class SPDBConstant {

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYYMMDD = "yyyyMMdd";

    /**
     * http 状态码
     */
    public static final String HTTP_STATUS_CODE_SUCCESS = "200"; //下载成功
    public static final String HTTP_STATUS_CODE_REQ_PARAM_ERROR_CLI = "400"; //请求参数错误

    public static final String HTTP_STATUS_CODE_NO_ACCESS_PERMISSION = "401"; //无访问权限
    public static final String HTTP_STATUS_CODE_USER_IP_FORBIDDEN = "403"; //用户IP禁止访问

    public static final String HTTP_STATUS_CODE_NOT_FOUND = "404"; //找不到请求路径

    public static final String HTTP_STATUS_CODE_ILLEGAL_REQUEST = "405"; //非法的请求方式

    public static final String HTTP_STATUS_CODE_REQ_PARAM_ERROR_SERVER = "500"; //请求参数有误

    /**
     * 响应报文头状态码
     */

    public static final String STATUS_CODE_0000 = "0000"; //交易成功
    public static final String STATUS_CODE_0001 = "0001"; //未知错误
    public static final String STATUS_CODE_0002 = "0002"; //参数校验失败


    /**
     * 支付状态码
     */

    public static final String STATUS_CODE_00 = "00"; //交易成功
    public static final String STATUS_CODE_01 = "01"; //交易失败
    public static final String STATUS_CODE_02 = "03"; //部分退货

    public static final String STATUS_CODE_O4 = "04"; //全部退货
    public static final String STATUS_CODE_05 = "05"; //退货中
    public static final String STATUS_CODE_09 = "09"; //支付中

    public static final String STATUS_CODE_99 = "99"; //超时


    /**
     * 交易类型
     */

    public static final String TRAN_TYPE_OA = "OA"; //微信公众号支付
    public static final String TRAN_TYPE_OB = "OB"; //微信动态扫码支付
    public static final String TRAN_TYPE_OC = "OC"; //微信刷卡支付

    public static final String TRAN_TYPE_OD = "OD"; //支付宝固定二维码支付
    public static final String TRAN_TYPE_OE = "OE"; //支付宝动态扫码支付
    public static final String TRAN_TYPE_OF = "OF"; //支付宝条码支付
    public static final String TRAN_TYPE_OG = "OG"; //微信APP支付

    public static final String TRAN_TYPE_OH = "OH"; //微信H5支付


    public static final String TRAN_TYPE_OK = "OK"; //聚合动态码支付
    public static final String TRAN_TYPE_OM = "OM"; //微信小程序支付
    public static final String TRAN_TYPE_UA = "UA"; //银联扫码支付(被扫)
    public static final String TRAN_TYPE_UB = "UB"; //银联动态码支付(主扫)


    /* 人名币标示*/
    public static final String CCY = "01";


    public static final String ORDER_ClOSE_SUCCESS = "0000000"; //订单关闭成功

    public static final String ORDER_ClOSE_FAIL = "EPY1126"; //订单关闭失败


    public static final String PAY_TRAN_TYPE = "OOPY"; //支付

    public static final String REFUND_TRAN_TYPE = "OORF"; //退款

    public static final String REVOKE_TRAN_TYPE = "OORV"; // 撤销


}
