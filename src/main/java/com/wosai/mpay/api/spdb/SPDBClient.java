package com.wosai.mpay.api.spdb;


import com.wosai.mpay.api.spdb.security.SHA1;
import com.wosai.mpay.api.spdb.security.SM2Cryptor;
import com.wosai.mpay.api.spdb.security.SM2Sign;
import com.wosai.mpay.api.spdb.security.SM2SignVerify;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import okhttp3.Headers;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.bind.DatatypeConverter;
import java.util.HashMap;
import java.util.Map;

import static com.wosai.mpay.api.spdb.SPDBRequestFields.X_SPDB_SIGNATURE;
import static com.wosai.mpay.util.HttpClientUtils.BODY_RESULT_FIELD;
import static com.wosai.mpay.util.HttpClientUtils.HEADERS_RESULT_FIELD;

public class SPDBClient {
    public static final Logger logger = LoggerFactory.getLogger(SPDBClient.class);

    private static final String CONTENT_TYPE = "application/json;charset=utf-8";


    private static final String charset = "UTF-8";

    public static final String POST = "post";// post

    public static final String GET = "get";// get


    private int connectTimeout = 10000;
    private int readTimeout = 30000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map<String, Object> call(SPDBRequestBuilder spdbRequestBuilder, String serviceUrl
            , String sqbPrivatekey, String spdbPublicKey, String secret, String method)
            throws Exception {
        String encryptedBody;
        String signature;
        Map<String, String> header;
        String head;
        String body;
        try {
            Map<String, Object> data = spdbRequestBuilder.getBody();
            header = spdbRequestBuilder.getHead();
            body = JsonUtil.objectToJsonString(data);
            head = JsonUtil.objectToJsonString(header);
            encryptedBody = SM2Cryptor.encrypt(secret, body);
            String newBodyData = body;
            byte[] dataBytes = newBodyData.getBytes(charset);
            SM2Sign sign = SM2SignVerify.sign(ByteUtils.fromHexString(sqbPrivatekey), dataBytes);
            byte[] signBytes = sign.getSm2_sign().getBytes(charset);
            signature = DatatypeConverter.printBase64Binary(signBytes);
        } catch (Exception e) {
            throw new MpayException("参数构建异常", e);
        }
        logger.info("request header is {},body is {},encryptedBody is {},signature is {}", head, body, encryptedBody, signature);
        // 将生成的签名放在请求头中
        header.put(X_SPDB_SIGNATURE, signature);
        Map<String, String> response = new HashMap<>();
        Map<String, Object> map = new HashMap<>();
        if (method.equals(POST)) {
            map = HttpClientUtils.doCommonMethod(SPDBClient.class.getName(), null, null, serviceUrl, null, CONTENT_TYPE, encryptedBody, header, "utf-8", connectTimeout, readTimeout, POST);

        } else if (method.equals(GET)) {
            map = HttpClientUtils.doCommonMethod(SPDBClient.class.getName(), null, null, serviceUrl + "?encryptBody=" + encryptedBody, null, CONTENT_TYPE, encryptedBody, header, "utf-8", connectTimeout, readTimeout, GET);
        }
        Headers headerMap = (Headers) (MapUtil.getObject(map, HEADERS_RESULT_FIELD));
        response.put(SPDBProtocolFields.SIGN, headerMap.get("X-SPDB-SIGNATURE"));
        response.put(SPDBProtocolFields.BODY, MapUtil.getString(map, BODY_RESULT_FIELD));
        String decryptBody = getDecryptBody(spdbPublicKey, secret, response);
        return JsonUtil.jsonStrToObject(decryptBody, Map.class);
    }

    private static String getDecryptBody(String spdbPublicKey, String secret, Map<String, String> response) throws MpayApiUnknownResponse {
        String resBody = response.get(SPDBProtocolFields.BODY);
        String decryptBody = SM2Cryptor.decrypt(secret, resBody);
        String sg = response.get(SPDBProtocolFields.SIGN);
        String digest = SHA1.digest(decryptBody);
        byte[] bytes;
        try {
            bytes = digest.getBytes(charset);
        } catch (Exception e) {
            throw new MpayApiUnknownResponse("获取解密后响应体字节数据出错" + response, e);
        }

        byte[] signatureBytes = ByteUtils.fromHexString(new String(DatatypeConverter.parseBase64Binary(sg)));
        SM2Sign sm2Sign = SM2SignVerify.validateSign(ByteUtils.fromHexString(spdbPublicKey), bytes, signatureBytes);

        boolean validateSign = sm2Sign.isVerify();
        logger.info("验签的结果是:{}", validateSign);

        if (!validateSign) {
            throw new MpayApiUnknownResponse("验证签名失败");
        }
        return decryptBody;
    }


}
