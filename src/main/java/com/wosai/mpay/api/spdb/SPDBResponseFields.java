package com.wosai.mpay.api.spdb;

public class SPDBResponseFields {


    public static final String X_SPDB_SIGNATURE = "X-SPDB-SIGNATURE"; //报文签名
    public static final String STATUS_MSG = "statusMsg"; //返回信息

    public static final String STATUS_CODE = "statusCode"; //返回状态码


    public static final String TRANS_NO = "transNo"; //交易流水号


    public static final String IS_SBSCRB_FLG = "isSbscrbFlg"; //是否关注公众账号

    public static final String IS_FLAG = "isFlag"; //是否关注子公众账号

    public static final String PY_BNK_INFO = "pyBnkInfo"; //支付银行

    public static final String TOTAL_AMT = "totalAmt"; //订单总金额

    public static final String CLUE_DTL = "clueDtl"; //营销详情

    public static final String BYR_OF_ALIPAY_ACCT_NO = "byrOfAlipayAcctNO"; //买家支付宝账号
    public static final String ACT_RCV_AMT = "actRcvAmt"; //实收金额
    public static final String BYR_PAY_AMT = "byrPayAmt"; //买家付款的金额

    public static final String US_PNTS_PAY_AMT = "usPntsPayAmt"; //使用积分宝付款的金额

    public static final String TO_US_ESTB_INV_AMT = "toUsEstbInvAmt"; //可给用户开具发票的金额

    public static final String INRCHL_CNL_NO = "inrChlCnlNo"; //交易支付使用的资金渠道

    public static final String ALPAY_CDBAL = "aLPAYCdBal"; //支付宝卡余额
    public static final String MRCH_STR_NM = "mrchStrNm"; //商户门店名称

    public static final String USER_ID = "userId"; //买家在支付宝的用户id

    public static final String ADD_IT_INNAL = "addItInNal"; //单品券优惠的商品优惠信息

    public static final String FLD_DATA = "fldData"; //所有优惠券信息

    public static final String CLRG_DATE = "clrgDate"; //清算日期

    public static final String ORDR_ST = "ordrSt"; //订单状态

    public static final String TRAN_TYPE = "tranType"; //交易类型

    public static final String TRAN_AMT = "tranAmt"; //交易金额

    public static final String THD_PTY_SEQ = "thdPtySeq"; //第三方流水


    public static final String TRAN_ORDR_NO = "tranOrdrNo"; //收单系统订单号


    public static final String TRAN_DATE = "tranDate"; //交易日期

    public static final String QR_CD_LINK = "qRCdLink"; //二维码链接

    public static final String PRA_PAY_CMM_FLG = "praPayCmmFlg"; //预支付交易会话标识

    public static final String MRCH_ID = "mrchId"; //商户号

    public static final String MRCH_TM = "mrchTm"; //商户时间

    public static final String SIGNATURE = "signature"; //签名


    public static final String SGN_DATA = "sgnData"; //签名数据


    public static final String ALTRN_CRC = "aLTrnCrc"; //标价币种


    public static final String PYMT_MD = "pymtMd"; //预授权支付模式


    public static final String BUS_INFO = "busInfo"; //商户传入的业务信息


    public static final String DSCNT_AMNT = "dscntAmnt"; //商家优惠金额


    public static final String APND_PY_MD = "apndPyMd"; //异步支付模式


    public static final String PBLC_ACCT_ID = "pblcAcctId"; //公众账号ID


    public static final String TRD_MER_MECH_NO = "trdMerMechNo"; //第三方主商户号


    public static final String WECHAT_SUB_MECH_NO = "weChatSubMechNo"; //第三方子商户号


    public static final String BUSS_RET_INFO = "bussRetInfo"; //业务结果


    public static final String ERR_CODE = "errCode"; //错误代码


    public static final String ERR_INFO = "errInfo"; //错误信息

    public static final String TRD_CHNL = "trdChnl"; //交易通道


    public static final String BNKNGBSNSS_SEQNO = "bnkngbsnssSeqNo"; //备用字段1(核心流水号)


    public static final String REMARK = "remark"; //备注

    public static final String RETURN_ST_CD = "returnStCd"; //交易返回状态


    public static final String TRAN_DSC = "tranDsc"; //交易描述

    public static final String TOTAL_NUM = "totalNum"; //交易总笔数


    public static final String CORP_TKN_SNAN_INFO = "corpTkNSnanInfo"; //查单请求返回结果列表


    public static final String TRAN_TIME = "tranTime"; // 交易时间


    public static final String SYS_SEQ_NO = "sysSeqNo";//系统流水号


    public static final String CTR_SEQNO = "ctrSeqNo"; //终端流水号


    public static final String AUTHR_CD = "authrCd"; //授权码

    public static final String CARD_NO = "cardNo"; //卡号


    public static final String MRCH_NAME = "mrchName";//商户名称


    public static final String CRD_TYPE = "crdType"; //卡类型

    public static final String SRCH_REFRNO = "srchRefrNo"; //检索参考号


    public static final String ACCPT_BNK_CODE = "accptBnkCode";//受理行

    public static final String TRAN_TP_DSC = "tranTpDsc"; //交易类型描述


    public static final String RVC_YRD_DSC = "rvcYrdDsc";//交易应答码

    public static final String ENDRS_DT_DSC = "endrsStDsc";//应答状态描述

    public static final String TRAN_CHANNEL = "tranChannel";//交易渠道

    public static final String RSRV_FIELD = "rsrvField";//第三方流水号


    public static final String PYMT_ST = "pymtSt";//支付状态

    public static final String ORDR_NO = "ordrNo";//订单号

    public static final String TRAN_TP_CODE = "tranTpCode";//交易类别


    public static final String MRCH_ORDR_NO = "mrchOrdrNo";//商户前台订单号

    public static final String TRANS_AMT = "transAmt";//原交易金额 、现金支付金额

    public static final String ORIG_TRAN_DATE = "origTranDate";//原交易日期

    public static final String OLD_TRAN_SEQ_NO = "oldTranSeqNo";//原交易流水号

    public static final String ORG_ORDR_ST_CD = "orgOrdrStCd";//原订单状态


    public static final String RET_GDS_AMT = "retGdsAmt";//退款金额

    public static final String ORDR_AMT = "ordrAmt";//应结订单金额


    public static final String CASH_RFND_AMT = "cashRfndAmt";//现金退款金额


    public static final String RFD_AMT = "rfdAmt";//代金券退款总金

    public static final String VCHER_NUM = "vcherNum";//退款代金券使用


    public static final String BYROF_ALIPAY_ACCT = "byrOfAlipayAcct";//买家支付宝账号


    public static final String FND_CHNG_FLG = "fndChngFlg";//资金变化标志

    public static final String WTHDRWN_US_AMT_CNL = "wthdrwnUsAmtCnl";//退款使用的资金渠道


    public static final String CCY = "ccy";//币种 01-人民币


    public static final String MRCH_ORIG_ORDR_NO = "mrchOrigOrdrNo";//原收单系统订单号 我行收单系统生成的原支付交易的订单号


    public static final String DCN_RFD_DTL = "dcnRfdDtl";//优惠退款详情 银联返回的优惠退款详情

    public static final String RFND_ACCEPT_TIME = "rfndAcceptTime";//退款受理时间 银联退款受理时间

    public static final String THD_PLTFRM_TM = "thdPltfrmTm";//第三方平台时间 退款支付时间 yyyyMMddHHmmss

    public static final String BYR_RFND_AMNT = "byrRfndAmnt";//买家退款金额 本次退款金额中买家退款金额

    public static final String DSCNT_RFND_AMNT = "dscntRfndAmnt";//平台优惠退款金额 本次退款金额中平台优惠退款金额


    public static final String BUSS_RET_CD = "bussRetCd";//第三方返回码

    public static final String RETURN_INFO = "returnInfo";//第三方返回信息


    public static final String BUSS_BRANCH_ID = "bussBranchId";//营业机构


    public static final String TRAN_TELLER_NO = "tranTellerNo";//交易柜员


    public static final String CORP_RCV_PY_RET_INFO = "corpRcvPyRetInfo";//交易柜员


    public static final String VCHR_TP_CD = "vchrTpCd";//代金券类型 微信退款返回


    public static final String RFND_VCNR_ID = "rfndVchrId";//退款代金券ID 微信退款返回


    public static final String SNGL_VCNR_WTHDRWN = "snglVchrWthdrwn";//单个代金券退款金额

    public static final String FL_DWNLD_NTRLNKG = "flDwnldNtrlnkg";//文件key

    public static final String HTTP_CODE = "httpCode";//状态码

    public static final String HTTP_MESSAGE = "httpMessage";//响应信息

    public static final String MORE_INFORMATION = "moreInformation";//详细信息,仅在HTTP响应状态码非200时返回该字段


    public static final String CHANNEL = "Channel";//交易码

    public static final String ORDRST = "OrdrSt";//订单状态

    public static final String MRCH_NO = "MrchNo";//商户编号

    public static final String TERMINAL_NO2 = "TerminalNo2";//终端号


    public static final String TRANS_AMT4 = "TransAmt4";//交易金额

    public static final String CCY4 = "Ccy4";//币种

    public static final String TRAN_TP1 = "TranTp1";//交易类型


    public static final String MRCH_ORDR_NO3 = "MrchOrdrNo3";//商户订单号

    public static final String ORDR_NO1 = "OrdrNo1";//订单编号


    public static final String CHL_TP = "ChlTp";//推送渠道


    public static final String PYBNK_INFO = "PyBnkInfo";//付款银行信息


    public static final String THDPTY_SEQ = "ThdPtySeq";//第三方流水号


    public static final String BUSS_RETCD = "BussRetCd";//业务返回码

    public static final String RETURNINFO = "ReturnInfo";//返回信息

    public static final String USR_FLG_ID1 = "UsrFlgId1";//用户标识ID

    public static final String IS_SBSCRBFLG = "IsSbscrbFlg";//关注公众账号标志

    public static final String BUSN_PCKT = "BusnPckt";//商家数据包


    public static final String USR_FLG_ID = "usrFlgId";//用户标识


    public static final String USR_CHILD_FLG= "usrChildFlg";//用户子标识



    public static final String BUSH_PCKT = "busnPckt";//商家数据包




    public static final String WTH_DRWN_US_AMT_CNL = "wthdrwnUsAmtCnl";//退款渠道













}
