package com.wosai.mpay.api.spdb.security;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.Security;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;

public class SM2Cryptor {
    public static final String ALGORITHM_NAME = "sm4";
    public static final String ALGORITHM_NAME_ECB_PADDING = "SM4/ECB/PKCS5Padding";

    public SM2Cryptor() {
    }

    public static String sha256(String str) {
        MessageDigest messageDigest = null;
        String enencdeStr = "";

        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            byte[] hash = messageDigest.digest(str.getBytes("UTF-8"));
            enencdeStr = Hex.encodeHexString(hash);
        } catch (Exception var4) {
            var4.printStackTrace();
        }

        return enencdeStr;
    }

    public static String md5(String hash) {
        String md5Str = DigestUtils.md5Hex(hash);
        return md5Str;
    }

    public static String sm3(String data) {
        String charset = "UTF-8";
        String sm3Data = "";

        try {
            byte[] dataBytes = data.getBytes(charset);
            byte[] hashBytes = hash(dataBytes);
            sm3Data = ByteUtils.toHexString(hashBytes);
        } catch (UnsupportedEncodingException var5) {
            var5.printStackTrace();
        }

        return sm3Data;
    }

    public static byte[] hash(byte[] dataBytes) {
        SM3Digest digest = new SM3Digest();
        digest.update(dataBytes, 0, dataBytes.length);
        byte[] hash = new byte[digest.getDigestSize()];
        digest.doFinal(hash, 0);
        return hash;
    }

    public static String encrypt(String key, String data) {
        String encrypted = "";

        try {
            String charset = "UTF-8";
            String sha256Key = sha256(key);
            String sm3Key = sm3(sha256Key);
            String md5Key = md5(sm3Key);
            byte[] keyBytes = ByteUtils.fromHexString(md5Key);
            byte[] dataBytes = data.getBytes(charset);
            Cipher cipher = Cipher.getInstance("SM4/ECB/PKCS5Padding", "BC");
            SecretKeySpec sm4Key = new SecretKeySpec(keyBytes, "sm4");
            cipher.init(1, sm4Key);
            byte[] encryptBytes = cipher.doFinal(dataBytes);
            String hexSignature = ByteUtils.toHexString(encryptBytes).toUpperCase();
            byte[] signBytes = hexSignature.getBytes(charset);
            encrypted = DatatypeConverter.printBase64Binary(signBytes);
            return encrypted;
        } catch (Exception var14) {
            var14.printStackTrace();
            return encrypted;
        }
    }

    public static String decrypt(String key, String encrypted) {
        String decrypted = "";

        try {
            String sha256Key = sha256(key);
            String sm3Key = sm3(sha256Key);
            String md5Key = md5(sm3Key);
            byte[] keyBytes = ByteUtils.fromHexString(md5Key);
            byte[] encryptBytes = DatatypeConverter.parseBase64Binary(encrypted);
            String hexSignature = (new String(encryptBytes)).toLowerCase();
            byte[] cipherBytes = ByteUtils.fromHexString(hexSignature);
            Cipher cipher = Cipher.getInstance("SM4/ECB/PKCS5Padding", "BC");
            SecretKeySpec sm4Key = new SecretKeySpec(keyBytes, "sm4");
            cipher.init(2, sm4Key);
            byte[] doFinal = cipher.doFinal(cipherBytes);
            decrypted = new String(doFinal);
            return decrypted;
        } catch (Exception var13) {
            var13.printStackTrace();
            return decrypted;
        }
    }

    static {
        Security.addProvider(new BouncyCastleProvider());
    }
}
