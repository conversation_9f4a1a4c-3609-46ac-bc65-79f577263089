package com.wosai.mpay.api.spdb.security;

import okhttp3.Headers;

public class ApiResponse {
    private int code;
    private Headers headers;
    private String body;
    private String decryptBody;
    private Boolean verify;

    public ApiResponse() {
    }

    public int getCode() {
        return this.code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public Headers getHeaders() {
        return this.headers;
    }

    public void setHeaders(Headers headers) {
        this.headers = headers;
    }

    public String getBody() {
        return this.body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getDecryptBody() {
        return this.decryptBody;
    }

    public void setDecryptBody(String decryptBody) {
        this.decryptBody = decryptBody;
    }

    public Boolean getVerify() {
        return this.verify;
    }

    public void setVerify(Boolean verify) {
        this.verify = verify;
    }

    public ApiResponse(int code, Headers headers, String body, String decryptBody, Boolean verify) {
        this.code = code;
        this.headers = headers;
        this.body = body;
        this.decryptBody = decryptBody;
        this.verify = verify;
    }
}

