package com.wosai.mpay.api.spdb;

public class SPDBRequestFields {

    public static final String X_SPDB_CLIENT_ID = "X-SPDB-Client-ID"; //应用标识


    public static final String X_SPDB_SIGNATURE = "X-SPDB-SIGNATURE"; //报文签名


    public static final String CONTENT_TYPE = "Content-Type";// 内容类型

    public static final String X_SPDB_SM = "X-SPDB-SM"; //是否用SM算法

    public static final String X_SPDB_ENCRYPTION = "X-SPDB-Encryption"; //是否加密


    public static final String SUB_MECH_NO_ACCT_ID = "subMechNoAcctID"; //子商户公众账号ID

    public static final String BUSN_PCKT = "busnPckt"; //商家数据包


    public static final String CRT_TM = "crtTm"; //交易创建时间


    public static final String APL_END_TM = "aplEndTm"; //交易结束时间


    public static final String ORD_DCB_SIGN = "ordDcnSign"; //订单优惠标记


    public static final String APND_PY_MD = "apndPyMd"; //指定支付方式


    public static final String USR_FLG_ID = "usrFlgId"; //用户标识

    public static final String USR_CHILD_FLG = "usrChildFlg"; //用户子标识

    public static final String LSRO_INFO = "lSroInfo"; //场景信息


    public static final String ORD_TTL = "ordTtl"; //订单标题

    public static final String USER_ID1 = "userId1"; //卖家的支付宝用户id

    public static final String USER_ID2 = "userId2"; //买家的支付宝用户id

    public static final String BYR_OF_ALIPAY_ACCTNO = "byrOfAlipayAcctNO"; //买家支付宝账号

    public static final String AV_DSNT_AMNT = "avlDsntAmnt"; //参与优惠计算的金额


    public static final String NO_PCP_PREF_CLCAMT = "noPcpPrefClcAmt"; //不参与优惠计算的金额


    public static final String STR_ID = "strID"; //支付宝的店铺编号


    public static final String BSN_EXN_PARM = "bsnExnParm"; //业务扩展参数

    public static final String LTS_PY_TM1 = "ltstPyTm1"; //允许的最晚付款时间

    public static final String DSC_CL_ACCT_INFO = "dscClAcctInfo"; //描述分账信息


    public static final String ACT_CNL = "actCnl"; //可用渠道

    public static final String PRHBT_PAY_CNL = "prhbtPayCnl"; //禁用支付渠道

    public static final String EXT_APND_BYR = "extApndByr"; //外部指定买家

    public static final String MRCH_ORIG_ORDR_NO = "mrchOrigOrdrNo"; //商户原始订单号

    public static final String CMDTY_CD = "cmdtyCd"; //商品代码

    public static final String TERMINAL_NO = "terminalNo"; //终端号

    public static final String CMDTY_DSC = "cmdtyDsc"; //商品描述

    public static final String CMDTY_DTL = "cmdtyDtl"; //商品详情

    public static final String TRAN_AMT = "tranAmt"; //交易金额

    public static final String CCY = "ccy"; //币种

    public static final String IP_ADDRESS = "iPAdress"; //终端IP

    public static final String AUTHR_CD = "authrCd"; //授权码

    public static final String MRCHL_INFM_ADR = "mrchlInfmAdr"; //商户通知地址


    public static final String TRAN_TYPE = "tranType"; //交易类型

    public static final String MRCH_ID = "mrchId"; //商户号

    public static final String MRCH_ORDR_NO = "mrchOrdrNo"; //商户订单号

    public static final String MRCH_TM = "mrchTm"; //商户时间
    public static final String STR_NO = "strNo"; //门店编号

    public static final String CSHR_NO = "cshrNo"; //收银员编号

    public static final String CHANNEL_NO = "channelNo"; //渠道号

    public static final String PY_INFO_GTH = "pyInfoGth"; //实名支付

    public static final String PR_AUHR_CTN_MD = "prAuhrCtnMd"; //预授权确认模式

    public static final String COM_IN_PT = "comInPt"; //商户传入终端设备相关信息

    public static final String BUS_INFO = "busInfo"; //商户传入的业务信息

    public static final String IDCFLG = "iDCFlg"; //网联IDC标识

    public static final String INFO_DSC = "infoDsc"; //代扣业务需要传入协议相关信息

    public static final String PRE_AUTHR_NO = "preAuthrNo"; //预授权号

    public static final String SETL_INFO = "setlInfo"; //结算条款

    public static final String LTST_PYTM2 = "ltstPyTm2"; //二维码最晚付款时间

    public static final String UPMCHID = "uPMchId"; //银联转接编号

    public static final String EINVACTN = "eInvActn"; //电子发票功能

    public static final String BUSS_BRANCH_ID = "bussBranchId"; //营业机构

    public static final String TRAN_TELLER_NO = "tranTellerNo"; //交易柜员


    public static final String QUERY_FLAG = "queryFlag"; //查询标识


    public static final String BRANCH_CODE = "branchCode"; //分行号


    public static final String TRAN_STRT_DT = "tranStrtDt"; //交易日期开始日

    public static final String ORDD_DATE_END_DT = "orddDateEndDt"; //交易日期结束日

    public static final String CARD_NO = "cardNo"; //卡号

    public static final String SRCH_REFR_NO = "srchRefrNo"; //检索参考号

    public static final String TRAN_INST = "tranInSt"; //联机交易状态


    public static final String START_SERIAL_NO = "startSerialNo"; //分页查询件数开始序号


    public static final String END_SERIAL_NO = "endSerialNo"; //分页查询件数结束序号

    public static final String TRAN_PWD_SET_CNL = "tranPwdSetCnl"; //扫码交易渠道


    public static final String PYMT_ST = "pymtSt"; //支付状态

    public static final String ORDR_NO = "ordrNo"; //订单号

    public static final String ORIG_TRAN_DATE = "origTranDate"; //原交易日期

    public static final String EBANK_PY_ORDR_NO = "ebankPyOrdrNo"; //原收单系统支付订单号

    public static final String TRAN_ORDR_NO = "tranOrdrNo"; //商户关单交易订单号

    public static final String TRAN_TIME = "tranTime"; //商户交易时间


    public static final String RFND_RLST = "rfndRlst"; //退款原因

    public static final String FUND_SOURCE = "fundSource"; //退款资金来源


    public static final String FRMR_MRCH_ORDR_NO = "frmrMrchOrdrNo"; //原收单系统订单号

    public static final String BUSS_LST_NO = "bussLstNo"; //原商户订单号

    public static final String FRMR_MRCH_DATE = "frmrMrchDate"; //原商户订单号

    public static final String CLRG_DATE = "clrgDate"; //清算日期

    public static final String FILEID = "fileId"; // 下载文件唯一标识码

    public static final String REMARK = "remark"; //备注

    public static final String TRAN_DATE = "tranDate"; //交易日期

    public static final String MRCH_OTDR_NO = "mrchOrdrNo"; //商户订单号

    public static final String MRCH_DATE = "mrchDate"; //商户日期


    public static final String SPDB_NOTIFY = "spdb_notify";


}
