package com.wosai.mpay.api.spdb.security;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.Security;
import java.util.Enumeration;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import org.bouncycastle.asn1.ASN1InputStream;
import org.bouncycastle.asn1.ASN1Integer;
import org.bouncycastle.asn1.ASN1Primitive;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;

public class DigestUtils {
    public static final String ALGORITHM_NAME = "sm4";
    public static final String ALGORITHM_NAME_ECB_PADDING = "SM4/ECB/PKCS5Padding";
    public static String USER_ID;

    public DigestUtils() {
    }

    public static String keyDigest(String algorithm, String content, String charset) {
        try {
            MessageDigest digest = MessageDigest.getInstance(algorithm);
            digest.update(content.getBytes(charset));
            byte[] digestBytes = digest.digest();
            return DatatypeConverter.printHexBinary(digestBytes).toLowerCase();
        } catch (Exception var5) {
            var5.printStackTrace();
            return null;
        }
    }

    public static String dataDigest(String algorithm, String content, String charset) {
        try {
            MessageDigest digest = MessageDigest.getInstance(algorithm);
            digest.update(content.getBytes(charset));
            byte[] digestBytes = digest.digest();
            return DatatypeConverter.printBase64Binary(digestBytes);
        } catch (Exception var5) {
            var5.printStackTrace();
            return null;
        }
    }

    public static String md5Digest(String hash) {
        String md5Str = org.apache.commons.codec.digest.DigestUtils.md5Hex(hash);
        return md5Str;
    }

    public static String sm3(String data) {
        String charset = "UTF-8";
        String sm3Data = "";

        try {
            byte[] dataBytes = data.getBytes(charset);
            byte[] hashBytes = hash(dataBytes);
            sm3Data = ByteUtils.toHexString(hashBytes);
        } catch (UnsupportedEncodingException var5) {
            var5.printStackTrace();
        }

        return sm3Data;
    }

    public static byte[] hash(byte[] dataBytes) {
        SM3Digest digest = new SM3Digest();
        digest.update(dataBytes, 0, dataBytes.length);
        byte[] hash = new byte[digest.getDigestSize()];
        digest.doFinal(hash, 0);
        return hash;
    }

    public static byte[] encrypt(byte[] key, byte[] data) {
        try {
            Cipher cipher = Cipher.getInstance("SM4/ECB/PKCS5Padding", "BC");
            SecretKeySpec sm4Key = new SecretKeySpec(key, "sm4");
            cipher.init(1, sm4Key);
            return cipher.doFinal(data);
        } catch (Exception var4) {
            var4.printStackTrace();
            return null;
        }
    }

    public static byte[] decrypt(byte[] key, byte[] signature) {
        try {
            Cipher cipher = Cipher.getInstance("SM4/ECB/PKCS5Padding", "BC");
            SecretKeySpec sm4Key = new SecretKeySpec(key, "sm4");
            cipher.init(2, sm4Key);
            return cipher.doFinal(signature);
        } catch (Exception var4) {
            var4.printStackTrace();
            return null;
        }
    }

    public static String sign(String key, String data) {
        try {
            String charset = "UTF-8";
            String shaKey = keyDigest("SHA-256", key, charset);
            String sm3Key = sm3(shaKey);
            String sm4Key = md5Digest(sm3Key);
            String sm4Data = sm3(dataDigest("SHA-1", data, charset));
            byte[] keyBytes = ByteUtils.fromHexString(sm4Key);
            byte[] dataBytes = sm4Data.getBytes(charset);
            byte[] encryptBytes = encrypt(keyBytes, dataBytes);
            String hexSignature = ByteUtils.toHexString(encryptBytes).toUpperCase();
            byte[] signBytes = hexSignature.getBytes(charset);
            return DatatypeConverter.printBase64Binary(signBytes);
        } catch (Exception var12) {
            var12.printStackTrace();
            return null;
        }
    }

    public static String fileMD5(String[] filePath) {
        String filesMD5 = "";

        for(int i = 0; i < filePath.length; ++i) {
            try {
                String fileMD5 = org.apache.commons.codec.digest.DigestUtils.md5Hex(new FileInputStream(filePath[i]));
                if (filesMD5 == "") {
                    filesMD5 = fileMD5;
                } else {
                    filesMD5 = filesMD5 + "," + fileMD5;
                }
            } catch (FileNotFoundException var4) {
                var4.printStackTrace();
            } catch (IOException var5) {
                var5.printStackTrace();
            }
        }

        return filesMD5;
    }

    public static String bodyMD5(String requstBody) {
        String bodyMD5 = "";
        bodyMD5 = org.apache.commons.codec.digest.DigestUtils.md5Hex(requstBody);
        return bodyMD5;
    }

    public static SM2Sign validateSign(byte[] publicKey, byte[] sourceData, byte[] signData) {
        try {
            SM2Sign verifyVo = new SM2Sign();
            verifyVo.setSm2_type("verify");
            byte[] formatedPubKey;
            if (publicKey.length == 64) {
                formatedPubKey = new byte[65];
                formatedPubKey[0] = 4;
                System.arraycopy(publicKey, 0, formatedPubKey, 1, publicKey.length);
            } else {
                formatedPubKey = publicKey;
            }

            SM2Factory factory = SM2Factory.getInstance();
            ECPoint userKey = factory.ecc_curve.decodePoint(formatedPubKey);
            SM3Digest sm3Digest = new SM3Digest();
            byte[] z = factory.sm2GetZ(USER_ID.getBytes(), userKey);
            verifyVo.setSm3_z(SM2Util.getHexString(z));
            sm3Digest.update(z, 0, z.length);
            sm3Digest.update(sourceData, 0, sourceData.length);
            byte[] md = new byte[32];
            sm3Digest.doFinal(md, 0);
            verifyVo.setSm3_digest(SM2Util.getHexString(md));
            ByteArrayInputStream bis = new ByteArrayInputStream(signData);
            ASN1InputStream dis = new ASN1InputStream(bis);
            SM2Result sm2Result = null;
            ASN1Primitive derObj = dis.readObject();
            dis.close();
            bis.close();
            Enumeration<ASN1Integer> e = ((ASN1Sequence)derObj).getObjects();
            BigInteger r = ((ASN1Integer)e.nextElement()).getValue();
            BigInteger s = ((ASN1Integer)e.nextElement()).getValue();
            sm2Result = new SM2Result();
            sm2Result.r = r;
            sm2Result.s = s;
            verifyVo.setVerify_r(sm2Result.r.toString(16));
            verifyVo.setVerify_s(sm2Result.s.toString(16));
            factory.sm2Verify(md, userKey, sm2Result.r, sm2Result.s, sm2Result);
            boolean verifyFlag = sm2Result.r.equals(sm2Result.R);
            verifyVo.setVerify(verifyFlag);
            return verifyVo;
        } catch (IllegalArgumentException var18) {
            System.out.println(var18);
            return null;
        } catch (Exception var19) {
            var19.printStackTrace();
            return null;
        }
    }

    static {
        Security.addProvider(new BouncyCastleProvider());
        USER_ID = "1234567812345678";
    }
}
