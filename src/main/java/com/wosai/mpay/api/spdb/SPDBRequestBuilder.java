package com.wosai.mpay.api.spdb;

import java.util.HashMap;
import java.util.Map;


public class SPDBRequestBuilder {

    private Map<String,Object> body;
    private Map<String,String> head;

    public SPDBRequestBuilder(){
        body = new HashMap<>();
        head = new HashMap<>();
    }

    public SPDBRequestBuilder(Map<String, String> head, Map<String, Object> body){
        this.body = body;
        this.head = head;
    }

    public void setBody(String field, Object value) {
        body.put(field,  value);
    }

    public void setHead(String field, String value) {
        head.put(field,  value);
    }

    public Map<String,String> getHead(){

        return head;
    }
    public Map<String,Object> getBody(){
        return body;
    }



}
