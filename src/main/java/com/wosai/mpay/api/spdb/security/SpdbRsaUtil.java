package com.wosai.mpay.api.spdb.security;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.asymmetric.Sign;
import cn.hutool.crypto.asymmetric.SignAlgorithm;

import java.io.UnsupportedEncodingException;

public class SpdbRsaUtil {

    /**
     * 验证签名
     *
     * @param decryptBody   报文明文
     * @param spdbSign      浦发签名，一般放在请求头的"SPDB_PUBLIC_KEY"字段中
     * @param spdbPublicKey 浦发公钥
     * @return 是否验签通过
     * @throws UnsupportedEncodingException
     */
    public static boolean verifySign(String decryptBody, String spdbSign, String spdbPublicKey) throws UnsupportedEncodingException {
        Sign sign = SecureUtil.sign(SignAlgorithm.SHA1withRSA, null, spdbPublicKey);
        boolean verify = sign.verify(decryptBody.getBytes("utf-8"), Base64.decode(spdbSign));
        return verify;
    }

    /**
     * 使用合作方私钥解密报文体
     *
     * @param encryptBody  报文密文
     * @param myPrivateKey 合作方私钥
     * @return 解密后的明文
     */
    public static String decryptStr(String encryptBody, String myPrivateKey) {
        RSA rsa = new RSA(myPrivateKey, null);
        String decryptStr = rsa.decryptStr(encryptBody, KeyType.PrivateKey);
        return decryptStr;
    }
}


