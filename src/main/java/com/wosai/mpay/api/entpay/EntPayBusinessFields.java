package com.wosai.mpay.api.entpay;

import com.google.common.collect.Lists;

import java.util.*;

/***
 * @ClassName: EntPayBusinessFields
 * @Description:
 * @Auther: dabuff
 * @Date: 2024/2/18 5:38 PM
 */
public class EntPayBusinessFields {

    //付款类型
    public static final String PURCHASER_TYPE = "purchaser_type";

    //平台支付单号，数字、字母、下划线、中划线任意组合，平台自定义的唯一编号
    public static final String OUT_PAYMENT_ID = "out_payment_id";

    //微企付支付单号，支付下单接口返回的单号(payment_id)
    public static final String PAYMENT_ID = "payment_id";

    //币种
    public static final String CURRENCY = "currency";

    //过期时间，东八区时间 如:2021-06-08T10:34:56+08:00，不能超过下单时间72小时，超过则自动关单
    public static final String EXPIRE_TIME = "expire_time";

    //支付金额，单位：分
    public static final String AMOUNT = "amount";

    //附言，只支持空格、中文、数字、英文大小写、下划线、全角半角的连字符、逗号和句号
    public static final String MEMO = "memo";

    //订单附加信息，附加数据，在查询API中原样返回，可作为自定义参数使用
    public static final String ATTACHMENT = "attachment";

    //渠道选项，指定收银台支持的付款渠道
    public static final String CHANNEL_OPTIONS = "channel_options";
    /*  channel_options start  */
    //指定银行简称列表 银行简称，指定收银台支持的银行列表，例如平安银行为：PAB
    public static final String INCLUDE_BANK_SNAMES = "include_bank_snames";
    //指定排除银行简称列表  银行简称，指定收银台不支持的银行列表，例如平安银行为：PAB
    public static final String EXCLUDE_BANK_SNAMES = "exclude_bank_snames";
    //指定支付渠道
    public static final String INCLUDE_PAY_CHANNELS = "include_pay_channels";
    //指定排除支付渠道
    public static final String EXCLUDE_PAY_CHANNELS = "exclude_pay_channels";
    /*  channel_options end    */

    //微企付收款方信息
    public static final String PAYEE = "payee";
    /*  payee start  */
    //收款商户企业ID，即商户入驻成功时返回的企业ID
    public static final String ENT_ID = "ent_id";
    //收款商户名称，对应商户入驻申请接口入参的merchant_name
    public static final String ENT_NAME = "ent_name";
    /*  payee end    */

    //付款方信息
    public static final String PAYER = "payer";
    /*  payer start  */
    //当purchaser_type为ENTERPRISE时，该字段必须设置为付款企业名称，当purchaser_type为INDIVIDUAL_NAME时，该字段必须设置为指定个人付款人姓名
    public static final String PAYER_NAME = "payer_name";
    //付款方卡号，当purchaser_type为INDIVIDUAL_CARD时必须设置为个人付款卡号
    public static final String BANK_ACCOUNT_NUMBERS = "bank_account_numbers";
    //平台付款方Id，即合作平台的用户id，付款方在平台的唯一id，只支持英文大小写以及数字
    public static final String OUT_PAYER_ID = "out_payer_id";
    /*  payer end    */

    //商品信息，建议上送不多于5种商品信息
    public static final String GOODS = "goods";
    /*  goods start  */
    //商品名称
    public static final String GOOD_NAME = "good_name";
    //商品数量
    public static final String GOOD_NUMBER = "good_number";
    //商品金额，单位：分
    public static final String GOOD_AMOUNT = "good_amount";
    /*  goods end    */

    //风控信息
    public static final String RISK_CONTROL = "risk_control";
    /*  risk_control start  */
    //设备号，终端设备号（门店号或收银设备ID），示例值：POS1:1
    public static final String DEVICE_ID = "device_id";
    //用户终端IP，用户端实际ip，示例值：***********
    public static final String PAYER_CLIENT_IP = "payer_client_ip";
    //用户UA，浏览器User-Agent
    public static final String PAYER_UA = "payer_ua";
    //下单时间
    public static final String CREATE_TIME = "create_time";
    //提货方式
    public static final String PICK_TYPE = "pick_type";
    //提货描述
    public static final String PICK_DESCRIPTION = "pick_description";
    /*  risk_control end    */

    //支付异步回调信息
    public static final String NOTIFY_URL = "notify_url";
    /*  notify_url start  */
    //后端支付结果通知url，支付结果通知的平台后台回调地址，通知url必须为外网可访问的url，不能携带参数
    public static final String SERVER_NOTIFY_URL = "server_notify_url";
    //前端回跳地址，支付结果通知，包含所有前端类型的通知地址
    public static final String FRONT_CALLBACK_URL = "front_callback_url";
    /*  front_callback_url start  */
    //Mp 支付完成前端回跳小程序路径，路径和参数内容由开发者设置
    public static final String MP_PATH = "mp_path";
    //Mp 支付完成前端回跳小程序appid
    public static final String MP_APPID = "mp_appid";
    //Mp 支付完成前端回跳小程序原始id
    public static final String MP_USERNAME = "mp_username";
    //Mp 支付完成前端回跳小程序urlscheme
    public static final String MP_URLSCHEME = "mp_urlscheme";
    //微信h5页面回跳地址，支付成功后回跳到指定的页面
    public static final String H5_URL = "h5_url";
    //APP-Android 支付完成前端回跳url
    public static final String ANDROID_URL = "android_url";
    //App-IOS 支付完成前端回跳url
    public static final String IOS_URL = "ios_url";
    /*  front_callback_url end    */
    /*  notify_url end    */

    //场景信息
    public static final String SCENE_INFO = "scene_info";
    /*  scene_info start  */
    //门店信息
    public static final String STORE_INFO = "store_info";
    /*  store_info start  */
    //门店信息
    public static final String STORE_INFO_ID = "id";
    //门店信息
    public static final String STORE_INFO_NAME = "name";
    //门店信息
    public static final String STORE_INFO_AREA_CODE = "area_code";
    /*  store_info start  */
    /*  scene_info end    */

    //分账标识
    public static final String PROFIT_ALLOCATION_FLAG = "profit_allocation_flag";

    //关单原因，关单重入需要保持一致
    public static final String CLOSE_REASON = "close_reason";

    //微企付退款单号
    public static final String REFUND_ID = "refund_id";

    //平台退款单号
    public static final String OUT_REFUND_ID = "out_refund_id";

    //原支付订单支付金额，单位：分
    public static final String TOTAL_AMOUNT = "total_amount";

    //退款金额，单位：分
    public static final String REFUND_AMOUNT = "refund_amount";

    //退款原因，用于退款信息展示
    public static final String REFUND_REASON = "refund_reason";

    //待加密字段
    public static final List<String> NEED_ENCRYPT_FIELD = Lists.newArrayList(String.format("%s.%s", PAYEE, ENT_NAME),
            String.format("%s.%s", PAYER, PAYER_NAME),
            String.format("%s.%s", PAYER, BANK_ACCOUNT_NUMBERS));

    //微企付关单接口所允许的字段
    public static final Set<String> CANCEL_ALLOWED_FIELDS = new HashSet<String>(){{
        addAll(Collections.singletonList(CLOSE_REASON));
    }};

    //微企付退款接口所允许的字段
    public static final Set<String> REFUND_ALLOWED_FIELDS = new HashSet<String>(){{
        addAll(Collections.singletonList(REFUND_REASON));
    }};

    //微企付退款接口所允许的字段
    public static final Set<String> PRECREATE_ALLOWED_FIELDS = new HashSet<String>(){{
        addAll(Lists.newArrayList(PURCHASER_TYPE, CHANNEL_OPTIONS, PAYEE, MEMO, ATTACHMENT, RISK_CONTROL, GOODS));
    }};
}
