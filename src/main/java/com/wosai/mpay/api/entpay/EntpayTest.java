package com.wosai.mpay.api.entpay;

import com.wosai.mpay.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLEncoder;
import java.util.*;

/***
 * @ClassName: EntpayTest
 * @Description:
 * @Auther: dabuff
 * @Date: 2024/1/24 4:30 PM
 */
public class EntpayTest {

    public static final Logger logger = LoggerFactory.getLogger(EntpayTest.class);

    public EntpayTest() {

    }

    //平台id (platformId) : sandbox4010020009012200
    //平台私钥序列号（platfromSerialNo）: 37B1B74D5D666D43
    //商企付公钥序列号（tbepSerialNo）: 504F175ACA443AAE
    //商企付公钥（tbepPublicKey）: MIIBCgKCAQEArIgIs9fJKyWhvyalaNqYXA65/HC4CiyUkMZja7dfHpyPenC48QqPjy9lr+hDXtroh5XHJaZ5l//udG679Br9WYNPAM3GDEYbRj+GzGCrtQoBjlLCUat5awLHwH7CXbYthIdT6vYZuYbLioG7PKUL/UPpfpTMm3iToDNdhLZcumItT/6b/r+HIQI3coYgI/qa7bpnkNMCW/HooN18zZNn06f4XLwBluj9aikllfGgqZKleVqsYFoG/peL7v3cClYV8YGA7kvPWj3OW07nwcXletjpCfym6I9xBrqZRQE+XUVPp1qc8t4xHaboFbKmQZQcoZ6sUb1C1BMKQOqlXqqujQIDAQAB
    private static final String platformId = "sandbox4010020009012200";
    private static final String platformSerialNo = "37B1B74D5D666D43";
    private static final String platformPrivateKey = "";
    private static final String tbepSerialNo = "504F175ACA443AAE";
    private static final String tbepPublicKey = "";


//    private static void payTest() throws Exception {
//        // 付款商户卡号
//        List<String> bankAccountNumbers = new ArrayList<>();
//        bankAccountNumbers.add("bankAccountNumber");
//        // 付款方信息
//        PaymentPayer payer = PaymentPayer.builder()
//                .payerName("payerName") // 付款方名称
//                .outPayerId("outPayerId") // 平台付款方Id
//                .bankAccountNumbers(bankAccountNumbers) // 付款商户卡号
//                .build();
//        // 收款方信息
//        PaymentPayee payee = PaymentPayee.builder()
//                .entId("************") // 收款商户Id
//                .entName("微企付联调企业") // 收款商户名称
//                .build();
//        // 商品信息
//        List<Goods> goodsList = new ArrayList<>();
//        for (int i = 0; i < 2; i++) {
//            Goods goods = Goods.builder()
//                    .goodName("goodName" + i) // 商品名称
//                    .goodNumber(1) // 商品数量
//                    .goodAmount(8) // 商品金额
//                    .build();
//            goodsList.add(goods);
//        }
//        FrontCallbackUrl frontCallbackUrl = FrontCallbackUrl.builder()
//                .androidUrl("http://upay-gateway.iwosai.com") // APP-Android 支付完成前端回跳url
//                .iosUrl("http://upay-gateway.iwosai.com") // App-IOS 支付完成前端回跳url
//                .build();
//        // 回调url
//        PaymentNotifyUrl paymentNotifyUrl = PaymentNotifyUrl.builder()
//                .serverNotifyUrl("http://upay-gateway.iwosai.com") // 后端支付结果通知url
//                .frontCallbackUrl(frontCallbackUrl)
//                .build();
//        // 过期时间
//        Date now = new Date();
//        Date expireDate = new Date(now.getTime() + 600000 * 2); //10分钟后的时间
//        PaymentParam paymentParam = PaymentParam.builder()
//                .purchaserType(PurchaserTypeConstant.INDIVIDUAL)
//                .outPaymentId("789520240220002") // 平台支付单号
//                .amount(100L) // 支付金额
//                .currency(CurrencyConstant.CNY) // 币种
//                .expireTime(expireDate)
//                .payee(payee)
//                .payer(payer)
//                .goods(goodsList)
//                .memo("memo") // 订单附加信息
//                .attachment("attachment") // 附言
//                .notifyUrl(paymentNotifyUrl)
//                .build();
//        // 调用支付接口
//        Payment payment = Payment.createPay(paymentParam);
//
//        System.out.println(payment);
//    }


    private static void payTestV2() throws Exception {
        // 付款商户卡号
        List<String> bankAccountNumbers = new ArrayList<>();
        bankAccountNumbers.add("bankAccountNumber");
        // 付款方信息
        Map<String, Object> payer = new HashMap<>();
        payer.put(EntPayBusinessFields.PAYER_NAME, "payerName");
        payer.put(EntPayBusinessFields.OUT_PAYER_ID, "outPayerId");
        payer.put(EntPayBusinessFields.BANK_ACCOUNT_NUMBERS, bankAccountNumbers);

        // 收款方信息
        Map<String, Object> payee = new HashMap<>();
        payee.put(EntPayBusinessFields.ENT_ID, "************");
        payee.put(EntPayBusinessFields.ENT_NAME, "微企付联调企业");

        // 商品信息
        List<Map<String, Object>> goodsList = new ArrayList<>();
        for (int i = 1; i < 3; i++) {
            Map<String, Object> good = new HashMap<>();
            good.put(EntPayBusinessFields.GOOD_NAME, "goodName" + i);
            good.put(EntPayBusinessFields.GOOD_NUMBER, i);
            good.put(EntPayBusinessFields.GOOD_AMOUNT, i);
            goodsList.add(good);
        }


        Map<String, Object> frontCallbackUrl = new HashMap<>();
        frontCallbackUrl.put(EntPayBusinessFields.ANDROID_URL, "http://upay-gateway.iwosai.com");
        frontCallbackUrl.put(EntPayBusinessFields.IOS_URL, "http://upay-gateway.iwosai.com");

        // 回调url
        Map<String, Object> notifyUrl = new HashMap<>();
        notifyUrl.put(EntPayBusinessFields.SERVER_NOTIFY_URL, "http://upay-gateway.iwosai.com");
        notifyUrl.put(EntPayBusinessFields.FRONT_CALLBACK_URL, frontCallbackUrl);

        // 过期时间
        Date now = new Date();
        Date expireDate = new Date(now.getTime() + 600000 * 2); //10分钟后的时间

        EntPayRequestBuilder entPayRequestBuilder = new EntPayRequestBuilder();
        entPayRequestBuilder.set(EntPayBusinessFields.PURCHASER_TYPE, "INDIVIDUAL");
        entPayRequestBuilder.set(EntPayBusinessFields.OUT_PAYMENT_ID, "78952024022000003");
        entPayRequestBuilder.set(EntPayBusinessFields.AMOUNT, 100L);
        entPayRequestBuilder.set(EntPayBusinessFields.CURRENCY, "CNY");
        entPayRequestBuilder.set(EntPayBusinessFields.EXPIRE_TIME, expireDate);
        entPayRequestBuilder.set(EntPayBusinessFields.GOODS, goodsList);
        entPayRequestBuilder.set(EntPayBusinessFields.MEMO, "memo");
        entPayRequestBuilder.set(EntPayBusinessFields.ATTACHMENT, "attachment");
        entPayRequestBuilder.set(EntPayBusinessFields.NOTIFY_URL, notifyUrl);
        entPayRequestBuilder.set(EntPayBusinessFields.PAYEE, payee);
        entPayRequestBuilder.set(EntPayBusinessFields.PAYER, payer);
//
//        Payment.createQrCodePay();
//        Redirect.create();
//        Payment.retrieveByOutPaymentId();
//        Payment.retrieveFee();
//        Payment.close();
//        Refund.create();
//        Refund.retrieveByOutRefundId();
//
//        Payment.createMpPay();
//
//        Payment.createH5Pay();
//
//        Payment.createPay();

        EntPayClient entPayClient = new EntPayClient();

        Map<String, Object> map = entPayClient.call("https://sandbox-api.businesspay.qq.com/v3/mse-pay/payments/app-pay", EntPayClient.METHOD_POST,  entPayRequestBuilder.build(),
                platformId, platformSerialNo, platformPrivateKey, tbepPublicKey, tbepSerialNo);


        System.out.println(JsonUtil.objectToJsonString(map));
    }


    private static void getAppH5() throws Exception {
        String paymentId = "APPTMS000100202402204072785888";

        EntPayClient entPayClient = new EntPayClient();

        String requestUrl = "https://sandbox-api.businesspay.qq.com/v3/mse-pay/redirects";

        EntPayRequestBuilder entPayRequestBuilder = new EntPayRequestBuilder();
        entPayRequestBuilder.set(EntPayBusinessFields.PAYMENT_ID, paymentId);


        Map<String, Object> map = entPayClient.call(requestUrl, EntPayClient.METHOD_POST, entPayRequestBuilder.build(),
                platformId, platformSerialNo, platformPrivateKey, tbepPublicKey, tbepSerialNo);

        System.out.println(map);

    }

    private static void closeTest() throws Exception {

        String paymentId = "APPTMS000100202402204072785888";

        EntPayClient entPayClient = new EntPayClient();

        EntPayRequestBuilder entPayRequestBuilder = new EntPayRequestBuilder();
        entPayRequestBuilder.set(EntPayBusinessFields.PAYMENT_ID, paymentId);
        entPayRequestBuilder.set(EntPayBusinessFields.CLOSE_REASON, "over");

        String requestUrl = String.format("https://sandbox-api.businesspay.qq.com/v3/mse-pay/payments/%s/close", URLEncoder.encode(paymentId, "utf8").replaceAll("\\+", "%20"));

        Map<String, Object> map = entPayClient.call(requestUrl, EntPayClient.METHOD_POST, entPayRequestBuilder.build(),
                platformId, platformSerialNo, platformPrivateKey, tbepPublicKey, tbepSerialNo);


        System.out.println(JsonUtil.objectToJsonString(map));
    }

    private static void queryTest() throws Exception {

        String outPaymentId = "78952024022000003";

        EntPayClient entPayClient = new EntPayClient();

        String requestUrl = String.format("https://sandbox-api.businesspay.qq.com/v3/mse-pay/payments/out-payment-id/%s", outPaymentId);

        Map<String, Object> map = entPayClient.call(requestUrl, EntPayClient.METHOD_GET, null,
                platformId, platformSerialNo, platformPrivateKey, tbepPublicKey, tbepSerialNo);

        System.out.println(JsonUtil.objectToJsonString(map));
    }

//    private static void getQrcode() throws Exception {
//        String paymentId = "APPTMS000100202402204072785888";
//        RedirectParam redirectParam = RedirectParam.builder()
//                .id(paymentId)
//                .build();
//        Redirect link = Redirect.create(redirectParam);
//
//        System.out.println(link);
//
//    }

    public static void main(String[] args) throws Exception {
//        EntpayConfig.setNormalMode(platformId, platformPrivateKey, platformSerialNo, tbepSerialNo, tbepPublicKey);
//        EntpayConfig.setEnv(EnvironmentEnum.SANDBOX);
        try {
//            payTest();
//            payTestV2();
//            getAppH5();
//            closeTest();
            queryTest();
//            getQrcode();

        } catch (Exception e) {
            logger.info("exception", e);
        }
    }
}
