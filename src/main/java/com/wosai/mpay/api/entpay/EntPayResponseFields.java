package com.wosai.mpay.api.entpay;

/***
 * @ClassName: EntPayResponseFields
 * @Description:
 * @Auther: dabuff
 * @Date: 2024/2/18 6:08 PM
 */
public class EntPayResponseFields {

    // 普通错误返回：
    //    {
    //        "error": {
    //              "desc": "系统繁忙，请稍后重试", // 错误描述
    //              "code": "SYSTEM" // 错误类型
    //              "detail": {
    //                  "msg": "错误的详细描述"
    //              }
    //        },
    //        "uri": "/v3/xxx",
    //        "uri_pattern": "/v3/xxxx",
    //        "trace_id": "xxx"
    //    }
    //错误返回
    public static final String ERROR = "error";
    //错误描述
    public static final String ERROR_DESC = "desc";
    //错误类型
    public static final String ERROR_CODE = "code";
    public static final String ERROR_DETAIL = "detail";
    //错误路径
    public static final String ERROR_DETAIL_PATH = "path";
    //错误的详细描述
    public static final String ERROR_DETAIL_MSG = "msg";

    //微企付支付单号
    public static final String PAYMENT_ID = "payment_id";

    //平台支付单号，数字、字母、下划线、中划线任意组合，平台自定义的唯一编号
    public static final String OUT_PAYMENT_ID = "out_payment_id";

    //支付状态
    public static final String PAY_STATUS = "pay_status";

    //支付金额，单位：分
    public static final String AMOUNT = "amount";

    //币种
    public static final String CURRENCY = "currency";

    //微企付收款方信息
    public static final String PAYEE = "payee";
    /*  payee start  */
    //收款商户企业ID，即商户入驻成功时返回的企业ID
    public static final String ENT_ID = "ent_id";
    //收款商户名称，对应商户入驻申请接口入参的merchant_name
    public static final String ENT_NAME = "ent_name";
    /*  payee end    */

    //付款方信息
    public static final String PAYER = "payer";
    /*  payer start  */
    //微企付openId，pay_status为SUCCEEDED且非分享付时返回
    public static final String PAYER_ID = "payer_id";
    //付款银行简称，pay_status为SUCCEEDED时返回，例如平安银行为：PAB
    public static final String PAYER_BANK_SNAME = "payer_bank_sname";
    //付款方付款银行卡后四位
    public static final String PAYER_ACCT_LAST4 = "payer_acct_last4";
    //付款账户类型，pay_status为SUCCEEDED时返回
    public static final String PAYER_ACCT_TYPE = "payer_acct_type";
    //付款银行，pay_status为SUCCEEDED时返回，例如微信服务商为：WXPAY_SERVICE，建设银行信用卡为：CCB_CREDIT
    public static final String PAYER_BANK_TYPE = "payer_bank_type";
    /*  payer end    */

    //附言，只支持空格、中文、数字、英文大小写、下划线、全角半角的连字符、逗号和句号
    public static final String MEMO = "memo";

    //订单附加信息，附加数据，在查询API中原样返回，可作为自定义参数使用
    public static final String ATTACHMENT = "attachment";

    //静态二维码跳转参数 静态二维码链接，合作平台使用该URL独立画码
    public static final String STATIC_QRCODE = "static_qrcode";

    //小程序跳转参数
    public static final String MINI_PROGRAM = "mini_program";
    /*  mini_program start  */
    //小程序路径
    public static final String MP_PATH = "mp_path";
    //小程序appid
    public static final String MP_APPID = "mp_appid";
    //小程序原始id
    public static final String MP_USERNAME = "mp_username";
    //小程序版本
    public static final String MP_VERSION = "mp_version";
    /*  mini_program end    */

    //微信H5 跳转参数 微信h5收银台跳转链接
    public static final String WX_H5 = "wx_h5";

    //APP H5跳转参数 APP内嵌h5收银台跳转链接
    public static final String APP_H5 = "app_h5";

    //支付成功时间，pay_status为SUCCEEDED时返回，格式yyyy-MM-dd HH:mm:ss
    public static final String PAY_TIME = "pay_time";

    //手续费金额，单位：分，订单需支付成功
    public static final String FEE_AMOUNT = "fee_amount";

    //单据状态
    public static final String STATUS = "status";

    //关单成功时间
    public static final String CLOSE_TIME = "close_time";

    //微企付退款单号
    public static final String REFUND_ID = "refund_id";

    //平台退款单号
    public static final String OUT_REFUND_ID = "out_refund_id";

    //退款单创建时间，东八区时间 如:2021-06-08T10:34:56+08:00
    public static final String CREATED_TIME = "created_time";

    //退款原因，用于退款信息展示
    public static final String REFUND_REASON = "refund_reason";

    //退款成功时间，东八区时间 如:2021-06-08T10:34:56+08:00, status=SUCCEEDED时存在
    public static final String SUCCEEDED_TIME = "succeeded_time";

    //回调结果返回码
    public static final String RET_CODE = "retcode";

    //回调结果返回信息
    public static final String RET_MSG = "retmsg";


}
