package com.wosai.mpay.api.entpay;

/***
 * @ClassName: EntPayProtocolFields
 * @Description:
 * @Auther: dabuff
 * @Date: 2024/2/18 5:37 PM
 */
public class EntPayProtocolFields {

    public static final String PLATFORM_ID = "platform_id";                         //平台的平台帐号
    public static final String NONCE = "nonce";                                     //请求随机串
    public static final String TIMESTAMP = "timestamp";                             //时间戳
    public static final String PLATFORM_SERIAL_NUMBER = "platform_serial_number";   //平台的API私钥证书序列号
    public static final String TBEP_SERIAL_NUMBER = "tbep_serial_number";           //商企付公钥序列号
    public static final String SIGNATURE = "signature";                             //签名值
    public static final String SIGNATURE_ALGORITHM = "signature_algorithm";         //签名算法
    public static final String ENC_KEY = "enc_key";                                 //通过腾讯微企付公钥进行RSAES-OAEP(Optimal Asymmetric Encryption Padding)加密后的秘钥值
    public static final String IV = "iv";                                           //CBC加密模式用到的iv
    public static final String ALGORITHM = "algorithm";                             //加密算法
    public static final String TBEP_ENCRYPT = "TBEP-Encrypt";
    public static final String AUTHORIZATION = "Authorization";
    public static final String ENTERPRISE_AUTHORIZATION = "Enterprise-Authorization";
    public static final String CONTENT_TYPE = "Content-Type";
}
