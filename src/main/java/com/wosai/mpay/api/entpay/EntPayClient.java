package com.wosai.mpay.api.entpay;

import cn.hutool.core.bean.BeanUtil;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayClientError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Base64;
import com.wosai.mpay.util.*;
import org.apache.commons.lang.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.PublicKey;
import java.util.*;
import java.util.regex.Pattern;

/***
 * @ClassName: EntPayClient
 * @Description:
 * @Auther: dabuff
 * @Date: 2024/2/19 11:15 AM
 */
public class EntPayClient {

    public static final String METHOD_POST = "POST";
    public static final String METHOD_GET = "GET";
    public static final Logger logger = LoggerFactory.getLogger(EntPayClient.class);
    private static final String CONTENT_TYPE = "application/json;charset=UTF-8";
    private static final Pattern AUTH_PATTERN = Pattern.compile("^((\\w+=\"[^\"]+\",)*)\\w+=\"[^\"]+\"$");
    private static final String TEMPLATE = "%s=\"%s\",";

    private int connectTimeout = 3000;
    private int readTimeout = 15000;

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map<String, Object> call(String requestUrl, String method, Map<String, Object> request, String platformId, String platformPrivateCertSerialNo
            , String platformPrivateKeyStr, String tbepPublicKeyStr, String tbepSerialNumber) throws MpayException, MpayApiNetworkError {

        URL url;
        try {
            url = new URL(requestUrl);
        } catch (MalformedURLException e) {
            throw new MpayException("请求地址格式不正确", e);
        }

        String path = url.getPath();
        String key = RandomStringUtils.randomAlphanumeric(16);
        String iv = RandomStringUtils.randomAlphanumeric(16);
        String body = "";
        boolean hasEncryptedField = false;
        if (METHOD_POST.equals(method)) {
            hasEncryptedField = constructRequestBody(request, key.getBytes(StandardCharsets.UTF_8), iv.getBytes(StandardCharsets.UTF_8));
            body = JsonUtil.objectToJsonString(request);
        }
        String authorization = getAuthorization(method, path, body, platformId, platformPrivateKeyStr, platformPrivateCertSerialNo);
        Map<String, String> headers = buildHttpHeader(hasEncryptedField, key, iv, tbepPublicKeyStr, tbepSerialNumber, authorization);

        logger.info("request {}", body);
        Map<String, Object> result;
        if (METHOD_GET.equals(method)) {
            result = HttpClientUtils.doWechatV3Get(EntPayClient.class.getName(), null, null, requestUrl, headers, StandardCharsets.UTF_8.name(), connectTimeout, readTimeout);
        } else {
            result = HttpClientUtils.doWechatV3Post(EntPayClient.class.getName(), null, null, requestUrl, CONTENT_TYPE, body, headers, StandardCharsets.UTF_8.name(), connectTimeout, readTimeout);
        }

        logger.info("response {}", JsonUtil.objectToJsonString(result));

        return result;
    }

    //请求参数中加密字段处理
    public boolean constructRequestBody(Map<String, Object> param, byte[] key, byte[] iv) {
        boolean hasEncryptedField = false;
        for (String item : EntPayBusinessFields.NEED_ENCRYPT_FIELD) {
            Object value = MapUtils.getNestedProperty(param, item);
            if (Objects.isNull(value)) {
                continue;
            }

            value = encryptRequestBody(value, key, iv);
            BeanUtil.setProperty(param, item, value);
            hasEncryptedField = true;
        }
        return hasEncryptedField;
    }

    public Object encryptRequestBody(Object value, byte[] key, byte[] iv) {
        if (value instanceof String) {
            value = Base64.encode(CipherUtils.encryptSm4Cbc(((String) value).getBytes(StandardCharsets.UTF_8), key, iv));
        } else {
            value = Base64.encode(CipherUtils.encryptSm4Cbc(JsonUtil.toJsonStr(value).getBytes(StandardCharsets.UTF_8), key, iv));
        }

        return value;
    }

    //加签
    private String sign(String method, String path, String body, String nonceStr, String timestamp, String platformPrivateKeyStr) throws MpayException {
        String message = method + "\n" + path + "\n" + timestamp + "\n" + nonceStr + "\n" + body + "\n";
        return RsaSignature.sign(message, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, platformPrivateKeyStr, StandardCharsets.UTF_8.name());
    }

    //获取authorization
    public String getAuthorization(String method, String path, String body, String platformId, String platformPrivateKeyStr, String privateCertSerialNo) throws MpayException {
        String nonceStr = RandomStringUtils.randomAlphanumeric(32);
        String timeStamp = String.valueOf(System.currentTimeMillis() / 1000L);
        String signature = sign(method, path, body, nonceStr, timeStamp, platformPrivateKeyStr);

        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        map.put(EntPayProtocolFields.PLATFORM_ID, platformId);
        map.put(EntPayProtocolFields.NONCE, nonceStr);
        map.put(EntPayProtocolFields.TIMESTAMP, timeStamp);
        map.put(EntPayProtocolFields.PLATFORM_SERIAL_NUMBER, privateCertSerialNo);
        map.put(EntPayProtocolFields.SIGNATURE, signature);
        map.put(EntPayProtocolFields.SIGNATURE_ALGORITHM, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA);
        return appendStr(map);
    }

    //构建请求头
    private Map<String, String> buildHttpHeader(boolean hasEncryptedField, String key, String iv, String tbepPublicKeyStr, String tbepSerialNumber, String authorization) {

        Map<String, String> headers = new HashMap<>();
        headers.put(EntPayProtocolFields.AUTHORIZATION, authorization);
        headers.put(EntPayProtocolFields.CONTENT_TYPE, EntPayConstants.CONTENT_TYPE);
        if (hasEncryptedField) {
            PublicKey tbepPublicKey = CipherUtils.generatePublicKey(tbepPublicKeyStr);
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            map.put(EntPayProtocolFields.ENC_KEY, CipherUtils.rsaEncryptOAEP(key, null, tbepPublicKey));
            map.put(EntPayProtocolFields.IV, Base64.encode(iv.getBytes(StandardCharsets.UTF_8)));
            map.put(EntPayProtocolFields.TBEP_SERIAL_NUMBER, tbepSerialNumber);
            map.put(EntPayProtocolFields.ALGORITHM, RsaSignature.SIG_ALG_NAME_RSA_OAEP_With_SM4_128_CBC);
            String tbepEncrypt = appendStr(map);
            headers.put(EntPayProtocolFields.TBEP_ENCRYPT, tbepEncrypt);
        }

        return headers;
    }

    private String appendStr(LinkedHashMap<String, String> map) {

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            sb.append(String.format(TEMPLATE, entry.getKey(), entry.getValue()));
        }

        return sb.delete(sb.length() - 1, sb.length()).toString();
    }

    //响应报文中相应字段解密处理
    public static String decrypt(String cipherText, String tbepEncryptheader, String platformPrivateKey) throws MpayClientError {

        String key;
        String iv;

        if (!AUTH_PATTERN.matcher(tbepEncryptheader).matches()) {
            throw new MpayClientError("解密协调头部格式不匹配");
        } else {
            Map<String, String> encMap = new HashMap();
            String[] split = tbepEncryptheader.split(",\\s*");
            String[] var4 = split;
            int var5 = split.length;

            for (int var6 = 0; var6 < var5; ++var6) {
                String sp = var4[var6];
                String[] sps = sp.split("=", 2);
                encMap.put(sps[0], sps[1].replaceFirst("^\"", "").replaceFirst("\"$", ""));
            }

            String encKey = encMap.get(EntPayProtocolFields.ENC_KEY);
            iv = encMap.get(EntPayProtocolFields.IV);
            key = CipherUtils.rsaDecryptOAEP(encKey, null, platformPrivateKey.getBytes(StandardCharsets.UTF_8));
        }

        byte[] srcs = Base64.decode(cipherText);
        return new String(CipherUtils.decryptSm4Cbc(srcs, key.getBytes(StandardCharsets.UTF_8), iv.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
    }

}
