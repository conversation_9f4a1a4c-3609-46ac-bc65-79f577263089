package com.wosai.mpay.api.entpay;

/***
 * @ClassName: EntPayConstants
 * @Description:
 * @Auther: dabuff
 * @Date: 2024/2/19 10:00 AM
 */
public class EntPayConstants {

    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_TIME_FORMAT_WITH_T = "yyyy-MM-dd'T'HH:mm:ss";

    public static final String CONTENT_TYPE = "application/json";

    /* pay_status 支付状态 */
    public static final String PROCESSING = "PROCESSING";       //支付处理中
    public static final String BANK_ACCEPTED = "BANK_ACCEPTED"; //提交银行成功
    public static final String SUCCEEDED = "SUCCEEDED";         //支付成功
    public static final String CLEARING = "CLEARING";           //银行清算中（付款渠道为企业网银支付时存在）
    public static final String REVOKED = "REVOKED";             //支付退票（付款渠道为企业网银时存在）
    public static final String CLOSED = "CLOSED";               //支付关单


    /* status 退款状态 */
    public static final String INIT = "INIT";//退款申请
    public static final String ACCEPTED = "ACCEPTED";//退款受理成功
    public static final String REFUND_SUCCEEDED = "SUCCEEDED";//退款成功
    public static final String CANCELLING = "CANCELLING";
    public static final String FAILED = "FAILED";

    /* profit_allocation_flag 分账标识 */
    public static final String NO_PROFIT_ALLOCATION = "NO_PROFIT_ALLOCATION";//无需分账，profit_allocation_flag不传时默认为无需分账
    public static final String API_PROFIT_ALLOCATION = "API_PROFIT_ALLOCATION";//需要分账
}
