package com.wosai.mpay.api.bestpay;

import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.BestpaySignature;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;

public class BestpayClient {
    public  static final Logger logger = LoggerFactory.getLogger(BestpayClient.class);
    private int connectTimeout = 3000;
    private int readTimeout = 15000;
    private static SSLContext sslContext = null;
    private static HostnameVerifier hostnameVerifier = null;

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public static  SSLContext  getSSLContext() throws MpayException{
    	if(null == sslContext){
	        try{
	        	sslContext = SSLContext.getInstance("SSL");  
	        	sslContext.init(null, new TrustManager[]{new X509TrustManager(){
					@Override
					public void checkClientTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
					}
	
					@Override
					public void checkServerTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
					}
	
					@Override
					public X509Certificate[] getAcceptedIssuers() {
						return new X509Certificate[]{};  
					}
		        	
		        }}, new java.security.SecureRandom()); 
	        }catch (Exception e) {
				throw new MpayException("获取证书失败", e);
			}
    	}
        return sslContext;
    }
    
    public static  HostnameVerifier  getHostnameVerifier() throws MpayException{
    	if(null == hostnameVerifier){
    		hostnameVerifier = new HostnameVerifier(){
    			public boolean verify(String arg0, SSLSession arg1) {
    				return true;
    			}
            };
    	}
        return hostnameVerifier;
    }
    
    /**
     * 根据不同的接口调用返回需要参与签名的字段
     *
     * @param payMethod
     * @return
     */
    public static List<String> getSignatureColumns(int payMethod) {
        List<String> signColumns = Arrays.asList();
        switch (payMethod) {
            case BestpayConstants.METHOD_BSC_PAY:
                signColumns = BusinessFields.BSC_PAY_SIGNATURE_PARAMS;
                break;
            case BestpayConstants.METHOD_WAP_PAY:
                signColumns = BusinessFields.WAP_PAY_SIGNATURE_PARAMS;
                break;
            case BestpayConstants.METHOD_WAP_H5_PAY:
                signColumns = BusinessFields.H5_PAY_SIGNATURE_PARAMS;
                break;
            case BestpayConstants.METHOD_CANCEL:
                signColumns = BusinessFields.BSC_CANCEL_SIGNATURE_PARAMS;
                break;
            case BestpayConstants.METHOD_QUERY:
                signColumns = BusinessFields.QUERY_SIGNATURE_PARAMS;
                break;
            case BestpayConstants.METHOD_REFUND:
                signColumns = BusinessFields.REFUND_SIGNATURE_PARAMS;
                break;
        }
        return signColumns;
    }

    public Map<String,Object> call(String serviceUrl,String signKey, int payMethod,  Map<String,String> request) throws MpayException, MpayApiNetworkError {
        request.remove(ProtocolFields.MAC);
        request.put(ProtocolFields.MAC, BestpaySignature.getSign(request, signKey, getSignatureColumns(payMethod)));
        if(BestpayConstants.METHOD_WAP_PAY == payMethod){
       	 	request.remove(ProtocolFields.MAC.toUpperCase());
       	 	request.put(ProtocolFields.MAC.toUpperCase(), request.get(ProtocolFields.MAC));
       	 	request.remove(ProtocolFields.MAC);
        }
        logger.debug("request {}", request);
//        String response = WebUtils.doPost(getSSLContext(), getHostnameVerifier(), serviceUrl, request, BestpayConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        String response = HttpClientUtils.doPost(BestpayClient.class.getName(), getSSLContext(), getHostnameVerifier(), serviceUrl, request, BestpayConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        logger.debug("response {}",response.replaceAll("\\n", ""));//微信返回的xml报文，有换行，打印日志时，打印在一行上面。
        Map<String, Object> result = JsonUtil.jsonStrToObject(response, Map.class);
        if(null == result && response.indexOf("&") >= 0){
        	result = new HashMap<String, Object>();
        	String[] responses = response.split("&");
        	result.put(ResponseFields.SUCCESS, responses[0]);
        	result.put(ResponseFields.MESSAGE, responses[1]);
        }
        return result;
    }
    
    public static void main(String[] args) throws MpayApiNetworkError, MpayException {
		testQuery();
	}
    
    public static void testQuery() throws MpayApiNetworkError, MpayException{
    	BestpayClient client = new BestpayClient();
    	RequestBuilder builder = new RequestBuilder();
    	builder.set(BusinessFields.MERCHANT_ID, "01440109025345000");
        builder.set(BusinessFields.ORDER_NO, "7895251766646401");
        builder.set(BusinessFields.ORDER_REQ_NO, "7895251766646401");
        builder.set(BusinessFields.ORDER_DATE, "20180928");
        Map<String, Object> result = client.call("https://webpaywg.bestpay.com.cn/query/queryOrder", "23CB477D755315A3AE8D5E5559A41CAD93D85C807D295351", BestpayConstants.METHOD_QUERY, builder.build());
        System.out.println(result);
    }
}
