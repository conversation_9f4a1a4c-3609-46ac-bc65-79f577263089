package com.wosai.mpay.api.bestpay;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.RsaSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.io.UnsupportedEncodingException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;

/**
 * 翼支付，使用RSA签名
 *
 * <AUTHOR>
 * @date 2020/04/29
 */
public class BestpayV2Client {

    public static final Logger logger = LoggerFactory.getLogger(BestpayV2Client.class);
    private int connectTimeout = 3000;
    private int readTimeout = 15000;
    private static SSLContext sslContext = null;
    private static HostnameVerifier hostnameVerifier = null;
    private static final ObjectMapper om = new ObjectMapper();

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public static SSLContext getSSLContext() throws MpayException {
        if (null == sslContext) {
            try {
                sslContext = SSLContext.getInstance("SSL");
                sslContext.init(null, new TrustManager[]{new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
                    }

                    @Override
                    public void checkServerTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
                    }

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[]{};
                    }

                }}, new java.security.SecureRandom());
            } catch (Exception e) {
                throw new MpayException("获取证书失败", e);
            }
        }
        return sslContext;
    }

    public static HostnameVerifier getHostnameVerifier() throws MpayException {
        if (null == hostnameVerifier) {
            hostnameVerifier = new HostnameVerifier() {
                public boolean verify(String arg0, SSLSession arg1) {
                    return true;
                }
            };
        }
        return hostnameVerifier;
    }

    /**
     * 发起请求
     *
     * @param serviceUrl
     * @param signKey
     * @param request
     * @return
     * @throws MpayException
     * @throws MpayApiNetworkError
     * @throws JsonProcessingException
     * @throws UnsupportedEncodingException
     */
    public Map<String, Object> call(String serviceUrl, String signKey, Map<String, String> request) throws MpayException, MpayApiNetworkError, JsonProcessingException {
        //RSA签名并添加到请求参数中
        request.put(ProtocolV2Fields.SIGN, RsaSignature.sign(request, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, signKey));
        logger.debug("request {}", request);
        String postParams = om.writeValueAsString(request);
        //使用 "application/json" contentType发起请求
        String response = HttpClientUtils.doPost(BestpayV2Client.class.getName(), getSSLContext(), getHostnameVerifier(), serviceUrl, "application/json", postParams, BestpayConstants.CHARSET_UTF8, connectTimeout, readTimeout);
        logger.debug("response {}", response);//微信返回的xml报文，有换行，打印日志时，打印在一行上面。
        Map<String, Object> result = JsonUtil.jsonStrToObject(response, Map.class);
        return result;
    }

    public static void main(String[] args) throws MpayApiNetworkError, MpayException, JsonProcessingException {
         testPay();
//         testQuery();
//         testRefund();
//         testRefundQuery();
    }

    public static void testPay() throws MpayApiNetworkError, MpayException, JsonProcessingException {
        BestpayV2Client client = new BestpayV2Client();
        RequestBuilder builder = new RequestBuilder();

        builder.set("merchantNo", "3178002070426323");
        builder.set("outTradeNo", "20170919148520128231133");
        builder.set("tradeAmt", "1");
        builder.set("subject", "subject");
        builder.set("authCode", "510692963021588836");
        builder.set("ccy", "156");
        builder.set("requestDate", "2020-05-20 11:12:12");
        builder.set("operator", "operator");
        builder.set("storeCode", "2");
        builder.set("tradeChannel", "APP");
        builder.set("accessCode", "EXTERNAL_PAYMENT");

        String signKey = "";
        Map<String, Object> result = client.call("http://mapi-test.bestpay.cn/mapi/uniformReceipt/barCodePay", signKey, builder.build());

        System.out.println(result);
    }

    public static void testQuery() throws MpayApiNetworkError, MpayException, JsonProcessingException {
        BestpayV2Client client = new BestpayV2Client();
        RequestBuilder builder = new RequestBuilder();

        builder.set("merchantNo", "3178002070426323");
        builder.set("outTradeNo", "789425924974577201");
        builder.set("tradeDate", "2020-05-13 11:12:12");

        String signKey = "";

        Map<String, Object> result = client.call("http://mapi-test.bestpay.cn/mapi/uniformReceipt/tradeQuery ", signKey, builder.build());
        System.out.println(result);
    }

    public static void testRefund() throws MpayApiNetworkError, MpayException, JsonProcessingException {
        BestpayV2Client client = new BestpayV2Client();
        RequestBuilder builder = new RequestBuilder();

        builder.set("outRequestNo", "20200507100000210002100256878208");
        builder.set("merchantNo", "3178002070426323");
        builder.set("outTradeNo", "20170919148520128231133");
        builder.set("refundAmt", "1");
        builder.set("requestDate", "2020-05-20 15:19:12");
        builder.set("ccy", "156");
        builder.set("originalTradeDate", "2020-05-20 12:12:12");
        builder.set("operator", "operator");
        builder.set("tradeChannel", "APP");
        builder.set("accessCode", "EXTERNAL_PAYMENT");

        String signKey = "";
        Map<String, Object> result = client.call("http://mapi-test.bestpay.cn/mapi/uniformReceipt/tradeRefund", signKey, builder.build());
        System.out.println(result);
    }

    public static void testRefundQuery() throws MpayApiNetworkError, MpayException, JsonProcessingException {
        BestpayV2Client client = new BestpayV2Client();
        RequestBuilder builder = new RequestBuilder();

        builder.set("merchantNo", "3178002070426323");
        builder.set("outTradeNo", "789425924974577201");
        builder.set("outRequestNo","7894259249745070");
        builder.set("refundDate", "2020-05-00 12:12:12");


        String signKey = "";
        Map<String, Object> result = client.call("http://mapi-test.bestpay.cn/mapi/uniformReceipt/refundOrderQuery ", signKey, builder.build());

        System.out.println(result);
    }

}
