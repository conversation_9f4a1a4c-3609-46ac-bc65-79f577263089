package com.wosai.mpay.api.bestpay;

/**
 * 翼支付3.0版本的请求fields
 *
 * <AUTHOR>
 * @date 2020/04/29
 */
public class BusinessV2Fields {

    public static final String INSTITUTION_TYPE = "institutionType";             // 机构类型
    public static final String INSTITUTION_CODE = "institutionCode";             // 机构号

    public static final String MERCHANT_NO = "merchantNo";						// 商户号
    public static final String OUT_TRADE_NO = "outTradeNo";						// 订单号
    public static final String TRADE_AMT = "tradeAmt";							// 订单总金额,单位为分
    public static final String SUBJECT = "subject";                             // 订单标题（商品名称）
    public static final String AUTH_CODE = "authCode";							// 条形码号
    public static final String CCY = "ccy";                                     // 币种
    public static final String REQUEST_DATE = "requestDate";                    // 发送请求的时间
    public static final String TRADE_CHANNEL = "tradeChannel";                  // 交易渠道
    public static final String ACCESS_CODE = "accessCode";                      // 接入方标识码,固定传：EXTERNAL_PAYMENT
    public static final String OPERATOR = "operator";                           // 操作人,发起交易操作人,如：商户号或者手机号码
    public static final String STORE_CODE = "storeCode";                        // 商户门店编号
    public static final String OUT_REQUEST_NO = "outRequestNo";                 // 退款流水号
    public static final String REFUND_AMT = "refundAmt";                        // 退款金额
    public static final String ORIGINAL_TRADE_DATE = "originalTradeDate";       // 原交易日期
    public static final String TRADE_DATE = "tradeDate";                        // 交易日期
    public static final String REFUND_DATE = "refundDate";                      // 退款请求日期

    public static final String LEDGER_ACCOUNT = "ledgerAccount";                // 分账信息
    public static final String NOTIFY_URL = "notifyUrl";                        // 回调地址
    public static final String STORE_NAME = "storeName";                        // 门店名称
    public static final String TERMINAL_CODE = "terminalCode";                  // 终端号
    public static final String GOODS_INFO = "goodsInfo";                        // 商品信息摘要
    public static final String MEMO = "memo";                                   // 备注
    public static final String TIME_OUT = "timeOut";                            // 支付超时时间
    public static final String GOODS_DETAILS = "goodsDetails";                  // 商品详情列表
    public static final String RISK_CONTROL_INFO = "riskControlInfo";           // 风控参数
    public static final String REFUND_CAUSE = "refundCause";                    // 退款原因
    public static final String REMARK = "remark";                               // 备注

}
