package com.wosai.mpay.api.bestpay;

import java.util.Arrays;
import java.util.List;

public class BestpayConstants {
	/** UTF-8字符集 **/
    public static final String CHARSET_UTF8     = "UTF-8";
    public static String  DATE_TIME_FORMAT = "yyyyMMddHHmmss";
    public static String  DATE_TIME_FORMAT2 = "yyyy-MM-dd HH:mm:ss";
    public static String  DATE_TIME_FORMAT_LONG = "yyyy-MM-dd'T'HH:mm:ss.SSS";

    public static final String KEY_RSA = "RSA";
    public static final String KEY_MD5 = "MD5";

    /**
     * 各种接口的简易编号
     */
    public static final int METHOD_BSC_PAY = 1;
    public static final int METHOD_WAP_PAY = 2;
    public static final int METHOD_WAP_H5_PAY = 3;
    public static final int METHOD_CANCEL = 4;
    public static final int METHOD_QUERY = 5;
    public static final int METHOD_REFUND = 6;

    public static final String ERROR_CODE_BARCODE_VALID_1 = "BARCODE_VALID";
    public static final String ERROR_CODE_BARCODE_VALID_ERROR = "BARCODE_VALIDATE_ERROR";
    public static final String ERROR_CODE_REMOTE_SERVICE_INVOKE_FAIL = "REMOTE_SERVICE_INVOKE_FAIL"; // 条码已过期
    
    public static final String TRANS_STATUS_INPROG = "A";		// 支付中
    public static final String TRANS_STATUS_TRADE = "B";		// 支付成功
    public static final String TRANS_STATUS_FAIL = "C";			// 支付失败

	public static final String CHANNEL = "05";
	public static final String BUSI_TYPE = "0000001";
	
	public static final String TRANS_CODE_01 = "01"; // 收单类交易，默认填01；
	public static final String PRODUCT_ID_04 = "04"; // 收单类交易，默认填04；
	public static final String ENCODE_TYPE_1 = "1"; // 加密方式：默认为1
    
	public static final String ERROR_CODE_REFUNDED = "BE301016"; // 已经退款
	public static final String ERROR_CODE_DUPLICATE_CANCEL = "BE301012"; // 重复取消订单
	public static final String ERROR_CODE_ORDER_NOT_EXISTED = "BE110078"; // 原交易不存在或超过退款限制时间
	public static final String ERROR_CODE_ORDER_NOT_EXISTED_2 = "BE110062"; // 订单号不存在
	public static final String RESPONSE_CODE_BALANCE_NOT_ENOUGH = "BALANCE_NOT_ENOUGH";// 余额不足

    //v2版本返回值
    public static final String ACCESS_CODE = "EXTERNAL_PAYMENT";
    public static final String TRADE_CHANNEL = "APP";
    public static final String TRANSACTION_CURRENCY_CODE = "156"; //交易币种，固定为156

    public static final String TRANS_STATUS_INPROG_V2 = "WAITFORPAY";   //等待支付
    public static final String TRANS_STATUS_TRADE_V2 = "SUCCESS";	    // 支付成功
    public static final String TRANS_STATUS_FAIL_V2 = "FAIL";			// 支付失败

    public static final String RESULT_ERROR_CODE_SYSTEM_ERROR = "SYSTEM_INNER_ERROR";                           // 系统内部错误
    public static final String RESULT_ERROR_CODE_PUBLIC_INTERNAL_ERROR = "PUBLIC_INTERNAL_ERROR";               // 业务请求失败请稍后重试
    public static final String RESULT_ERROR_CODE_PARAM_ERROR = "PARAMETER_VALID_NOT_PASS";                      // 请求参数有误
    public static final String RESULT_ERROR_CODE_REMOTE_BARCODE_FAILED = "REMOTE_BARCODE_FAILED";               // 条码解析失败,（条码过期)
    public static final String RESULT_ERROR_CODE_TRANS_AMOUNT_CANNOT_BE_ZERO = "TRANS_AMOUNT_CANNOT_BE_ZERO";   // 交易金额不能小于等于0
    public static final String RESULT_ERROR_CODE_DUIPLICAT_ORDER_ERROR = "DUIPLICAT_ORDER_ERROR";               // 重复下单
    public static final String RESULT_ERROR_CODE_CUSTOMER_NOT_FOUND = "CUSTOMER_NOT_FOUND";                     // 客户未找到
    public static final String RESULT_ERROR_CODE_MERCHANT_PAY_SELF = "MERCHANT_PAY_SELF";                       // 不能与自己签约的店铺进行交易
    public static final String RESULT_ERROR_CODE_REMOTE_ERROR_EXCEPTION = "REMOTE_ERROR_EXCEPTION";             // 调用远程服务返回异常
    public static final String RESULT_ERROR_CODE_CUSTOMER_OFFLINE_TRADE_FAIL = "CUSTOMER_OFFLINE_TRADE_FAIL";   // 用户处于离线状态下，现有交易额度不足，请提示用户刷新付款码或更换交易方式
    public static final String RESULT_ERROR_CODE_ORDER_FAIL = "ORDER_FAIL";                                     // 下单失败
    public static final String RESULT_ERROR_CODE_CALL_DUBBO_TIMEOUT = "CALL_DUBBO_TIMEOUT";                     // 网络超时
    public static final String RESULT_ERROR_CODE_REFUND_ALREADY = "REFUND_ALREADY";                             // 订单已退款成功，不允许重复发起
    public static final String RESULT_ERROR_CODE_ORDER_NO_EXIST = "ORDER_NO_EXIST";                             // 订单不存在
    public static final String RESULT_ERROR_CODE_ORDER_NOT_EXIST_ERROR = "ORDER_NOT_EXIST_ERROR";               // 订单信息不存在
    public static final String RESULT_ERROR_CODE_REFUND_AMT_LIMIT = "REFUND_AMT_LIMIT";                         // 退款金额大于原订单金额
    public static final String RESULT_ERROR_CODE_DATABASE_QUERY_EMPTY = "DATABASE_QUERY_EMPTY";                 // 无退款记录
    public static final String RESULT_ERROR_CODE_BAL_CANNOT_NEGATIVE = "BAL_CANNOT_NEGATIVE";                   // 退款账户余额不足


    public static final String REFUND_TRUE = "TRUE"; //已退款
    public static final String REFUND_FALSE = "FALSE"; //未退款
    public static final String REFUND_SECTION = "SECTION"; //部分退款

    //接口返回错误列表 支付结果失败
    public static final List<String> BEST_PAY_RESULT_ERROR_CODE_FAIL_LIST = Arrays.asList(
            RESULT_ERROR_CODE_PARAM_ERROR, RESULT_ERROR_CODE_REMOTE_BARCODE_FAILED, RESULT_ERROR_CODE_TRANS_AMOUNT_CANNOT_BE_ZERO,
            RESULT_ERROR_CODE_DUIPLICAT_ORDER_ERROR, RESULT_ERROR_CODE_CUSTOMER_NOT_FOUND, RESULT_ERROR_CODE_MERCHANT_PAY_SELF,
            RESULT_ERROR_CODE_CUSTOMER_OFFLINE_TRADE_FAIL, RESULT_ERROR_CODE_ORDER_FAIL, RESULT_ERROR_CODE_PUBLIC_INTERNAL_ERROR
    );

    //接口返回错误列表，退款查询结果失败
    public static final List<String> BEST_PAY_RESULT_ERROR_CODE_REFUND_FAIL_LIST = Arrays.asList(
            RESULT_ERROR_CODE_PARAM_ERROR,RESULT_ERROR_CODE_TRANS_AMOUNT_CANNOT_BE_ZERO,RESULT_ERROR_CODE_CUSTOMER_NOT_FOUND,RESULT_ERROR_CODE_ORDER_NOT_EXIST_ERROR,
            RESULT_ERROR_CODE_REFUND_ALREADY,RESULT_ERROR_CODE_REFUND_AMT_LIMIT,RESULT_ERROR_CODE_ORDER_NO_EXIST,RESULT_ERROR_CODE_DATABASE_QUERY_EMPTY);

    //接口返回错误列表，支付结果未知
    public static final List<String> BEST_PAY_RESULT_ERROR_CODE_UNKNOWN_LIST = Arrays.asList(
            RESULT_ERROR_CODE_SYSTEM_ERROR, RESULT_ERROR_CODE_REMOTE_ERROR_EXCEPTION, RESULT_ERROR_CODE_CALL_DUBBO_TIMEOUT
    );


}
