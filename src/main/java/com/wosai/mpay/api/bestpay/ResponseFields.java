package com.wosai.mpay.api.bestpay;

public class ResponseFields {

    public static final String SUCCESS = "success";				// 是否成功
    public static final String MESSAGE = "message";				// 接口返回信息
    public static final String MERCHANT_ID = "merchantId";		// 商户号
    public static final String ORDER_NO = "orderNo";			// 订单号
    public static final String ORDER_REQ_NO = "orderReqNo";		// 订单请求流水号
    public static final String ORDER_DATE = "orderDate";		// 预留字段
    public static final String OUR_TRANS_NO = "ourTransNo";		// 翼支付交易号
    public static final String TRANS_AMT = "transAmt";			// 订单金额
    public static final String TRANS_STATUS = "transStatus";	// 交易状态
    public static final String ENCODE_TYPE = "encodeType";		// 签名方式
    public static final String SIGN = "sign";					// sign校验域
    public static final String COUPON = "coupon";				// 优惠金额
    public static final String SC_VALUE = "scValue";			// 商户营销优惠成本
    public static final String PAYER_ACCOUNT = "payerAccount";	// 付款人账号
    public static final String PAY_CHANNEL = "payChannel";		// 付款 明细
    public static final String PRODUCT_DESC = "productDesc";	// 备注
    public static final String REFUND_FLAG = "refundFlag";		// 退款标示
    public static final String CUSTOMER_ID = "customerId";		// 客户 登陆账号
    public static final String MCHNT_TM_NUM = "mchntTmNum";		// 商户自定义终端号
    public static final String DEVICE_TM_NUM = "deviceTmNum";	// 设备终端号
    public static final String ATTACH = "attach";				// 附加信息
    public static final String TRANS_PHONE = "transPhone";		// 交易手机号
    public static final String ERROR_CODE = "errorCode";		// 错误码
    public static final String ERROR_MSG = "errorMsg";			// 错误描述
	public static final String RESULT = "result";				// 返回结果
	public static final String RESP_CODE = "respCode";              // 返回结果
	public static final String RESP_DESC = "respDesc";              // 返回结果描述

	
	public static final String RESPONSE_KEY_TRANS_STATUS = String.format("%s.%s", ResponseFields.RESULT, ResponseFields.TRANS_STATUS);

}
