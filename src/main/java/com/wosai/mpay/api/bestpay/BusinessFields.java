package com.wosai.mpay.api.bestpay;

import java.util.Arrays;
import java.util.List;

public class BusinessFields {

    public static final String MERCHANT_ID = "merchantId";						// 商户号
    public static final String SUB_MERCHANT_ID = "subMerchantId";				// 子商户号
    public static final String BARCODE = "barcode";								// 条形码号
    public static final String ORDER_NO = "orderNo";							// 订单号
    public static final String ORDER_REQ_NO = "orderReqNo";						// 订单请求交易流水号
    public static final String CHANNEL = "channel";								// 渠道
    public static final String BUSI_TYPE = "busiType";							// 业务类型
    public static final String ORDER_DATE = "orderDate";						// 订单日期
    public static final String ORDER_AMT = "orderAmt";							// 订单总金额
    public static final String PRODUCT_AMT = "productAmt";						// 产品金额
    public static final String ATTACH_AMT = "attachAmt";						// 附加金额
    public static final String GOODS_NAME = "goodsName";						// 商品名称
    public static final String STORE_ID = "storeId";							// 门店号
    public static final String BACK_URL = "backUrl";							// 后台返回地址
    public static final String LEDGER_DETAIL  = "ledgerDetail";					// 分账信息
    public static final String ATTACH = "attach";								// 附加信息
    public static final String MAC = "mac";										// MAC校验域
    public static final String MCHNT_TM_NUM = "mchntTmNum";						// 商户自定义终端号
    public static final String DEVICE_TM_NUM = "deviceTmNum";					// 设备终端号
    public static final String ERP_NO = "erpNo";								// 商户营业员编号
    public static final String GOODS_DETAIL = "goodsDetail";					// 商品详情
    public static final String MERCHANT_PWD = "merchantPwd";					// 商户调用密码
    public static final String OLD_ORDER_NO = "oldOrderNo";						// 原扣款订单号
    public static final String OLD_ORDER_REQ_NO = "oldOrderReqNo";				// 原订单请求支付流水号
    public static final String REFUND_REQ_NO = "refundReqNo";					// 退款流水号
    public static final String REFUND_REQ_DATE = "refundReqDate";				// 退款请求日期
    public static final String TRANS_AMT = "transAmt";							// 退款交易金额
    public static final String ORDER_SEQ ="ORDERSEQ";							// 订单号
    public static final String ORDER_REQ_TRANSEQ ="ORDERREQTRANSEQ";			// 订单请求流水号
    public static final String ORDER_REQ_TIME ="ORDERREQTIME";					// 订单请求时间 格式：yyyyMMDDhhmmss
    public static final String TRANS_CODE ="TRANSCODE";							// 交易代码
    public static final String PRODUCT_ID ="PRODUCTID";							// 商品代码
    public static final String PRODUCT_DESC ="PRODUCTDESC";						// 商品描述
    public static final String ENCODE_TYPE ="ENCODETYPE";						// MAC字段的加密方式
    public static final String BG_URL ="BGURL";									// 后台通知地址
    public static final String SERVICE ="SERVICE";								// 接口名称
    public static final String BACK_MERCHANT_URL = "BACKMERCHANTURL";			// 支付结果后台通知地址(必须填写和下单中BGURL填写一致)
    public static final String SIGN_TYPE = "SIGNTYPE";							// 签名方式：MD5、RSA、CA
    public static final String ORDER_TIME = "ORDERTIME";						// 订单请求时间格式yyyyMMddHHmmss
    public static final String ORDER_AMOUNT = "ORDERAMOUNT";					// 订单金额／积分扣减（单位：元，保留小数点后两位）
    public static final String CURTYPE = "CURTYPE";								// 币种（默认填 RMB ）
    public static final String PRODUCT_AMOUNT = "PRODUCTAMOUNT";				// 产品金额
    public static final String ATTACH_AMOUNT = "ATTACHAMOUNT";					// 附加金额单位0.01元
    public static final String ACCOUNT_ID = "ACCOUNTID";						// 翼支付账户号
    public static final String CUSTOMER_ID = "CUSTOMERID";						// 用户手机号
    public static final String SUBJECT = "SUBJECT";								// 商品描述
    public static final String OTHER_FLOW = "OTHERFLOW";						// 默认填写01
    public static final String ORDER_VALIDITY_TIME = "ORDERVALIDITYTIME";		// 订单有效时间
    public static final String SWTICH_ACC = "SWTICHACC";						// 是否可切换登录的翼支付账号。true表示可切换账号；false表示不可切换账
    public static final String UPTRANSEQ = "UPTRANSEQ";							// 翼支付网关平台交易流水号
    
    
	public static final List<String> BSC_PAY_SIGNATURE_PARAMS = Arrays.asList(MERCHANT_ID, ORDER_NO, ORDER_REQ_NO, ORDER_DATE, BARCODE, ORDER_AMT);
	public static final List<String> WAP_PAY_SIGNATURE_PARAMS = Arrays.asList(MERCHANT_ID.toUpperCase(), ORDER_SEQ, ORDER_REQ_TRANSEQ, ORDER_REQ_TIME);
	public static final List<String> H5_PAY_SIGNATURE_PARAMS = Arrays.asList(SERVICE, MERCHANT_ID.toUpperCase(), MERCHANT_PWD.toUpperCase(), SUB_MERCHANT_ID.toUpperCase(), BACK_MERCHANT_URL, ORDER_SEQ, ORDER_REQ_TRANSEQ, ORDER_TIME, ORDER_VALIDITY_TIME, CURTYPE, ORDER_AMOUNT, SUBJECT, PRODUCT_ID, PRODUCT_DESC,CUSTOMER_ID, SWTICH_ACC);
	public static final List<String> BSC_CANCEL_SIGNATURE_PARAMS = Arrays.asList(MERCHANT_ID, MERCHANT_PWD, OLD_ORDER_NO, OLD_ORDER_REQ_NO, REFUND_REQ_NO, REFUND_REQ_DATE, TRANS_AMT);
	public static final List<String> QUERY_SIGNATURE_PARAMS = Arrays.asList(MERCHANT_ID, ORDER_NO, ORDER_REQ_NO, ORDER_DATE);
	public static final List<String> REFUND_SIGNATURE_PARAMS = Arrays.asList(MERCHANT_ID, MERCHANT_PWD, OLD_ORDER_NO, OLD_ORDER_REQ_NO, REFUND_REQ_NO, REFUND_REQ_DATE, TRANS_AMT, LEDGER_DETAIL);


}
