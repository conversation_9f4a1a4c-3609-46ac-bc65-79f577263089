package com.wosai.mpay.api.bestpay;

/**
 * 翼支付3.0版本的应答fields
 *
 * <AUTHOR>
 * @date 2020/04/29
 */
public class ResponseV2Fields {

    public static final String SUCCESS = "success";				            // 是否成功
    public static final String RESULT = "result";				            // 应答对象
    public static final String ERROR_CODE = "errorCode";		            // 错误码
    public static final String ERROR_MSG = "errorMsg";			            // 错误描述

    public static final String OUT_TRADE_NO = "outTradeNo";		            // 外部交易流水
    public static final String TRADE_NO = "TradeNo";			            // 交易订单号
    public static final String TRADE_AMT = "tradeAmt";			            // 订单总金额,单位为分
    public static final String TRADE_PROD_NO = "tradeprodNo";	            // 收单订单号
    public static final String MERCHANT_NO = "merchantNo";				    // 商户号
    public static final String BUYER_LOGIN_NO = "buyerLoginNo";			    // 买家账号
    public static final String PAY_AMT = "payAmt";						    // 支付金额
    public static final String DISCOUNT_AMT = "disCountAmt";				// 优惠金额
    public static final String TRADE_FINISHED_DATE = "tradeFinishedDate";	// 交易完成时间
    public static final String REFUND_DATE = "refundDate";                  // 退款请求日期
    public static final String REFUND_AMT = "refundAmt";                    // 退款金额
    public static final String TRADE_STATUS = "tradeStatus";                // 退款状态
    public static final String MERCHANT_NAME = "merchantName";              // 商户名称
    public static final String REFUND_FLAG = "refundFlag";                  // 退款标识
    public static final String CLOSE_FLAG = "closeFlag";                    // 交易结束标识
    public static final String PAY_FINISHED_DATE = "payFinishedDate";       // 支付完成时间
    public static final String PAY_RESULT_CODE = "payResultCode";           // 支付结果码
    public static final String PAY_RESULT_DESC = "payResultDesc";           // 支付结果明细
    public static final String SUBJECT = "Subject";                         // 订单标题

    public static final String RESPONSE_KEY_TRADE_STATUS = String.format("%s.%s", ResponseV2Fields.RESULT, ResponseV2Fields.TRADE_STATUS);
}
