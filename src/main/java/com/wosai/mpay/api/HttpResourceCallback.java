package com.wosai.mpay.api;

import com.wosai.middleware.carrier.CarrierItem;
import com.wosai.mpay.util.TracingUtil;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 01/03/2018.
 */
public interface HttpResourceCallback<T> {

    /**
     * Called if the request was successful.
     *
     * @param result the result object of the http response (possibly null)
     */
    void onComplete(T result);

    /**
     * Called if there was an error in the request.
     *
     * @param t
     */
    void onError(Throwable t);


    static <T> HttpResourceCallback<T> create(HttpResourceCallbackFunction<T> resourceFinished){
        return create(null, resourceFinished);
    }

    /**
     *
     * @param carrierItem
     * @param resourceFinished
     * @param <T>
     * @return
     */
    static <T> HttpResourceCallback<T> create(CarrierItem carrierItem, HttpResourceCallbackFunction<T> resourceFinished){
        return new HttpResourceCallback<T>() {
            @Override
            public void onComplete(T t) {
                TracingUtil.storeThreadLocalTraceInfo(carrierItem);
                try{
                    resourceFinished.apply(t, null);
                }catch (Throwable throwable){
                    onError(throwable);
                }
            }

            @Override
            public void onError(Throwable throwable) {
                TracingUtil.storeThreadLocalTraceInfo(carrierItem);
                resourceFinished.apply(null, throwable);
            }
        };
    }


}
