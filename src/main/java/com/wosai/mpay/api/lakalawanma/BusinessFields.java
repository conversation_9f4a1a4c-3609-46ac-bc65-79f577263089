package com.wosai.mpay.api.lakalawanma;

public class BusinessFields {

    public static final String RECE_ORG_NO = "rece_org_no";// 受理机构编号
    public static final String TECH_ORG_NO = "tech_org_no";//技术服务方
    public static final String VERNO = "verno";//版本号
    public static final String MERCHANT_ID = "merchant_id";//万码商户编号
    public static final String DEVICE_INFO = "device_info";//设备号
    public static final String BODY = "body";//商品描述
    public static final String DETAIL = "detail";//商品详情
    public static final String ATTACH = "attach";//附加数据
    public static final String MERCHANT_TRADE_NO = "merchant_trade_no";//商户订单号
    public static final String TRADE_NO = "trade_no";//万码商户订单号
    public static final String TOTAL_FEE = "total_fee";//总金额
    public static final String FEE_TYPE = "fee_type";//货币类型
    public static final String SPBILL_CREATE_IP = "spbill_create_ip";//终端IP
    public static final String GOODS_TAG = "goods_tag";//订单优惠标记
    public static final String AUTH_CODE = "auth_code";//授权码
    public static final String NONCE_STR = "nonce_str";//随机字符串
    public static final String SIGN_TYPE = "sign_type";//签名类型
    public static final String SIGN = "sign";//签名
    public static final String LIMIT_PAY = "limit_pay"; // 指定支付方式
    public static final String MERCHANT_REFUND_NO = "merchant_refund_no"; //商户退款单号 
    public static final String REFUND_FEE = "refund_fee"; // 申请退款金额 
    public static final String OP_USER_ID = "op_user_id"; // 操作员
    public static final String TRADE_TYPE = "trade_type"; // 交易类型 
    public static final String SUB_APPID = "sub_appid"; // 子商户应用ID 
    public static final String SUB_OPENID = "sub_openid"; // 用户子标识 
    public static final String SCENE = "scene"; // 支付场景 
    public static final String SUBJECT = "subject"; // 订单标题
    public static final String TOTAL_AMOUNT = "total_amount"; //订单总金额
    public static final String OPERATER_ID = "operater_id"; //操作员编号
    public static final String NOTIFY_URL = "notify_url"; //通知地址
    public static final String BUYER_ID = "buyer_id"; //通知地址
    
    public static final String REFUND_AMOUNT = "refund_amount"; // 申请退款金额
    public static final String WECHAT_TRADE_NO = "wechat_trade_no";		// 微信支付订单号
    public static final String RECE_MERCHANT_ID = "rece_merchant_id";	// 受理机构端商户号
    public static final String OUT_MERCHANT_ID = "out_merchant_id";		// 外部商户编号
    public static final String MERCHANT_NAME = "merchant_name";			// 商户名称
    public static final String MERCHANT_SHORTNAME = "merchant_shortname";	// 商户简称
    public static final String SERVICE_PHONE = "service_phone"; 		// 客服电话
    public static final String BUSINESS = "business";					// 经营类目
    public static final String CONTACT_NAME = "contact_name";			// 联系人
    public static final String CONTACT_PHONE = "contact_phone";			// 联系电话
    public static final String CONTACT_EMAIL = "contact_email";			// 联系邮箱
    
    // 微信进件个性化信息
    public static final String CHANNEL_ID = "channel_id";				// 渠道号
    public static final String CONTACT_WECHATID_TYPE = "contact_wechatid_type";	// 联系人微信帐号
    public static final String CONTACT_WECHATID = "contact_wechatid";	// 商户备注
    public static final String MERCHANT_REMARK = "merchant_remark";		// 随机字符串
    
    // 支付宝进件个性化信息
    public static final String SOURCE = "source"; 						// 来源机构标志
    public static final String BUSINESS_LICENSE = "business_license";	// 商户证件编码
    public static final String BUSINESS_LICENSE_TYPE = "business_license_type";	// 商户证件类型
    public static final String CONTACT_ID_NO = "contact_id_no";			// 身份证号
    public static final String PROVINCE_CODE = "province_code";			// 商户所在省
    public static final String CITY_CODE = "city_code";					// 商户所在市
    public static final String DISTRICT_CODE = "district_code";			// 商户所在区县
    public static final String ADDRESS = "address";						// 商户详细地址
    public static final String LONGITUDE = "longitude";					// 经度
    public static final String LATITUDE = "latitude";					// 纬度
    public static final String ADD_TYPE = "add_type";					// 地址类型
    public static final String CARD_NO = "card_no";						// 地址类型
    public static final String CARD_NAME = "card_name";					// 持卡人姓名
    
    public static final String RETURN_CODE = "return_code";				// 返回状态码
    public static final String RETURN_MSG = "return_msg";				// 返回信息
    public static final String SUB_MERCHANT_ID = "sub_merchant_id";		// 微信商户号
    public static final String RESULT_CODE = "result_code";				// 返回状态码
    
    public static final String MILLIONBEZRA_ID = "millionbezraId";				// 万码系统子商户号
    
    public static final String SUBSCRIBE_APPID = "subscribe_appid";		// 商户推荐关注公众账号APPID
    public static final String JSAPI_PATH = "jsapi_path";				// 商户公众账号JSAPI支付授权目录
    public static final String BANK_TYPE = "bank_type";					// 付款银行
    public static final String CASH_FEE = "cash_fee";					// 现金支付金额
    public static final String END_DATE = "end_date";					// 支付完成日期
    public static final String END_TIME = "end_time";					// 支付完成时间
    public static final String OPENID = "openid";						// 用户标识
    
    public static final String BUS_TYPE = "bus_type";					// 用户标识

    public static final String START_DATE = "start_date";				// 交易起始日期
    public static final String START_TIME = "start_time";				// 交易起始时间
    public static final String EXPIRE_DATE = "expire_date";				// 交易结束日期
    public static final String EXPIRE_TIME = "expire_time";				// 交易结束时间
    public static final String PREPAY_ID = "prepay_id";					// 预支付交易会话标识
    public static final String PAY_INFO = "pay_info";					// 支付信息
    public static final String CODE_URL = "code_url";					// 二维码链接
    public static final String WECHAT_REFUND_NO = "wechat_refund_no";	// 微信退款订单号
    
    public static final String ALIPAY_TRADE_NO = "alipay_trade_no";		// 支付宝交易号
    public static final String RECEIPT_AMOUNT = "receipt_amount";		// 实收金额
    
    
    public static final String ALIPAY_REFUND_NO = "alipay_refund_no";	// 支付宝退款订单号
    public static final String FUND_CHANGE = "fund_change";				// 本次退款是否发生了资金变化
    
    public static final String O_END_DATE = "o_end_date";				// 订单支付结束时间
    
    public static final String REQ_DATE = "req_date";					// 请求日期
    public static final String REQ_TIME = "req_time";					// 请求时间
    public static final String FILE_TYPE = "file_type";					// 文件类型
    public static final String FILE_NAME = "file_name";					// 文件名
    public static final String BACK_URL = "back_url";					// 结果文件通知地址
}

