package com.wosai.mpay.api.lakalawanma;

public class ResponseFields {

    public static final String RETURN_CODE = "return_code";// 返回状态码
    public static final String RETURN_MSG = "return_msg";// 返回信息
    public static final String RECE_ORG_NO = "rece_org_no"; // 受理机构编号
    public static final String TECH_ORG_NO = "tech_org_no"; // 技术服务方
    public static final String VERNO = "verno"; // 版本号
    public static final String MERCHANT_ID = "merchant_id"; // 万码商户编号
    public static final String DEVICE_INFO = "device_info"; // 设备号
    public static final String RESULT_CODE = "result_code"; // 业务结果
    public static final String ERR_CODE = "err_code"; // 错误代码
    public static final String ERR_CODE_DES = "err_code_des"; // 错误代码描述
    public static final String TRADE_TYPE ="trade_type"; // 交易类型	
    public static final String BANK_TYPE = "bank_type"; // 付款银行	
    public static final String FEE_TYPE = "fee_type"; // 标价币种
    public static final String TOTAL_FEE = "total_fee"; // 标价金额
    public static final String CASH_FEE_TYPE = "cash_fee_type"; // 现金支付币种
    public static final String CASH_FEE = "cash_fee"; // 现金支付金额
    public static final String SETTLEMENT_TOTAL_FEE = "settlement_total_fee"; // 应结订单金额
    public static final String COUPON_FEE = "coupon_fee"; // 代金券金额
    public static final String WECHAT_TRADE_NO = "wechat_trade_no"; // 微信订单号
    public static final String TRADE_NO = "trade_no"; // 万码系统订单号
    public static final String MERCHANT_TRADE_NO = "merchant_trade_no"; // 商户订单号
    public static final String ATTACH = "attach"; // 商家数据包
    public static final String END_DATE = "end_date"; // 交易完成日期
    public static final String END_TIME = "end_time"; // 交易完成时间
    public static final String START_DATE = "start_date"; // 交易起始日期
    public static final String START_TIME = "Start_time"; // 交易起始时间
    public static final String EXPIRE_DATE = "Expire_date"; // 交易结束日期
    public static final String EXPIRE_TIME = "Expire_time"; // 交易结束时间
    public static final String GOODS_TAG = "goods_tag"; // 订单优惠标记
    public static final String NOTIFY_URL = "notify_url"; // 通知地址
    public static final String PRODUCT_ID = "product_id"; // 商品ID 
    public static final String LIMIT_PAY = "limit_pay"; // 指定支付方式
    public static final String PREPAY_ID = "prepay_id"; // 预支付交易会话标识
    public static final String CODE_URL = "code_url"; // 二维码链接
    public static final String SUB_APPID = "sub_appid"; // 用户子商户
    public static final String SUB_OPENID = "sub_openid"; //用户子标识
    public static final String PAY_INFO = "pay_info"; // 支付信息
    public static final String BUS_TYPE = "bus_type"; // 原支付交易类型
    public static final String REFUND_FEE = "refund_fee"; // 申请退款金额 
    public static final String REFUND_FEE_TYPE = "refund_fee_type"; // 货币种类
    public static final String OP_USER_ID = "op_user_id"; // 操作员
    public static final String REFUND_ACCOUNT = "refund_account"; // 退款资金来源
    public static final String SETTLEMENT_REFUND_FEE = "settlement_refund_fee"; // 退款金额
    public static final String COUPON_REFUND_FEE = "coupon_refund_fee"; // 代金券退款总金额
    public static final String COUPON_REFUND_COUNT = "coupon_refund_count"; // 退款代金券使用数量
    public static final String COUPON_TYPE_ = "coupon_type_"; // CASH--充值代金券 NO_CASH ---非充值代金券订单使用代金券时有返回（取值：CASH、NO_CASH）。$n为下标,从0开始编号，举例：coupon_type_0
    public static final String COUPON_REFUND_NO_ = "coupon_refund_no_"; // 退款代金券ID
    public static final String COUPON_REFUND_FEE_ = "coupon_refund_fee_"; // 单个代金券退款金额
    public static final String OPENID = "openid"; // 用户标识
    public static final String IS_SUBSCRIBE = "is_subscribe"; // 是否关注公众账号
    public static final String SUB_IS_SUBSCRIBE = "sub_is_subscribe"; // 是否关注子公众账号
    public static final String TRADE_STATUS = "trade_status"; // 交易状态
    public static final String TRADE_STATE = "trade_state"; // 交易状态
    public static final String TRADE_STATE_DESC = "trade_state_desc"; // 交易状态描述
    public static final String MERCHANT_REFUND_NO = "merchant_refund_no"; //商户退款单号
    public static final String REFUND_NO = "refund_no"; // 万码系统退款单号
    public static final String WECHAT_REFUND_NO = "wechat_refund_no"; // 微信退款订单号
    public static final String WECHAT_REFUND_NO_ = "wechat_refund_no_"; // 微信退款单号
    public static final String REFUND_NO_ = "refund_no_"; // 万码退款单号
    public static final String MERCHANT_REFUND_NO_ = "merchant_refund_no_"; // 商户退款单号
    public static final String REFUND_COUNT = "refund_count"; // 退款笔数
    public static final String REFUND_CHANNEL_ = "refund_channel_"; //退款渠道
    public static final String REFUND_FEE_ = "refund_fee_"; //申请退款金额
    public static final String SETTLEMENT_REFUND_FEE_$ = "settlement_refund_fee_"; //退款金额
    
    public static final String SCENE = "scene"; // 支付场景
    public static final String AUTH_CODE = "auth_code"; // 支付授权码
    public static final String PRODUCT_CODE = "product_code"; //  销售产品码
    public static final String SUBJECT = "subject"; // 订单标题
    public static final String BUYER_ID = "buyer_id"; // 买家的支付宝用户id
    public static final String SELLER_ID = "seller_id"; // 商户签约账号对应的支付宝用户ID
    public static final String TOTAL_AMOUNT = "total_amount"; // 订单总金额
    public static final String DISCOUNTABLE_AMOUNT = "discountable_amount"; // 参与优惠计算的金额
    public static final String UNDISCOUNTABLE_AMOUNT = "undiscountable_amount"; // 不参与优惠计算的金额
    public static final String BODY = "body"; // 订单描述
    public static final String OPERATOR_ID = "operator_id"; // 商户操作员编号
    public static final String STORE_ID = "store_id"; // 商户门店编号
    public static final String TERMINAL_ID = "terminal_id"; // 终端编号
    public static final String ALIPAY_STORE_ID = "alipay_store_id"; // 支付宝的店铺编号
    public static final String DISABLE_PAY_CHANNELS = "disable_pay_channels"; // 商户编号
    public static final String ALIPAY_TRADE_NO = "alipay_trade_no"; // 支付宝交易号
    public static final String BUYER_LOGON_ID = "buyer_logon_id"; // 买家支付宝账号
    public static final String RECEIPT_AMOUNT = "receipt_amount"; // 实收金额
    public static final String BUYER_PAY_AMOUNT = "buyer_pay_amount"; // 买家付款的金额
    public static final String POINT_AMOUNT = "point_amount"; // 使用积分宝付款的金额
    public static final String INVOICE_AMOUNT = "invoice_amount"; // 交易中可给用户开具发票的金额
    public static final String FUND_CHANNEL = "fund_channel"; // 交易使用的资金渠道
    public static final String AMOUNT = "amount"; // 该支付工具类型所使用的金额
	public static final String REAL_AMOUNT = "real_amount"; // 渠道实际付款金额
	public static final String CARD_BALANCE = "card_balance"; // 支付宝卡余额
	public static final String STORE_NAME = "store_name"; // 发生支付交易的商户门店名称
	public static final String BUYER_USER_ID = "buyer_user_id"; // 买家在支付宝的用户id
	public static final String DISCOUNT_GOODS_DETAIL = "discount_goods_detail"; // 商品优惠信息
	public static final String VOUCHER_DETAIL_LIST = "voucher_detail_list"; // 
	public static final String VOUCHER_ID = "id";// 券id
	public static final String VOUCHER_NAME = "name";// 券名称
	public static final String VOUCHER_TYPE = "type";// 券类型
	public static final String VOUCHER_AMOUNT = "amount";// 券面额
	public static final String VOUCHER_MERCHANT_CONTRIBUTE = "merchant_contribute";// 券商家出资
	public static final String VOUCHER_OTHER_CONTRIBUTE = "other_contribute";// 券其他出资
	public static final String VOUCHER_MEMO = "memo";// 优惠券信息
	public static final String ALIPAY_GOODS_ID = "alipay_goods_id";// 支付宝定义的统一商品编号
	public static final String GOODS_NAME = "goods_name";// 商品名称
	public static final String QUANTITY = "quantity";// 商品数量
	public static final String PRICE = "price";// 商品单价，单位为元
	public static final String GOODS_CATEGORY = "goods_category";// 商品类目
	public static final String SHOW_URL = "show_url";// 商品的展示地址
	public static final String PERATOR_ID = "perator_id";// 商户操作员编号
	public static final String HB_FQ_NUM = "hb_fq_num";// 使用花呗分期要进行的分期数
	public static final String HB_FQ_SELLER_PERC = "hb_fq_seller_perc";// 花呗分期
	public static final String QR_CODE = "qr_code";// 当前预下单请求生成的二维码码串
	public static final String OUT_REQUEST_NO = "out_request_no";// 退款申请流水
	public static final String INDUSTRY_SEPC_DETAIL = "industry_sepc_detail";//行业特殊信息
	public static final String RETRY_FLAG = "retry_flag"; // 是否重试
	public static final String ACTION = "action"; // 交易动作
	
}
