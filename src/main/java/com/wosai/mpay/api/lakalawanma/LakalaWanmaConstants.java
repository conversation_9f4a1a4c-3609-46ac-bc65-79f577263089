package com.wosai.mpay.api.lakalawanma;

/**
 * Created by maoyu
 */
public class LakalaWanmaConstants {

    public static final String FUNCOD_BSC = "8001";//B扫C对应的功能码,固定为 8001
    public static final String FUNCOD_BSC_CANCEL = "8002";//B扫C,撤单
    public static final String FUNCOD_CSB = "8011";//C扫B,或者门店码
    public static final String FUNCOD_CSB_CANCEL = "8012";//C扫B撤单
    public static final String FUNCOD_QUERY = "8021";//订单查询接口
    public static final String FUNCOD_QUERY_POSITIVE = "8022";//B扫C、C扫B的查询场景。已在用户端生成成功电子凭证，根据原订单号进行查询。
    public static final String FUNCOD_REFUND = "8031";//退款申请
    public static final String FUNCOD_REFUND_QUERY = "8032";//退款查询

    public static final String CHARSET_GBK = "GBK";
    public static String DATE_TIME_FORMAT = "yyyyMMddHHmmss";
    /**
     * Date默认时区
     **/
    public static final String DATE_TIMEZONE = "GMT+8";
    /**
     * sign type
     **/
    public static String SIGN_TYPE_MD5 = "MD5";
    /**
     * 交易类型
     **/
    public static final String TRADE_TYPE_JSAPI = "JSAPI";//公众号支付
    public static final String TRADE_TYPE_NATIVE = "NATIVE";//原生扫码支付
    public static final String TRADE_TYPE_APP = "APP";//app支付

    public static final String TRADE_STATUS_SUCC = "SUCCESS";//本次操作成功
    public static final String TRADE_STATE_USERPAYING = "0100C0";//支付进行中
    public static final String REQUEST_LOG_NO_DUPLICATE = "100015";//请求流水号重复

    /**
     * 支付渠道类型
     **/
    public static final String PAY_WAY_WEIXIN = "WECHAT";
    public static final String PAY_WAY_ALIPAY = "ALIPAY";
    public static final String PAY_WAY_BAIDU = "BAIDUPAY";
    public static final String PAY_WAY_LKLPAY = "LKLPAY";//拉卡拉钱包
    public static final String PAY_WAY_UNIONPAY = "UNIONPAY";//银联钱包
    public static final String PAY_WAY_JDPAY = "JDPAY";//京东钱包
    public static final String PAY_WAY_CCBLZF = "CCBLZF";//建行龙支付钱包
    public static final String PAY_WAY_UQRCODEPAY = "UQRCODEPAY";//银联二维码支付


    public static final String CANCEL_REQ_TYPE_USER_CANCEL = "01";//用户主动撤销
    public static final String CANCEL_REQ_TYPE_POS_TIMEOUT = "02";//POS轮询超时
    
    public static final String RETURN_CODE_SUCCESS = "SUCCESS";
    public static final String RESULT_CODE_SUCCESS = "SUCCESS";

    /**
     * 各种接口的简易编号
     */
    public static final int METHOD_BSC_PAY = 1;
    public static final int METHOD_BSC_CANCEL = 2;
    public static final int METHOD_CSB_PAY = 3;
    public static final int METHOD_CSB_CANCEL = 4;
    public static final int METHOD_QUERY = 5;
    public static final int METHOD_POSITIVE_QUERY = 6;
    public static final int METHOD_REFUND = 7;
    public static final int METHOD_REFUND_QUERY = 8;
    
    public static final String ERR_CODE_SUCCESS  		= "CNL00000";    //SUCCESS 
    public static final String ERR_CODE_UNKNOWN 	 	= "CNL00002";    //结果未知                                                                     
    public static final String ERR_CODE_PAY_IN_PROG  	= "CNL00003";    //等待用户付款，或用户支付中                                                   
    public static final String ERR_CODE_PAY_FAIL  		= "CNL00004";    //支付失败                                                                     
    public static final String ERR_CODE_ORDER_CLOSED  	= "CNL00005";    //订单已关闭                                                                   
    public static final String ERR_CODE_ORDER_CANCELED  = "CNL00006";    //订单已撤销                                                                   
    public static final String ERR_CODE_ORDER_NOT_PAY  	= "CNL00007";    //订单未支付                                                                   
    public static final String ERR_CODE_ORDER_FAIL_OPERATING  = "CNL00008";    //订单完成，不可撤销，不可退款                                                 
    public static final String ERR_CODE_ORDER_ALREADY_REFUND  = "CNL00009";    //已发生退款                                                                   
    public static final String ERR_CODE_ORDER_ALREADY_PAY  	  = "CNL00010";    //已支付，不可关单                                                             
    public static final String ERR_CODE_ORDER_NO_REPEAT  	  = "CNL00011";    //同一个订单不可在另外一个钱包机构重复支付，请使用新的订单号                   
    public static final String ERR_CODE_ORDER_STATUS_UNKNOWN  = "CNL00012";    //订单未知状态，请通过查询获取结果，不可重复支付                               
    public static final String ERR_CODE_CNL00013  = "CNL00013";    //万码订单号不匹配                                                             
    public static final String ERR_CODE_CNL00098  = "CNL00098";    //验签失败                                                                     
    public static final String ERR_CODE_CNL00099  = "CNL00099";    //系统错误，请稍后再试                                                         
    public static final String ERR_CODE_CNL10000  = "CNL10000";    //登记数据库失败                                                               
    public static final String ERR_CODE_CNL10001  = "CNL10001";    //订单状态不允许撤销                                                           
    public static final String ERR_CODE_CNL10002  = "CNL10002";    //查询数据库异常                                                               
    public static final String ERR_CODE_CNL20000  = "CNL20000";    //受理机构不存在                                                               
    public static final String ERR_CODE_CNL20001  = "CNL20001";    //受理机构已关闭                                                               
    public static final String ERR_CODE_CNL20002  = "CNL20002";    //技术服务方不存在                                                             
    public static final String ERR_CODE_CNL20003  = "CNL20003";    //技术服务方不可用                                                             
    public static final String ERR_CODE_CNL20004  = "CNL20004";    //商户未报备                                                                   
    public static final String ERR_CODE_CNL20005  = "CNL20005";    //受理机构未配置该费率                                                         
    public static final String ERR_CODE_CNL20006  = "CNL20006";    //受理机构该费率状态已关闭                                                     
    public static final String ERR_CODE_CNL20007  = "CNL20007";    //账户端信息不存在                                                             
    public static final String ERR_CODE_CNL20008  = "CNL20008";    //账户端appid未配置                                                           
    public static final String ERR_CODE_CNL20009  = "CNL20009";    //受理机构权限交易码参数异常                                                   
    public static final String ERR_CODE_CNL20010  = "CNL20010";    //账户端机构号参数异常                                                         
    public static final String ERR_CODE_CNL20011  = "CNL20011";    //刷卡支付才允许撤销                                                           
    public static final String ERR_CODE_CNL20012  = "CNL20012";    //原支付流水不存在                                                             
    public static final String ERR_CODE_CNL20013  = "CNL20013";    //订单状态不允许退款                                                           
    public static final String ERR_CODE_CNL20014  = "CNL20014";    //总金额与原订单金额不符                                                       
    public static final String ERR_CODE_CNL20015  = "CNL20015";    //退款金额超限                                                                 
    public static final String ERR_CODE_CNL20016  = "CNL20016";    //账户端状态异常                                                               
    public static final String ERR_CODE_CNL20017  = "CNL20017";    //生成商户号失败[序列号不存在]                                                 
    public static final String ERR_CODE_CNL20018  = "CNL20018";    //接入模式不支持                                                               
    public static final String ERR_CODE_CNL20019  = "CNL20019";    //受理机构费率不存在                                                           
    public static final String ERR_CODE_CNL20020  = "CNL20020";    //账户端未配置该支付权限                                                       
    public static final String ERR_CODE_CNL20021  = "CNL20021";    //账户端该支付权限状态已关闭                                                   
    public static final String ERR_CODE_CNL20022  = "CNL20022";    //账户端费率不存在                                                             
    public static final String ERR_CODE_CNL20023  = "CNL20023";    //重复通知                                                                     
    public static final String ERR_CODE_CNL20024  = "CNL20024";    //交易权限不存在                                                               
    public static final String ERR_CODE_CNL20025  = "CNL20025";    //账户端费率已关闭                                                             
    public static final String ERR_CODE_CNL20026  = "CNL20026";    //交易权限状态已关闭                                                           
    public static final String ERR_CODE_CNL20027  = "CNL20027";    //受理机构费率已关闭                                                           
    public static final String ERR_CODE_CNL20028  = "CNL20028";    //更新商户信息失败                                                             
    public static final String ERR_CODE_CNL20033  = "CNL20033";    //接入模式参数异常                                                             
    public static final String ERR_CODE_CNL20034  = "CNL20034";    //业务类型与原订单不符                                                         
    public static final String ERR_CODE_CNL20035  = "CNL20035";    //输入金额有误                                                                 
    public static final String ERR_CODE_CNL20036  = "CNL20036";    //账户端类型校验异常                                                           
    public static final String ERR_CODE_CNL20037  = "CNL20037";    //获取账户端接入ID异常                                                        
    public static final String ERR_CODE_CNL20038  = "CNL20038";    //订单失效时间转换异常                                                         
    public static final String ERR_CODE_CNL20039  = "CNL20039";    //退款处理中                                                                   
    public static final String ERR_CODE_CNL20040  = "CNL20040";    //输入日期有误                                                                 
    public static final String ERR_CODE_CNL20041  = "CNL20041";    //expire_time失效时间过短                                                     
    public static final String ERR_CODE_CNL20042  = "CNL20042";    //万码订单号有误                                                               
    public static final String ERR_CODE_CNL20043  = "CNL20043";    //万码退款订单号有误                                                           
    public static final String ERR_CODE_CNL20044  = "CNL20044";    //订单状态不允许关单                                                           
    public static final String ERR_CODE_CNL20045  = "CNL20045";    //订单业务类型不允许关单                                                       
    public static final String ERR_CODE_CNL20046  = "CNL20046";    //退款失败                                                                     
    public static final String ERR_CODE_CNL20047  = "CNL20047";    //更新数据库失败                                                               
    public static final String ERR_CODE_CNL20048  = "CNL20048";    //未登记受理机构通知状态，不发送商户通知                                       
    public static final String ERR_CODE_CNL00101  = "CNL00101";    //请求账户端超时                                                               
    public static final String ERR_CODE_CNL00102  = "CNL00102";    //获取账户端返回超时                                                           
    public static final String ERR_CODE_CNL00103  = "CNL00103";    //请求账户端系统异常                                                           
    public static final String ERR_CODE_CNL00104  = "CNL00104";    //调用第三方服务失败                                                           
    public static final String ERR_CODE_CNL30001  = "CNL30001";    //查询不到账户端信息                                                           
    public static final String ERR_CODE_CNL30003  = "CNL30003";    //数据库执行失败                                                               
    public static final String ERR_CODE_CNL30004  = "CNL30004";    //更新差错表上日存疑记录失败                                                   
    public static final String ERR_CODE_CNL30005  = "CNL30005";    //更新流水表上日存疑记录失败                                                   
    public static final String ERR_CODE_CNL30006  = "CNL30006";    //微信对账文件不存在                                                           
    public static final String ERR_CODE_CNL30066  = "CNL30066";    //支付宝对账文件不存在                                                         
    public static final String ERR_CODE_CNL30007  = "CNL30007";    //微信对账文件入库失败                                                         
    public static final String ERR_CODE_CNL30008  = "CNL30008";    //文件不存在                                                                   
    public static final String ERR_CODE_CNL30009  = "CNL30009";    //查询对账总控表失败                                                           
    public static final String ERR_CODE_CNL30010  = "CNL30010";    //对账正在进行中，不允许重新对账                                               
    public static final String ERR_CODE_CNL30011  = "CNL30011";    //删除对账总控表失败                                                           
    public static final String ERR_CODE_CNL30012  = "CNL30012";    //更新流水表对账状态失败                                                       
    public static final String ERR_CODE_CNL30013  = "CNL30013";    //解压zip文件失败                                                             
    public static final String ERR_CODE_CNL30014  = "CNL30014";    //更新对账总控表状态失败                                                       
    public static final String ERR_CODE_CNL30015  = "CNL30015";    //对账状态不为2,不允许进行3-redis对账                                        
    public static final String ERR_CODE_CNL30065  = "CNL30065";    //对账状态不为4,不允许进行上日长款对平调账                                    
    public static final String ERR_CODE_CNL30016  = "CNL30016";    //插入差错信息表失败                                                           
    public static final String ERR_CODE_CNL30017  = "CNL30017";    //更新流水表交易状态为1-长款存疑失败                                          
    public static final String ERR_CODE_CNL30018  = "CNL30018";    //更新流水表交易状态为5-金额差错失败                                          
    public static final String ERR_CODE_CNL30023  = "CNL30023";    //参数校验失败                                                                 
    public static final String ERR_CODE_CNL30024  = "CNL30024";    //请求参数为空                                                                 
    public static final String ERR_CODE_CNL30025  = "CNL30025";    //版本号有误                                                                   
    public static final String ERR_CODE_CNL30026  = "CNL30026";    //请求参数过长                                                                 
    public static final String ERR_CODE_CNL30027  = "CNL30027";    //签名类型有误                                                                 
    public static final String ERR_CODE_CNL30028  = "CNL30028";    //未配置受理机构密钥                                                           
    public static final String ERR_CODE_CNL30029  = "CNL30029";    //未配置技术服务方密钥                                                         
    public static final String ERR_CODE_CNL30040  = "CNL30040";    //费率类型为阶梯费率，不能使用单一费率计算                                     
    public static final String ERR_CODE_CNL30030  = "CNL30030";    //当前窗口非N状态，不允许进行切日                                             
    public static final String ERR_CODE_CNL30031  = "CNL30031";    //不允许超过当前系统日期                                                       
    public static final String ERR_CODE_CNL30032  = "CNL30032";    //对账状态运行表没有维护该账务日期的对账日期记录                               
    public static final String ERR_CODE_CNL30033  = "CNL30033";    //切日失败                                                                     
    public static final String ERR_CODE_CNL30034  = "CNL30034";    //更新t_run_sts的CUR_CHK_STS的对账状态为N失败                               
    public static final String ERR_CODE_CNL30035  = "CNL30035";    //文件名有误                                                                   
    public static final String ERR_CODE_CNL30036  = "CNL30036";    //批处理进行中                                                                 
    public static final String ERR_CODE_CNL30037  = "CNL30037";    //交易类型有误                                                                 
    public static final String ERR_CODE_ORDER_NOT_EXIST  = "CNL30038";    //原交易不存在                                                                 
    public static final String ERR_CODE_CNL30041  = "CNL30041";    //订单已支付，不能关单                                                         
    public static final String ERR_CODE_CNL30043  = "CNL30043";    //查询日期超过7天                                                             
    public static final String ERR_CODE_CNL30044  = "CNL30044";    //日期转换错误                                                                 
    public static final String ERR_CODE_CNL30045  = "CNL30045";    //未配置技术服务方sftp信息                                                    
    public static final String ERR_CODE_CNL30046  = "CNL30046";    //未配置受理机构sftp信息                                                      
    public static final String ERR_CODE_CNL30047  = "CNL30047";    //未配置万码订单号规则                                                         
    public static final String ERR_CODE_CNL30050  = "CNL30050";    //没有busmap找到对应的交易码                                                  
    public static final String ERR_CODE_CNL30051  = "CNL30051";    //查询受理机构费率配置表无记录，无法进行判断阶梯还是单笔手续费                 
    public static final String ERR_CODE_CNL40001  = "CNL40001";    //用户支付中                                                                   
    public static final String ERR_CODE_CNL40002  = "CNL40002";    //更新流水表失败                                                               
    public static final String ERR_CODE_CNL40003  = "CNL40003";    //读取文件异常，读取失败                                                       
    public static final String ERR_CODE_CNL40004  = "CNL40004";    //IO异常，导致失败                                                            
    public static final String ERR_CODE_CNL40005  = "CNL40005";    //订单不存在                                                                   
    public static final String ERR_CODE_CNL40006  = "CNL40006";    //订单号不符                                                                   
    public static final String ERR_CODE_CNL40007  = "CNL40007";    //订单查询失败                                                                 
    public static final String ERR_CODE_CNL40008  = "CNL40008";    //原订单已支付，不可重新进行支付，请使用新的订单号                             
    public static final String ERR_CODE_CNL40010  = "CNL40010";    //原订单支付失败，不可重新进行支付，请使用新的订单号                           
    public static final String ERR_CODE_CNL40012  = "CNL40012";    //用户支付中，请稍后查询                                                       
    public static final String ERR_CODE_CNL40013  = "CNL40013";    //流水交易状态已明确                                                           
    public static final String ERR_CODE_CNL40014  = "CNL40014";    //查询时间小于60S，请稍后查询                                                 
    public static final String ERR_CODE_CNL50001  = "CNL50001";    //受理机构对账文件未全部生成                                                   
    public static final String ERR_CODE_CNL50002  = "CNL50002";    //清算对账文件未全部生成                                                       
    public static final String ERR_CODE_CNL50003  = "CNL50003";    //生成受理机构对账文件初始化数据失败                                           
    public static final String ERR_CODE_CNL50004  = "CNL50004";    //更新原交易失败   
    public static final String ERR_CODE_CNL50007  = "CNL50007";    //订单不存在

    public static final String TRADE_STATUS_INIT = "INIT";    		//初始化 
    public static final String TRADE_STATUS_CREATE = "CREATE";    	//下单成功 
    public static final String TRADE_STATUS_DEAL = "DEAL";     		//支付处理中 
    public static final String TRADE_STATUS_SUCCESS = "SUCCESS";    //支付成功 
    public static final String TRADE_STATUS_FAIL = "FAIL";			//支付失败 
    public static final String TRADE_STATUS_CLOSE = "CLOSE";		//订单关闭（超时后关闭、手动关闭） 
    public static final String TRADE_STATUS_REFUND = "REFUND";		//转入退款
    public static final String TRADE_STATUS_UNKOWN = "UNKNOWN";		//结果未知
    public static final String TRADE_STATUS_REVOKED = "REVOKED";	//订单撤销 
    public static final String TRADE_STATUS_NOTPAY = "NOTPAY";		//未支付 
    public static final String TRADE_STATUS_FINISHED = "FINISHED";	//不可撤销不可退款 
    public static final String TRADE_STATUS_USERPAYING = "USERPAYING";	//用户正在支付中
}
