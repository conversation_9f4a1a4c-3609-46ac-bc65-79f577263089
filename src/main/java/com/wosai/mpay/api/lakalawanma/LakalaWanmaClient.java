package com.wosai.mpay.api.lakalawanma;

import java.io.UnsupportedEncodingException;
import java.util.Map;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSession;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.LakalaWanmaSignature;
import com.wosai.mpay.util.XmlUtils;


/**
 * Created by maoyu
 */
public class LakalaWanmaClient {

    public static final Logger logger = LoggerFactory.getLogger(LakalaWanmaClient.class);
    private int connectTimeout = 3000;
    private int readTimeout = 15000;
    private static HostnameVerifier allHostsValid = new HostnameVerifier() {
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    };


    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public Map<String, Object> call(String serviceUrl, String signKey, Map<String, Object> request) throws MpayException, MpayApiNetworkError {
        for (Object mapKey : request.keySet().toArray()) {
            if (request.get(mapKey) == null) {
                request.remove(mapKey);
            }
        }
        request.remove(BusinessFields.SIGN);
        request.put(BusinessFields.NONCE_STR, new java.security.SecureRandom().nextInt(Integer.MAX_VALUE)+"");
        request.put(BusinessFields.SIGN, LakalaWanmaSignature.getSign(request, signKey, LakalaWanmaConstants.CHARSET_GBK));
        String requestXml = XmlUtils.map2XmlString(request, "xml");
        logger.debug("request {}", requestXml);
//        String response = WebUtils.doPost(null, allHostsValid, serviceUrl, "text/xml", getBytes(requestXml, LakalaWanmaConstants.CHARSET_GBK), connectTimeout, readTimeout, LakalaWanmaConstants.CHARSET_GBK);
        String response = HttpClientUtils.doPost(LakalaWanmaClient.class.getName(), null, allHostsValid, serviceUrl, "text/xml", requestXml, LakalaWanmaConstants.CHARSET_GBK, connectTimeout, readTimeout);
        logger.debug("response {}", response.replaceAll("\\n", ""));//返回的xml报文，有换行，打印日志时，打印在一行上面。
        Map<String, Object> result = XmlUtils.parse(response);
        return result;
    }


    private byte[] getBytes(String string, String charset) throws MpayException {
        if (string == null) {
            return null;
        } else {
            try {
                return string.getBytes(charset);
            } catch (UnsupportedEncodingException e) {
                throw new MpayException("UnsupportedEncodingException :", e);
            }
        }
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }
}
