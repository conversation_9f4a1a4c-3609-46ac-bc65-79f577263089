package com.wosai.mpay.api.airwallex;

import com.wosai.mpay.api.yop.YopConstant;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.wosai.mpay.util.HttpClientUtils.BODY_RESULT_FIELD;
import static com.wosai.mpay.util.MapEncryptUtil.resetValueAndReturnNewMap;

/**
 * API client for Airwallex payment gateway
 */
public class AirwallexClient {

    private static final Logger logger = LoggerFactory.getLogger(AirwallexClient.class);

    private static final String HTTP_METHOD_POST = "post";
    private static final String HTTP_METHOD_GET = "get";

    private int connectTimeout = 1000;
    private int readTimeout = 5000;


    //打印日志时需要加密的字段
    private static final List<String> NEED_ENCRYPT_FIELDS_WHEN_LOG = Arrays.asList(
            AirwallexRequestFields.CardPresent.TRACK1,
            AirwallexRequestFields.CardPresent.TRACK2,
            AirwallexRequestFields.CardPresent.EMV_TAGS,
            AirwallexRequestFields.CardPresent.ENCRYPTED_PIN,
            AirwallexRequestFields.CardPresent.NUMBER,
            AirwallexResponseFields.LatestPaymentAttempt.AUTHORIZATION_CODE);

    //默认加密后的值
    private static final String DEFAULT_ENCRYPT_VALUE = "*";


    /**
     * Authenticate with Airwallex API
     *
     * @param url      Base URL for Airwallex API
     * @param clientId Airwallex client ID
     * @param apiKey   Airwallex API key
     * @return Authentication response
     * @throws MpayException if authentication fails
     */
    public Map<String, Object> authenticate(String url, String clientId, String apiKey) throws Exception {
        if (StringUtils.isEmpty(url) || StringUtils.isEmpty(clientId) || StringUtils.isEmpty(apiKey)) {
            throw new MpayException("Authentication parameters cannot be empty");
        }

        Map<String, String> headers = new HashMap<>();
        headers.put(AirwallexRequestFields.X_CLIENT_ID, clientId);
        headers.put(AirwallexRequestFields.X_API_KEY, apiKey);

        return execute(
                AirwallexConstants.AirwallexRequestMethod.POST,
                url,
                AirwallexConstants.AIRWALLEX_HTTP_CONTENT_TYPE_TEXT,
                headers,
                new HashMap<>()
        );
    }

    /**
     * Execute an API request to Airwallex
     *
     * @param method        HTTP method (POST or GET)
     * @param url           API endpoint URL
     * @param contentType   Content type of the request
     * @param headers       HTTP headers
     * @param requestParams Request parameters
     * @return API response as a Map
     * @throws Exception if the request fails
     */
    public Map<String, Object> execute(AirwallexConstants.AirwallexRequestMethod method, String url,
                                       String contentType, Map<String, String> headers,
                                       Map<String, Object> requestParams) throws Exception {
        if (StringUtils.isEmpty(url)) {
            throw new MpayException("URL cannot be empty");
        }

        if (method == null) {
            throw new MpayException("HTTP method cannot be null");
        }

        Map<String, Object> response;
        String content = "";

        // Convert request parameters to JSON string if not empty
        if (requestParams != null && !requestParams.isEmpty()) {
            content = JsonUtil.objectToJsonString(requestParams);
        }
        // Execute HTTP request based on method
        try {
            logger.info("airwallex request: {}", toJsonStringWithEncryptFields(requestParams));

            if (method == AirwallexConstants.AirwallexRequestMethod.POST) {
                response = HttpClientUtils.doCommonMethod(
                        AirwallexClient.class.getName(),
                        null,
                        null,
                        url,
                        null,
                        contentType,
                        content,
                        headers,
                        YopConstant.CHARSET_UTF8,
                        this.connectTimeout,
                        this.readTimeout,
                        HTTP_METHOD_POST
                );
            } else if (method == AirwallexConstants.AirwallexRequestMethod.GET) {
                response = HttpClientUtils.doCommonMethod(
                        AirwallexClient.class.getName(),
                        null,
                        null,
                        url,
                        requestParams,
                        contentType,
                        null,
                        headers,
                        YopConstant.CHARSET_UTF8,
                        this.connectTimeout,
                        this.readTimeout,
                        HTTP_METHOD_GET
                );
            } else {
                throw new MpayException("Unsupported HTTP method: " + method);
            }
        } catch (Exception e) {
            logger.error("HTTP request failed: {}", e.getMessage(), e);
            throw e;
        }
        // Parse response body
        String responseBody = MapUtil.getString(response, BODY_RESULT_FIELD);
        if (StringUtils.isEmpty(responseBody)) {
            logger.warn("Empty response body from airwallex API response: {}", response);
            return null;
        }
        Map<String, Object> result = JsonUtil.jsonStringToObject(responseBody, Map.class);
        logger.info("airwallex response: {}", toJsonStringWithEncryptFields(result));

        return result;
    }

    /**
     * Get connection timeout in milliseconds
     */
    public int getConnectTimeout() {
        return connectTimeout;
    }

    /**
     * Set connection timeout in milliseconds
     */
    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    /**
     * Get read timeout in milliseconds
     */
    public int getReadTimeout() {
        return readTimeout;
    }

    /**
     * Set read timeout in milliseconds
     */
    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }



    /**
     * 给指定需要脱敏的字段进行脱敏，并返回Json String
     *
     * @param data
     * @return
     * @throws MpayException
     */
    private static String toJsonStringWithEncryptFields(Map<String, Object> data) throws MpayException {
        Map<String, Object> newMap = resetValueAndReturnNewMap(data, NEED_ENCRYPT_FIELDS_WHEN_LOG, DEFAULT_ENCRYPT_VALUE);
        return JsonUtil.objectToJsonString(newMap);
    }
}
