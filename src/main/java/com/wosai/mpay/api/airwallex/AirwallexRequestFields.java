package com.wosai.mpay.api.airwallex;

/**
 * Request field constants for Airwallex API
 */
public class AirwallexRequestFields {

    public static final String AUTHORIZATION = "authorization";
    // Request Fields
    public static final String X_CLIENT_ID = "x-client-id";
    public static final String X_API_KEY = "x-api-key";
    public static final String X_API_VERSION = "x-api-version";
    public static final String X_ON_BEHALF_OF = "x-on-behalf-of";
    public static final String X_CARD_PRESENT_TERMINAL_IP="x-card-present-terminal-ip";
    public static final String X_CARD_PRESENT_TERMINAL_ID="x-card-present-terminal-id";

    // Common fields
    public static final String REQUEST_ID = "request_id";
    public static final String MERCHANT_ORDER_ID = "merchant_order_id";
    public static final String AMOUNT = "amount";
    public static final String CURRENCY = "currency";
    public static final String DESCRIPTOR = "descriptor";

    // Funds split data
    public static final String FUNDS_SPLIT_DATA = "funds_split_data";

    // Device data
    public static final String DEVICE_DATA = "device_data";
    public static final String IP_ADDRESS = "ip_address";

    // Payment method
    public static final String PAYMENT_METHOD = "payment_method";
    public static final String TYPE = "type";

    // Payment method options
    public static final String PAYMENT_METHOD_OPTIONS = "payment_method_options";

    /**
     * Card present payment method fields
     */
    public static class CardPresent {
        public static final String CARD_PRESENT = "card_present";
        public static final String CARD_SEQUENCE_NUMBER = "card_sequence_number";
        public static final String PAN_ENTRY_MODE = "pan_entry_mode";
        public static final String FALLBACK = "fallback";
        public static final String TRACK1 = "track1";
        public static final String TRACK2 = "track2";
        public static final String CARDHOLDER_VERIFICATION_METHOD = "cardholder_verification_method";
        public static final String ENCRYPTED_PIN = "encrypted_pin";
        public static final String EMV_TAGS = "emv_tags";
        public static final String EXPIRY_MONTH = "expiry_month";
        public static final String EXPIRY_YEAR = "expiry_year";
        public static final String TERMINAL_INFO = "terminal_info";

        public static final String NUMBER = "number";

        /**
         * PAN entry mode constants
         * The way the terminal reads the card information.
         */
        public static class PanEntryMode {
            public static final String MANUAL_ENTRY = "manual_entry";  // Manually keyed into POS terminal
            public static final String CHIP = "chip";                  // Read from direct contact with a chip card
            public static final String MAGSTRIPE = "magstripe";        // Read from direct contact with magnetic stripe card
            public static final String CONTACTLESS_CHIP = "contactless_chip";  // Read from a contactless interface using chip data
            public static final String CONTACTLESS_MAGSTRIPE = "contactless_magstripe";  // Read from a contactless interface using magnetic stripe data (MSD)
        }

        /**
         * Cardholder verification method constants
         */
        public static class CardholderVerificationMethod {
            public static final String SKIPPED = "skipped";
            public static final String MANUAL_SIGNATURE = "manual_signature";
            public static final String ONLINE_PIN = "online_pin";
            public static final String OFFLINE_PIN = "offline_pin";
        }
    }

    /**
     * Terminal information fields
     */
    public static class TerminalInfo {
        public static final String TERMINAL_ID = "terminal_id";
        public static final String PIN_ENTRY_CAPABILITY = "pin_entry_capability";
        public static final String SUPPORTED_PAN_ENTRY_MODES = "supported_pan_entry_modes";
        public static final String MOBILE_DEVICE = "mobile_device";
        public static final String USE_EMBEDDED_READER = "use_embedded_reader";

        /**
         * PIN entry capability constants
         * Describes the capability of the terminal device to accept PIN.
         */
        public static class PinEntryCapability {
            public static final String UNSUPPORTED = "unsupported";  // Terminal does not have PIN entry capability
            public static final String SUPPORTED = "supported";      // Terminal has PIN entry capability (except for software_based cases)
            public static final String SOFTWARE_BASED = "software_based";  // For a mobile POS device, a user can input PIN through software-based keyboard
            public static final String DISABLED = "disabled";        // Terminal has PIN entry capability but PIN pad is not currently operative
        }
    }

    /**
     * Card payment method options
     */
    public static class CardOptions {
        public static final String CARD = "card";
        public static final String AUTHORIZATION_TYPE = "authorization_type";
        public static final String AUTO_CAPTURE = "auto_capture";

        /**
         * Authorization type constants
         * The authorization type of the card payment.
         * Only applicable when payment_method is provided. Default to final_auth.
         * Currently only available when brand is visa or mastercard.
         */
        public static class AuthorizationType {
            public static final String FINAL_AUTH = "final_auth";  // Final authorization
            public static final String PRE_AUTH = "pre_auth";      // Pre-authorization
        }
    }

    /**
     * Funds split data fields
     */
    public static class FundsSplitData {
        public static final String AMOUNT = "amount";
        public static final String DESTINATION = "destination";
    }

    /**
     * Card payment method type constants
     */
    public static class PaymentMethodType {
        public static final String CARD = "card";
        public static final String CARD_PRESENT = "card_present";
    }


    //Token 的过期时间，采用 ISO8601 格式
    public static final String EXPIRES_AT = "expires_at";

    /**
     * Refund related fields
     */
    public static class Refund {
        public static final String PAYMENT_INTENT_ID = "payment_intent_id";
        public static final String AMOUNT = "amount";
        public static final String REASON = "reason";
        public static final String METADATA = "metadata";
    }
}