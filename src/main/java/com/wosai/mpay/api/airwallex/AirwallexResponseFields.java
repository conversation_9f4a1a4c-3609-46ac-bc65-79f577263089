package com.wosai.mpay.api.airwallex;

public class AirwallexResponseFields {



    //授权接口 token  令牌
    public static final String TOKEN = "token";



    // Common response fields
    public static final String CODE = "code";
    public static final String MESSAGE = "message";
    public static final String STATUS = "status";
    public static final String PROVIDER_ORIGINAL_RESPONSE_CODE = "provider_original_response_code";

    public static final String LATEST_PAYMENT_ATTEMPT ="latest_payment_attempt";

    public static final String PAYMENT_METHOD_TRANSACTION_ID="payment_method_transaction_id";


    public static final String ID ="id";

    public static final String CUSTOMER_ID ="customer_id";

    public static final String UPDATED_AT ="updated_at";
    /**
     * 用户实际支付金额
     */
    public static final String CAPTURED_AMOUNT = "captured_amount";

    public static final String REQUEST_ID = "request_id";

    public static final String CURRENCY = "currency";

    /**
     * Refund related response fields
     */
    public static class Refund {
        public static final String ID = "id";
        public static final String PAYMENT_INTENT_ID = "payment_intent_id";
        public static final String AMOUNT = "amount";
        public static final String CURRENCY = "currency";
        public static final String STATUS = "status";
        public static final String CREATED_AT = "created_at";
        public static final String UPDATED_AT = "updated_at";
    }

    public static class LatestPaymentAttempt {

        public static final String ID = "id";

        public static final String AMOUNT = "amount";

        public static final String CURRENCY = "currency";

        public static final String STATUS = "status";

        public static final String AUTHORIZATION_CODE = "authorization_code";

        public static final String PAYMENT_METHOD = "payment_method";

        public static final String PAYMENT_METHOD_CARD_PRESENT = "card_present";

        public static final String PAYMENT_METHOD_CARD_PRESENT_EMV_TAGS = "emv_tags";

        public static final String PAYMENT_METHOD_CARD_PRESENT_card_type = "card_type";

    }
}
