package com.wosai.mpay.api.airwallex;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;


public class AirwallexAccessTokenCache {

    public static final Logger logger = LoggerFactory.getLogger(AirwallexAccessTokenCache.class);

    //访问令牌有效期为 30 分钟，缓存25分钟
    private final LoadingCache<TokenKey, String> TOKEN_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(25, TimeUnit.MINUTES)
            .build(new CacheLoader<TokenKey, String>() {
                @Override
                public String load(TokenKey key) {

                    return loadToken(key.getClientId(), key.getApiKey(), key.getUrl());
                }
            });

    private AirwallexClient airwallexClient;

    public AirwallexAccessTokenCache(AirwallexClient airwallexClient) {
        this.airwallexClient = airwallexClient;
    }

    public String getAccessToken(String clientId, String apiKey, String url) {
        TokenKey tokenKey = new TokenKey(clientId, apiKey, url);
        String token;
        try {
            token = TOKEN_CACHE.get(tokenKey);
        } catch (ExecutionException e) {
            throw new RuntimeException("获取token失败");
        }
        return token;
    }

    public void refreshToken(String clientId, String apiKey, String url) {
        TokenKey tokenKey = new TokenKey(clientId, apiKey, url);
        TOKEN_CACHE.refresh(tokenKey);
    }


    private String loadToken(String clientId, String apiKey, String url) {

        Map<String, Object> result;
        try {
            result = retryIfNetworkException(clientId, apiKey, url);
        } catch (Exception e) {
            throw new RuntimeException("获取token失败");
        }

        String accessToken = null;
        if (result != null && !result.isEmpty()) {
            accessToken = String.valueOf(result.get(AirwallexResponseFields.TOKEN));
        }
        if (StringUtils.isEmpty(accessToken) || "null".equalsIgnoreCase(accessToken)) {
            throw new RuntimeException("获取token失败");
        }

        return accessToken;
    }

    private Map<String, Object> retryIfNetworkException(String clientId, String apiKey, String url) throws Exception {
        Exception exception = null;
        for (int i = 0; i < 3; ++i) {
            try {
                return airwallexClient.authenticate(url, clientId, apiKey);
            } catch (MpayApiNetworkError ex) {
                exception = ex;
                logger.warn("encountered ioex in sodexo {}", ex);
            }
        }
        logger.error("loadToken still network i/o error after retrying 3 times.");
        throw exception;
    }


    private static class TokenKey {
        private String clientId;
        private String apiKey;
        private String url;

        public TokenKey(String clientId, String apiKey, String url) {
            this.clientId = clientId;
            this.apiKey = apiKey;
            this.url = url;
        }

        public String getClientId() {
            return clientId;
        }

        public void setClientId(String clientId) {
            this.clientId = clientId;
        }

        public String getApiKey() {
            return apiKey;
        }

        public void setApiKey(String apiKey) {
            this.apiKey = apiKey;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        /**
         * clientId作为缓存的key
         */
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            TokenKey tokenKey = (TokenKey) o;
            return java.util.Objects.equals(clientId, tokenKey.clientId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(clientId);
        }
    }
}
