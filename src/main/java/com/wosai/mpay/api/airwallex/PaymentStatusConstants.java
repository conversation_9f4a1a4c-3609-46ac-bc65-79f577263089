package com.wosai.mpay.api.airwallex;

/**
 * Payment status constants for Airwallex
 */
public class PaymentStatusConstants {

    /**
     * The PaymentIntent is waiting for the confirm request
     */
    public static final String REQUIRES_PAYMENT_METHOD = "REQUIRES_PAYMENT_METHOD";

    /**
     * The PaymentIntent is waiting for customer authentication
     */
    public static final String REQUIRES_CUSTOMER_ACTION = "REQUIRES_CUSTOMER_ACTION";

    /**
     * The PaymentIntent is waiting for capture to complete payment
     */
    public static final String REQUIRES_CAPTURE = "REQUIRES_CAPTURE";

    /**
     * The PaymentIntent is pending final result from provider
     */
    public static final String PENDING = "PENDING";

    /**
     * Payment succeeded and is complete
     */
    public static final String SUCCEEDED = "SUCCEEDED";

    /**
     * Payment has been cancelled
     */
    public static final String CANCELLED = "CANCELLED";

    /**
     * Check if payment requires further action
     */
    public static boolean requiresAction(String status) {
        return REQUIRES_PAYMENT_METHOD.equals(status) 
            || REQUIRES_CUSTOMER_ACTION.equals(status)
            || REQUIRES_CAPTURE.equals(status);
    }

    /**
     * Check if payment is in a terminal state
     */
    public static boolean isTerminalState(String status) {
        return SUCCEEDED.equals(status) || CANCELLED.equals(status);
    }

    /**
     * Check if payment is still processing
     */
    public static boolean isProcessing(String status) {
        return PENDING.equals(status) || REQUIRES_CAPTURE.equals(status) || REQUIRES_CUSTOMER_ACTION.equals(status) || REQUIRES_PAYMENT_METHOD.equals(status);
    }

    /**
     * Check if payment succeeded
     */
    public static boolean isSuccessful(String status) {
        return SUCCEEDED.equals(status);
    }
}