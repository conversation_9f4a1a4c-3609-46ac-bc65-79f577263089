package com.wosai.mpay.api.airwallex;

/**
 * Refund status constants for Airwallex
 */
public class RefundStatusConstants {

    /**
     * The Refund is created, which means the requested refund amount is valid 
     * and the payment is not in dispute.
     */
    public static final String RECEIVED = "RECEIVED";

    /**
     * The Refund has been accepted by the payment method provider 
     * and can be considered completed.
     */
    public static final String ACCEPTED = "ACCEPTED";

    /**
     * The Refund has been reconciled and will show up in the 
     * Airwallex settlement report.
     */
    public static final String SETTLED = "SETTLED";

    /**
     * The Refund has been rejected, which can be caused by the insufficient 
     * Airwallex receivable/available balance, or by the provider for more 
     * channel-specific reasons.
     */
    public static final String FAILED = "FAILED";

    /**
     * Check if refund is in a terminal state (either completed or failed)
     */
    public static boolean isTerminalState(String status) {
        return ACCEPTED.equals(status) || SETTLED.equals(status) || FAILED.equals(status);
    }

    /**
     * Check if refund is still processing
     */
    public static boolean isProcessing(String status) {
        return RECEIVED.equals(status);
    }

    /**
     * Check if refund succeeded (accepted or settled)
     */
    public static boolean isSuccessful(String status) {
        return ACCEPTED.equals(status) || SETTLED.equals(status);
    }

    /**
     * Check if refund failed
     */
    public static boolean isFailed(String status) {
        return FAILED.equals(status);
    }
}
