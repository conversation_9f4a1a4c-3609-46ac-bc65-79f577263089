package com.wosai.mpay.model.applepay;

/**
 * {
 *   "token": {
 *     "paymentData": {
 *       "data": "u+nlH8GaIRm1BXa5+zI/AsaNCtd",
 *       "signature": "xdsfdfsdf",
 *       "header": {
 *         "publicKeyHash": "1aaqBXVpj2UsludhuoXdUUouP1Kp19q83ILL/kGggAw=",
 *         "ephemeralPublicKey": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAES7Reep1z3CS8Gv9t8xSMFEap9bzuZLo3nQWRaYGLjH4jJ/BzfpKQbaMex8yVO0PWSeSrHz2vmadkv+rsxv1OZg==",
 *         "transactionId": "4e61413c6cfbac8a4016724b39fe4fcf82051084864dd8cc0bf7150a806256fe"
 *       },
 *       "version": "EC_v1"
 *     },
 *     "paymentMethod": {
 *       "displayName": "MasterCard2114",
 *       "network": "MasterCard",
 *       "type": "debit"
 *     },
 *     "transactionIdentifier": "4e61413c6cfbac8a4016724b39fe4fcf82051084864dd8cc0bf7150a806256fe"
 *   }
 * }
 */
public class ApplePayPayload {
    private Token token;

    public Token getToken() {
        return token;
    }

    public void setToken(Token token) {
        this.token = token;
    }

    public static class Token {
        private PaymentData paymentData;
        private PaymentMethod paymentMethod;
        private String transactionIdentifier;

        // Getters and Setters
        public PaymentData getPaymentData() {
            return paymentData;
        }

        public void setPaymentData(PaymentData paymentData) {
            this.paymentData = paymentData;
        }

        public PaymentMethod getPaymentMethod() {
            return paymentMethod;
        }

        public void setPaymentMethod(PaymentMethod paymentMethod) {
            this.paymentMethod = paymentMethod;
        }

        public String getTransactionIdentifier() {
            return transactionIdentifier;
        }

        public void setTransactionIdentifier(String transactionIdentifier) {
            this.transactionIdentifier = transactionIdentifier;
        }

        // Inner classes for nested structures
        public static class PaymentData {
            private String data;
            private String signature;
            private Header header;
            private String version;

            // Getters and Setters
            public String getData() {
                return data;
            }

            public void setData(String data) {
                this.data = data;
            }

            public String getSignature() {
                return signature;
            }

            public void setSignature(String signature) {
                this.signature = signature;
            }

            public Header getHeader() {
                return header;
            }

            public void setHeader(Header header) {
                this.header = header;
            }

            public String getVersion() {
                return version;
            }

            public void setVersion(String version) {
                this.version = version;
            }
        }

        public static class Header {
            private String publicKeyHash;
            private String ephemeralPublicKey;
            private String transactionId;

            // Getters and Setters
            public String getPublicKeyHash() {
                return publicKeyHash;
            }

            public void setPublicKeyHash(String publicKeyHash) {
                this.publicKeyHash = publicKeyHash;
            }

            public String getEphemeralPublicKey() {
                return ephemeralPublicKey;
            }

            public void setEphemeralPublicKey(String ephemeralPublicKey) {
                this.ephemeralPublicKey = ephemeralPublicKey;
            }

            public String getTransactionId() {
                return transactionId;
            }

            public void setTransactionId(String transactionId) {
                this.transactionId = transactionId;
            }
        }

        public static class PaymentMethod {
            private String displayName;
            private String network;
            private String type;

            // Getters and Setters
            public String getDisplayName() {
                return displayName;
            }

            public void setDisplayName(String displayName) {
                this.displayName = displayName;
            }

            public String getNetwork() {
                return network;
            }

            public void setNetwork(String network) {
                this.network = network;
            }

            public String getType() {
                return type;
            }

            public void setType(String type) {
                this.type = type;
            }
        }
    }
}


