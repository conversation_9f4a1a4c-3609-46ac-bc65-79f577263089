package com.wosai.mpay.util.cmbapp;

import cn.hutool.core.codec.Base64Encoder;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import org.apache.commons.lang.StringUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.Iterator;
import java.util.Map;
import java.util.TreeMap;

public final class URLUtils {
    private URLUtils() {
    }

    public static String assembleUrl(String prefix, String queryString) {
        return StringUtils.isBlank(prefix) ? queryString : prefix + (prefix.contains("?") ? "&" : "?") + queryString;
    }

    public static String assembleUrl(String prefix, Map<String, Object> paramsMap, boolean isUrlEncode) {
        return assembleUrl(prefix, mapToQueryString(paramsMap, true, isUrlEncode));
    }

    public static String assembleProtocol(String funcName, Map<String, Object> paramsMap, boolean isUrlEncode) {
        return assembleUrl("cmblife://" + funcName, paramsMap, isUrlEncode);
    }

    public static String mapToQueryString(Map<String, Object> map, boolean isSort, boolean isUrlEncode) {
        Object tempMap;
        if (isSort) {
            tempMap = new TreeMap();
            ((Map)tempMap).putAll(map);
        } else {
            tempMap = map;
        }

        StringBuilder sb = new StringBuilder();
        Iterator i$ = ((Map)tempMap).entrySet().iterator();

        while(i$.hasNext()) {
            Map.Entry<String, Object> entry = (Map.Entry)i$.next();
            String key = (String)entry.getKey();
            Object value = entry.getValue();
            String valueStr = objectToString(value);
            if (!StringUtils.isBlank(valueStr)) {
                if (isUrlEncode) {
                    valueStr = urlEncode(valueStr);
                }

                if (StringUtils.isNotBlank(valueStr)) {
                    sb.append(key).append("=").append(valueStr).append("&");
                }
            }
        }

        String queryString = sb.toString();
        if (queryString.length() > 1) {
            queryString = queryString.substring(0, queryString.length() - 1);
        }

        return queryString;
    }

    public static String objectToString(Object object) {
        if (null == object) {
            return "";
        } else {
            try {
                return !(object instanceof Map) && !(object instanceof Collection) ? object + "" : JsonUtil.objectToJsonString(object);
            } catch (MpayException e) {
                return "";
            }
        }
    }

    public static String urlEncode(String str) {
        try {
            String[] temp = str.split("\\ ");
            StringBuilder sb = new StringBuilder();

            for(int t = 0; t < temp.length; ++t) {
                sb.append(URLEncoder.encode(temp[t], StandardCharsets.UTF_8.name()));
                if (t < temp.length - 1) {
                    sb.append("%20");
                }
            }

            return sb.toString();
        } catch (Exception var4) {
            var4.printStackTrace();
            return null;
        }
    }
}