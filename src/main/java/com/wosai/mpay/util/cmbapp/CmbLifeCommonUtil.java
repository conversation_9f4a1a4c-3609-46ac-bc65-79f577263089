package com.wosai.mpay.util.cmbapp;

import com.wosai.mpay.exception.MpayClientError;

import java.security.SecureRandom;

public class CmbLifeCommonUtil {

    private CmbLifeCommonUtil() {
    }

    public static byte[] randomBytes(int size) throws MpayClientError {
        try {
            byte[] buffer = new byte[size];
            (new SecureRandom()).nextBytes(buffer);
            return buffer;
        } catch (Exception var2) {
            throw new MpayClientError("Gen randomBytes error");
        }
    }

    public static byte[] getUtf8Bytes(String str) throws MpayClientError {
        if (null == str) {
            throw new MpayClientError("UTF-8 String convert byteArray error");
        } else {
            try {
                return str.getBytes("UTF-8");
            } catch (Exception var2) {
                throw new MpayClientError("UTF-8 String convert byteArray error");
            }
        }
    }

    public static String getUtf8String(byte[] bytes) throws MpayClientError {
        if (null == bytes) {
            throw new MpayClientError("ByteArray convert UTF-8 String error");
        } else {
            try {
                return new String(bytes, "UTF-8");
            } catch (Exception var2) {
                throw new MpayClientError( "ByteArray convert UTF-8 String error");
            }
        }
    }

    public static String byteArrayToHexString(byte[] byteArray) throws MpayClientError {
        StringBuilder result = new StringBuilder();
        if (null == byteArray) {
            throw new MpayClientError( "ByteArray convert Hex String error");
        } else {
            byte[] var2 = byteArray;
            int var3 = byteArray.length;

            for(int var4 = 0; var4 < var3; ++var4) {
                byte aByteArray = var2[var4];
                result.append(Character.forDigit(aByteArray >>> 4 & 15, 16));
                result.append(Character.forDigit(aByteArray & 15, 16));
            }

            return result.toString();
        }
    }

    public static byte[] hexStringToByteArray(String src) throws MpayClientError {
        if (null == src) {
            throw new MpayClientError( "Hex String convert byteArray error");
        } else {
            int len = src.length();
            if (len % 2 != 0) {
                throw new MpayClientError( "Hex String convert byteArray error");
            } else {
                byte[] out = new byte[len / 2];

                for(int i = 0; i < len; i += 2) {
                    int h = hexToBin(src.charAt(i));
                    int l = hexToBin(src.charAt(i + 1));
                    if (h == -1 || l == -1) {
                        throw new MpayClientError( "Hex String convert byteArray error");
                    }

                    out[i / 2] = (byte)(h * 16 + l);
                }

                return out;
            }
        }
    }

    private static int hexToBin(char ch) {
        if ('0' <= ch && ch <= '9') {
            return ch - 48;
        } else if ('A' <= ch && ch <= 'F') {
            return ch - 65 + 10;
        } else {
            return 'a' <= ch && ch <= 'f' ? ch - 97 + 10 : -1;
        }
    }
}
