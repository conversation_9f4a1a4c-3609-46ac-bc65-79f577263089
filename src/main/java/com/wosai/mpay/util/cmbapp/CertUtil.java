package com.wosai.mpay.util.cmbapp;

import kl.ssl.gmvpn.crypto.TlsCertificate;
import kl.ssl.gmvpn.crypto.TlsCrypto;
import kl.ssl.gmvpn.crypto.impl.jcajce.JcaTlsCertificate;
import kl.ssl.gmvpn.crypto.impl.jcajce.JcaTlsCrypto;
import org.bouncycastle.asn1.ASN1ObjectIdentifier;
import org.bouncycastle.asn1.pkcs.PKCSObjectIdentifiers;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.asn1.x509.AlgorithmIdentifier;
import org.bouncycastle.asn1.x9.X9ObjectIdentifiers;
import org.bouncycastle.util.io.pem.PemObject;
import org.bouncycastle.util.io.pem.PemReader;

import java.io.IOException;
import java.io.StringReader;
import java.security.GeneralSecurityException;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;

public class CertUtil {
    private static final String CERTIFICATE = "CERTIFICATE";
    private static final String PRIVATE_KEY = "PRIVATE KEY";

    public CertUtil() {
    }

    public static PrivateKey loadJcaPrivateKey(TlsCrypto crypto, String pemContent) throws IOException {
        return loadJcaPrivateKey((JcaTlsCrypto) crypto, pemContent);
    }

    private static PrivateKey loadJcaPrivateKey(JcaTlsCrypto crypto, String pemContent) throws IOException {
        PemObject pem = loadPemContent(pemContent);
        Throwable cause = null;
        if ("PRIVATE KEY".equals(pem.getType())) {
            try {
                return loadJcaPkcs8PrivateKey(crypto, pem.getContent());
            } catch (GeneralSecurityException var5) {
                cause = var5;
            }
        }

        throw new IllegalArgumentException("'resource' doesn't specify a valid private key", cause);
    }

    private static TlsCertificate loadTlsCertificate(TlsCrypto crypto, String pemContent) throws IOException {
        pemContent = formatCertStr(pemContent);
        PemObject pem = loadPemContent(pemContent);
        if (pem.getType().endsWith("CERTIFICATE")) {
            return crypto.createCertificate(pem.getContent());
        } else {
            throw new IllegalArgumentException("'resource' doesn't specify a valid certificate");
        }
    }

    private static String formatCertStr(String pemContent) {
        return !pemContent.contains("CERTIFICATE") ? "-----BEGIN CERTIFICATE-----\n" + pemContent + "\n-----END CERTIFICATE-----\n" : pemContent;
    }

    public static JcaTlsCertificate loadJcaTlsCertificate(TlsCrypto crypto, String pemContent) throws IOException {
        return (JcaTlsCertificate) loadTlsCertificate(crypto, pemContent);
    }

    private static PemObject loadPemContent(String pemContent) throws IOException {
        PemReader p = new PemReader(new StringReader(pemContent));
        PemObject o = p.readPemObject();
        p.close();
        return o;
    }

    private static PrivateKey loadJcaPkcs8PrivateKey(JcaTlsCrypto crypto, byte[] encoded) throws GeneralSecurityException {
        PrivateKeyInfo pki = PrivateKeyInfo.getInstance(encoded);
        AlgorithmIdentifier algID = pki.getPrivateKeyAlgorithm();
        ASN1ObjectIdentifier oid = algID.getAlgorithm();
        String name;
        if (X9ObjectIdentifiers.id_ecPublicKey.equals(oid)) {
            name = "EC";
        } else if (!PKCSObjectIdentifiers.rsaEncryption.equals(oid) && !PKCSObjectIdentifiers.id_RSASSA_PSS.equals(oid)) {
            name = oid.getId();
        } else {
            name = "RSA";
        }

        KeyFactory kf = crypto.getHelper().createKeyFactory(name);
        return kf.generatePrivate(new PKCS8EncodedKeySpec(encoded));
    }

    public static String formatKey(String key) {
        return !key.contains("PRIVATE KEY") ? "-----BEGIN PRIVATE KEY-----\n" + key + "\n-----END PRIVATE KEY-----" : key;
    }

    public static String formatCert(String cert) {
        return !cert.contains("CERTIFICATE") ? "-----BEGIN CERTIFICATE-----\n" + cert + "\n-----END CERTIFICATE-----\n" : cert;
    }
}