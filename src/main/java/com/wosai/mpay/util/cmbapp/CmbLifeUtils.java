package com.wosai.mpay.util.cmbapp;

import cn.hutool.core.codec.Base64Encoder;
import com.google.common.collect.Maps;
import com.wosai.mpay.exception.MpayClientError;
import com.wosai.mpay.util.Base64;
import com.wosai.mpay.util.cmbapp.sign.SM2Utils;
import org.apache.commons.lang.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Map;

public class CmbLifeUtils {

    private static final SM2Utils sm2Utils = new SM2Utils();

    public static String signForRequest(String funcName, Map<String, String> params, String signKey) throws MpayClientError {
        if (StringUtils.isNotBlank(funcName) && !funcName.contains(".json")) {
            funcName = funcName + ".json";
        }

        String signBody = URLUtils.assembleUrl(funcName, Maps.newHashMap(params), false);
        byte[] sign = sm2Utils.sm2SignWithSM3ASN1(CmbLifeCommonUtil.hexStringToByteArray(signKey),
                    signBody.getBytes(StandardCharsets.UTF_8));
        return new String(Base64Encoder.encode(sign, false), StandardCharsets.UTF_8);
    }

    public static String encrypt(String encryptBody, String cmbPublicKey) throws MpayClientError {
        byte[] body = sm2Utils.asymmetricEncrypt(
                CmbLifeCommonUtil.hexStringToByteArray(cmbPublicKey),
                encryptBody.getBytes(StandardCharsets.UTF_8)
        );
        return Base64.encode(body);
    }

    public static String decrypt(String encryptBody, String merPrivateKey) throws MpayClientError {
        byte[] body = sm2Utils.asymmetricDecrypt(CmbLifeCommonUtil.hexStringToByteArray(merPrivateKey),
                Base64.decode(encryptBody));
        return new String(body, StandardCharsets.UTF_8);
    }

    public static String assembleUrl(String prefix, String queryString) {
        return StringUtils.isBlank(prefix) ? queryString : prefix + (prefix.contains("?") ? "&" : "?") + queryString;
    }

}
