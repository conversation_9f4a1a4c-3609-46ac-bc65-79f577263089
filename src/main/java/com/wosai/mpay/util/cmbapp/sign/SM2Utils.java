package com.wosai.mpay.util.cmbapp.sign;

import com.wosai.mpay.exception.MpayClientError;
import org.bouncycastle.asn1.*;
import org.bouncycastle.crypto.AsymmetricCipherKeyPair;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.generators.ECKeyPairGenerator;
import org.bouncycastle.crypto.params.*;
import org.bouncycastle.crypto.params.ParametersWithID;
import org.bouncycastle.crypto.signers.SM2Signer;
import org.bouncycastle.jce.ECNamedCurveTable;
import org.bouncycastle.jce.spec.ECParameterSpec;
import org.bouncycastle.math.ec.ECCurve;
import org.bouncycastle.math.ec.ECPoint;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

public class SM2Utils {
    public static final String CURVE_NAME = "sm2p256v1";
    private static final byte[] USER_ID = "1234567812345678".getBytes();

    public SM2Utils() {
    }

    public static SM2Utils getInstance() {
        return new SM2Utils();
    }

    private ECDomainParameters getECDomainParameters() {
        ECParameterSpec spec = ECNamedCurveTable.getParameterSpec("sm2p256v1");
        return new ECDomainParameters(spec.getCurve(), spec.getG(), spec.getN(), spec.getH(), spec.getSeed());
    }

    private ECCurve getSM2Curve() {
        ECParameterSpec spec = ECNamedCurveTable.getParameterSpec("sm2p256v1");
        return spec.getCurve();
    }

    public Map<String, byte[]> sm2KeyGen() {
        ECDomainParameters domainParameters = this.getECDomainParameters();
        ECKeyPairGenerator generator = new ECKeyPairGenerator();
        ECKeyGenerationParameters parameters = new ECKeyGenerationParameters(domainParameters, new SecureRandom());
        generator.init(parameters);
        AsymmetricCipherKeyPair keyPair = generator.generateKeyPair();
        ECPublicKeyParameters publicKeyParameters = (ECPublicKeyParameters)keyPair.getPublic();
        ECPrivateKeyParameters privateKeyParameters = (ECPrivateKeyParameters)keyPair.getPrivate();
        Map<String, byte[]> map = new HashMap();
        map.put("publickey", publicKeyParameters.getQ().getEncoded(false));
        map.put("privatekey", this.format(privateKeyParameters.getD().toByteArray()));
        return map;
    }

    private byte[] format(byte[] value) {
        if (value.length == 32) {
            return value;
        } else {
            byte[] bytes = new byte[32];
            if (value.length > 32) {
                System.arraycopy(value, value.length - 32, bytes, 0, 32);
            } else {
                System.arraycopy(value, 0, bytes, 32 - value.length, value.length);
            }

            return bytes;
        }
    }

    public byte[] asymmetricEncrypt(byte[] pubkey, byte[] msg) throws MpayClientError {
        if (pubkey == null) {
            throw new MpayClientError(ErrorCode.E10400.getMessage());
        } else if (msg == null) {
            throw new MpayClientError(ErrorCode.E10400.getMessage());
        } else if (msg.length == 0) {
            throw new MpayClientError(ErrorCode.E10415.getMessage());
        } else if (pubkey.length != 65) {
            throw new MpayClientError(ErrorCode.E10417.getMessage());
        } else if (pubkey[0] != 4) {
            throw new MpayClientError(ErrorCode.E10403.getMessage());
        } else {
            ECPublicKeyParameters publicKey = null;

            try {
                publicKey = this.encodePublicKey(pubkey);
            } catch (Exception var7) {
                throw new MpayClientError(ErrorCode.E10416.getMessage());
            }

            SM2Engine engine = new SM2Engine();
            engine.init(true, new ParametersWithRandom(publicKey, new SecureRandom()));

            try {
                byte[] cipherText = engine.processBlock(msg, 0, msg.length);
                return this.c1C2C3ToC1C3C2(cipherText);
            } catch (InvalidCipherTextException var6) {
                throw new MpayClientError(ErrorCode.E10200.getMessage(), var6);
            }
        }
    }

    public byte[] asymmetricDecrypt(byte[] privkey, byte[] msg) throws MpayClientError {
        if (privkey == null) {
            throw new MpayClientError(ErrorCode.E10400.getMessage());
        } else if (privkey.length != 32) {
            throw new MpayClientError(ErrorCode.E10418.getMessage());
        } else if (msg == null) {
            throw new MpayClientError(ErrorCode.E10400.getMessage());
        } else if (msg.length < 97) {
            throw new MpayClientError(ErrorCode.E10419.getMessage());
        } else if (msg[0] != 4) {
            throw new MpayClientError(ErrorCode.E10424.getMessage());
        } else {
            msg = this.c1C3C2ToC1C2C3(msg);
            ECPrivateKeyParameters privateKey = null;

            try {
                privateKey = this.encodePrivateKey(privkey);
            } catch (Exception var7) {
                throw new MpayClientError(ErrorCode.E10421.getMessage());
            }

            SM2Engine engine = new SM2Engine();
            engine.init(false, privateKey);

            try {
                return engine.processBlock(msg, 0, msg.length);
            } catch (InvalidCipherTextException var6) {
                throw new MpayClientError(ErrorCode.E10201.getMessage(), var6);
            }
        }
    }

    private ECPublicKeyParameters encodePublicKey(byte[] value) {
        byte[] x = new byte[32];
        byte[] y = new byte[32];
        System.arraycopy(value, 1, x, 0, 32);
        System.arraycopy(value, 33, y, 0, 32);
        BigInteger X = new BigInteger(1, x);
        BigInteger Y = new BigInteger(1, y);
        ECPoint Q = this.getSM2Curve().createPoint(X, Y);
        return new ECPublicKeyParameters(Q, this.getECDomainParameters());
    }

    private ECPrivateKeyParameters encodePrivateKey(byte[] value) {
        BigInteger d = new BigInteger(1, value);
        return new ECPrivateKeyParameters(d, this.getECDomainParameters());
    }

    private byte[] c1C2C3ToC1C3C2(byte[] cipherText) throws MpayClientError {
        if (cipherText != null && cipherText.length >= 97) {
            byte[] bytes = new byte[cipherText.length];
            System.arraycopy(cipherText, 0, bytes, 0, 65);
            System.arraycopy(cipherText, cipherText.length - 32, bytes, 65, 32);
            System.arraycopy(cipherText, 65, bytes, 97, cipherText.length - 97);
            return bytes;
        } else {
            throw new MpayClientError(ErrorCode.E10406.getMessage());
        }
    }

    private byte[] c1C3C2ToC1C2C3(byte[] cipherText) throws MpayClientError {
        if (cipherText != null && cipherText.length >= 97) {
            byte[] bytes = new byte[cipherText.length];
            System.arraycopy(cipherText, 0, bytes, 0, 65);
            System.arraycopy(cipherText, 97, bytes, 65, cipherText.length - 97);
            System.arraycopy(cipherText, 65, bytes, cipherText.length - 32, 32);
            return bytes;
        } else {
            throw new MpayClientError(ErrorCode.E10406.getMessage());
        }
    }

    public byte[] sm2SignWithSM3(byte[] privkey, byte[] msg) throws MpayClientError {
        if (privkey == null) {
            throw new MpayClientError(ErrorCode.E10400.getMessage());
        } else if (msg == null) {
            throw new MpayClientError(ErrorCode.E10400.getMessage());
        } else if (msg.length == 0) {
            throw new MpayClientError(ErrorCode.E10415.getMessage());
        } else if (privkey.length != 32) {
            throw new MpayClientError(ErrorCode.E10418.getMessage());
        } else {
            ECPrivateKeyParameters privateKey = this.encodePrivateKey(privkey);
            SM2Signer signer = new SM2Signer();
            org.bouncycastle.crypto.params.ParametersWithID parameters = new org.bouncycastle.crypto.params.ParametersWithID(privateKey, USER_ID);
            signer.init(true, parameters);
            signer.update(msg, 0, msg.length);

            try {
                return this.decodeDERSignature(signer.generateSignature());
            } catch (Exception var7) {
                throw new MpayClientError(ErrorCode.E10202.getMessage(), var7);
            }
        }
    }

    public int sm2VerifyWithSM3(byte[] pubkey, byte[] msg, byte[] signature) throws MpayClientError {
        if (pubkey == null) {
            throw new MpayClientError(ErrorCode.E10400.getMessage());
        } else if (signature == null) {
            throw new MpayClientError(ErrorCode.E10400.getMessage());
        } else if (msg == null) {
            throw new MpayClientError(ErrorCode.E10400.getMessage());
        } else if (msg.length == 0) {
            throw new MpayClientError(ErrorCode.E10415.getMessage());
        } else if (pubkey.length != 65) {
            throw new MpayClientError(ErrorCode.E10417.getMessage());
        } else if (pubkey[0] != 4) {
            throw new MpayClientError(ErrorCode.E10403.getMessage());
        } else if (signature.length != 64) {
            throw new MpayClientError(ErrorCode.E10405.getMessage());
        } else {
            ECPublicKeyParameters publicKey = this.encodePublicKey(pubkey);
            SM2Signer signer = new SM2Signer();
            org.bouncycastle.crypto.params.ParametersWithID parameters = new ParametersWithID(publicKey, USER_ID);
            signer.init(false, parameters);
            signer.update(msg, 0, msg.length);
            return !signer.verifySignature(this.encodeDERSignature(signature)) ? -1 : 0;
        }
    }

    public byte[] sm2SignWithSM3ASN1(byte[] privkey, byte[] msg) throws MpayClientError {
        byte[] rsSign = this.sm2SignWithSM3(privkey, msg);
        return this.encodeDERSignature(rsSign);
    }

    public int sm2VerifyWithSM3ASN1(byte[] pubkey, byte[] msg, byte[] signature) throws MpayClientError {
        if (signature == null) {
            throw new MpayClientError(ErrorCode.E10400.getMessage());
        } else if (signature.length == 64) {
            return this.sm2VerifyWithSM3(pubkey, msg, signature);
        } else {
            byte[] rsSign = this.decodeDERSignature(signature);
            return this.sm2VerifyWithSM3(pubkey, msg, rsSign);
        }
    }

    private byte[] decodeDERSignature(byte[] signature) throws MpayClientError {
        try {
            ASN1InputStream stream = new ASN1InputStream(new ByteArrayInputStream(signature));
            Throwable var3 = null;

            byte[] var11;
            try {
                ASN1Sequence primitive = (ASN1Sequence)stream.readObject();
                Enumeration enumeration = primitive.getObjects();
                BigInteger R = ((ASN1Integer)enumeration.nextElement()).getValue();
                BigInteger S = ((ASN1Integer)enumeration.nextElement()).getValue();
                byte[] bytes = new byte[64];
                byte[] r = this.format(R.toByteArray());
                byte[] s = this.format(S.toByteArray());
                System.arraycopy(r, 0, bytes, 0, 32);
                System.arraycopy(s, 0, bytes, 32, 32);
                var11 = bytes;
            } catch (Throwable var21) {
                var3 = var21;
                throw var21;
            } finally {
                if (stream != null) {
                    if (var3 != null) {
                        try {
                            stream.close();
                        } catch (Throwable var20) {
                            var3.addSuppressed(var20);
                        }
                    } else {
                        stream.close();
                    }
                }

            }

            return var11;
        } catch (Exception var23) {
            throw new MpayClientError(ErrorCode.E10501.getMessage(), var23);
        }
    }

    private byte[] encodeDERSignature(byte[] signature) throws MpayClientError {
        byte[] r = new byte[32];
        byte[] s = new byte[32];
        System.arraycopy(signature, 0, r, 0, 32);
        System.arraycopy(signature, 32, s, 0, 32);
        ASN1EncodableVector vector = new ASN1EncodableVector();
        vector.add(new ASN1Integer(new BigInteger(1, r)));
        vector.add(new ASN1Integer(new BigInteger(1, s)));

        try {
            return (new DERSequence(vector)).getEncoded();
        } catch (IOException var6) {
            throw new MpayClientError(ErrorCode.E10501.getMessage(), var6);
        }
    }

    static {
        System.setProperty("org.bouncycastle.asn1.allow_unsafe_integer", "true");
    }
}
