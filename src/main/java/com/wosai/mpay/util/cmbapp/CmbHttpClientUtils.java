package com.wosai.mpay.util.cmbapp;

import cn.hutool.core.date.StopWatch;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiReadError;
import com.wosai.mpay.exception.MpayApiSendError;
import kl.okhttp3.*;
import kl.okhttp3.internal.Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

public class CmbHttpClientUtils {

    public static final Logger logger = LoggerFactory.getLogger(CmbHttpClientUtils.class);

    private final static Map<String, OkHttpClient> httpClientManagers = new ConcurrentHashMap<>();

    /**
     * post 请求
     *
     * @param url
     * @param paramsUrlEncode
     * @param clientCallback
     * @return
     * @throws MpayApiNetworkError
     */
    public static String doPost(
            String clientName,
            String url,
            String paramsUrlEncode,
            Function<String, OkHttpClient> clientCallback) throws Exception {
        StopWatch stopWatch = new StopWatch("doPost");
        OkHttpClient client = httpClientManagers.computeIfAbsent(clientName, clientCallback);
        RequestBody body = RequestBody.create(MediaType.parse("application/x-www-form-urlencoded"), paramsUrlEncode);
        Request.Builder builders = new Request.Builder().url(url).post(body);
        Request request = builders.build();
        String responseStr;
        Response response = null;
        try {
            try {
                stopWatch.start();
                response = client.newCall(request).execute();
            } catch (IOException e) {
                throw new MpayApiSendError("failed to send request", e);
            }
            if (!response.isSuccessful()) {
                throw new MpayApiReadError("failed to send request", new Exception(response.toString()));
            }
            ResponseBody responseBody = response.body();
            if (Objects.isNull(responseBody)) {
                throw new MpayApiReadError("failed to send request", new Exception(response.toString()));
            }
            try {
                responseStr = new String(responseBody.bytes(), StandardCharsets.UTF_8);
            } catch (IOException e) {
                throw new MpayApiReadError("failed to read response", e);
            }
            if (logger.isDebugEnabled()) {
                logger.debug("response: {}", responseStr);
            }
        } finally {
            Util.closeQuietly(response);
            stopWatch.stop();
            if (logger.isDebugEnabled()) {
                logger.debug("url:{}, cost:{}", url, stopWatch);
            }
        }
        return responseStr;
    }
}
