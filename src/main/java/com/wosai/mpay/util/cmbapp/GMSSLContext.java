package com.wosai.mpay.util.cmbapp;


import kl.ssl.gmvpn.crypto.TlsCrypto;
import kl.ssl.gmvpn.crypto.impl.jcajce.JcaTlsCertificate;
import kl.ssl.gmvpn.crypto.impl.jcajce.JcaTlsCryptoProvider;
import kl.ssl.jsse.provider.KlGMJsseProvider;
import kl.ssl.jsse.provider.ProvX509TrustManager;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.net.ssl.*;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.SecureRandom;
import java.security.Security;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class GMSSLContext {
    private static final TlsCrypto CRYPTO;
    private String[] certChain;
    private TrustManager[] trustManagers;
    private SSLContext sslContext;
    List<String> certList = new ArrayList();

    public TrustManager[] getTrustManagers() {
        return this.trustManagers;
    }

    public SSLContext getSslContext() {
        return this.sslContext;
    }

    public GMSSLContext(String[] certChain) {
        this.certChain = certChain;
        this.initSSLContext();
    }

    public void initSSLContext() {
        if (this.certChain != null && this.certChain.length > 0) {
            Arrays.stream(this.certChain).forEach((certx) -> {
                this.certList.add(CertUtil.formatCert(certx));
            });
        } else {
            ProvX509TrustManager.validateCertChain = false;
            this.certList.add(CertUtil.formatCert("MIIB6zCCAZGgAwIBAgIJAKgrGNzYxOMDMAoGCCqBHM9VAYN1MFIxCzAJBgNVBAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBXaWRnaXRzIFB0eSBMdGQxCzAJBgNVBAMMAkNBMB4XDTE2MTExMDA4MzY0MFoXDTI2MTExMDA4MzY0MFowUjELMAkGA1UEBhMCQVUxEzARBgNVBAgMClNvbWUtU3RhdGUxITAfBgNVBAoMGEludGVybmV0IFdpZGdpdHMgUHR5IEx0ZDELMAkGA1UEAwwCQ0EwWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAARX1VACQONhW7g0qxDIRvo04TqEO2sfs2wOaSMNRzzWe9rUwQIAg9APtrY/ptfgVVSNukdBfNkR3GJNf+UOJA9vo1AwTjAdBgNVHQ4EFgQUJxjEUs00BMH5dvcG6MgThqZwYdIwHwYDVR0jBBgwFoAUJxjEUs00BMH5dvcG6MgThqZwYdIwDAYDVR0TBAUwAwEB/zAKBggqgRzPVQGDdQNIADBFAiBKdKEHCr1jqdnArxfPVQb+SJYazCcoB8l9r64EEr60lwIhAPE1gU+f6bVEpTIEsNsUVKc6XuyKhu599/wqLkHLVhF7"));
        }

        TrustManagerFactory trustMgrFact;
        try {
            trustMgrFact = constructTrustManagerFactory(this.certList);
        } catch (Exception var13) {
            throw new RuntimeException("client construct cert chain error", var13);
        }

        KeyManagerFactory keyMgrFact = null;

        try {
            this.sslContext = SSLContext.getInstance("GMVPNv1.1", "KLGMJSSE");
            this.trustManagers = trustMgrFact.getTrustManagers();
            KeyManager[] keyManagers = null;
            if (keyMgrFact != null) {
                keyManagers = keyMgrFact.getKeyManagers();
            }

            this.sslContext.init(keyManagers, this.trustManagers, new SecureRandom());
        } catch (Exception var11) {
            throw new RuntimeException("create sslContext error", var11);
        }
    }

    private static TrustManagerFactory constructTrustManagerFactory(List<String> certList) throws Exception {
        KeyStore trustStore = KeyStore.getInstance("BKS");
        trustStore.load((InputStream)null, (char[])null);
        setKeyStore(trustStore, certList);
        TrustManagerFactory trustMgrFact = TrustManagerFactory.getInstance("PKIX", "KLGMJSSE");
        trustMgrFact.init(trustStore);
        return trustMgrFact;
    }

    private static void setKeyStore(KeyStore trustStore, List<String> certList) throws IOException, KeyStoreException {
        for(int i = 0; i < certList.size(); ++i) {
            JcaTlsCertificate tlsCertificate = CertUtil.loadJcaTlsCertificate(CRYPTO, (String)certList.get(i));
            X509Certificate caCert = tlsCertificate.getX509Certificate();
            trustStore.setCertificateEntry("ca" + i, caCert);
        }

    }

    static {
        Security.removeProvider(BouncyCastleProvider.PROVIDER_NAME);
        Security.insertProviderAt(new BouncyCastleProvider(), 1);
        JcaTlsCryptoProvider cryptoProvider = new JcaTlsCryptoProvider();
        cryptoProvider.setProvider(new BouncyCastleProvider());
        CRYPTO = cryptoProvider.create(new SecureRandom());
        Security.addProvider(new KlGMJsseProvider(cryptoProvider));
    }
}