package com.wosai.mpay.util.cmbapp;

import kl.okhttp3.ConnectionSpec;
import kl.okhttp3.OkHttpClient;
import kl.okhttp3.TlsVersion;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.util.Collections;

public class CmbLifeOkHttpClientBuilder {
    private String[] certChain;

    public CmbLifeOkHttpClientBuilder(String[] certChain) {
        this.certChain = certChain;
    }

    public OkHttpClient.Builder getOkHttpClientBuilder() {
        GMSSLContext sslContextWithSmf = new GMSSLContext(this.certChain);
        SSLContext sslContext = sslContextWithSmf.getSslContext();
        TrustManager[] trustManagers = sslContextWithSmf.getTrustManagers();
        ConnectionSpec spec = (new ConnectionSpec.Builder(ConnectionSpec.MODERN_TLS))
                .tlsVersions(TlsVersion.values())
                .cipherSuites("GMVPN_SM2_WITH_SM4_CBC_SM3")
                .build();
        HostnameVerifier hostnameVerifier = (s, sslSession) -> true;
        return (new OkHttpClient.Builder()).hostnameVerifier(hostnameVerifier).connectionSpecs(Collections.singletonList(spec))
                .sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustManagers[0]);
    }
}
