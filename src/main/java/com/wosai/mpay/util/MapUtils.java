package com.wosai.mpay.util;


import java.text.NumberFormat;
import java.text.ParseException;
import java.util.*;

public class MapUtils {

    public static Object getNestedProperty(Map<String, Object> value, String path) {
        if (value == null) {
            return null;
        }
        int index = path.indexOf(".");
        if (index < 0) {
            return value.get(path);
        }else{
            String prop = path.substring(0, index);
            Object subValue = value.get(prop);
            String subPath = path.substring(index+1);
            if (subValue instanceof Map) {
                return getNestedProperty((Map<String,Object>)subValue, subPath);
            }else{
                return null;
            }
        }
    }

    public static Map<String, Object> copyInclusive(Map<String, Object> from, String... keys) {
        Map<String, Object> to = new LinkedHashMap<>();
        for (String key : keys) {
            if (from.containsKey(key)) {
                to.put(key, from.get(key));
            }
        }
        return to;
    }

    public static Map<String, Object> copyExclusive(Map<String, Object> from, String... keys) {
        Map<String, Object> to = new LinkedHashMap<>(from);
        for (String key : keys) {
            to.remove(key);
        }
        return to;
    }

    public static void addKeysIfNotExist(
            Map<String, Object> from,
            Map<String, Object> to,
            String... keys
    ) {
        for (String key : keys) {
            if (from.containsKey(key) && !to.containsKey(key)) {
                to.put(key, from.get(key));
            }
        }
    }


    public static <K, V> Map<K, V> hashMap(K key, V value, Object... kvs) {
        Map<K, V> m = new LinkedHashMap<>();
        m.put(key, value);

        for (int i = 0; i < kvs.length / 2; ++i) {
            m.put((K) kvs[2 * i], (V) kvs[2 * i + 1]);
        }
        return m;
    }


    public static Object getObject(Map map, Object key) {
        return map != null ? map.get(key) : null;
    }

    public static String getString(Map map, Object key) {
        if (map != null) {
            Object answer = map.get(key);
            if (answer != null) {
                return answer.toString();
            }
        }

        return null;
    }

    public static Boolean getBoolean(Map map, Object key) {
        if (map != null) {
            Object answer = map.get(key);
            if (answer != null) {
                if (answer instanceof Boolean) {
                    return (Boolean) answer;
                }

                if (answer instanceof String) {
                    return new Boolean((String) answer);
                }

                if (answer instanceof Number) {
                    Number n = (Number) answer;
                    return n.intValue() != 0 ? Boolean.TRUE : Boolean.FALSE;
                }
            }
        }

        return null;
    }

    public static Number getNumber(Map map, Object key) {
        if (map != null) {
            Object answer = map.get(key);
            if (answer != null) {
                if (answer instanceof Number) {
                    return (Number) answer;
                }

                if (answer instanceof String) {
                    try {
                        String text = (String) answer;
                        return NumberFormat.getInstance().parse(text);
                    } catch (ParseException var4) {
                    }
                }
            }
        }

        return null;
    }

    public static Byte getByte(Map map, Object key) {
        Number answer = getNumber(map, key);
        if (answer == null) {
            return null;
        } else {
            return answer instanceof Byte ? (Byte) answer : new Byte(answer.byteValue());
        }
    }

    public static Short getShort(Map map, Object key) {
        Number answer = getNumber(map, key);
        if (answer == null) {
            return null;
        } else {
            return answer instanceof Short ? (Short) answer : new Short(answer.shortValue());
        }
    }

    public static Integer getInteger(Map map, Object key) {
        Number answer = getNumber(map, key);
        if (answer == null) {
            return null;
        } else {
            return answer instanceof Integer ? (Integer) answer : new Integer(answer.intValue());
        }
    }

    public static Long getLong(Map map, Object key) {
        Number answer = getNumber(map, key);
        if (answer == null) {
            return null;
        } else {
            return answer instanceof Long ? (Long) answer : new Long(answer.longValue());
        }
    }

    public static Float getFloat(Map map, Object key) {
        Number answer = getNumber(map, key);
        if (answer == null) {
            return null;
        } else {
            return answer instanceof Float ? (Float) answer : new Float(answer.floatValue());
        }
    }

    public static Double getDouble(Map map, Object key) {
        Number answer = getNumber(map, key);
        if (answer == null) {
            return null;
        } else {
            return answer instanceof Double ? (Double) answer : new Double(answer.doubleValue());
        }
    }

    public static Map getMap(Map map, Object key) {
        if (map != null) {
            Object answer = map.get(key);
            if (answer != null && answer instanceof Map) {
                return (Map) answer;
            }
        }

        return null;
    }

    public static Object getObject(Map map, Object key, Object defaultValue) {
        if (map != null) {
            Object answer = map.get(key);
            if (answer != null) {
                return answer;
            }
        }

        return defaultValue;
    }

    public static String getString(Map map, Object key, String defaultValue) {
        String answer = getString(map, key);
        if (answer == null) {
            answer = defaultValue;
        }

        return answer;
    }

    public static Boolean getBoolean(Map map, Object key, Boolean defaultValue) {
        Boolean answer = getBoolean(map, key);
        if (answer == null) {
            answer = defaultValue;
        }

        return answer;
    }

    public static Number getNumber(Map map, Object key, Number defaultValue) {
        Number answer = getNumber(map, key);
        if (answer == null) {
            answer = defaultValue;
        }

        return answer;
    }

    public static Byte getByte(Map map, Object key, Byte defaultValue) {
        Byte answer = getByte(map, key);
        if (answer == null) {
            answer = defaultValue;
        }

        return answer;
    }

    public static Short getShort(Map map, Object key, Short defaultValue) {
        Short answer = getShort(map, key);
        if (answer == null) {
            answer = defaultValue;
        }

        return answer;
    }

    public static Integer getInteger(Map map, Object key, Integer defaultValue) {
        Integer answer = getInteger(map, key);
        if (answer == null) {
            answer = defaultValue;
        }

        return answer;
    }

    public static Long getLong(Map map, Object key, Long defaultValue) {
        Long answer = getLong(map, key);
        if (answer == null) {
            answer = defaultValue;
        }

        return answer;
    }

    public static Float getFloat(Map map, Object key, Float defaultValue) {
        Float answer = getFloat(map, key);
        if (answer == null) {
            answer = defaultValue;
        }

        return answer;
    }

    public static Double getDouble(Map map, Object key, Double defaultValue) {
        Double answer = getDouble(map, key);
        if (answer == null) {
            answer = defaultValue;
        }

        return answer;
    }

    public static Map getMap(Map map, Object key, Map defaultValue) {
        Map answer = getMap(map, key);
        if (answer == null) {
            answer = defaultValue;
        }

        return answer;
    }

    public static boolean getBooleanValue(Map map, Object key) {
        Boolean booleanObject = getBoolean(map, key);
        return booleanObject == null ? false : booleanObject;
    }

    public static byte getByteValue(Map map, Object key) {
        Byte byteObject = getByte(map, key);
        return byteObject == null ? 0 : byteObject;
    }

    public static short getShortValue(Map map, Object key) {
        Short shortObject = getShort(map, key);
        return shortObject == null ? 0 : shortObject;
    }

    public static int getIntValue(Map map, Object key) {
        Integer integerObject = getInteger(map, key);
        return integerObject == null ? 0 : integerObject;
    }

    public static long getLongValue(Map map, Object key) {
        Long longObject = getLong(map, key);
        return longObject == null ? 0L : longObject;
    }

    public static float getFloatValue(Map map, Object key) {
        Float floatObject = getFloat(map, key);
        return floatObject == null ? 0.0F : floatObject;
    }

    public static double getDoubleValue(Map map, Object key) {
        Double doubleObject = getDouble(map, key);
        return doubleObject == null ? 0.0D : doubleObject;
    }

    public static boolean getBooleanValue(Map map, Object key, boolean defaultValue) {
        Boolean booleanObject = getBoolean(map, key);
        return booleanObject == null ? defaultValue : booleanObject;
    }

    public static byte getByteValue(Map map, Object key, byte defaultValue) {
        Byte byteObject = getByte(map, key);
        return byteObject == null ? defaultValue : byteObject;
    }

    public static short getShortValue(Map map, Object key, short defaultValue) {
        Short shortObject = getShort(map, key);
        return shortObject == null ? defaultValue : shortObject;
    }

    public static int getIntValue(Map map, Object key, int defaultValue) {
        Integer integerObject = getInteger(map, key);
        return integerObject == null ? defaultValue : integerObject;
    }

    public static long getLongValue(Map map, Object key, long defaultValue) {
        Long longObject = getLong(map, key);
        return longObject == null ? defaultValue : longObject;
    }

    public static float getFloatValue(Map map, Object key, float defaultValue) {
        Float floatObject = getFloat(map, key);
        return floatObject == null ? defaultValue : floatObject;
    }

    public static double getDoubleValue(Map map, Object key, double defaultValue) {
        Double doubleObject = getDouble(map, key);
        return doubleObject == null ? defaultValue : doubleObject;
    }

    public static Properties toProperties(Map map) {
        Properties answer = new Properties();
        if (map != null) {
            Iterator iter = map.entrySet().iterator();

            while (iter.hasNext()) {
                Map.Entry entry = (Map.Entry) iter.next();
                Object key = entry.getKey();
                Object value = entry.getValue();
                answer.put(key, value);
            }
        }

        return answer;
    }

    public static Map toMap(ResourceBundle resourceBundle) {
        Enumeration enumeration = resourceBundle.getKeys();
        HashMap map = new HashMap();

        while (enumeration.hasMoreElements()) {
            String key = (String) enumeration.nextElement();
            Object value = resourceBundle.getObject(key);
            map.put(key, value);
        }

        return map;
    }

    public static Map invertMap(Map map) {
        Map out = new HashMap(map.size());
        Iterator it = map.entrySet().iterator();

        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            out.put(entry.getValue(), entry.getKey());
        }

        return out;
    }

    public static void safeAddToMap(Map map, Object key, Object value) throws NullPointerException {
        if (value == null) {
            map.put(key, "");
        } else {
            map.put(key, value);
        }

    }

    public static boolean isEmpty(Map map) {
        return map == null || map.isEmpty();
    }

    public static boolean isNotEmpty(Map map) {
        return !isEmpty(map);
    }

    public static Map synchronizedMap(Map map) {
        return Collections.synchronizedMap(map);
    }

    /**
     *
     * @param map
     * @param keys 需要打码（星号）的字段名称
     * @return
     */
    public static <T> Map copyAndMaskSensitiveInfo(Map<String,T> map, List<String> keys){
        if(map == null){
            return null;
        }
        Map<String,Object> clone = new HashMap<>();
        for (String key : map.keySet()) {
            Object valueO = map.get(key);
            if(keys.contains(key)){
                clone.put(key, "*");
            }else if(valueO instanceof Map){
                clone.put(key, copyAndMaskSensitiveInfo((Map<String, Object>) valueO, keys));
            }else{
                clone.put(key, valueO);
            }
        }
        return clone;
    }

    /**
     * 注意，此方法只会深拷贝map类型的value, 其他对象还是浅拷贝
     * @param original
     * @return
     */
    public static Map<String, Object> clone(Map<String, Object> original) {
        if(original == null){
            return null;
        }
        Map<String,Object> target = new HashMap<String,Object>();
        synchronized (original){
            for(String key: original.keySet()){
                Object value = original.get(key);
                if(value instanceof Map){
                    target.put(key, clone((Map<String, Object>) value));
                }else{
                    target.put(key, value);
                }
            }
        }
        return target;
    }

}
