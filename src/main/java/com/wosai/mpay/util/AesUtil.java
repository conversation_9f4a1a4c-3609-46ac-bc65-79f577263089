package com.wosai.mpay.util;

import com.wosai.mpay.exception.MpayException;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

public class AesUtil {

    public static String AES = "AES";
    static final int KEY_LENGTH_BYTE = 32;
    static final int KEY_LENGTH_BYTE_V2 = 24;
    static final int TAG_LENGTH_BIT = 128;
    private static final String CHARSET_ENCODING = "UTF-8";
    private static final String CIPHER_ALGORITHM_CBC = "AES/CBC/PKCS5Padding";
    private static final String CHARSET_NAME = "ASCII";

    public static String decryptToString(String aesKey ,String associatedData, String nonce, String ciphertext) throws GeneralSecurityException {
        try {
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            SecretKeySpec key = new SecretKeySpec(aesKey.getBytes(), "AES");
            GCMParameterSpec spec = new GCMParameterSpec(TAG_LENGTH_BIT, nonce.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, key, spec);
            cipher.updateAAD(associatedData.getBytes());
            return new String(cipher.doFinal(Base64.decode(ciphertext)), StandardCharsets.UTF_8);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException e) {
            throw new IllegalStateException(e);
        } catch (InvalidKeyException | InvalidAlgorithmParameterException e) {
            throw new IllegalArgumentException(e);
        }
    }

    public static byte[] encryptMode(byte[] keystr, byte[] text, Cipher cipher) throws Exception {
        String ivStr = "0000000000000000";
        byte[] keyBytes = new byte[16];
        byte[] ivBytes = new byte[16];
        byte[] v = ivStr.getBytes("UTF-8");
        int len = keystr.length;
        int len2 = v.length;
        if (len > keyBytes.length) {
            len = keyBytes.length;
        }

        if (len2 > ivBytes.length) {
            len2 = ivBytes.length;
        }

        System.arraycopy(keystr, 0, keyBytes, 0, len);
        System.arraycopy(v, 0, ivBytes, 0, len2);
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);
        cipher.init(1, keySpec, ivSpec);
        byte[] results = cipher.doFinal(text);
        return results;
    }

    public static byte[] decryptMode(byte[] keystr, byte[] text, Cipher cipher, byte[] iv) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(keystr, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        cipher.init(2, keySpec, ivSpec);
        byte[] results = cipher.doFinal(text);
        return results;
    }

    /**
     * 加密
     *
     * @param data
     * @param appSecret
     * @return
     * @throws MpayException
     */
    public static String encrypt(String data, String appSecret) throws MpayException {
        try {
            String key = appSecret.substring(0, KEY_LENGTH_BYTE_V2);
            String iv = appSecret.substring(KEY_LENGTH_BYTE_V2);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM_CBC);
            byte[] raw = key.getBytes();
            SecretKeySpec keySpec = new SecretKeySpec(raw, AES);
            IvParameterSpec ivParam = new IvParameterSpec(iv.getBytes());
            cipher.init(1, keySpec, ivParam);
            byte[] encrypted = cipher.doFinal(data.getBytes(CHARSET_ENCODING));
            return org.apache.commons.codec.binary.Base64.encodeBase64String(encrypted);
        } catch (Exception e) {
            throw new MpayException("加密异常:" + e.getMessage());
        }
    }

    /**
     * 解密
     *
     * @param data
     * @return
     * @throws MpayException
     */
    public static String decrypt(String data, String appSecret) throws MpayException {
        try {
            String key = appSecret.substring(0, KEY_LENGTH_BYTE_V2);
            String iv = appSecret.substring(KEY_LENGTH_BYTE_V2);
            byte[] raw = key.getBytes(CHARSET_NAME);
            SecretKeySpec keySpec = new SecretKeySpec(raw, AES);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM_CBC);
            IvParameterSpec ivParam = new IvParameterSpec(iv.getBytes());
            cipher.init(2, keySpec, ivParam);
            byte[] encrypted = org.apache.commons.codec.binary.Base64.decodeBase64(data);
            byte[] original = cipher.doFinal(encrypted);
            return new String(original, CHARSET_ENCODING);
        } catch (Exception e) {
            throw new MpayException("解密异常:" + e.getMessage());
        }
    }

}
