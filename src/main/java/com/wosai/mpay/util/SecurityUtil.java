package com.wosai.mpay.util;

import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.Signature;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/12/11.
 */
public class SecurityUtil {
    public static final ThreadLocal<Map<String,KeyFactory>> keyFactoryMapThreadLocal = new ThreadLocal<Map<String,KeyFactory>>();
    public static final ThreadLocal<Map<String, Signature>> signatureMapThreadLocal = new ThreadLocal<Map<String, Signature>>();
    public static final ThreadLocal<Map<String, MessageDigest>> messageDigestMapThreadLocal = new ThreadLocal<Map<String, MessageDigest>>();


    public static KeyFactory getKeyFactory(String algorithm) throws NoSuchAlgorithmException {
        Map<String,KeyFactory> keyFactoryMap = keyFactoryMapThreadLocal.get();
        if(keyFactoryMap == null){
            keyFactoryMap = new HashMap<String,KeyFactory>();
            keyFactoryMapThreadLocal.set(keyFactoryMap);
        }
        KeyFactory keyFactory = keyFactoryMap.get(algorithm);
        if(keyFactory == null){
            keyFactory = KeyFactory.getInstance(algorithm);
            keyFactoryMap.put(algorithm, keyFactory);
        }
        return keyFactory;
    }

    public static Signature getSignature(String sigAlgName) throws NoSuchAlgorithmException {
        Map<String,Signature> signatureMap = signatureMapThreadLocal.get();
        if(signatureMap == null){
            signatureMap = new HashMap<String,Signature>();
            signatureMapThreadLocal.set(signatureMap);
        }
        Signature signature  = signatureMap.get(sigAlgName);
        if(signature == null){
            signature = Signature.getInstance(sigAlgName);
            signatureMap.put(sigAlgName, signature);
        }
        return signature;
    }

    public static MessageDigest getMessageDigest(String algorithm) throws NoSuchAlgorithmException {
        Map<String,MessageDigest> messageDigestMap = messageDigestMapThreadLocal.get();
        if(messageDigestMap == null){
            messageDigestMap = new HashMap<String,MessageDigest>();
            messageDigestMapThreadLocal.set(messageDigestMap);
        }
        MessageDigest messageDigest  = messageDigestMap.get(algorithm);
        if(messageDigest == null){
            messageDigest = MessageDigest.getInstance(algorithm);
            messageDigestMap.put(algorithm, messageDigest);
        }
        return messageDigest;
    }

    public static void main(String[] args) throws NoSuchAlgorithmException {
        SecurityUtil.getKeyFactory("RSA");
    }
}
