package com.wosai.mpay.util.hxbank;

import com.wosai.mpay.util.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.security.Security;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;

/**
 * <AUTHOR>
 * @Description EnvApplication
 * @Date 2021/8/23 6:25 PM
 */
public class EnvApplication {
    public static final Logger log = LoggerFactory.getLogger(EnvApplication.class);

    private CertificateFactory cf = null;

    public EnvApplication() {
        try {
            Security.addProvider(new BouncyCastleProvider());
            this.cf = CertificateFactory.getInstance("X.509", "BC");
            log.info("EnvApplication初始化成功!");
        } catch (Exception var2) {
            log.error("EnvApplication初始化异常：" + var2.getMessage());
        }

    }

    public String getCert(String cerPath) {
        String result = "";
        try {
            FileInputStream fileInputStream = new FileInputStream(cerPath);
            byte[] b = new byte[fileInputStream.available()];
            fileInputStream.read(b);
            fileInputStream.close();
            result = (new BASE64Encoder()).encode(b);
        } catch (Exception var6) {
            log.error("获取证书失败：" + var6.getMessage());
        }

        return result;
    }

    public String makeEnvelope(String cer, String alg, byte[] data) {
        String base64Res = "";
        try {
            byte[] certBytes = Base64.decode(cer);
            InputStream inputStream = new ByteArrayInputStream(certBytes);
            X509Certificate cert = (X509Certificate) this.cf.generateCertificate(inputStream);
            X509Certificate[] certs = new X509Certificate[]{cert};
            JPkcs7 jPkcs7 = new JPkcs7();
            Cipher sessionKeyCipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            Cipher sealOrignalChipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            byte[] resBytes = jPkcs7.makePKCS7ENC(certs, data, alg, sessionKeyCipher, sealOrignalChipher);
            base64Res = Base64.encode(resBytes);
            return base64Res;
        } catch (Exception var14) {
            var14.printStackTrace();
            log.error("信封加密失败：" + var14.getMessage());
            return base64Res;
        }

    }

    public byte[] openEnvelope(byte[] keyBytes, String data) {
        byte[] original = null;
        try {
            JPkcs7 jPkcs7 = new JPkcs7();
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
            original = jPkcs7.openPKCS7ENC(keyBytes, data, cipher);
            return original;
        } catch (Exception var10) {
            log.error("信封解密失败：" + var10.getMessage());
            return original;
        }
    }
}
