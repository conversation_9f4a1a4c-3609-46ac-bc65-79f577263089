package com.wosai.mpay.util;


import org.bouncycastle.asn1.x509.SubjectPublicKeyInfo;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringReader;
import java.nio.charset.Charset;
import java.security.*;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.KeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Enumeration;

/**
 * <AUTHOR> Date: 2020/2/19 Time: 3:49 下午
 */
public class CipherUtils {
    private static final String DEFAULT_CHARSET = "UTF-8";
    private static final int TAG_LENGTH_BIT = 128;
    private static final String EMPTY = "";

    private CipherUtils() {}

    public static String rsaEncryptOAEP(String message, String charset, byte[] publicKeyBytes) {
        PublicKey publicKey = genPublicKey(publicKeyBytes);
        return rsaEncryptOAEP(message, charset, publicKey);
    }

    public static String rsaEncryptOAEP(String message, String charset, PublicKey publicKey) {
        if (charset == null || charset.isEmpty()) {
            charset = DEFAULT_CHARSET;
        }
        try {
            Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-1AndMGF1Padding");
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] data = message.getBytes(Charset.forName(charset));
            byte[] ciphertext = cipher.doFinal(data);
            return Base64.getEncoder().encodeToString(ciphertext);
        } catch (NoSuchPaddingException | NoSuchAlgorithmException var1) {
            throw new RuntimeException("当前Java环境不支持RSA v1.5/OAEP", var1);
        } catch (InvalidKeyException var2) {
            throw new IllegalArgumentException("无效的证书", var2);
        } catch (Exception e) {
            throw new RuntimeException("RSA加密失败", e);
        }
    }

    public static String rsaDecryptOAEP(String ciphertext, String charset, byte[] privateKeyBytes) {
        if (charset == null || charset.isEmpty()) {
            charset = DEFAULT_CHARSET;
        }
        try {
            Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-1AndMGF1Padding");
            cipher.init(Cipher.DECRYPT_MODE, genPrivateKey(privateKeyBytes));
            byte[] data = Base64.getDecoder().decode(ciphertext);
            return new String(cipher.doFinal(data), Charset.forName(charset));
        } catch (Exception e) {
            throw new RuntimeException("RSA解密失败", e);
        }
    }

    public static byte[] encryptSm4Cbc(byte[] plainText, byte[] key, byte[] iv) {
        SecretKeySpec sm4Key = new SecretKeySpec(key, "SM4");

        try {
            Cipher cipher = Cipher.getInstance("SM4/CBC/PKCS7Padding");
            int mode = Cipher.ENCRYPT_MODE;
            if (iv == null) {
                cipher.init(mode, sm4Key);
            } else {
                cipher.init(mode, sm4Key, new IvParameterSpec(iv));
            }

            return cipher.doFinal(plainText);
        } catch (NoSuchPaddingException | NoSuchAlgorithmException var1) {
            throw new RuntimeException("当前Java环境不支持SM4 CBC", var1);
        } catch (InvalidKeyException var2) {
            throw new IllegalArgumentException("无效的证书", var2);
        } catch (Exception e) {
            throw new RuntimeException("加密失败", e);
        }
    }

    public static byte[] decryptSm4Cbc(byte[] ciphertext, byte[] key, byte[] iv) {
        SecretKeySpec sm4Key = new SecretKeySpec(key, "SM4");

        try {
            Cipher cipher = Cipher.getInstance("SM4/CBC/PKCS7Padding");
            int mode = Cipher.DECRYPT_MODE;
            if (iv == null) {
                cipher.init(mode, sm4Key);
            } else {
                cipher.init(mode, sm4Key, new IvParameterSpec(iv));
            }

            return cipher.doFinal(ciphertext);
        } catch (NoSuchPaddingException | NoSuchAlgorithmException var1) {
            throw new RuntimeException("当前Java环境不支持SM4 CBC", var1);
        } catch (InvalidKeyException var2) {
            throw new IllegalArgumentException("无效的证书", var2);
        }  catch (Exception e) {
            throw new RuntimeException("解密失败", e);
        }
    }

    public static String aesEncrypt(byte[] aesKey, byte[] associatedData, byte[] nonce, String charset, String plaintext) {
        if (aesKey == null || (aesKey.length != 16 && aesKey.length != 24 && aesKey.length != 32)) {
            throw new IllegalArgumentException("密钥长度必须为16或24或32字节");
        }
        if (associatedData == null) {
            associatedData = EMPTY.getBytes();
        }
        if (nonce == null) {
            nonce = EMPTY.getBytes();
        }
        if (charset == null || charset.isEmpty()) {
            charset = DEFAULT_CHARSET;
        }
        try {
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            SecretKeySpec key = new SecretKeySpec(aesKey, "AES");
            GCMParameterSpec spec = new GCMParameterSpec(TAG_LENGTH_BIT, nonce);
            cipher.init(Cipher.ENCRYPT_MODE, key, spec);
            cipher.updateAAD(associatedData);
            return Base64.getEncoder().encodeToString(cipher.doFinal(plaintext.getBytes(Charset.forName(charset))));
        } catch (Exception e) {
            throw new RuntimeException("AES加密失败, ex: ", e);
        }
    }

    public static String aesDecrypt(byte[] aesKey, byte[] associatedData, byte[] nonce, String charset, String ciphertext) {
        if (aesKey == null || (aesKey.length != 16 && aesKey.length != 24 && aesKey.length != 32)) {
            throw new IllegalArgumentException("密钥长度必须为16或24或32字节");
        }
        if (associatedData == null) {
            associatedData = EMPTY.getBytes();
        }
        if (nonce == null) {
            nonce = EMPTY.getBytes();
        }
        if (charset == null || charset.isEmpty()) {
            charset = DEFAULT_CHARSET;
        }
        try {
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            SecretKeySpec key = new SecretKeySpec(aesKey, "AES");
            GCMParameterSpec spec = new GCMParameterSpec(TAG_LENGTH_BIT, nonce);
            cipher.init(Cipher.DECRYPT_MODE, key, spec);
            cipher.updateAAD(associatedData);
            return new String(cipher.doFinal(Base64.getDecoder().decode(ciphertext)), charset);
        } catch (Exception e) {
            throw new RuntimeException("AES解密失败, ex: ", e);
        }
    }

    private static PublicKey genPublicKey(byte[] publicKeyBytes) {
        try {
            KeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePublic(keySpec);
        } catch (Exception e) {
            throw new RuntimeException("生成RSA公钥失败, ex: ", e);
        }
    }

    public static PublicKey generatePublicKey(String publicKeyStr) {
        JcaPEMKeyConverter converter = (new JcaPEMKeyConverter()).setProvider("BC");

        try {
            PEMParser pemParser = new PEMParser(new StringReader(publicKeyStr));
            return converter.getPublicKey((SubjectPublicKeyInfo)pemParser.readObject());
        } catch (IOException var3) {
            throw new IllegalArgumentException("公钥加载失败，请检查环境配置", var3);
        }
    }

    private static PrivateKey genPrivateKey(byte[] privateKeyBytes) {
        try {
            KeySpec priKeySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePrivate(priKeySpec);
        } catch (Exception e) {
            throw new RuntimeException("生成RSA私钥失败, ex: ", e);
        }
    }

    public static String getPrivateKey(String file, String password) throws Exception {
        KeyPair kp = exKeyFromJks(file, password);
        if (kp != null) {
            PrivateKey private1 = kp.getPrivate();
            return com.wosai.mpay.util.Base64.encode(private1.getEncoded());
        } else {
            return null;
        }
    }

    public static byte[] getPrivateKeybyte(String file, String password) throws Exception {
        KeyPair kp = exKeyFromJks(file, password);
        if (kp != null) {
            PrivateKey private1 = kp.getPrivate();
            return private1.getEncoded();
        } else {
            return null;
        }
    }

    public static String getPublicKey(String file, String password) throws Exception {
        if (file.toUpperCase().endsWith(".JKS")) {
            KeyPair kp = exKeyFromJks(file, password);
            if (kp != null) {
                PublicKey public1 = kp.getPublic();
                return (new BASE64Encoder()).encodeBuffer(public1.getEncoded());
            }
        } else if (file.toUpperCase().endsWith(".CER")) {
            FileInputStream fis = new FileInputStream(file);
            X509Certificate certificate = readCer(fis);
            fis.close();
            PublicKey pubKey = certificate.getPublicKey();
            return (new BASE64Encoder()).encodeBuffer(pubKey.getEncoded());
        }

        return null;
    }

    public static KeyPair exKeyFromJks(String path, String password) throws Exception {
        FileInputStream is = new FileInputStream(path);
        KeyStore ks = KeyStore.getInstance("JKS");
        ks.load(is, password.toCharArray());
        is.close();
        Enumeration<?> enumas = ks.aliases();
        String alias = null;
        if (enumas.hasMoreElements()) {
            alias = (String)enumas.nextElement();
        }

        Key key = ks.getKey(alias, password.toCharArray());
        if (key instanceof PrivateKey) {
            Certificate cert = ks.getCertificate(alias);
            PublicKey publicKey = cert.getPublicKey();
            return new KeyPair(publicKey, (PrivateKey)key);
        } else {
            return null;
        }
    }

    public static X509Certificate readCer(InputStream in) throws CertificateException {
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
        return (X509Certificate)cf.generateCertificate(in);
    }

}
