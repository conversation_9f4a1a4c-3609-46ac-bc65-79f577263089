package com.wosai.mpay.util;


import com.wosai.mpay.model.applepay.ApplePayPayload;
import com.wosai.mpay.model.applepay.ApplePaymentToken;
import org.bouncycastle.asn1.ASN1ObjectIdentifier;
import org.bouncycastle.asn1.x509.Extension;
import org.bouncycastle.asn1.x509.Extensions;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter;
import org.bouncycastle.cms.CMSProcessableByteArray;
import org.bouncycastle.cms.CMSSignedData;
import org.bouncycastle.cms.SignerInformation;
import org.bouncycastle.cms.SignerInformationStore;
import org.bouncycastle.cms.jcajce.JcaSimpleSignerInfoVerifierBuilder;
import org.bouncycastle.openssl.PEMKeyPair;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.bouncycastle.util.Store;

import javax.crypto.Cipher;
import javax.crypto.KeyAgreement;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.*;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.Base64;

/**
 * https://developer.apple.com/documentation/passkit/payment-token-format-reference
 *
 */
public class ApplePayUtil {

    private static final String LEAF_CERTIFICATE_OID = "1.2.840.113635.100.6.29";
    private static final String INTERMEDIATE_CA_OID = "1.2.840.113635.**********";
    private static final String SIGNINGTIME_OID = "1.2.840.113549.1.9.5";
    private static final String MERCHANT_ID_FIELD_OID = "1.2.840.113635.100.6.32";
    private static final long TOKEN_EXPIRE_WINDOW = Long.MAX_VALUE; // should be set to several minutes per Apple

    // 私有构造函数，防止实例化
    private ApplePayUtil() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * 加载Apple根证书
     * @param rootCAPath Apple根证书路径
     * @return X509Certificate
     * @throws IOException
     * @throws CertificateException
     */
    public static X509Certificate loadAppleRootCA(String rootCAPath) throws IOException, CertificateException {
        return loadAppleRootCA(Files.readAllBytes(Paths.get(rootCAPath)));
    }

    /**
     * 加载Apple根证书
     * @param rootCABytes Apple根证书
     * @return X509Certificate
     * @throws IOException
     * @throws CertificateException
     */
    public static X509Certificate loadAppleRootCA(byte[] rootCABytes) throws CertificateException {
        return loadCertificate(rootCABytes);
    }

    /**
     * 加载证书通用方法
     * @param certPath
     * @return
     * @throws IOException
     * @throws CertificateException
     */
    public static X509Certificate loadCertificate(String certPath) throws IOException, CertificateException {
        return loadCertificate(Files.readAllBytes(Paths.get(certPath)));
    }

    /**
     * 加载证书通用方法
     * @param certBytes
     * @return
     * @throws CertificateException
     */
    public static X509Certificate loadCertificate(byte [] certBytes) throws CertificateException {
        CertificateFactory certFactory = CertificateFactory.getInstance("X.509");
        return (X509Certificate) certFactory.generateCertificate(new ByteArrayInputStream(certBytes));
    }

    /**
     * 加载私钥 pem 格式
     * @param keyPath
     * @return
     * @throws Exception
     */
    public static PrivateKey loadPrivateKeyByPath(String keyPath) throws Exception {
        return loadPrivateKey(new String(Files.readAllBytes(Paths.get(keyPath))));
    }

    /**
     * 加载私钥 pem 格式
     * @param keyContent
     * @return
     * @throws Exception
     */
    public static PrivateKey loadPrivateKey(String keyContent) throws Exception {
        // 移除PEM头尾和换行符
        if (keyContent.contains("-----BEGIN PRIVATE KEY-----")) {
            keyContent = keyContent
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s+", "");

            byte[] decodedKey = Base64.getDecoder().decode(keyContent);
            PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(decodedKey);
            KeyFactory keyFactory = KeyFactory.getInstance("EC");
            return keyFactory.generatePrivate(spec);
        } else if (keyContent.contains("-----BEGIN EC PRIVATE KEY-----")) {
            // 处理EC格式的私钥
            PEMParser pemParser = new PEMParser(new StringReader(keyContent));
            Object obj = pemParser.readObject();
            pemParser.close();
            JcaPEMKeyConverter converter = new JcaPEMKeyConverter();
            return converter.getPrivateKey(((PEMKeyPair) obj).getPrivateKeyInfo());
        } else {
            throw new Exception("Unsupported private key format");
        }
    }

    /**
     * 解密Apple Pay令牌
     * @param payload Apple Pay payload
     * @param publicCertPath 商家公钥证书路径
     * @param privateKeyPath 商家私钥路径
     * @param appleRootCAPath Apple根证书
     * @return 解密后的支付数据
     * @throws Exception
     */
    public static ApplePaymentToken decrypt(String payload, String publicCertPath, String privateKeyPath, String appleRootCAPath) throws Exception {
        X509Certificate merchantPublicCert = loadCertificate(publicCertPath);
        PrivateKey privateKey = loadPrivateKeyByPath(privateKeyPath);
        X509Certificate appleRootCA = loadAppleRootCA(appleRootCAPath);
        return decrypt(JsonUtil.jsonStrToObject(payload, ApplePayPayload.class), merchantPublicCert, privateKey, appleRootCA);
    }

    /**
     * 解密Apple Pay令牌
     * @param payload Apple Pay payload
     * @param merchantPublicCert 商家公钥证书路径
     * @param merchantPrivateKey 商家私钥路径
     * @param appleRootCA Apple根证书
     * @return 解密后的支付数据
     * @throws Exception
     */
    public static ApplePaymentToken decrypt(ApplePayPayload payload, X509Certificate merchantPublicCert, PrivateKey merchantPrivateKey, X509Certificate appleRootCA) throws Exception {
        // 解析token JSON
        // 1. 验证签名
        try {
            verifySignature(payload, appleRootCA);
        } catch (Exception e) {
            throw new Exception("Signature validation failed: " + e.getMessage(), e);
        }

        // 2. 使用publicKeyHash值确定使用了哪个商家公钥
        ApplePayPayload.Token.Header header = payload.getToken().getPaymentData().getHeader();
        String publicKeyHash = header.getPublicKeyHash();
        if (!checkPublicKeyHash(publicKeyHash, merchantPublicCert)) {
            throw new Exception("Public key hash does not match");
        }

        // 3. 恢复对称密钥
        String symmetricKey;
        try {
            String ephemeralPublicKey = header.getEphemeralPublicKey();
            symmetricKey = restoreSymmetricKey(ephemeralPublicKey, merchantPublicCert, merchantPrivateKey);
        } catch (Exception e) {
            throw new Exception("Restore symmetric key failed: " + e.getMessage(), e);
        }

        try {
            // 4. 使用对称密钥解密数据
            String data = payload.getToken().getPaymentData().getData();
            String decrypted = decryptCiphertextFunc(symmetricKey, data);
            return JsonUtil.jsonStringToObject(decrypted, ApplePaymentToken.class);
        } catch (Exception e) {
            throw new Exception("Decrypt cipher data failed: " + e.getMessage(), e);
        }
    }

    public static void verifySignature(ApplePayPayload payload, X509Certificate appleRootCA) throws Exception {
        // 构建签名数据
        ApplePayPayload.Token.Header header = payload.getToken().getPaymentData().getHeader();
        byte[] ephemeralPublicKeyBytes = java.util.Base64.getDecoder().decode(header.getEphemeralPublicKey());
        byte[] dataBytes = java.util.Base64.getDecoder().decode(payload.getToken().getPaymentData().getData());
        byte[] transactionIdBytes = hexStringToByteArray(header.getTransactionId());

        byte[] signedData = new byte[ephemeralPublicKeyBytes.length + dataBytes.length + transactionIdBytes.length];
        System.arraycopy(ephemeralPublicKeyBytes, 0, signedData, 0, ephemeralPublicKeyBytes.length);
        System.arraycopy(dataBytes, 0, signedData, ephemeralPublicKeyBytes.length, dataBytes.length);
        System.arraycopy(transactionIdBytes, 0, signedData, ephemeralPublicKeyBytes.length + dataBytes.length, transactionIdBytes.length);

        byte[] signatureBytes = java.util.Base64.getDecoder().decode(payload.getToken().getPaymentData().getSignature());

        // 解析CMS签名数据
        CMSSignedData cmsSignedData = new CMSSignedData(new CMSProcessableByteArray(signedData), signatureBytes);

        // 验证证书
        checkCertificates(cmsSignedData);

        // 验证签名
        validateSignature(cmsSignedData, appleRootCA);

        // 检查签名时间
        checkSigningTime(cmsSignedData);
    }

    private static void checkCertificates(CMSSignedData cmsSignedData) throws Exception {
        Store<X509CertificateHolder> certificates = cmsSignedData.getCertificates();
        Collection<X509CertificateHolder> certCollection = certificates.getMatches(null);

        if (certCollection.size() != 2) {
            throw new Exception("Signature certificates number error: expected 2 but got " + certCollection.size());
        }

        List<X509CertificateHolder> certList = new ArrayList<>(certCollection);

        // 检查叶子证书
        boolean leafHasExtension = false;
        Extensions extensions = certList.get(0).getExtensions();
        for (ASN1ObjectIdentifier oid : extensions.getExtensionOIDs()) {
            Extension ext = extensions.getExtension(oid);
            if (ext.getExtnId().getId().equals(LEAF_CERTIFICATE_OID)) {
                leafHasExtension = true;
                break;
            }
        }
        if (!leafHasExtension) {
            throw new Exception("Leaf certificate doesn't have extension: " + LEAF_CERTIFICATE_OID);
        }

        // 检查中间CA证书
        boolean intermediateHasExtension = false;
        Extensions extensions1 = certList.get(1).getExtensions();
        for (ASN1ObjectIdentifier oid : extensions1.getExtensionOIDs()) {
            Extension ext = extensions1.getExtension(oid);
            if (ext.getExtnId().getId().equals(INTERMEDIATE_CA_OID)) {
                intermediateHasExtension = true;
                break;
            }
        }
        if (!intermediateHasExtension) {
            throw new Exception("Intermediate certificate doesn't have extension: " + INTERMEDIATE_CA_OID);
        }
    }

    private static void validateSignature(CMSSignedData cmsSignedData, X509Certificate appleRootCA)  throws Exception {
        SignerInformationStore signers = cmsSignedData.getSignerInfos();
        SignerInformation signer = signers.getSigners().iterator().next();
        Store<X509CertificateHolder> certificates = cmsSignedData.getCertificates();
        Collection<X509CertificateHolder> certCollection = certificates.getMatches(signer.getSID());

        if (certCollection.isEmpty()) {
            throw new Exception("No matching certificate found for signer");
        }

        X509CertificateHolder certHolder = certCollection.iterator().next();
        X509Certificate cert = new JcaX509CertificateConverter().getCertificate(certHolder);

        // 验证证书链
        validateCertificateChain(cmsSignedData, appleRootCA);

        // 验证签名
        JcaSimpleSignerInfoVerifierBuilder bu = new JcaSimpleSignerInfoVerifierBuilder();
        boolean isVerified = signer.verify(bu.build(cert));

        if (!isVerified) {
            throw new Exception("CMS signed data verification failed");
        }
    }

    private static void validateCertificateChain(CMSSignedData cmsSignedData, X509Certificate appleRootCA) throws Exception {
        Store<X509CertificateHolder> certStore = cmsSignedData.getCertificates();
        Collection<X509CertificateHolder> certCollection = certStore.getMatches(null);

        List<X509Certificate> certificates = new ArrayList<>();
        for (X509CertificateHolder holder : certCollection) {
            certificates.add(new JcaX509CertificateConverter().getCertificate(holder));
        }

        // 添加根证书
        certificates.add(appleRootCA);

        // 创建证书库
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
        List<X509Certificate> chain = new ArrayList<>(certificates);

        // 验证证书链
        for (int i = 0; i < chain.size() - 1; i++) {
            X509Certificate cert = chain.get(i);
            X509Certificate issuer = chain.get(i + 1);

            cert.verify(issuer.getPublicKey());
        }
    }

    private static void checkSigningTime(CMSSignedData cmsSignedData) throws Exception {
        SignerInformationStore signers = cmsSignedData.getSignerInfos();
        SignerInformation signer = signers.getSigners().iterator().next();

        // 获取签名时间
        Date signingTime = null;
        if (signer.getSignedAttributes() != null) {
            ASN1ObjectIdentifier signingTimeOID = new ASN1ObjectIdentifier(SIGNINGTIME_OID);
            org.bouncycastle.asn1.cms.Attribute attr = signer.getSignedAttributes().get(signingTimeOID);
            if (attr != null) {
                signingTime = org.bouncycastle.asn1.cms.Time.getInstance(attr.getAttrValues().getObjectAt(0)).getDate();
            }
        }

        if (signingTime == null) {
            throw new Exception("Signing time not found in signature");
        }

        // 检查签名时间是否过期
        Date now = new Date();
        if (now.getTime() - signingTime.getTime() > TOKEN_EXPIRE_WINDOW) {
            throw new Exception("Signature has expired");
        }
    }

    private static boolean checkPublicKeyHash(String publicKeyHash, X509Certificate publicCert)
            throws NoSuchAlgorithmException {
        byte[] publicKeyInfo = publicCert.getPublicKey().getEncoded();
        MessageDigest sha256 = MessageDigest.getInstance("SHA-256");
        byte[] hash = sha256.digest(publicKeyInfo);
        String calculatedHash = java.util.Base64.getEncoder().encodeToString(hash);

        return calculatedHash.equals(publicKeyHash);
    }

    private static String restoreSymmetricKey(String ephemeralPublicKeyB64, X509Certificate publicCert, PrivateKey privateKey)
            throws Exception {
        // 生成共享密钥
        String sharedSecret = sharedSecretFunc(ephemeralPublicKeyB64, privateKey);

        // 获取商家ID
        String merchantId = merchantIdFunc(publicCert);

        // 派生对称密钥
        return symmetricKeyFunc(merchantId, sharedSecret);
    }

    private static String sharedSecretFunc(String ephemeralPublicKeyB64, PrivateKey privateKey)
            throws Exception {
        // 解析临时公钥
        byte[] ephemeralPublicKeyBytes = java.util.Base64.getDecoder().decode(ephemeralPublicKeyB64);
        KeyFactory keyFactory = KeyFactory.getInstance("EC");
        PublicKey ephemeralPublicKey = keyFactory.generatePublic(new X509EncodedKeySpec(ephemeralPublicKeyBytes));

        // 执行ECDH密钥协商
        KeyAgreement keyAgreement = KeyAgreement.getInstance("ECDH");
        keyAgreement.init(privateKey);
        keyAgreement.doPhase(ephemeralPublicKey, true);

        // 生成共享密钥
        byte[] sharedSecretBytes = keyAgreement.generateSecret();
        return bytesToHex(sharedSecretBytes);
    }

    private static String merchantIdFunc(X509Certificate publicCert) throws Exception {
        // 从证书中提取商家ID
        // new String(merchantIdExtValue) like B\f@7E04A998B88C29DFFBA1461DD6512F29BF69EF966642515F51C76475077B73DA
        byte[] merchantIdExtValue = publicCert.getExtensionValue(MERCHANT_ID_FIELD_OID);
        if (merchantIdExtValue == null) {
            throw new Exception("Unable to extract merchant ID from certificate");
        }

        // 解析扩展值（这里简化处理，实际上需要更复杂的ASN.1解析）
        // 跳过一些字节（ASN.1标记和长度）
        byte[] merchantIdBytes = Arrays.copyOfRange(merchantIdExtValue, 4, merchantIdExtValue.length);
        // new String(merchantIdExtValue) like 7E04A998B88C29DFFBA1461DD6512F29BF69EF966642515F51C76475077B73DA
        return new String(merchantIdBytes);
    }

    private static String symmetricKeyFunc(String merchantId, String sharedSecret)
            throws NoSuchAlgorithmException {
        // KDF参数
        String kdfAlgorithm = new String(new byte[] { 0x0D }, StandardCharsets.UTF_8) + "id-aes256-GCM"; // 0x0D后跟ASCII字符串
        byte[] kdfPartyV = hexStringToByteArray(merchantId);
        String kdfPartyU = "Apple";

        // 构建KDF信息
        byte[] kdfInfo = (kdfAlgorithm + kdfPartyU + new String(kdfPartyV, StandardCharsets.ISO_8859_1)).getBytes(StandardCharsets.ISO_8859_1);
        // 计算对称密钥
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        digest.update(new byte[] {0, 0, 0}); // 3个零字节
        digest.update(new byte[] {1});       // 计数器值1
        digest.update(hexStringToByteArray(sharedSecret));
        digest.update(kdfInfo);

        return bytesToHex(digest.digest());
    }

    private static String decryptCiphertextFunc(String symmetricKey, String data)
            throws Exception {
        byte[] encryptedData = java.util.Base64.getDecoder().decode(data);
        byte[] symmetricKeyBytes = hexStringToByteArray(symmetricKey);
        byte[] iv = new byte[16]; // 全零IV

        // 分离密文和认证标签
        byte[] ciphertext = Arrays.copyOfRange(encryptedData, 0, encryptedData.length - 16);
        byte[] tag = Arrays.copyOfRange(encryptedData, encryptedData.length - 16, encryptedData.length);

        // 创建AES-GCM密码
        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        SecretKeySpec keySpec = new SecretKeySpec(symmetricKeyBytes, "AES");
        GCMParameterSpec gcmParams = new GCMParameterSpec(128, iv);

        // 初始化解密
        cipher.init(Cipher.DECRYPT_MODE, keySpec, gcmParams);

        // 解密
        byte[] plaintext = new byte[0];
        try {
            cipher.updateAAD(new byte[0]); // 空AAD
            byte[] combined = new byte[ciphertext.length + tag.length];
            System.arraycopy(ciphertext, 0, combined, 0, ciphertext.length);
            System.arraycopy(tag, 0, combined, ciphertext.length, tag.length);
            plaintext = cipher.doFinal(combined);
        } catch (Exception e) {
            // 尝试替代方法
            cipher = Cipher.getInstance("AES/GCM/NoPadding");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, gcmParams);
            cipher.updateAAD(new byte[0]);
            cipher.update(ciphertext);
            plaintext = cipher.doFinal(tag);
        }

        return new String(plaintext, StandardCharsets.UTF_8);
    }




    private static byte[] hexStringToByteArray(String hexString) {
        int len = hexString.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                    + Character.digit(hexString.charAt(i + 1), 16));
        }
        return data;
    }

    private static String bytesToHex(byte[] bytes) {
        return Hex.encodeToString(bytes);
    }


}

