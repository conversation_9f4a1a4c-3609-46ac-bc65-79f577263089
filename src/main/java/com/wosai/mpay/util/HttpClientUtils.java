package com.wosai.mpay.util;

import com.wosai.mpay.api.alipay.*;
import com.wosai.mpay.api.ccb.CcbClient;
import com.wosai.mpay.api.common.HttpConstant;
import com.wosai.mpay.api.grabpay.GrabPayClient;
import com.wosai.mpay.api.spdb.SPDBClient;
import com.wosai.mpay.api.uqpay.UqpayClient;
import com.wosai.mpay.api.weixin.*;
import com.wosai.mpay.exception.*;
import okhttp3.*;
import okhttp3.internal.Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

public class HttpClientUtils {
	public static final String HEADERS_RESULT_FIELD = "headers";
	public static final String BODY_RESULT_FIELD = "body";

	private static final Integer DEFAULT_POLL_SIZE = 20;
	private static final Integer DEFAULT_KEEPALIVE_DURATION = 5;
	private static final Logger logger = LoggerFactory.getLogger(HttpClientUtils.class);
	private static Map<String, OkHttpClient> httpClientManagers = new ConcurrentHashMap<String, OkHttpClient>();

	public static OkHttpClient getHttpClient(String clientName, SSLContext sslContext, HostnameVerifier verifier, int connectTimeout, int readTimeout) {
		return getHttpClient(clientName, sslContext, verifier, connectTimeout, readTimeout, null, null);
	}

	public static OkHttpClient getHttpClient(String clientName, SSLContext sslContext, HostnameVerifier verifier, int connectTimeout, int readTimeout, Integer disPatcherMaxRequest, Integer dispatcherMaxRequestPerRoute) {
        StringBuilder poolNameBuilder = new StringBuilder();
        poolNameBuilder.append(clientName);
        poolNameBuilder.append(connectTimeout);
        poolNameBuilder.append(readTimeout);
        poolNameBuilder.append(disPatcherMaxRequest);
        poolNameBuilder.append(dispatcherMaxRequestPerRoute);
		// 同一个client使用的不一样的证书时，需要使用不同的线程池
		if(null != sslContext){
		    poolNameBuilder.append(sslContext.hashCode());
		}
		String poolName = poolNameBuilder.toString();
		if(null != httpClientManagers.get(poolName)){
			return httpClientManagers.get(poolName);
		}

		synchronized (poolName.intern()) {
			if(null == httpClientManagers.get(poolName)){
				OkHttpClient.Builder builder= new OkHttpClient.Builder()
						.connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
						.readTimeout(readTimeout, TimeUnit.MILLISECONDS)
						.retryOnConnectionFailure(true);
				long keepAliveDuration = DEFAULT_KEEPALIVE_DURATION;
				if(!StringUtils.isEmpty(System.getProperty("pool-keepAliveDuration"))){
					keepAliveDuration = Long.parseLong(System.getProperty("pool-keepAliveDuration"));
				}
				String poolSize = "";
				if(null != sslContext){
					poolSize = System.getProperty("pool-" + clientName.replaceAll(sslContext.hashCode()+"", ""));
				}else {
					poolSize = System.getProperty("pool-" + clientName);
				}

				int maxIdleConnections = DEFAULT_POLL_SIZE;
				if(StringUtils.isEmpty(poolSize)){
					if(clientName.indexOf(WeixinClient.class.getName()) >= 0){
						maxIdleConnections = DEFAULT_POLL_SIZE * 80;

					}if(clientName.indexOf(AlipayV2NewClient.class.getName()) >= 0){
						maxIdleConnections = DEFAULT_POLL_SIZE * 60;

					}
				}else{
					maxIdleConnections = Integer.parseInt(poolSize);

				}
				builder.connectionPool(new ConnectionPool(maxIdleConnections, keepAliveDuration, TimeUnit.MINUTES));

				if (disPatcherMaxRequest != null && disPatcherMaxRequest > 0
						&& dispatcherMaxRequestPerRoute != null && dispatcherMaxRequestPerRoute > 0) {
					Dispatcher dispatcher = new Dispatcher();
					dispatcher.setMaxRequests(disPatcherMaxRequest);
					dispatcher.setMaxRequestsPerHost(dispatcherMaxRequestPerRoute);
					builder.dispatcher(dispatcher);
				}

				if(null != sslContext){
					builder.sslSocketFactory(sslContext.getSocketFactory(), buildX509TrustManager());
				}
				if(null != verifier){
					builder.hostnameVerifier(verifier);
				}
				httpClientManagers.put(poolName, builder.build());
			}
		}

		return httpClientManagers.get(poolName);
	}

	public static String doPost(String clientName, SSLContext ctx, HostnameVerifier verifier, String url, String ctype, String content, final String charset, int connectTimeout, int readTimeout) throws MpayApiNetworkError{
		return doPost(clientName, ctx, verifier, url, ctype, content, null, charset, connectTimeout, readTimeout);
	}

	public static String doPost(String clientName, SSLContext ctx, HostnameVerifier verifier, String url, String ctype, String content, Map<String, String> header, final String charset, int connectTimeout, int readTimeout) throws MpayApiNetworkError {
		return doPost(clientName, ctx, verifier, url, ctype, content, header, charset, connectTimeout, readTimeout, false);
	}

	public static String doPost(String clientName, SSLContext ctx, HostnameVerifier verifier, String url, String ctype, String content, Map<String, String> header, final String charset, int connectTimeout, int readTimeout, boolean ignoreHttpStatus) throws MpayApiNetworkError {
		OkHttpClient client = getHttpClient(clientName, ctx, verifier, connectTimeout, readTimeout);

		Request.Builder requestBuilder = new Request.Builder()
				.url(url)
				.post(RequestBody.create(MediaType.parse(ctype + "; charset=" + charset), content));

		if (header != null && header.size() > 0) {
			requestBuilder.headers(Headers.of(header));
		}

		long start = System.currentTimeMillis();
		Response response = null;
		Request req = requestBuilder.build();

		try {
			try {
				response = client.newCall(req).execute();
			} catch (IOException e) {
				throw new MpayApiSendError("failed to send request", e);
			}
			if(!response.isSuccessful() && !ignoreHttpStatus){
                if (GrabPayClient.class.getName().equals(clientName) || UqpayClient.class.getName().equals(clientName)) {
					String responseStr;
					try {
						responseStr = response.body() != null ? new String(response.body().bytes(), charset) : "";
					} catch (IOException e) {
						throw new RuntimeException("failed to parse response body");
					}
					throw new HttpFailException(response.code(), responseStr);
				} else {
					throw new MpayApiReadError("failed to send request", new Exception(response.toString()));
				}
			}
			try {
				return new String(response.body().bytes(), charset);
			} catch (IOException e) {
				throw new MpayApiReadError("failed to read response", e);
			}
		}finally {
			closeResponse(response);
			long end = System.currentTimeMillis();
			if(logger.isDebugEnabled()){
				logger.debug("cost: {} ms, url: {}", end - start, url);
			}
		}
	}

	public static String doGet(String clientName, SSLContext ctx, HostnameVerifier verifier, String url, Map request, final String charset, int connectTimeout, int readTimeout) throws MpayApiNetworkError{
		OkHttpClient client = getHttpClient(clientName, ctx, verifier, connectTimeout, readTimeout);
		long start = System.currentTimeMillis();
		Response response = null;
		try{
			Request req = null;
			try {
				req = new Request.Builder()
						.url(WebUtils.buildGetUrl(url, WebUtils.buildQuery(request, charset)))
						.get()
						.build();
			} catch (IOException e) {
				throw new MpayApiSendError("failed to buildQuery request", e);
			}
			try {
				response = client.newCall(req).execute();
			} catch (IOException e) {
				throw new MpayApiSendError("failed to send request", e);
			}
			if(!response.isSuccessful()){
				throw new MpayApiSendError("failed to send request", new Exception(response.toString()));
			}
			try {
				return new String(response.body().bytes(), charset);
			} catch (IOException e) {
				throw new MpayApiReadError("failed to read response", e);
			}
		}finally {
			closeResponse(response);
			long end = System.currentTimeMillis();
			if(logger.isDebugEnabled()){
				logger.debug("cost: {} ms, url: {}", end - start, url);
			}
		}
	}

	public static Map<String, Object> doWechatV3Get(String clientName, SSLContext ctx, HostnameVerifier verifier, String url, Map<String, String> header, final String charset, int connectTimeout, int readTimeout) throws MpayApiNetworkError{
        OkHttpClient client = getHttpClient(clientName, ctx, verifier, connectTimeout, readTimeout);
        long start = System.currentTimeMillis();
        Request req;
        Response response = null;
        try {
            Request.Builder builder = new Request.Builder()
                    .url(url)
                    .get();

            if (header != null && !header.isEmpty()) {
                Set<Map.Entry<String, String>> entries = header.entrySet();
                for (Map.Entry<String, String> stringEntry : entries) {
                    builder.header(stringEntry.getKey(), stringEntry.getValue());
                }
            }
            req = builder.build();
            try {
                response = client.newCall(req).execute();
            } catch (IOException e) {
                throw new MpayApiSendError("failed to send request", e);
            }
            int code = response.code();
            Map<String, Object> result = new HashMap<>();
            try {
                if (response.body() != null) {
                    String responseBodyStr = response.body().string();
                    try {
                        result = JsonUtil.jsonStringToObject(responseBodyStr, Map.class);
                    } catch (Exception ex) {
                        logger.error("JSON.parseObject error");
                    }
                } else {
                    throw new MpayApiSendError("failed to send request", new Exception(response.toString()));
                }
                result.put(HttpConstant.HTTP_CODE, code);
                return result;
            } catch (IOException e) {
                throw new MpayApiReadError("failed to read response", e);
            }
        } finally {
			closeResponse(response);
            long end = System.currentTimeMillis();
            if(logger.isDebugEnabled()){
                logger.debug("cost: {} ms, url: {}", end - start, url);
            }
        }
    }

	public static Map<String, Object> doWechatV3Post(String clientName, SSLContext ctx, HostnameVerifier verifier, String url, String ctype, String content, Map<String, String> header, final String charset, int connectTimeout, int readTimeout) throws MpayApiNetworkError {
        OkHttpClient client = getHttpClient(clientName, ctx, verifier, connectTimeout, readTimeout);
        long start = System.currentTimeMillis();
        Request req;
        Response response = null;
        try {
            Request.Builder builder = new Request.Builder()
                    .url(url)
                    .post(RequestBody.create(MediaType.parse(ctype), content));

            if (header != null && !header.isEmpty()) {
                Set<Map.Entry<String, String>> entries = header.entrySet();
                for (Map.Entry<String, String> stringEntry : entries) {
                    builder.header(stringEntry.getKey(), stringEntry.getValue());
                }
            }
            req = builder.build();
            try {
                response = client.newCall(req).execute();
            } catch (IOException e) {
                throw new MpayApiSendError("failed to send request", e);
            }
            int code = response.code();
            Map<String, Object> result = new HashMap<>();
            try {
                if (response.body() != null) {
                    String responseBodyStr = response.body().string();
                    try {
                        result = JsonUtil.jsonStringToObject(responseBodyStr, Map.class);
                    } catch (Exception ex) {
                        logger.error("JSON.parseObject error");
                    }
                } else {
                    throw new MpayApiSendError("failed to send request", new Exception(response.toString()));
                }
                result.put(HttpConstant.HTTP_CODE, code);
                return result;
            } catch (IOException e) {
                throw new MpayApiReadError("failed to read response", e);
            }
        } finally {
			closeResponse(response);
            long end = System.currentTimeMillis();
            if(logger.isDebugEnabled()){
                logger.debug("cost: {} ms, url: {}", end - start, url);
            }
        }
    }

	public static Map<String, Object> doGet(String clientName, SSLContext ctx, HostnameVerifier verifier, String url, Map request, Map<String, String> header, final String charset, int connectTimeout, int readTimeout) throws MpayApiNetworkError {
		OkHttpClient client = getHttpClient(clientName, ctx, verifier, connectTimeout, readTimeout);
		long start = System.currentTimeMillis();
		Request req;
		Response response = null;
		try {
			try {
				Request.Builder builder = new Request.Builder()
						.url(WebUtils.buildGetUrl(url, WebUtils.buildQuery(request, charset)))
						.get();

				if (header != null && !header.isEmpty()) {
					Set<Map.Entry<String, String>> entries = header.entrySet();
					for (Map.Entry<String, String> stringEntry : entries) {
						builder.header(stringEntry.getKey(), stringEntry.getValue());
					}
				}
				req = builder.build();
			} catch (IOException e) {
				throw new MpayApiSendError("failed to buildQuery request", e);
			}
			try {
				response = client.newCall(req).execute();
			} catch (IOException e) {
				throw new MpayApiSendError("failed to send request", e);
			}
			if(!response.isSuccessful()){
				if (GrabPayClient.class.getName().equals(clientName)) {
					String responseStr;
					try {
						ResponseBody body = response.body();
						responseStr = body != null ? new String(body.string().getBytes(StandardCharsets.UTF_8), charset) : "";
					} catch (IOException e) {
						throw new RuntimeException("failed to parse response body");
					}
					throw new HttpFailException(response.code(), responseStr);
				} else if (CcbClient.class.getName().equals(clientName)) {
					String contentType = response.header("Content-Type");
					String respCharset = charset;
					String responseStr;
					if (Objects.nonNull(contentType)) {
						String[] contentArr = contentType.split(";");
						if (contentArr.length == 2) {
							respCharset = contentArr[1].trim();
						}
					}
					ResponseBody body = response.body();
					try {
						responseStr = body != null ? new String(body.string().getBytes(respCharset), charset) : "";
					} catch (IOException e) {
						throw new RuntimeException("failed to parse response body");
					}
					throw new HttpFailException(response.code(), responseStr);
				} else {
					throw new MpayApiSendError("failed to send request", new Exception(response.toString()));
				}
			}
			try {
				Map<String, Object> result = new HashMap<>();
				result.put(HEADERS_RESULT_FIELD, response.headers());
				result.put(BODY_RESULT_FIELD, new String(response.body().bytes(), charset));
				return result;
			} catch (IOException e) {
				throw new MpayApiReadError("failed to read response", e);
			}
		} finally {
			closeResponse(response);
			long end = System.currentTimeMillis();
			if(logger.isDebugEnabled()){
				logger.debug("cost: {} ms, url: {}", end - start, url);
			}
		}
	}

	public static String doPost(String clientName, SSLContext ctx, HostnameVerifier verifier, String url, Map request, final String charset, int connectTimeout, int readTimeout) throws MpayApiNetworkError{
		String content = null;
		try{
			content = WebUtils.buildQuery(request, charset);
		}catch (Exception e) {
			throw new MpayApiSendError("failed to buildQuery request", e);
		}
		return doPost(clientName, ctx, verifier, url, "application/x-www-form-urlencoded", content,  charset, connectTimeout, readTimeout);
	}

	public static String doPost(String clientName, SSLContext ctx, HostnameVerifier verifier, String url,String ctype, Map request, final String charset, int connectTimeout, int readTimeout) throws MpayApiNetworkError{
		String content = null;
		try{
			content = WebUtils.buildQuery(request, charset);
		}catch (Exception e) {
			throw new MpayApiSendError("failed to buildQuery request", e);
		}
		return doPost(clientName, ctx, verifier, url, ctype, content,  charset, connectTimeout, readTimeout);
	}

	public static String doPut(String clientName, SSLContext ctx, HostnameVerifier verifier, String url, String ctype, String content, Map<String, String> header, final String charset, int connectTimeout, int readTimeout) throws MpayApiNetworkError {
		OkHttpClient client = getHttpClient(clientName, ctx, verifier, connectTimeout, readTimeout);

		Request.Builder requestBuilder = new Request.Builder()
				.url(url)
				.put(RequestBody.create(MediaType.parse(ctype + "; charset=" + charset), content));

		if (header != null && header.size() > 0) {
			requestBuilder.headers(Headers.of(header));
		}

		long start = System.currentTimeMillis();
		Response response = null;
		Request req = requestBuilder.build();

		try {
			try {
				response = client.newCall(req).execute();
			} catch (IOException e) {
				throw new MpayApiSendError("failed to send request", e);
			}
			if(!response.isSuccessful()){
				if (GrabPayClient.class.getName().equals(clientName)) {
					String responseStr;
					try {
						responseStr = response.body() != null ? new String(response.body().bytes(), charset) : "";
					} catch (IOException e) {
						throw new RuntimeException("failed to parse response body");
					}
					throw new HttpFailException(response.code(), responseStr);
				} else {
					throw new MpayApiReadError("failed to send request", new Exception(response.toString()));
				}
			}
			try {
				return new String(response.body().bytes(), charset);
			} catch (IOException e) {
				throw new MpayApiReadError("failed to read response", e);
			}
		}finally {
			closeResponse(response);
			long end = System.currentTimeMillis();
			if(logger.isDebugEnabled()){
				logger.debug("cost: {} ms, url: {}", end - start, url);
			}
		}

	}

	public static X509TrustManager buildX509TrustManager(){
		TrustManagerFactory trustManagerFactory;
		try {
			trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
			trustManagerFactory.init((KeyStore) null);
		} catch (Exception e) {
			throw new RuntimeException("build trust manager error: " + e.getMessage(), e);
		}
		TrustManager[] trustManagers = trustManagerFactory.getTrustManagers();
		if (trustManagers.length != 1 || !(trustManagers[0] instanceof X509TrustManager)) {
			throw new IllegalStateException("Unexpected default trust managers:" + Arrays.toString(trustManagers));
		}
		return (X509TrustManager) trustManagers[0];
	}

    public static Map<String, Object> doCommonMethod(String clientName, SSLContext ctx, HostnameVerifier verifier, String url, Map request, String ctype, String content, Map<String, String> header, final String charset, int connectTimeout, int readTimeout, String method) throws MpayApiNetworkError {
        OkHttpClient client = getHttpClient(clientName, ctx, verifier, connectTimeout, readTimeout);
        long start = System.currentTimeMillis();
        Request req;
        Response response = null;
        try {
            try {
                Request.Builder builder = new Request.Builder();
                if (method.equals("get")) {
                    builder = new Request.Builder()
                            .url(WebUtils.buildGetUrl(url, WebUtils.buildQuery(request, charset)))
                            .get();
                } else if (method.equals("post")) {
                    builder = new Request.Builder()
                            .url(url)
                            .post(RequestBody.create(MediaType.parse(ctype + "; charset=" + charset), content));
                }
                if (header != null && !header.isEmpty()) {
                    Set<Map.Entry<String, String>> entries = header.entrySet();
                    for (Map.Entry<String, String> stringEntry : entries) {
                        builder.header(stringEntry.getKey(), stringEntry.getValue());
                    }
                }
                req = builder.build();
            } catch (IOException e) {
                throw new MpayApiSendError("failed to buildQuery request", e);
            }
            try {
                response = client.newCall(req).execute();
            } catch (IOException e) {
                throw new MpayApiSendError("failed to send request", e);
            }
            try {
                Map<String, Object> result = new HashMap<>();
                result.put(HEADERS_RESULT_FIELD, response.headers());
                result.put(BODY_RESULT_FIELD, new String(response.body().bytes(), charset));
                return result;
            } catch (IOException e) {
                throw new MpayApiReadError("failed to read response", e);
            }
        } finally {
			closeResponse(response);
            long end = System.currentTimeMillis();
            if (logger.isDebugEnabled()) {
                logger.debug("cost: {} ms, url: {}", end - start, url);
            }
        }
    }

	private static void closeResponse(Response response) {
		if (response != null) {
			Util.closeQuietly(response);
		}
	}
}
