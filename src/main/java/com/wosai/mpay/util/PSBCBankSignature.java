package com.wosai.mpay.util;

import cfca.sadk.algorithm.common.PKIException;
import cfca.sadk.algorithm.sm2.SM2PrivateKey;
import cfca.sadk.algorithm.sm2.SM2PublicKey;
import com.google.common.collect.Lists;
import com.wosai.mpay.api.psbcbank.PSBCBankConstants;
import com.wosai.mpay.api.psbcbank.PSBCBusinessFields;
import com.wosai.mpay.api.psbcbank.PSBCResponseFields;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description PSBCBankSignature
 * @Date 2021/4/9 3:24 PM
 */
public class PSBCBankSignature {

    private static final Logger LOGGER = LoggerFactory.getLogger(PSBCBankSignature.class);

    public static String sign(Map<String, Object> bizParam, String privateKey, String sm2Pass) {
        List<String> keySet = Lists.newArrayList(PSBCBusinessFields.TXN_CODE, PSBCBusinessFields.PLATFORM_ID,
                PSBCBusinessFields.REQ_TRACE_ID, PSBCBusinessFields.REQ_DATE, PSBCResponseFields.TXN_AMT, PSBCBusinessFields.MCHT_NO, PSBCResponseFields.RESP_CODE);
        //1、组装macbuff签名域（待签名数据）
        StringBuilder temp = new StringBuilder(128);
        for (String key : keySet) {
            Object value = bizParam.get(key);
            if (Objects.isNull(value)) {
                continue;
            }
            if (temp.length() > 0) {
                temp.append(" ");
            }
            temp.append(value);
        }

        //获取sm2私钥
        byte[] d = new byte[0];
        try {
            SM2PrivateKey sm2PrivateKey = SM2Util.getInstance().getSM2PrivateKey(sm2Pass, privateKey);
            d = sm2PrivateKey.getD_Bytes();
        } catch (PKIException e) {
            //获取密钥失败
            e.printStackTrace();
        }
        byte[] sign = SM2Util.getInstance().SM2Sign(d, temp.toString().getBytes());

        String signature = new String(sign);

        return signature;
    }

    public static boolean verifySign(Map<String, Object> bizParam, String privateKey, String sm2Pass) throws Exception {

        String isSign = (String) bizParam.get(PSBCBusinessFields.IS_SIGN);
        if (PSBCBankConstants.NOT_SIGN.equals(isSign)){
            return true;
        }
        String sign = (String) bizParam.get(PSBCBusinessFields.SIGNATURE);

        List<String> keySet = Lists.newArrayList(PSBCBusinessFields.TXN_CODE, PSBCBusinessFields.PLATFORM_ID,
                PSBCBusinessFields.REQ_TRACE_ID, PSBCBusinessFields.REQ_DATE, PSBCResponseFields.TXN_AMT, PSBCBusinessFields.MCHT_NO, PSBCResponseFields.RESP_CODE);
        //1、组装macbuff签名域（待签名数据）
        StringBuilder temp = new StringBuilder(128);
        for (String key : keySet) {
            Object value = bizParam.get(key);
            if (Objects.isNull(value)) {
                continue;
            }
            if (temp.length() > 0) {
                temp.append(" ");
            }
            temp.append(value);
        }
        //获取sm2公钥
        SM2PublicKey sm2PublicKey = null;
        try {
            SM2PrivateKey sm2PrivateKey = SM2Util.getInstance().getSM2PrivateKey(sm2Pass, privateKey);
            sm2PublicKey = sm2PrivateKey.getSM2PublicKey();
        } catch (Exception e) {
            //获取密钥失败
            LOGGER.error("[邮储通道获取sm2公钥失败]", e);
        }
        if (sm2PublicKey == null){
            throw new Exception("获取公钥失败");
        }

        return SM2Util.getInstance().SM2Verify(sm2PublicKey.getPubX(), sm2PublicKey.getPubY(), temp.toString().getBytes(), sign.getBytes());
    }
}
