package com.wosai.mpay.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

public class MacAddressUtil {

    private static final Logger log = LoggerFactory.getLogger(MacAddressUtil.class);

    /**
     * 获取本机MAC地址，失败时返回默认值
     * @param defaultMac 默认MAC地址（如 "00-00-00-00-00-00"）
     * @return MAC地址字符串（格式：XX-XX-XX-XX-XX-XX）
     */
    public static String getLocalMacAddress(String defaultMac) {
        try {
            // 遍历所有网络接口
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();

                // 跳过回环接口、未启用的接口、虚拟接口
                if (networkInterface.isLoopback() ||
                        !networkInterface.isUp() ||
                        networkInterface.isVirtual()) {
                    continue;
                }

                // 获取MAC地址字节数组
                byte[] macBytes = networkInterface.getHardwareAddress();
                if (macBytes != null && macBytes.length > 0) {
                    // 将字节数组转换为XX-XX-XX-XX-XX-XX格式
                    StringBuilder macBuilder = new StringBuilder();
                    for (int i = 0; i < macBytes.length; i++) {
                        macBuilder.append(String.format("%02X%s",
                                macBytes[i],
                                (i < macBytes.length - 1) ? "-" : ""));
                    }
                    return macBuilder.toString();
                }
            }
        } catch (SocketException e) {
            log.error("获取MAC地址失败: {}", e.getMessage());
        }

        // 获取失败时返回默认值
        return defaultMac;
    }
}
