package com.wosai.mpay.util;

import com.wosai.mpay.api.abc.RSAEncrypt;
import com.wosai.mpay.api.guotong.GuotongProtocolFields;
import com.wosai.mpay.exception.MpayException;
import org.apache.commons.lang.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @ClassName: GuotongSignature
 * @Description: 国通支付签名工具类，用于生成请求签名
 * @Auther:
 * @Date: 2025/4/22
 */
public class GuotongSignature {

    // 字符集编码
    private static final String CHARSET = StandardCharsets.UTF_8.toString();

    /**
     * 获取签名
     * 直接拼接key在最后面
     * 例：key=123456789的时候待签名的字符串为，aa=3&bb=1&cc=1&dd=20160426999123456789
     *
     * @param publicKey 访问公钥
     * @param params    参数Map
     * @return 签名字符串
     * @throws MpayException 签名异常
     */
    public static String getSign(String publicKey, Map<String, Object> params) throws MpayException {
        // 直接拼接key
        String signContent = getSignContent(params);
        try {
            signContent = byte2Hex(Digest.sha256Bytes(signContent.getBytes(CHARSET)));
            return RSAEncrypt.encrypt(signContent, publicKey);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取签名内容
     *
     * @param params 参数Map
     * @return 签名内容字符串
     */
    private static String getSignContent(Map<String, Object> params) throws MpayException {
        TreeMap<String, Object> tm = new TreeMap<>(params);
        StringBuilder sb = new StringBuilder();
        int i = 0;
        for (Map.Entry<String, Object> e : tm.entrySet()) {
            String k = e.getKey();
            Object value = e.getValue();
            // 但要保证顺序和传参时的顺序一致（若参数值为null，则不要拼接，如果是空字符串，则还是需要拼接）
            if (Objects.isNull(value) || GuotongProtocolFields.SIGN.equals(k)) {
                continue;
            }
            String v;
            // 所有全部转换 string
            if (value instanceof Iterable || value instanceof Map) {
                v = JsonUtil.objectToJsonString(value);
            } else {
                v = value + "";
            }
            if (i != 0) {
                sb.append("&");
            }
            sb.append(k).append("=").append(v);
            i = i + 1;
        }
        return sb.toString();
    }

    /**
     * 将byte转为16进制
     *
     * @param bytes
     * @return
     */
    private static String byte2Hex(byte[] bytes) {
        String hex = Hex.byteToHex(bytes);
        return StringUtils.lowerCase(hex);
    }
}
