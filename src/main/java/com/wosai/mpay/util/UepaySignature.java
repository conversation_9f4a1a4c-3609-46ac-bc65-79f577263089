package com.wosai.mpay.util;

import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;
import com.wosai.mpay.api.uepay.ProtocolFields;

import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;

/**
 * <AUTHOR> Date: 2020/6/9 Time: 11:00 上午
 */
public class UepaySignature {

    public static String sign(Map<String, String> bizParam, String merchantNo, String requestType
            , String appVersion, String appSource, String tradeType, String secret) {

        bizParam.put(ProtocolFields.MERCHANT_NO, merchantNo);
        bizParam.put(ProtocolFields.REQUEST_TYPE, requestType);
        bizParam.put(ProtocolFields.APP_VERSION, appVersion);
        bizParam.put(ProtocolFields.APP_SOURCE, appSource);
        bizParam.put(ProtocolFields.TRADE_TYPE, tradeType);
        Set<String> keySet = new TreeSet<>(bizParam.keySet());

        StringBuilder temp = new StringBuilder(128);
        for (String key : keySet) {
            Object value = bizParam.get(key);
            if (Objects.isNull(value)) {
                continue;
            }
            if (temp.length() > 0) {
                temp.append("&");
            }
            temp.append(key).append("=").append(value);
        }
        temp.append("&").append(ProtocolFields.KEY).append("=").append(secret);

        return Hashing.md5().hashString(temp.toString(), Charsets.UTF_8).toString().toUpperCase();
    }
}
