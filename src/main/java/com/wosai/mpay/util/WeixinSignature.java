package com.wosai.mpay.util;

import com.wosai.mpay.api.weixin.B2b.ResponseFields;
import com.wosai.mpay.api.weixin.ProtocolFields;
import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.exception.MpayException;
import com.wosai.pantheon.util.MapUtil;

import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.mpay.api.weixin.WeixinConstants.SIGN_TYPE_HMAC_SHA256;
import static com.wosai.mpay.api.weixin.WeixinConstants.SIGN_TYPE_MD5;

/**
 * User: rizenguo
 * Date: 2014/10/29
 * Time: 15:23
 */
public class WeixinSignature {

    /**
     * 获取md5签名, 返回大写字符串
     * @param content
     * @param key
     * @param charset
     * @return
     * @throws MpayException
     */
    public static String getSign(Map<String, Object> content, String key, String charset) throws MpayException {
        Map<String, Object> result = generateSignAndSortedContent(content, key, charset, SIGN_TYPE_MD5);
        return MapUtils.getString(result, ProtocolFields.SIGN);
    }

    /**
     * 获取sha256签名, 返回大写字符串
     * @param content
     * @param key
     * @param charset
     * @return
     * @throws MpayException
     */
    public static String getSHA256Sign(Map<String, Object> content, String key, String charset) throws MpayException {
        Map<String, Object> result = generateSignAndSortedContent(content, key, charset, SIGN_TYPE_HMAC_SHA256);
        return MapUtils.getString(result, ProtocolFields.SIGN);
    }

    /**
     * 生成签名和排序后的content
     *
     * @param content  签名内容
     * @param key      密钥
     * @param charset  字符集
     * @param signType 签名类型
     * @return 签名结果
     * @throws MpayException
     */
    public static Map<String, Object> generateSignAndSortedContent(Map<String, Object> content, String key, String charset, String signType) throws MpayException {
        String contentStr = getSortedContent(content);
        String sign;
        switch (signType) {
            case WeixinConstants.SIGN_TYPE_MD5:
                sign = getMd5Sign(contentStr, key, charset);
                break;
            case WeixinConstants.SIGN_TYPE_HMAC_SHA256:
                sign = getSHA256Sign(contentStr, key, charset);
                break;
            default:
                throw new MpayException("unsupported signType:" + signType);
        }
        Map<String, Object> result = new HashMap<>();
        result.put(ProtocolFields.SIGN, sign);
        result.put(WeixinConstants.SORTED_CONTENT, contentStr);
        return result;
    }

    /**
     * 排序拼接字符串
     *
     * @param content
     * @return
     */
    public static String getSortedContent(Map<String, Object> content) {
        ArrayList<String> list = new ArrayList<>();
        for(Map.Entry<String,Object> entry:content.entrySet()){
            if (entry.getValue() != null && entry.getValue() != "") {
                list.add(entry.getKey() + "=" + entry.getValue() + "&");
            }
        }
        int size = list.size();
        String [] arrayToSort = list.toArray(new String[size]);
        Arrays.sort(arrayToSort, String.CASE_INSENSITIVE_ORDER);
        StringBuilder sb = new StringBuilder();
        for(int i = 0; i < size; i ++) {
            sb.append(arrayToSort[i]);
        }
        return sb.substring(0, sb.length() - 1);
    }
    /**
     * 从API返回的XML数据里面重新计算一次签名
     * @param responseString API返回的XML数据
     * @return 新鲜出炉的签名
     * @throws MpayException
     */
    public static String getSignFromResponseString(String responseString, String key,  String charset) throws MpayException {
        Map<String,Object> map = XmlUtils.parse(responseString);
        //清掉返回数据对象里面的Sign数据（不能把这个数据也加进去进行签名），然后用签名算法进行签名
        map.put("sign","");
        //将API返回的数据根据用签名算法进行计算新的签名，用来跟API返回的签名进行比较
        return WeixinSignature.getSign(map, key, charset);
    }

    /**
     * 检验API返回的数据里面的签名是否合法，避免数据在传输的过程中被第三方篡改
     * @param responseString API返回的XML数据字符串
     * @return API签名是否合法
     * @throws MpayException
     */
    public static boolean checkIsSignValidFromResponseString(String responseString, String key,  String charset) throws  MpayException {
        return checkIsSignValidFromResponse(XmlUtils.parse(responseString), key, charset);
    }

    /**
     * 检验API返回的数据里面的签名是否合法，避免数据在传输的过程中被第三方篡改
     * @param response
     * @return API签名是否合法
     * @throws MpayException
     */
    public static boolean checkIsSignValidFromResponse(Map<String,Object> response, String key,  String charset) throws  MpayException {
        String signFromAPIResponse = response.get("sign").toString();
        if(StringUtils.isEmpty(signFromAPIResponse)){
            return false;
        }
        //清掉返回数据对象里面的Sign数据（不能把这个数据也加进去进行签名），然后用签名算法进行签名
        response.put("sign","");
        //将API返回的数据根据用签名算法进行计算新的签名，用来跟API返回的签名进行比较
        String signForAPIResponse = WeixinSignature.getSign(response, key, charset);

        if(!signForAPIResponse.equals(signFromAPIResponse)){
            //签名验不过，表示这个API返回的数据有可能已经被篡改了
            return false;
        }
        return true;
    }

    /**
     * 获取md5签名串， 全部大写
     * @param content
     * @param key
     * @param charset
     * @return
     * @throws MpayException
     */
    public static String getMd5Sign(String content, String key,  String charset) throws MpayException{
        String md5 = "";
        try {
            md5 = Digest.md5((content + "&key=" + key).getBytes(charset)).toUpperCase();
        } catch (Exception e) {
            throw new MpayException("failed to generate md5 signature.", e);
        }
        return md5;
    }
    
    /**
     *  利用java原生的摘要实现SHA256加密
     * @param str 加密后的报文
     * @return
     */
    public static String getSHA256Sign(String content, String key,  String charset) throws MpayException{
    	String sha256 = "";
        try {
        	sha256 = Digest.sha256((content + "&key=" + key).getBytes(charset)).toUpperCase();
        } catch (Exception e) {
            throw new MpayException("failed to generate sha256 signature.", e);
        }
        return sha256;
    }


    /**
     * 检验API返回的数据里面的签名是否合法，避免数据在传输的过程中被第三方篡改
     * @param response
     * @return API签名是否合法
     * @throws MpayException
     */
    public static boolean checkIsSha1SignValidFromResponse(Map<String, String> response, List<String> keysToExtract, String signToken) throws MpayException {
        String signFromAPIResponse = MapUtil.getString(response, ResponseFields.SIGNATURE);
        if(StringUtils.isEmpty(signFromAPIResponse)){
            return false;
        }
        String signContent = getSignContent(response, keysToExtract, signToken);
        //将API返回的数据根据用签名算法进行计算新的签名，用来跟API返回的签名进行比较
        String signForAPIResponse = getSha1Sign(signContent);

        if(!signForAPIResponse.equals(signFromAPIResponse)){
            //签名验不过，表示这个API返回的数据有可能已经被篡改了
            return false;
        }
        return true;
    }

    /**
     * 获取sha1签名
     * @param content
     * @return
     */
    public static String getSha1Sign(String content) throws MpayException{
        String sha1 = "";
        try {
            sha1 = Digest.sha1(content.getBytes());
        } catch (Exception e) {
            throw new MpayException("failed to generate sha1 signature.", e);
        }
        return sha1;
    }

    /**
     * value 排序拼接字符串
     * @param params
     * @return
     */
    public static String getSignContent(Map<String, String> params, List<String> keysToExtract, String signToken) {

        // 使用Stream API过滤并收集所需值
        List<String> extractedValues = keysToExtract.stream()
                .filter(params::containsKey)
                .map(params::get)
                .collect(Collectors.toList());

        extractedValues.add(signToken);

        Collections.sort(extractedValues); // 字典序排序
        StringBuilder content = new StringBuilder();
        for (String item : extractedValues) {
            content.append(item);
        }

        return content.toString();
    }

    /**
     * 将byte转为16进制
     * @param bytes
     * @return
     */
    private static String byte2Hex(byte[] bytes){
        StringBuffer stringBuffer = new StringBuffer();
        String temp = null;
        for (int i=0;i<bytes.length;i++){
            temp = Integer.toHexString(bytes[i] & 0xFF);
            if (temp.length()==1){
                //1得到一位的进行补0操作
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }


    public static void main(String[] args) throws MpayException {
        String md5 = WeixinSignature.getMd5Sign("appid=111&attach=234&body=234&device_info=2342&mch_id=222&nonce_str=234&out_trade_no=1xc&spbill_create_ip=12&total_fee=22", "2222", WeixinConstants.CHARSET_UTF8);
        System.out.println(md5.toUpperCase().equals("9243C16E24D416AA3F27B24DE2492DA0"));
        String content = "<xml>\n" +
                "\t<appid>111</appid>\n" +
                "\t<attach>234</attach>\n" +
                "\t<body>234</body>\n" +
                "\t<device_info>2342</device_info>\n" +
                "\t<mch_id>222</mch_id>\n" +
                "\t<nonce_str>234</nonce_str>\n" +
                "\t<out_trade_no>1xc</out_trade_no>\n" +
                "\t<spbill_create_ip>12</spbill_create_ip>\n" +
                "\t<total_fee>22</total_fee>\n" +
                "\t<sign>9243C16E24D416AA3F27B24DE2492DA0</sign>\n" +
                "</xml>";
        System.out.println(WeixinSignature.getSignFromResponseString(content, "2222", WeixinConstants.CHARSET_UTF8));
        boolean isValid = WeixinSignature.checkIsSignValidFromResponseString(content, "2222", WeixinConstants.CHARSET_UTF8);
        System.out.println(isValid);
    }

}
