package com.wosai.mpay.util;

import java.nio.charset.StandardCharsets;
import java.util.*;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import com.wosai.mpay.exception.MpayException;

public class HmacSignature {
    public static final String SIG_ALG_NAME_HMAC_SHA256 = "HmacSHA256";
    
    /**
     * 签名
     * 设所有发送或接收到的数据为集合M，将集合M内非空参数值的参数按照参数名ASCII码从小到 大排序(字典序)，使用URL键值对的格式(即key1=value1&key2=value2...)拼接成字符串stringA。 特别注意以下重要规则:
     *  1)参数名ASCII码从小到大排序(字典序);
     *  2)如果参数的值为空不参与签名;
     *  3)参数名区分大小写;
     * @param params
     * @param sigAlgName SHA256WithRSA
     * @param privateKey
     * @return
     * @throws Exception
     */
    public static String sign(Map params, String privateKey) throws MpayException {
        String signData = getSignCheckContent(params) + "&key=" + privateKey;
        return sign(signData, SIG_ALG_NAME_HMAC_SHA256, privateKey);
    }


    public static String sign(String data, String sigAlgName, String privateKey) throws MpayException{
        return sign(data, sigAlgName, privateKey, true);
    }

    public static String sign(String data, String sigAlgName, String privateKey, boolean isUpperCase) throws MpayException{
        try{
            byte[] array = signBytes(data, sigAlgName, privateKey);
            StringBuilder sb = new StringBuilder();
            for (byte item : array) {
                sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
            }
            if (isUpperCase) {
                return sb.toString().toUpperCase();
            } else {
                return sb.toString();
            }
        }catch (Exception e){
            throw new MpayException("failed to sign", e);
        }
    }

    public static byte[] signBytes(String data, String sigAlgName, String privateKey) throws MpayException{
        try{
            Mac sha256Hmac = Mac.getInstance(sigAlgName);
            SecretKeySpec secertKey = new SecretKeySpec(privateKey.getBytes(StandardCharsets.UTF_8), sigAlgName);
            sha256Hmac.init(secertKey);
            return sha256Hmac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        }catch (Exception e){
            throw new MpayException("failed to sign", e);
        }
    }

    /**
     * 设所有发送或接收到的数据为集合M，将集合M内非空参数值的参数按照参数名ASCII码从小到 大排序(字典序)，使用URL键值对的格式(即key1=value1&key2=value2...)拼接成字符串stringA。 特别注意以下重要规则:
     *  1)参数名ASCII码从小到大排序(字典序);
     *  2)如果参数的值为空不参与签名;
     *  3)参数名区分大小写;
     */
    public static String getSignCheckContent(Map params) {
        if (params == null) {
            return null;
        }
        StringBuffer content = new StringBuffer();
        List<String> keys = new ArrayList<String>(params.keySet());
        Collections.sort(keys);
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = params.get(key);
            if(null == value || "".equals(value)) {
                continue;
            }
            content.append((i == 0 ? "" : "&") + key + "=" + value);
        }
        return content.toString();
    }
}
