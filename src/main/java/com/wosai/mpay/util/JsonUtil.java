package com.wosai.mpay.util;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.exception.MpayException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.StringWriter;

/**
 * Created by xuchmao on 15/12/11.
 */
public class JsonUtil {
    private static Logger log =  LoggerFactory.getLogger(JsonUtil.class);
    public static ObjectMapper objectMapper;
    static {
        objectMapper = new ObjectMapper();
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        //允许出现特殊字符和转义符
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
    }

    @Deprecated
    /**
     * 使用jsonStringToObject代替
     */
    public static <T> T jsonStrToObject(String jsonStr, Class<T> valueType){
        if(jsonStr == null || valueType == null){
            return null;
        }
        T t = null;
        try {
            t = objectMapper.readValue(jsonStr, valueType);
        }catch (Exception e){
        }finally {
            return t;
        }
    }

    @Deprecated
    /**
     * 使用objectToJsonString代替
     */
    public static <T> String toJsonStr(T obj){
        if(obj == null){
            return null;
        }
        String jsonStr = null;
        try {
            StringWriter sw = new StringWriter();
            JsonGenerator gen = new JsonFactory().createJsonGenerator(sw);
            objectMapper.writeValue(gen, obj);
            gen.close();
            jsonStr = sw.toString();
        }catch (IOException e){
            log.error(e.getMessage());
        }
        finally {
            return jsonStr;
        }
    }

    public static <T> T jsonStringToObject(String jsonStr, Class<T> valueType) throws MpayException {
        if(jsonStr == null || valueType == null){
            return null;
        }
        try {
            return objectMapper.readValue(jsonStr, valueType);
        }catch (Exception e){
            throw new MpayException(e.getMessage(), e.getCause());
        }
    }

    public static <T> T jsonStringToObject(String jsonStr, TypeReference<T> typeReference) throws MpayException {
        if(jsonStr == null || typeReference == null){
            return null;
        }
        try {
            return objectMapper.readValue(jsonStr, typeReference);
        }catch (Exception e){
            throw new MpayException(e.getMessage(), e.getCause());
        }
    }

    public static <T> String objectToJsonString(T obj) throws MpayException {
        if(obj == null){
            return null;
        }
        try {
            return objectMapper.writeValueAsString(obj);
        }catch (IOException e){
            throw new MpayException(e.getMessage(), e.getCause());
        }
    }



}