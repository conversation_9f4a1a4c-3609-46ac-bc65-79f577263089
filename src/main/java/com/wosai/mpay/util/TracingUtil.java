package com.wosai.mpay.util;

import com.wosai.middleware.carrier.CarrierItem;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/9/21.
 */
public class TracingUtil {
    private static ThreadLocal<CarrierItem> local = new ThreadLocal<>();

    public static void storeThreadLocalTraceInfo(CarrierItem item){
        local.remove();
        if(item != null){
            local.set(item);
        }
    }


    public static CarrierItem getTraceCarrierItem(){
        return local.get();
    }

    public static void remove(){
        local.remove();
    }
}
