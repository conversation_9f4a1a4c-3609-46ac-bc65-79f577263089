package com.wosai.mpay.util;


import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;


public class CMCCSignature {

    public static String MD5Sign(String signData, String signkey) {
        String value = cryptMd5(signData, "");
        String value2 = cryptMd5(value, signkey);
        return value2;
    }

    public static String cryptMd5(String source, String key) {
        byte[] k_ipad = new byte[64];
        byte[] k_opad = new byte[64];

        byte[] keyb;
        byte[] value;
        try {
            keyb = key.getBytes("UTF-8");
            value = source.getBytes("UTF-8");
        } catch (UnsupportedEncodingException var10) {
            keyb = key.getBytes();
            value = source.getBytes();
        }

        Arrays.fill(k_ipad, keyb.length, 64, (new Integer(54)).byteValue());
        Arrays.fill(k_opad, keyb.length, 64, (new Integer(92)).byteValue());

        for(int i = 0; i < keyb.length; ++i) {
            k_ipad[i] = (byte)(keyb[i] ^ 54);
            k_opad[i] = (byte)(keyb[i] ^ 92);
        }

        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException var9) {
            return null;
        }

        md.update(k_ipad);
        md.update(value);
        byte[] dg = md.digest();
        md.reset();
        md.update(k_opad);
        md.update(dg, 0, 16);
        dg = md.digest();
        return new String(Hex.encode(dg));
    }
}
