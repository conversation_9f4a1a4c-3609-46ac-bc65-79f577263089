package com.wosai.mpay.util;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.StringWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.net.ssl.*;

import com.wosai.mpay.api.alipay.FileItem;
import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiReadError;
import com.wosai.mpay.exception.MpayApiSendError;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class WebUtils {
    private static final Logger logger = LoggerFactory.getLogger(WebUtils.class);
    public static final String DEFAULT_CHARSET_UTF8 = "UTF-8";
    private static final String METHOD_POST     = "POST";
    private static final String METHOD_GET      = "GET";

    private static class DefaultTrustManager implements X509TrustManager {
        public X509Certificate[] getAcceptedIssuers() {
            return null;
        }

        public void checkClientTrusted(X509Certificate[] chain, String authType)
                                                                                throws CertificateException {
        }

        public void checkServerTrusted(X509Certificate[] chain, String authType)
                                                                                throws CertificateException {
        }
    }

    private WebUtils() {
    }

    /**
     * 执行HTTP POST请求。application/x-www-form-encoded;charset=utf-8
     * 
     * @param url 请求地址
     * @param params 请求参数
     * @return 响应字符串
     * @throws IOException
     */
    public static String doPost(SSLContext ctx, HostnameVerifier hostnameVerifier,
                                String url, Map<String, String> params, int connectTimeout,
                                int readTimeout) throws MpayApiNetworkError {
        return doPost(ctx, hostnameVerifier, url, params, DEFAULT_CHARSET_UTF8, connectTimeout, readTimeout);
    }

    /**
     * 执行HTTP POST请求。application/x-www-form-encoded
     * 
     * @param url 请求地址
     * @param params 请求参数
     * @param charset 字符集，如UTF-8, GBK, GB2312
     * @return 响应字符串
     * @throws IOException
     */
    public static String doPost(SSLContext ctx, HostnameVerifier hostnameVerifier,
                                String url, Map<String, String> params, String charset,
                                int connectTimeout, int readTimeout) throws MpayApiNetworkError {
        String ctype = "application/x-www-form-urlencoded;charset=" + charset.toLowerCase();
        byte[] content = {};
        try {
            String query = buildQuery(params, charset);
            if (query != null) {
                content = query.getBytes(charset);
            }
        }catch(IOException e) {
            throw new MpayApiConnectError("failed to build query string", e);
        }
        return doPost(ctx, hostnameVerifier, url, ctype, content, connectTimeout, readTimeout);
    }

    /**
     * 执行HTTP POST请求。通用方法。
     * 
     * @param url 请求地址
     * @param ctype 请求类型
     * @param content 请求字节数组
     * @return 响应字符串
     * @throws IOException
     */
    public static String doPost(SSLContext ctx, HostnameVerifier hostnameVerifier,
                                String url, String ctype, byte[] content, int connectTimeout,
                                int readTimeout) throws MpayApiNetworkError {
        return doPost(ctx, hostnameVerifier, url, ctype, content, connectTimeout, readTimeout, null);
    }
    /**
     * 执行HTTP POST请求。通用方法。
     *
     * @param url 请求地址
     * @param ctype 请求类型
     * @param content 请求字节数组
     * @param responseCharset 响应的字符编码
     * @return 响应字符串
     * @throws IOException
     */
    public static String doPost(SSLContext ctx, HostnameVerifier hostnameVerifier,
                                String url, String ctype, byte[] content, int connectTimeout,
                                int readTimeout, String responseCharset) throws MpayApiNetworkError {
        HttpURLConnection conn = null;
        OutputStream out = null;
        String rsp = null;
        long start = System.currentTimeMillis();
        try {
            try {
                conn = getConnection(ctx, hostnameVerifier, new URL(url), METHOD_POST, ctype);
                conn.setConnectTimeout(connectTimeout);
                conn.setReadTimeout(readTimeout);
                out = conn.getOutputStream();
            } catch (IOException e) {
                throw new MpayApiConnectError("failed to establish network connection", e);
            }
            try {
                out.write(content);
            } catch (IOException e) {
                throw new MpayApiSendError("failed to send request", e);
            }
            try {
                rsp = getResponseAsString(conn, responseCharset);
            } catch (IOException e) {
                throw new MpayApiReadError("failed to read response", e);
            }

        } finally {
            if (out != null) {
                try {
                    out.close();
                }
                catch (IOException e) {
                }
            }
            if (conn != null) {
                conn.disconnect();
            }
            long end = System.currentTimeMillis();
            if(logger.isDebugEnabled()){
                logger.debug("cost: {} ms, url: {}", end - start, url);
            }
        }
        return rsp;
    }

    /**
     * 通用表单POST。通过fileParams区分是否应该提交application/x-www-form-urlencoded 或者 multipart/form-data。
     * 
     * @param url 请求地址
     * @param params 文本请求参数
     * @param fileParams 文件请求参数
     * @return 响应字符串
     * @throws IOException
     */
    public static String doPost(SSLContext ctx, HostnameVerifier hostnameVerifier,
                                String url, Map<String, String> params,
                                Map<String, FileItem> fileParams, int connectTimeout,
                                int readTimeout) throws IOException {
        if (fileParams == null || fileParams.isEmpty()) {
            return doPost(ctx, hostnameVerifier, url, params, DEFAULT_CHARSET_UTF8, connectTimeout, readTimeout);
        } else {
            return doPost(ctx, hostnameVerifier, url, params, fileParams, DEFAULT_CHARSET_UTF8, connectTimeout, readTimeout);
        }
    }

    /**
     * 执行带文件上传的HTTP POST请求。multipart/form-data
     * 
     * @param url 请求地址
     * @param params 文本请求参数
     * @param fileParams 文件请求参数
     * @param charset 字符集，如UTF-8, GBK, GB2312
     * @return 响应字符串
     * @throws IOException
     */
    public static String doPost(SSLContext ctx, HostnameVerifier hostnameVerifier,
                                String url, Map<String, String> params,
                                Map<String, FileItem> fileParams, String charset,
                                int connectTimeout, int readTimeout) throws IOException {
        if (fileParams == null || fileParams.isEmpty()) {
            return doPost(ctx, hostnameVerifier, url, params, charset, connectTimeout, readTimeout);
        }

        String boundary = System.currentTimeMillis() + ""; // 随机分隔线
        HttpURLConnection conn = null;
        OutputStream out = null;
        String rsp = null;
        long start = System.currentTimeMillis();
        try {
            try {
                String ctype = "multipart/form-data;boundary=" + boundary + ";charset=" + charset;
                conn = getConnection(ctx, hostnameVerifier, new URL(url), METHOD_POST, ctype);
                conn.setConnectTimeout(connectTimeout);
                conn.setReadTimeout(readTimeout);
            } catch (IOException e) {
                throw e;
            }

            try {
                out = conn.getOutputStream();

                byte[] entryBoundaryBytes = ("\r\n--" + boundary + "\r\n").getBytes(charset);

                // 组装文本请求参数
                Set<Entry<String, String>> textEntrySet = params.entrySet();
                for (Entry<String, String> textEntry : textEntrySet) {
                    byte[] textBytes = getTextEntry(textEntry.getKey(), textEntry.getValue(),
                        charset);
                    out.write(entryBoundaryBytes);
                    out.write(textBytes);
                }

                // 组装文件请求参数
                Set<Entry<String, FileItem>> fileEntrySet = fileParams.entrySet();
                for (Entry<String, FileItem> fileEntry : fileEntrySet) {
                    FileItem fileItem = fileEntry.getValue();
                    byte[] fileBytes = getFileEntry(fileEntry.getKey(), fileItem.getFileName(),
                        fileItem.getMimeType(), charset);
                    out.write(entryBoundaryBytes);
                    out.write(fileBytes);
                    out.write(fileItem.getContent());
                }

                // 添加请求结束标志
                byte[] endBoundaryBytes = ("\r\n--" + boundary + "--\r\n").getBytes(charset);
                out.write(endBoundaryBytes);
                rsp = getResponseAsString(conn, DEFAULT_CHARSET_UTF8);
            } catch (IOException e) {
                throw e;
            }

        } finally {
            if (out != null) {
                out.close();
            }
            if (conn != null) {
                conn.disconnect();
            }
            long end = System.currentTimeMillis();
            if(logger.isDebugEnabled()){
                logger.debug("cost: {} ms, url: {}", end - start, url);
            }
        }

        return rsp;
    }

    
    /**
     * 执行HTTP POST请求
     * 
     * @param url 请求地址
     * @param requestHeaders head 信息
     * @param content 请求内容
     * @return 响应字符串
     * @throws IOException
     */
    public static String doPost(SSLContext ctx, HostnameVerifier hostnameVerifier, String url, String ctype, Map<String, String> requestHeaders, byte[] content, int connectTimeout, int readTimeout)
    	    throws MpayApiNetworkError
	  {
	    HttpURLConnection conn = null;
	    OutputStream out = null;
	    String rsp = null;
	    long start = System.currentTimeMillis();
	    try
	    {
	      try
	      {
	        conn = getConnection(ctx, hostnameVerifier, new URL(url), "POST", ctype);
	        conn.setConnectTimeout(connectTimeout);
	        conn.setReadTimeout(readTimeout);
	        if ((null != requestHeaders) && (!requestHeaders.isEmpty()))
	        {
	          Set<Map.Entry<String, String>> textEntrySet = requestHeaders.entrySet();
	          for (Map.Entry<String, String> textEntry : textEntrySet) {
	            conn.setRequestProperty((String)textEntry.getKey(), (String)textEntry.getValue());
	          }
	        }
	      }
	      catch (IOException e)
	      {
	        throw new MpayApiConnectError("failed to establish network connection", e);
	      }
	      try
	      {
	        out = conn.getOutputStream();
	        out.write(content);
	      }
	      catch (IOException e)
	      {
	        throw new MpayApiSendError("failed to send request", e);
	      }
	      try
	      {
	        rsp = getResponseAsString(conn, null);
	      }
	      catch (IOException e)
	      {
	        throw new MpayApiReadError("failed to read response", e);
	      }
	    }
	    finally
	    {
	      long end;
	      if (out != null) {
	        try
	        {
	          out.close();
	        }
	        catch (IOException localIOException2) {}
	      }
	      if (conn != null) {
	        conn.disconnect();
	      }
	      end = System.currentTimeMillis();
	      if (logger.isDebugEnabled()) {
	        logger.debug("cost: {} ms, url: {}", Long.valueOf(end - start), url);
	      }
	    }
	    return rsp;
	  }
    
    /**
     * 执行HTTP GET请求。
     * 
     * @param url 请求地址
     * @param requestHeaders head信息
     * @param params 请求参数
     * @param charset 字符集，如UTF-8, GBK, GB2312
     * @return 响应字符串
     * @throws IOException
     */
    public static String doGet(SSLContext ctx, HostnameVerifier hostnameVerifier, String url, Map<String, String> requestHeaders, Map<String, String> params, String charset)
    	    throws IOException
    {
	    HttpURLConnection conn = null;
	    String rsp = null;
	    try
	    {
	      String ctype = "application/x-www-form-urlencoded;charset=" + charset;
	      String query = buildQuery(params, charset);
	      try
	      {
	        conn = getConnection(ctx, hostnameVerifier, buildGetUrl(url, query), "GET", ctype);
	        if ((null != requestHeaders) && (!requestHeaders.isEmpty()))
	        {
	          Set<Map.Entry<String, String>> textEntrySet = requestHeaders.entrySet();
	          for (Map.Entry<String, String> textEntry : textEntrySet) {
	            conn.setRequestProperty((String)textEntry.getKey(), (String)textEntry.getValue());
	          }
	        }
	      }
	      catch (IOException e)
	      {
	        throw e;
	      }
	      try
	      {
	        rsp = getResponseAsString(conn, "UTF-8");
	      }
	      catch (IOException e)
	      {
	        throw e;
	      }
	    }
	    finally
	    {
	      if (conn != null) {
	        conn.disconnect();
	      }
	    }
	    return rsp;
	
	  }


    private static byte[] getTextEntry(String fieldName, String fieldValue, String charset)
                                                                                           throws IOException {
        StringBuilder entry = new StringBuilder();
        entry.append("Content-Disposition:form-data;name=\"");
        entry.append(fieldName);
        entry.append("\"\r\nContent-Type:text/plain\r\n\r\n");
        entry.append(fieldValue);
        return entry.toString().getBytes(charset);
    }

    private static byte[] getFileEntry(String fieldName, String fileName, String mimeType,
                                       String charset) throws IOException {
        StringBuilder entry = new StringBuilder();
        entry.append("Content-Disposition:form-data;name=\"");
        entry.append(fieldName);
        entry.append("\";filename=\"");
        entry.append(fileName);
        entry.append("\"\r\nContent-Type:");
        entry.append(mimeType);
        entry.append("\r\n\r\n");
        return entry.toString().getBytes(charset);
    }

    /**
     * 执行HTTP GET请求。
     * 
     * @param url 请求地址
     * @param params 请求参数
     * @return 响应字符串
     * @throws IOException
     */
    public static String doGet(SSLContext ctx, HostnameVerifier hostnameVerifier,
                               String url, Map<String, String> params) throws IOException {
        return doGet(ctx, hostnameVerifier, url, params, DEFAULT_CHARSET_UTF8);
    }

    /**
     * 执行HTTP GET请求。
     * 
     * @param url 请求地址
     * @param params 请求参数
     * @param charset 字符集，如UTF-8, GBK, GB2312
     * @return 响应字符串
     * @throws IOException
     */
    public static String doGet(SSLContext ctx, HostnameVerifier hostnameVerifier,
                               String url, Map<String, String> params, String charset)
                                                                                      throws IOException {
        return doGet(ctx, hostnameVerifier, url, params, charset, 0, 0);
    }

    /**
     * 执行HTTP GET请求。
     * @param ctx
     * @param hostnameVerifier
     * @param url
     * @param params
     * @param charset
     * @param connectTimeout 为0， 为系统默认连接超时
     * @param readTimeout 为0， 为系统默认读取超时
     * @return
     * @throws IOException
     */
    public static String doGet(SSLContext ctx, HostnameVerifier hostnameVerifier, String url, Map<String, String> params, String charset, int connectTimeout, int readTimeout) throws IOException {
        HttpURLConnection conn = null;
        String rsp = null;
        try {
            String ctype = "application/x-www-form-urlencoded;charset=" + charset;
            String query = buildQuery(params, charset);
            try {
                conn = getConnection(ctx, hostnameVerifier, buildGetUrl(url, query), METHOD_GET, ctype);
                if(connectTimeout != 0){
                    conn.setConnectTimeout(connectTimeout);
                }
                if(readTimeout != 0){
                    conn.setReadTimeout(readTimeout);
                }

            } catch (IOException e) {
                throw e;
            }

            try {
                rsp = getResponseAsString(conn, DEFAULT_CHARSET_UTF8);
            } catch (IOException e) {
                throw e;
            }

        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }

        return rsp;
    }

    public static SSLContext getAllowAllCertsSSLContext() throws IOException {
        SSLContext ctx;
        try {
            ctx = SSLContext.getInstance("TLS");
            ctx.init(null, new TrustManager[] { new DefaultTrustManager() }, new SecureRandom());
            return ctx;
        }
        catch (Exception e) {
            throw new IOException(e);
        }
    }
    
    public static HostnameVerifier getAllowAllHostnameVerifier() {
        return new HostnameVerifier() {
            public boolean verify(String hostname, SSLSession session) {
                return true;
            }
        };
    }
    
    public static HostnameVerifier getDenyAllHostnameVerifier() {
        return new HostnameVerifier() {
            public boolean verify(String hostname, SSLSession session) {
                return false;
            }
        };
    }

    private static HttpURLConnection getConnection(SSLContext sslContext,
                                                   HostnameVerifier hostnameVerifier,
                                                   URL url,
                                                   String
                                                   method,
                                                   String ctype) throws IOException {
        HttpURLConnection conn = null;
        if ("https".equals(url.getProtocol())) {
            HttpsURLConnection connHttps = (HttpsURLConnection) url.openConnection();
            if (sslContext!=null) {
                connHttps.setSSLSocketFactory(sslContext.getSocketFactory());
            }
            if (hostnameVerifier!=null) {
                connHttps.setHostnameVerifier(hostnameVerifier);
            }
            conn = connHttps;
        } else {
            conn = (HttpURLConnection) url.openConnection();
        }

        conn.setRequestMethod(method);
        conn.setDoInput(true);
        conn.setDoOutput(true);
        conn.setRequestProperty("Accept", "text/xml,text/javascript,text/html,*/*");
        conn.setRequestProperty("User-Agent", "aop-sdk-java");
        conn.setRequestProperty("Content-Type", ctype);
        return conn;
    }

    public static URL buildGetUrl(String strUrl, String query) throws IOException {
        URL url = new URL(strUrl);
        if (StringUtils.isEmpty(query)) {
            return url;
        }

        if (StringUtils.isEmpty(url.getQuery())) {
            if (strUrl.endsWith("?")) {
                strUrl = strUrl + query;
            } else {
                strUrl = strUrl + "?" + query;
            }
        } else {
            if (strUrl.endsWith("&")) {
                strUrl = strUrl + query;
            } else {
                strUrl = strUrl + "&" + query;
            }
        }

        return new URL(strUrl);
    }

    public static String buildQuery(Map<String, String> params, String charset) throws IOException {
        return buildQuery(params, charset, true);
    }

    public static String buildQuery(Map<String, String> params, String charset, boolean isEncodeValue) throws IOException {
        if (params == null || params.isEmpty()) {
            return null;
        }

        StringBuilder query = new StringBuilder();
        Set<Entry<String, String>> entries = params.entrySet();
        boolean hasParam = false;

        for (Entry<String, String> entry : entries) {
            String name = entry.getKey();
            String value = entry.getValue();
            // 忽略参数名或参数值为空的参数
            if (StringUtils.areNotEmpty(name, value)) {
                if (hasParam) {
                    query.append("&");
                } else {
                    hasParam = true;
                }
                if (isEncodeValue) {
                    value = URLEncoder.encode(value, charset);
                }
                query.append(name).append("=").append(value);
            }
        }

        return query.toString();
    }

    protected static String getResponseAsString(HttpURLConnection conn, String responseCharset) throws IOException {
        String charset = getResponseCharset(conn.getContentType(),responseCharset);
        InputStream es = conn.getErrorStream();
        if (es == null) {
            return getStreamAsString(conn.getInputStream(), charset);
        } else {
            String msg = getStreamAsString(es, charset);
            if (StringUtils.isEmpty(msg)) {
                throw new IOException(conn.getResponseCode() + ":" + conn.getResponseMessage());
            } else {
                throw new IOException(msg);
            }
        }
    }

    public static String getStreamAsString(InputStream stream, String charset) throws IOException {
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new InputStreamReader(stream, charset));
            StringWriter writer = new StringWriter();

            char[] chars = new char[256];
            int count = 0;
            while ((count = reader.read(chars)) > 0) {
                writer.write(chars, 0, count);
            }

            return writer.toString();
        } finally {
            if (reader != null){
                try {
                    reader.close();
                }catch (IOException e){

                }finally {
                    reader = null;
                }
            }
            if (stream != null) {
                try {
                    stream.close();
                }catch (IOException e){
                    throw  e;
                }
            }

        }
    }
    
    private static byte[] getResponseAsBytes(HttpURLConnection conn, String defaultResponseCharset) throws IOException {
        String charset = getResponseCharset(conn.getContentType(), defaultResponseCharset);
        InputStream es = conn.getErrorStream();
        if (es == null) {
            return getStreamAsBytes(conn.getInputStream());
        }else {
            String msg = getStreamAsString(es, charset);
            if (StringUtils.isEmpty(msg)) {
                throw new IOException(conn.getResponseCode() + ":" + conn.getResponseMessage());
            }else{
                throw new IOException(msg);
            }
        }
    }
    
    private static byte[] getStreamAsBytes(InputStream stream) throws IOException {
        BufferedInputStream in = new BufferedInputStream(stream);
        byte[] buffer = new byte[1024];
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        int len;
        while ((len = in.read(buffer)) > 0) {
            baos.write(buffer, 0, len);
        }
        return baos.toByteArray();
    }

    public static String getResponseCharset(String ctype, String defaultCharset) {
        String charset = defaultCharset;
        if(charset == null){
            charset = DEFAULT_CHARSET_UTF8;
        }
        if (!StringUtils.isEmpty(ctype)) {
            String[] params = ctype.split(";");
            for (String param : params) {
                param = param.trim();
                if (param.startsWith("charset")) {
                    String[] pair = param.split("=", 2);
                    if (pair.length == 2) {
                        if (!StringUtils.isEmpty(pair[1])) {
                            charset = pair[1].trim();
                        }
                    }
                    break;
                }
            }
        }

        return charset;
    }

    /**
     * 使用默认的UTF-8字符集反编码请求参数值。
     * 
     * @param value 参数值
     * @return 反编码后的参数值
     */
    public static String decode(String value) {
        return decode(value, DEFAULT_CHARSET_UTF8);
    }

    /**
     * 使用默认的UTF-8字符集编码请求参数值。
     * 
     * @param value 参数值
     * @return 编码后的参数值
     */
    public static String encode(String value) {
        return encode(value, DEFAULT_CHARSET_UTF8);
    }

    /**
     * 使用指定的字符集反编码请求参数值。
     * 
     * @param value 参数值
     * @param charset 字符集
     * @return 反编码后的参数值
     */
    public static String decode(String value, String charset) {
        String result = null;
        if (!StringUtils.isEmpty(value)) {
            try {
                result = URLDecoder.decode(value, charset);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return result;
    }

    /**
     * 使用指定的字符集编码请求参数值。
     * 
     * @param value 参数值
     * @param charset 字符集
     * @return 编码后的参数值
     */
    public static String encode(String value, String charset) {
        String result = null;
        if (!StringUtils.isEmpty(value)) {
            try {
                result = URLEncoder.encode(value, charset);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return result;
    }

    private static Map<String, String> getParamsFromUrl(String url) {
        Map<String, String> map = null;
        if (url != null && url.indexOf('?') != -1) {
            map = splitUrlQuery(url.substring(url.indexOf('?') + 1));
        }
        if (map == null) {
            map = new HashMap<String, String>();
        }
        return map;
    }

    /**
     * 从URL中提取所有的参数。
     * 
     * @param query URL地址
     * @return 参数映射
     */
    public static Map<String, String> splitUrlQuery(String query) {
        Map<String, String> result = new HashMap<String, String>();

        String[] pairs = query.split("&");
        if (pairs != null && pairs.length > 0) {
            for (String pair : pairs) {
                String[] param = pair.split("=", 2);
                if (param != null && param.length == 2) {
                    result.put(param[0], param[1]);
                }
            }
        }

        return result;
    }

}
