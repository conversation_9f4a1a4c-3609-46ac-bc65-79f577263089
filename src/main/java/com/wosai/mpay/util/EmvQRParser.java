package com.wosai.mpay.util;

import com.wosai.pantheon.util.MapUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * emv qr code parser
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/8/11.
 */
public class EmvQRParser {

    /**
     *
     * @param content
     * @param path 属性定义路径，嵌套的用.号拼接
     * @return
     */
    public static String getFieldContentByFieldPath(String content, String path){
        if(!path.contains(".")){
            return MapUtil.getString(parseQRCodeContent(content), path);
        }else{
            int firstFieldIndex = path.indexOf(".");
            String subContent = MapUtil.getString(parseQRCodeContent(content), path.substring(0, firstFieldIndex));
            if(subContent == null || subContent.isEmpty()){
                return subContent;
            }
            return getFieldContentByFieldPath(subContent, path.substring(firstFieldIndex + 1));
        }

    }


    public static Map<String, String> parseQRCodeContent(String content) {
        if(content == null){
            return null;
        }
        Map<String, String> dataMap = new HashMap<>();
        int index = 0;

        while (index < content.length()) {
            // 获取字段类型（前两位）
            String fieldType = content.substring(index, index + 2);
            index += 2;

            // 获取字段长度（下两位）
            int fieldLength = Integer.parseInt(content.substring(index, index + 2));
            index += 2;

            // 获取字段内容
            String fieldValue = content.substring(index, index + fieldLength);
            index += fieldLength;

            // 存储字段
            dataMap.put(fieldType, fieldValue);
        }

        return dataMap;
    }

}
