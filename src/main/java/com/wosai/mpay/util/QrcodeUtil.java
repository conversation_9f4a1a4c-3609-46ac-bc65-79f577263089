package com.wosai.mpay.util;

import com.google.zxing.*;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.common.HybridBinarizer;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/11/20.
 */
public class QrcodeUtil {

    public static String readQRCodeFromBase64(String base64String) throws IOException, NotFoundException, ChecksumException, FormatException {
        // 解码Base64字符串为字节数组
        byte[] imageBytes = Base64.decode(base64String);

        // 将字节数组转换为BufferedImage
        ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
        BufferedImage bufferedImage = ImageIO.read(bis);
        bis.close();

        // 使用ZXing读取二维码内容
        LuminanceSource source = new BufferedImageLuminanceSource(bufferedImage);
        BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(source));

        Reader reader = new MultiFormatReader();
        Result result = reader.decode(bitmap);

        return result.getText();
    }
}
