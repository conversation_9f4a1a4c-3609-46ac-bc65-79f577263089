package com.wosai.mpay.util.ccb.giftcard;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.Security;
import java.util.Base64;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.ShortBufferException;
import javax.crypto.spec.SecretKeySpec;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

public class MCipherEncryptor {
    private String encryptKey = "9R@e8Y3#";

    static {
        if (Security.getProvider("BC") == null) {
            Security.addProvider(new BouncyCastleProvider());
        }

    }

    public MCipherEncryptor(String key) {
        this.encryptKey = key.substring(0, 8);
    }

    public String getEncryptKey() {
        return this.encryptKey;
    }

    public void setEncryptKey(String encryptKey) {
        this.encryptKey = encryptKey.substring(0, 8);
    }

    private static byte[] wrapBytes(byte[] srcBytes, byte[] wrapKey) throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, ShortBufferException, IllegalBlockSizeException, BadPaddingException, NoSuchProviderException, InvalidAlgorithmParameterException {
        SecretKeySpec key = new SecretKeySpec(wrapKey, "DES");
        Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding", "BC");
        cipher.init(1, key);
        byte[] cipherText = cipher.doFinal(srcBytes);
        return cipherText;
    }

    public String doEncrypt(String srcString) throws InvalidKeyException, NoSuchAlgorithmException, NoSuchPaddingException, ShortBufferException, IllegalBlockSizeException, BadPaddingException, NoSuchProviderException, InvalidAlgorithmParameterException, UnsupportedEncodingException {
        byte[] cipherBytes = wrapBytes(srcString.getBytes("utf-16"), this.encryptKey.getBytes("ISO-8859-1"));
        String basedString = Base64.getEncoder().encodeToString(cipherBytes);
        String resultString = basedString.replaceAll("\\+", ",");
        resultString = insertEveryNCharacter(resultString,"\n",76);
        return URLEncoder.encode(resultString, "iso-8859-1");
    }

    public static String insertEveryNCharacter(String text, String insert, int everyN){
        StringBuilder sb = new StringBuilder();
        for(int i = 0; i < text.length(); i++){
            if(i >0 && i % everyN == 0){
                sb.append(insert);
            }
            sb.append(text.charAt(i));
        }
        return sb.toString();
    }
}

