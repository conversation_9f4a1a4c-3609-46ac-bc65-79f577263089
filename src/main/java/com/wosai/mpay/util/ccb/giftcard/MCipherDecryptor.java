package com.wosai.mpay.util.ccb.giftcard;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//


import java.io.IOException;
import java.net.URLDecoder;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.Security;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.ShortBufferException;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

public class MCipherDecryptor {
    private String encryptKey = "9R@e8Y3#";

    static {
        if (Security.getProvider("BC") == null) {
            Security.addProvider(new BouncyCastleProvider());
        }

    }

    public MCipherDecryptor(String key) {
        this.encryptKey = key.substring(0, 8);
    }

    public String getEncryptKey() {
        return this.encryptKey;
    }

    public void setEncryptKey(String encryptKey) {
        this.encryptKey = encryptKey.substring(0, 8);
    }

    private static byte[] getSrcBytes(byte[] srcBytes, byte[] wrapKey) throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, ShortBufferException, IllegalBlockSizeException, BadPaddingException, NoSuchProviderException, InvalidAlgorithmParameterException {
        SecretKeySpec key = new SecretKeySpec(wrapKey, "DES");
        Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding", "BC");
        cipher.init(2, key);
        byte[] cipherText = cipher.doFinal(srcBytes);
        return cipherText;
    }

    public String getDecodeString(String urlString) throws IOException, InvalidKeyException, NoSuchAlgorithmException, NoSuchPaddingException, ShortBufferException, IllegalBlockSizeException, BadPaddingException, NoSuchProviderException, InvalidAlgorithmParameterException {
        String tempString = URLDecoder.decode(urlString, "ISO-8859-1");
        String basedString = tempString.replaceAll(",", "+");
        byte[] tempBytes = Base64.decodeBase64(basedString);
        byte[] tempSrcBytes = getSrcBytes(tempBytes, this.encryptKey.getBytes("ISO-8859-1"));
        return new String(tempSrcBytes, "ISO-8859-1");
    }

    public String doDecrypt(String urlString) throws IOException, InvalidKeyException, NoSuchAlgorithmException, NoSuchPaddingException, ShortBufferException, IllegalBlockSizeException, BadPaddingException, NoSuchProviderException, InvalidAlgorithmParameterException {
        String tempString = URLDecoder.decode(urlString, "iso-8859-1");
        String basedString = tempString.replaceAll(",", "+");
        byte[] tempBytes = Base64.decodeBase64(basedString);
        byte[] tempSrcBytes = getSrcBytes(tempBytes, this.encryptKey.getBytes("ISO-8859-1"));
        return new String(tempSrcBytes, "utf-16");
    }
}
