package com.wosai.mpay.util;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import org.w3c.dom.Attr;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import com.wosai.mpay.exception.MpayException;

public class XmlUtils {
    static DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
    private static boolean canReuseBuilders = false;
    private static final ThreadLocal<DocumentBuilder> REUSABLE_BUILDER = new ThreadLocal<DocumentBuilder>() {
        @Override
        protected DocumentBuilder initialValue() {
          try {
        	  return dbf.newDocumentBuilder();
          } catch (ParserConfigurationException e) {
            throw new RuntimeException(e);
          }
        }
    };

    static TransformerFactory tf = TransformerFactory.newInstance();
    static Pattern xmlEncodingPattern = Pattern.compile("encoding=\"(\\S{1,})\"");

    static{
    	try {
    		dbf.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
    	} catch (ParserConfigurationException e) {}
    	try {
			dbf.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
		} catch (ParserConfigurationException e) {}
    	try {
			dbf.setFeature("http://xml.org/sax/features/external-general-entities", false);
		} catch (ParserConfigurationException e) {}
    	try {
			dbf.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
		} catch (ParserConfigurationException e) {}
    	try {
			dbf.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
		} catch (ParserConfigurationException e) {}
    	try {
			dbf.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
		} catch (ParserConfigurationException e) {}
    	dbf.setXIncludeAware(false);
    	dbf.setExpandEntityReferences(false);
        try {
            DocumentBuilder builder = dbf.newDocumentBuilder();
            builder.reset();
            canReuseBuilders = true;
          } catch (UnsupportedOperationException e) {
            canReuseBuilders = false;
          } catch (ParserConfigurationException e) {
        	  canReuseBuilders = false;
          }
    }
    
    public static Map<String, Object> parse(String source) {
        String charset = "UTF-8";
        String header = "<?xml version=";
        if (source.startsWith(header)) {
            Matcher matcher = xmlEncodingPattern.matcher(source);
            if(matcher.find()){
                charset = matcher.group(1);
            }
        }
        
        try {
            byte[] bytes = source.getBytes(charset);
            return parse(new ByteArrayInputStream(bytes));
        }
        catch (UnsupportedEncodingException e) {
            return null;
        }
    }

    public static Map<String, Object> parse(InputStream in) {
        try {
            Document document = getBuilder().parse(in);
            
            Object ret = getElementValue(document.getDocumentElement());
            if (ret instanceof Map) {
                return (Map<String, Object>)ret;
            }else{
                return Collections.emptyMap();
            }
            
        }
        catch (Exception e) {
            return Collections.emptyMap();
        }
        
    }
    
    private static Object getElementValue(Element element) {
        Map<String, Object> m = new LinkedHashMap<String, Object>();
        NodeList nodeList = element.getChildNodes();
        for (int i=0; i<nodeList.getLength(); ++i) {
            Node child = nodeList.item(i);
            if(child.getNodeType() == Node.ELEMENT_NODE) {
                Element e = (Element)child;
                if (m.containsKey(e.getTagName())){
                    Object existingValue = m.get(e.getTagName());
                    if(existingValue instanceof List) {
                        ((List<Object>)existingValue).add(getElementValue(e));
                    }else{
                        List<Object> list = new ArrayList<Object>();
                        list.add(existingValue);
                        list.add(getElementValue(e));
                        m.put(e.getTagName(), list);
                    }
                }else{
                    m.put(e.getTagName(), getElementValue(e));
                }
            }
        }

        if (m.isEmpty()) {
            NamedNodeMap attributes = element.getAttributes();
            if (attributes == null || attributes.getLength() == 0) {
                return element.getTextContent();
            }else {
                for(int i=0; i<attributes.getLength(); ++i) {
                    Node node = attributes.item(i);
                    if (node instanceof Attr) {
                        m.put(((Attr)node).getName(), ((Attr)node).getValue());
                    }
                }
                m.put("value", element.getTextContent());
                return m;
            }
        }else{
            return m;
        }
    }
    
    private static Element kv2element(Document doc, String key, Object value) {
        Element element = doc.createElement(key);
        if (value instanceof Map) {
            for (Element child: map2elements(doc, (Map<String,Object>)value)) {
                element.appendChild(child);
            }
        }else if(value instanceof Integer){
        	element.appendChild(doc.createTextNode(value + ""));
        }else if(value instanceof Long){
        	element.appendChild(doc.createTextNode(value + ""));
        }else{
            element.appendChild(doc.createTextNode((String)value));
        }
        return element;
    }
    private static List<Element> map2elements(Document doc, Map<String, Object> m) {
        List<Element> elements = new ArrayList<Element>();
        for(String key: m.keySet()) {
            Object value = m.get(key);
            
            if (value instanceof List) {
                for (Object v: (List<Object>)value){
                    elements.add(kv2element(doc, key, v));
                }
            }else{
                elements.add(kv2element(doc, key, value));
            }
        }
        return elements;
    }

    public static Document map2document(Map<String, Object> m, String rootTag) {
        try {
            Document doc = getBuilder().newDocument();
            Element rootElement = doc.createElement(rootTag);
            doc.appendChild(rootElement);
            for(Element child: map2elements(doc, m)) {
                rootElement.appendChild(child);
            }
            return doc;
        }
        catch (ParserConfigurationException e) {
            return null;
        }
    }

    public static String map2XmlString(Map<String, Object> m, String rootTag) throws MpayException {
        Document doc = map2document(m, rootTag);
        return document2XmlString(doc);

    }

    public static String document2XmlString(Document doc) throws MpayException {
        TransformerFactory tf = TransformerFactory.newInstance();
        Transformer transformer = null;
        try {
            transformer = tf.newTransformer();
            transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");
            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(doc), new StreamResult(writer));
            return writer.getBuffer().toString();
        } catch (Exception e) {
            throw  new MpayException("failed to transformer document to string", e);
        }

    }

    public static void write(Map<String, Object> m, String rootTag, String encoding, OutputStream out) {
        try {
            Document doc = map2document(m, rootTag);
            Transformer transformer = tf.newTransformer();
            DOMSource source = new DOMSource(doc);
            StreamResult result = new StreamResult(out);
            transformer.setOutputProperty(OutputKeys.ENCODING, encoding);
            transformer.transform(source, result);
        }
        catch (TransformerConfigurationException e) {
        }
        catch (TransformerException e) {
        }
    }

    public static void write(Map<String, Object> value, String encoding, OutputStream out) {
        if (value.size() != 1) {
            return;
        }
        Iterator<String> it = value.keySet().iterator();
        String rootTag = it.next();
        if(value.get(rootTag) instanceof Map) {
            write((Map<String,Object>)value.get(rootTag), rootTag, encoding, out);
        }
    }
    
    public static void writeMulti(Map<String, Object> m, String encoding, OutputStream out) {
        try {
            Document doc = map2document(m, "ROOT");
            Transformer transformer = tf.newTransformer();
            
            StreamResult result = new StreamResult(out);
            transformer.setOutputProperty(OutputKeys.ENCODING, encoding);
            transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");
            
            Node root = doc.getDocumentElement();
            for(int i=0; i<root.getChildNodes().getLength(); ++i) {
                Node child = root.getChildNodes().item(i);
                DOMSource source = new DOMSource(child);
                transformer.transform(source, result);
            }
        }
        catch (TransformerConfigurationException e) {
        }
        catch (TransformerException e) {
        }
    }
    
    private static DocumentBuilder getBuilder() throws ParserConfigurationException {
        DocumentBuilder builder;
        if (canReuseBuilders) {
          builder = REUSABLE_BUILDER.get();
          builder.reset();
        } else {
          builder = dbf.newDocumentBuilder();
        }
        return builder;
    }

    public static void main(String[] args) {

        String aa = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<root xmlns=\"namespace_string\">" +
                "<MsgHeader>" +
                "<name>234</name>" +
                "</MsgHeader>" +
                "<MsgBody>" +
                "<name>123</name>" +
                "</MsgBody>" +
                "</root>";
        Map  bb =  parse(aa);


        String source = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><XML><UserId><id>123</id><name>刘盾</name></UserId><UserId><id>234</id><name>刘盾</name></UserId><AppId><![CDATA[201411516]]></AppId></XML>";

        String rgex = "<UserId>(.*?)</UserId>";

        Pattern pattern = Pattern.compile(rgex);// 匹配的模式
        Matcher m = pattern.matcher(source);

        while (m.find()) {
            System.out.println(m.group(1));

        }
//        String source = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><XML><UserId><id>123</id><name>刘盾</name></UserId><UserId><id>234</id><name>刘盾</name></UserId><AppId><![CDATA[201411516]]></AppId></XML>";
//        Map<String, Object> m = parse(source);
//
//        System.out.println(m);
//
//        write(Collections.<String, Object>singletonMap("response", m), "GBK", System.out);
//
//        System.out.println();
//
//        writeMulti(m, "GBK", System.out);


    }
}
