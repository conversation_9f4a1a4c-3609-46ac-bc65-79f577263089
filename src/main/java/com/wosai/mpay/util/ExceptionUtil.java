package com.wosai.mpay.util;

import com.wosai.mpay.exception.MpayApiConnectError;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayApiReadError;
import org.apache.http.NoHttpResponseException;
import org.apache.http.ProtocolException;
import org.apache.http.auth.AuthenticationException;
import org.apache.http.auth.MalformedChallengeException;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.ConnectionPoolTimeoutException;

import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;

/**
 * Created by w<PERSON><PERSON><PERSON><PERSON> on 05/03/2018.
 */
public class ExceptionUtil {


    public static String getAllExceptionMessage(Throwable t) {
        if(t == null){
            return "";
        }
        String message = t.getMessage();
        StringBuilder sb = new StringBuilder(t.getClass() + ":");
        sb.append(message == null ? "" : message);
        Throwable cause = t.getCause();
        while (cause != null) {
            String causeMessage = cause.getMessage();
            sb.append(";").append(causeMessage);
            cause = cause.getCause();
        }
        return sb.toString();
    }


    public static MpayApiNetworkError transformToMpayApiNetworkError(Throwable t) {
        if (t instanceof UnknownHostException || t instanceof ConnectTimeoutException || t instanceof ConnectionPoolTimeoutException
                || t instanceof ProtocolException || t instanceof MalformedChallengeException || t instanceof AuthenticationException
                || t instanceof ConnectException
                ) {
            return new MpayApiConnectError("failed to establish network connection", t);
        } else if (t instanceof SocketTimeoutException || t instanceof NoHttpResponseException) {
            return new MpayApiReadError("failed to read response", t);
        } else {
            return new MpayApiReadError("failed to read response", t);
        }


    }
}
