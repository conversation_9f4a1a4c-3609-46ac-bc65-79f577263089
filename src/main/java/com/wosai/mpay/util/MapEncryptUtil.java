package com.wosai.mpay.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2024-09-19
 */

public class MapEncryptUtil {
    /**
     * 给指定key的值，重置为新的值，并返回新map
     *
     * @param map
     * @param keysToReset
     * @param newValue
     * @return
     */
    public static Map<String, Object> resetValueAndReturnNewMap(Map<String, Object> map, List<String> keysToReset, Object newValue) {
        Map<String, Object> newMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue() instanceof Map) {
                newMap.put(entry.getKey(), resetValueAndReturnNewMap((Map<String, Object>) entry.getValue(), keysToReset, newValue));
                continue;
            }
            if (keysToReset.contains(entry.getKey())) {
                newMap.put(entry.getKey(), newValue);
                continue;
            }

            if (entry.getValue() instanceof List) {
                newMap.put(entry.getKey(), resetValueInList((List<Object>) entry.getValue(), keysToReset, newValue));
                continue;
            }
            newMap.put(entry.getKey(), entry.getValue());
        }
        return newMap;
    }

    /**
     * 重置list中的值
     *
     * @param list
     * @param keysToReset
     * @param newValue
     * @return
     */
    private static List<Object> resetValueInList(List<Object> list, List<String> keysToReset, Object newValue) {
        List<Object> newList = new ArrayList<>();
        for (Object item : list) {
            if (item instanceof Map) {
                newList.add(resetValueAndReturnNewMap((Map<String, Object>) item, keysToReset, newValue));
            } else {
                newList.add(item);
            }
        }
        return newList;
    }
}
