package com.wosai.mpay.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.mpay.api.HttpResourceCallback;
import com.wosai.mpay.exception.MpayApiUnknownResponse;
import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.slf4j.Logger;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CancellationException;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 05/03/2018.
 * 支付通道的client的工具类 提供统一入口来打印请求响应的相关日志, 提供对支付通道返回的解析操作
 */
public class AsyncClientUtil {

    public static final ObjectMapper objectMapper = new ObjectMapper();

    public static enum ResponseType {
        XML, JSON, STRING
    }

    public static final String CLIENTS_KEY_NON_CUSTOM_SSL_CONTEXT = "default";
    private static final Map<String, CloseableHttpAsyncClient> clients = new HashMap<>();

    /**
     *
     * @param sslContext
     * @param hostnameVerifier
     * @return
     */
    public static CloseableHttpAsyncClient getCloseableHttpAsyncClient(SSLContext sslContext, HostnameVerifier hostnameVerifier){
        String key = sslContext != null ? sslContext.toString() : CLIENTS_KEY_NON_CUSTOM_SSL_CONTEXT;
        CloseableHttpAsyncClient client = clients.get(key);
        if(client == null){
            synchronized (AsyncClientUtil.class){
                client = clients.get(key);
                if(client == null){
                    final CloseableHttpAsyncClient notReadyClient = getHttpAsyncClientBuilder(sslContext, hostnameVerifier).build();
                    notReadyClient.start();
                    client = notReadyClient;
                    clients.put(key, client);
                }
            }
        }
        return client;
    }

    public static HttpAsyncClientBuilder getHttpAsyncClientBuilder(SSLContext sslContext, HostnameVerifier hostnameVerifier){
        HttpAsyncClientBuilder httpAsyncClientBuilder = HttpAsyncClients.custom();
        httpAsyncClientBuilder.setMaxConnPerRoute(Integer.MAX_VALUE);
        httpAsyncClientBuilder.setMaxConnTotal(5000);
        httpAsyncClientBuilder.setUserAgent("aop-sdk-java");
        httpAsyncClientBuilder.disableAuthCaching();
        httpAsyncClientBuilder.disableCookieManagement();
        if(sslContext != null){
            httpAsyncClientBuilder.setSSLContext(sslContext);
        }
        if(hostnameVerifier != null){
            httpAsyncClientBuilder.setSSLHostnameVerifier(hostnameVerifier);
        }
        return httpAsyncClientBuilder;
    }

    public static void logRequest(Logger logger, Object request){
        logger.debug("request: {}", request);
    }

    public static void logResponse(Logger logger, String url, long cost, Object response, Throwable throwable){
        if(throwable != null){
            logger.error(String.format("url: %s, cost: %d ms, get response error:  %s", url, cost, ExceptionUtil.getAllExceptionMessage(throwable)), throwable);
        }else{
            logger.debug(String.format("url: %s, cost: %d ms, get response normal:  %s", url, cost, response));
        }

    }


    /**
     *
     * @param logger
     * @param responseCharset
     * @param responseType 如果为null, 会自动判断返回的内容是xml还是json, 如果是xml, json 返回会转为map, 其他转为字符串。
     * @param callback
     * @return
     */
    public static <T> FutureCallback<HttpResponse> getFutureCallback(Logger logger, String responseCharset, ResponseType responseType, HttpResourceCallback<T> callback){
        return new FutureCallback<HttpResponse>() {
            @Override
            public void completed(HttpResponse httpResponse) {
                try{
                    String charset = responseCharset;
                    if(StringUtils.isEmpty(charset) && httpResponse.getEntity() != null){
                        Header contentTypeHeader = httpResponse.getEntity().getContentType();
                        charset = WebUtils.getResponseCharset(contentTypeHeader != null ? contentTypeHeader.getValue() : null, "UTF8");
                    }
                    String response = httpResponse.getEntity() != null ? WebUtils.getStreamAsString(httpResponse.getEntity().getContent(), charset) : null;
                    T result = null;
                    ResponseType localResponseType = responseType == null ? guessResponseType(response) : responseType;
                    int statusCode = httpResponse.getStatusLine().getStatusCode();
                    if(httpResponse.getStatusLine().getStatusCode() != 200){
                        throw new MpayApiUnknownResponse("response code is " + statusCode, null);
                    }
                    if(localResponseType == ResponseType.XML){
                        result = (T)XmlUtils.parse(response);
                    }else if(localResponseType == ResponseType.JSON){
                        result = (T)objectMapper.readValue(response.getBytes(), Map.class);
                    }else {
                        result = (T)response;
                    }
                    if(result == null || (result instanceof  Map && ((Map)result).isEmpty())){
                        throw new MpayApiUnknownResponse("result is empty", null);
                    }
                    callback.onComplete(result);
                }catch (Exception e){
                    callback.onError(e);
                }
            }

            @Override
            public void failed(Exception ex) {
                try{
                    callback.onError(ExceptionUtil.transformToMpayApiNetworkError(ex));
                }catch (Exception e){
                    logger.warn("invoking failed callback occurs error, it is ok if the exception is NPE in some cases, exception: {}", ExceptionUtil.getAllExceptionMessage(e), e);
                }
            }

            @Override
            public void cancelled() {
                failed(new CancellationException("request canceled"));
            }
        };

    }

    private static ResponseType guessResponseType(String response){
        if(response == null){
            return null;
        }else if(response.startsWith("{")){
            return ResponseType.JSON;
        }else if(response.startsWith("<")){
            return ResponseType.XML;
        }else {
            return null;
        }
    }
}
