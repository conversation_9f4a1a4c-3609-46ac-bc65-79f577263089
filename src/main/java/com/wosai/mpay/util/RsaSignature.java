package com.wosai.mpay.util;

import com.google.gson.*;
import com.wosai.mpay.exception.MpayException;

import javax.crypto.Cipher;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URL;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.sql.SQLOutput;
import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/23.
 */
public class RsaSignature {
    public static final String SIG_ALG_NAME_SHA256_With_RSA = "SHA256WithRSA";
    public static final String SIG_ALG_NAME_SHA512_With_RSA = "SHA512WithRSA";
    public static final String SIG_ALG_NAME_RSA_OAEP_With_SM4_128_CBC = "RSA_OAEP_with_SM4_128_CBC";
    public static final String SIG_ALG_NAME_SHA1_With_RSA = "SHA1WithRSA";
    public static final String SIG_ALG_NAME_MD5_With_RSA = "MD5WithRSA";
    public static final String KEY_RSA = "RSA";
    public static final String CRYPT_RSA_TRANSFORMATION = "RSA/ECB/PKCS1Padding";

    /**
     * 签名
     * 设所有发送或接收到的数据为集合M，将集合M内非空参数值的参数按照参数名ASCII码从小到 大排序(字典序)，使用URL键值对的格式(即key1=value1&key2=value2...)拼接成字符串stringA。 特别注意以下重要规则:
     *  1)参数名ASCII码从小到大排序(字典序);
     *  2)如果参数的值为空不参与签名;
     *  3)参数名区分大小写;
     * @param params
     * @param sigAlgName SHA256WithRSA
     * @param privateKey
     * @return
     * @throws Exception
     */
    public static String sign(Map params, String sigAlgName, String privateKey) throws MpayException {
        String signData = getSignCheckContent(params);
        return sign(signData, sigAlgName, privateKey);
    }


    public static String sign(String data, String sigAlgName, String privateKey, String charset) throws MpayException {
        if(StringUtils.isEmpty(charset)) {
            return sign(data.getBytes(), sigAlgName, privateKey);
        }else {
            try {
                return sign(data.getBytes(charset), sigAlgName, privateKey);
            } catch (UnsupportedEncodingException e) {
                throw new MpayException("failed to sign", e);
            }
        }
    }

    public static String sign(byte[] data, String sigAlgName, String privateKey) throws MpayException{
        try{
            Signature sa = SecurityUtil.getSignature(sigAlgName);
            sa.initSign(getPrivateKeyFromPKCS8(KEY_RSA, privateKey));
            sa.update(data);
            return Base64.encode(sa.sign());
        }catch (Exception e){
            throw new MpayException("failed to sign", e);
        }
    }

    public static String cmccSign(String data, String sigAlgName, String privateKey, String charset) throws MpayException{
        try{
            Signature sa = SecurityUtil.getSignature(sigAlgName);
            sa.initSign(getPrivateKeyFromPKCS8(KEY_RSA, privateKey));
            if(StringUtils.isEmpty(charset)) {
                sa.update(data.getBytes());
            }else {
                sa.update(data.getBytes(charset));
            }
            return Hex.byteToHex(sa.sign());
        }catch (Exception e){
            throw new MpayException("failed to sign", e);
        }
    }

    /**
     * @param params
     * @param sigAlgName SHA256WithRSA
     * @param privateKey
     * @return
     * @throws Exception
     */
    public static String hxbankSign(String params, String sigAlgName, String privateKey) throws MpayException {
        String signData = getHXSignCheckContent(params);
        return sign(signData, sigAlgName, privateKey, "utf-8");
    }

    public static String icbcSign(String serviceUrl, Map params, String sigAlgName, String privateKey) throws MpayException {
        String signData = getSignCheckContent(params);
        if (Objects.nonNull(serviceUrl)) {
            signData = serviceUrl + "?" + signData;
        }
        return sign(signData, sigAlgName, privateKey);
    }

    /**
     *  交行参数加签
     * @param serviceUrl
     * @param params
     * @param sigAlgName SHA256WithRSA
     * @param privateKey
     * @return
     * @throws Exception
     */
    public static String bocomSign(String serviceUrl, Map params, String sigAlgName, String privateKey, String charset) throws MpayException {
        String signData = getBOCOMSignCheckContent(params, serviceUrl);
        return sign(signData, sigAlgName, privateKey, charset);
    }

    public static byte[] encryptByPublicKey(byte[] data, String key) throws Exception {
        PublicKey publicKey = getPublicKeyFromX509(KEY_RSA, key);
        Cipher cipher = Cipher.getInstance(CRYPT_RSA_TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        return cipher.doFinal(data);
    }

    public static byte[] encryptByPublicKey(byte[] data, String key, Cipher cipher) throws Exception {
        PublicKey publicKey = getPublicKeyFromX509(KEY_RSA, key);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        return cipher.doFinal(data);
    }

    public static byte[] decryptByPrivateKey(byte[] data, String key) throws Exception {
        PrivateKey privateKey = getPrivateKeyFromPKCS8(KEY_RSA, key);
        Cipher cipher = Cipher.getInstance(CRYPT_RSA_TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        return cipher.doFinal(data);
    }

    public static String sign(String data, String sigAlgName, String privateKey) throws MpayException{
        return sign(data, sigAlgName, privateKey, null);
    }

    /**
     * 验签
     * @param data 待验签的内容
     * @param ignoreKey 不参与签名的字段
     * @param sign 签名
     * @param sigAlgName 验签算法
     * @param publicKey 验签公钥
     * @return
     * @throws MpayException
     */
    public static boolean validateSign(Map<String,Object> data, String ignoreKey, String sign, String sigAlgName, String publicKey) throws MpayException{
        String signData = getSignCheckContent(data, ignoreKey);
        return validateSign(signData, sign, sigAlgName, publicKey);
    }


    /**
     * 验签
     * @param data 待验签的内容
     * @param sign 签名
     * @param sigAlgName 验签算法
     * @param publicKey 验签公钥
     * @param charset 验签公钥
     * @return
     * @throws MpayException
     */
    public static boolean validateSign(String data, String sign, String sigAlgName, String publicKey, String charset) throws MpayException{
        if(StringUtils.isEmpty(charset)) {
            return validateSign(data.getBytes(), sign, sigAlgName, publicKey);
        }else {
            try {
                return validateSign(data.getBytes(charset), sign, sigAlgName, publicKey);
            } catch (UnsupportedEncodingException e) {
                throw new MpayException("failed to validate sign", e);
            }
        }
    }

    /**
     * 验签
     * @param data 待验签的内容
     * @param sign 签名
     * @param sigAlgName 验签算法
     * @param publicKey 验签公钥
     * @return
     * @throws MpayException
     */
    public static boolean validateSign(byte[] data, String sign, String sigAlgName, String publicKey) throws MpayException{
        try {
            Signature signature = SecurityUtil.getSignature(sigAlgName);
            PublicKey localPublicKey = getPublicKeyFromX509(KEY_RSA, publicKey);
            signature.initVerify(localPublicKey);
            signature.update(data);
            byte[] bytesSign = Base64.decode(sign);
            return signature.verify(bytesSign);
        }catch (Exception e){
            throw new MpayException("failed to validate sign", e);
        }
    }

    /**
     * 验签
     * @param data 待验签的内容
     * @param sign 签名
     * @param sigAlgName 验签算法
     * @param publicKey 验签公钥
     * @return
     * @throws MpayException
     */
    public static boolean validateSign(byte[] data, byte[] sign, String sigAlgName, String publicKey) throws MpayException{
        try {
            Signature signature = SecurityUtil.getSignature(sigAlgName);
            PublicKey localPublicKey = getPublicKeyFromX509(KEY_RSA, publicKey);
            signature.initVerify(localPublicKey);
            signature.update(data);
            return signature.verify(sign);
        }catch (Exception e){
            throw new MpayException("failed to validate sign", e);
        }
    }
    
    /**  验签
     * @param data 签名原数据
     * @param sign 签名
     */
    public static boolean validateSign(String data, String sign, String sigAlgName, String publicKey) throws MpayException{
        return validateSign(data, sign, sigAlgName, publicKey, null);
    }


    public static String getSignCheckContent(Map params) {
        return getSignCheckContent(params, null);
    }

    public static String getSignCheckContent(Map params, String ignoreKey) {
        return getSignCheckContent(params, ignoreKey, false);
    }

    public static String getBOCOMSignCheckContent(Map params, String serviceUrl) throws MpayException {

        String path;
        try {
            path = (new URL(serviceUrl)).getPath();
            if (path.contains("/api/")) {
                path = path.substring(path.indexOf("/api/"));
            }
        } catch (Exception e) {
            throw new MpayException("failed to get path", e);
        }

        String signStr = getSignCheckContent(params, null, true);

        return path + "?" + signStr;
    }

    /**
     * 设所有发送或接收到的数据为集合M，将集合M内非空参数值的参数按照参数名ASCII码从小到 大排序(字典序)，使用URL键值对的格式(即key1=value1&key2=value2...)拼接成字符串stringA。 特别注意以下重要规则:
     *  1)参数名ASCII码从小到大排序(字典序);
     *  2)如果参数的值为空不参与签名;
     *  3)参数名区分大小写;
     *  @param params
     *  @param ignoreKey 不参与签名的字段
     *  @param ignoreNullValue 空值不参与签名
     *  @return
     */
    public static String getSignCheckContent(Map params, String ignoreKey, boolean ignoreNullValue) {
        if (params == null) {
            return null;
        }
        StringBuffer content = new StringBuffer();
        List<String> keys = new ArrayList<String>(params.keySet());
        Collections.sort(keys);
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            if(key.equals(ignoreKey)){
                continue;
            }
            Object value = params.get(key);
            if (ignoreNullValue && (StringUtils.isEmpty(key) || (Objects.isNull(value) || Objects.equals(value, StringUtils.EMPTY)))) {
                continue;
            }
            content.append((i == 0 ? "" : "&") + key + "=" + value);
        }
        return content.toString();
    }

    /**
     * 设所有发送或接收到的数据为集合M，将集合M内非空参数值的参数按照参数名ASCII码从小到 大排序(字典序)，使用URL键值对的格式(即key1=value1&key2=value2...)拼接成字符串stringA。 特别注意以下重要规则:
     *  1)参数名ASCII码从小到大排序(字典序);
     *  2)如果参数的值为空不参与签名;
     *  3)参数名区分大小写;
     *  @param json
     *  @return
     */
    public static String getHXSignCheckContent(String json) {
        String ret;
        json = sortJson(json);
        StringBuilder sb = new StringBuilder();
        Map<String, Object> map1 = (Map)(new Gson()).fromJson(json, Map.class);
        Iterator var5 = map1.entrySet().iterator();

        while(true) {
            Map.Entry entry;
            String key;
            do {
                if (!var5.hasNext()) {
                    sb.deleteCharAt(sb.length() - 1);
                    ret = sb.toString();
                    ret = ret.replace("\r", "");
                    ret = ret.replace("\n", "");
                    ret = ret.replace(",", "");
                    ret = ret.replace(" ", "");
                    return ret;
                }

                entry = (Map.Entry)var5.next();
                key = (String)entry.getKey();
            } while(key == null && "".equals(key.trim()));

            Object o = entry.getValue();
            if (o != null && !"".equals(o.toString().trim())) {
                sb.append(key + "=" + o.toString() + "&");
            }
        }
    }

    private static String sortJson(String json) {
        Gson g = (new GsonBuilder()).setPrettyPrinting().create();
        JsonParser p = new JsonParser();
        JsonElement e = p.parse(json);
        sort(e);
        return g.toJson(e);
    }

    public static void sort(JsonElement e) {
        if (!e.isJsonNull() && !e.isJsonPrimitive()) {
            if (e.isJsonArray()) {
                JsonArray a = e.getAsJsonArray();
                Iterator<JsonElement> it = a.iterator();
                it.forEachRemaining((i) -> {
                    sort(i);
                });
            } else {
                if (e.isJsonObject()) {
                    Map<String, JsonElement> tm = new TreeMap(getComparator());
                    Iterator var3 = e.getAsJsonObject().entrySet().iterator();

                    while(var3.hasNext()) {
                        Map.Entry<String, JsonElement> en = (Map.Entry)var3.next();
                        tm.put(en.getKey(), en.getValue());
                    }

                    Iterator var5 = tm.entrySet().iterator();

                    while(var5.hasNext()) {
                        Map.Entry<String, JsonElement> en = (Map.Entry)var5.next();
                        String key = en.getKey();
                        JsonElement val = en.getValue();
                        e.getAsJsonObject().remove(key);
                        e.getAsJsonObject().add(key, val);
                        sort(val);
                    }
                }

            }
        }
    }

    private static Comparator<String> getComparator() {
        return String::compareTo;
    }

    public static PrivateKey getPrivateKeyFromPKCS8(String algorithm, String privateKey) throws Exception {
        return getPrivateKeyFromPKCS(algorithm, privateKey);
    }

    public static PrivateKey getPrivateKeyFromPKCS(String algorithm, String privateKey) throws Exception {
        if (privateKey.startsWith("-----BEGIN PRIVATE KEY-----")) {
            privateKey = privateKey.replace("-----BEGIN PRIVATE KEY-----", "");
            privateKey = privateKey.replace("-----END PRIVATE KEY-----", "");
        }else if(privateKey.startsWith("-----BEGIN RSA PRIVATE KEY")){
            return SecurityUtil.getKeyFactory(algorithm).generatePrivate(new PKCS8EncodedKeySpec(buildPKCSKey(privateKey)));
        }
        return SecurityUtil.getKeyFactory(algorithm).generatePrivate(new PKCS8EncodedKeySpec(Base64.decode(privateKey)));
    }



    private static byte[] buildPKCSKey(String privateKey) throws IOException {
        if (privateKey.contains("-----BEGIN PRIVATE KEY-----")) {
            return Base64.decode(privateKey.replaceAll("-----\\w+ PRIVATE KEY-----", ""));
        } else if (privateKey.contains("-----BEGIN RSA PRIVATE KEY-----")) {
            final byte[] innerKey = Base64.decode(privateKey.replaceAll("-----\\w+ RSA PRIVATE KEY-----", ""));
            final byte[] result = new byte[innerKey.length + 26];
            System.arraycopy(Base64.decode("MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKY="), 0, result, 0, 26);
            System.arraycopy(BigInteger.valueOf(result.length - 4).toByteArray(), 0, result, 2, 2);
            System.arraycopy(BigInteger.valueOf(innerKey.length).toByteArray(), 0, result, 24, 2);
            System.arraycopy(innerKey, 0, result, 26, innerKey.length);
            return result;
        } else {
            return Base64.decode(privateKey);
        }
    }

    public static PublicKey getPublicKeyFromX509(String algorithm, String publicKey) throws Exception {
        return SecurityUtil.getKeyFactory(algorithm).generatePublic(new X509EncodedKeySpec(Base64.decode(publicKey)));
    }



}
