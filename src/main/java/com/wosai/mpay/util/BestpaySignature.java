package com.wosai.mpay.util;

import java.util.List;
import java.util.Map;

import com.wosai.mpay.exception.MpayException;

public class BestpaySignature {
	private static final String CHARSET = "UTF-8";

    /**
     * 获取md5签名, 返回大写字符串
     * @param content
     * @param key
     * @param charset
     * @return
     * @throws MpayException
     */
    public static String getSign(Map<String,String> content, String key,  List<String> signColumns) throws MpayException {
        StringBuilder sb = new StringBuilder();
        for(int i = 0; i < signColumns.size(); i ++) {
            sb.append(signColumns.get(i).toUpperCase()).append("=").append(content.get(signColumns.get(i))).append("&");
        }
        sb.append("KEY=").append(key);
        
        String md5="";
        try {
            md5 = Digest.md5(sb.toString().getBytes(CHARSET)).toUpperCase();
        } catch (Exception e) {
            throw new MpayException("failed to generate md5 signature.", e);
        }
        return md5;
    }
    
    /**
     * 从API返回的XML数据里面重新计算一次签名
     * @param responseString API返回的XML数据
     * @return 新鲜出炉的签名
     * @throws MpayException
     */
    public static String getSignFromResponseString(String responseString, String key) throws MpayException {
        return null;
    }

    /**
     * 检验API返回的数据里面的签名是否合法，避免数据在传输的过程中被第三方篡改
     * @param responseString API返回的XML数据字符串
     * @return API签名是否合法
     * @throws MpayException
     */
    public static boolean checkIsSignValidFromResponseString(String responseString, String key) throws  MpayException {
        return false;
    }

    /**
     * 检验API返回的数据里面的签名是否合法，避免数据在传输的过程中被第三方篡改
     * @param response
     * @return API签名是否合法
     * @throws MpayException
     */
    public static boolean checkIsSignValidFromResponse(Map<String,Object> response, String key) throws  MpayException {
        return false;
    }
}
