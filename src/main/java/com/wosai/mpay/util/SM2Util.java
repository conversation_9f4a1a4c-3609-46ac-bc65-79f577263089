package com.wosai.mpay.util;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.PrivateKey;
import java.security.SecureRandom;
import java.security.Security;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;


import com.wosai.mpay.util.SM3;
import com.wosai.mpay.util.SignatureInfo;
import org.bouncycastle.asn1.ASN1Encodable;
import org.bouncycastle.asn1.ASN1Encoding;
import org.bouncycastle.asn1.ASN1InputStream;
import org.bouncycastle.asn1.ASN1Integer;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.DEROctetString;
import org.bouncycastle.asn1.DERSequence;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.asn1.sec.ECPrivateKey;
import org.bouncycastle.asn1.x509.SubjectPublicKeyInfo;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.AsymmetricCipherKeyPair;
import org.bouncycastle.crypto.CipherParameters;
import org.bouncycastle.crypto.CryptoException;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.generators.ECKeyPairGenerator;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECKeyGenerationParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.crypto.params.ParametersWithID;
import org.bouncycastle.crypto.params.ParametersWithRandom;
import org.bouncycastle.crypto.signers.SM2Signer;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.math.ec.ECCurve;
import org.bouncycastle.math.ec.ECFieldElement;
import org.bouncycastle.math.ec.ECPoint;

import cfca.sadk.algorithm.common.PKIException;
import cfca.sadk.algorithm.sm2.SM2PrivateKey;
import cfca.sadk.algorithm.sm2.SM2PublicKey;
import cfca.sadk.util.KeyUtil;

/**
 * <AUTHOR>
 * @Description SM2Util
 * @Date 2021/4/30 4:03 PM
 */
public class SM2Util {

    /* !< 设置默认椭圆曲线参数(P A B N Gx Gy), 以下设置表示采用国密7号曲线    */
    private static final BigInteger a  = new BigInteger("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC",16);
    private static final BigInteger b  = new BigInteger("28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93",16);
    private static final BigInteger gx = new BigInteger("32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7",16);
    private static final BigInteger gy = new BigInteger("BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0",16);
    private static final BigInteger n  = new BigInteger("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123",16);
    private static final BigInteger p  = new BigInteger("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF",16);

    /* !< 开源算法库BouncyCastle中用于SM2运算的对象实例 */
    private  ECFieldElement ecc_gx_fieldelement;
    private  ECFieldElement     ecc_gy_fieldelement;
    private  ECCurve ecc_curve;
    private  ECPoint ecc_point_g;
    private  ECDomainParameters ecc_bc_spec;
    private  ECKeyPairGenerator ecc_key_pair_generator;

    private static final X9ECParameters sm2p256v1 = GMNamedCurves.getByName("sm2p256v1");
    private static final int SM3_DIGEST_LENGTH_32 = 32;
    private static final byte[] defaultUserID = "1234567812345678".getBytes();
    private static final Map<String, byte[]> UNIONPAY_SM2_D_PRIVATE_KEY = new HashMap<String, byte[]>();
    private static final Map<String, SM2PublicKey> UNIONPAY_SM2_PUBLIC_KEY = new HashMap<String, SM2PublicKey>();

    private static SecureRandom random = new SecureRandom();

    private static ECPoint G;

    static{
        try{
            Security.addProvider(new BouncyCastleProvider());
        }catch(Exception e){
            throw new RuntimeException("加载BC配置出错");
        }
    }

    /**
     * 构造方法,通过国密7号曲线参数设置BC包的实例
     */
    @SuppressWarnings("deprecation")
    private SM2Util() {
        this.ecc_gx_fieldelement = new ECFieldElement.Fp(SM2Util.p, SM2Util.gx);
        this.ecc_gy_fieldelement = new ECFieldElement.Fp(SM2Util.p, SM2Util.gy);

        this.ecc_curve = new ECCurve.Fp(SM2Util.p, SM2Util.a, SM2Util.b);

        G = ecc_curve.createPoint(SM2Util.gx, SM2Util.gy);

        this.ecc_point_g = new ECPoint.Fp(this.ecc_curve, this.ecc_gx_fieldelement,this.ecc_gy_fieldelement, false);
        this.ecc_bc_spec = new ECDomainParameters(this.ecc_curve, this.ecc_point_g, SM2Util.n);

        ECKeyGenerationParameters ecc_ecgenparam = new ECKeyGenerationParameters(this.ecc_bc_spec, new SecureRandom());

        this.ecc_key_pair_generator = new ECKeyPairGenerator();
        this.ecc_key_pair_generator.init(ecc_ecgenparam);
    }

    public static SM2Util getInstance(){
        return new SM2Util();
    }

    /**
     * 计算Z值,该值将参与业务数据的SM3运算
     * @param userId         初始化向量,国密标准固定为"1234567812345678"
     * @param userKey        公钥数据,计算Z值需要SM2公钥x/y参与运算
     * @return               返回Z值
     */
    private byte[] sm2GetZ(byte[] userId, ECPoint userKey) {
        SM3Digest sm3 = new SM3Digest();

        /* !< 初始化向量长度和初始化数据参与Z值的计算                                */
        int len = userId.length * 8;
        sm3.update((byte) (len >> 8 & 0xFF));
        sm3.update((byte) (len & 0xFF));
        sm3.update(userId, 0, userId.length);

        /* !< 公钥x/y和椭圆曲线参数参与Z值的计算,数据(A B Gx Gy x y)长度固定32字节   */
        /* !< 当数据第一个字节大于0x80时BigInteger自动补0x00, 所以取后32字节有效数据 */
        byte[] p = SM2Util.a.toByteArray();
        sm3.update(p, p.length-32, 32);

        p = SM2Util.b.toByteArray();
        sm3.update(p, p.length-32, 32);

        p = SM2Util.gx.toByteArray();
        sm3.update(p, p.length-32, 32);

        p = SM2Util.gy.toByteArray();
        sm3.update(p, p.length-32, 32);

        p = userKey.normalize().getXCoord().toBigInteger().toByteArray();
        sm3.update(p, p.length-32, 32);

        p = userKey.normalize().getYCoord().toBigInteger().toByteArray();
        sm3.update(p, p.length-32, 32);

        /* !< 得到最终的Z值结果,整个过程是对初始化向量/公钥/椭圆曲线参数进行SM3运算 */
        byte[] md = new byte[sm3.getDigestSize()];
        sm3.doFinal(md, 0);
        return md;
    }

    /**
     * SM2签名运算,使用私钥对业务数据哈希值进行签名运算
     * @param md              业务数据哈希值
     * @param userD           SM2私钥
     * @return                SM2签名数据R/S
     */
    private BigInteger[] sm2Sign(byte[] md, BigInteger userD) {
        BigInteger e = new BigInteger(1, md);
        BigInteger k = null;
        ECPoint   kp = null;
        BigInteger r = null;
        BigInteger s = null;
        do {
            do {
                AsymmetricCipherKeyPair keypair = ecc_key_pair_generator.generateKeyPair();
                ECPrivateKeyParameters ecpriv = (ECPrivateKeyParameters) keypair.getPrivate();
                ECPublicKeyParameters ecpub = (ECPublicKeyParameters) keypair.getPublic();
                k = ecpriv.getD();
                kp = ecpub.getQ();

                r = e.add(kp.getXCoord().toBigInteger());
                r = r.mod(SM2Util.n);
            } while (r.equals(BigInteger.ZERO) || r.add(k).equals(SM2Util.n)||r.toString(16).length()!=64);

            BigInteger da_1 = userD.add(BigInteger.ONE);
            da_1 = da_1.modInverse(SM2Util.n);

            s = r.multiply(userD);
            s = k.subtract(s).mod(SM2Util.n);
            s = da_1.multiply(s).mod(SM2Util.n);
        } while (s.equals(BigInteger.ZERO)||(s.toString(16).length()!=64));

        return new BigInteger[]{r, s};
    }

    /**
     * SM2验签运算,使用公钥对业务数据哈希值进行验证运算
     * @param md              业务数据哈希值
     * @param userKey         SM2公钥
     * @param r               SM2签名数据R
     * @param s               SM2签名数据S
     * @return                验签成功返回true,否则返回false
     */
    private boolean sm2Verify(byte[] md, ECPoint userKey, BigInteger r, BigInteger s) {
        BigInteger e = new BigInteger(1, md);
        BigInteger t = r.add(s).mod(SM2Util.n);
        if (t.equals(BigInteger.ZERO)) {
            return false;
        } else {
            ECPoint x1y1 = ecc_point_g.multiply(s);
            x1y1 = x1y1.add(userKey.multiply(t));
            BigInteger R = e.add(x1y1.normalize().getXCoord().toBigInteger()).mod(SM2Util.n);
            return r.equals(R);
        }
    }

    /**
     * 将SM2签名数据R/S转化为DER编码
     * @param R               SM2签名数据R
     * @param S               SM2签名数据S
     * @return                DER编码后的SM2签名数据
     */
    private byte[] encodeDer(BigInteger R, BigInteger S) {
        byte[] r = R.toByteArray();
        byte[] s = S.toByteArray();

        int rLen = r.length;
        int sLen = s.length;

        /* !< SM2签名值的DER编码格式(0x30 + 数据总长度 + 0x02 + R的长度 + R + 0x02 + S的长度 + S */
        byte[] der = new byte[6+rLen+sLen];
        der[0]      = 0x30;
        der[1]      = (byte)(4+rLen+sLen);

        der[2]      = 0x02;
        der[3]      = (byte)rLen;
        System.arraycopy(r, 0, der, 4, rLen);

        der[4+rLen] = 0x02;
        der[5+rLen] = (byte)sLen;
        System.arraycopy(s, 0, der, 6+rLen, sLen);

        return der;
    }

    /**
     * 将DER编码的SM2签名转化为数据R/S
     * @param der
     * @return
     */
    private static BigInteger[] decodeDer(byte[] der) {
        /* !< der[3]和der[5+rLen]分别标识了R和S的数据长度                           */
        int rLen = (int)der[3];
        byte[] r = new byte[rLen];
        System.arraycopy(der, 4, r, 0, rLen);

        int sLen = (int)der[5+rLen];
        byte[] s = new byte[sLen];
        System.arraycopy(der, 6+rLen, s, 0, sLen);

        /* !< 将数组类型转化为BigInteger类型                                         */
        /* !< 注意此处不能使用new BigInteger(byte[]),因为他会忽略以0x00结尾的数组    */
        BigInteger[] RS = new BigInteger[2];
        RS[0] = new BigInteger(1, r);
        RS[1] = new BigInteger(1, s);

        return RS;
    }

    /* !< SM3运算的初始化向量,国密标准固定为"1234567812345678"               */
    private static final String USER_ID = "1234567812345678";

    /**
     * SM2withSM3签名
     * @param d               SM2私钥D（32字节）
     * @param sourceData      业务数据
     * @return                DER+Base64编码后的签名结果
     */
    public byte[] SM2Sign(byte[] d, byte[] sourceData) {
        /* !< 将byte[]类型的私钥数据转化为ECPoint实例                */
        BigInteger userD = new BigInteger(1, d);
        ECPoint userPriKey = ecc_point_g.multiply(userD);

        /* !< 通过初始化向量和密钥计算SM3的Z值                       */
        byte[] z = sm2GetZ(USER_ID.getBytes(), userPriKey);

        /* !< 通过Z值计算用户数据的哈希值                            */
        SM3Digest sm3Digest = new SM3Digest();
        sm3Digest.update(z, 0, z.length);
        sm3Digest.update(sourceData,0,sourceData.length);
        byte [] md = new byte[32];
        sm3Digest.doFinal(md, 0);

        /* !< 进行SM2签名,将签名值R/S进行DER编码和Base64编码后返回   */
        BigInteger[] rs = sm2Sign(md, userD);
        byte[] sign = encodeDer(rs[0], rs[1]);
        return java.util.Base64.getEncoder().encode(sign);
    }

    /**
     * SM2withSM3验证
     * @param x              SM2公钥X（32字节）
     * @param y              SM2公钥Y（32字节）
     * @param sourceData     业务数据
     * @param signData       DER+Base64编码后的签名结果
     * @return               true验签通过/false验签失败
     */
    public boolean SM2Verify(byte[] x, byte[] y, byte[] sourceData, byte[] signData) {
        return sm2Verify(x, y, sourceData, signData, defaultUserID, true);
    }

    /**
     * SM2withSM3验证
     * @param x              SM2公钥X（32字节）
     * @param y              SM2公钥Y（32字节）
     * @param sourceData     业务数据
     * @param signData       Base64编码后的签名结果
     * @param userId         秘钥id
     * @param isDer          签名是否为der编码
     * @return               true验签通过/false验签失败
     */
    public boolean sm2Verify(byte[] x, byte[] y, byte[] sourceData, byte[] signData, byte[] userId, boolean isDer) {
        /* !< 对公钥x/y进行DER编码,格式为(0x04 + x + y)              */
        byte[] publicKey = new byte[65];
        publicKey[0] = 0x04;
        System.arraycopy(x, 0, publicKey,  1, 32);
        System.arraycopy(y, 0, publicKey, 33, 32);
        /* !< 将byte[]类型的公钥数据转化为ECPoint实例                */
        ECPoint userPubKey = ecc_curve.decodePoint(publicKey);

        /* !< 通过初始化向量和密钥计算SM3的Z值                       */
        byte[] z = sm2GetZ(userId, userPubKey);
        /* !< 通过Z值计算用户数据的哈希值                            */
        SM3Digest sm3Digest = new SM3Digest();
        sm3Digest.update(z,0,z.length);
        sm3Digest.update(sourceData, 0, sourceData.length);
        byte[] md = new byte[32];
        sm3Digest.doFinal(md, 0);

        BigInteger rSq = null;
        BigInteger sSq = null;
        if(isDer) {
            /* !< 将SM2签名值进行Base64解码和DER解码,得到签名值R/S      */
            byte[] signDataAsn1 = Base64.getDecoder().decode(signData);
            BigInteger[] rs = decodeDer(signDataAsn1);
            rSq = rs[0];
            sSq = rs[1];
        } else {
            String signHex = new String(signData);
            rSq = new BigInteger(signHex.substring(0, signHex.length() / 2), 16);
            if (rSq.compareTo(BigInteger.ONE) < 0 || rSq.compareTo(n) > 0) {
                return false;
            }
            sSq = new BigInteger(signHex.substring(signHex.length() / 2), 16);
            if (sSq.compareTo(BigInteger.ONE) < 0 || sSq.compareTo(n) > 0) {
                return false;
            }
        }
        /* !< 进行SM2验签,验签通过返回true                          */
        return sm2Verify(md, userPubKey, rSq,  sSq);
    }

    /**
     * 返回SM2密钥
     *
     * @param sm2Pass
     * @param sm2Text
     * @return
     * @throws PKIException
     */
    public SM2PrivateKey getSM2PrivateKey(String sm2Pass, String sm2Text) throws PKIException {
        return KeyUtil.getPrivateKeyFromSM2(sm2Text.getBytes(), sm2Pass);
    }

    /**
     * 获取报文签名
     * @param srcString
     * @param pvkBytes
     * @param encoding
     * @return
     * @throws Exception
     */
    public static String signature(String srcString, byte[] pvkBytes, String encoding) throws Exception{
        if(pvkBytes.length>32) {
            pvkBytes=getPrivateKey(pvkBytes);
        }
        byte[] signedBytes=sign(pvkBytes, srcString.getBytes(encoding));
        return Base64.getEncoder().encodeToString(signedBytes);
    }

    public static String sm2EncryptString(String srcString, byte[] pukBytes, String encoding) throws Exception{
        if(pukBytes.length>64) {
            pukBytes=getPublicKey(pukBytes);
        }
        byte[] encryptBytes=encrypt(pukBytes, srcString.getBytes(encoding));
        return Base64.getEncoder().encodeToString(encryptBytes);
    }

    /**
     * 获取der编码下的私钥
     * @param derData der编码的私钥，二进制数据
     * @return 返回私钥值
     * @throws IOException
     */
    public static byte[] getPrivateKey(byte[] derData) throws IOException {

        PrivateKeyInfo pinfo = PrivateKeyInfo.getInstance(derData);
        ECPrivateKey cpk = ECPrivateKey.getInstance(pinfo.parsePrivateKey());

        int length = 32;
        byte[] bytes = cpk.getKey().toByteArray();
        if (bytes.length == length) {
            return bytes;
        }

        int start = bytes[0] == 0 ? 1 : 0;
        int count = bytes.length - start;

        if (count > length) {
            return null;
        }

        byte[] tmp = new byte[length];
        System.arraycopy(bytes, start, tmp, tmp.length - count, count);
        return tmp;
    }

    /**
     * sm2签名
     * <p>userId使用默认：1234567812345678
     * @param privateKey 私钥，二进制数据
     * @param sourceData 待签名数据
     * @return 返回der编码的签名值
     * @throws CryptoException
     */
    public static byte[] sign(byte[] privateKey, byte[] sourceData) throws CryptoException {
        return sign(defaultUserID, privateKey, sourceData);
    }

    /**
     * sm2签名
     * @param userId ID值，若无约定，使用默认：1234567812345678
     * @param privateKey 私钥，二进制数据
     * @param sourceData 待签名数据
     * @return 返回der编码的签名值
     * @throws CryptoException
     */
    public static byte[] sign(byte[] userId, byte[] privateKey, byte[] sourceData) throws CryptoException {

        ECDomainParameters parameters = new ECDomainParameters(sm2p256v1.getCurve(), sm2p256v1.getG(), sm2p256v1.getN());
        ECPrivateKeyParameters priKeyParameters = new ECPrivateKeyParameters(new BigInteger(1,privateKey),parameters);
        SM2Signer signer = new SM2Signer();
        CipherParameters param = null;
        ParametersWithRandom pwr = new ParametersWithRandom(priKeyParameters, new SecureRandom());
        if (userId != null) {
            param = new ParametersWithID(pwr, userId);
        } else {
            param = pwr;
        }
        signer.init(true, param);
        signer.update(sourceData, 0, sourceData.length);
        return signer.generateSignature();
    }

    /**
     * 银联sm2签名
     * @param userId ID值，若无约定，使用默认：1234567812345678
     * @param privateKey 私钥，二进制数据
     * @param sourceData 待签名数据
     * @return 返回der编码的签名值
     * @throws CryptoException
     * @throws InvalidKeySpecException 
     * @throws NoSuchProviderException 
     * @throws NoSuchAlgorithmException 
     */
    public static String unionpaySign(String userId, String privateKey, String sourceData) throws NoSuchAlgorithmException, NoSuchProviderException, InvalidKeySpecException, CryptoException {
        byte[] signBytes = sign(userId.getBytes(), getUnionpayPrivateHexKey(privateKey), sourceData.getBytes());
        BigInteger[] dd = decodeDer(signBytes);
        return Base64.getEncoder().encodeToString(Hex.decodeToBytes(Hex.encodeToString(bigIntegerToByteArray(dd[0], 32)) + Hex.encodeToString(bigIntegerToByteArray(dd[1], 32))));
    }

    /**
     * 将一个大整数转成一个指定长度的字节数组（前补零）
     * 
     * @param bi 待转换的大整数
     * @return 转换后得到的字节数组
     * 
     * 注意：算法来自银联
     */
    private static byte[] bigIntegerToByteArray(BigInteger bi, int byLength) {
        byte[] bibyte = bi.toByteArray();
        byte[] ubibyte;

        if (bibyte[0] == 0 && bibyte.length > byLength) {
            ubibyte = new byte[bibyte.length - 1];
            System.arraycopy(bibyte, 1, ubibyte, 0, ubibyte.length);
        } else
            ubibyte = bibyte;

        if (ubibyte.length >= byLength)
            return ubibyte;
        else {

            byte[] temp = new byte[byLength];
            System.arraycopy(bibyte, 0, temp, byLength - bibyte.length,
                    bibyte.length);
            return temp;
        }
    }

    /**
     * 获取der编码下的公钥
     * @param derData der编码的公钥，二进制数据
     * @return 返回公钥值
     */
    public static byte[] getPublicKey(byte[] derData) {
        SubjectPublicKeyInfo info = SubjectPublicKeyInfo.getInstance(derData);
        return info.getPublicKeyData().getBytes();
    }

    /**
     * sm2解密
     * @param publicKey 公钥，二进制数据，若被编码的，请解码再传入，如被hex编码，则hex解码后再传进来
     * @param data 待加密的数据
     * @return 返回der编码的密文数据
     * @throws InvalidCipherTextException
     * @throws IOException
     */
    public static byte[] encrypt(byte[] publicKey, byte[] data) throws InvalidCipherTextException, IOException {
        if(publicKey.length == 64) {//首位填充0x04，标识未被压缩
            byte tmp[] = new byte[65];
            System.arraycopy(publicKey, 0, tmp, 1, publicKey.length);
            tmp[0] = 0x04;
            publicKey = tmp;
        }
        ECDomainParameters parameters = new ECDomainParameters(sm2p256v1.getCurve(), sm2p256v1.getG(), sm2p256v1.getN());
        ECPublicKeyParameters pubKeyParameters = new ECPublicKeyParameters(sm2p256v1.getCurve().decodePoint(publicKey), parameters);
        SM2Engine engine = new SM2Engine();
        ParametersWithRandom pwr = new ParametersWithRandom(pubKeyParameters, new SecureRandom());
        engine.init(true, pwr);
        byte[] cipher = engine.processBlock(data, 0, data.length);
        return encodeSM2CipherToDER(cipher);
    }

    /**
     * 将c1c2c3密文转成der编码
     * @param cipher c1c2c3密文
     * @return der编码的sm2密文
     * @throws IOException
     */
    public static byte[] encodeSM2CipherToDER(byte[] cipher)
            throws IOException {
        int startPos = 1;
        int curveLength = (sm2p256v1.getCurve().getFieldSize() + 7) / 8;
        int digestLength = SM3_DIGEST_LENGTH_32;

        byte[] c1x = new byte[curveLength];
        System.arraycopy(cipher, startPos, c1x, 0, c1x.length);
        startPos += c1x.length;

        byte[] c1y = new byte[curveLength];
        System.arraycopy(cipher, startPos, c1y, 0, c1y.length);
        startPos += c1y.length;

        byte[] c2 = new byte[cipher.length - c1x.length - c1y.length - 1 - digestLength];
        System.arraycopy(cipher, startPos, c2, 0, c2.length);
        startPos += c2.length;

        byte[] c3 = new byte[digestLength];
        System.arraycopy(cipher, startPos, c3, 0, c3.length);

        ASN1Encodable[] arr = new ASN1Encodable[4];
        arr[0] = new ASN1Integer(new BigInteger(1, c1x));
        if(new BigInteger(1, c1x).toByteArray().length<32){
            System.out.println("");
        }
        arr[1] = new ASN1Integer(new BigInteger(1,c1y));
        arr[2] = new DEROctetString(c3);
        arr[3] = new DEROctetString(c2);
        DERSequence ds = new DERSequence(arr);
        return ds.getEncoded(ASN1Encoding.DER);
    }

    /**
     * sm2解密
     *
     * @param privateKey    私钥，二进制数据，若被编码的，请解码再传入，如被hex编码，则hex解码后再传进来
     * @param encryptedData 密文，二进制数据
     * @return 返回解密后的数据
     * @throws InvalidCipherTextException
     * @throws IOException
     */
    public static byte[] decrypt(byte[] privateKey, byte[] encryptedData) throws InvalidCipherTextException, IOException {
        if (privateKey.length > 32) {
            privateKey = SM2Util.getPrivateKey(privateKey);
        }

        ECDomainParameters parameters = new ECDomainParameters(sm2p256v1.getCurve(), sm2p256v1.getG(), sm2p256v1.getN());

        ECPrivateKeyParameters priKeyParameters = new ECPrivateKeyParameters(new BigInteger(1, privateKey), parameters);
        SM2Engine engine = new SM2Engine();
        engine.init(false, priKeyParameters);
        byte[] cipher = decodeDERSM2Cipher(encryptedData);
        return engine.processBlock(cipher, 0, cipher.length);
    }

    /**
     * 解DER编码密文（根据《SM2密码算法使用规范》 GM/T 0009-2012）
     *
     * @param derCipher 将der编码的sm2密文转成c1c2c3格式
     * @return 返回c1c2c3格式密文
     * @throws IOException
     */
    public static byte[] decodeDERSM2Cipher(byte[] derCipher) throws IOException {

        ByteArrayInputStream bis = new ByteArrayInputStream(derCipher);
        ASN1InputStream dis = new ASN1InputStream(bis);
//        ASN1Sequence as = DERSequence.getInstance(derCipher);
        ASN1Sequence as = (ASN1Sequence) dis.readObject();
        byte[] c1x = ((ASN1Integer) as.getObjectAt(0)).getValue().toByteArray();
        byte[] c1y = ((ASN1Integer) as.getObjectAt(1)).getValue().toByteArray();
        byte[] c3 = ((DEROctetString) as.getObjectAt(2)).getOctets();
        byte[] c2 = ((DEROctetString) as.getObjectAt(3)).getOctets();
        dis.close();

        int pos = 0;
        int curveLength = (sm2p256v1.getCurve().getFieldSize() + 7) / 8;
        byte[] cipherText = new byte[1 + curveLength * 2 + c2.length + c3.length];

        final byte uncompressedFlag = 0x04;
        cipherText[0] = uncompressedFlag;
        pos += 1;

        if (c1x.length >= curveLength) {
            System.arraycopy(c1x, c1x.length - curveLength, cipherText, pos, curveLength);
        } else {
            System.arraycopy(c1x, 0, cipherText, pos + curveLength - c1x.length, c1x.length);
        }
        pos += curveLength;

        if (c1y.length >= curveLength) {
            System.arraycopy(c1y, c1y.length - curveLength, cipherText, pos, curveLength);
        } else {
            System.arraycopy(c1y, 0, cipherText, pos + curveLength - c1y.length, c1y.length);
        }
        pos += curveLength;

        System.arraycopy(c2, 0, cipherText, pos, c2.length);
        pos += c2.length;

        System.arraycopy(c3, 0, cipherText, pos, c3.length);

        return cipherText;
    }


    /**
     * 报文验签
     * @param srcString
     * @param respSignature
     * @param pukBytes
     * @param encoding
     * @return
     * @throws Exception
     */
    public static boolean verifySign(String srcString,String respSignature, byte[] pukBytes, String encoding) throws Exception{
        if(pukBytes.length>64) {
            pukBytes=getPublicKey(pukBytes);
        }
        return verifySign(pukBytes, srcString.getBytes(encoding), Base64.getDecoder().decode(respSignature));
    }

    /**
     * sm2验签
     * <p>userId使用默认：1234567812345678
     * @param publicKey 公钥，二进制数据
     * @param sourceData 待验签数据
     * @param signData 签名值
     * @return 返回是否成功
     */
    public static boolean verifySign(byte[] publicKey, byte[] sourceData, byte[] signData){
        return verifySign(defaultUserID, publicKey, sourceData, signData);
    }

    /**
     * sm2验签
     * @param userId ID值，若无约定，使用默认：1234567812345678
     * @param publicKey 公钥，二进制数据
     * @param sourceData 待验签数据
     * @param signData 签名值
     * @return 返回是否成功
     */
    public static boolean verifySign(byte[] userId, byte[] publicKey, byte[] sourceData, byte[] signData) {

        if(publicKey.length == 64) {
            byte tmp[] = new byte[65];
            System.arraycopy(publicKey, 0, tmp, 1, publicKey.length);
            tmp[0] = 0x04;
            publicKey = tmp;
        }

        ECDomainParameters parameters = new ECDomainParameters(sm2p256v1.getCurve(), sm2p256v1.getG(), sm2p256v1.getN());
        ECPublicKeyParameters pubKeyParameters = new ECPublicKeyParameters(sm2p256v1.getCurve().decodePoint(publicKey), parameters);
        SM2Signer signer = new SM2Signer();
        CipherParameters param;
        if (userId != null) {
            param = new ParametersWithID(pubKeyParameters, userId);
        } else {
            param = pubKeyParameters;
        }
        signer.init(false, param);
        signer.update(sourceData, 0, sourceData.length);
        return signer.verifySignature(signData);
    }

    /**
     * 
     * 银联sm2验签
     * 
     * @param request   验签信息
     * @param certId    验签证书ID
     * @param publicKey 验签公钥
     * @param charset   编码格式
     * @return  boolean 是否验签通过
     * @throws PKIException
     * @throws UnsupportedEncodingException 
     */
    public boolean unionpayVerifySign(String content, String sign, String publicKey, String certId, String charset) throws PKIException, UnsupportedEncodingException {
        SM2PublicKey key = getUnionpayPublicKey(publicKey);
        return sm2Verify(key.getPubX(), key.getPubY(), content.getBytes(charset), new String(Hex.encode(com.wosai.mpay.util.Base64.decode(sign))).getBytes(charset), certId.getBytes(charset), false);
    }

    private static SM2PublicKey getUnionpayPublicKey(String publicKey) throws PKIException {
        SM2PublicKey sm2PublicKey = UNIONPAY_SM2_PUBLIC_KEY.get(publicKey);
        if(sm2PublicKey == null) {
            synchronized (publicKey.intern()) {
                if((sm2PublicKey = UNIONPAY_SM2_PUBLIC_KEY.get(publicKey)) == null) {
                    sm2PublicKey = KeyUtil.getSM2PublicKey(publicKey.getBytes());
                    UNIONPAY_SM2_PUBLIC_KEY.put(publicKey, sm2PublicKey);
                }
            }
        }
        return sm2PublicKey;
    }

    private static byte[] getUnionpayPrivateHexKey(String privateKey) throws NoSuchAlgorithmException, NoSuchProviderException, InvalidKeySpecException{
        byte[] hexPrivateKey = UNIONPAY_SM2_D_PRIVATE_KEY.get(privateKey);
        if(hexPrivateKey == null) {
            synchronized (privateKey.intern()) {
                if((hexPrivateKey = UNIONPAY_SM2_D_PRIVATE_KEY.get(privateKey)) == null) {
                    KeyFactory keyFact = KeyFactory.getInstance("EC", "BC");
                    PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey));
                    PrivateKey priv = keyFact.generatePrivate(pkcs8EncodedKeySpec);
                    org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPrivateKey ePrivateKey = (org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPrivateKey)priv;
                    hexPrivateKey = ePrivateKey.getD().toByteArray();
                    UNIONPAY_SM2_D_PRIVATE_KEY.put(privateKey, hexPrivateKey);
                }
            }
        }
        return hexPrivateKey;
    }

    public String encryptWithSM3Hash(String publicKey, String encryptedData) throws IOException {
        ECPoint publicKeyECPoint = ecc_curve.decodePoint(Hex.hexToByte(publicKey));
        return Hex.byteToHex(encrypt(encryptedData, publicKeyECPoint));
    }

    /**
     * 加密
     *
     * @param input     待加密消息M
     * @param publicKey 公钥
     * @return byte[] 加密后的字节数组
     */

    private byte[] encrypt(String input, ECPoint publicKey) {

        byte[] inputBuffer = input.getBytes();

        /* 1 产生随机数k，k属于[1, n-1] */
        BigInteger k = random(n);

        /* 2 计算椭圆曲线点C1 = [k]G = (x1, y1) */
        ECPoint C1 = G.multiply(k);
        byte[] C1Buffer = C1.getEncoded(false);

        // 3 计算椭圆曲线点 S = [h]Pb * curve没有指定余因子，h为空

        // BigInteger h = curve.getCofactor(); System.out.print("h: ");
        // printHexString(h.toByteArray()); if (publicKey != null) { ECPoint
        // result = publicKey.multiply(h); if (!result.isInfinity()) {
        // System.out.println("pass"); } else {
        // System.err.println("计算椭圆曲线点 S = [h]Pb失败"); return null; } }

        /* 4 计算 [k]PB = (x2, y2) */
        ECPoint kpb = publicKey.multiply(k).normalize();

        /* 5 计算 t = KDF(x2||y2, klen) */
        byte[] kpbBytes = kpb.getEncoded(false);
        byte[] t;
        t = KDF(kpbBytes, inputBuffer.length);


        if (allZero(t)) {
            throw new RuntimeException("all zero");
        }

        /* 6 计算C2=M^t */
        byte[] C2 = new byte[inputBuffer.length];
        for (int i = 0; i < inputBuffer.length; i++) {
            C2[i] = (byte) (inputBuffer[i] ^ t[i]);
        }

        /* 7 计算C3 = Hash(x2 || M || y2) */
        byte[] C3 = sm3hash(kpb.getXCoord().toBigInteger().toByteArray(), inputBuffer, kpb.getYCoord().toBigInteger().toByteArray());

        /* 8 输出密文 C=C1 || C2 || C3 */
        byte[] encryptResult = new byte[C1Buffer.length + C2.length + C3.length];
        System.arraycopy(C1Buffer, 0, encryptResult, 0, C1Buffer.length);
        System.arraycopy(C2, 0, encryptResult, C1Buffer.length, C2.length);
        System.arraycopy(C3, 0, encryptResult, C1Buffer.length + C2.length, C3.length);

        return encryptResult;
    }


    public String decryptWithSM3Hash(String privateKey, String encryptData) throws IOException {
        byte[] bb = Hex.hexToByte(privateKey);
        BigInteger userD = new BigInteger(1, bb);
        return decrypt(Hex.hexToByte(encryptData), userD);
    }
    private  String decrypt(byte[] encryptData, BigInteger privateKey) {

        byte[] C1Byte = new byte[65];
        System.arraycopy(encryptData, 0, C1Byte, 0, C1Byte.length);

        ECPoint C1 = ecc_curve.decodePoint(C1Byte).normalize();

        /* 计算[dB]C1 = (x2, y2) */
        ECPoint dBC1 = C1.multiply(privateKey).normalize();

        /* 计算t = KDF(x2 || y2, klen) */
        byte[] dBC1Bytes = dBC1.getEncoded(false);
//		DerivationFunction kdf = new KDF1BytesGenerator(new ShortenedDigest(new SHA256Digest(), 20));

        int klen = encryptData.length - 65 - 32;

        byte[] t = KDF(dBC1Bytes, klen);
        if (allZero(t)) {
            throw new RuntimeException("all zero");
        }

        /* 5 计算M'=C2^t */
        byte[] M = new byte[klen];
        for (int i = 0; i < M.length; i++) {
            M[i] = (byte) (encryptData[C1Byte.length + i] ^ t[i]);
        }

        /* 6 计算 u = Hash(x2 || M' || y2) 判断 u == C3是否成立 */
        byte[] C3 = new byte[32];
        System.arraycopy(encryptData, encryptData.length - 32, C3, 0, 32);
        byte[] u = sm3hash(dBC1.getXCoord().toBigInteger().toByteArray(), M, dBC1.getYCoord().toBigInteger().toByteArray());
        if (Arrays.equals(u, C3)) {
            return new String(M);
        } else {
            throw new RuntimeException("解密验证失败");
        }
    }


    public SignatureInfo sign(String publisherId, String privateKey, String sourceData, String publicKey) {
        return sign(sourceData.getBytes(), publisherId.getBytes(), Hex.hexToByte(privateKey.trim()), Hex.hexToByte(publicKey));
    }

    private SignatureInfo sign(byte[] M, byte[] IDA, byte[] priKey, byte[] pubKey) {
        ECPoint publicKey = ecc_curve.decodePoint(pubKey);
        BigInteger privateKey = new BigInteger(1, priKey);
        byte[] ZAdata = ZA(IDA, publicKey);
        byte[] M_ = join(ZAdata, M);
        BigInteger e = new BigInteger(1, sm3hash(M_));
        BigInteger k;
        BigInteger r;
        do {
            do {
                k = this.random(n);
                ECPoint p1 = G.multiply(k).normalize();
                BigInteger x1 = p1.getXCoord().toBigInteger();
                r = e.add(x1);
                r = r.mod(n);
            } while (r.equals(BigInteger.ZERO));
        } while (r.add(k).equals(n));

        BigInteger s = privateKey.add(BigInteger.ONE).modInverse(n).multiply(k.subtract(r.multiply(privateKey)).mod(n)).mod(n);
        return new SignatureInfo(r, s);
    }

    public boolean verifySign(String publisherId, String publicKey, String sourceData, SignatureInfo signature) {
        return verify(sourceData.getBytes(), signature, publisherId.getBytes(), Hex.hexToByte(publicKey));
    }
    public boolean verify(byte[] M, SignatureInfo signature, byte[] IDA, byte[] publicKey) {
        ECPoint aPublicKey = ecc_curve.decodePoint(publicKey);
        if (!this.between(signature.getR(), BigInteger.ONE, n)) {
            return false;
        } else if (!this.between(signature.getS(), BigInteger.ONE, n)) {
            return false;
        } else {
            byte[] M_ = join(ZA(IDA, aPublicKey), M);
            BigInteger e = new BigInteger(1, sm3hash(M_));
            BigInteger t = signature.getR().add(signature.getS()).mod(n);
            if (t.equals(BigInteger.ZERO)) {
                return false;
            } else {
                ECPoint p1 = G.multiply(signature.getS()).normalize();
                ECPoint p2 = aPublicKey.multiply(t).normalize();
                BigInteger x1 = p1.add(p2).normalize().getXCoord().toBigInteger();
                BigInteger R = e.add(x1).mod(n);
                return R.equals(signature.getR());
            }
        }
    }

    private static byte[] ZA(byte[] IDA, ECPoint aPublicKey) {
        int entlenA = IDA.length * 8;
        byte[] ENTLA = new byte[]{(byte)(entlenA & '\uff00'), (byte)(entlenA & 255)};
        byte[] ZA = sm3hash(ENTLA, IDA, a.toByteArray(), b.toByteArray(), gx.toByteArray(), gy.toByteArray(), aPublicKey.getXCoord().toBigInteger().toByteArray(), aPublicKey.getYCoord().toBigInteger().toByteArray());
        return ZA;
    }

    private static byte[] KDF(byte[] Z, int klen) {
        int ct = 1;
        int end = (int)Math.ceil((double)klen * 1.0 / 32.0);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        try {
            for(int i = 1; i < end; ++i) {
                baos.write(sm3hash(Z, SM3.toByteArray(ct)));
                ++ct;
            }

            byte[] last = sm3hash(Z, SM3.toByteArray(ct));
            if (klen % 32 == 0) {
                baos.write(last);
            } else {
                baos.write(last, 0, klen % 32);
            }

            return baos.toByteArray();
        } catch (Exception var6) {
            var6.printStackTrace();
            return null;
        }
    }

    public BigInteger random(BigInteger max) {
        BigInteger r = new BigInteger(256, random);
        while (r.compareTo(max) >= 0) {
            r = new BigInteger(128, random);
        }
        return r;
    }

    private boolean allZero(byte[] buffer) {
        for (int i = 0; i < buffer.length; i++) {
            if (buffer[i] != 0) {
                return false;
            }
        }
        return true;
    }


    private static byte[] sm3hash(byte[]... params) {
        byte[] res = null;

        try {
            res = SM3.hash(join(params));
        } catch (IOException var3) {
            var3.printStackTrace();
        }

        return res;
    }
    private static byte[] join(byte[]... params) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] res = null;

        try {
            for(int i = 0; i < params.length; ++i) {
                baos.write(params[i]);
            }

            res = baos.toByteArray();
        } catch (IOException var4) {
            var4.printStackTrace();
        }

        return res;
    }

    private boolean between(BigInteger param, BigInteger min, BigInteger max) {
        return param.compareTo(min) >= 0 && param.compareTo(max) < 0;
    }
}