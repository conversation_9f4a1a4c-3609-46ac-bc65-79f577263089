package com.wosai.mpay.util;


import javax.net.ssl.*;
import java.security.*;
import java.security.cert.X509Certificate;
import java.util.Set;

public final class SSLUtil{

    private static final TrustManager[] UNQUESTIONING_TRUST_MANAGER = new TrustManager[]{
            new X509TrustManager() {
                public java.security.cert.X509Certificate[] getAcceptedIssuers(){
                    return new java.security.cert.X509Certificate[]{};
                }
                public void checkClientTrusted( X509Certificate[] certs, String authType ){}
                public void checkServerTrusted( X509Certificate[] certs, String authType ){}
            }
    };

    public  static void turnOffSslChecking() throws NoSuchAlgorithmException, KeyManagementException {
        // Install the all-trusting trust manager
        final SSLContext sc = SSLContext.getInstance("SSL");
        sc.init( null, UNQUESTIONING_TRUST_MANAGER, null );
        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
        // Create all-trusting host name verifier
        HostnameVerifier allHostsValid = new HostnameVerifier() {
            public boolean verify(String hostname, SSLSession session) {
                return true;
            }
        };
        // Install the all-trusting host verifier
        HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
    }

    public static void turnOnSslChecking() throws KeyManagementException, NoSuchAlgorithmException {
        SSLContext.getInstance("SSL").init( null, null, null );
    }

    public static SSLContext getUnsafeSSLContext(){
        try{
            final SSLContext sc = SSLContext.getInstance("SSL");
            sc.init( null, UNQUESTIONING_TRUST_MANAGER, null );
            return sc;
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    public static HostnameVerifier getUnsafeHostnameVerifier(final Set<String> whiteHostNames){
        HostnameVerifier verifier = new HostnameVerifier() {
            public boolean verify(String hostname, SSLSession session) {
                if(whiteHostNames != null && whiteHostNames.contains(hostname)){
                    return true;
                }else{
                    HttpsURLConnection.getDefaultHostnameVerifier().verify(hostname, session);
                }
                return true;
            }
        };
        return verifier;
    }

    private SSLUtil(){
        throw new UnsupportedOperationException( "Do not instantiate libraries.");
    }
}