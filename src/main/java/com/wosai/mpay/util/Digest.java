package com.wosai.mpay.util;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class Digest {
    public static final String MD5 = "MD5";
    public static final String SHA1 = "SHA-1";
    public static final String SHA256 = "SHA-256";
    
    public static String md5(byte[] input) throws NoSuchAlgorithmException {
        MessageDigest digest = SecurityUtil.getMessageDigest(MD5);
        return new String(Hex.encode(digest.digest(input)));
    }

    public static String sha256(byte[] input) throws NoSuchAlgorithmException {
        MessageDigest digest = SecurityUtil.getMessageDigest(SHA256);
        return new String(Hex.encode(digest.digest(input)));
    }

    public static byte [] sha256Bytes(byte[] input) throws NoSuchAlgorithmException {
        MessageDigest digest = SecurityUtil.getMessageDigest(SHA256);
        return digest.digest(input);
    }

    public static String sha1(byte[] input) throws NoSuchAlgorithmException {
        MessageDigest digest = SecurityUtil.getMessageDigest(SHA1);
        return new String(Hex.encode(digest.digest(input)));
    }
}
