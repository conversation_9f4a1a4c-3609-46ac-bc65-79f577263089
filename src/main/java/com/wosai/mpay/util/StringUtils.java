package com.wosai.mpay.util;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public abstract class StringUtils {

	public static final String EMPTY = "";
	
    private StringUtils() {}

    /**
     * 检查指定的字符串是否为空。
     * <ul>
     * <li>SysUtils.isEmpty(null) = true</li>
     * <li>SysUtils.isEmpty("") = true</li>
     * <li>SysUtils.isEmpty("   ") = true</li>
     * <li>SysUtils.isEmpty("abc") = false</li>
     * </ul>
     * 
     * @param value 待检查的字符串
     * @return true/false
     */
    public static boolean isEmpty(String value) {
        int strLen;
        if (value == null || (strLen = value.length()) == 0) {
            return true;
        }
        for (int i = 0; i < strLen; i++) {
            if ((Character.isWhitespace(value.charAt(i)) == false)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查对象是否为数字型字符串,包含负数开头的。
     */
    public static boolean isNumeric(Object obj) {
        if (obj == null) {
            return false;
        }
        char[] chars = obj.toString().toCharArray();
        int length = chars.length;
        if(length < 1)
            return false;
        
        int i = 0;
        if(length > 1 && chars[0] == '-')
            i = 1;
        
        for (; i < length; i++) {
            if (!Character.isDigit(chars[i])) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查指定的字符串列表是否不为空。
     */
    public static boolean areNotEmpty(String... values) {
        boolean result = true;
        if (values == null || values.length == 0) {
            result = false;
        } else {
            for (String value : values) {
                result &= !isEmpty(value);
            }
        }
        return result;
    }

    /**
     * 过滤不可见字符
     */
    public static String stripNonValidXMLCharacters(String input) {
        if (input == null || ("".equals(input)))
            return "";
        StringBuilder out = new StringBuilder();
        char current;
        for (int i = 0; i < input.length(); i++) {
            current = input.charAt(i);
            if ((current == 0x9) || (current == 0xA) || (current == 0xD)
                    || ((current >= 0x20) && (current <= 0xD7FF))
                    || ((current >= 0xE000) && (current <= 0xFFFD))
                    || ((current >= 0x10000) && (current <= 0x10FFFF)))
                out.append(current);
        }
        return out.toString();
    }

    public static String cents2yuan(long cents) {
        String sign = "";
        if (cents < 0) {
            cents = -cents;
            sign = "-";
        }
        return String.format("%s%d.%02d", sign, cents/100, cents%100);
    }
    
    private static Pattern leadingNegativeSign = Pattern.compile("^\\s*\\-");
    private static Pattern leadingPositiveSign = Pattern.compile("^\\s*\\+");
    public static long yuan2cents(String yuan) {
        long sign = 1;
        Matcher posMatcher = leadingPositiveSign.matcher(yuan);
        if (posMatcher.find()) {
            yuan = yuan.substring(posMatcher.end());
        }
        Matcher negMatcher = leadingNegativeSign.matcher(yuan);
        if (negMatcher.find()) {
            yuan = yuan.substring(negMatcher.end());
            sign = -1;
        }
        int indexOfDot = yuan.indexOf(".");
        String integral = "0";
        String fraction = "00";
        if (indexOfDot >= 0){
            integral += yuan.substring(0, indexOfDot);
            fraction = (yuan.substring(indexOfDot+1) + fraction).substring(0, 2);
        }else{
            integral += yuan;
        }
        return (Long.parseLong(integral,10) * 100 + Long.parseLong(fraction,10))*sign;
    }
    
    public static boolean empty(String str) {
        return str==null || str.isEmpty();
    }
    
    public static String join(Object... args){
    	if(null != args && args.length > 0){
    		StringBuilder builer = new StringBuilder();
    		for (Object object : args) {
				builer.append(object);
			}
    		return builer.toString();
    	}
    	return EMPTY;
    }

    /**
     * 字符串追加
     * 
     * @param str 原始字符串
     * @param regex 分隔符
     * @param appendValue 添加值
     * @return
     */
    public static String stringAppend(String str, String regex, String appendValue) {
        return stringAppend(str, regex, appendValue, true);
    }

    /**
     * 字符串追加
     * 
     * @param str 原始字符串
     * @param regex 分隔符
     * @param appendValue 添加值
     * @param allowDuplication 是否允许重复
     * @return
     */
    public static String stringAppend(String str, String regex, String appendValue, boolean allowDuplication) {
        String [] strs = str.split(regex);
        List<String> newStrs = new ArrayList<>();
        boolean append = true;
        for (String string : strs) {
            if(!isEmpty(string)) {
                newStrs.add(string);
                if(Objects.equals(string, appendValue)) {
                    if(!allowDuplication) {
                        append = false;
                    }
                }
            }
        }
        if(append) {
            newStrs.add(appendValue);
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (String string : newStrs) {
            stringBuilder.append(string).append(regex);
        }
        if(stringBuilder.length() > 0) {
            stringBuilder.deleteCharAt(stringBuilder.length() -1 );
        }
        return stringBuilder.toString();
    }

    /**
     * 字符串移除
     * 
     * @param str 原始字符串
     * @param regex 分隔符
     * @param removeVal 移除值
     * @return
     */
    public static String stringRemove(String str, String regex, String removeVal) {
        String [] strs = str.split(regex);
        List<String> newStrs = new ArrayList<>();
        for (String string : strs) {
            if(!isEmpty(string)) {
                if(!Objects.equals(string, removeVal)) {
                    newStrs.add(string);
                }
            }
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (String string : newStrs) {
            stringBuilder.append(string).append(regex);
        }
        if(stringBuilder.length() > 0) {
            stringBuilder.deleteCharAt(stringBuilder.length() -1 );
        }
        return stringBuilder.toString();
    }

    /**
     * 解析form表单字符串
     * name=John&age=25&city=NewYork
     * @param formString
     * @return
     * @throws UnsupportedEncodingException
     */
    public static Map<String, String> parseEncodedFormString(String formString) {
        Map<String, String> map = new HashMap<>();

        if (formString == null || formString.trim().isEmpty()) {
            return map;
        }

        String[] pairs = formString.split("&");
        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            if (idx > 0) {
                String key = URLDecoder.decode(pair.substring(0, idx));
                String value = idx < pair.length() - 1
                        ? URLDecoder.decode(pair.substring(idx + 1))
                        : "";
                map.put(key, value);
            }
        }

        return map;
    }

    /**
     * 截断字符串至不超过指定字节长度（确保无乱码且不超限）
     * @param text 原始文本
     * @param maxBytes 最大字节数（必须 > 0）
     * @return 截断后的合法UTF-8字符串（保证 bytes.length ≤ maxBytes）
     */
    public static String byteTruncate(String text, int maxBytes) {
        // 参数检查
        if (text == null || text.isEmpty() || maxBytes <= 0) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        int byteCount = 0;
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            int charByteSize = calculateCharByteSize(c);
            if (byteCount + charByteSize > maxBytes) {
                break;
            }
            result.append(c);
            byteCount += charByteSize;
        }
        return result.toString();
    }

    /**
     * 计算单个字符的UTF-8字节数
     */
    private static int calculateCharByteSize(char c) {
        if (c <= 0x7F) {  // ASCII字符
            return 1;
        } else if (c <= 0x7FF) {  // 2字节字符
            return 2;
        } else if (c <= 0xFFFF) {  // 3字节字符
            return 3;
        }
        return 4;  // 4字节字符（补充平面字符）
    }

}
