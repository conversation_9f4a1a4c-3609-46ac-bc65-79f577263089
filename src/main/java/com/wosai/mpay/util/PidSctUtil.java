package com.wosai.mpay.util;

import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * 收款通道云闪付支付方式生成服务商户秘文工具类
 */
public class PidSctUtil {

    public static final Logger logger = LoggerFactory.getLogger(PidSctUtil.class);

    public static String generatePidSct(String pid, String orderId, long amount, String key) {
        try {
            // ===== 步骤a: 构建MAB =====
            String mab = String.format("%-11s%-40s%012d", pid, orderId, amount);
            byte[] mabBytes = mab.getBytes(StandardCharsets.UTF_8);
            // ===== 步骤b: 分组异或 =====
            byte[] xorResult = calculateXor(mabBytes);

            // ===== 步骤c: 转换为32个HEXDECIMAL =====
            String hexResult = Hex.toHexString(xorResult).toUpperCase();


            // ===== 步骤d: 前16字节转ASCII加密 =====
            String group1Hex = hexResult.substring(0, 16);
            byte[] group1Ascii = hexToAsciiBytes(group1Hex);

            byte[] encryptedGroup1 = SM4Util.encryptECB(group1Ascii, Hex.decode(key));

            // ===== 步骤e: 后16字节转ASCII异或 =====
            String group2Hex = hexResult.substring(16, 32);
            byte[] group2Ascii = hexToAsciiBytes(group2Hex);

            byte[] tempBlock = xorBytes(encryptedGroup1, group2Ascii);

            // ===== 步骤f: 最终加密 =====
            byte[] finalResult = SM4Util.encryptECB(tempBlock, Hex.decode(key));

            // ===== 步骤g&h: 取结果前8字符 =====
            String tusnMac = Hex.toHexString(finalResult).toUpperCase().substring(0, 8);
            return tusnMac;
        } catch (Exception e) {
            logger.error("加密失败", e);
            throw new RuntimeException("加密失败", e);
        }

    }

    // 将Hex字符串转为ASCII字节数组（核心转换）
    private static byte[] hexToAsciiBytes(String hexStr) {
        byte[] bytes = new byte[hexStr.length()];
        for (int i = 0; i < hexStr.length(); i++) {
            bytes[i] = (byte) hexStr.charAt(i); // 关键点：直接取字符的ASCII值
        }
        return bytes;
    }

    // 分组异或计算（补零到64字节）
    private static byte[] calculateXor(byte[] data) {
        byte[] padded = Arrays.copyOf(data, 64); // 63+1补零
        byte[] result = new byte[16];

        // 4组16字节异或
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 16; j++) {
                result[j] ^= padded[i * 16 + j];
            }
        }
        return result;
    }

    // 字节异或操作
    private static byte[] xorBytes(byte[] a, byte[] b) {
        byte[] result = new byte[Math.min(a.length, b.length)];
        for (int i = 0; i < result.length; i++) {
            result[i] = (byte) (a[i] ^ b[i]);
        }
        return result;
    }

}
