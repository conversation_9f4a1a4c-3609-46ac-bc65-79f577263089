package com.wosai.mpay.util;

import cfca.ch.qos.logback.core.encoder.ByteArrayUtil;
import com.wosai.pantheon.util.StringUtil;

import java.nio.charset.StandardCharsets;

/***
 * @ClassName: GrabPaySignature
 * @Description:
 * @Auther: dabuff
 * @Date: 2022/2/10 4:36 PM
 */
public class GrabPaySignature {

    /**
     * 获取GrabPay HMAC签名结果
     *
     * @param httpMethod
     * @param contentType
     * @param GMTDateTime
     * @param requestURL
     * @param requestBody
     * @param partnerSecret
     * @return
     */
    public static String sign(String httpMethod, String contentType,
                              String GMTDateTime, String requestURL,
                              String requestBody, String partnerSecret) throws Exception{

        String body;
        if (!StringUtil.isBlank(requestBody)) {
            byte[] bytes = ByteArrayUtil.hexStringToByteArray(Digest.sha256(requestBody.getBytes(StandardCharsets.UTF_8)));
            body = Base64.encode(bytes);
        } else {
            body = requestBody;
        }
        //格式化处理加签内容
        String content = String.format("%s\n%s\n%s\n%s\n%s\n", httpMethod, contentType, GMTDateTime, requestURL, body);

        //签名
        byte[] byteMacResult = HmacSignature.signBytes(content, HmacSignature.SIG_ALG_NAME_HMAC_SHA256, partnerSecret);
        return Base64.encode(byteMacResult);
    }
}
