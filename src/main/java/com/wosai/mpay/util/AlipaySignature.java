package com.wosai.mpay.util;

import java.io.ByteArrayOutputStream;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import javax.crypto.Cipher;

import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.exception.AlipayV2Exception;
import com.wosai.mpay.exception.MpayException;


/**
 * 
 * <AUTHOR>
 */
public class AlipaySignature {

    /** RSA最大加密明文大小  */
    private static final int MAX_ENCRYPT_BLOCK = 117;

    /** RSA最大解密密文大小   */
    private static final int MAX_DECRYPT_BLOCK = 128;

    /**
     * 
     * @param sortedParams
     * @return
     */
    public static String getSignContent(Map<String, String> sortedParams) {
        StringBuffer content = new StringBuffer();
        List<String> keys = new ArrayList<String>(sortedParams.keySet());
        Collections.sort(keys);
        int index = 0;
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = sortedParams.get(key);
            if (StringUtils.areNotEmpty(key, value)) {
                content.append((index == 0 ? "" : "&") + key + "=" + value);
                index++;
            }
        }
        return content.toString();
    }

    public static String rsaSign(String content, String privateKey, String charset)
                                                                                   throws AlipayV2Exception {
        try {
            return RsaSignature.sign(content, RsaSignature.SIG_ALG_NAME_SHA1_With_RSA, privateKey, charset);
        } catch (Exception e) {
            throw new AlipayV2Exception("99999", "signature error", null, null, e);
        }
    }


    public static String rsaSign(Map<String, String> params, String privateKey, String charset)
                                                                                               throws AlipayV2Exception {
        String signContent = getSignContent(params);
        return rsaSign(signContent, privateKey, charset);

    }


    public static String rsa256Sign(String content, String privateKey, String charset)
            throws AlipayV2Exception {
        try {
            return RsaSignature.sign(content, RsaSignature.SIG_ALG_NAME_SHA256_With_RSA, privateKey, charset);
        } catch (Exception e) {
            throw new AlipayV2Exception("99999", "signature error", null, null, e);
        }
    }

    public static String rsa256Sign(Map<String, String> params, String privateKey, String charset)
            throws AlipayV2Exception {
        String signContent = getSignContent(params);

        return rsa256Sign(signContent, privateKey, charset);

    }

    public static String getSignCheckContentV1(Map<String, String> params) {
        if (params == null) {
            return null;
        }

        params.remove("sign");
        params.remove("sign_type");

        StringBuffer content = new StringBuffer();
        List<String> keys = new ArrayList<String>(params.keySet());
        Collections.sort(keys);

        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = params.get(key);
            content.append((i == 0 ? "" : "&") + key + "=" + value);
        }

        return content.toString();
    }

    public static String getSignCheckContentV2(Map<String, String> params) {
        if (params == null) {
            return null;
        }

        params.remove("sign");

        StringBuffer content = new StringBuffer();
        List<String> keys = new ArrayList<String>(params.keySet());
        Collections.sort(keys);

        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = params.get(key);
            content.append((i == 0 ? "" : "&") + key + "=" + value);
        }

        return content.toString();
    }

    public static boolean rsaCheckV1(Map<String, String> params, String publicKey, String charset)
            throws AlipayV2Exception {
        String sign = params.get("sign");
        String signType = params.get("sign_type");
        String content = getSignCheckContentV1(params);

        return rsaCheckContent(content, sign, publicKey, charset, signType);
    }

    public static boolean rsaCheckV2(Map<String, String> params, String publicKey, String charset)
            throws AlipayV2Exception {
        String sign = params.get("sign");
        String content = getSignCheckContentV2(params);
        
        String signType = params.get("sign_type");
        if(StringUtils.isEmpty(signType)) {
            signType = AlipayConstants.SIGN_TYPE_RSA;
        }

        return rsaCheckContent(content, sign, publicKey, charset, signType);
    }

    public static boolean rsaCheckContent(String content, String sign, String publicKey,
            String charset, String signType) throws AlipayV2Exception {
        try {
            return RsaSignature.validateSign(content, sign, AlipayConstants.SIGN_TYPE_RSA2.equals(signType) ? RsaSignature.SIG_ALG_NAME_SHA256_With_RSA : RsaSignature.SIG_ALG_NAME_SHA1_With_RSA, publicKey, charset);
        } catch (Exception e) {
            throw new AlipayV2Exception("99999", "signature verification internal error", null, null, e);
        }
    }

    public static boolean rsaCheckContent(String content, String sign, String publicKey,
                                          String charset) throws AlipayV2Exception {
        return rsaCheckContent(content, sign, publicKey, charset, AlipayConstants.SIGN_TYPE_RSA);
    }

    /**
     * 验签并解密
     * <p>
     * <b>目前适用于公众号</b><br>
     * params参数示例：
     * <br>{
     *    <br>biz_content=M0qGiGz+8kIpxe8aF4geWJdBn0aBTuJRQItLHo9R7o5JGhpic/MIUjvXo2BLB++BbkSq2OsJCEQFDZ0zK5AJYwvBgeRX30gvEj6eXqXRt16/IkB9HzAccEqKmRHrZJ7PjQWE0KfvDAHsJqFIeMvEYk1Zei2QkwSQPlso7K0oheo/iT+HYE8aTATnkqD/ByD9iNDtGg38pCa2xnnns63abKsKoV8h0DfHWgPH62urGY7Pye3r9FCOXA2Ykm8X4/Bl1bWFN/PFCEJHWe/HXj8KJKjWMO6ttsoV0xRGfeyUO8agu6t587Dl5ux5zD/s8Lbg5QXygaOwo3Fz1G8EqmGhi4+soEIQb8DBYanQOS3X+m46tVqBGMw8Oe+hsyIMpsjwF4HaPKMr37zpW3fe7xOMuimbZ0wq53YP/jhQv6XWodjT3mL0H5ACqcsSn727B5ztquzCPiwrqyjUHjJQQefFTzOse8snaWNQTUsQS7aLsHq0FveGpSBYORyA90qPdiTjXIkVP7mAiYiAIWW9pCEC7F3XtViKTZ8FRMM9ySicfuAlf3jtap6v2KPMtQv70X+hlmzO/IXB6W0Ep8DovkF5rB4r/BJYJLw/6AS0LZM9w5JfnAZhfGM2rKzpfNsgpOgEZS1WleG4I2hoQC0nxg9IcP0Hs+nWIPkEUcYNaiXqeBc=,
     *    <br>sign=rlqgA8O+RzHBVYLyHmrbODVSANWPXf3pSrr82OCO/bm3upZiXSYrX5fZr6UBmG6BZRAydEyTIguEW6VRuAKjnaO/sOiR9BsSrOdXbD5Rhos/Xt7/mGUWbTOt/F+3W0/XLuDNmuYg1yIC/6hzkg44kgtdSTsQbOC9gWM7ayB4J4c=,
     *    sign_type=RSA,
     *    <br>charset=UTF-8
     * <br>}
     * </p>
     * @param params
     * @param alipayPublicKey 支付宝公钥
     * @param cusPrivateKey   商户私钥
     * @param isCheckSign     是否验签
     * @param isDecrypt       是否解密
     * @return 解密后明文，验签失败则异常抛出
     * @throws AlipayV2Exception
     */
    public static String checkSignAndDecrypt(Map<String, String> params, String alipayPublicKey,
                                             String cusPrivateKey, boolean isCheckSign,
                                             boolean isDecrypt) throws AlipayV2Exception {
        String charset = params.get("charset");
        String bizContent = params.get("biz_content");
        if (isCheckSign) {
            if (!rsaCheckV2(params, alipayPublicKey, charset)) {
                throw new AlipayV2Exception("99999", "signature verification failed.", null, null);
            }
        }

        if (isDecrypt) {
            return rsaDecrypt(bizContent, cusPrivateKey, charset);
        }

        return bizContent;
    }

    /**
     * 加密并签名<br>
     * <b>目前适用于公众号</b>
     * @param bizContent      待加密、签名内容
     * @param alipayPublicKey 支付宝公钥
     * @param cusPrivateKey   商户私钥
     * @param charset         字符集，如UTF-8, GBK, GB2312
     * @param isEncrypt       是否加密，true-加密  false-不加密
     * @param isSign          是否签名，true-签名  false-不签名
     * @return 加密、签名后xml内容字符串
     * <p>
     * 返回示例：
     * <alipay>
     *  <response>密文</response>
     *  <encryption_type>RSA</encryption_type>
     *  <sign>sign</sign>
     *  <sign_type>RSA</sign_type>
     * </alipay>
     * </p>
     * @throws AlipayV2Exception
     */
    public static String encryptAndSign(String bizContent,
                                        String alipayPublicKey,
                                        String cusPrivateKey,
                                        String charset,
                                        boolean isEncrypt,
                                        boolean isSign) throws AlipayV2Exception {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isEmpty(charset)) {
            charset = AlipayConstants.CHARSET_GBK;
        }
        sb.append("<?xml version=\"1.0\" encoding=\"" + charset + "\"?>");
        if (isEncrypt) {// 加密
            sb.append("<alipay>");
            String encrypted = rsaEncrypt(bizContent, alipayPublicKey, charset);
            sb.append("<response>" + encrypted + "</response>");
            sb.append("<encryption_type>RSA</encryption_type>");
            if (isSign) {
                String sign = rsaSign(encrypted, cusPrivateKey, charset);
                sb.append("<sign>" + sign + "</sign>");
                sb.append("<sign_type>RSA</sign_type>");
            }
            sb.append("</alipay>");
        } else if (isSign) {// 不加密，但需要签名
            sb.append("<alipay>");
            sb.append("<response>" + bizContent + "</response>");
            String sign = rsaSign(bizContent, cusPrivateKey, charset);
            sb.append("<sign>" + sign + "</sign>");
            sb.append("<sign_type>RSA</sign_type>");
            sb.append("</alipay>");
        } else {// 不加密，不加签
            sb.append(bizContent);
        }
        return sb.toString();
    }

    /**
     * 公钥加密
     * 
     * @param content   待加密内容
     * @param publicKey 公钥
     * @param charset   字符集，如UTF-8, GBK, GB2312
     * @return 密文内容
     * @throws AlipayV2Exception
     */
    public static String rsaEncrypt(String content, String publicKey, String charset)
            throws AlipayV2Exception {

        try {
            PublicKey pubKey = RsaSignature.getPublicKeyFromX509(AlipayConstants.SIGN_TYPE_RSA, publicKey);
            Cipher cipher = Cipher.getInstance(RsaSignature.CRYPT_RSA_TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, pubKey);
            byte[] data = StringUtils.isEmpty(charset) ? content.getBytes() : content
                .getBytes(charset);
            int inputLen = data.length;
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int offSet = 0;
            byte[] cache;
            int i = 0;
            // 对数据分段加密  
            while (inputLen - offSet > 0) {
                if (inputLen - offSet > MAX_ENCRYPT_BLOCK) {
                    cache = cipher.doFinal(data, offSet, MAX_ENCRYPT_BLOCK);
                } else {
                    cache = cipher.doFinal(data, offSet, inputLen - offSet);
                }
                out.write(cache, 0, cache.length);
                i++;
                offSet = i * MAX_ENCRYPT_BLOCK;
            }
            String encryptedData = Base64.encode(out.toByteArray());
            out.close();

            return encryptedData;
        } catch (Exception e) {
            throw new AlipayV2Exception("99999", "encryption internal error", null, null, e);
        }
    }

    /**
     * 私钥解密
     * 
     * @param content    待解密内容
     * @param privateKey 私钥
     * @param charset    字符集，如UTF-8, GBK, GB2312
     * @return 明文内容
     * @throws AlipayV2Exception
     */
    public static String rsaDecrypt(String content, String privateKey, String charset)
                                                                                      throws AlipayV2Exception {
        try {
            PrivateKey priKey = RsaSignature.getPrivateKeyFromPKCS8(AlipayConstants.SIGN_TYPE_RSA, privateKey);
            Cipher cipher = Cipher.getInstance(RsaSignature.CRYPT_RSA_TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, priKey);
            byte[] encryptedData = Base64.decode(content);
            int inputLen = encryptedData.length;
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int offSet = 0;
            byte[] cache;
            int i = 0;
            // 对数据分段解密  
            while (inputLen - offSet > 0) {
                if (inputLen - offSet > MAX_DECRYPT_BLOCK) {
                    cache = cipher.doFinal(encryptedData, offSet, MAX_DECRYPT_BLOCK);
                } else {
                    cache = cipher.doFinal(encryptedData, offSet, inputLen - offSet);
                }
                out.write(cache, 0, cache.length);
                i++;
                offSet = i * MAX_DECRYPT_BLOCK;
            }
            byte[] decryptedData = out.toByteArray();
            out.close();

            return StringUtils.isEmpty(charset) ? new String(decryptedData) : new String(
                decryptedData, charset);
        } catch (Exception e) {
            throw new AlipayV2Exception("99999", "decryption internal error.", null, null, e);
        }
    }

    public static String md5Sign(String content, String md5key, String charset) throws MpayException {
        try {
            byte[] input = (content+md5key).getBytes(charset);
            return Digest.md5(input);
        }catch(Exception e){
            throw new MpayException("failed to generate md5 signature.", e);
        }
    }

    public static String md5Sign(Map<String, String> params, String md5key, String charset) throws MpayException {
        String content = getSignContent(params);
        return md5Sign(content, md5key, charset);
    }
    
}
