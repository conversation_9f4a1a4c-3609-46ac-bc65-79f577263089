package com.wosai.mpay.util;

import com.wosai.mpay.exception.MpayException;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * Created by maoyu
 */
public class LakalaWanmaSignature {
    /**
     * 获取md5签名, 返回大写字符串
     *
     * @param content
     * @param key
     * @param charset
     * @return
     * @throws MpayException
     */
    public static String getSign(Map<String, Object> content, String key, String charset) throws MpayException {
    	StringBuffer sb = new StringBuffer();
        List<String> keys = new ArrayList<String>(content.keySet());
        Collections.sort(keys);
        int index = 0;
        String tmpKey = null;
        Object value = null;
        for (int i = 0; i < keys.size(); i++) {
        	tmpKey = keys.get(i);
        	value = content.get(tmpKey);
            if (null != tmpKey && null != value && !"".equals(value)) {
            	sb.append((index == 0 ? "" : "&") + tmpKey + "=" + value);
                index++;
            }
        }
        sb.append("&key=").append(key);
        
        String md5Str = "";
        try {
        	md5Str = Digest.md5(new String(sb.toString().getBytes(charset),charset).getBytes()).toUpperCase();
        } catch (Exception e) {
            throw new MpayException("failed to generate sha1 signature.", e);
        }
        return md5Str;
    }

    /**
     * 从API返回的XML数据里面重新计算一次签名
     *
     * @param responseString API返回的XML数据
     * @return 新鲜出炉的签名
     * @throws MpayException
     */
    public static String getSignFromResponseString(String responseString, String key, String charset) throws MpayException {
        Map<String, Object> map = XmlUtils.parse(responseString);
        //清掉返回数据对象里面的Sign数据（不能把这个数据也加进去进行签名），然后用签名算法进行签名
        map.put("sign", "");
        //将API返回的数据根据用签名算法进行计算新的签名，用来跟API返回的签名进行比较
        return WeixinSignature.getSign(map, key, charset);
    }

    /**
     * 检验API返回的数据里面的签名是否合法，避免数据在传输的过程中被第三方篡改
     *
     * @param responseString API返回的XML数据字符串
     * @return API签名是否合法
     * @throws MpayException
     */
    public static boolean checkIsSignValidFromResponseString(String responseString, String key, String charset) throws MpayException {

        Map<String, Object> map = XmlUtils.parse(responseString);

        String signFromAPIResponse = map.get("sign").toString();
        if (StringUtils.isEmpty(signFromAPIResponse)) {
            return false;
        }
        //清掉返回数据对象里面的Sign数据（不能把这个数据也加进去进行签名），然后用签名算法进行签名
        map.put("sign", "");
        //将API返回的数据根据用签名算法进行计算新的签名，用来跟API返回的签名进行比较
        String signForAPIResponse = WeixinSignature.getSign(map, key, charset);

        if (!signForAPIResponse.equals(signFromAPIResponse)) {
            //签名验不过，表示这个API返回的数据有可能已经被篡改了
            return false;
        }
        return true;
	}
}
