package com.wosai.mpay.util;

import org.jpos.iso.ISOUtil;
import org.jpos.tlv.TLVList;
import org.jpos.tlv.TLVMsg;

import java.util.HashMap;
import java.util.Map;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className EMVTagsUtil
 * @description:
 * @create: 2025-07-02 16:49
 **/
public class EMVTagsUtil {

    /**
     * 交易货币代码
     */
    public static final String TAG_5F2A_TRANSACTION_CURRENCY_CODE = "5F2A";
    /**
     * 应用交互特征
     */
    public static final String TAG_82_APPLICATION_INTERCHANGE_PROFILE = "82";
    /**
     * 专用文件名称
     */
    public static final String TAG_84_DEDICATED_FILE_NAME = "84";
    /**
     * 终端验证结果
     */
    public static final String TAG_95_TERMINAL_VERIFICATION_RESULTS = "95";
    /**
     * 交易日期
     */
    public static final String TAG_9A_TRANSACTION_DATE = "9A";
    /**
     * 交易类型
     */
    public static final String TAG_9C_TRANSACTION_TYPE = "9C";
    /**
     * 授权金额
     */
    public static final String TAG_9F02_AUTHORIZED_AMOUNT = "9F02";
    /**
     * 其它金额
     */
    public static final String TAG_9F03_OTHER_AMOUNT = "9F03";
    /**
     * 应用版本
     */
    public static final String TAG_9F08_APPLICATION_VERSION = "9F08";
    /**
     * 应用版本号
     */
    public static final String TAG_9F09_APPLICATION_VERSION_NUMBER = "9F09";
    /**
     * 发卡行应用数据
     */
    public static final String TAG_9F10_ISSUER_APPLICATION_DATA = "9F10";
    /**
     * 终端国家代码
     */
    public static final String TAG_9F1A_TERMINAL_COUNTRY_CODE = "9F1A";
    /**
     * 接口设备序列号
     */
    public static final String TAG_9F1E_INTERFACE_DEVICE_SERIAL_NUMBER = "9F1E";
    /**
     * 应用密文
     */
    public static final String TAG_9F26_APPLICATION_CRYPTOGRAM = "9F26";
    /**
     * 密文信息数据
     */
    public static final String TAG_9F27_CRYPTOGRAM_INFORMATION_DATA = "9F27";
    /**
     * 终端性能
     */
    public static final String TAG_9F33_TERMINAL_CAPABILITIES = "9F33";
    /**
     * 持卡人验证方法结果
     */
    public static final String TAG_9F34_CARDHOLDER_VERIFICATION_METHOD_RESULTS = "9F34";
    /**
     * 终端类型
     */
    public static final String TAG_9F35_TERMINAL_TYPE = "9F35";
    /**
     * 应用交易计数器
     */
    public static final String TAG_9F36_APPLICATION_TRANSACTION_COUNTER = "9F36";
    /**
     * 不可预知数
     */
    public static final String TAG_9F37_UNPREDICTABLE_NUMBER = "9F37";
    /**
     * 交易序列计数器
     */
    public static final String TAG_9F41_TRANSACTION_SEQUENCE_COUNTER = "9F41";
    /**
     * Pan 序列号
     */
    public static final String TAG_5F34_PAN_SEQUENCE_NUMBER = "5F34";
    /**
     * Cryptogram Info Data
     */
    public static final String TAG_4F_CRYPTOGRAM_INFO_DATA = "4F";
    /**
     * IAC-Denial Code
     */
    public static final String TAG_9F0E_IAC_DENIAL_CODE = "9F0E";
    /**
     * IAC-Online Code
     */
    public static final String TAG_9F0F_IAC_ONLINE_CODE = "9F0F";
    /**
     * IAC-Default
     */
    public static final String TAG_9F0D_IAC_DEFAULT = "9F0D";
    /**
     * Form Factor Indicator
     */
    public static final String TAG_9F6E_FORM_FACTOR_INDICATOR = "9F6E";


    /**
     * 解析 EMV Tags 字符串 (TLV 编码).
     *
     * @param emvTags EMV Tags 字符串，例如 "9F2608..."
     * @return 一个 Map, 其中 Tag 是 Key, Value 是 Value.
     */
    public static Map<String, String> parseEMVTags(String emvTags) {
        Map<String, String> tagMap = new HashMap<>();
        // 2. 创建一个TLVList实例用于解析
        TLVList tlvList = new TLVList();

        // 3. 将十六进制字符串转换为字节数组，并使用unpack方法进行解包
        byte[] emvDataBytes = ISOUtil.hex2byte(emvTags);
        tlvList.unpack(emvDataBytes);

        for (TLVMsg tlv : tlvList.getTags()) {
            tagMap.put(Integer.toHexString(tlv.getTag()).toUpperCase(), tlv.getStringValue());
        }
        return tagMap;
    }

}