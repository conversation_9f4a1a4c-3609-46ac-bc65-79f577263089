package com.wosai.mpay.util;

import com.wosai.mpay.exception.MpayException;

import javax.crypto.Cipher;
import java.security.Key;
import java.security.KeyFactory;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;


public class JDSignature {
    public static final String KEY_ALGORITHM = "RSA";
    public static final String KEY_ALGORITHM_DETAIL = "RSA/ECB/PKCS1Padding";


    /**
     * 获取md5签名, 返回小写字符串
     * @param content
     * @param key
     * @param charset
     * @return
     * @throws MpayException
     */
    public static String getSign(Map<String,Object> content, String key,  String charset) throws MpayException {
        ArrayList<String> list = new ArrayList<String>();
        for(Map.Entry<String,Object> entry:content.entrySet()){
            if(entry.getValue()!=""){
                list.add(entry.getKey() + "=" + entry.getValue() + "&");
            }
        }
        int size = list.size();
        String [] arrayToSort = list.toArray(new String[size]);
        Arrays.sort(arrayToSort, String.CASE_INSENSITIVE_ORDER);
        StringBuilder sb = new StringBuilder();
        for(int i = 0; i < size; i ++) {
            sb.append(arrayToSort[i]);
        }
        String result = sb.substring(0, sb.length() - 1);
        result = getMd5Sign(result, key,  charset);
        return result;
    }

    /**
     * 从API返回的XML数据里面重新计算一次签名
     * @param responseString API返回的XML数据
     * @return 新鲜出炉的签名
     * @throws MpayException
     */
    public static String getSignFromResponseString(String responseString, String key,  String charset) throws MpayException {
        Map<String,Object> map = XmlUtils.parse(responseString);
        //清掉返回数据对象里面的Sign数据（不能把这个数据也加进去进行签名），然后用签名算法进行签名
        map.put("sign","");
        //将API返回的数据根据用签名算法进行计算新的签名，用来跟API返回的签名进行比较
        return JDSignature.getSign(map, key, charset);
    }

    /**
     * 检验API返回的数据里面的签名是否合法，避免数据在传输的过程中被第三方篡改
     * @param responseString API返回的XML数据字符串
     * @return API签名是否合法
     * @throws MpayException
     */
    public static boolean checkIsSignValidFromResponseString(String responseString, String key,  String charset) throws  MpayException {

        Map<String,Object> map = XmlUtils.parse(responseString);

        String signFromAPIResponse = map.get("sign").toString();
        if(StringUtils.isEmpty(signFromAPIResponse)){
            return false;
        }
        //清掉返回数据对象里面的Sign数据（不能把这个数据也加进去进行签名），然后用签名算法进行签名
        map.put("sign","");
        //将API返回的数据根据用签名算法进行计算新的签名，用来跟API返回的签名进行比较
        String signForAPIResponse = JDSignature.getSign(map, key, charset);

        if(!signForAPIResponse.equals(signFromAPIResponse)){
            //签名验不过，表示这个API返回的数据有可能已经被篡改了
            return false;
        }
        return true;
    }

    /**
     * 获取md5签名串， 全部小写
     * @param content
     * @param charset
     * @return
     * @throws MpayException
     */
    public static String getMd5Sign(String content, String key, String charset) throws MpayException{
        String md5 = "";
        try {
            md5 = Digest.md5((content + key ).getBytes(charset)).toLowerCase();
        } catch (Exception e) {
            throw new MpayException("failed to generate md5 signature.", e);
        }
        return md5;
    }

    /**
     * 通过证书私钥进行签名
     * @return
     */
    public static String getSignByPrivateKey(String content, String privateKey, String charset) throws MpayException {
        try {
            String sha256SourceSignString = Digest.sha256(content.getBytes(charset));
            byte [] data = sha256SourceSignString.getBytes(charset);
            // 对密钥解密
            byte[] keyBytes = Base64.decode(privateKey);
            // 取得私钥
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            Key key = keyFactory.generatePrivate(pkcs8KeySpec);
            // 对数据加密
            Cipher cipher = Cipher.getInstance(KEY_ALGORITHM_DETAIL);
            cipher.init(Cipher.ENCRYPT_MODE, key);
            return Base64.encode(cipher.doFinal(data));
        } catch (Exception e) {
            throw new MpayException("generate sign error: ", e);
        }
    }

}
