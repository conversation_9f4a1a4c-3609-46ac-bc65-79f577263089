package com.wosai.mpay.util;


import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;
import java.security.Key;
import java.util.Arrays;

public class Des3Utils {

    /**
     * 加密算法
     */
    private static final String KEY_ALGORITHM = "DESede";
    private static final String CIPHER_ALGORITHM = "DESede/ECB/NoPadding";

    /**
     * 3DES 加密
     *
     * @param key   秘钥（24位）
     * @param data  需要加密的字符串
     * @return 返回加密的字符串
     */
    public static String encrypt(String key, String data) {
        try {
            byte[] keyBytes = hexToBytes(key);
            keyBytes = adjustKeyLength(keyBytes);
            DESedeKeySpec spec = new DESedeKeySpec(keyBytes);
            SecretKeyFactory keyfactory = SecretKeyFactory.getInstance(KEY_ALGORITHM);
            Key deskey = keyfactory.generateSecret(spec);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, deskey);
            byte[] bOut = cipher.doFinal(hexToBytes(data));
            return bytesToHex(bOut);
        } catch (Exception e) {
            throw new RuntimeException("3DES 解密错误");
        }
    }

    /**
     * Adjusts the key length to 24 bytes as required by 3DES.
     *
     * @param keyBytes The original key bytes
     * @return The adjusted key bytes (24 bytes)
     */
    private static byte[] adjustKeyLength(byte[] keyBytes) {
        // For 3DES, key must be 24 bytes (192 bits)
        if (keyBytes.length == 24) {
            return keyBytes;
        }

        // If key is 16 bytes, extend it to 24 bytes by appending the first 8 bytes
        if (keyBytes.length == 16) {
            byte[] extendedKey = new byte[24];
            System.arraycopy(keyBytes, 0, extendedKey, 0, 16);
            System.arraycopy(keyBytes, 0, extendedKey, 16, 8);
            return extendedKey;
        }

        // If key is longer than 24 bytes, use only the first 24 bytes
        if (keyBytes.length > 24) {
            return Arrays.copyOf(keyBytes, 24);
        }

        // If key is shorter than 16 bytes, pad it to 24 bytes
        byte[] adjustedKey = new byte[24];
        System.arraycopy(keyBytes, 0, adjustedKey, 0, keyBytes.length);
        return adjustedKey;
    }

    public static void main(String[] args) {
        String data = "0612a2c6fe9e8ebc";

        System.out.println("需要加密的字符串内容为：" + data);

        String des3EncodeCBC = encrypt("64FD13F892044ABABA8A8926EA615EB5", data);
        System.out.println("加密后的字符串内容为：" + des3EncodeCBC);

    }

    /**
     * Convert hexadecimal string to bytes
     */
    private static byte[] hexToBytes(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }

    /**
     * Convert bytes to hexadecimal string
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder(2 * bytes.length);
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString().toUpperCase();
    }
}
