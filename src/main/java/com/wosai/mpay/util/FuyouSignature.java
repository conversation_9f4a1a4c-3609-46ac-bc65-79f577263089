package com.wosai.mpay.util;

import com.wosai.mpay.api.fuyou.FuyouBusinessFields;
import com.wosai.mpay.api.fuyou.FuyouConstants;
import com.wosai.mpay.exception.MpayException;

import java.util.*;
import java.util.stream.Collectors;

/***
 * @ClassName: FuyouSignature
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/9/14 12:05 PM
 */
public class FuyouSignature {

    /**
     *
     * @param params, 根据method获取指定的需要加签的字段
     * @return
     */
    public static String getSignContent(Map<String, Object> params, int method) {
        List<String> keys = getSignatureColumns(method);
        return getSignContent(params, keys);
    }

    public static String getSignContent(Map<String, Object> params, List<String> keys) {
        StringBuffer content = new StringBuffer();
        int index = 0;
        for (String key : keys) {
            Object value;
            value = params.getOrDefault(key, StringUtils.EMPTY);
            content.append(index == 0 ? "" : "&").append(key).append("=").append(value);
            index++;
        }
        return content.toString();
    }

    public static String md5WithRsaSign(Map<String, Object> params, String privateKey, String charset, int method) throws MpayException {
        String signContent = getSignContent(params, method);
        return md5WithRsaSign(signContent, privateKey, charset);
    }

    public static String md5WithRsaSign(String content, String privateKey, String charset)
            throws MpayException {
        try {
            return RsaSignature.sign(content, RsaSignature.SIG_ALG_NAME_MD5_With_RSA, privateKey, charset);
        } catch (Exception e) {
            throw new MpayException("fuyou signature error", e);
        }
    }



    /**
     * 根据不同的接口调用返回需要参与签名的字段
     *
     * @param payMethod
     * @return
     */
    public static List<String> getSignatureColumns(int payMethod) {
        List<String> signColumns = Collections.emptyList();
        switch (payMethod) {
            case FuyouConstants.METHOD_PAY:
                signColumns = FuyouBusinessFields.PAY_SIGNATURE_PARAMS;
                break;
            case FuyouConstants.METHOD_PRECREATE:
                signColumns = FuyouBusinessFields.PRECREATE_SIGNATURE_PARAMS;
                break;
            case FuyouConstants.METHOD_CLOSE:
                signColumns = FuyouBusinessFields.CLOSE_SIGNATURE_PARAMS;
                break;
            case FuyouConstants.METHOD_CANCEL:
                signColumns = FuyouBusinessFields.CANCEL_SIGNATURE_PARAMS;
                break;
            case FuyouConstants.METHOD_QUERY:
                signColumns = FuyouBusinessFields.QUERY_SIGNATURE_PARAMS;
                break;
            case FuyouConstants.METHOD_HISTORY_QUERY:
                signColumns = FuyouBusinessFields.HISTORY_QUERY_SIGNATURE_PARAMS;
                break;
            case FuyouConstants.METHOD_REFUND:
                signColumns = FuyouBusinessFields.REFUND_SIGNATURE_PARAMS;
                break;
            case FuyouConstants.METHOD_REFUND_QUERY:
                signColumns = FuyouBusinessFields.REFUND_QUERY_SIGNATURE_PARAMS;
                break;
            case FuyouConstants.METHOD_NOTIFY:
                signColumns = FuyouBusinessFields.NOTIFY_SIGNATURE_PARAMS;
                break;
            case FuyouConstants.METHOD_WALLET_QUERY:
                signColumns = FuyouBusinessFields.WALLET_QUERY_SIGNATURE_PARAMS;
                break;
            case FuyouConstants.METHOD_SETTLE:
            case FuyouConstants.METHOD_ASYNC_SETTLE:
                signColumns = FuyouBusinessFields.SETTLE_SIGNATURE_PARAMS;
                break;
            case FuyouConstants.METHOD_TXN_FEE_QUERY:
                signColumns = FuyouBusinessFields.TXN_FEE_QUERY_PARAMS;
                break;
            case FuyouConstants.METHOD_QUERY_SETTLEMENT:
                signColumns = FuyouBusinessFields.QUERY_SETTLEMENT_PARAMS;
                break;
            case FuyouConstants.METHOD_AUTH_2_OPENID:
                signColumns = FuyouBusinessFields.AUTH_2_OPENID_PARAMS;
                break;
            case FuyouConstants.METHOD_TRANSFER://富友营销转账接口
                signColumns = FuyouBusinessFields.TRANSFER_PARAMS;
                break;
            case FuyouConstants.METHOD_TRANSFER_QUERY://富友营销转账查询接口
                signColumns = FuyouBusinessFields.TRANSFER_QUERY_PARAMS;
                break;
            case FuyouConstants.METHOD_TRANSFER_REFUND://富友营销转账退回接口
                signColumns = FuyouBusinessFields.TRANSFER_REFUND_PARAMS;
                break;
        }
        return signColumns.stream().sorted().collect(Collectors.toList());
    }


}
