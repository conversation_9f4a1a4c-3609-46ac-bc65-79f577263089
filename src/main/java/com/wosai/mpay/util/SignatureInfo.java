package com.wosai.mpay.util;

import java.math.BigInteger;

public class SignatureInfo {
    BigInteger r;
    BigInteger s;

    public SignatureInfo(BigInteger r, BigInteger s) {
        this.r = r;
        this.s = s;
    }

    public String toString() {
        return "Signature [r=" + this.r.toString(16) + ", s=" + this.s.toString(16) + "]";
    }

    public BigInteger getR() {
        return this.r;
    }

    public BigInteger getS() {
        return this.s;
    }
}