package com.wosai.mpay.util;

import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;
import com.wosai.mpay.exception.MpayException;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Date: 2019/4/25 Time: 4:59 PM
 */
public class ChinaumsSignature {

    public static final Charset CHARSET_UTF8 = Charsets.UTF_8;

    public static String sign(String content, String appId, String appKey, String timestamp, String nonce) {
        String contextHexHash = Hashing.sha256().hashString(content, CHARSET_UTF8).toString();
        StringBuilder builder = new StringBuilder(256);
        builder.append(appId).append(timestamp).append(nonce).append(contextHexHash);
        byte[] bytes = Hashing.hmacSha256(appKey.getBytes(CHARSET_UTF8)).hashString(builder.toString(), CHARSET_UTF8).asBytes();
        return Base64.encode(bytes);
    }

    public static boolean check(Map<String, Object> request, String appKey) {
        String sign = (String)request.get("sign");
        request.remove(sign);
        String signType = MapUtils.getString(request, "signType", "").toUpperCase();
        String guessSign = "";
        if(StringUtils.empty(signType) || "MD5".equals(signType)) {
            guessSign = getMd5Sign(getSignContent(request), appKey, "utf-8");
        }else if(StringUtils.empty(signType) || "SHA256".equals(signType)){
            try {
                guessSign = HmacSignature.sign(getSignContent(request) + sign, HmacSignature.SIG_ALG_NAME_HMAC_SHA256, sign);
            } catch (MpayException e) {
            }
        }
        return guessSign.equals(sign);
    }

    /**
     * 获取md5签名串， 全部大写
     * @param content
     * @param key
     * @param charset
     * @return
     * @throws MpayException
     */
    public static String getMd5Sign(String content, String key,  String charset){
        String md5 = "";
        try {
            md5 = Digest.md5((content + key).getBytes(charset)).toUpperCase();
        } catch (Exception e) {
        }
        return md5;
    }

    /**
     * 
     * @param sortedParams
     * @return
     */
    public static String getSignContent(Map<String, Object> sortedParams) {
        StringBuffer content = new StringBuffer();
        List<String> keys = new ArrayList<String>(sortedParams.keySet());
        Collections.sort(keys);
        int index = 0;
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = null;
            if(sortedParams.get(key) instanceof Map) {
                value = JsonUtil.toJsonStr(sortedParams.get(key));
            }else if(sortedParams.get(key) != null){
                value = sortedParams.get(key).toString();
            }
            if (StringUtils.areNotEmpty(key, value)) {
                content.append((index == 0 ? "" : "&") + key + "=" + value);
                index++;
            }
        }
        return content.toString();
    }

    public static void main(String[] args) {
//        String content = "11223";
//        String appId = "f0ec96ad2c3848b5b810e7aadf369e2f";
//        String appKey = "775481e2556e4564985f5439a5e6a277";
//        String timestamp = String.valueOf(System.currentTimeMillis()) + "112";
//        String nonce = String.valueOf(ThreadLocalRandom.current().nextLong(1222222222,10000000000L));
//        System.out.println(sign(content, appId, appKey, timestamp, nonce));
    }

}
