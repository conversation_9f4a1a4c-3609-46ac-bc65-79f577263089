package com.wosai.mpay.util;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.wosai.mpay.exception.MpayException;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.security.MessageDigest;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

/***
 * @ClassName: HaikeSignature
 * @Description:
 * @Auther: dabuff
 * @Date: 2023/7/10 5:08 PM
 */
public class HaikeSignature {

    private static final String CHARSET = "UTF-8";

    private static final BouncyCastleProvider PROVIDER = new BouncyCastleProvider();

    /**
     * 直接拼接key在最后面
     * 例：key=123456789的时候待签名的字符串为，aa=3&bb=1&cc=1&dd=20160426999123456789
     * @param accessKey
     * @param params
     * @return
     */
    public  static String getSign(String accessKey, Map<String,Object> params) throws MpayException {
        //直接拼接key
        String signContent = getSignContent(params) + accessKey;
        return getMd5Sign(signContent);
    }

    public static String getMd5Sign(String data) throws MpayException {
        try {
            MessageDigest md = MessageDigest.getInstance(Digest.MD5, PROVIDER);
            byte[] rtn = md.digest(data.getBytes(CHARSET));
            return byte2Hex(rtn).toUpperCase();
        } catch (Exception e) {
            throw new MpayException("failed to generate md5 signature.", e);
        }
    }

    public static String getSignContent(Map<String,Object> params){
        TreeMap tm = new TreeMap();
        tm.putAll(params);
        Set set = tm.entrySet();
        StringBuffer sb = new StringBuffer();
        int i = 0;
        for (Iterator iterator = set.iterator(); iterator.hasNext();) {
            Map.Entry e = (Map.Entry) iterator.next();
            String k = (String)e.getKey();
            if(e.getValue() == null || "".equals(e.getValue()) || "sign".equals(k)){
                continue;
            }
            String v;
            if(e.getValue() instanceof JSONArray){
                v = getJsonArrayStr((JSONArray)e.getValue());
            }else if(e.getValue() instanceof JSONObject){
                v = getSignContent((JSONObject)e.getValue());
            }else{
                v = e.getValue().toString();
            }
            if(i != 0){
                sb.append("&");
            }
            sb.append(k).append("=").append(v);
            i = i + 1;
        }
        return sb.toString();
    }

    public static String getJsonArrayStr(JSONArray a){
        StringBuilder r = new StringBuilder();
        for(int j=0; j < a.size() ; j++){
            Object b = a.get(j);
            String v;
            if(b instanceof JSONArray){
                v = getJsonArrayStr((JSONArray)b);
            }else if(b instanceof JSONObject){
                v = getSignContent((JSONObject)b);
            }else{
                v = b.toString();
            }
            if(j ==0){
                r = new StringBuilder(v);
            }else{
                r.append("&").append(v);
            }
        }
        return r.toString();
    }

    public static String byte2Hex(byte[] b) {
        StringBuilder sb = new StringBuilder();
        String stmp;
        for (byte value : b) {
            stmp = (Integer.toHexString(value & 0XFF));
            if (stmp.length() == 1) {
                sb.append("0").append(stmp);
            } else {
                sb.append(stmp);
            }
        }
        return sb.toString().toUpperCase();
    }

}
