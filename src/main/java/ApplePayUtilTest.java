import com.wosai.mpay.model.applepay.ApplePayPayload;
import com.wosai.mpay.model.applepay.ApplePaymentToken;
import com.wosai.mpay.util.ApplePayUtil;
import com.wosai.mpay.util.JsonUtil;

public class ApplePayUtilTest {

    // 主方法示例
    public static void main(String[] args) {
        try {
            // 示例JSON令牌
            String tokenJson = "{\n" +
                    "  \"token\": {\n" +
                    "    \"paymentData\": {\n" +
                    "      \"data\": \"u+nlH8GaIRm1BXa5+zI/AsaNCtddRJ9vkPTJ2s+0cphuVlne55nuDaaTUG/efV6+jW5lteWxMd3vUI2th+gcdNEe9X57QO73CSZSnmS8cGSw10qiNyjkiAGt3Xt0v0nty8V3zNOCirdSOcjSM4cy1pV1FhZz7oHdC0YYNa51IF/hpSY155L42jbfm4Kh8bByYtG8t2nGTQaSrLmqZOMWr6qJ3gV3orUj3kZv9veGwZG0NLrZ52s2IHls5gnh9jjyi4efr1zHFEUzKH5i81Asr9gd7X7/4GHP6mzv6hUDePGECmceC8kUfLPDZ7yOFdVL/gUs/OwgHmpks5XrCwmBTzGoL8KpDC6rPLt9zMR1dyJ15oJ/1ZCfNen9QCqWRiSfznjCtSaWSRwEjQ==\",\n" +
                    "      \"signature\": \"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\",\n" +
                    "      \"header\": {\n" +
                    "        \"publicKeyHash\": \"1aaqBXVpj2UsludhuoXdUUouP1Kp19q83ILL/kGggAw=\",\n" +
                    "        \"ephemeralPublicKey\": \"MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAES7Reep1z3CS8Gv9t8xSMFEap9bzuZLo3nQWRaYGLjH4jJ/BzfpKQbaMex8yVO0PWSeSrHz2vmadkv+rsxv1OZg==\",\n" +
                    "        \"transactionId\": \"4e61413c6cfbac8a4016724b39fe4fcf82051084864dd8cc0bf7150a806256fe\"\n" +
                    "      },\n" +
                    "      \"version\": \"EC_v1\"\n" +
                    "    },\n" +
                    "    \"paymentMethod\": {\n" +
                    "      \"displayName\": \"MasterCard2114\",\n" +
                    "      \"network\": \"MasterCard\",\n" +
                    "      \"type\": \"debit\"\n" +
                    "    },\n" +
                    "    \"transactionIdentifier\": \"4e61413c6cfbac8a4016724b39fe4fcf82051084864dd8cc0bf7150a806256fe\"\n" +
                    "  }\n" +
                    "}";

            // 解密令牌
            ApplePaymentToken decryptedData = ApplePayUtil.decrypt(
                    JsonUtil.objectToJsonString(JsonUtil.jsonStrToObject(tokenJson, ApplePayPayload.class)),
//                    "/Users/<USER>/work/wosai/documents/收钱吧/applepay/publicCert.pem",
                    "/Users/<USER>/work/wosai/documents/收钱吧/applepay/ecc256-apple_pay.cer",
                    "/Users/<USER>/work/wosai/documents/收钱吧/applepay/privateKey-copy.pem",
                    "/Users/<USER>/work/wosai/documents/收钱吧/applepay/AppleRootCA-G3.cer");

            // 输出解密后的数据
            System.out.println("解密后的支付数据: " + JsonUtil.objectToJsonString(decryptedData));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
