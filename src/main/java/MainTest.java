//import com.google.zxing.ChecksumException;
//import com.google.zxing.FormatException;
//import com.google.zxing.NotFoundException;
//import com.wosai.mpay.util.QrcodeUtil;
//
//import java.io.IOException;
//
//public class MainTest {
//
//    public static void main(String[] args) throws ChecksumException, NotFoundException, IOException, FormatException {
//        String image = "0002010102111531370107020052044610000000002732726830017com.qq.weixin.pay01091263138450205273270536aHR0cHM6Ly9wYXkudXFwYXkuY29tL3dlcXI=27600010com.alipay0142https://pay.uqpay.com/v3/27327/SGD?tid=00128620009SG.PAYNOW010130218UEN201611509K#FAZZ030110513OACM27327X00151660007SG.SGQR010527327020701.0004030110401105011060400000708202505195204581253037025802SG5915Keming Bing Sat6009Singapore62540124paynow0d12d8a217476453210315Keming Bing Sat07030016304D7D6";
//        String s = QrcodeUtil.readQRCodeFromBase64(image);
//        UqpayUtil.buildQrcodeTagId(staticQrcodeContent);
//        System.out.println(s);
//    }
//}
