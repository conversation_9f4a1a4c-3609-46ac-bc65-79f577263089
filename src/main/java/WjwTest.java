//import cn.hutool.core.date.DateUtil;
//import com.wosai.mpay.api.abc.*;
//import com.wosai.mpay.exception.MpayApiNetworkError;
//import com.wosai.mpay.exception.MpayException;
//import com.wosai.mpay.util.RsaSignature;
//import com.wosai.mpay.util.StringUtils;
//import com.wosai.mpay.util.ccb.giftcard.MCipherEncryptor;
//import com.wosai.mpay.util.cmbapp.GMSSLContext;
//import org.bouncycastle.jce.provider.BouncyCastleProvider;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.security.Provider;
//import java.security.Security;
//import java.text.SimpleDateFormat;
//import java.util.Date;
//import java.util.Map;
//
///**
// * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/10/13.
// */
//public class WjwTest {
//    private static final Logger logger = LoggerFactory.getLogger(WjwTest.class);
//
//    public static void main(String[] args) throws Exception {
//        logger.debug("hello");
////        testAbc();
////        test();
//        test1();
//    }
//
//    public static void test(){
//        for (Provider provider : Security.getProviders()) {
//            for (Provider.Service service : provider.getServices()) {
//                String type = service.getType();
//                String algorithm = service.getAlgorithm();
//
//                if (type.equals("Cipher")) { // 只打印Cipher算法
//                    System.out.println(provider.getName() + ": Algorithm: " + algorithm);
//                }
//            }
//        }
//
//    }
//    public static void test1() throws Exception {
//        String privatekey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCQoNehvKZhVLdlGsHMGaGr517yvjwDmeLgPce39iAXUivcuJVlIi/O1yOU5/xNS25maRtvRVBjGfvF/gaEy8eyTOrRNLf5Ak8L7iozMBN956i0XukbV1gkdSUDc9Gq6hjer2DhtxEvBMH/evrHsnoW3LeCBqPxblWtLuCFxqzdC2xQ27e6OPb4stRcT7iLCGRVakXrdTGrQsOuUYHr3LimBGF/P6+00ZHgYuME3ty5EntKddyhmG4v7C8BWuuN5hufsFiDE3Y6Ujv3RtfX8GYc37/Rtv9MjbSuUkQ/ea5kz74KufJ9SBIsM2evTQRmi6Me50NSLVQs1dTeA0l+l8APAgMBAAECggEAJzRCX2hI3ZdeQzzWjaHhDj0gosJoeD1tn4V/ruE737Z5h1JNxOVYekQdNA8SeE1bNs2FD0uFt+diNVBfqf2nOzDYci9NM2Wp0b0UmV2N2xJBy+ryW2P406EhkGcDzcW5ShvWMjkyokEy6TMH6bv6llnsMIXmSNq0xMYxPHIXh69W6vj5Sd5sgdSLY/9bgNCB5axQJqAQnxCdjl2trDl8QkCne9gibr0fwDaYFerZifYXfIo8S1EtkuIozQ6L0dnVSvI6OFrlZxq0NZn2+gq7FU1BXM+A07AUZc1KQfyYcm4bjrMkJwMtqt5ZleXlxjP9a6MfE4uNh+1bJrV6Xf+JyQKBgQDyQyhK/9iiCv2yrLVyJ2KiAE1cWDvpDTe25Lv52RmxU7SA0w6LrrQyDtldm8v8qoPkh9X8JPoBaAQDe4LvHtsnEpGo2dovnpzv7tLI+QcfX0lwoVOXY8k06AsCjx1BEW8+dHqediajpX5QdDihZswecLFKq7P54FAs8N3TTudPNQKBgQCY1F0WqECQ3zlxvfkwUxNPkNdBWXbE4/HD5m4lwLiszKg+6L/xgMSHAoNVtG0p+GYwsm5olavxg4ia5bmnb41aP5H7JgQIdNPXqjxlnvoIpVgTafWpbi4VI7voVzh+651IDVpzXkA3hWyzcywVAjrnum/6I60zObuMsJlvZGOmswKBgQCrjeUVnzGAJnpbMbRXvw44nDAsKYtbUHHYwQdgOV3ZVeYnp83QGooePKeugyojpXg6t/pPULv9jPPyp18kB9y3QY4GXScFu7N0sNFxYe+qRqbR7Fwug4ozPfK2ADR+JGN7fa5rUk2wavwU6whDO3FmSPUzZH3RgAgmmvbqsSBZ1QKBgGZLx19yEX2nHRFIQrKY+NUKMzr3wir6FIq3A8VbYYGwn3E0Y74oHePsx7InSeaM8QHpfUOd43CORXLBD+rGHUxqPrFn+c0bPe0ecrzmk6AT5IdiJmBZcpkNySo0r4ks9qMS5RdR4BQJx/+5uKj7TChtv6QkGzutih8a2ikYfidHAoGADwchrvJIwlcBwghAkZhm5zHhe6xgvRllzDAZ2CYyMbkOwCKX0dZZpQVb00yuGnPjz+avDQE21Ce3ec9i76kiTAewCEWRsR+QVCf5Rq8CCM0Lb7fNRtwp13UGVRe/Vo0/6BPJ2qvmgsqsXIqqW+ci0Q2ZP2S6QxglZwrDK1NqeTY=";
////        String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2iCJ36duFr0Ke5M37zZzZJ4kK\nmRApr42H8rG1mpuCQNvNmgSKl3L+0P1mqQf3/0RrseB9G8BKNwlqBQSz28L9ynWQ\nv4Hq1LTgHtEjQc0w73ZJ32JeOmfq8oor84YxjNDNJu3RHNNkuOyM7lFr3BtYkaIa\nIAvLc8LeO2tx4nvRTwIDAQAB";
//        String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkKDXobymYVS3ZRrBzBmhq+de8r48A5ni4D3Ht/YgF1Ir3LiVZSIvztcjlOf8TUtuZmkbb0VQYxn7xf4GhMvHskzq0TS3+QJPC+4qMzATfeeotF7pG1dYJHUlA3PRquoY3q9g4bcRLwTB/3r6x7J6Fty3ggaj8W5VrS7ghcas3QtsUNu3ujj2+LLUXE+4iwhkVWpF63Uxq0LDrlGB69y4pgRhfz+vtNGR4GLjBN7cuRJ7SnXcoZhuL+wvAVrrjeYbn7BYgxN2OlI790bX1/BmHN+/0bb/TI20rlJEP3muZM++CrnyfUgSLDNnr00EZoujHudDUi1ULNXU3gNJfpfADwIDAQAB";
//
//        String data = "hello";
//        String encrypt = RSAEncrypt.encrypt(data, publicKey);
//        System.out.println(encrypt);
//        String decrypt = RSAEncrypt.decrypt(encrypt, privatekey.replaceAll("\n", ""));
//        System.out.println(decrypt);
//        String sign = RSAEncrypt.sign(data.getBytes(), privatekey);
//        System.out.println(RSAEncrypt.verify(data.getBytes(), publicKey, sign));
//
//
//    }
//
//    public static void testAbc() throws MpayException, MpayApiNetworkError {
//        System.out.println(GMSSLContext.class);
//        System.out.println(MCipherEncryptor.class);
////        new MCipherEncryptor("xxxxxxxxxxxx");
//        new GMSSLContext(null);
//
//        String gateway = "https://bjpay.echase.cn/gateway/bmpapi/postrans";
//        String key = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2iCJ36duFr0Ke5M37zZzZJ4kK\nmRApr42H8rG1mpuCQNvNmgSKl3L+0P1mqQf3/0RrseB9G8BKNwlqBQSz28L9ynWQ\nv4Hq1LTgHtEjQc0w73ZJ32JeOmfq8oor84YxjNDNJu3RHNNkuOyM7lFr3BtYkaIa\nIAvLc8LeO2tx4nvRTwIDAQAB";
//        ABCClient client = new ABCClient();
//        String countno = "950120000289713";
//        com.wosai.mpay.api.abc.RequestBuilder requestBuilder = new RequestBuilder();
//        requestBuilder.set(ABCRequestFields.PAYMODE, ABCConstant.QRCODE);
//        //默认为查询
//        requestBuilder.set(ABCRequestFields.TRANSTYPE, ABCConstant.QUERY);
//        requestBuilder.set(ABCRequestFields.COUNTNO, countno);
//        String tsn = "TEST" + System.currentTimeMillis() + "";
//        requestBuilder.set(ABCRequestFields.TERMINAL_SERIALNO, tsn);
//
//        long amount = 1;
//        String orderAmount = StringUtils.cents2yuan(amount);
//        requestBuilder.set(ABCRequestFields.AMOUNT, orderAmount);
//
//        String tradeTime = new SimpleDateFormat(ABCConstant.DATE_SIMPLE_FORMAT).format(new Date());
//        String tradeTime2 = new SimpleDateFormat(ABCConstant.DATE_TIME_SIMPLE_FORMAT).format(new Date());
//        requestBuilder.setPlatformField(ABCRequestFields.REQ_DATE, tradeTime);
//        requestBuilder.setPlatformField(ABCRequestFields.REQ_TIME, tradeTime2);
//        requestBuilder.setPlatformField(ABCRequestFields.PROV_CODE, ABCConstant.AREA_CODE);
//        requestBuilder.setPlatformField(ABCRequestFields.SRC_AREA_CODE, ABCConstant.AREA_CODE);
//        requestBuilder.setPlatformField(ABCRequestFields.ACCESS_TYPE, ABCConstant.ACCESS_TYPE);
//        requestBuilder.set(ABCRequestFields.TRANSTYPE, ABCConstant.PAY);
//
//        //b2c 支付 加上二维码
//        requestBuilder.set(ABCRequestFields.AUTH_CODE, "287413321788945991");
//        Map<String, Object> call = client.call(requestBuilder, gateway, key, ABCConstant.HEADERS);
//        System.out.println(call);
//
//    }
//}
