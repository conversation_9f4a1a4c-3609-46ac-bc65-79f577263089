package com.wosai.mpay;

import com.wosai.mpay.api.yop.*;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;

import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/3/30.
 */
public class YopClientMainTest {
//    private static final String APP_KEY = "sandbox_rsa_10080041523";
//    private static final String MERCHANT_NO = "10080041523";
//    private static final String ISV_PRIVATE_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDChomOdGrRa/D889B9ARtsLb/9zVJxq7VPV9yTu6ZjbhtpwS8Qep/95W7pWcK9yvNH5pWPpYZYwRB55z2Z/9SVqn+2sVU18JCxsgw7o35FUg9Qu9eiZeRpCiV8fbjhI9ZJpDh4v48MKcYBRM0LBZSQjA67xh4K90PYxI7UAgnMWW25Ny37oYxmH8ZoihemWvaRPFx4k4c9knBL5aPEspwIQjfDztOcOoRMkhZo5hUuI/GsKlw0REZ5lUdGKzgQ5ec4ZBe4ijFTsEjSvHcLaJbVnw9PdQ7a80Gw0Cf3qwS6dOR9LgsPLoUsKf9XRu0X6csS5uqu4e4I8lJm+WNZb+l3AgMBAAECggEAFeSOCwjXfsiptND8mB0C85VgjsAddRVF+281hYZF2dnqCZMdk2Vhp/G686tPq2Gcfhfu9t2Xta8g22oRjklIfoGpDFbVSBP84kAvd+9/cMN6ssjj928v75HIme63sIBX3S43fCt+/iJIxRrJAuJhZTVGG+RWZus2Pmlnc704/L+qP93XOVwFk/hKXhy7/Aa2S7KjVr5SpEDNUJ94W8WEpFgfccCrlkbuLAWG9nJF1gAoi0w6AJfEsTWpnNjpdHfFDtcT9UdULgJuz9yhzZya+mYbcjmLATqFihXJWsw4AKPnjOyMRPZP3EdtmOdiRddRnj82dsN5pjz651xFk5EuvQKBgQDWScg70XXwXgm527Kytra81VHW/WKcOw1w37GUiLJG4324fJeupXJ2zt4bmILrLhPTJkDNxE/1roX7GR1tn523H9s29b9Dqwfr38MSjfvJ5yPlaXrOLzDD7myDhZHTqvqgV1/id0IJZ3BeXTVEwd8yjfwEuZHW5s8TilOLoIcXswKBgQDoY/WTLfNleJLHFG4iIDbYBdK7zx105WVOmZF893sEP/4XPVq4RakSZG3CpVpYH5bsXyPTjwUoCoLLXmJzSJFLISemkn4Ot8xJRWPgZoDqm1Fi72O6VgNJlu9lX2ZcMKH4pAXqe7WMBSrxZqXL52gZsyv5YM+uBkY29DtHp7zFLQKBgDn1juEPEHVJGhxZHgZUgSymDhK2SjuzhTkoZ+Gi74VY9qI1oNkuCr2zykNwhsiRl+8eg5ykInRzFe4KpvkFmST0ytgcs/Tbh7L2vM6B9L5xdDYSx5KJFQmJrXQNZpn3vv4rY9XfJ89fWPdNAqFsRrBn0uh8QMP9fbjtSxeS/bcdAoGBAJTJqymYegW1tQQRaJIg3fxhfhMRAGMfnEU+vY+tQ+3sqtpmRfdFYoKMGlpNVBKn5xFfuKhzIXIJiMR8obv98kiP6bsUf/EcbIddDh1Wg6Ox3eHiM4/SEjjDknLtKbRMzudK3R7MJeiIRn5Yoj5y4ovR043PFijti3cT2ACAvLPhAoGAX5zTGz4M/JuqF5TWLKARGAZ9HiYoTLtJs5l/yTkKR4NO/E79TsBlUfDBhVYb+A3ChN6P8JX3cSwUplSiUo12eqG9/DPUSmeRrhTXlbul7metzaEVl1fQhaOIHflxkvg+FZIIt+NHyi6oGmDPXYfwUU3QqifMP5mF+v7IjTcPyD4=";
//    private static final String YOP_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4g7dPL+CBeuzFmARI2GFjZpKODUROaMG+E6wdNfv5lhPqC3jjTIeljWU8AiruZLGRhl92QWcTjb3XonjaV6k9rf9adQtyv2FLS7bl2Vz2WgjJ0FJ5/qMaoXaT+oAgWFk2GypyvoIZsscsGpUStm6BxpWZpbPrGJR0N95un/130cQI9VCmfvgkkCaXt7TU1BbiYzkc8MDpLScGm/GUCB2wB5PclvOxvf5BR/zNVYywTEFmw2Jo0hIPPSWB5Yyf2mx950Fx8da56co/FxLdMwkDOO51Qg3fbaExQDVzTm8Odi++wVJEP1y34tlmpwFUVbAKIEbyyELmi/2S6GG0j9vNwIDAQAB";
//private static final String SERVER_ROOT = "https://sandbox.yeepay.com/yop-center";

    //  生产
//    private static final String APP_KEY = "app_10090973331";
////    private static final String MERCHANT_NO = "10091113328";
//    private static final String MERCHANT_NO = "10090716648";
////    private static final String MERCHANT_NO = "10091149285"; //运营给的
//    // 10091149285
//    private static final String ISV_MERCHANT_NO = "10090973331";
//    private static final String ISV_PRIVATE_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDdXZQ4/yhiFp+gfyMgOozggJnmVFsnUgXSABBtO4M6AevTLwjb6AwwTHj2VSVOVgB9kXGEsR3ltj4W2IhWMBKtq4mDOu8wSnAf4YEPMlLYpJwwxGz9kMtIChrIB2r/LScB7qxxtjtHNoof/zv+0TV67zTW3Q1qFV7NKJFWZE5OgFdVGDXg5qgXrAww4HC8Rp989pkZhnx8SXUnMCAr6sZZkKZfHDoLqclQ4tu/Kq2N6a/s2C8p79zHMuM2M9YDdaSAWCuZgURQuyknH0UwFcrc9PkFqshgKQI+yZbLGwIX+IPePqISNNLLhbp88dwfXGgikYMPpIQQTEq9JLpuxoaTAgMBAAECggEAU636SAl8dIfk08D7wEUh2AQFKWKeCqEg/h90Fja3BoiPYweWq7puURu3ZjcNoS01hkIizWw+xwU7N1unPh1yEiCUHP18SZ75/7M5HEuDsc2YFX9LTBceIR9gvFjBg7DH3B0jJuEiS2s8j5fmUA85zjz6lnZ9iaqGabt6JVRJnbaGS+LwBWW5xCvU+/n+tpe+mU8hSGDXfNvg/g5Rg40XYlluBmY6CcitwDub3xxbkLXPwU0l87F4cATwT2jpdpIpKQSKsgIsWtPKuLU2teIRdCRhf75n6rqxuSXqLu+SaypqgCFUD8c0XmoMNB4f9UqXNni63kHyfRBXWjnrda5DEQKBgQDqF3LAGkIH8BzCimVwACUpxfUu4b/+rdUlPL/UNGQZhTYL0bj2fA+7qA6G4SgsfFfOwlBA7r5jUZrLRHOXdth7sW5PSJ5ZFpWCca7qY+TYXt6wVnMYMs+Co9r4Ner13Yvx+Tarnk8q+zxpzkui6+RBYImaUxxohNmxQGlAMKbGBQKBgQDyFTrcjHKpb1xFIFJtQBVrXt1DNDgpDg1/CWzkI1qqcByERnTjYJ5gpea+nKit3P/3iWx42wlWjvAmx46tH0UEplj9NjzmAj3l5xoY682rr5CtNOQ9MiNTdmWa9RBKHiSsZ5CG8ZmuyENrMjC2COPXTb0/J6j7FlyLM1oZQeNltwKBgC4c/F/XTFaf+CpidOUBQlDeTBQ2KEoVGD3Dn84/m9BjK2hcoeD8Q2UmD+aGCBHrVKjIImMIJL0P4DxWeabGpJKJOz+nAHpwNneByYEmuHffCGMYRIfpynLjsEjg5i4UvHKoMxrPjqza3OXC26d2bHpsd+7MnHYCZZfDJnicnN25AoGAd6pe6l7S30aZCeKsXCztaaH/JhVOHZ1AtqdmGHARhWbd8dUSOsOsBoJ7yYWPLEKulSJtaGB+ATevJrxGnvVyHJZOLvz8pYY+KsNOdzs488FZdwVKPaXnKH7CQxMY9kpoPoEDRHFxUqDUEs1AnvtOBUP8ZJeqRFQNcxHzW8DiM4kCgYEAsJGvQmsX7TRRFZLAJuPLLK+OAl3XDMDpvZhicAlYll8y1Iv7nfoJpLk4AwbSw9LJ5r7bq7h5BqBYY6r/jwGIwAGwDgWG4ay0aOEyl5fTdxnoFq2TVtJXVYjA+BCBNa9PL8tuW/a7lcZfJoLvMf+AVdL0XO5ykSqOMUCNd8NLdYE=";
//    private static final String YOP_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4g7dPL+CBeuzFmARI2GFjZpKODUROaMG+E6wdNfv5lhPqC3jjTIeljWU8AiruZLGRhl92QWcTjb3XonjaV6k9rf9adQtyv2FLS7bl2Vz2WgjJ0FJ5/qMaoXaT+oAgWFk2GypyvoIZsscsGpUStm6BxpWZpbPrGJR0N95un/130cQI9VCmfvgkkCaXt7TU1BbiYzkc8MDpLScGm/GUCB2wB5PclvOxvf5BR/zNVYywTEFmw2Jo0hIPPSWB5Yyf2mx950Fx8da56co/FxLdMwkDOO51Qg3fbaExQDVzTm8Odi++wVJEP1y34tlmpwFUVbAKIEbyyELmi/2S6GG0j9vNwIDAQAB";

    private static final String APP_KEY = "app_10090973331";
    //    private static final String MERCHANT_NO = "10091113328";
    private static final String MERCHANT_NO = "10090716648";
    //    private static final String MERCHANT_NO = "10091149285"; //运营给的
    // 10091149285
    private static final String ISV_MERCHANT_NO = "10090973331";
    private static final String ISV_PRIVATE_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDdXZQ4/yhiFp+gfyMgOozggJnmVFsnUgXSABBtO4M6AevTLwjb6AwwTHj2VSVOVgB9kXGEsR3ltj4W2IhWMBKtq4mDOu8wSnAf4YEPMlLYpJwwxGz9kMtIChrIB2r/LScB7qxxtjtHNoof/zv+0TV67zTW3Q1qFV7NKJFWZE5OgFdVGDXg5qgXrAww4HC8Rp989pkZhnx8SXUnMCAr6sZZkKZfHDoLqclQ4tu/Kq2N6a/s2C8p79zHMuM2M9YDdaSAWCuZgURQuyknH0UwFcrc9PkFqshgKQI+yZbLGwIX+IPePqISNNLLhbp88dwfXGgikYMPpIQQTEq9JLpuxoaTAgMBAAECggEAU636SAl8dIfk08D7wEUh2AQFKWKeCqEg/h90Fja3BoiPYweWq7puURu3ZjcNoS01hkIizWw+xwU7N1unPh1yEiCUHP18SZ75/7M5HEuDsc2YFX9LTBceIR9gvFjBg7DH3B0jJuEiS2s8j5fmUA85zjz6lnZ9iaqGabt6JVRJnbaGS+LwBWW5xCvU+/n+tpe+mU8hSGDXfNvg/g5Rg40XYlluBmY6CcitwDub3xxbkLXPwU0l87F4cATwT2jpdpIpKQSKsgIsWtPKuLU2teIRdCRhf75n6rqxuSXqLu+SaypqgCFUD8c0XmoMNB4f9UqXNni63kHyfRBXWjnrda5DEQKBgQDqF3LAGkIH8BzCimVwACUpxfUu4b/+rdUlPL/UNGQZhTYL0bj2fA+7qA6G4SgsfFfOwlBA7r5jUZrLRHOXdth7sW5PSJ5ZFpWCca7qY+TYXt6wVnMYMs+Co9r4Ner13Yvx+Tarnk8q+zxpzkui6+RBYImaUxxohNmxQGlAMKbGBQKBgQDyFTrcjHKpb1xFIFJtQBVrXt1DNDgpDg1/CWzkI1qqcByERnTjYJ5gpea+nKit3P/3iWx42wlWjvAmx46tH0UEplj9NjzmAj3l5xoY682rr5CtNOQ9MiNTdmWa9RBKHiSsZ5CG8ZmuyENrMjC2COPXTb0/J6j7FlyLM1oZQeNltwKBgC4c/F/XTFaf+CpidOUBQlDeTBQ2KEoVGD3Dn84/m9BjK2hcoeD8Q2UmD+aGCBHrVKjIImMIJL0P4DxWeabGpJKJOz+nAHpwNneByYEmuHffCGMYRIfpynLjsEjg5i4UvHKoMxrPjqza3OXC26d2bHpsd+7MnHYCZZfDJnicnN25AoGAd6pe6l7S30aZCeKsXCztaaH/JhVOHZ1AtqdmGHARhWbd8dUSOsOsBoJ7yYWPLEKulSJtaGB+ATevJrxGnvVyHJZOLvz8pYY+KsNOdzs488FZdwVKPaXnKH7CQxMY9kpoPoEDRHFxUqDUEs1AnvtOBUP8ZJeqRFQNcxHzW8DiM4kCgYEAsJGvQmsX7TRRFZLAJuPLLK+OAl3XDMDpvZhicAlYll8y1Iv7nfoJpLk4AwbSw9LJ5r7bq7h5BqBYY6r/jwGIwAGwDgWG4ay0aOEyl5fTdxnoFq2TVtJXVYjA+BCBNa9PL8tuW/a7lcZfJoLvMf+AVdL0XO5ykSqOMUCNd8NLdYE=";
    private static final String YOP_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4g7dPL+CBeuzFmARI2GFjZpKODUROaMG+E6wdNfv5lhPqC3jjTIeljWU8AiruZLGRhl92QWcTjb3XonjaV6k9rf9adQtyv2FLS7bl2Vz2WgjJ0FJ5/qMaoXaT+oAgWFk2GypyvoIZsscsGpUStm6BxpWZpbPrGJR0N95un/130cQI9VCmfvgkkCaXt7TU1BbiYzkc8MDpLScGm/GUCB2wB5PclvOxvf5BR/zNVYywTEFmw2Jo0hIPPSWB5Yyf2mx950Fx8da56co/FxLdMwkDOO51Qg3fbaExQDVzTm8Odi++wVJEP1y34tlmpwFUVbAKIEbyyELmi/2S6GG0j9vNwIDAQAB";



    private static final String SERVER_ROOT = "https://openapi.yeepay.com/yop-center";
    // query url
    private static final String QUERY_ORDER_URL = "/rest/v1.0/trade/order/query";

    private static final String AES_ENCRYPT_ALG = "AES/ECB/PKCS5Padding";
    private static final String DEFAULT_ENCODING = "UTF-8";

    // API Endpoints
    public static final String TUTELAGE_PRE_PAY_URL = "/rest/v1.0/aggpay/tutelage/pre-pay";
    public static final String PAY_URL = "/rest/v1.0/aggpay/pay";
    public static final String PRE_PAY_URL = "/rest/v1.0/aggpay/pre-pay";
    public static final String PAY_LINK_URL = "/rest/v1.0/aggpay/pay-link";
    public static final String CLOSE_ORDER_URL = "/rest/v1.0/aggpay/close-order";
    public static final String GET_USER_ID_URL = "/rest/v1.0/aggpay/get-user-id";
    public static final String REFUND_URL = "/rest/v1.0/trade/refund";
    public static final String QUERY_REFUND_URL = "/rest/v1.0/trade/refund/query";
    public static final String CLOSE_TRADE_URL = "/rest/v1.0/trade/order/close";
    public static final String WECHAT_CONFIG_ADD_URL = "/rest/v2.0/aggpay/wechat-config/add";



    public static void main(String[] args) throws Exception {
        testPay();
//        testQuery();
//        testGetUserId();
//        testAddWechatAppid();
    }

    // test add wechat appid
    public static void testAddWechatAppid() throws Exception {
        YopClient client = new YopClient(1000, 30000);
        Map<String, String> request = buildCommonRequestParams(null);

        request.put(YopRequestFields.TRADE_AUTH_DIR_LIST, JsonUtil.objectToJsonString(Arrays.asList(
                "", ""
        )));
        request.put(YopRequestFields.APP_ID_LIST, JsonUtil.objectToJsonString(Arrays.asList(
                MapUtils.hashMap(
                        YopRequestFields.AppId.APP_ID, "",
                        YopRequestFields.AppId.APP_SECRET, "test",
                  YopRequestFields.AppId.APP_ID_TYPE, YopRequestFields.AppIdType.MINI_PROGRAM
                ),
                MapUtils.hashMap(
                        YopRequestFields.AppId.APP_ID, "",
                        YopRequestFields.AppId.APP_SECRET, "test",
                        YopRequestFields.AppId.APP_ID_TYPE, YopRequestFields.AppIdType.MINI_PROGRAM
                )
        )));
        request.put(YopRequestFields.REPORT_MERCHANT_NO, "");
        Map<String, Object> call = client.call(YopConstant.YopRequestMethod.POST, YopConstant.YopRequestContentType.FORM_URL_ENCODE, SERVER_ROOT + GET_USER_ID_URL, request, APP_KEY, ISV_PRIVATE_KEY, YOP_PUBLIC_KEY);
        System.out.println(call);

    }

    // test get userid
    public static void testGetUserId() throws Exception {
        YopClient client = new YopClient(1000, 30000);
        Map<String, String> request = buildCommonRequestParams(null);

        request.put(YopRequestFields.USER_AUTH_CODE, "0e1ae58c200000611wJLyhQ4");

        Map<String, Object> call = client.call(YopConstant.YopRequestMethod.POST, YopConstant.YopRequestContentType.JSON, SERVER_ROOT + GET_USER_ID_URL, request, APP_KEY, ISV_PRIVATE_KEY, YOP_PUBLIC_KEY);
        System.out.println(call);
    }

    // testPay
    public static void testPay() throws Exception {
        YopClient client = new YopClient(1000, 30000);
        Map<String, String> request = buildCommonRequestParams(null);
        request.put(YopRequestFields.PAY_WAY, PaywayConstant.MERCHANT_SCAN);

        request.put(YopRequestFields.AUTH_CODE, "132678839844311186");
//        request.put(YopRequestFields.SCENE, SceneConstants.AlipayScene.OFFLINE);
        request.put(YopRequestFields.SCENE, SceneConstants.WechatScene.OFFLINE);

        request.put(YopRequestFields.USER_IP, "127.0.0.1");
        request.put(YopRequestFields.TERMINAL_ID, "001");
        request.put(YopRequestFields.ORDER_ID, getUniqOrderId());
        request.put(YopRequestFields.ORDER_AMOUNT, "0.01");
        request.put(YopRequestFields.GOODS_NAME, "测试商品");
        request.put(YopRequestFields.EXPIRE_TIME, YopUtil.formatDate(System.currentTimeMillis() + 1000 * 60 * 1));
        request.put(YopRequestFields.NOTIFY_URL, "https://upay-gateway.iwosai.com/upay/v2/notify/yop/2cce8873bba45b8f8515beea6f7c5489/");

        Map<String, Object> call = client.call(YopConstant.YopRequestMethod.POST, YopConstant.YopRequestContentType.FORM_URL_ENCODE, SERVER_ROOT + PAY_URL, request, APP_KEY, ISV_PRIVATE_KEY, YOP_PUBLIC_KEY);
        System.out.println(call);

    }

    // get uniq orderId
    public static String getUniqOrderId() {
        return String.valueOf("TEST" + System.currentTimeMillis());
    }

    public static void testQuery() throws Exception {
        YopClient client = new YopClient(1000, 30000);
        Map<String, String> request = buildCommonRequestParams(null);
        request.put(YopRequestFields.ORDER_ID, "7894259299071072");
        Map<String, Object> response = client.call(YopConstant.YopRequestMethod.GET, YopConstant.YopRequestContentType.FORM_URL_ENCODE, SERVER_ROOT + QUERY_ORDER_URL, request, APP_KEY, ISV_PRIVATE_KEY, YOP_PUBLIC_KEY);
        System.out.println(response);

    }


    private static Map<String, String> buildCommonRequestParams(Map<String,Object> config) {
        Map<String, String> request = new HashMap<String, String> ();
        request.put(YopRequestFields.MERCHANT_NO, MERCHANT_NO);
        request.put(YopRequestFields.PARENT_MERCHANT_NO, ISV_MERCHANT_NO);
        return  request;
    }

}
