////package com.wosai.mpay;
////
////import com.wosai.mpay.api.paynet.DuitNowQRParser;
////import com.wosai.mpay.api.uqpay.*;
////import com.wosai.mpay.util.*;
////
////import java.io.IOException;
////import java.nio.file.Files;
////import java.nio.file.Paths;
////import java.util.Collections;
////import java.util.Map;
////
/////**
//// * Created by wujian<PERSON> on 2024/12/24.
//// */
////public class UqpayTest {
////    // merchantId
////    private static final String MERCHANT_ID = "1006049";
////    // agentId
////    private static final String AGENT_ID = "1006048";
////
////    // agent rsa private key
//////    private static final String AGENT_PRIVATE_KEY = "MIIEpQIBAAKCAQEAlzokDvsXdgKrcLKHkZ4YnFKGTyfDi71U3BHDJLwhQ6OOxuCPh+lyMdBZbp7bl8NgNGw7ugFesMaYhGbzxXH2MzGoejFapRel5PIFKj8zEV2iQGRXcbecTH91VCpSygv4zy//vKUxEOAshBfoK7RqFdtTsqdmDcuiqFo6Ot+pKJC0UwZ8rhUII6xkPHYMwFrPaVarHoJ3xQkIhJhRnzWSsJjY3QvoL7jU+TKn8CLOGupjI/fxLzTxlyC4keWAvtDbz9Pvbhip5g5IVydMTDwfHpEY5vKg5A3RL4IcWLp0vZzPINjYHXrQg+TQPPj0x/nwUCE4F98netOnVWfhDu0xIQIDAQABAoIBAQCRS2UVl50k3s/EyWKDP7g5iPhN2NCB0/hSMCajhrUW/W/SGDMOy/XRcnJaW/pzR12JQGvp/CL0CPrESv3vN9zpts4+CGkksSHMY9MekfYIu0OGRmHVeimLPfKGWNuOEA6PUXiN0NDhe+CJUUbZps8+DKnPvhHJM1smEPpdUkJ+G3npa8LmIG/P/gbGn8g2udf4p//4ji0Q6PN4ny21QlSReJc+kWKDseqUpC1Kvoi0iCMPbpiuNGy4ZmwFypaMK+ybkTCip1CrbUubl359S1HN8vmc2ELhTQm57SzMlb8cJF112V3s50GHFG3pAmlch1m86mWYSGF0/VAIpk5WCSQ5AoGBAOlijRk9icIJSOlDVATm7mtoto4JcM1xCxLu//0uMZtxGiJHHHQLe5rnGejDfOoUOEMw09qd0euDs7gCFMnGhJlLxQDijzXgrMDAC7rSEa1SnMIo8/FbprLBQlvBz1pAQb3aklZXPDHwvmwJEnY/TCso/I6UQ5PkyoXYGN/1F1YzAoGBAKXhi9p900TuL+ZizlS+4eLZjeeoLE4lfRd+cT/HInvKSvRB5zhnc4sUzzkiE/N6xIkwEAcQ9qtgDRtxd25ODKcvLxMF5eS18AcaU8nKuV18zO2H9Et+jERVz4MToIQAuF5Tl1urcFU4Z24kPO/4iggbBM87lpfRI7VG/29P6T9bAoGBAKsYCZLAb3ZlciM3raeuWIk7ieGVADJMNi8aMWa9b7ah8uPAOBbGuEX2OCNQ9Qlx61EuwZEac+NxeNZkCDNoKptrVbBxZ0W7QUpJQBAkorrqvmsEtNlzNEEFtw0nh0iFpOx3CSLs7v85m4/+Ep/HhaJ6eLRZ8zS/3MzEgG/m8MXLAoGADpgne9OyMNBb+oMa8C3TJehKKeWL8FwwA08mm5vjlsyXh6H8u+OuMMBl8vrikntsqz1FS7ncWfuba5yyC0xmpJbsJ1pPfa3cLmPMl2Z0pJsEVKmvfqzlnmWGWlvxtWtyzSnBmVkeCwFpaDFcAyj0y/3EkI8HktfdsmdKtfpr5R0CgYEA1pE4BO37QxJrk3oDgqyPyxXI0kJd9L8RMtk5o3jmakYMw7Icu7oza+3Qq7s+yJ+7J00VzBvttPs0xiuXSiIyHF5DY1/vwWM7/qsFPNWvavZeKv2jLTZsuov4xGG6k7OynFftkuFnL6ghc8eKciV4SxvLohAXdDm6EBVV9AxjpAY=";
////    private static final String AGENT_PRIVATE_KEY = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCXOiQO+xd2Aqtw\n" +
////            "soeRnhicUoZPJ8OLvVTcEcMkvCFDo47G4I+H6XIx0FluntuXw2A0bDu6AV6wxpiE\n" +
////            "ZvPFcfYzMah6MVqlF6Xk8gUqPzMRXaJAZFdxt5xMf3VUKlLKC/jPL/+8pTEQ4CyE\n" +
////            "F+grtGoV21Oyp2YNy6KoWjo636kokLRTBnyuFQgjrGQ8dgzAWs9pVqsegnfFCQiE\n" +
////            "mFGfNZKwmNjdC+gvuNT5MqfwIs4a6mMj9/EvNPGXILiR5YC+0NvP0+9uGKnmDkhX\n" +
////            "J0xMPB8ekRjm8qDkDdEvghxYunS9nM8g2NgdetCD5NA8+PTH+fBQITgX3yd606dV\n" +
////            "Z+EO7TEhAgMBAAECggEBAJFLZRWXnSTez8TJYoM/uDmI+E3Y0IHT+FIwJqOGtRb9\n" +
////            "b9IYMw7L9dFyclpb+nNHXYlAa+n8IvQI+sRK/e833Om2zj4IaSSxIcxj0x6R9gi7\n" +
////            "Q4ZGYdV6KYs98oZY244QDo9ReI3Q0OF74IlRRtmmzz4Mqc++EckzWyYQ+l1SQn4b\n" +
////            "eelrwuYgb8/+BsafyDa51/in//iOLRDo83ifLbVCVJF4lz6RYoOx6pSkLUq+iLSI\n" +
////            "Iw9umK40bLhmbAXKlowr7JuRMKKnUKttS5uXfn1LUc3y+ZzYQuFNCbntLMyVvxwk\n" +
////            "XXXZXeznQYcUbekCaVyHWbzqZZhIYXT9UAimTlYJJDkCgYEA6WKNGT2JwglI6UNU\n" +
////            "BObua2i2jglwzXELEu7//S4xm3EaIkccdAt7mucZ6MN86hQ4QzDT2p3R64OzuAIU\n" +
////            "ycaEmUvFAOKPNeCswMALutIRrVKcwijz8VumssFCW8HPWkBBvdqSVlc8MfC+bAkS\n" +
////            "dj9MKyj8jpRDk+TKhdgY3/UXVjMCgYEApeGL2n3TRO4v5mLOVL7h4tmN56gsTiV9\n" +
////            "F35xP8cie8pK9EHnOGdzixTPOSIT83rEiTAQBxD2q2ANG3F3bk4Mpy8vEwXl5LXw\n" +
////            "BxpTycq5XXzM7Yf0S36MRFXPgxOghAC4XlOXW6twVThnbiQ87/iKCBsEzzuWl9Ej\n" +
////            "tUb/b0/pP1sCgYEAqxgJksBvdmVyIzetp65YiTuJ4ZUAMkw2LxoxZr1vtqHy48A4\n" +
////            "Fsa4RfY4I1D1CXHrUS7BkRpz43F41mQIM2gqm2tVsHFnRbtBSklAECSiuuq+awS0\n" +
////            "2XM0QQW3DSeHSIWk7HcJIuzu/zmbj/4Sn8eFonp4tFnzNL/czMSAb+bwxcsCgYAO\n" +
////            "mCd707Iw0Fv6gxrwLdMl6Eop5YvwXDADTyabm+OWzJeHofy7464wwGXy+uKSe2yr\n" +
////            "PUVLudxZ+5trnLILTGakluwnWk99rdwuY8yXZnSkmwRUqa9+rOWeZYZaW/G1a3LN\n" +
////            "KcGZWR4LAWloMVwDKPTL/cSQjweS192yZ0q1+mvlHQKBgQDWkTgE7ftDEmuTegOC\n" +
////            "rI/LFcjSQl30vxEy2TmjeOZqRgzDshy7ujNr7dCruz7In7snTRXMG+20+zTGK5dK\n" +
////            "IjIcXkNjX+/BYzv+qwU81a9q9l4q/aMtNmy6i/jEYbqTs7KcV+2S4WcvqCFzx4py\n" +
////            "JXhLG8uiEBd0OboQFVX0DGOkBg==";
//////    private static final String AGENT_PRIVATE_KEY = "-----BEGIN RSA PRIVATE KEY-----\n" +
//////            "MIIEpQIBAAKCAQEAlzokDvsXdgKrcLKHkZ4YnFKGTyfDi71U3BHDJLwhQ6OOxuCP\n" +
//////            "h+lyMdBZbp7bl8NgNGw7ugFesMaYhGbzxXH2MzGoejFapRel5PIFKj8zEV2iQGRX\n" +
//////            "cbecTH91VCpSygv4zy//vKUxEOAshBfoK7RqFdtTsqdmDcuiqFo6Ot+pKJC0UwZ8\n" +
//////            "rhUII6xkPHYMwFrPaVarHoJ3xQkIhJhRnzWSsJjY3QvoL7jU+TKn8CLOGupjI/fx\n" +
//////            "LzTxlyC4keWAvtDbz9Pvbhip5g5IVydMTDwfHpEY5vKg5A3RL4IcWLp0vZzPINjY\n" +
//////            "HXrQg+TQPPj0x/nwUCE4F98netOnVWfhDu0xIQIDAQABAoIBAQCRS2UVl50k3s/E\n" +
//////            "yWKDP7g5iPhN2NCB0/hSMCajhrUW/W/SGDMOy/XRcnJaW/pzR12JQGvp/CL0CPrE\n" +
//////            "Sv3vN9zpts4+CGkksSHMY9MekfYIu0OGRmHVeimLPfKGWNuOEA6PUXiN0NDhe+CJ\n" +
//////            "UUbZps8+DKnPvhHJM1smEPpdUkJ+G3npa8LmIG/P/gbGn8g2udf4p//4ji0Q6PN4\n" +
//////            "ny21QlSReJc+kWKDseqUpC1Kvoi0iCMPbpiuNGy4ZmwFypaMK+ybkTCip1CrbUub\n" +
//////            "l359S1HN8vmc2ELhTQm57SzMlb8cJF112V3s50GHFG3pAmlch1m86mWYSGF0/VAI\n" +
//////            "pk5WCSQ5AoGBAOlijRk9icIJSOlDVATm7mtoto4JcM1xCxLu//0uMZtxGiJHHHQL\n" +
//////            "e5rnGejDfOoUOEMw09qd0euDs7gCFMnGhJlLxQDijzXgrMDAC7rSEa1SnMIo8/Fb\n" +
//////            "prLBQlvBz1pAQb3aklZXPDHwvmwJEnY/TCso/I6UQ5PkyoXYGN/1F1YzAoGBAKXh\n" +
//////            "i9p900TuL+ZizlS+4eLZjeeoLE4lfRd+cT/HInvKSvRB5zhnc4sUzzkiE/N6xIkw\n" +
//////            "EAcQ9qtgDRtxd25ODKcvLxMF5eS18AcaU8nKuV18zO2H9Et+jERVz4MToIQAuF5T\n" +
//////            "l1urcFU4Z24kPO/4iggbBM87lpfRI7VG/29P6T9bAoGBAKsYCZLAb3ZlciM3raeu\n" +
//////            "WIk7ieGVADJMNi8aMWa9b7ah8uPAOBbGuEX2OCNQ9Qlx61EuwZEac+NxeNZkCDNo\n" +
//////            "KptrVbBxZ0W7QUpJQBAkorrqvmsEtNlzNEEFtw0nh0iFpOx3CSLs7v85m4/+Ep/H\n" +
//////            "haJ6eLRZ8zS/3MzEgG/m8MXLAoGADpgne9OyMNBb+oMa8C3TJehKKeWL8FwwA08m\n" +
//////            "m5vjlsyXh6H8u+OuMMBl8vrikntsqz1FS7ncWfuba5yyC0xmpJbsJ1pPfa3cLmPM\n" +
//////            "l2Z0pJsEVKmvfqzlnmWGWlvxtWtyzSnBmVkeCwFpaDFcAyj0y/3EkI8HktfdsmdK\n" +
//////            "tfpr5R0CgYEA1pE4BO37QxJrk3oDgqyPyxXI0kJd9L8RMtk5o3jmakYMw7Icu7oz\n" +
//////            "a+3Qq7s+yJ+7J00VzBvttPs0xiuXSiIyHF5DY1/vwWM7/qsFPNWvavZeKv2jLTZs\n" +
//////            "uov4xGG6k7OynFftkuFnL6ghc8eKciV4SxvLohAXdDm6EBVV9AxjpAY=\n" +
//////            "-----END RSA PRIVATE KEY-----";
////    // agent rsa public key
////    private static final String AGENT_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlzokDvsXdgKrcLKHkZ4YnFKGTyfDi71U3BHDJLwhQ6OOxuCPh+lyMdBZbp7bl8NgNGw7ugFesMaYhGbzxXH2MzGoejFapRel5PIFKj8zEV2iQGRXcbecTH91VCpSygv4zy//vKUxEOAshBfoK7RqFdtTsqdmDcuiqFo6Ot+pKJC0UwZ8rhUII6xkPHYMwFrPaVarHoJ3xQkIhJhRnzWSsJjY3QvoL7jU+TKn8CLOGupjI/fxLzTxlyC4keWAvtDbz9Pvbhip5g5IVydMTDwfHpEY5vKg5A3RL4IcWLp0vZzPINjYHXrQg+TQPPj0x/nwUCE4F98netOnVWfhDu0xIQIDAQAB";
////    // uqpay public key
////    private static final String UQPAY_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvaD5bmMlGmACHVVRaxVOwCvRf+8v2Ne8skg0OpV8c4A6vgI+PjPdRAMPtFo5LUKKTN2a2uCp0gjnWDVNPFAlIztzs02qm5hIRpwWUWmP6fYJcK/VTdo86jsoRZjG7qD+0hAjR8J4yK1gIXNgsSNeEHkzkMJwsiggkQP4dpS5QQwQyEGC8uc6teQf/2XghayXlchldMU6jIsLEZv7RoaacfXg/t4tMZ9yKP7NdnmJuWC2Ty73y9CHjCkxWbbax2Zd5NghmNL3BSGs0mdhTnP8HLXIlhJsVQ5mIwlePeks6AFS9huo0LwAjIUT9LpcbSXMWmYYObZapU0Foh4o2r/BQwIDAQAB";
////    // notify url
////    private static final String NOTIFY_URL = "https://gateway.aimt-horizon.com/uqpay/notify";
////
////    //https://paygate.uqpay.cn/query
////    //https://paygate.uqpay.cn/pay
//
//import com.wosai.mpay.api.ccb.BusinessFields;
//
//////    private static final String QUERY_URL = "https://paygate.uqpay.net/query";
////    private static final String QUERY_URL = "https://paygate.uqpay.com/query";
//    private static final String PAY_URL = "https://paygate.uqpay.net/pay";
////    //https://paygate.uqpay.net/cancel
////    private static final String CANCEL_URL = "https://paygate.uqpay.net/cancel";
////    //https://paygate.uqpay.net/refund
////    private static final String REFUND_URL = "https://paygate.uqpay.net/refund";
////
////
////
////    public static void main(String[] args) throws Exception {
//////        testPay();
//////        testC2b();
//////            testQuery();
//////        testRefund();
//////        testCancel();
//////        testSignVerify();
////        testApp();
//
//import com.wosai.mpay.api.ccb.BusinessFields;
//
//////        testCardPay();
//////        byte[] bytes = readFileAsBinaryArray("/Users/<USER>/Downloads/20250114-110729.jpeg");
//////        String s = QrcodeUtil.readQRCodeFromBase64(Base64.encode(bytes));
//////        System.out.println(s);
//////        Map<String, String> stringStringMap = DuitNowQRParser.parseQRCodeContent(s);
//////        System.out.println(stringStringMap);
//////        System.out.println(DuitNowQRParser.parseQRCodeContent("0010com.alipay0144https://pay.uqpay.net/v3/1006049/SGD?tid=123"));
//////        System.out.println(DuitNowQRParser.parseQRCodeContent("3701070200520446100000001006049"));
////    }
////
////    public static byte[] readFileAsBinaryArray(String filePath) throws IOException {
////        // 使用 Files.readAllBytes 读取文件为字节数组
////        return Files.readAllBytes(Paths.get(filePath));
////    }
////
////    // test card pay
////
//    public static void testCardPay() throws Exception {
//        UqpayClient client = new UqpayClient();
//        RequestBuilder builder = getCommonRequestBuilder(UqpayConstant.TRANSACTION_TYPE_PAY);
//        builder.set(BusinessFields.ORDER_ID, getOrderSn());
//        builder.set(BusinessFields.METHOD_ID, MethodIdConstant.UnionSecurePay);
//        builder.set(BusinessFields.MERCHANT_CITY, "singapore");
//        builder.set(BusinessFields.TERMINAL_ID, "1234");
//        builder.set(BusinessFields.AMOUNT, "0.01");
//        builder.set(BusinessFields.QUANTITY, "1");
//        builder.set(BusinessFields.TRANSACTION_NAME, "producttest");
//        builder.set(BusinessFields.CURRENCY, UqpayConstant.CURRENCY_SGD);
//        builder.set(BusinessFields.CALLBACK_URL, NOTIFY_URL);
//        builder.set(BusinessFields.CLIENT_IP, "*************");
////        builder.set(BusinessFields.RETURN_URL, "aimtapp://alipay"); //支付宝需要
//        builder.set(com.wosai.mpay.api.mcash.BusinessFields.RETURN_URL, "https://shouqianba.com/app/sqbmerchant/"); //支付宝需要
//        builder.set(ProtocolFields.CLIENT_TYPE, UqpayConstant.CLIENT_TYPE_IOS);
////        builder.set(BusinessFields.FIRST_NAME, "邬");
////        builder.set(BusinessFields.LAST_NAME, "建伟");
////        builder.set(BusinessFields.CARD_NUM, "****************");
////        builder.set(BusinessFields.CVV, "176");
////        builder.set(BusinessFields.EXPIRE_YEAR, "2026");
////        builder.set(BusinessFields.EXPIRE_MONTH, "08");
//
////
////        Map<String, Object> response = client.call(PAY_URL, AGENT_PRIVATE_KEY, builder.build());
////        System.out.println(JsonUtil.objectToJsonString(response));
////
////    }
////
////    // test app
////    public static void testApp() throws Exception {
////        UqpayClient client = new UqpayClient();
////        RequestBuilder builder = getCommonRequestBuilder(UqpayConstant.TRANSACTION_TYPE_PAY);
////        builder.set(BusinessFields.ORDER_ID, getOrderSn());
////        builder.set(BusinessFields.METHOD_ID, MethodIdConstant.Alipay_InAPP);
////        builder.set(BusinessFields.MERCHANT_CITY, "singapore");
////        builder.set(BusinessFields.TERMINAL_ID, "1234");
////        builder.set(BusinessFields.AMOUNT, "0.01");
////        builder.set(BusinessFields.QUANTITY, "1");
//////        builder.set(BusinessFields.CHANNEL_INFO, MapUtils.hashMap(
//////                BusinessFields.SUB_APPID, "wxa56c7420fd33309e"
//////        ));
//////        builder.set(BusinessFields.SUB_OPENID, "oPmqRs_T8gHTwfvTZyANuqjza1a0");
////        builder.set(BusinessFields.TRANSACTION_NAME, "producttest");
////        builder.set(BusinessFields.CURRENCY, UqpayConstant.CURRENCY_SGD);
////        builder.set(BusinessFields.CALLBACK_URL, NOTIFY_URL);
////        builder.set(BusinessFields.CLIENT_IP, "127.0.0.1");
////        builder.set(BusinessFields.RETURN_URL, "aimtapp://alipay"); //支付宝需要
//////        builder.set(BusinessFields.RETURN_URL, "https://shouqianba.com/app/sqbmerchant/"); //支付宝需要
////        builder.set(ProtocolFields.CLIENT_TYPE, UqpayConstant.CLIENT_TYPE_ANDROID);
////        Map<String, Object> response = client.call(PAY_URL, AGENT_PRIVATE_KEY, builder.build());
////        System.out.println(JsonUtil.objectToJsonString(response));
////    }
////
////    public static void testPay() throws Exception {
////        UqpayClient client = new UqpayClient();
////        RequestBuilder builder = getCommonRequestBuilder(UqpayConstant.TRANSACTION_TYPE_PAY);
////        builder.set(BusinessFields.ORDER_ID, getOrderSn());
////        builder.set(BusinessFields.METHOD_ID, MethodIdConstant.PayNowOfflineQR);
////        builder.set(BusinessFields.SCAN_TYPE, UqpayConstant.SCAN_TYPE_MERCHANT);
////        builder.set(BusinessFields.IDENTITY, "289863863138100000");
////        builder.set(BusinessFields.MERCHANT_CITY, "hangzhou");
////        builder.set(BusinessFields.TERMINAL_ID, "1234");
////        builder.set(BusinessFields.AMOUNT, "0.01");
////        builder.set(BusinessFields.QUANTITY, "1");
////        builder.set(BusinessFields.TRANSACTION_NAME, "producttest");
////        builder.set(BusinessFields.CURRENCY, UqpayConstant.CURRENCY_SGD);
////        builder.set(BusinessFields.CALLBACK_URL, NOTIFY_URL);
////        builder.set(BusinessFields.CLIENT_IP, "127.0.0.1");
////        Map<String, Object> response = client.call(PAY_URL, AGENT_PRIVATE_KEY, builder.build());
////        System.out.println(response);
////
////    }
////
////    public static void testC2b() throws Exception {
////        UqpayClient client = new UqpayClient();
////        RequestBuilder builder = getCommonRequestBuilder(UqpayConstant.TRANSACTION_TYPE_PAY);
////        builder.set(BusinessFields.ORDER_ID, getOrderSn());
////        builder.set(BusinessFields.METHOD_ID, MethodIdConstant.AlipayOfflineQR);
////        builder.set(BusinessFields.SCAN_TYPE, UqpayConstant.SCAN_TYPE_CONSUMER);
////        builder.set(BusinessFields.MERCHANT_CITY, "singapore");
////        builder.set(BusinessFields.TERMINAL_ID, "2234");
////        builder.set(BusinessFields.AMOUNT, "0.1");
////        builder.set(BusinessFields.QUANTITY, "1");
////        builder.set(BusinessFields.TRANSACTION_NAME, "product test");
////        builder.set(BusinessFields.CURRENCY, UqpayConstant.CURRENCY_SGD);
////        builder.set(BusinessFields.CALLBACK_URL, NOTIFY_URL);
////        builder.set(BusinessFields.CLIENT_IP, "127.0.0.1");
////        Map<String, Object> response = client.call(PAY_URL, AGENT_PRIVATE_KEY, builder.build());
////        System.out.println(response);
////
////    }
////
////    // test query
////    public static void testQuery() throws Exception {
////        UqpayClient client = new UqpayClient();
////        RequestBuilder builder = getCommonRequestBuilder(UqpayConstant.TRANSACTION_TYPE_QUERY);
////        builder.set(BusinessFields.ORDER_ID, "7894355726511772");
////        Map<String, Object> response = client.call(QUERY_URL, AGENT_PRIVATE_KEY, builder.build());
////        System.out.println(response);
////    }
////
////    // test refund
////    public static void testRefund() throws Exception {
////        UqpayClient client = new UqpayClient();
////        RequestBuilder builder = getCommonRequestBuilder(UqpayConstant.TRANSACTION_TYPE_REFUND);
////        builder.set(BusinessFields.ORDER_ID, "7894259299904970");
////        builder.set(BusinessFields.UQ_ORDER_ID, "0");
////        builder.set(BusinessFields.CALLBACK_URL, NOTIFY_URL);
////        builder.set(BusinessFields.AMOUNT, "0.01");
////        Map<String, Object> response = client.call(REFUND_URL, AGENT_PRIVATE_KEY, builder.build());
////        System.out.println(response);
////    }
////
////    // test refund
////    public static void testCancel() throws Exception {
////        UqpayClient client = new UqpayClient();
////        RequestBuilder builder = getCommonRequestBuilder(UqpayConstant.TRANSACTION_TYPE_CANCEL);
////        builder.set(BusinessFields.ORDER_ID, "7894259299904970");
////        builder.set(BusinessFields.UQ_ORDER_ID, "0");
////        builder.set(BusinessFields.CALLBACK_URL, NOTIFY_URL);
////        Map<String, Object> response = client.call(CANCEL_URL, AGENT_PRIVATE_KEY, builder.build());
////        System.out.println(response);
////    }
////
////    //get test orderSn
////    private static String getOrderSn() {
////        return "TEST" + System.currentTimeMillis();
////    }
////
////
////    // get common request builder
////    private static RequestBuilder getCommonRequestBuilder(String transType) {
////        RequestBuilder builder = new RequestBuilder();
////        builder.set(ProtocolFields.MERCHANT_ID, MERCHANT_ID);
////        builder.set(ProtocolFields.AGENT_ID, AGENT_ID);
////        builder.set(ProtocolFields.TRANSACTION_TYPE, transType);
////        builder.set(ProtocolFields.DATE, "" + System.currentTimeMillis());
////        builder.set(ProtocolFields.CLIENT_TYPE, UqpayConstant.CLIENT_TYPE_WEB);
////        return builder;
////    }
////    // test sign verify
////    public static void testSignVerify() throws Exception {
////        String response = "{\"date\":\"1735105490381\",\"amount\":\"0.1\",\"agentid\":\"1006048\",\"code\":\"10000\",\"orderid\":\"TEST1735105488443\",\"qrcode\":\"00000101021226500009SG.PAYNOW010120213201411660RXFR0301004142021041012022352040000530370254040.165802SG5914XFERS PTE. LTD6009Singapore62140110DONTSCANME63045205\",\"transtype\":\"pay\",\"sign\":\"riZmA6KpeOdPWDah0fNPsZGswjcBQ+yPOO15kOgKE5YGhEAOBE0ANwp41GH/lO4ZpY5uDJVc0iTBD2J5enfP8u7vDy+Fu//F9YV6JTe2VRvm0qPpl8kvUMjxDGyK2SH8TcuKg21hIP8MGS/2TPKOIODajygO7ZlMmo89kD5shK+nE9YunUolrYdapdqXb4gtJ2PF5vtbCWfMQvxl23LKAxO2sajUn+pu61QVshvu1stnAY5uvzrknnHeVArG/Gji9hqNpSr/kyZCApBZu1OwlS3XAYStVPy3JVAynbX6LkyMgUCN2Qr/O6j4KAMAdvbP2zzIqO4qqX9f2Z+5af6TgA==\",\"methodid\":\"1012\",\"signtype\":\"RSA\",\"message\":\"TransSuccessed\",\"scantype\":\"1\",\"channelinfo\":\"{\\\"city\\\":\\\"singapore\\\",\\\"terminalID\\\":\\\"1234\\\"}\",\"merchantid\":\"1006049\",\"uqorderid\":\"2290182079702784\",\"billamount\":\"0.1\",\"currency\":\"SGD\",\"extendinfo\":\"{\\\"terminalID\\\":\\\"1234\\\"}\",\"state\":\"Paying\",\"qrcodeurl\":\"https://paygate.uqpay.com/qr?data=00000101021226500009SG.PAYNOW010120213201411660RXFR0301004142021041012022352040000530370254040.165802SG5914XFERS+PTE.+LTD6009Singapore62140110DONTSCANME63045205\"}\n";
////        Map map = JsonUtil.jsonStringToObject(response, Map.class);
////        System.out.println(UqpayUtil.verifySign(map, (String) map.get("sign"), UQPAY_PUBLIC_KEY));
////
////    }
////
////}
