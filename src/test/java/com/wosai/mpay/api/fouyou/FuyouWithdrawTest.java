package com.wosai.mpay.api.fouyou;

import com.wosai.mpay.api.fuyou.*;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * 结算打款
 *
 * <AUTHOR>
 * @date 2024/9/12
 **/

public class FuyouWithdrawTest {

    private Logger logger = LoggerFactory.getLogger(FuyouWithdrawTest.class);


    @Test
    public void withdrawSettlement() throws Exception {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouProtocolFields.INS_CD, ins_cd);
        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, mchnt_cd);
        requestBuilder.set(FuyouBusinessFields.RESERVED_ORDER_NO, "202401091213002");
        requestBuilder.set(FuyouBusinessFields.SETTLE_AMT, 10);
        requestBuilder.set(FuyouBusinessFields.FEE_AMT, 0);
        requestBuilder.set(FuyouBusinessFields.TXN_TYPE, 2);
        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(ASYNC_WITHDRAW_SETTLEMENT, withdrawPrivateKey, requestBuilder.build(), FuyouConstants.METHOD_SETTLE);
        logger.info("result:{}", JsonUtil.toJsonStr(result));
    }

    @Test
    public void withdrawSettlementProd() throws Exception {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouProtocolFields.INS_CD, ins_cd);
        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, mchnt_cd);
        requestBuilder.set(FuyouBusinessFields.RESERVED_ORDER_NO, "202401091816001");
        requestBuilder.set(FuyouBusinessFields.SETTLE_AMT, 1);
        requestBuilder.set(FuyouBusinessFields.FEE_AMT, 0);
        requestBuilder.set(FuyouBusinessFields.TXN_TYPE, 2);
        requestBuilder.set(FuyouBusinessFields.RESERVED_NOTIFY_URL, "https://e6711de245724529a4599315a7069988.api.mockbin.io/");
        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(ASYNC_WITHDRAW_SETTLEMENT, withdrawPrivateKeyProd, requestBuilder.build(), FuyouConstants.METHOD_ASYNC_SETTLE);
        logger.info("result:{}", JsonUtil.toJsonStr(result));
    }

    @Test
    public void querySettlementProd() throws Exception {
        FuyouRequestBuilder requestBuilder = new FuyouRequestBuilder();
        requestBuilder.set(FuyouProtocolFields.INS_CD, ins_cd);
        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, mchnt_cd);
//        requestBuilder.set(FuyouBusinessFields.MCHNT_CD, "0003320F6723689");
        requestBuilder.set(FuyouBusinessFields.MCHNT_ORDER_NO, "202401091215002");
        requestBuilder.set(FuyouBusinessFields.RESERVED_ORDER_NO, "202401091215002");
        requestBuilder.set(FuyouBusinessFields.DATE, "20240912");
        requestBuilder.set(FuyouBusinessFields.START_INDEX, 1);
        requestBuilder.set(FuyouBusinessFields.END_INDEX, 10);
//        requestBuilder.set(FuyouBusinessFields.FY_TRACE_NO, "091820797670"); //040700999345
        requestBuilder.set(FuyouBusinessFields.WITHDRAW_TYPE, 1);
        //requestBuilder.set(FuyouBusinessFields.FY_TRACE_NO, "221428632168");
        FuyouClient fuyouClient = new FuyouClient();
        Map<String, Object> result = fuyouClient.call(QUERY_SETTLEMENT, withdrawPrivateKeyProd, requestBuilder.build(), FuyouConstants.METHOD_QUERY_SETTLEMENT);
        System.out.println(JsonUtil.toJsonStr(result));
    }


    private static final String WITHDRAW_SETTLEMENT = "https://scan-rim-mc.fuioupay.com/withdraw";

    private static final String QUERY_SETTLEMENT = "https://scan-rim-mc.fuioupay.com/querySettlement";

    private static final String ASYNC_WITHDRAW_SETTLEMENT = "https://scan-rim-mc.fuioupay.com/withdrawDF";

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter
            .ofPattern("yyyyMMddHHmmss");


    private static final String ins_cd = "08M0031385";
    private static final String mchnt_cd = "0003050F6573416";

    private static SafeSimpleDateFormat df = new SafeSimpleDateFormat("yyyyMMddHHmmss");

    //公钥
    private static final String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCBv9K+jiuHqXIehX81oyNSD2RfVn+KTPb7NRT5HDPFE35CjZJd7Fu40r0U2Cp7Eyhayv/mRS6ZqvBT/8tQqwpUExTQQBbdZjfk+efb9bF9a+uCnAg0RsuqxeJ2r/rRTsORzVLJy+4GKcv06/p6CcBc5BI1gqSKmyyNBlgfkxLYewIDAQAB";
    //私钥
    private static final String privateKey  = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJgAzD8fEvBHQTyxUEeK963mjziM\n" +
            "WG7nxpi+pDMdtWiakc6xVhhbaipLaHo4wVI92A2wr3ptGQ1/YsASEHm3m2wGOpT2vrb2Ln/S7lz1\n" +
            "ShjTKaT8U6rKgCdpQNHUuLhBQlpJer2mcYEzG/nGzcyalOCgXC/6CySiJCWJmPyR45bJAgMBAAEC\n" +
            "gYBHFfBvAKBBwIEQ2jeaDbKBIFcQcgoVa81jt5xgz178WXUg/awu3emLeBKXPh2i0YtN87hM/+J8\n" +
            "fnt3KbuMwMItCsTD72XFXLM4FgzJ4555CUCXBf5/tcKpS2xT8qV8QDr8oLKA18sQxWp8BMPrNp0e\n" +
            "pmwun/gwgxoyQrJUB5YgZQJBAOiVXHiTnc3KwvIkdOEPmlfePFnkD4zzcv2UwTlHWgCyM/L8SCAF\n" +
            "clXmSiJfKSZZS7o0kIeJJ6xe3Mf4/HSlhdMCQQCnTow+TnlEhDTPtWa+TUgzOys83Q/VLikqKmDz\n" +
            "kWJ7I12+WX6AbxxEHLD+THn0JGrlvzTEIZyCe0sjQy4LzQNzAkEAr2SjfVJkuGJlrNENSwPHMugm\n" +
            "vusbRwH3/38ET7udBdVdE6poga1Z0al+0njMwVypnNwy+eLWhkhrWmpLh3OjfQJAI3BV8JS6xzKh\n" +
            "5SVtn/3Kv19XJ0tEIUnn2lCjvLQdAixZnQpj61ydxie1rggRBQ/5vLSlvq3H8zOelNeUF1fT1QJA\n" +
            "DNo+tkHVXLY9H2kdWFoYTvuLexHAgrsnHxONOlSA5hcVLd1B3p9utOt3QeDf6x2i1lqhTH2w8gzj\n" +
            "vsnx13tWqg==";

    private static final String withdrawPrivateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJgAzD8fEvBHQTyxUEeK963mjziMWG7nxpi+pDMdtWiakc6xVhhbaipLaHo4wVI92A2wr3ptGQ1/YsASEHm3m2wGOpT2vrb2Ln/S7lz1ShjTKaT8U6rKgCdpQNHUuLhBQlpJer2mcYEzG/nGzcyalOCgXC/6CySiJCWJmPyR45bJAgMBAAECgYBHFfBvAKBBwIEQ2jeaDbKBIFcQcgoVa81jt5xgz178WXUg/awu3emLeBKXPh2i0YtN87hM/+J8fnt3KbuMwMItCsTD72XFXLM4FgzJ4555CUCXBf5/tcKpS2xT8qV8QDr8oLKA18sQxWp8BMPrNp0epmwun/gwgxoyQrJUB5YgZQJBAOiVXHiTnc3KwvIkdOEPmlfePFnkD4zzcv2UwTlHWgCyM/L8SCAFclXmSiJfKSZZS7o0kIeJJ6xe3Mf4/HSlhdMCQQCnTow+TnlEhDTPtWa+TUgzOys83Q/VLikqKmDzkWJ7I12+WX6AbxxEHLD+THn0JGrlvzTEIZyCe0sjQy4LzQNzAkEAr2SjfVJkuGJlrNENSwPHMugmvusbRwH3/38ET7udBdVdE6poga1Z0al+0njMwVypnNwy+eLWhkhrWmpLh3OjfQJAI3BV8JS6xzKh5SVtn/3Kv19XJ0tEIUnn2lCjvLQdAixZnQpj61ydxie1rggRBQ/5vLSlvq3H8zOelNeUF1fT1QJADNo+tkHVXLY9H2kdWFoYTvuLexHAgrsnHxONOlSA5hcVLd1B3p9utOt3QeDf6x2i1lqhTH2w8gzjvsnx13tWqg==";


    private static final String withdrawPrivateKeyProd = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAJ50Ks7GrtC4/pZoGQ0zgeqksI0kLGVLvgNZI/r7r4KSxOxjHD/aGAWQZf2S1t9Uktak5uAUGSlbGf9ygDC6jXO59bZQ2FTLZSNxTZQvvB1BcKx3da80lbOjU6+OTII65JfDpiobmPleFW9KxurfdOBWKkfBOzhwbdAamdyVEiVrAgMBAAECgYAjo/OBARWPIx04jX+dBYniUlrX1FwcCLZboHBEYtouX37lwME454BhuAII0D9+ilJg70RzAHJMaA5n88Q7saphE7QNo1tTkvgzdvK2gtTqrlm8npZsX8lVaVBLT/O3zMsPm7cSO2yKupDyCeItIkYcyXmaIU+l3Sdd0M+ZuE5e2QJBANHBaFlc2s529H8QzSDe6icAPHN9A2bgpjnZ631rl2rOOY7Jyhh1+KaRfj1NEQyKvDh75w4PU0WubUDOaxPhxo8CQQDBY0reTS21heywuk/ufESjimsczgUDApudgskdhVkL88RTWDvqf19qJzYR+F2gxe5JcCSYiPoLhMHcJjnzY8FlAkAZ0afJkC9yyJux5FyLlkTWRiYqxRGdITv01QkyqAK8Z+QX2mjg9+yCfFhArXLSy2T9wuqfBW3nnt/d4Uq26MyrAkA/hbT29lT4Vkhb+Kb+yDTnnzT13jpJNdcV0vrqxn1sbqLjWY08xp/5x218U5+1iq5t0vdCX2P62p2vCaNpbi/9AkB6ZJInSK5tPCFrVK21VDig9sEHqnJ7nlLW6HSKKucqsutR5su4cXHdtUV6RXW1mSXbZO4UwJHC4xhiS9dvpnGB";
}
