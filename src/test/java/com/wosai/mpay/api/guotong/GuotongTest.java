package com.wosai.mpay.api.guotong;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.google.common.collect.ImmutableList;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName: GuotongTest
 * @Description: 国通支付测试类
 * @Doc https://sqb.feishu.cn/wiki/AtVXwhlgAi6dEzklgDqconBjnsg
 * @date 2025/4/22
 */
public class GuotongTest {

    //    /**
//     * 测试用
//     */
//    // 测试用的机构号
//    private static final String TEST_AGENT_ID = "61000000339277";
//    private static final String TEST_SERVICE_URL = "http://jghwucyshtgjcbrnxgwe.gtxygroup.com/testterpay";
//    // 测试用的公钥
//    // 测试用的商户号
//    private static final String TEST_CUST_ID = "61000000526963";
//    // 生产用的机构号
//    private static final String PROD_SERVICE_URL = "http://jghwucyshtgjcbrnxgwe.gtxygroup.com/testterpay";
//
//
//    //
    private static final String SERVICE_URL = "https://yyfsvxm.postar.cn";

    private static final String AGENT_ID = "61000000526963";

    private static final String CUST_ID = "60000010259993";

    private static final String PUBLIC_KEY = "";

    /**
     * 测试统一下单接口（C扫B）
     */
    public static void testBarcodePayByWexin() throws MpayException, MpayApiNetworkError {
        String url = SERVICE_URL + "/yyfsevr/order/pay";
        // 1. 微信支付必传参数测试
        GuotongRequestBuilder wxBuilder = new GuotongRequestBuilder();
        // 设置基本必传参数
        wxBuilder.set(GuotongProtocolFields.AGET_ID, AGENT_ID); // 机构号
        wxBuilder.set(GuotongProtocolFields.CUST_ID, CUST_ID); // 商户号
        wxBuilder.set(GuotongProtocolFields.ORDER_NO, "sqb_wx_" + LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd") + "002"); // 订单号，需唯一
        wxBuilder.set(GuotongProtocolFields.TXAMT, "1"); // 交易金额，单位分
        wxBuilder.set(GuotongBusinessFields.OPENID, "ogDZBwDFIupUsvdSu06OMFIrFW54"); // 微信用户标识
        wxBuilder.set(GuotongBusinessFields.PAY_WAY, GuotongConstants.PAY_WAY_WECHAT); // 支付方式：1.微信支付（小程序）
        wxBuilder.set(GuotongBusinessFields.IP, "127.0.0.1"); // IP地址
        wxBuilder.set(GuotongBusinessFields.TITLE, "微信测试商品"); // 商品标题
        wxBuilder.set(GuotongBusinessFields.WX_APPID, "wxccbcac9a3ece5112"); // 微信支付必传
        wxBuilder.set(GuotongBusinessFields.TRA_TYPE, "8"); // 微信支付必传，8小程序
        wxBuilder.set(GuotongBusinessFields.TIME_STAMP, LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss"));
        wxBuilder.set(GuotongBusinessFields.VERSION, GuotongConstants.DEFAULT_VERSION);
        wxBuilder.set(GuotongBusinessFields.GOODS_DETAIL,
                ImmutableList.of(MapUtil.hashMap(
                        "goods_id", "1001",
                        "goods_name", "商品销售",
                        "quantity", 1,
                        "price", 1
                ))
        ); // 微信支付必传
        // 创建客户端并发送请求
        GuotongClient client = new GuotongClient();
        Map<String, Object> wxResult = client.call(url, wxBuilder.build(), PUBLIC_KEY);
        System.out.println("微信支付必传参数测试结果：" + JsonUtil.objectToJsonString(wxResult));
    }

    public static void testBarcodePayByAlipay() throws MpayException, MpayApiNetworkError {
        String url = SERVICE_URL + "/yyfsevr/order/pay";
        // 2. 支付宝支付必传参数测试
        GuotongRequestBuilder aliBuilder = new GuotongRequestBuilder();
        // 设置基本必传参数
        aliBuilder.set(GuotongProtocolFields.AGET_ID, AGENT_ID); // 机构号
        aliBuilder.set(GuotongProtocolFields.CUST_ID, CUST_ID); // 商户号
        aliBuilder.set(GuotongProtocolFields.ORDER_NO, "sqb_ali_" + LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd") + "003"); // 订单号，需唯一
        aliBuilder.set(GuotongProtocolFields.TXAMT, "1"); // 交易金额，单位分
        aliBuilder.set(GuotongBusinessFields.OPENID, "2088270374598900"); // 支付宝用户ID（以2088开头）
        aliBuilder.set(GuotongBusinessFields.PAY_WAY, GuotongConstants.PAY_WAY_ALIPAY); // 支付方式：2.支付宝支付
        aliBuilder.set(GuotongBusinessFields.IP, "127.0.0.1"); // IP地址
        aliBuilder.set(GuotongBusinessFields.TITLE, "支付宝测试商品"); // 商品标题
        //aliBuilder.set(GuotongBusinessFields.ZFB_APPID, "2088011691288213"); // 支付宝支付必传
        // 创建客户端并发送请求
        GuotongClient client = new GuotongClient();
        // 创建客户端并发送请求
        Map<String, Object> aliResult = client.call(url, aliBuilder.build(), PUBLIC_KEY);
        System.out.println("支付宝支付必传参数测试结果：" + JsonUtil.objectToJsonString(aliResult));
    }

    public static void testBarcodePayByUnion() throws MpayException, MpayApiNetworkError {
        String url = SERVICE_URL + "/yyfsevr/order/pay";
        // 3. 银联支付必传参数测试
        GuotongRequestBuilder unionBuilder = new GuotongRequestBuilder();
        // 设置基本必传参数
        unionBuilder.set(GuotongProtocolFields.AGET_ID, AGENT_ID); // 机构号
        unionBuilder.set(GuotongProtocolFields.CUST_ID, CUST_ID); // 商户号
        unionBuilder.set(GuotongProtocolFields.ORDER_NO, "sqb_union_" + LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd") + "001"); // 订单号，需唯一
        unionBuilder.set(GuotongProtocolFields.TXAMT, "1"); // 交易金额，单位分
        unionBuilder.set(GuotongBusinessFields.OPENID, "85731015995U01V"); // 银联用户标识
        unionBuilder.set(GuotongBusinessFields.PAY_WAY, GuotongConstants.PAY_WAY_UNION); // 支付方式：3.银联支付
        unionBuilder.set(GuotongBusinessFields.IP, "127.0.0.1"); // IP地址
        unionBuilder.set(GuotongBusinessFields.TITLE, "银联测试商品"); // 商品标题
//        unionBuilder.set(GuotongBusinessFields.QR_CODE, "https://qr.95516.com/00010000/***********"); // 银联支付必传
//        unionBuilder.set(GuotongBusinessFields.QR_CODE_TYPE, "0"); // 银联支付，收款二维码为动态码时，则上送值为0

        // 创建客户端并发送请求
        Map<String, Object> unionResult = new GuotongClient().call(url, unionBuilder.build(), PUBLIC_KEY);
        System.out.println("银联支付必传参数测试结果：" + JsonUtil.objectToJsonString(unionResult));
    }

    /**
     * 测试商户主扫（B扫C）
     * 根据商户主扫（B扫C）－请求参数文档实现
     */
    public static void testMerchantBarcodePay() throws MpayException, MpayApiNetworkError {
        String url = SERVICE_URL + "/yyfsevr/order/scanByMerchant";

        // 创建请求构建器
        GuotongRequestBuilder builder = new GuotongRequestBuilder();

        // 设置基本必传参数
        builder.set(GuotongProtocolFields.AGET_ID, AGENT_ID); // 商户所在国通系统内机构号（或虚拟机构号）
        builder.set(GuotongProtocolFields.CUST_ID, CUST_ID); // 商户所在国通系统内商户号
        builder.set(GuotongProtocolFields.ORDER_NO, "sqb" + LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd") + "002"); // 订单号，需唯一
        builder.set(GuotongProtocolFields.TXAMT, "1"); // 交易金额，单位分
        builder.set(GuotongBusinessFields.CODE, "130184777362809760"); // 设备读取用户微信或支付宝中的条码或者二维码信息（付款码）

        // 设置可选参数
        builder.set(GuotongBusinessFields.TRADING_IP, "127.0.0.1"); // 商户端终端设备 IP 地址
        builder.set(GuotongBusinessFields.TYPE, GuotongConstants.TYPE_PC); // 设备类型，C-PC端
        builder.set(GuotongBusinessFields.DRIVE_NO, "12345678"); // 字母或数字
        builder.set(GuotongBusinessFields.IS_POP, GuotongConstants.YES); // 1-是，其他为否
        builder.set(GuotongBusinessFields.REMARK, "测试商户主扫"); // 备注
        builder.set(GuotongBusinessFields.TITLE, "测试商品"); // 商品标题
        builder.set(GuotongBusinessFields.LATITUDE, "119.411869"); // 纬度
        builder.set(GuotongBusinessFields.LONGITUDE, "119.411869"); // 经度
        builder.set(GuotongBusinessFields.OPERATOR, "test_operator"); // 店员标识
        //builder.set(GuotongBusinessFields.OUT_TIME, "10"); // 超时时间，单位分钟
        builder.set(GuotongBusinessFields.LIMIT_PAY, GuotongConstants.LIMIT_PAY_NO_LIMIT); // 0-无限制，1-不能使用信用卡
        builder.set(GuotongBusinessFields.TIME_STAMP, LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss")); // 时间戳
        builder.set(GuotongBusinessFields.VERSION, GuotongConstants.DEFAULT_VERSION); // 版本号

        // 创建客户端并发送请求
        GuotongClient client = new GuotongClient();
        Map<String, Object> result = client.call(url, builder.build(), PUBLIC_KEY);

        // 打印结果
        System.out.println("商户主扫（B扫C）测试结果：" + JsonUtil.objectToJsonString(result));
    }

    /**
     * 测试交易查询接口（全部）
     * 根据交易查询接口（全部）－请求参数文档实现
     */
    public static void testTransactionQuery() throws MpayException, MpayApiNetworkError {
        String url = SERVICE_URL + "/yyfsevr/order/orderQuery";

        // 创建请求构建器
        GuotongRequestBuilder builder = new GuotongRequestBuilder();

        // 设置基本参数 - 服务商订单号与国通订单号二选一
        builder.set(GuotongProtocolFields.ORDER_NO, "sqb20250423001"); // 服务商订单号
        // builder.set(GuotongBusinessFields.GT_ORDER_NO, "gt_order_no"); // 国通订单号

        // 设置必传参数
        builder.set(GuotongProtocolFields.AGET_ID, AGENT_ID); // 商户所在国通系统内机构号（或虚拟机构号）
        builder.set(GuotongProtocolFields.CUST_ID, CUST_ID); // 商户所在国通系统内商户号

        // 设置可选参数
        builder.set(GuotongBusinessFields.ORDER_TIME, LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd")); // 格式yyyyMMdd
        builder.set(GuotongBusinessFields.TIME_STAMP, LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss")); // 格式yyyyMMddHHmmss
        builder.set(GuotongBusinessFields.VERSION, GuotongConstants.DEFAULT_VERSION); // 默认1.0.0

        // 创建客户端并发送请求
        GuotongClient client = new GuotongClient();
        Map<String, Object> result = client.call(url, builder.build(), PUBLIC_KEY);

        // 打印结果
        System.out.println("交易查询接口（全部）测试结果：" + JsonUtil.objectToJsonString(result));
    }

    /**
     * 测试退款接口
     * 根据退款接口-请求参数文档实现
     */
    public static void testRefundTransaction() throws MpayException, MpayApiNetworkError {
        String url = SERVICE_URL + "/yyfsevr/order/refund";

        // 创建请求构建器
        GuotongRequestBuilder builder = new GuotongRequestBuilder();

        // 设置基本参数 - 三选一
        builder.set(GuotongProtocolFields.OLD_TORDER_NO, "sqb20250423001"); // 第三方退款订单号，需唯一
        builder.set(GuotongBusinessFields.ORDER_NO, "refund_" + LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd") + "001"); // 第三方退款订单号，需唯一

        // 设置必传参数
        builder.set(GuotongProtocolFields.AGET_ID, AGENT_ID); // 商户所在国通系统内机构号（或虚拟机构号）
        builder.set(GuotongProtocolFields.CUST_ID, CUST_ID); // 商户所在国通系统内商户号
        builder.set(GuotongBusinessFields.TAG, GuotongConstants.TAG_WECHAT); // 商户所在国通系统内商户号
        builder.set(GuotongBusinessFields.REFUND_AMOUNT, "1"); // 商户所在国通系统内商户号

        builder.set(GuotongBusinessFields.TIME_STAMP, LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss")); // 时间戳
        builder.set(GuotongBusinessFields.VERSION, GuotongConstants.DEFAULT_VERSION); // 版本号

        // 创建客户端并发送请求
        GuotongClient client = new GuotongClient();
        Map<String, Object> result = client.call(url, builder.build(), PUBLIC_KEY);

        // 打印结果
        System.out.println("退款接口测试结果：" + JsonUtil.objectToJsonString(result));
    }

    /**
     * 测试退款查询
     * 根据退款查询-请求参数文档实现
     */
    public static void testRefundQuery() throws MpayException, MpayApiNetworkError {
        String url = SERVICE_URL + "/yyfsevr/order/refundQuery";

        // 创建请求构建器
        GuotongRequestBuilder builder = new GuotongRequestBuilder();

        // 设置必传参数
        builder.set(GuotongProtocolFields.ORDER_NO, "refund_20250423001"); // 服务商退款的时候上送的退款订单号
        builder.set(GuotongProtocolFields.AGET_ID, AGENT_ID); // 商户所在国通系统内机构号（或虚拟机构号）
        builder.set(GuotongProtocolFields.CUST_ID, CUST_ID); // 商户所在国通系统内商户号

        // 设置可选参数
        //builder.set(GuotongBusinessFields.ORDER_TYPE, "00"); // 00普通，01收银调终端，未传时默认00普通
        builder.set(GuotongBusinessFields.ORIGIN_TRADE_DATE, LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd")); // 格式yyyyMMdd，支持60天查询，不传默认查询当天
        builder.set(GuotongBusinessFields.TIME_STAMP, LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss")); // 时间戳
        builder.set(GuotongBusinessFields.VERSION, GuotongConstants.DEFAULT_VERSION); // 版本号

        // 创建客户端并发送请求
        GuotongClient client = new GuotongClient();
        Map<String, Object> result = client.call(url, builder.build(), PUBLIC_KEY);

        // 打印结果
        System.out.println("退款查询测试结果：" + JsonUtil.objectToJsonString(result));
    }

    /**
     * 主函数
     */
    public static void main(String[] args) throws Exception {

//        testBarcodePay();
        testBarcodePayByWexin();
//        testBarcodePayByAlipay();

//        testBarcodePayByUnion();
        // 测试主扫支付（B扫C）
//        testMerchantBarcodePay();


        // 测试交易查询接口（全部）
//        testTransactionQuery();

        // 测试退款接口
//        testRefundTransaction();

        // 测试退款查询
//         testRefundQuery();

        System.out.println("测试完成");
    }


}