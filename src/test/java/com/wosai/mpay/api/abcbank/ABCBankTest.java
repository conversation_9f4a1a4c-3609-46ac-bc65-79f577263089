package com.wosai.mpay.api.abcbank;

import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.wosai.mpay.api.abcbank.ABCBankBusinessFields.*;
import static com.wosai.mpay.api.abcbank.ABCBankProtocolFields.*;

/**
 * <AUTHOR>
 * @description 农业银行-测试类
 * @date 2024/9/2
 */
public class ABCBankTest {
    public static final Logger logger = LoggerFactory.getLogger(ABCBankTest.class);

    public ABCBankTest() {
    }

    private static final SafeSimpleDateFormat dateSimpleFormat = new SafeSimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    //农业银行公钥
    private static final String abcBankPublicKey = "";
    //收钱吧私钥
    private static final String privateKey = "";
    //收钱吧公钥
    private static final String publicKey = "";

    //测试环境访问地址
    private static final String BASE_URL = "https://obgateway.test.abchina.com/AraratGateWay/openabc/api/bmp/";
    private static final String PAY_URL = BASE_URL + "anonymousscannedconsumerequest/v1";
    private static final String PAY_QUERY_URL = BASE_URL + "qryorderstate/v1";
    private static final String WECHAT_PREPAY_URL = BASE_URL + "anonymouswechatorder/v1";
    private static final String ALIPAY_PREPAY_URL = BASE_URL + "anonymousalipayorder/v1";
    private static final String REFUND_URL = BASE_URL + "refundrequest/v1";
    private static final String REFUND_QUERY_URL = BASE_URL + "refundquery/v1";

    private static final String appId = "e69dd9d6-9516-48a5-8f8b-e5086c6bb9df";//
    private static final String appSecret = ""; //AES对称加密的密钥
    private static final String providerMchId = "709080000000653";//农行商户编号
    private static final String idServiceProvider = "e69dd9d6-9516-48a5-8f8b-e5086c6bb9df21414";//开放银行服务商ID
    //private static final String terminalId = "SHyQgEGJ";//农行终端设备号(支付宝和云闪付）
    private static final String terminalId = "FE5XM8PV";//农行终端设备号（微信）
    private static final String deviceType = "11";//农行设备类型，固定传11，表示：条码支付辅助受理终端；

    private static final String orderSn = "***************";
    private static final String ipv6 = "fe80:0000:0001:0000:0440:44ff:1233:5678";

    static private final ABCBankClient abcBankClient = new ABCBankClient();

    /**
     * b2c支付
     *
     * @throws Exception
     */
    private static void b2cPay() throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put(MCH_TRADENO, orderSn);
        params.put(TOTAL_AMOUNT, "0.01");
        params.put(GOODS_NAME, "测试subject");
        params.put(TIME_EXPIRE, 1);
        params.put(AUTH_CODE, "280449423346211784");
        params.put(DEVICE_TYPE, deviceType);
        params.put(TERMINAL_ID, terminalId);

        params.put(TERMINAL_IP, ipv6);
        params.put(SUB_APPID, "wx72534f3638c59073");//上送收钱吧的微信appId

        ABCBankRequestBuilder requestBuilder = buildDefaultRequest(params);
        System.out.println("入参: " + JsonUtil.objectToJsonString(requestBuilder.build()));
        Map<String, Object> result = abcBankClient.call(PAY_URL, requestBuilder.build(), appSecret, privateKey, abcBankPublicKey);
        System.out.println("响应: " + JsonUtil.objectToJsonString(result));
    }

    /**
     * 微信预下单
     *
     * @throws Exception
     */
    private static void wechatPrepay() throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put(MCH_TRADENO, orderSn);
        params.put(TOTAL_AMOUNT, "0.01");
        params.put(NOTIFY_URL, "");//回调通知地址
        params.put(GOODS_NAME, "测试subject-微信预下单");
        params.put(SUB_APPID, "");
        params.put(SUB_OPENID, "");
        params.put(WX_API_TYPE, ABCBankTradeTypeEnum.JSAPI.getType());
        params.put(SPBILL_CREATE_IP, ipv6);
        params.put(PAY_ACTV_TIME, 4);
        params.put(DEVICE_TYPE, deviceType);
        params.put(TERMINAL_ID, terminalId);
        params.put(TERMINAL_IP, ipv6);

        ABCBankRequestBuilder requestBuilder = buildDefaultRequest(params);
        System.out.println("入参: " + JsonUtil.objectToJsonString(requestBuilder.build()));
        Map<String, Object> result = abcBankClient.call(WECHAT_PREPAY_URL, requestBuilder.build(), appSecret, privateKey, abcBankPublicKey);
        System.out.println("响应: " + JsonUtil.objectToJsonString(result));
    }

    /**
     * 支付宝预下单
     *
     * @throws Exception
     */
    private static void aliPrepay() throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put(MCH_TRADENO, orderSn);
        params.put(TOTAL_AMOUNT, "0.01");
        params.put(NOTIFY_URL, "");//回调通知地址
        params.put(GOODS_NAME, "测试subject-支付宝预下单");
        params.put(ALIPAY_APP_ID, "****************");
        params.put(ALIPAY_BUYER_ID, "");
        params.put(PAY_ACTV_TIME, 4);
        params.put(DEVICE_TYPE, deviceType);
        params.put(TERMINAL_ID, terminalId);
        params.put(TERMINAL_IP, ipv6);

        ABCBankRequestBuilder requestBuilder = buildDefaultRequest(params);
        System.out.println("入参: " + JsonUtil.objectToJsonString(requestBuilder.build()));
        Map<String, Object> result = abcBankClient.call(ALIPAY_PREPAY_URL, requestBuilder.build(), appSecret, privateKey, abcBankPublicKey);
        System.out.println("响应: " + JsonUtil.objectToJsonString(result));
    }

    /**
     * 支付查询
     *
     * @throws Exception
     */
    private static void payQuery() throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put(MCH_TRADENO, orderSn);

        ABCBankRequestBuilder requestBuilder = buildDefaultRequest(params);
        System.out.println("入参: " + JsonUtil.objectToJsonString(requestBuilder.build()));
        Map<String, Object> result = abcBankClient.call(PAY_QUERY_URL, requestBuilder.build(), appSecret, privateKey, abcBankPublicKey);
        System.out.println("响应: " + JsonUtil.objectToJsonString(result));
    }

    private static ABCBankRequestBuilder buildDefaultRequest(Map<String, Object> params) {
        Map<String, Object> bizParams = new HashMap<>(params);
        bizParams.put(MERCHANT_ID, providerMchId);
        bizParams.put(TERMINAL_TRANSFORM_FLAG, DEFAULT_TERMINAL_TRANSFORM_FLAG);
        //bizParams.put(ID_SERVICE_PROVIDER, idServiceProvider);//可以不上送该字段。不上送的话，由开放银行自动填

        Map<String, Object> data = new HashMap<>();
        data.put(DATA, bizParams);

        ABCBankRequestBuilder requestBuilder = new ABCBankRequestBuilder();
        String nonce = UUID.randomUUID().toString().replace("-", "");
        requestBuilder.set(ABCBankBusinessFields.APP_ID, appId);
        requestBuilder.set(ABCBankProtocolFields.BIZ_DATA, data);
        requestBuilder.set(ABCBankProtocolFields.SIGN_TYPE, DEFAULT_SIGN_TYPE);
        requestBuilder.set(ABCBankProtocolFields.ENCRYPT_TYPE, DEFAULT_ENCRYPT_TYPE);
        requestBuilder.set(ABCBankProtocolFields.TIMESTAMP, dateSimpleFormat.format(new Date()));
        requestBuilder.set(ABCBankProtocolFields.NONCE, nonce);
        return requestBuilder;
    }

    /**
     * 退款
     *
     * @throws Exception
     */
    private static void refund() throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put(REFUND_AMOUNT, "0.01");
        params.put(MCH_TRADENO, orderSn);
        params.put(MCH_REFUNDNO, "00" + orderSn);

        ABCBankRequestBuilder requestBuilder = buildDefaultRequest(params);
        System.out.println("入参: " + JsonUtil.objectToJsonString(requestBuilder.build()));
        Map<String, Object> result = abcBankClient.call(REFUND_URL, requestBuilder.build(), appSecret, privateKey, abcBankPublicKey);
        System.out.println("响应: " + JsonUtil.objectToJsonString(result));
    }

    /**
     * 退款查询
     *
     * @throws Exception
     */
    private static void refundQuery() throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put(MCH_REFUNDNO, "00" + orderSn);

        ABCBankRequestBuilder requestBuilder = buildDefaultRequest(params);
        System.out.println("入参: " + JsonUtil.objectToJsonString(requestBuilder.build()));
        Map<String, Object> result = abcBankClient.call(REFUND_QUERY_URL, requestBuilder.build(), appSecret, privateKey, abcBankPublicKey);
        System.out.println("响应: " + JsonUtil.objectToJsonString(result));
    }

    public static void main(String[] args) throws Exception {
        try {
            b2cPay();
            //payQuery();
            //wechatPrepay();
            //aliPrepay();
            //refund();
            //refundQuery();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
