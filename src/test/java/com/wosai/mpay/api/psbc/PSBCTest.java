package com.wosai.mpay.api.psbc;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

public class PSBCTest {

    public static final Logger logger = LoggerFactory.getLogger(PSBCTest.class);
    public static void main(String[] args) throws MpayApiNetworkError, MpayException {
//
//        b2c();
//
        query();

//        refund();
//        wxprecreate();

//        zfbprecreate();

//        refundQuery();

//
//        notify1();


    }

    public static void b2c() throws MpayApiNetworkError, MpayException {

        PSBCRequestBuilder requestBuilder = new PSBCRequestBuilder();
        SafeSimpleDateFormat dateFormat = new SafeSimpleDateFormat(PSBCConstant.YYYYMMDDHHMMSS);
        //应用appid
        String merchantId = "tradeGroupPre001";
        //应用Id
        String appID = "1095757516090363904001";

        String platformID = "300370100000013";

        String mchtNo = "100370100000697";

        String orderSn = "789435572641354589";

        String serialNo = SerialNoUtil.getSerialNo();


        String sopPublicKey = "";
        //商户私钥
        String privateKey = "";
        //商户公钥
        String publicKey = "";

        String url = "http://wap.dev.psbc.com/sop-h5/biz_pre/unionpay/tradeGroupPre001.htm?partnerTxSriNo=";

        // b2c
        String date = dateFormat.format(new Date());
        requestBuilder.setHead(PSBCRequestFields.VERSION, PSBCConstant.VERSION);
        requestBuilder.setHead(PSBCRequestFields.ACCESS_TYPE, PSBCConstant.API);
        requestBuilder.setHead(PSBCRequestFields.MERCHANT_ID, merchantId);
        requestBuilder.setHead(PSBCRequestFields.APP_ID, appID);
        requestBuilder.setHead(PSBCRequestFields.REQ_TIME, date);
        requestBuilder.setHead(PSBCRequestFields.METHOD, "unionpay.orderPay");

        Map<String, Object> data = new HashMap<>();
        // 公共字段
        data.put(PSBCRequestFields.VERSION, PSBCConstant.INTERFACE_VERSION);
        data.put(PSBCRequestFields.TXN_CODE, PSBCConstant.SCANNED_PAY_CODE);
        data.put(PSBCRequestFields.CHANNEL_ID, PSBCConstant.CHANNEL_ID);
        data.put(PSBCRequestFields.PLATFORM_ID, platformID);
        data.put(PSBCRequestFields.REQ_TRACE_ID, orderSn);
        data.put(PSBCRequestFields.REQ_DATE, dateFormat.format(new Date()));
        data.put(PSBCRequestFields.REQ_RESERVED, "");
        // 业务字段
        data.put(PSBCRequestFields.MCHT_NO, mchtNo);
        data.put(PSBCRequestFields.TXN_AMT, "0.01");
        data.put(PSBCRequestFields.CURRENCY_CODE, PSBCConstant.CNY);
        data.put(PSBCRequestFields.QR_CODE, "131355100809750572");
        data.put(PSBCRequestFields.WHETHER_NOTIFY, PSBCConstant.NOTIFY_FLAG);
        data.put(PSBCRequestFields.BACK_URL, "www.baidu.com");

        Map<String, String> orderData = new HashMap<>();
        orderData.put(PSBCRequestFields.ORDER_FLAG, PSBCConstant.ORDER_FLAG_0);
        orderData.put(PSBCRequestFields.ORDER_TITLE, "测试");
        orderData.put(PSBCRequestFields.ORDER_AMT, "0.01");
        data.put(PSBCRequestFields.ORDER_DATA, orderData);
        Map<String, String> terminalData = new HashMap<>();
        terminalData.put(PSBCRequestFields.DEVICE_TYPE, "11");
        terminalData.put(PSBCRequestFields.DEVICE_ID, "te961546");
        String terminalInfo = "";
        try {
            terminalInfo = JsonUtil.objectToJsonString(terminalData);
        } catch (Exception e) {
            logger.error("terminalInfo转换json失败", e);
        }
        data.put(PSBCRequestFields.TERM_INFO, terminalInfo);
        data.put(PSBCRequestFields.TERM_ID, "te961546");
        requestBuilder.setBody(PSBCRequestFields.DATA, data);
        requestBuilder.setBody(PSBCRequestFields.BUSI_MAIN_ID, serialNo);
        requestBuilder.setBody(PSBCRequestFields.REQ_TRANS_TIME, dateFormat.format(new Date()));
        requestBuilder.setHead(PSBCRequestFields.PARTNER_TX_SRI_NO, serialNo);

        PSBCClient psbcClient = new PSBCClient();
        Map post = psbcClient.call(requestBuilder.build(), url + serialNo, merchantId, sopPublicKey, publicKey, privateKey, "post");
        System.out.println(post);
    }


    public static void query() throws MpayApiNetworkError {
        PSBCRequestBuilder requestBuilder = new PSBCRequestBuilder();
        SafeSimpleDateFormat dateFormat = new SafeSimpleDateFormat(PSBCConstant.YYYYMMDDHHMMSS);
        //应用appid
        String merchantId = "tradeGroupPre001";
        //应用Id
        String appID = "1095757516090363904001";

        String platformID = "300370100000013";

        String mchtNo = "100370100000697";

        String orderSn = "789435572641354589";

        String serialNo = SerialNoUtil.getSerialNo();

        String sopPublicKey = "";
        //商户私钥
        String privateKey = "";
        //商户公钥
        String publicKey = "";

        String url = "http://wap.dev.psbc.com/sop-h5/biz_pre/unionpay/tradeGroupPre001.htm?partnerTxSriNo=";
        // b2c
        String date = dateFormat.format(new Date());
        requestBuilder.setHead(PSBCRequestFields.VERSION, PSBCConstant.VERSION);
        requestBuilder.setHead(PSBCRequestFields.ACCESS_TYPE, PSBCConstant.API);
        requestBuilder.setHead(PSBCRequestFields.MERCHANT_ID, merchantId);
        requestBuilder.setHead(PSBCRequestFields.APP_ID, appID);
        requestBuilder.setHead(PSBCRequestFields.REQ_TIME, date);
        requestBuilder.setHead(PSBCRequestFields.METHOD, "unionpay.orderQuery");

        Map<String, Object> data = new HashMap<>();
        // 公共字段
        data.put(PSBCRequestFields.VERSION, PSBCConstant.INTERFACE_VERSION);
        data.put(PSBCRequestFields.TXN_CODE, PSBCConstant.PAYMENT_QUERY_CODE);
        data.put(PSBCRequestFields.CHANNEL_ID, PSBCConstant.CHANNEL_ID);
        data.put(PSBCRequestFields.PLATFORM_ID, platformID);
        data.put(PSBCRequestFields.REQ_TRACE_ID, orderSn);
        data.put(PSBCRequestFields.REQ_DATE, dateFormat.format(new Date()));
        data.put(PSBCRequestFields.REQ_RESERVED, "");
        // 业务字段
        data.put(PSBCRequestFields.MCHT_NO, mchtNo);
        data.put(PSBCRequestFields.ORG_REQ_TRACE_ID, orderSn);

        requestBuilder.setBody(PSBCRequestFields.DATA, data);
        requestBuilder.setBody(PSBCRequestFields.BUSI_MAIN_ID, serialNo);
        requestBuilder.setBody(PSBCRequestFields.REQ_TRANS_TIME, dateFormat.format(new Date()));
        requestBuilder.setHead(PSBCRequestFields.PARTNER_TX_SRI_NO, serialNo);

        PSBCClient psbcClient = new PSBCClient();
        Map post = psbcClient.call(requestBuilder.build(), url + serialNo, merchantId, sopPublicKey, publicKey, privateKey, "post");
        System.out.println(post);

    }

    public static void refund() throws MpayApiNetworkError {
        PSBCRequestBuilder requestBuilder = new PSBCRequestBuilder();
        SafeSimpleDateFormat dateFormat = new SafeSimpleDateFormat(PSBCConstant.YYYYMMDDHHMMSS);
        //应用appid
        String merchantId = "tradeGroupPre001";
        //应用Id
        String appID = "1095757516090363904001";

        String platformID = "300370100000013";

        String mchtNo = "100370100000697";

        String orderSn = "7894355726135770";

        String serialNo = SerialNoUtil.getSerialNo();

        String sopPublicKey = "";
        //商户私钥
        String privateKey = "";
        //商户公钥
        String publicKey = "";

        String url = "http://wap.dev.psbc.com/sop-h5/biz_pre/unionpay/tradeGroupPre001.htm?partnerTxSriNo=";


        String date = dateFormat.format(new Date());
        requestBuilder.setHead(PSBCRequestFields.VERSION, PSBCConstant.VERSION);
        requestBuilder.setHead(PSBCRequestFields.ACCESS_TYPE, PSBCConstant.API);
        requestBuilder.setHead(PSBCRequestFields.MERCHANT_ID, merchantId);
        requestBuilder.setHead(PSBCRequestFields.APP_ID, appID);
        requestBuilder.setHead(PSBCRequestFields.REQ_TIME, date);
        requestBuilder.setHead(PSBCRequestFields.METHOD, "unionpay.orderRefund");
        Map<String, Object> data = new HashMap<>();


        // 公共字段
        data.put(PSBCRequestFields.VERSION, PSBCConstant.INTERFACE_VERSION);
        data.put(PSBCRequestFields.TXN_CODE, PSBCConstant.REFUND_CODE);
        data.put(PSBCRequestFields.CHANNEL_ID, PSBCConstant.CHANNEL_ID);
        data.put(PSBCRequestFields.PLATFORM_ID, platformID);
        data.put(PSBCRequestFields.REQ_TRACE_ID, orderSn + 13);
        data.put(PSBCRequestFields.REQ_DATE, dateFormat.format(new Date()));
        data.put(PSBCRequestFields.REQ_RESERVED, "");

        data.put(PSBCRequestFields.MCHT_NO, mchtNo);
        data.put(PSBCRequestFields.TXN_AMT, "0.01");
        data.put(PSBCRequestFields.ORG_REQ_TRACE_ID, orderSn);
        data.put(PSBCRequestFields.WHETHER_NOTIFY, PSBCConstant.NON_NOTIFY_FLAG);


        Map<String, String> terminalData = new HashMap<>();
        terminalData.put(PSBCRequestFields.DEVICE_TYPE, "11");
        terminalData.put(PSBCRequestFields.DEVICE_ID, "te961546");
        String terminalInfo = "";
        try {
            terminalInfo = JsonUtil.objectToJsonString(terminalData);
        } catch (Exception e) {
            logger.error("terminalInfo转换json失败", e);
        }
        data.put(PSBCRequestFields.TERM_INFO, terminalInfo);
        data.put(PSBCRequestFields.TERM_ID, "te961546");
        requestBuilder.setBody(PSBCRequestFields.DATA, data);
        requestBuilder.setBody(PSBCRequestFields.BUSI_MAIN_ID, serialNo);
        requestBuilder.setBody(PSBCRequestFields.REQ_TRANS_TIME, dateFormat.format(new Date()));
        requestBuilder.setHead(PSBCRequestFields.PARTNER_TX_SRI_NO, serialNo);
        PSBCClient psbcClient = new PSBCClient();
        Map post = psbcClient.call(requestBuilder.build(), url + serialNo, merchantId, sopPublicKey, publicKey, privateKey, "post");
        System.out.println(post);

    }

    public static void wxprecreate() throws MpayApiNetworkError, MpayException {

        PSBCRequestBuilder requestBuilder = new PSBCRequestBuilder();
        SafeSimpleDateFormat dateFormat = new SafeSimpleDateFormat(PSBCConstant.YYYYMMDDHHMMSS);
        //应用appid
        String merchantId = "tradeGroupPre001";
        //应用Id
        String appID = "1095757516090363904001";

        String platformID = "300370100000013";

        String mchtNo = "100440100073025";

        String orderSn = "202504231123342928435893";
        String tsn = orderSn + getRandomNum(10);

        String sopPublicKey = "";
        //商户私钥
        String privateKey = "";
        //商户公钥
        String publicKey = "";

        String url = "http://wap.dev.psbc.com/sop-h5/biz_pre/unionpay/tradeGroupPre001.htm?partnerTxSriNo=";

        String date = dateFormat.format(new Date());
        requestBuilder.setHead(PSBCRequestFields.VERSION, PSBCConstant.VERSION);
        requestBuilder.setHead(PSBCRequestFields.ACCESS_TYPE, PSBCConstant.API);
        requestBuilder.setHead(PSBCRequestFields.MERCHANT_ID, merchantId);
        requestBuilder.setHead(PSBCRequestFields.APP_ID, appID);
        requestBuilder.setHead(PSBCRequestFields.REQ_TIME, date);
        requestBuilder.setHead(PSBCRequestFields.METHOD, "unionpay.uniPay");
        Map<String, Object> data = new HashMap<>();

        // 公共字段
        data.put(PSBCRequestFields.VERSION, PSBCConstant.INTERFACE_VERSION);
        data.put(PSBCRequestFields.TXN_CODE, PSBCConstant.WX_PAY_CODE);
        data.put(PSBCRequestFields.CHANNEL_ID, PSBCConstant.CHANNEL_ID);
        data.put(PSBCRequestFields.PLATFORM_ID, platformID);
        data.put(PSBCRequestFields.REQ_TRACE_ID, orderSn);
        data.put(PSBCRequestFields.REQ_DATE, dateFormat.format(new Date()));
        data.put(PSBCRequestFields.REQ_RESERVED, "");


        data.put(PSBCRequestFields.MCHT_NO, mchtNo);
        data.put(PSBCRequestFields.TXN_AMT, "0.01");
        data.put(PSBCRequestFields.CURRENCY_CODE, PSBCConstant.CNY);
        data.put(PSBCRequestFields.TRADE_TYPE_WX, "JSAPI");
        data.put(PSBCRequestFields.IS_CREDIT, "0");
        data.put(PSBCRequestFields.BODY_WX, "测试");


        data.put(PSBCRequestFields.SUB_APP_ID, "wx422bf31b9ffbc08c");
        data.put(PSBCRequestFields.SUB_OPEN_ID_WX, "ovVc-0x9zdNTjlbOAQ2dGXhOz0gI");
        data.put(PSBCRequestFields.WHETHER_NOTIFY, PSBCConstant.NOTIFY_FLAG);
        data.put("identity", new HashMap<>());//实名支付

        data.put(PSBCRequestFields.BACK_URL, "http://127.0.0.1");

        Map<String, String> orderData = new HashMap<>();
        orderData.put(PSBCRequestFields.ORDER_FLAG, PSBCConstant.ORDER_FLAG_0);
        orderData.put(PSBCRequestFields.ORDER_TITLE, "111");
        orderData.put(PSBCRequestFields.ORDER_AMT, "0.01");

        data.put(PSBCRequestFields.ORDER_DATA, JsonUtil.objectToJsonString(orderData));
        Map<String, String> terminalData = new HashMap<>();
        terminalData.put(PSBCRequestFields.DEVICE_TYPE, "11");
        terminalData.put(PSBCRequestFields.DEVICE_ID, "13567890");
        String terminalInfo = "";
        try {
            terminalInfo = JsonUtil.objectToJsonString(terminalData);
        } catch (Exception e) {
            logger.error("terminalInfo转换json失败", e);
        }
        data.put(PSBCRequestFields.TERM_INFO, terminalInfo);
        data.put(PSBCRequestFields.TERM_ID, "13567890");
        requestBuilder.setBody(PSBCRequestFields.DATA, data);
        requestBuilder.setBody(PSBCRequestFields.BUSI_MAIN_ID, orderSn);
        requestBuilder.setBody(PSBCRequestFields.REQ_TRANS_TIME, dateFormat.format(new Date()));
        requestBuilder.setHead(PSBCRequestFields.PARTNER_TX_SRI_NO, orderSn);


        PSBCClient psbcClient = new PSBCClient();
        Map post = psbcClient.call(requestBuilder.build(), url + orderSn, merchantId, sopPublicKey, publicKey, privateKey, "post");
        System.out.println(post);

    }

    public static void zfbprecreate() throws MpayApiNetworkError, MpayException {

        PSBCRequestBuilder requestBuilder = new PSBCRequestBuilder();
        SafeSimpleDateFormat dateFormat = new SafeSimpleDateFormat(PSBCConstant.YYYYMMDDHHMMSS);
        //应用appid
        String merchantId = "tradeGroupPre001";
        //应用Id
        String appID = "1095757516090363904001";

        String platformID = "300370100000013";

        String mchtNo = "100440100073025";

        String orderSn = "202504231123342927433757";

        String sopPublicKey = "";
        //商户私钥
        String privateKey = "";
        //商户公钥
        String publicKey = "";

        String url = "http://wap.dev.psbc.com/sop-h5/biz_pre/unionpay/tradeGroupPre001.htm?partnerTxSriNo=";

        String date = dateFormat.format(new Date());
        requestBuilder.setHead(PSBCRequestFields.VERSION, PSBCConstant.VERSION);
        requestBuilder.setHead(PSBCRequestFields.ACCESS_TYPE, PSBCConstant.API);
        requestBuilder.setHead(PSBCRequestFields.MERCHANT_ID, merchantId);
        requestBuilder.setHead(PSBCRequestFields.APP_ID, appID);
        requestBuilder.setHead(PSBCRequestFields.REQ_TIME, date);
        requestBuilder.setHead(PSBCRequestFields.METHOD, "unionpay.aliCreOrder");
        Map<String, Object> data = new HashMap<>();

        // 公共字段
        data.put(PSBCRequestFields.VERSION, PSBCConstant.INTERFACE_VERSION);
        data.put(PSBCRequestFields.TXN_CODE, PSBCConstant.ALIPAY_PAY_CODE);
        data.put(PSBCRequestFields.CHANNEL_ID, PSBCConstant.CHANNEL_ID);
        data.put(PSBCRequestFields.PLATFORM_ID, platformID);
        data.put(PSBCRequestFields.REQ_TRACE_ID, orderSn);
        data.put(PSBCRequestFields.REQ_DATE, dateFormat.format(new Date()));
        data.put(PSBCRequestFields.REQ_RESERVED, "");


        data.put(PSBCRequestFields.MCHT_NO, mchtNo);
        data.put(PSBCRequestFields.TXN_AMT, "0.01");
        data.put(PSBCRequestFields.CURRENCY_CODE, PSBCConstant.CNY);
        data.put(PSBCRequestFields.WHETHER_NOTIFY, PSBCConstant.NON_NOTIFY_FLAG);
        data.put("identity", new HashMap<>());//实名支付

        data.put(PSBCRequestFields.BACK_URL, "http://127.0.0.1");
        data.put(PSBCRequestFields.BUYE_ID, "2088022969520650");
        data.put(PSBCRequestFields.SUBJECT, "测试");
        data.put(PSBCRequestFields.BODY, "测试");

        Map<String, String> orderData = new HashMap<>();
        orderData.put(PSBCRequestFields.ORDER_FLAG, PSBCConstant.ORDER_FLAG_0);
        orderData.put(PSBCRequestFields.ORDER_TITLE, "111");
        orderData.put(PSBCRequestFields.ORDER_AMT, "0.01");


        data.put(PSBCRequestFields.ORDER_DATA, JsonUtil.objectToJsonString(orderData));
        Map<String, String> terminalData = new HashMap<>();
        terminalData.put(PSBCRequestFields.DEVICE_TYPE, "11");
        terminalData.put(PSBCRequestFields.DEVICE_ID, "13567890");
        String terminalInfo = "";
        try {
            terminalInfo = JsonUtil.objectToJsonString(terminalData);
        } catch (Exception e) {
            logger.error("terminalInfo转换json失败", e);
        }
        data.put(PSBCRequestFields.TERM_INFO, terminalInfo);
        data.put(PSBCRequestFields.TERM_ID, "13567890");
        requestBuilder.setBody(PSBCRequestFields.DATA, data);
        requestBuilder.setBody(PSBCRequestFields.BUSI_MAIN_ID, orderSn);
        requestBuilder.setBody(PSBCRequestFields.REQ_TRANS_TIME, dateFormat.format(new Date()));
        requestBuilder.setHead(PSBCRequestFields.PARTNER_TX_SRI_NO, orderSn);


        PSBCClient psbcClient = new PSBCClient();
        Map post = psbcClient.call(requestBuilder.build(), url + orderSn, merchantId, sopPublicKey, publicKey, privateKey, "post");
        System.out.println(post);

    }

    public static void refundQuery() throws MpayApiNetworkError, MpayException {

        PSBCRequestBuilder requestBuilder = new PSBCRequestBuilder();
        SafeSimpleDateFormat dateFormat = new SafeSimpleDateFormat(PSBCConstant.YYYYMMDDHHMMSS);
        //应用appid
        String merchantId = "tradeGroupPre001";
        //应用Id
        String appID = "1095757516090363904001";

        String platformID = "300370100000013";

        String mchtNo = "100370100000697";

//        String orderSn = "7894259299226972";
        String orderSn = "789435572641251912";

        String tsn = SerialNoUtil.getSerialNo();

        String sopPublicKey = "";
        //商户私钥
        String privateKey = "";
        //商户公钥
        String publicKey = "";

        String url = "http://wap.dev.psbc.com/sop-h5/biz_pre/unionpay/tradeGroupPre001.htm?partnerTxSriNo=";


        String date = dateFormat.format(new Date());
        requestBuilder.setHead(PSBCRequestFields.VERSION, PSBCConstant.VERSION);
        requestBuilder.setHead(PSBCRequestFields.ACCESS_TYPE, PSBCConstant.API);
        requestBuilder.setHead(PSBCRequestFields.MERCHANT_ID, merchantId);
        requestBuilder.setHead(PSBCRequestFields.APP_ID, appID);
        requestBuilder.setHead(PSBCRequestFields.REQ_TIME, date);
        requestBuilder.setHead(PSBCRequestFields.METHOD, "unionpay.refundQuery");
        Map<String, Object> data = new HashMap<>();


        // 公共字段
        data.put(PSBCRequestFields.VERSION, PSBCConstant.INTERFACE_VERSION);
        data.put(PSBCRequestFields.TXN_CODE, PSBCConstant.REFUND_QUERY_CODE);
        data.put(PSBCRequestFields.CHANNEL_ID, PSBCConstant.CHANNEL_ID);
        data.put(PSBCRequestFields.PLATFORM_ID, platformID);
        data.put(PSBCRequestFields.REQ_TRACE_ID, orderSn);
        data.put(PSBCRequestFields.REQ_DATE, dateFormat.format(new Date()));
        data.put(PSBCRequestFields.REQ_RESERVED, "");

        data.put(PSBCRequestFields.MCHT_NO, mchtNo);
        data.put(PSBCRequestFields.TXN_AMT, "0.01");
        data.put(PSBCRequestFields.ORG_REQ_TRACE_ID, orderSn);
        data.put(PSBCRequestFields.WHETHER_NOTIFY, PSBCConstant.NON_NOTIFY_FLAG);


        Map<String, String> terminalData = new HashMap<>();
        terminalData.put(PSBCRequestFields.DEVICE_TYPE, "11");
        terminalData.put(PSBCRequestFields.DEVICE_ID, "te961546");
        String terminalInfo = "";
        try {
            terminalInfo = JsonUtil.objectToJsonString(terminalData);
        } catch (Exception e) {
            logger.error("terminalInfo转换json失败", e);
        }
        data.put(PSBCRequestFields.TERM_INFO, terminalInfo);
        data.put(PSBCRequestFields.TERM_ID, "te961546");
        requestBuilder.setBody(PSBCRequestFields.DATA, data);
        requestBuilder.setBody(PSBCRequestFields.BUSI_MAIN_ID, tsn);
        requestBuilder.setBody(PSBCRequestFields.REQ_TRANS_TIME, dateFormat.format(new Date()));
        requestBuilder.setHead(PSBCRequestFields.PARTNER_TX_SRI_NO, tsn);
        PSBCClient psbcClient = new PSBCClient();
        Map post = psbcClient.call(requestBuilder.build(), url + tsn, merchantId, sopPublicKey, publicKey, privateKey, "post");
        System.out.println(post);


    }

    public static void notify1() {


        PSBCRequestBuilder requestBuilder = new PSBCRequestBuilder();
        SafeSimpleDateFormat dateFormat = new SafeSimpleDateFormat(PSBCConstant.YYYYMMDDHHMMSS);
        //应用appid
        String merchantId = "tradeGroupPre001";
        //应用Id
        String appID = "1095757516090363904001";

        String platformID = "300370100000013";

        String mchtNo = "100370100000697";

        String orderSn = "7894355726413545";


        String sopPublicKey = "";
        //商户私钥
        String privateKey = "";
        //商户公钥
        String publicKey = "";

        String url = "http://wap.dev.psbc.com/sop-h5/biz_pre/unionpay/tradeGroupPre001.htm?partnerTxSriNo=";
        String tsn = SerialNoUtil.getSerialNo();

        String date = dateFormat.format(new Date());
        requestBuilder.setHead(PSBCRequestFields.VERSION, PSBCConstant.VERSION);
        requestBuilder.setHead(PSBCRequestFields.PARTNER_TX_SRI_NO, tsn);
        requestBuilder.setHead(PSBCRequestFields.MERCHANT_ID, merchantId);
        requestBuilder.setHead(PSBCRequestFields.APP_ID, appID);
        requestBuilder.setHead(PSBCRequestFields.REQ_TIME, date);
        requestBuilder.setHead(PSBCRequestFields.METHOD, "unionpay.offlineNotify");


        Map<String, Object> data = new HashMap<>();

        data.put(PSBCRequestFields.MCHT_NO, mchtNo);
        data.put(PSBCRequestFields.VERSION, PSBCConstant.INTERFACE_VERSION);
        data.put(PSBCRequestFields.TXN_CODE, PSBCConstant.CONSUMPTION_RESULT_NOTIFICATION_CODE);
        data.put(PSBCRequestFields.CHANNEL_ID, PSBCConstant.CHANNEL_ID);
        data.put(PSBCRequestFields.PLATFORM_ID, platformID);
        data.put(PSBCRequestFields.REQ_TRACE_ID, orderSn);
        data.put(PSBCRequestFields.REQ_DATE, dateFormat.format(new Date()));


        data.put(PSBCResponseFields.RESP_CD, PSBCConstant.SUCCESS_CODE);
        data.put(PSBCResponseFields.RESP_DESC, "通知成功");


        String dataMsg = "";
        try {
            dataMsg = JsonUtil.objectToJsonString(data);
        } catch (Exception e) {
            logger.error("data转换json失败", e);
        }
        requestBuilder.setBody(PSBCRequestFields.DATA, dataMsg);
        requestBuilder.setBody(PSBCResponseFields.RESP_CODE, PSBCConstant.SUCCESS_CODE);
        requestBuilder.setBody(PSBCResponseFields.RESP_DESC, "通知成功");

        PSBCClient psbcClient = new PSBCClient();
        Map post = null;
        try {
            post = psbcClient.call(requestBuilder.build(), url + tsn, merchantId, sopPublicKey, publicKey, privateKey, "post");
        } catch (MpayApiNetworkError e) {
            throw new RuntimeException(e);
        }
        System.out.println(post);
    }


    public static String getRandomNum(int length) {
        if (length < 1) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }
}
