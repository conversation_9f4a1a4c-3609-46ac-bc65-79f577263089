package com.wosai.mpay.api.bocpay;

import com.wosai.mpay.api.SSLEnvFlag;
import com.wosai.mpay.api.boc.*;
import com.wosai.mpay.util.HttpClientUtils;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.SSLUtil;
import com.wosai.mpay.util.SafeSimpleDateFormat;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

public class BocTest {

    public static void main(String[] args) throws Exception {
        zfbprecreate();
//        wxprecreate();

//        refund();
//        query();

//        refundQuery();

//        test();

//        fileToBase64();
//        b2cFileToBase64();
//        b2c();
    }


    public static void b2c() throws Exception {

        Map<String, Object> msgHeader = new HashMap<>();
        msgHeader.put("MsgVer", "1000");
        msgHeader.put("InDate", "20250605");
        msgHeader.put("InTime", "093301");
        msgHeader.put("TranId", "201001");
        msgHeader.put("BussId", "JHZF00610001");
        msgHeader.put("MerType", "01");
        msgHeader.put("Drctn", "11");
        Map<String, Object> msgBody = new HashMap<>();
        msgBody.put("MerId", "104230189992165");
        msgBody.put("TermId", "21423018");
        msgBody.put("PayLs", "21423018202506051131010000001");
        msgBody.put("TraceNo", "0000001");
        msgBody.put("BatchNo", "20250605");
        msgBody.put("PayType", "ZFBA");
        msgBody.put("AuthCode", "282755475043119312");
        msgBody.put("TranAmt", "000000000001");
        msgBody.put("CcyCode", "156");
        SSLContext sslContext = SSLUtil.getUnsafeSSLContext();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "text/xml; charset=utf-8");
        HostnameVerifier hostnameVerifier = SSLUtil.getUnsafeHostnameVerifier(SSLEnvFlag.getNotVerifyHostNames());
        Map<String, Object> result = HttpClientUtils.doCommonMethod(BocClient.class.getName(), sslContext, hostnameVerifier, "https://spost4.bjyada.com:4443/merfpay", null, "text/xml", "", headers, BocConstant.CHARSET, 20000, 50000, "post");

    }


    public static void zfbprecreate() throws Exception {
        long c2bTimeoutExpress = 4 * 60 * 1000;
        BocRequestBuilder bocRequestBuilder = new BocRequestBuilder();
        SafeSimpleDateFormat dateFormat = new SafeSimpleDateFormat("yyyyMMddHHmmss");
        bocRequestBuilder.setBody(BocRequestFields.MERCHANT_NO, "104459970116834");
        bocRequestBuilder.setBody(BocRequestFields.ORDER_NO, "789067834231297");
        bocRequestBuilder.setBody(BocRequestFields.ORDER_AMOUNT, "0.01");
        bocRequestBuilder.setBody(BocRequestFields.PAY_TYPE, BocConstant.ONLINE_PAY);
        bocRequestBuilder.setBody(BocRequestFields.CUR_CODE, BocConstant.CNY);
        bocRequestBuilder.setBody(BocRequestFields.ORDER_TIME, dateFormat.format(new Date(System.currentTimeMillis())));
        bocRequestBuilder.setBody(BocRequestFields.ORDER_NOTE, "测试订单");
        bocRequestBuilder.setBody(BocRequestFields.ORDER_TIMEOUT_DATE, dateFormat.format(new Date(System.currentTimeMillis() + c2bTimeoutExpress)));
        bocRequestBuilder.setBody(BocRequestFields.ORDER_SUBJECT, "测试订单");
        bocRequestBuilder.setBody(BocRequestFields.ORDER_URL, "www.baidu.com");
        bocRequestBuilder.setBody(BocRequestFields.TRADE_TYPE, BocConstant.ZFBXCX);
        bocRequestBuilder.setBody(BocRequestFields.DEVICE_INFO, "2088170874100907");
        bocRequestBuilder.setBody(BocRequestFields.BODY, "测试订单");
        bocRequestBuilder.setBody(BocRequestFields.TERMINAL_CHNL, BocConstant.PHONE);
        bocRequestBuilder.setBody(BocRequestFields.BUYER_ID, "2088022969520650");
        BocClient bocClient = new BocClient();
        Map<String, Object> result = bocClient.call(bocRequestBuilder.build(), "https://***************/PGWPortal/B2CRecvOrder.do", "", "", "", "", Arrays.asList(BocRequestFields.ORDER_NO, BocRequestFields.ORDER_TIME, BocRequestFields.CUR_CODE, BocRequestFields.ORDER_AMOUNT, BocRequestFields.MERCHANT_NO), "post", false);
        System.out.println(result);
    }

    public static void wxprecreate() throws Exception {
        long c2bTimeoutExpress = 4 * 60 * 1000;
        BocRequestBuilder bocRequestBuilder = new BocRequestBuilder();
        SafeSimpleDateFormat dateFormat = new SafeSimpleDateFormat("yyyyMMddHHmmss");
        bocRequestBuilder.setBody(BocRequestFields.MERCHANT_NO, "104459970116834");
        bocRequestBuilder.setBody(BocRequestFields.ORDER_NO, "78906783423127");
        bocRequestBuilder.setBody(BocRequestFields.ORDER_AMOUNT, "0.01");
        bocRequestBuilder.setBody(BocRequestFields.PAY_TYPE, BocConstant.ONLINE_PAY);
        bocRequestBuilder.setBody(BocRequestFields.CUR_CODE, BocConstant.CNY);
        bocRequestBuilder.setBody(BocRequestFields.ORDER_TIME, dateFormat.format(new Date(System.currentTimeMillis())));
        bocRequestBuilder.setBody(BocRequestFields.ORDER_NOTE, "测试订单");
        bocRequestBuilder.setBody(BocRequestFields.ORDER_TIMEOUT_DATE, dateFormat.format(new Date(System.currentTimeMillis() + c2bTimeoutExpress)));
        bocRequestBuilder.setBody(BocRequestFields.ORDER_SUBJECT, "测试订单");
        bocRequestBuilder.setBody(BocRequestFields.ORDER_URL, "www.baidu.com");
        bocRequestBuilder.setBody(BocRequestFields.DEVICE_INFO, "2088170874100907");
        bocRequestBuilder.setBody(BocRequestFields.SUB_APPID, "wx72534f3638c59073");
        bocRequestBuilder.setBody(BocRequestFields.SUB_OPENID, "ovVc-0x9zdNTjlbOAQ2dGXhOz0gI");
        bocRequestBuilder.setBody(BocRequestFields.TRADE_TYPE, BocConstant.WXXCX);
        bocRequestBuilder.setBody(BocRequestFields.MCHT_CUST_IP, "127.0.0.1");
        bocRequestBuilder.setBody(BocRequestFields.SPBILL_CREATE_IP, "127.0.0.1");
        bocRequestBuilder.setBody(BocRequestFields.TERMINAL_CHNL, BocConstant.PHONE);
        bocRequestBuilder.setBody(BocRequestFields.BODY, "测试订单");
        BocClient bocClient = new BocClient();
//        orderNo|orderTime|curCode|orderAmount|merchantNo
        Map<String, Object> result = bocClient.call(bocRequestBuilder.build(), "https://***************/PGWPortal/B2CRecvOrder.do", "", "", "", "", Arrays.asList(BocRequestFields.ORDER_NO, BocRequestFields.ORDER_TIME, BocRequestFields.CUR_CODE, BocRequestFields.ORDER_AMOUNT, BocRequestFields.MERCHANT_NO, BocRequestFields.MCHT_CUST_IP), "post", false);
        System.out.println(result);
    }


    public static void refund() throws Exception {
        BocRequestBuilder bocRequestBuilder = new BocRequestBuilder();
        bocRequestBuilder.setBody(BocRequestFields.ORDER_NO, "7894259299847072");
        bocRequestBuilder.setBody(BocRequestFields.M_REFUND_SEQ, "78906783423130");
        bocRequestBuilder.setBody(BocRequestFields.MERCHANT_NO, "104459970116834");
        bocRequestBuilder.setBody(BocRequestFields.REFUND_AMOUNT, "0.02");
        bocRequestBuilder.setBody(BocRequestFields.CUR_CODE, BocConstant.CNY);
        BocClient bocClient = new BocClient();
        Map<String, Object> result = bocClient.call(bocRequestBuilder.build(), "https://***************/PGWPortal/RefundOrder.do", "", "", "", "", Arrays.asList(BocRequestFields.MERCHANT_NO, BocRequestFields.M_REFUND_SEQ, BocRequestFields.CUR_CODE, BocRequestFields.REFUND_AMOUNT, BocRequestFields.ORDER_NO), "post", false);
        System.out.println(result);
    }

    public static void query() throws Exception {
        BocRequestBuilder bocRequestBuilder = new BocRequestBuilder();
        bocRequestBuilder.setBody(BocRequestFields.MERCHANT_NO, "104459970116834");
        bocRequestBuilder.setBody(BocRequestFields.ORDER_NO, "7894259299845079");
        BocClient bocClient = new BocClient();
        Map<String, Object> result = bocClient.call(bocRequestBuilder.build(), "https://***************/PGWPortal/QueryOrderTrans.do", "", "", "", "", Arrays.asList(BocRequestFields.MERCHANT_NO, BocRequestFields.ORDER_NO), "post", true);
        System.out.println(result);
    }

    public static void refundQuery() throws Exception {
        BocRequestBuilder bocRequestBuilder = new BocRequestBuilder();
        bocRequestBuilder.setBody(BocRequestFields.MERCHANT_NO, "104459970116834");
        bocRequestBuilder.setBody(BocRequestFields.M_REFUND_SEQ, "78906783423130");
        BocClient bocClient = new BocClient();
        Map<String, Object> result = bocClient.call(bocRequestBuilder.build(), "https://***************/PGWPortal/QueryRefund.do", "", "", "", "", Arrays.asList(BocRequestFields.MERCHANT_NO, BocRequestFields.M_REFUND_SEQ), "post", true);
        System.out.println(result);
    }




    public static void fileToBase64() {

        try {
            // 读取.pfx文件的字节数组
            byte[] pfxBytes = Files.readAllBytes(Paths.get("src/main/java/com/wosai/mpay/api/boc/payment/95566SZ010006435.pfx"));
            // 将字节数组编码为Base64字符串
            String base64EncodedString = Base64.getEncoder().encodeToString(pfxBytes);
            System.out.println("pfx_base64:" + base64EncodedString);
            // 读取.pfx文件的字节数组
            byte[] bocBytes = Files.readAllBytes(Paths.get("src/main/java/com/wosai/mpay/api/boc/payment/boccaTest.cer"));
            // 将字节数组编码为Base64字符串
            String bocBytesString = Base64.getEncoder().encodeToString(bocBytes);
            System.out.println("boc_base64:" + bocBytesString);
        } catch (Exception e) {

        }
    }


    public static void b2cFileToBase64() {

        try {
            // 读取.pfx文件的字节数组
            byte[] pfxBytes = Files.readAllBytes(Paths.get("src/main/java/com/wosai/mpay/api/boc/security/95566SW010004692.pfx"));
            // 将字节数组编码为Base64字符串
            String base64EncodedString = Base64.getEncoder().encodeToString(pfxBytes);
            System.out.println("b2c_pfx_base64:" + base64EncodedString);
            // 读取.pfx文件的字节数组
            byte[] bocBytes = Files.readAllBytes(Paths.get("src/main/java/com/wosai/mpay/api/boc/security/95566SW010004692.cer"));
            // 将字节数组编码为Base64字符串
            String bocBytesString = Base64.getEncoder().encodeToString(bocBytes);
            System.out.println("b2c_boc_base64:" + bocBytesString);
        } catch (Exception e) {

        }
    }
}
