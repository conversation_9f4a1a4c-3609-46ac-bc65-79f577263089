package com.wosai.mpay.api.bocpay;

import com.wosai.mpay.api.boc.b2c.BocpayClient;
import com.wosai.mpay.api.boc.b2c.BocpayRequestBuilder;
import com.wosai.mpay.api.boc.b2c.BocpayRequestFields;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.Map;

public class BocpayTest {

    public static final Logger logger = LoggerFactory.getLogger(BocpayTest.class);

    public static void main(String[] args) {
        b2cTest();
    }

    public static void b2cTest() {
        BocpayClient bocpayClient = new BocpayClient();
        BocpayRequestBuilder bocpayRequestBuilder = new BocpayRequestBuilder();
        SafeSimpleDateFormat format = new SafeSimpleDateFormat("yyyyMMddHHmmss");
        SafeSimpleDateFormat format2 = new SafeSimpleDateFormat("yyyyMMdd");
        SafeSimpleDateFormat format3 = new SafeSimpleDateFormat("HHmmss");
        String dateTime = format.format(new Date());
        String date = format2.format(new Date());
        String time = format3.format(new Date());

        bocpayRequestBuilder.setHead(BocpayRequestFields.MSG_VER, "1000");
        bocpayRequestBuilder.setHead(BocpayRequestFields.IN_DATE, dateTime);
        bocpayRequestBuilder.setHead(BocpayRequestFields.IN_TIME, time);
        bocpayRequestBuilder.setHead(BocpayRequestFields.TRAN_ID, "201001");
        bocpayRequestBuilder.setHead(BocpayRequestFields.BUS_ID, "JHZF00610001");
        bocpayRequestBuilder.setHead(BocpayRequestFields.MER_TYPE, "01");
        bocpayRequestBuilder.setHead(BocpayRequestFields.DRCTN, "11");
        bocpayRequestBuilder.setHead(BocpayRequestFields.IP, "127.0.0.1");

        bocpayRequestBuilder.setBody(BocpayRequestFields.MER_ID, "104230189992165");
        bocpayRequestBuilder.setBody(BocpayRequestFields.TERM_ID, "21423018");
        bocpayRequestBuilder.setBody(BocpayRequestFields.PAY_LS, "21423018" + dateTime + "000001");
        bocpayRequestBuilder.setBody(BocpayRequestFields.TRACE_NO, "0000001");
        bocpayRequestBuilder.setBody(BocpayRequestFields.BATCH_NO, date);
        bocpayRequestBuilder.setBody(BocpayRequestFields.PAY_TYPE_V2, "ZFBA");
        bocpayRequestBuilder.setBody(BocpayRequestFields.AUTH_CODE, "282755475043119312");
        bocpayRequestBuilder.setBody(BocpayRequestFields.TRAN_AMT, "000000000001");
        bocpayRequestBuilder.setBody("Body", "test");
        bocpayRequestBuilder.setBody(BocpayRequestFields.CCY_CODE, "156");
        Map<String, Object> result = null;
        try {
            result = bocpayClient.call(bocpayRequestBuilder.build(), "https://spost4.bjyada.com:4443/merfpay", "", "", "", "", null);
        } catch (Exception e) {
            logger.error("call bocb2c result:{} error: {}", e.getMessage(), result, e);
        }
    }
}
