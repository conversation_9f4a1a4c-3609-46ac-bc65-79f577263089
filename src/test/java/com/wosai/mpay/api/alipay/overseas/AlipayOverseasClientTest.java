package com.wosai.mpay.api.alipay.overseas;

import com.wosai.mpay.api.alipay.AlipayConstants;
import com.wosai.mpay.exception.BuilderException;
import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import org.junit.Test;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

public class AlipayOverseasClientTest {
    private static String gatewayUrl = AlipayOverseasConstants.GATEWAY_URL;
    private static String appId = "2160120167577300";
    private static String signType = AlipayOverseasConstants.SIGN_TYPE_RSA2;
    private static String privateKey = "";

    @Test
    public void testPay() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayOverseasClient client = new AlipayOverseasClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SERVICE,AlipayOverseasConstants.SERVICE_NAME_PAY_BARCODE);

        builder.set(BusinessFields.ALIPAY_SELLER_ID,appId);
        builder.set(BusinessFields.TRANS_NAME,"矿泉水");
        builder.set(BusinessFields.PARTNER_TRANS_ID,new SimpleDateFormat(AlipayOverseasConstants.DATE_TIME_FORMAT).format(new Date()));
        builder.set(BusinessFields.CURRENCY,"HKD");
        builder.set(BusinessFields.TRANS_AMOUNT,"1.00");
        builder.set(BusinessFields.BUYER_IDENTITY_CODE,"288981443290898130");
        builder.set(BusinessFields.IDENTITY_CODE_TYPE,"barcode");
        builder.set(BusinessFields.BIZ_PRODUCT,AlipayOverseasConstants.BIZ_PRODUCT_OVERSEAS_MBARCODE_PAY);

        builder.extendInfoSet(BusinessFields.SECONDARY_MCH_ID,"1680002786688");
        builder.extendInfoSet(BusinessFields.SECONDARY_MCH_NAME,"支付宝境外支付测试商户");
        builder.extendInfoSet(BusinessFields.SECONDARY_MCH_INDUSTRY,"5311");

        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call(gatewayUrl, appId, signType, privateKey, request);
        System.out.println(result);
    }

    @Test
    public void testCancel() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayOverseasClient client = new AlipayOverseasClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SERVICE,AlipayOverseasConstants.SERVICE_NAME_CANCEL);
        builder.set(BusinessFields.TIMESTAMP,new Date().getTime()+"");
        builder.set(BusinessFields.OUT_TRADE_NO,"7893259247622826");

        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call(gatewayUrl, appId, signType, privateKey, request);
        System.out.println(result);
    }

    @Test
    public void testQuery() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayOverseasClient client = new AlipayOverseasClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SERVICE,AlipayOverseasConstants.SERVICE_NAME_QUERY);
        builder.set(BusinessFields.PARTNER_TRANS_ID, "7894259268307776");
        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call(gatewayUrl, appId, signType, privateKey, request);
        System.out.println(result);
    }

    @Test
    public void testRefund() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayOverseasClient client = new AlipayOverseasClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SERVICE,AlipayOverseasConstants.SERVICE_NAME_REFUND);
        builder.set(BusinessFields.PARTNER_TRANS_ID,"2017082151108128");
        builder.set(BusinessFields.PARTNER_REFUND_TRANS_ID,"6117082151108128");
        builder.set(BusinessFields.REFUND_AMOUNT,"0.79");
        builder.set(BusinessFields.CURRENCY,"HKD");
        builder.set(BusinessFields.NOTIFY_URL,"https://requestb.in/1jp71vt1");
        builder.set(BusinessFields.REFUND_IS_SYNC,AlipayOverseasConstants.REFUND_IS_SYNC_NO);

        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call(gatewayUrl, appId, signType, privateKey, request);
        System.out.println(result);
    }

    @Test
    public void testRefundQuery() throws BuilderException, MpayException, MpayApiNetworkError {
        AlipayOverseasClient client = new AlipayOverseasClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SERVICE,AlipayOverseasConstants.SERVICE_NAME_REFUND_QUERY);
        builder.set(BusinessFields.OUT_TRADE_NO,"7894259268307775");
        builder.set(BusinessFields.OUT_RETURN_NO,"7894259268307772");
        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call(gatewayUrl, appId, signType, privateKey, request);
        System.out.println(result);
    }

    @Test
    public void testPrecreate() throws MpayException, MpayApiNetworkError, BuilderException {
        AlipayOverseasClient client = new AlipayOverseasClient();
        RequestBuilder builder = new RequestBuilder();
        builder.set(ProtocolFields.SERVICE,AlipayOverseasConstants.SERVICE_NAME_ACQUIRE_CREATE);
        builder.set(BusinessFields.NOTIFY_URL, "http://gateway.shouqianba.com/upay/v2/notify/alipay/ff518483691140c8e49ed4cea58a3248/e5088%267895219349222037%261%261580000009919679%262f39367a-7846-4639-ae6f-9117a47d00ea%263%261750815887336%26100000%260%260");
        builder.set(BusinessFields.OUT_TRADE_NO, "7894259245224097");
        builder.set(BusinessFields.TIMESTAMP, System.currentTimeMillis()+"");
        builder.set(BusinessFields.SUBJECT, "矿泉水");
        builder.set(BusinessFields.PRODUCT_CODE, "OVERSEAS_MBARCODE_PAY");
        builder.set(BusinessFields.TOTAL_FEE, "0.05");
        builder.set(BusinessFields.CURRENCY, "HKD");
        builder.set(BusinessFields.TRANS_CURRENCY, "HKD");
        builder.set(BusinessFields.BUYER_ID, "2088612184396666");
        builder.extendParamsSet(BusinessFields.SECONDARY_MCH_ID,"1680008259154");
        builder.extendParamsSet(BusinessFields.SECONDARY_MCH_NAME,"四季教育（香港）有限公司");
        builder.extendParamsSet(BusinessFields.SECONDARY_MCH_INDUSTRY,"5311");
        builder.extendParamsSet(BusinessFields.STORE_ID,"1580000009919679");
        builder.extendParamsSet(BusinessFields.STORE_NAME,"四季教育");
        builder.extendParamsSet(BusinessFields.TERMINAL_ID,"100007720047292505");

        builder.set(BusinessFields.IT_B_PAY, "30m");

        Map<String,String> request = builder.build();
        Map<String,Object> result = client.call(gatewayUrl, appId, signType, privateKey, request);
        System.out.println(result);
    }

    /**
     * 获取过期时间
     * @return
     */
    private String getTenMinitesExpireTime(){
        return new SimpleDateFormat(AlipayConstants.DATE_TIME_FORMAT).format(new Date(System.currentTimeMillis() + 1000 * 60 * 10));
    }

}
