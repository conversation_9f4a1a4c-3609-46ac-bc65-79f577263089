package com.wosai.mpay.api.hopeedu;

import cn.hutool.core.lang.UUID;
import com.wosai.mpay.api.hopeedu.constants.HopeEduProtocolFieldsConstants;
import com.wosai.mpay.api.hopeedu.constants.HopeEduRequestFieldsConstants;
import com.wosai.mpay.api.hopeedu.constants.HopeEduUrlPathConstants;
import com.wosai.mpay.api.hopeedu.utils.HopeEduUtil;
import com.wosai.mpay.api.xzx.XZXClient;
import com.wosai.mpay.api.xzx.XZXClientTest;
import com.wosai.mpay.api.xzx.XZXRequestBuilder;
import com.wosai.mpay.api.xzx.constants.XZXMethodFieldsConstants;
import com.wosai.mpay.api.xzx.constants.XZXProtocolFieldsConstants;
import com.wosai.mpay.api.xzx.constants.XZXRequestFieldsConstants;
import com.wosai.mpay.api.xzx.enums.XZXDevIdEnum;
import com.wosai.mpay.api.xzx.enums.XZXPayPrdCodeEnum;
import com.wosai.mpay.api.yop.PaymentStatusConstants;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import com.wosai.mpay.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className HopeEduClientTest
 * @description:
 * @create: 2025-05-21 17:58
 **/
public class HopeEduClientTest {





    public static final Logger logger = LoggerFactory.getLogger(HopeEduClientTest.class);

    public HopeEduClientTest() {}

    //公钥
    private static final String publicKey = "";
    //私钥
    private static final String privateKey = "";
    //测试环境访问地址
    private static final String REQUEST_URL = "http://*************:16009";
    //appKey
    private static final String CHANNEL_CODE = "16852465";
    //学工号 LDN7HDQM024102815654  LDN7HDQM024102815727
    private static final String machineNumber = "LDN7HDQM024102815727";

    private static final String orderAmount = "0.04";

    private static final String accessToken = "daae6433dee94bf091a8cf2afbca7713";

    private static final String billKey = "57be927c227646ea8614f82838a43a9b";

    /**
     * 创建订单
     *
     * @throws Exception
     */
    private static void createOrder() throws Exception {
        HopeEduRequestBuilder requestBuilder = new HopeEduRequestBuilder(CHANNEL_CODE);
        //构建body请求参数
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.ORDER_AMOUNT, orderAmount);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.ORDER_TYPE, HopeEduProtocolFieldsConstants.ORDER_TYPE_VALUE);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.MACHINE_NUMBER, machineNumber);


        Map<String, Object> result = new HopeEduClient().call(REQUEST_URL, HopeEduUrlPathConstants.ORDER_CREATE,  requestBuilder.build(), privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));

        /* 响应结果示例
        {"head":{"version":"1.0.0","sign_type":"SHA256","date":"2025-05-28T09:14:33+0800","channel_code":"16852465"},"body":{"data":{"orderId":"202505280010081600091433432"},"result_code":200,"message":"操作成功"}}
{
    "response": {
        "head": {
            "version": "1.0.0",
            "sign_type": "SHA256",
            "date": "2025-05-26T16:44:27+0800",
            "channel_code": "16852465"
        },
        "body": {
            "data": {
                "orderId": "202505260010020800164427051"
            },
            "result_code": 200,
            "message": "操作成功"
        }
    },
    "signature": "W/PQRggbLClsCu1Nbq5kJ/PjdmOXpqGwGYIStmk+i5mv1Kh7qxKDHs/pRAhfcppqUsouo3bgESdwCoovzwH6DzMF1uOx/CJ5GOUzvfzKh+uRb/sg9eqHYTwp3K4BLJG9zZGXp/i89JeQ8mO1yOzFqjUq25ddJOJoHCRU6Gk349aSt7zu3fLmVYHSjYt/31PcdjFku05N/2NNh8PbXeVNrSssZhwC7CaTkAPacREjUIeIH0xPfWMMBM9fFdf28KM45437RHiBRBCDbOYf3uuUr1RTaPDYfHI/joeS63SoyiTuClfC52zBpVcqs5XC8W6VQYX/6dsjJ1AZCWdNJyUxhg=="
}
         */
    }

    /**
     * 订单支付
     *
     * @throws Exception
     */
    private static void payOrder(String orderId, String codeValue) throws Exception {


        HopeEduRequestBuilder requestBuilder = new HopeEduRequestBuilder(CHANNEL_CODE);
        //构建body请求参数
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.ORDER_AMOUNT, orderAmount);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.CODE_VALUE, codeValue);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.ORDER_ID, orderId);
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.MACHINE_NUMBER, machineNumber);


        Map<String, Object> result = new HopeEduClient().call(REQUEST_URL, HopeEduUrlPathConstants.ORDER_PAY,  requestBuilder.build(), privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));

        /* 响应示例
{
    "response": {
        "head": {
            "version": "1.0.0",
            "sign_type": "SHA256",
            "date": "2025-05-26T16:45:47+0800",
            "channel_code": "16852465"
        },
        "body": {
            "result_code": 200,
            "message": "操作成功"
        }
    },
    "signature": "D6tpUgmuJR0FOqW8MGp5RX03NRU608TvCXEiW/WBPUmqXK0c20008pClzTXBPgfonFI8r2G2FPbeyq/CRKZJblnfjGK6U9V14MuUpNNx8HlJtIxa4fSFRUejAjwVHlAqli2g3i00+mQxtxPrWWrSsajSkugFdb4BKklNR9uqFajZ4/ZcRyKYgp5JG/iHFpAPDDLzzVGbKP5XcchlSh9FqIVXE+rBSmUPoac3ryUPFniQpQ5//C8Rs0q0OfH1aoEUhB+A27FEKMlRK8+vcbFwV5X5Kn4hepG0EsPIUqv6YhSBAb9oFIydUCmtCvkVzBO80eUM840Wtstb83JpQp8Iuw=="
}
         */
    }



    /**
     * 查询订单
     *
     * @throws Exception
     */
    private static void queryOrder(String orderId) throws Exception {

        HopeEduRequestBuilder requestBuilder = new HopeEduRequestBuilder(CHANNEL_CODE);
        //构建body请求参数
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.ORDER_ID, orderId);


        Map<String, Object> result = new HopeEduClient().call(REQUEST_URL, HopeEduUrlPathConstants.ORDER_QUERY,  requestBuilder.build(), privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));

        /* 响应示例
        {
    "head": {
        "version": "1.0.0",
        "sign_type": "SHA256",
        "date": "2025-05-26T16:24:39+0800",
        "channel_code": "16852465"
    },
    "body": {
        "data": {
            "orderStatus": "已支付"
        },
        "result_code": 200,
        "message": "操作成功"
    }
}
         */
    }


    public static void pushMessage(String orderId) throws Exception {

        HopeEduRequestBuilder requestBuilder = new HopeEduRequestBuilder(CHANNEL_CODE);
        //构建body请求参数
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.PAY_VER, "210");
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.CARD_TYPE, "2");
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.MERCHANT_NO, "1680001476594");
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.MERCHANT_NAME, "土鸭家菜馆长沙市王公塘店");
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.TERMINAL_ID, "100000330002811389");
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.TERMINAL_TIME, Instant.ofEpochMilli(1748239194000L)
                .atZone(ZoneId.systemDefault())
                .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.TERMINAL_TRACE, "7894355726624582");
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.OUT_TRADE_NO, "7894355726624582");
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.OUT_REFUND_NO, "");
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.TOTAL_FEE, "4");
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.PAY_TYPE, "23");
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.END_TIME, Instant.ofEpochMilli(1748239199000L)
                .atZone(ZoneId.systemDefault())
                .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));

        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.ATTACH, "100000330002811389");
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.USER_ID, "123");
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.CHANNEL_TRADE_NO, "202505260010005700183032683");
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.PAY_STATUS_CODE, "1");
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.PAY_CHANNEL, "⽀付渠道");
        requestBuilder.setBodyField(HopeEduRequestFieldsConstants.Body.ORDER_BODY, "测试");

        Map<String, Object> result = new HopeEduClient().callMessagePush(REQUEST_URL, HopeEduUrlPathConstants.MESSAGE_RECEIVE,  requestBuilder.buildBody(), accessToken);
        System.out.println(JsonUtil.objectToJsonString(result));
    }



    public static void testConvertTerminal() {
        String decimalString = "100000330002811389"; // 示例：一个很长的十进制数字字符串
        String base62String = HopeEduUtil.convertToBase62(decimalString);
        System.out.println("Decimal: " + decimalString);
        System.out.println("Base62: " + base62String);

        String b2 = HopeEduUtil.convertToTerminal(decimalString);
        System.out.println("Decimal: " + decimalString);
        System.out.println("Base62: " + b2);

        String d3 = "1000009";

        String b3 = HopeEduUtil.convertToTerminal(d3);
        System.out.println("Decimal: " + b3);
        System.out.println("Base62: " + b3);

        String d4 = "1000Aaasdf9";

        String b4 = HopeEduUtil.convertToTerminal(d4);
        System.out.println("Decimal: " + b4);
        System.out.println("Base62: " + b4);
    }

    public static void main(String[] args) throws Exception {
        try {

//            createOrder();

//            payOrder("202505280010081600091433432", "664678839282906312");
//
//            queryOrder("202505260010095900190413956");
            queryOrder("202505280010081600091433432");
//            pushMessage("202505260010095900190413956");
//            pushMessage("202505260010095900190413956");
//            testConvertTerminal();

        } catch (Exception e) {


            System.out.println("执行失败, 错误如下:");
            e.printStackTrace();
        }
    }
}

