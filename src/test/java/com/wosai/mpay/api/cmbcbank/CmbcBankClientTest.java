package com.wosai.mpay.api.cmbcbank;

import java.util.Map;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;

public class CmbcBankClientTest {
    static String merchantPrivateKey = "";
    static String merchantPrivatePwd = "123abc";
    static String merchantPublicKey = "TUlJREp6Q0NBc3VnQXdJQkFnSUZNQlpETmlnd0RBWUlLb0VjejFVQmczVUZBREFyTVFzd0NRWURWUVFHRXdKRFRqRWNNQm9HQTFVRUNnd1RRMFpEUVNCVFRUSWdWRVZUVkNCUFEwRXlNVEFlRncweU1qQXpNREl3TnpBNE1qSmFGdzB5TnpBek1ESXdOekE0TWpKYU1ISXhDekFKQmdOVkJBWVRBa05PTVEwd0N3WURWUVFLREFSRFRVSkRNUkl3RUFZRFZRUUxEQWxEVFVKRFgwUkRUVk14R1RBWEJnTlZCQXNNRUU5eVoyRnVhWHBoZEdsdmJtRnNMVEV4SlRBakJnTlZCQU1NSERBek1EVkFXa3BPUmtnd01ERkE1cldPNVkyWDVZaUc2S0dNUURFd1dUQVRCZ2NxaGtqT1BRSUJCZ2dxZ1J6UFZRR0NMUU5DQUFSLzFHbktLTDFVbDlBN0h3dXptbjVZWkNnb1MveE1nVHJrTlZmWThXaHRENnAwRU45UXQrTE5jUGtydVNoWFJDdjU0bmU1eFpSdlhkbkh1Y1kyL0lJS280SUJrVENDQVkwd0h3WURWUjBqQkJnd0ZvQVU0bjYyRUx1VTZ4WG1ydEVWQ3YvbzE2QlhPWjB3U0FZRFZSMGdCRUV3UHpBOUJnaGdnUnlHN3lvQ0FqQXhNQzhHQ0NzR0FRVUZCd0lCRmlOb2RIUndPaTh2ZDNkM0xtTm1ZMkV1WTI5dExtTnVMM1Z6TDNWekxURXpMbWgwYlRDQjFBWURWUjBmQklITU1JSEpNQytnTGFBcmhpbG9kSFJ3T2k4dk1qRXdMamMwTGpReUxqTXZUME5CTWpFdlUwMHlMMk55YkRJeE1EY3pMbU55YkRDQmxhQ0JrcUNCajRhQmpHeGtZWEE2THk4eU1UQXVOelF1TkRJdU1UQTZNemc1TDBOT1BXTnliREl4TURjekxFOVZQVk5OTWl4UFZUMURVa3dzVHoxRFJrTkJJRk5OTWlCVVJWTlVJRTlEUVRJeExFTTlRMDQvWTJWeWRHbG1hV05oZEdWU1pYWnZZMkYwYVc5dVRHbHpkRDlpWVhObFAyOWlhbVZqZEdOc1lYTnpQV05TVEVScGMzUnlhV0oxZEdsdmJsQnZhVzUwTUFzR0ExVWREd1FFQXdJRCtEQWRCZ05WSFE0RUZnUVVtZjl3alRENk1DajJBRmE1MDQ0a0c3VVNKS293SFFZRFZSMGxCQll3RkFZSUt3WUJCUVVIQXdJR0NDc0dBUVVGQndNRU1Bd0dDQ3FCSE05VkFZTjFCUUFEU0FBd1JRSWhBSmFDZERlc3h4ZmtIOFVjb0ZCamxWR3pkZFBMTXVHMjRiajYrWVNranpWNkFpQlpPN3ArMGVOSjZnZng0K21nRHNuLzYxY0Q2TXcvdCtDSHlVL3FBNDRQQkE9PQ==";
    static String platformPublicKey = "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tDQpNSUlESmpDQ0FzdWdBd0lCQWdJRk1CZUhTQmd3REFZSUtvRWN6MVVCZzNVRkFEQXJNUXN3Q1FZRFZRUUdFd0pEDQpUakVjTUJvR0ExVUVDZ3dUUTBaRFFTQlRUVElnVkVWVFZDQlBRMEV5TVRBZUZ3MHlNakExTVRZd056VTBNREZhDQpGdzB5TnpBMU1UWXdOelUwTURGYU1ISXhDekFKQmdOVkJBWVRBa05PTVJFd0R3WURWUVFLREFoUFEwRXlNVk5ODQpNakVRTUE0R0ExVUVDd3dIVkVWRFZHVnpkREVaTUJjR0ExVUVDd3dRVDNKbllXNXBlbUYwYVc5dVlXd3RNVEVqDQpNQ0VHQTFVRUF3d2FUME5CTWpGQVoyOXVaM3BwWkdGcFptRkFXbG94TVRFeFFERXdXVEFUQmdjcWhrak9QUUlCDQpCZ2dxZ1J6UFZRR0NMUU5DQUFUYldPeHlXeXZwZWp5elQ5Uy9aS0ZFWFFKc1dYdmMva1pYTWpoL251L20vMFZaDQpIVERRNHlwZ1JnMlFDcVBrMnBKNUpSaWNXdHdBd1l0ejJnN0U4Ry9kbzRJQmtUQ0NBWTB3SHdZRFZSMGpCQmd3DQpGb0FVNG42MkVMdVU2eFhtcnRFVkN2L28xNkJYT1owd1NBWURWUjBnQkVFd1B6QTlCZ2hnZ1J5Rzd5b0NBakF4DQpNQzhHQ0NzR0FRVUZCd0lCRmlOb2RIUndPaTh2ZDNkM0xtTm1ZMkV1WTI5dExtTnVMM1Z6TDNWekxURXpMbWgwDQpiVENCMUFZRFZSMGZCSUhNTUlISk1DK2dMYUFyaGlsb2RIUndPaTh2TWpFd0xqYzBMalF5TGpNdlQwTkJNakV2DQpVMDB5TDJOeWJESXpNelF3TG1OeWJEQ0JsYUNCa3FDQmo0YUJqR3hrWVhBNkx5OHlNVEF1TnpRdU5ESXVNVEE2DQpNemc1TDBOT1BXTnliREl6TXpRd0xFOVZQVk5OTWl4UFZUMURVa3dzVHoxRFJrTkJJRk5OTWlCVVJWTlVJRTlEDQpRVEl4TEVNOVEwNC9ZMlZ5ZEdsbWFXTmhkR1ZTWlhadlkyRjBhVzl1VEdsemREOWlZWE5sUDI5aWFtVmpkR05zDQpZWE56UFdOU1RFUnBjM1J5YVdKMWRHbHZibEJ2YVc1ME1Bc0dBMVVkRHdRRUF3SUQrREFkQmdOVkhRNEVGZ1FVDQpFY1FNT0tzVmFxM1lOV1ZqcW5nU3FuYXY5U1V3SFFZRFZSMGxCQll3RkFZSUt3WUJCUVVIQXdJR0NDc0dBUVVGDQpCd01FTUF3R0NDcUJITTlWQVlOMUJRQURSd0F3UkFJZ09udEFhVDRDcks2Y0t1U1BHRWFMNEd1cTZ1MTdvMmd3DQo5dnl1RUg0MTl4c0NJR0hkNlM3UmRxUUNmdC82aUtFYXpJMmJhRWRBTDFDN3h4ditBenVpeW96aQ0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQ0K";

    static CMBCBankClient client = new CMBCBankClient();

    public static void main(String[] args) throws Exception {
//        testSign();
//        testQuery();
        testNotify();
    }

    private static void testNotify() throws MpayException {
        String context = "{\"context\":\"MIIEaAYKKoEcz1UGAQQCA6CCBFgwggRUAgECMYGdMIGaAgECgBSp1tCA9yPUhnYNbknV+TsM7roSWjANBgkqgRzPVQGCLQMFAARwdhy3J6MbfqE5WiWRMkAYSrMvECT4itl+QMrfwQ/xN4Qqh8i5i5445TPoWZ0Wz2PToXmW2XFZUryxKTVyMxItDimI3f6CMDKrj75IZDqu7szG+h9IGEZECd5n005RRpoMQ0trB0cFM2jS5QGYfHG04TCCA60GCiqBHM9VBgEEAgEwGwYHKoEcz1UBaAQQoPV2hqMqKet4Zoq2RL0AgYCCA4DeuZLKFj6DJKi55AELtgu5nQOgQzXIsvOooMn9AW9x9gBmBPw7FOXFcvnZCNF4w9o/WCHodB78u0+3wk9CTLztg5CEvWUFWrdF7VqX2Q9ngW7zrSOzgfaHEPnwNdKrJq1VdygI/0wEQIHkTY2kTC40e316tYae3DnJa3SVF0qkI2xLfvJbSFNc8PO5BGxZ1EfmQ0tTB9x8OQfb9nCkdaLfKk8UYlq3ZAn2EohbB2g6WRL3wt7oRX00kJTLz7OYxficqlmefJLTWHRuEXvz3CdvvQA3HZCuyY2o6njeVkBSql5mIy9B0mz6lLBe6yQMh16TH4MhAe9ormJoDYXePfwdrf53OOgiMGzG3+qYiKSkk5uAh6X7PZ7oNXZ6tLUXVLNOF3MBjCBCwhDZmF9kb4cC+2ICcwA+/BV0xBRe5VPxuGYDGGH/rhI8Vfel88q2O/0ISaECXY2zsMMcPYQ/1Jl+DUGpgPhT8IK3mUqkCzzR+t66f4BfiGdCzz2iclRMNhe03Z5Yk9UDAuGzfoX2Un8O1fBcOlHYKHaQ2jJ4aUKoLnc0IpiGLZYeFsCdsjIYKk25fjqQ6dEN8zU42SAv7uiuP0v+GIQQTUq5y2kt2P1cD5TyZzRwJ8oBRmi0eICCzWtZaP1lT8bO8zwGhcSmD7YqgMYCt1el7eN5gNwVlQvSbh8VZqcrdjzDmG/LTBkjTPcD32STGlPie3wSENenam3WgMf4z0cxBWtA20JkAvVGHRLA/M3HwycB5l0ye77KeDWOg0gi+VaCRnvyhdAMKl3TWmZ71kG8A3647baLWImRZ1MIeKDeldd6AtO2YK8LV8okxK3v0eY9GDnk0nXe1AB6uc3inDJB7yNm8zP/Bt57Hk4MfGAfR2z2h2zBKZ5k+2LulyfnjksiuCOLjI9kiXJGnLKgSKjWzM2qof8ff36idiQMX6x85yYKPAhCMpjbx1LoZMjHd+5TDIzuzVX+TVybfRtwNRffT6lBqqEs9wfiXR1idARSTfjWaQhf6Pl1tv4/Xh66C1UlPfCBJSGkKodhgF8bluWhv0/9lZTHUxoxelvrFGzytQbO2e/PDZKRSaYHM03PZyeIuZQIfsCo6ZWMNnalOBYWbpoR+/xlEJWeY8SBnHg1oG5+3cmP0lG/vWGaVop4stq4T5SO+OLGmKucnOz53IOPjm8yCkFI0Ggkwg==\"}";
        Map<String, Object> contextMap = JsonUtil.jsonStrToObject(context, Map.class);
        String encryptContext = MapUtil.getString(contextMap, "context");
        String decryptContext = CmbcBankSignUtil.dncrypt(merchantPrivateKey, merchantPrivatePwd, encryptContext);
        System.out.println(decryptContext);

    }

    public static void testSign() throws MpayException {
        String context = "{\"amount\":\"2\"," + "\"defaultTradeType\":\"API_WXQRCODE\","
                + "\"isConfirm\":\"0\"," + "\"isShowSuccess\":\"0\"," + "\"merchantName\":\"乐收银测试\","
                + "\"merchantNum\":\"M01002016070000000789\"," + "\"merchantSeq\":\"*****************\","
                + "\"notifyUrl\":\"http://111.205.207.103/merchantdemo/noticeServlet\"," + "\"orderInfo\":\"\","
                + "\"platformId\":\"cust0001\"," + "\"printFlag\":\"0\"," + "\"remark\":\"\","
                + "\"selectTradeType\":\"API_WXQRCODE\"," + "\"transDate\":\"********\","
                + "\"transTime\":\"***************\"}";

        String sign = CmbcBankSignUtil.getSign(merchantPrivateKey, merchantPrivatePwd, context);
        System.out.println("--------------------------------------");
        System.out.println("签名：");
        System.out.println(sign);

        String signContext = CmbcBankSignUtil.sign(sign, context);
        System.out.println("--------------------------------------");
        System.out.println("加密前：");
        System.out.println(signContext);

        String encryptContext = CmbcBankSignUtil.encrypt(merchantPublicKey, signContext);
        System.out.println("--------------------------------------");
        System.out.println("加密后：");
        System.out.println(encryptContext);

        String decryptContext = CmbcBankSignUtil.dncrypt(merchantPrivateKey, merchantPrivatePwd, encryptContext);
        System.out.println("--------------------------------------");
        System.out.println("解密后：");
        System.out.println(decryptContext);

        boolean signChkResult = CmbcBankSignUtil.signCheck(merchantPublicKey, decryptContext);
        System.out.println("--------------------------------------");
        System.out.println("验证签名结果：");
        System.out.println(signChkResult);

        CmbcBankSignUtil.encrypt(platformPublicKey, signContext);
    }

    public static void testQuery() throws MpayApiNetworkError, MpayException {
        RequestBuilder builder = new RequestBuilder();
        builder.set(BusinessFields.PLATFORM_ID, "A00012017060000000691");
        builder.set(BusinessFields.MERCHANT_NO, "M00002022030000044363");
        builder.set(BusinessFields.MERCHANT_SEQ, "****************");
        Map<String, Object> rs = client.call("https://wxpay.cmbc.com.cn/mobilePlatform/appserver/paymentResultSelectNew.do", builder.build(), platformPublicKey, merchantPrivateKey, merchantPrivatePwd);
        System.out.println(rs);
    }


}
