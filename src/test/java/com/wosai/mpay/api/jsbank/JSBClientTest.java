package com.wosai.mpay.api.jsbank;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import junit.framework.TestCase;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.wosai.mpay.api.jsbank.JSBBusinessFields.RECORD_LIST;
import static com.wosai.mpay.api.jsbank.JSBClient.RESPONSE_REGEX;

/**
 * <AUTHOR>
 * @description
 * @date 2024-11-04
 */

public class JSBClientTest extends TestCase {
    private static final String singleRecordResponse = "field1=&field2=&field3=&partnerId=ab53d34b404b4f19afe1e2c559b3ee30&respBizDate=********&respCode=027089&respMsg=外部订单号已存在&signType=RSA&sign=PYX/NI8W2po2L2PmUGfFdQQXGl9YtTn0bvzTc1Tnjh+WPlA/1Q8Z5on3Yj2mwy56yyikC/Ja11uKKYHl0RNvNhc7WSghygeqVnSit9RlxfCfLr9EFhlCJrcWDdHJ2au5KLjofSyWeDwuePuOhqKeVFf/xfLkGZNTHkjCkwQjfNg=";
    private static final String multiRecordResponse = "partnerid=85cle2b8a0d34dAAAA5d60618b412b37&field1=&field2=****************&field3=&respBizDate=********&respCode=000000&respMsg=交易成功&totalRows=12&pageSize=20&currentPage=18totalPages=1&hasNext=0&hasPrevious=0&signType=RSA&sign=T/GYe2GfgcUCNFBjCUGG1v9FNU4H11CSl8TQPLk4FJrQgBHd1Tg9LyUXPJofv5h5kect/BRN7kftrc3/K49BRjYxshQQTpwEqWO6b3mHKbHErRD/cXzJ+iIcVtc44xN9FUwpWkydpbSDSIIWQr8GDdfPmoSvNs+sN5l0TrY71R4=&-&transDate=********&transTime=********181311&orderNo=********1AAAA1085420473&outTradeNo=095552b2136f447db8573f2fe737df59&amount=430&orderStatus=1&orderType=1&checkStatus=&tradeType=2&extfld1=0.0|0.0&extfld2=info&extfld3=********181313&deviceNo=****************&operatorId=&payId=ohdRE03RlXXXX6M9oQWgEgUMxmY&-&transDate=********&transTime=********180210&orderNo=********AAAA10085394199&outTradeNo=b266edbd273e4ec48a9df3a8de60a8be&amount=18&orderStatus=1&orderType=1&checkStatus=&tradeType=2&extfld1=0.0|0.0&extfld2=info&extfld3=********180211&deviceN0=****************&operatorId=&payId=ohdXXXXytBp5R6xEvLlsTR_IckI&-&transDate=********&transTime=********143406&orderNo=********143AAAA84992479&outTradeNo=f4cfd4adee214406a8a8853e35729a8d8amount=248&orderStat=1&orderType=1&checkStatus=&tradeType=2&extfld1=0.0|0.0&extfld2=info&extfld3=********143408&deviceNo=****************&operatorId=&payId=ohdREOwN9XXXXvN80RKjnGZgwA88&-&transDate=********&transTime=********133930&orderNo=2021110AAAA930084894135&outTradeNo=4e819a61fcd24b18afe22e236972d66e&amount=2580&orderStatus=1&orderType=1&checkStatus=&tradeType=2&extfld1=0.0|0.0&extfld2=info&extfld3=********133936&deviceNo=****************&operatorId=&payId=ohdREOXXXXIRCjRN4ffITU_8KXFI&-&transDate=********&transTime=********1212178orderNo=202111AAAA1217084754950&outTradeNo=b0704314d0cf4111a5448dd209362f6a&amount=309&orderStatus=1&orderType=1&checkStatus=&tradeType=3&extfld1=0.0|0.0&extfld2=info&extfld3=********121218&deviceNo=****************&operatorId=&payId=2088902X";

    /**
     * 测试单条响应数据
     */
    public void testSingleRecord() {
        Pattern pattern = Pattern.compile(RESPONSE_REGEX);
        Matcher matcher = pattern.matcher(singleRecordResponse);

        assertTrue(matcher.find());

        String content = matcher.group(1);
        String signValue = matcher.group(2);
        String listData = matcher.group(3);

        assertTrue(content.startsWith("field1="));
        assertTrue(content.endsWith("外部订单号已存在"));
        assertTrue(signValue.startsWith("PYX/NI8W"));
        assertTrue(signValue.endsWith("fNg="));
        assertTrue(listData.isEmpty());
    }

    /**
     * 测试多条响应数据
     */
    public void testMultiRecord() {
        Pattern pattern = Pattern.compile(RESPONSE_REGEX);
        Matcher matcher = pattern.matcher(multiRecordResponse);

        assertTrue(matcher.find());

        String content = matcher.group(1);
        String signValue = matcher.group(2);
        String listData = matcher.group(3);

        assertTrue(content.startsWith("partnerid="));
        assertTrue(content.endsWith("hasPrevious=0"));
        assertTrue(signValue.startsWith("T/GYe2Gfgc"));
        assertTrue(signValue.endsWith("TrY71R4="));
        assertTrue(listData.startsWith("&-&transDate=********&transTime=********181311"));
        assertTrue(listData.endsWith("&payId=2088902X"));
    }

    /**
     * 解析单条响应数据
     */
    public void testParseResponseContentOfSingleRecord() throws MpayException {
        String response = "appId=wxccbcac9a3ece5112&field1=715261044&field2=&field3=&nonceStr=5ca3b7f7e9534103b3979b4c6a3c8ba2&orderNo=20241203091608590816489&outOrderNo=7895224580321104&packAge=wx0309160879389877ff7bc0fbf046140000&packageValue=&partnerId=c018f1d1410b4d899be0ce994da38a79&paySign=enc3g6ZazYsgYQ7b8IEklwZotL8I+2OQrocLJUuqeUuk4KwdghdHJ7Vuq9OFHhDtOd4pF9HmmTYGtwW/zoXrfZBDsDBac/DFofIRKIlKX6CBYvbv3S4EMwDzIFqzMly22Jvio95kah8DkbJDwNRt6Iae7UNLDOZFTu7i4ASeNfVXWrI2sfEd+cIu7MuQRuHscJeRE1NlJ3CH9GXjwi5yN+ycV4O1JZrF3FkpQ21rl60kZGR2x7IOClbs6WmgXhQZ2LQThrzsr/SZe9yKeOdJojcp4nbzxLOssD2qdV+FdwZUV4OqR5BqvehR/D+BZCjr5tvCV6NanEmtbRXBoM3wlg==&prepayId=&respBizDate=20241203&respCode=000000&respMsg=交易成功&timeStamp=1733188568&signType=RSA&sign=H2XEyK/SBKi6HIzhQ0+qW+7y7i7ppzHGbRk430EG900mxBCnxr3aE4ug9G9SmapLL88VjLMfkRBLySShhp+B1F7fQOKoPwaCGqoH+Hp7ixb5vCq0+VMahVTPZwsmkUpiuK+IS2tGm9pcBL2/gr53MZQsQPM0ajJivaxVMesdGyk=";
        String paySign = "enc3g6ZazYsgYQ7b8IEklwZotL8I+2OQrocLJUuqeUuk4KwdghdHJ7Vuq9OFHhDtOd4pF9HmmTYGtwW/zoXrfZBDsDBac/DFofIRKIlKX6CBYvbv3S4EMwDzIFqzMly22Jvio95kah8DkbJDwNRt6Iae7UNLDOZFTu7i4ASeNfVXWrI2sfEd+cIu7MuQRuHscJeRE1NlJ3CH9GXjwi5yN+ycV4O1JZrF3FkpQ21rl60kZGR2x7IOClbs6WmgXhQZ2LQThrzsr/SZe9yKeOdJojcp4nbzxLOssD2qdV+FdwZUV4OqR5BqvehR/D+BZCjr5tvCV6NanEmtbRXBoM3wlg==";
        Map<String, Object> result = new JSBClient().parseResponseContent(response);
        assertEquals(paySign, result.get("paySign"));
    }

    /**
     * 解析多条响应数据
     */
    public void testParseResponseContentOfMultiRecord() {
        Map<String, Object> result = new JSBClient().parseResponseContent(multiRecordResponse);
        assertNotNull(result.get(RECORD_LIST));
    }

    /**
     * 测试用户信息查询响应解析
     */
    public void testQueryLybUserInfoResponse() {
        // 模拟用户信息查询成功响应
        String userInfoResponse = "returnCode=00&returnMsg=查询成功&userStatus=0&userNo=JSB20241230001&avalaibleAcctNo=6228480402637874213&frozenAcctNo=6228480402637874214&extFld1=测试扩展字段1&extFld2=测试扩展字段2&extFld3=测试扩展字段3&partnerId=ab53d34b404b4f19afe1e2c559b3ee30&respBizDate=20241230&respCode=000000&respMsg=交易成功&signType=RSA&sign=TestSignatureValue123456789";

        Map<String, Object> result = new JSBClient().parseResponseContent(userInfoResponse);

        // 验证返回码和消息
        assertEquals("00", result.get("returnCode"));
        assertEquals("查询成功", result.get("returnMsg"));

        // 验证用户状态和账号信息
        assertEquals("0", result.get("userStatus"));
        assertEquals("JSB20241230001", result.get("userNo"));
        assertEquals("6228480402637874213", result.get("avalaibleAcctNo"));
        assertEquals("6228480402637874214", result.get("frozenAcctNo"));

        // 验证扩展字段
        assertEquals("测试扩展字段1", result.get("extFld1"));
        assertEquals("测试扩展字段2", result.get("extFld2"));
        assertEquals("测试扩展字段3", result.get("extFld3"));

        // 验证系统级字段
        assertEquals("ab53d34b404b4f19afe1e2c559b3ee30", result.get("partnerId"));
        assertEquals("20241230", result.get("respBizDate"));
        assertEquals("000000", result.get("respCode"));
        assertEquals("交易成功", result.get("respMsg"));
    }

    /**
     * 测试用户信息查询失败响应解析
     */
    public void testQueryLybUserInfoFailureResponse() {
        // 模拟用户信息查询失败响应
        String userInfoFailureResponse = "returnCode=01&returnMsg=用户不存在&userStatus=&userNo=&avalaibleAcctNo=&frozenAcctNo=&extFld1=&extFld2=&extFld3=&partnerId=ab53d34b404b4f19afe1e2c559b3ee30&respBizDate=20241230&respCode=000000&respMsg=交易成功&signType=RSA&sign=TestSignatureValue123456789";

        Map<String, Object> result = new JSBClient().parseResponseContent(userInfoFailureResponse);

        // 验证返回码和消息
        assertEquals("01", result.get("returnCode"));
        assertEquals("用户不存在", result.get("returnMsg"));

        // 验证用户状态和账号信息为空
        assertEquals("", result.get("userStatus"));
        assertEquals("", result.get("userNo"));
        assertEquals("", result.get("avalaibleAcctNo"));
        assertEquals("", result.get("frozenAcctNo"));

        // 验证扩展字段为空
        assertEquals("", result.get("extFld1"));
        assertEquals("", result.get("extFld2"));
        assertEquals("", result.get("extFld3"));
    }

    /**
     * 测试 queryLybUserInfo 方法调用
     * 注意：这是一个模拟测试，实际调用需要真实的服务器环境和密钥
     */
    public void testQueryLybUserInfoMethod() throws MpayException {
        JSBClient client = new JSBClient();

        // 测试参数
        String requestUrl = "https://epaytest.jsbchina.cn:9999/eis/merchant/merchantServices.htm";
        String partnerBuyerId = "test_user_001";
        String extFld1 = "扩展字段1";
        String extFld2 = "扩展字段2";
        String extFld3 = "扩展字段3";
        String sqbPrivateKey = "test_private_key";
        String jsbPublicKey = "test_public_key";

        try {
            // 由于这是单元测试环境，实际调用会失败，但我们可以验证方法存在且参数正确
            client.queryLybUserInfo(requestUrl, partnerBuyerId, extFld1, extFld2, extFld3, sqbPrivateKey, jsbPublicKey);
        } catch (Exception e) {
            // 预期会有网络异常或签名异常，这是正常的，因为我们使用的是测试密钥
            assertTrue("方法调用应该抛出异常（网络或签名相关）",
                e instanceof MpayApiNetworkError || e instanceof MpayException);
        }
    }

    /**
     * 测试 queryLybUserInfo 方法参数验证
     */
    public void testQueryLybUserInfoWithNullParameters() {
        JSBClient client = new JSBClient();

        String requestUrl = "https://epaytest.jsbchina.cn:9999/eis/merchant/merchantServices.htm";
        String sqbPrivateKey = "test_private_key";
        String jsbPublicKey = "test_public_key";

        try {
            // 测试必填参数为空的情况
            client.queryLybUserInfo(requestUrl, null, null, null, null, sqbPrivateKey, jsbPublicKey);
        } catch (Exception e) {
            // 预期会有异常
            assertTrue("应该抛出异常", e instanceof Exception);
        }

        try {
            // 测试只有必填参数的情况
            client.queryLybUserInfo(requestUrl, "test_user_001", null, null, null, sqbPrivateKey, jsbPublicKey);
        } catch (Exception e) {
            // 预期会有网络异常或签名异常
            assertTrue("方法调用应该抛出异常（网络或签名相关）",
                e instanceof MpayApiNetworkError || e instanceof MpayException);
        }
    }


}