package com.wosai.mpay.api.cmbapp;

import cn.hutool.core.util.RandomUtil;
import com.google.common.base.Objects;
import com.google.common.collect.Maps;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.pantheon.util.MapUtil;
import org.apache.commons.lang.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class CmbAppClientTest {


    private CmbAppClient client = new CmbAppClient();

    private String url = "https://sandbox.cdcc.cmbchina.com/AccessGateway/transIn/{funcName}.json";

    private String gmUrl = "https://az2openserviceccuat.gm.cmburl.cn/AccessGateway/transIn/{funcName}.json";

    private String merPrivateKey = "41BB99C724D960C799226940E12B5A2935FF0020952A1F887F5A3A1A54A2221D";

    private String cmbPublicKey = "04aa81ff06f2840c10ef3771b5670c8c2a31fd431b2915b64ebe1b11ad55d818370c27e5827a64a10322932a427e21e88b1021d7a1b332eea3cf998c4f4bdea712";

    private String aid = "f572d323c108444a9fe74b5f931ffeb5";

    private String mid = "352cd40b40303e2daa4e39779e10915b";

    private String merNo = "999901620";

    private String strNo = "00001";

    public static final Logger logger = LoggerFactory.getLogger(CmbAppClient.class);

    /**
     * 先支付，后查单
     */
    @Test
    public void payAndQuery() throws Exception {
        String orderNo = "T2022081900007";
        yummyOrderPay0(orderNo);
        yummyOrderQuery0(orderNo);
    }

    //T2022071300008
    //T2022071300009
    /**
     * 订单创建
     *
     * @throws Exception
     */
    @Test
    public void yummyOrderPay() throws Exception {
        yummyOrderPay0("T2022082600003");
    }

    private void yummyOrderPay0(String orderNo) throws Exception {
        RequestBuilder builder = new RequestBuilder("yummyOrderPay");
        Map<String, String> commonMap = buildCommonParamMap();
        commonMap.forEach(builder::set);
        builder.set(BusinessFields.MER_ORDER_NO, orderNo)
                .set(BusinessFields.MER_NO, merNo)
                .set(BusinessFields.STR_NO, strNo)
                .set(BusinessFields.AMOUNT, "1")
                .set(BusinessFields.TAG_CODE, "6255559116854227718")
        ;
        Map<String, Object> result = client.payCall(buildGmUrl("yummyOrderPay"),
                merPrivateKey, cmbPublicKey, CERT, builder);
        logger.debug("yummyOrderPay result:{}", JsonUtil.objectToJsonString(result));
    }

    /**
     * 订单查询
     *
     * @throws Exception
     */
    @Test
    public void yummyOrderQuery() throws Exception {
        yummyOrderQuery0("T2022081900004");
    }

    private void yummyOrderQuery0(String orderNo) throws Exception {
        RequestBuilder builder = new RequestBuilder("yummyOrderQuery");
        Map<String, String> commonMap = buildCommonParamMap();
        commonMap.forEach(builder::set);
        builder.set(BusinessFields.MER_ORDER_NO, orderNo)
                //.set(BusinessFields.CMB_ORDER_NO, "2206301542398936583503872")
                .set(BusinessFields.MER_NO, merNo)
                .set(BusinessFields.STR_NO, strNo)
        ;
        Map<String, Object> result = client.call(buildUrl("yummyOrderQuery"), merPrivateKey, builder);
        logger.debug("yummyOrderQuery result:{}", JsonUtil.objectToJsonString(result));
    }

    /**
     * 订单撤销
     *
     * @throws Exception
     */
    @Test
    public void yummyOrderCancel() throws Exception {
        RequestBuilder builder = new RequestBuilder("yummyOrderCancel");
        Map<String, String> commonMap = buildCommonParamMap();
        commonMap.forEach(builder::set);
        builder.set(BusinessFields.MER_ORDER_NO, "T2022081900002")
                .set(BusinessFields.MER_NO, merNo)
                .set(BusinessFields.STR_NO, strNo)
                //.set(BusinessFields.OPERATOR, "sqb")
        ;
        Map<String, Object> result = client.call(buildUrl("yummyOrderCancel"), merPrivateKey, builder);
        Assert.assertTrue(Objects.equal(result.get(ResponseFields.RESP_CODE), CmbAppConstants.RESP_CODE_SUCCESS));
        
        String reverseResult = MapUtil.getString(MapUtil.getMap(result, ResponseFields.BODY), ResponseFields.REVERSE_RESULT);
        
        Assert.assertTrue(Objects.equal(reverseResult, CmbAppConstants.REFUND_RESULT_SUCCESS));
    }

    /**
     * 申请退款
     *
     * @throws Exception
     */
    @Test
    public void yummyOrderRefund() throws Exception {
        RequestBuilder builder = new RequestBuilder("yummyOrderRefund");
        Map<String, String> commonMap = buildCommonParamMap();
        commonMap.forEach(builder::set);
        builder.set(BusinessFields.MER_ORDER_NO, "T2022071300008")
                //.set(BusinessFields.CMB_ORDER_NO, "2206301542398936583503872")
                .set(BusinessFields.MER_REFUND_ORDER_NO, "tT2022071300008")
                .set(BusinessFields.MER_NO, merNo)
                .set(BusinessFields.STR_NO, strNo)
                .set(BusinessFields.TOTAL_AMT, "1")
                .set(BusinessFields.REFUND_AMT, "1")
                .set(BusinessFields.REFUND_DESC, "顾客主动退款")
        ;
        client.call(buildUrl("yummyOrderRefund"), merPrivateKey, builder);
    }

    /**
     * 退款查询
     *
     * @throws Exception
     */
    @Test
    public void yummyRefundQuery() throws Exception {
        RequestBuilder builder = new RequestBuilder("yummyRefundQuery");
        Map<String, String> commonMap = buildCommonParamMap();
        commonMap.forEach(builder::set);
        builder.set(BusinessFields.MER_ORDER_NO, "T2022071300008")
                // .set(BusinessFields.CMB_ORDER_NO, "")
                .set(BusinessFields.MER_REFUND_ORDER_NO, "tT2022071300008")
                .set(BusinessFields.MER_NO, merNo)
                .set(BusinessFields.STR_NO, strNo)
        ;
        client.call(buildUrl("yummyRefundQuery"), merPrivateKey, builder);
    }

    private String buildUrl(String funName) {
        return StringUtils.replace(url, "{funcName}", funName);
    }

    private String buildGmUrl(String funName) {
        return StringUtils.replace(gmUrl, "{funcName}", funName);
    }

    private Map<String, String> buildCommonParamMap() {
        return MapUtils.hashMap(
                BusinessFields.MID, mid,
                BusinessFields.AID, aid,
                BusinessFields.DATE, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")), //格式yyyyMMddHHmmss
                BusinessFields.RANDOM, RandomUtil.randomString(32)
        );
    }

    private static final String CERT = "-----BEGIN CERTIFICATE-----\n" +
            "MIIDKDCCAsygAwIBAgIFQDkWl3cwDAYIKoEcz1UBg3UFADBhMQswCQYDVQQGEwJD\n" +
            "TjEwMC4GA1UECgwnQ2hpbmEgRmluYW5jaWFsIENlcnRpZmljYXRpb24gQXV0aG9y\n" +
            "aXR5MSAwHgYDVQQDDBdDRkNBIEFDUyBURVNUIFNNMiBPQ0EzMTAeFw0yMjA1MDkw\n" +
            "ODI2MTFaFw0yNTA1MDkwODI2MTFaMHcxCzAJBgNVBAYTAkNOMRIwEAYDVQQIDAnl\n" +
            "ub/kuJznnIExEjAQBgNVBAcMCea3seWcs+W4gjEnMCUGA1UECgwe5oub5ZWG6ZO2\n" +
            "6KGM6IKh5Lu95pyJ6ZmQ5YWs5Y+4MRcwFQYDVQQDDA4qLmdtLmNtYnVybC5jbjBZ\n" +
            "MBMGByqGSM49AgEGCCqBHM9VAYItA0IABMJfkQpCkjS7SPC7+GFWqRgY6icE6dSm\n" +
            "MrL4YhV7pfXK6D+qAHHW80T//dl9ewLzlc+SlzBh/vsUhowRT4kKNoqjggFXMIIB\n" +
            "UzAMBgNVHRMBAf8EAjAAMD8GCCsGAQUFBwEBBDMwMTAvBggrBgEFBQcwAYYjaHR0\n" +
            "cDovL29jc3B0ZXN0LmNmY2EuY29tLmNuOjgwL29jc3AwGQYDVR0RBBIwEIIOKi5n\n" +
            "bS5jbWJ1cmwuY24wDgYDVR0PAQH/BAQDAgbAMB0GA1UdDgQWBBQtodmdFJWAiJGJ\n" +
            "71WoaLDftf2N6jATBgNVHSUEDDAKBggrBgEFBQcDATAfBgNVHSMEGDAWgBQEx7z5\n" +
            "WQFpPow0NiBiGDzevLW7DDBIBgNVHSAEQTA/MD0GCGCBHIbvKgEEMDEwLwYIKwYB\n" +
            "BQUHAgEWI2h0dHA6Ly93d3cuY2ZjYS5jb20uY24vdXMvdXMtMTQuaHRtMDgGA1Ud\n" +
            "HwQxMC8wLaAroCmGJ2h0dHA6Ly8yMTAuNzQuNDIuMy9PQ0EzMS9TTTIvY3JsMTcx\n" +
            "LmNybDAMBggqgRzPVQGDdQUAA0gAMEUCIDR2MBI//lNBWbFQ+GVnutzOBGlqDlXc\n" +
            "nGJkIpHcph+zAiEA3jM0bBSReOqnFZgmy9IFFsYcf85OpQ8Ok7tiFFArOOE=\n" +
            "-----END CERTIFICATE-----\n";
}