package com.wosai.mpay.api.yeahpay;

import com.wosai.mpay.api.yeahpay.constants.YeahPayProtocolFieldsConstants;
import com.wosai.mpay.api.yeahpay.constants.YeahPayRequestFieldsConstants;
import com.wosai.mpay.api.yeahpay.constants.YeahPayUrlPathConstants;
import com.wosai.mpay.api.yeahpay.enums.YeahPayJsPayFlagEnum;
import com.wosai.mpay.api.yeahpay.enums.YeahPayPaywayEnum;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.UUIDGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className YeahPayTest
 * @description:
 * @create: 2025-06-20 11:09
 **/
public class YeahPayClientTest {


    public static final Logger logger = LoggerFactory.getLogger(YeahPayClientTest.class);

    public YeahPayClientTest() {}

    // appId
    private static final String APP_ID = "00460711";
    // apiKey
    private static final String API_KEY = "";
    //测试环境访问地址
    private static final String REQUEST_URL = "https://t-channel.lepass.cn/gw/abroad-business-acceptance-open-api";

    private static final String MERCHANT_ID = "123";

    private static final String AMOUNT = "0.01";

    private static final String SUB_APP_ID = "1";

    private static final String SUB_OPEN_ID = "2";

    /**
     * CSB
     *
     * @throws Exception
     */
    private static void csb(String thirdOrderId) throws Exception {
        YeahPayRequestBuilder requestBuilder = new YeahPayRequestBuilder(APP_ID, YeahPayUrlPathConstants.UNIFIED_ORDER);
        //构建body请求参数
        requestBuilder.setBodyField(YeahPayRequestFieldsConstants.UnifiedOrder.PAY_WAY, YeahPayPaywayEnum.WXZF.getCode());
        requestBuilder.setBodyField(YeahPayRequestFieldsConstants.UnifiedOrder.MERCHANT_ID, MERCHANT_ID);
        requestBuilder.setBodyField(YeahPayRequestFieldsConstants.UnifiedOrder.THIRD_ORDER_ID, thirdOrderId);
        requestBuilder.setBodyField(YeahPayRequestFieldsConstants.UnifiedOrder.AMOUNT, AMOUNT);
        requestBuilder.setBodyField(YeahPayRequestFieldsConstants.UnifiedOrder.CURRENCY, YeahPayProtocolFieldsConstants.CURRENCY_SGD);
        requestBuilder.setBodyField(YeahPayRequestFieldsConstants.UnifiedOrder.JSPAY_FLAG, YeahPayJsPayFlagEnum.JSAPI.getCode());
        requestBuilder.setBodyField(YeahPayRequestFieldsConstants.UnifiedOrder.SUB_APP_ID, SUB_APP_ID);
        requestBuilder.setBodyField(YeahPayRequestFieldsConstants.UnifiedOrder.SUB_OPEN_ID, SUB_OPEN_ID);
        requestBuilder.setBodyField(YeahPayRequestFieldsConstants.UnifiedOrder.SUB_APP_ID, SUB_APP_ID);


        Map<String, Object> result = new YeahPayClient().call(REQUEST_URL, requestBuilder, API_KEY);
        System.out.println(JsonUtil.objectToJsonString(result));

        /* 响应结果示例
         */
    }


    public static void main(String[] args) {
        try {

            String uuid = UUIDGenerator.getUUID();

            csb(uuid);

        } catch (Exception e) {


            System.out.println("执行失败, 错误如下:");
            e.printStackTrace();
        }
    }
}
