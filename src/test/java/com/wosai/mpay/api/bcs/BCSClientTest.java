package com.wosai.mpay.api.bcs;

import com.wosai.mpay.exception.MpayApiNetworkError;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

public class BCSClientTest {
    private static final Logger log = LoggerFactory.getLogger(BCSClientTest.class);
    // 使用UAT测试环境配置
    static String requestUrl = "https://test.bankofchangsha.com:1443/uatApiGw/V1/uaps/qrcode/pre-order";
    static String appId = "612d11fc-17fc******4ef4aac7a9";
    static String privateKey = "5cf391d0229fe42******d00458a84590f4";
    static String publicKey = "04EA83BB7*******0CB7706E9B";
    static String sm4Key = "A60B322******62C56301";
    static String sm4Iv = "7F8C5E50698******43084C069";
    static String mchtNo = "461430153312QA1"; // 需要替换为实际商户号
    /**
     * 测试统一下单 - 微信二维码主扫
     */
    public static void testWeChatQrPay() {
        try {
            BCSClient client = new BCSClient();

            String tradeNo = "2024040317514932251120";
            
            // 构建请求参数
            List<Map<String, Object>> orderModeList = new ArrayList<>();
            Map<String, Object> orderMode = new HashMap<>();
            orderMode.put("type", "TRANS");
            orderMode.put("amount", "1.00");
            orderMode.put("payMethod", "UnionAtPay");
            orderMode.put("openId", "2088132605270576");
//            orderMode.put("openId", "oThRp5CkDw3X2q2EQP31PwB8UjsQ");
//            orderMode.put("subAppId", "wxccbcac9a3ece5112");
            orderMode.put("deviceInfo", "WEB");
            orderModeList.add(orderMode);
            BCSRequestBuilder builder = new BCSRequestBuilder();
            builder.setBizType(BCSConstants.BIZ_TYPE_ALI_APP)
                   .setCurrency(BCSConstants.CURRENCY_CNY)
                   .setOrderAmount(new BigDecimal("1.00"))
                   .setGoods("测试商品")
                   .setTradeNo(tradeNo)
                   .setTradeType("WEB")
                   .setTradeTime(getCurrentDateTime())
                    .setOrderMode(orderModeList);
            // 设置业务请求头
            builder.setVersion(BCSProtocolFields.DEFAULT_VERSION)  // 设置默认版本号
                    .setMchtNo("461430153312QA1") // 设置商户号
                    .setCurrentDateTime()  // 设置日期时间
                    .setPlatform("461430153312QA1") // 无服务上ID，送商户号
                    .setSceneId(BCSProtocolFields.DEFAULT_SCENE_ID);  // 设置默认场景编号
            builder.setBusinessSeqNo(tradeNo);
            builder.setTxnType("BG0006");
            builder.setRequestIp("127.0.0.1");
            builder.setMac("00:00:00:00:00:00");

            Map<String, Object> params = builder.build();
            // 调用接口
            Map<String, Object> response = client.call(requestUrl, params, appId,
                                                      privateKey, publicKey, sm4Key, sm4Iv);
            System.out.println(response);
            
        } catch (MpayException | MpayApiNetworkError e) {
            System.out.println(2);
            System.out.println(e);
            log.error("测试微信二维码主扫失败", e);
        }
    }

    /**
     * 测试统一下单 - 支付宝被扫
     */
    public static void testAlipayMicroPay() {
        try {
            BCSClient client = new BCSClient();
            String tsn = "7894259294172773282391103051786017"; // TODO 每测试一次都需要修改
            String authCode = "62217871492181844662";  // TODO 每测试一次都需要修改

            // 构建请求参数
            BCSRequestBuilder requestBuilder = new BCSRequestBuilder();

            requestBuilder.setVersion(BCSProtocolFields.DEFAULT_VERSION)  // 设置默认版本号
                    .setMchtNo(mchtNo) // 设置商户号
                    .setCurrentDateTime()  // 设置日期时间
                    .setPlatform(mchtNo) // 无服务上ID，送商户号
                    .setSceneId(BCSProtocolFields.DEFAULT_SCENE_ID);  // 设置默认场景编号
            // 商户交易流水号
            requestBuilder.setBusinessSeqNo(tsn);
            // 设置交易代码
            requestBuilder.setTxnType("BG0006");
            // 设置请求 IP
            String terminalIp = "127.0.0.1";
            requestBuilder.setRequestIp(terminalIp);
            // 服务器 MAC 地址
            requestBuilder.setMac("00:00:00:00:00:00");
            // 设置业务报文-请求体
            // 设置业务类型
//            requestBuilder.setBizType("ALI_SCAN");  // TODO 微信需替换
            requestBuilder.setBizType("UN_SCAN");
            // 设置币种
            requestBuilder.setCurrency("CNY");
            // 设置订单金额
            String amount = StringUtils.cents2yuan(1);
            BigDecimal decimalAmount = new BigDecimal(amount);
            requestBuilder.setOrderAmount(decimalAmount);
            // 设置商品信息
            String subject = "测试商品";
            requestBuilder.set(BCSBusinessFields.GOODS, subject);
            // 设置商户订单流水号
            requestBuilder.setTradeNo(tsn);
            // 设置交易类型
            requestBuilder.setTradeType(BCSConstants.DEVICE_INFO_WEB);
            // 设置交易时间
            requestBuilder.setTradeTime(getCurrentDateTime());
            // 设置OrderMode中的通用字段值
            // 设置类型。
            requestBuilder.setType(BCSConstants.ORDER_MODE_TYPE_TRANS);
            // 设置金额
            requestBuilder.setAmount(decimalAmount);
            // 设置支付方式
            requestBuilder.setPayMethod("ScanCode");
            // 设置设备号
            requestBuilder.setDeviceInfo("461430100003F9P");
            // 设置付款码
            requestBuilder.setAuthCode(authCode);
            // 设置经纬度  参考GuotongServiceProvider
            requestBuilder.setLatitude("31.230525");
            requestBuilder.setLongitude("121.473667");

            Map<String, Object> params = requestBuilder.build();
            // 调用接口
            Map<String, Object> response = client.call(requestUrl, params, appId,
                                                      privateKey, publicKey, sm4Key, sm4Iv);
            log.info("request: {}", params);
            log.info("response: {}", response);
            
        } catch (MpayException | MpayApiNetworkError e) {
            log.error("测试支付宝被扫失败", e);
        }
    }


    /**
     * 获取当前日期时间
     */
    private static String getCurrentDateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat(BCSConstants.ORDER_TIME_FORMAT);
        return sdf.format(new Date());
    }


    // 二维码预下单
    public static void buildPaymentRequest() {
        Map<String, Object> dataMap = new HashMap<>();
        Map<String, Object> bizContentMap = new HashMap<>();

        String tsn = "789425929417277328239110305172232"; // TODO 每测试一次都需要修改

        // 构建bizContent内容
        bizContentMap.put("tradeNo", tsn);
        bizContentMap.put("tradeType", "SDK");
        bizContentMap.put("currency", "CNY");
        bizContentMap.put("callbackUrl", "https://ttest.et0731.com/api/holds/notify/index/compid/10085/type/BcsPay.html");
        bizContentMap.put("orderAmount", 0.01);
        bizContentMap.put("goods", "旅游产品");
        bizContentMap.put("tradeTime", "20240123110417");

        // 构建Data内容
        dataMap.put("businessSeqNo", tsn);
        dataMap.put("version", "1.0");
        dataMap.put("mchtNo", mchtNo);
        dataMap.put("clientDate", "20240123");
        dataMap.put("clientTime", "110417");
        dataMap.put("platform", mchtNo);
        dataMap.put("sceneId", "00");
        dataMap.put("mac", "00:16:3e:10:74:78");
        dataMap.put("requestIp", "**************");
        dataMap.put("txnType", "BG0017");
        dataMap.put("bizContent", bizContentMap);

        // 构建外层Map
        Map<String, Object> params = new HashMap<>();
        params.put("Data", dataMap);

        // 调用接口
        try {
            BCSClient client = new BCSClient();
            Map<String, Object> response = client.call(requestUrl, params, appId,
                    privateKey, publicKey, sm4Key, sm4Iv);
            System.out.println(response);
        } catch (Exception e){

        }
    }

    /**
     * 主测试方法
     */
    public static void main(String[] args) {
        buildPaymentRequest();
//        log.info("--- 测试微信二维码主扫 ---");
//        testWeChatQrPay();

//        log.info("--- 测试支付宝被扫 ---");
//        testAlipayMicroPay();

//        // 获取当前时间戳（毫秒）
//        long currentTimestamp = System.currentTimeMillis();
//
//        // 添加30秒（30 * 1000毫秒）
//        long futureTimestamp = currentTimestamp + 30 * 1000;
//
//        System.out.println("30秒后的时间戳: " + futureTimestamp);

    }
}
