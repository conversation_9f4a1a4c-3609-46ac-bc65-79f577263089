package com.wosai.mpay.api.xzx;

import cn.hutool.core.lang.UUID;
import com.wosai.mpay.api.xzx.constants.XZXMethodFieldsConstants;
import com.wosai.mpay.api.xzx.constants.XZXProtocolFieldsConstants;
import com.wosai.mpay.api.xzx.constants.XZXRequestFieldsConstants;
import com.wosai.mpay.api.xzx.enums.XZXDevIdEnum;
import com.wosai.mpay.api.xzx.enums.XZXPayPrdCodeEnum;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.SafeSimpleDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR>
 * @description 新中新请求测试
 * @date 2025/4/23
 */
public class XZXClientTest {


    /**
     * 日期格式
     */
    private static final String DATE_SIMPLE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 应用参数中日期时间格式
     */
    private static final String DATETIME_REQUEST_FORMAT = "yyyyMMddHHmmss";

    /**
     * 应用参数中日期格式
     */
    private static final String DATE_REQUEST_FORMAT = "yyyyMMdd";


    public static final Logger logger = LoggerFactory.getLogger(XZXClientTest.class);

    public XZXClientTest() {}

    private static final SafeSimpleDateFormat dateSimpleFormat = new SafeSimpleDateFormat(DATE_SIMPLE_FORMAT);

    private static final SafeSimpleDateFormat datetimeRequestFormat = new SafeSimpleDateFormat(DATETIME_REQUEST_FORMAT);

    private static final SafeSimpleDateFormat dateRequestFormat = new SafeSimpleDateFormat(DATE_REQUEST_FORMAT);

    //公钥
    private static final String publicKey = "";
    //私钥
    private static final String privateKey = "";
    //测试环境访问地址
    private static final String REQUEST_URL = "https://ykt.jzmu.edu.cn/api/v2/gateway";
    //appKey
    private static final String appKey = "MWMwZGNlMmUtZjM4MS00MTRhLWE4MDctYTQ2NWFkOTQ1OTVm";
    //秘钥
    private static final String appSecret = "";
    //学工号
    private static final String sno = "fanchao";
    //测试商户号
    private static final String mercacc = "1000000";
    //openid
    private static final String openid = "ofDgL0dZw_1Ougj7naEB8Izfnvjk";

    /**
     * 查询token
     *
     * @throws Exception
     */
    private static void queryToken() throws Exception {
        XZXRequestBuilder requestBuilder = new XZXRequestBuilder();
        //构建系统级请求参数
        requestBuilder.set(XZXProtocolFieldsConstants.TIMESTAMP, dateSimpleFormat.format(new Date()));
        requestBuilder.set(XZXProtocolFieldsConstants.FORMAT, XZXProtocolFieldsConstants.DEFAULT_APPLICATION_FORMAT);
        requestBuilder.set(XZXProtocolFieldsConstants.APP_KEY, appKey);
        requestBuilder.set(XZXProtocolFieldsConstants.ACCESS_TOKEN, XZXProtocolFieldsConstants.DEFAULT_ACCESS_TOKEN);
        requestBuilder.set(XZXProtocolFieldsConstants.API_VERSION, XZXProtocolFieldsConstants.DEFAULT_API_VERSION);
        requestBuilder.set(XZXProtocolFieldsConstants.SIGN_METHOD, XZXProtocolFieldsConstants.DEFAULT_SIGN_METHOD);

        //构建应用级请求参数
        Map<String, Object> params = new HashMap<>();
        params.put(XZXRequestFieldsConstants.APP_KEY, appKey);

        requestBuilder.set(XZXProtocolFieldsConstants.APPLICATION_PARAMS_KEY, params);

        Map<String, Object> result = new XZXClient().call(REQUEST_URL, XZXMethodFieldsConstants.METHOD_API_TOKEN,  requestBuilder.build(), appSecret, privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));

        /* 响应结果示例
        {
    "respCode": "0000",
    "respInfo": "处理成功",
    "obj": {
        "access_token": "eyJ0eXAiOiJKV1QiLCJraWQiOiIyNTA0MjMwMDAwNjUyMzYyIiwiZXhwIjoiMTc0NTQwNDMzODY5MCIsImFsZyI6IlJTNTEyIn0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TFqLCPt8AxUpVDAdJtkoey1P9zulCoa3BAY9x-jCnkItFKVjX_1SWT-g_8FuOKvBO9hqM7WL4nW4O_DCJmm8-O6wK6Ln5_S8WL21aAczclp_kk3075JMsusfihhc2d7UWN7c-Y8VnaG7NmJG8A3_muPOclzjROo_7eCF8C08zS7C1TvX-NPeEsNzIXQ1b9BO6-hnt8EpjF5y1hT7BSvgvlCKQqH6mrXY1mqzsuXN52wS42stJTyBQgwU0NQJ8v7JcCX3oIE3aNTqAHJa06RY2oD3InZUmZmS6w16xCKdaKQBnl9pKWRzgIMYolAW3TYEGJW301dJqXx1QHIulFGd0Q",
        "expires_in": "2400"
    },
    "errcode": "0000"
}

         */
    }

    /**
     * 商户下单
     *
     * @throws Exception
     */
    private static void apply(String outTradeNo) throws Exception {
        XZXRequestBuilder requestBuilder = new XZXRequestBuilder();
        Date now = new Date();
        Date timeOut = new Date(now.getTime() + 1000 * 60 * 4);

        //构建系统级请求参数
        requestBuilder.set(XZXProtocolFieldsConstants.TIMESTAMP, dateSimpleFormat.format(now));
        requestBuilder.set(XZXProtocolFieldsConstants.FORMAT, XZXProtocolFieldsConstants.DEFAULT_APPLICATION_FORMAT);
        requestBuilder.set(XZXProtocolFieldsConstants.APP_KEY, appKey);
        requestBuilder.set(XZXProtocolFieldsConstants.ACCESS_TOKEN, getAccessToken());
        requestBuilder.set(XZXProtocolFieldsConstants.API_VERSION, XZXProtocolFieldsConstants.DEFAULT_API_VERSION);
        requestBuilder.set(XZXProtocolFieldsConstants.SIGN_METHOD, XZXProtocolFieldsConstants.DEFAULT_SIGN_METHOD);

        //构建应用级请求参数
        String nowStr = datetimeRequestFormat.format(now);

        Map<String, Object> goodsParams = new HashMap<>();
        goodsParams.put(XZXRequestFieldsConstants.GOODS_ID, "2000000");
        goodsParams.put(XZXRequestFieldsConstants.GOODS_NAME, "测试商品_" + nowStr);
        goodsParams.put(XZXRequestFieldsConstants.QUANTITY, 1);
        goodsParams.put(XZXRequestFieldsConstants.PRICE, 1);
        goodsParams.put(XZXRequestFieldsConstants.GOODS_CATEGORY, "测试类目");
        goodsParams.put(XZXRequestFieldsConstants.BODY, "商品描述");
        goodsParams.put(XZXRequestFieldsConstants.SHOW_URL, "https://www.shouqianba.com/");

        Map<String, Object> extendParams = new HashMap<>();
        extendParams.put(XZXRequestFieldsConstants.OPEN_ID, openid);

        Map<String, Object> payParams = new HashMap<>();
        payParams.put(XZXRequestFieldsConstants.MCH_ACCT_ID, mercacc);
        payParams.put(XZXRequestFieldsConstants.DEV_ID, XZXDevIdEnum.DEV_ID_MINI_PROGRAM.getCode());
        payParams.put(XZXRequestFieldsConstants.OUT_TRADE_NO, outTradeNo);
        payParams.put(XZXRequestFieldsConstants.TRAN_DT, nowStr);
        payParams.put(XZXRequestFieldsConstants.PAY_TIMEOUT, datetimeRequestFormat.format(timeOut));
        payParams.put(XZXRequestFieldsConstants.TRAN_AMT, "1");
        payParams.put(XZXRequestFieldsConstants.BODY, "测试商品_" + nowStr);
        payParams.put(XZXRequestFieldsConstants.GOODS_DETAIL, Arrays.asList(goodsParams));
        payParams.put(XZXRequestFieldsConstants.ATTACH, "附加数据_" + nowStr);
        payParams.put(XZXRequestFieldsConstants.EXTEND_PARAMS, extendParams);

        //设置应用级请求参数
        requestBuilder.set(XZXProtocolFieldsConstants.APPLICATION_PARAMS_KEY, payParams);

        Map<String, Object> result = new XZXClient().call(REQUEST_URL, XZXMethodFieldsConstants.METHOD_PAYMENT_ORDERS_APPLY,  requestBuilder.build(), appSecret, privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));

        /* 响应示例
        {
    "respCode": "0000",
    "respInfo": "处理成功",
    "obj": {
        "mchAcctId": null,
        "custId": "************",
        "empno": null,
        "custMemberId": null,
        "sysCode": 1600000106,
        "devId": "MINI_PROGRAM",
        "outTradeNo": "65a93a0c5bd14ce7986348bf1ea60381",
        "prepayId": "2025042314521300000003549356",
        "transactionId": null,
        "ticket": null,
        "appKey": null,
        "codeUrl": null,
        "tranCode": null,
        "returnUrl": null,
        "tranAmt": 1,
        "bizStatus": "01",
        "transDt": null,
        "systemDt": "**************",
        "extendParams": null,
        "attach": "附加数据_20250423145205",
        "quickJump": null,
        "payPrdCode": null,
        "bankPayOrderNo": null
    },
    "errcode": "0000"
}
         */
    }



    /**
     * 调起收银台
     *
     * @throws Exception
     */
    private static void cashier(String outTradeNo, String prepayId) throws Exception {
        XZXRequestBuilder requestBuilder = new XZXRequestBuilder();
        Date now = new Date();

        //构建系统级请求参数
        requestBuilder.set(XZXProtocolFieldsConstants.TIMESTAMP, dateSimpleFormat.format(now));
        requestBuilder.set(XZXProtocolFieldsConstants.FORMAT, XZXProtocolFieldsConstants.DEFAULT_APPLICATION_FORMAT);
        requestBuilder.set(XZXProtocolFieldsConstants.APP_KEY, appKey);
        requestBuilder.set(XZXProtocolFieldsConstants.ACCESS_TOKEN, getAccessToken());
        requestBuilder.set(XZXProtocolFieldsConstants.API_VERSION, XZXProtocolFieldsConstants.DEFAULT_API_VERSION);
        requestBuilder.set(XZXProtocolFieldsConstants.SIGN_METHOD, XZXProtocolFieldsConstants.DEFAULT_SIGN_METHOD);

        //构建应用级请求参数
        String nowStr = datetimeRequestFormat.format(now);

        Map<String, Object> cashierParams = new HashMap<>();
        cashierParams.put(XZXRequestFieldsConstants.MCH_ACCT_ID, mercacc);
        cashierParams.put(XZXRequestFieldsConstants.OUT_TRADE_NO, outTradeNo);
        cashierParams.put(XZXRequestFieldsConstants.PREPAY_ID, prepayId);
        cashierParams.put(XZXRequestFieldsConstants.QUICK_JUMP, "1");
        cashierParams.put(XZXRequestFieldsConstants.DEV_ID, XZXDevIdEnum.DEV_ID_MINI_PROGRAM.getCode());
        cashierParams.put(XZXRequestFieldsConstants.PAY_PRD_CODE, XZXPayPrdCodeEnum.BANK_WXPAY.getCode());

        //设置应用级请求参数
        requestBuilder.set(XZXProtocolFieldsConstants.APPLICATION_PARAMS_KEY, cashierParams);

        Map<String, Object> result = new XZXClient().call(REQUEST_URL, XZXMethodFieldsConstants.METHOD_PAYMENT_CASHIER,  requestBuilder.build(), appSecret, privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));


        /* 响应示例
        {
    "obj": {
        "acctId": "19999",
        "acctIdx": "19999",
        "attach": "附加数据_20250423164738",
        "bizStatus": "20",
        "chargeId": "2504230300380301560002312327",
        "completed": "0",
        "custId": "************",
        "devId": "MINI_PROGRAM",
        "extendParams": {
            "timeStamp": "**********",
            "package": "prepay_id=wx23164817851824bc84de8365b6ade80001",
            "paySign": "DCB86C8C75799E313745E124EDDA380E",
            "appId": "wxdffbcb6bcd20bb7b",
            "signType": "MD5",
            "returnUrl": "https://ykt.jzmu.edu.cn/payment/charges/frontNotify/2504230300380301560002312327",
            "nonceStr": "69bfbba5870f4d879b3a4261dd3c7cfa"
        },
        "mchAcctId": "1000000",
        "outTradeNo": "53b3c686ceb84625b927274f03235cbc",
        "payPrdCode": "A38",
        "prepayId": "2025042316471300000003549832",
        "refundAmt": "0",
        "sysCode": 1600000106,
        "systemDt": "20250423164741",
        "tranAmt": 1,
        "tranCode": "15",
        "tranJnl": "1539044",
        "transDt": "20250423164738",
        "transactionId": "2025042316481301560003550316"
    },
    "respCode": "6206",
    "respInfo": "处理成功"
}
         */
    }

    /**
     * 订单查询
     *
     * @throws Exception
     */
    private static void queryOrder(String transactionId) throws Exception {
        XZXRequestBuilder requestBuilder = new XZXRequestBuilder();
        Date now = new Date();

        //构建系统级请求参数
        requestBuilder.set(XZXProtocolFieldsConstants.TIMESTAMP, dateSimpleFormat.format(now));
        requestBuilder.set(XZXProtocolFieldsConstants.FORMAT, XZXProtocolFieldsConstants.DEFAULT_APPLICATION_FORMAT);
        requestBuilder.set(XZXProtocolFieldsConstants.APP_KEY, appKey);
        requestBuilder.set(XZXProtocolFieldsConstants.ACCESS_TOKEN, getAccessToken());
        requestBuilder.set(XZXProtocolFieldsConstants.API_VERSION, XZXProtocolFieldsConstants.DEFAULT_API_VERSION);
        requestBuilder.set(XZXProtocolFieldsConstants.SIGN_METHOD, XZXProtocolFieldsConstants.DEFAULT_SIGN_METHOD);

        //构建应用级请求参数
        String nowStr = datetimeRequestFormat.format(now);
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put(XZXRequestFieldsConstants.TRANSACTION_ID, transactionId);

        //设置应用级请求参数
        requestBuilder.set(XZXProtocolFieldsConstants.APPLICATION_PARAMS_KEY, queryParam);

        Map<String, Object> result = new XZXClient().call(REQUEST_URL, XZXMethodFieldsConstants.METHOD_PAYMENT_ORDERS_QUERY,  requestBuilder.build(), appSecret, privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));

        /** 响应示例
         {
         "respCode": "0000",
         "respInfo": "处理成功",
         "obj": {
         "mchAcctId": "1000000",
         "custId": "************",
         "sysCode": 1600000106,
         "outTradeNo": "bbc843d57ea244a3ac7ca70ffa9b1c32",
         "acctId": "19999",
         "acctIdx": "19999",
         "tranJnl": "1519047",
         "tranCode": "15",
         "transactionId": "2025042515091301560003574676",
         "chargeId": "2504250300380301560002335115",
         "prepayId": "2025042515081300000003572852",
         "codeUrl": null,
         "tranAmt": 1,
         "bizStatus": "20",
         "transDt": "**************",
         "systemDt": "**************",
         "returnUrl": null,
         "devId": "MINI_PROGRAM",
         "extendParams": {
         "returnMsg": null,
         "name": "聚合支付",
         "acctId": "19999"
         },
         "attach": "附加数据_**************",
         "payPrdCode": "A38",
         "bankPayOrderNo": null,
         "refundAmt": "0",
         "completed": "0"
         },
         "errcode": "0000"
         }
         */
    }


    /**
     * 取消支付
     *
     * @throws Exception
     */
    private static void cancelPay(String transactionId) throws Exception {
        XZXRequestBuilder requestBuilder = new XZXRequestBuilder();
        Date now = new Date();

        //构建系统级请求参数
        requestBuilder.set(XZXProtocolFieldsConstants.TIMESTAMP, dateSimpleFormat.format(now));
        requestBuilder.set(XZXProtocolFieldsConstants.FORMAT, XZXProtocolFieldsConstants.DEFAULT_APPLICATION_FORMAT);
        requestBuilder.set(XZXProtocolFieldsConstants.APP_KEY, appKey);
        requestBuilder.set(XZXProtocolFieldsConstants.ACCESS_TOKEN, getAccessToken());
        requestBuilder.set(XZXProtocolFieldsConstants.API_VERSION, XZXProtocolFieldsConstants.DEFAULT_API_VERSION);
        requestBuilder.set(XZXProtocolFieldsConstants.SIGN_METHOD, XZXProtocolFieldsConstants.DEFAULT_SIGN_METHOD);

        //构建应用级请求参数
        String nowStr = datetimeRequestFormat.format(now);

        Map<String, Object> params = new HashMap<>();
        params.put(XZXRequestFieldsConstants.TRANSACTION_ID, transactionId);

        //设置应用级请求参数
        requestBuilder.set(XZXProtocolFieldsConstants.APPLICATION_PARAMS_KEY, params);

        Map<String, Object> result = new XZXClient().call(REQUEST_URL, XZXMethodFieldsConstants.METHOD_PAYMENT_ORDERS_CANCEL_PAY,  requestBuilder.build(), appSecret, privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));

        /* 目前暂不支持，提示如下错误
        {"respCode":"0003","respInfo":"AppID不存在，请检查后再试","errcode":"0000"}
         */
    }


    /**
     * 支付退款
     *
     * @throws Exception
     */
    private static void refund(String outTradeNo, String oriTransactionId) throws Exception {
        XZXRequestBuilder requestBuilder = new XZXRequestBuilder();
        Date now = new Date();

        //构建系统级请求参数
        requestBuilder.set(XZXProtocolFieldsConstants.TIMESTAMP, dateSimpleFormat.format(now));
        requestBuilder.set(XZXProtocolFieldsConstants.FORMAT, XZXProtocolFieldsConstants.DEFAULT_APPLICATION_FORMAT);
        requestBuilder.set(XZXProtocolFieldsConstants.APP_KEY, appKey);
        requestBuilder.set(XZXProtocolFieldsConstants.ACCESS_TOKEN, getAccessToken());
        requestBuilder.set(XZXProtocolFieldsConstants.API_VERSION, XZXProtocolFieldsConstants.DEFAULT_API_VERSION);
        requestBuilder.set(XZXProtocolFieldsConstants.SIGN_METHOD, XZXProtocolFieldsConstants.DEFAULT_SIGN_METHOD);

        //构建应用级请求参数
        String nowStr = datetimeRequestFormat.format(now);

        Map<String, Object> params = new HashMap<>();
        params.put(XZXRequestFieldsConstants.MCH_ACCT_ID, mercacc);
        params.put(XZXRequestFieldsConstants.OUT_TRADE_NO, outTradeNo);
        params.put(XZXRequestFieldsConstants.ORI_TRANSACTION_ID, oriTransactionId);
        params.put(XZXRequestFieldsConstants.TRAN_AMT, 1);
        params.put(XZXRequestFieldsConstants.TOTAL_AMT, 1);

        //设置应用级请求参数
        requestBuilder.set(XZXProtocolFieldsConstants.APPLICATION_PARAMS_KEY, params);

        Map<String, Object> result = new XZXClient().call(REQUEST_URL, XZXMethodFieldsConstants.METHOD_PAYMENT_ORDERS_REFUND,  requestBuilder.build(), appSecret, privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));

        /**
         {
         "obj": {
         "acctAmt": 0,
         "sysCode": 1600000106,
         "acctId": null,
         "mchAcctId": "1000000",
         "cardNo": null,
         "transactionId": "2025051414010301560003741705",
         "feeAmt": 0,
         "openCustId": null,
         "extendParams": null,
         "balance": 0,
         "custMemberId": "000000018656",
         "outTradeNo": "7894259294338575",
         "tranAmt": 400,
         "custId": "************",
         "payPrdCode": "A38",
         "bizStatus": "20",
         "cardBal": 0,
         "subsidySeq": null,
         "acctIdx": 19999
         },
         "respInfo": "处理成功",
         "respCode": "0000"
         }
         */
    }


    /**
     * 对账单下载
     *
     * @throws Exception
     */
    private static void download(String dateStr) throws Exception {
        XZXRequestBuilder requestBuilder = new XZXRequestBuilder();
        Date now = new Date();

        //构建系统级请求参数
        requestBuilder.set(XZXProtocolFieldsConstants.TIMESTAMP, dateSimpleFormat.format(now));
        requestBuilder.set(XZXProtocolFieldsConstants.FORMAT, XZXProtocolFieldsConstants.DEFAULT_APPLICATION_FORMAT);
        requestBuilder.set(XZXProtocolFieldsConstants.APP_KEY, appKey);
        requestBuilder.set(XZXProtocolFieldsConstants.ACCESS_TOKEN, getAccessToken());
        requestBuilder.set(XZXProtocolFieldsConstants.API_VERSION, XZXProtocolFieldsConstants.DEFAULT_API_VERSION);
        requestBuilder.set(XZXProtocolFieldsConstants.SIGN_METHOD, XZXProtocolFieldsConstants.DEFAULT_SIGN_METHOD);

        //构建应用级请求参数

        Map<String, Object> params = new HashMap<>();
        params.put(XZXRequestFieldsConstants.MCH_ACCT_ID, mercacc);
        params.put(XZXRequestFieldsConstants.CMP_DATE, dateStr);

        //设置应用级请求参数
        requestBuilder.set(XZXProtocolFieldsConstants.APPLICATION_PARAMS_KEY, params);

        Map<String, Object> result = new XZXClient().call(REQUEST_URL, XZXMethodFieldsConstants.METHOD_PAYMENT_CMPFLIE_DOWNLOAD,  requestBuilder.build(), appSecret, privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));
    }


    /**
     * 交易结果通知
     *
     * @throws Exception
     */
    private static void notify(String outTradeNo, String transactionId, String bizStatus) throws Exception {
        XZXRequestBuilder requestBuilder = new XZXRequestBuilder();
        Date now = new Date();

        //构建系统级请求参数
        requestBuilder.set(XZXProtocolFieldsConstants.TIMESTAMP, dateSimpleFormat.format(now));
        requestBuilder.set(XZXProtocolFieldsConstants.FORMAT, XZXProtocolFieldsConstants.DEFAULT_APPLICATION_FORMAT);
        requestBuilder.set(XZXProtocolFieldsConstants.APP_KEY, appKey);
        requestBuilder.set(XZXProtocolFieldsConstants.ACCESS_TOKEN, getAccessToken());
        requestBuilder.set(XZXProtocolFieldsConstants.API_VERSION, XZXProtocolFieldsConstants.DEFAULT_API_VERSION);
        requestBuilder.set(XZXProtocolFieldsConstants.SIGN_METHOD, XZXProtocolFieldsConstants.DEFAULT_SIGN_METHOD);

        //构建应用级请求参数
        String nowStr = datetimeRequestFormat.format(now);

        Map<String, Object> params = new HashMap<>();
        params.put(XZXRequestFieldsConstants.MCH_ACCT_ID, mercacc);
        params.put(XZXRequestFieldsConstants.OUT_TRADE_NO, outTradeNo);
        params.put(XZXRequestFieldsConstants.TRANSACTION_ID, transactionId);
        params.put(XZXRequestFieldsConstants.TRAN_AMT, 1);
        params.put(XZXRequestFieldsConstants.BIZ_STATUS, bizStatus);

        //设置应用级请求参数
        requestBuilder.set(XZXProtocolFieldsConstants.APPLICATION_PARAMS_KEY, params);

        Map<String, Object> result = new XZXClient().call(REQUEST_URL, XZXMethodFieldsConstants.METHOD_PAYMENT_ORDER_NOTIFY,  requestBuilder.build(), appSecret, privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));
    }

    private static String getAccessToken() {
        return "eyJ0eXAiOiJKV1QiLCJraWQiOiIyNTA0MzAwMDAwNjUzMzgzIiwiZXhwIjoiMTc0NjAwNjgxNDE1MCIsImFsZyI6IlJTNTEyIn0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.nr4VSV0-9sCDmHxP4PBGG4rCLhQ_ehrv0To_kJKgvrSPYiF8eYGuQ5OIB8KsiuoXh-UwjjprDMg1S3O_89k7BX9PURX9sMcZmJGMjWQrMsSKR2cT2SGUz3-zMql_3zv8Gp7IQJ8hcNm_FJnWD49zL26es8BViwRsxS0ruYZ66x2TZPSjTQi53XhCfzyenZvFMXthULtUix9fHCW4XJIsAALwZU8q87iEQ3jvwVnpNmuT887jQBACAYF8Mq7G5My04C5AgcRhT_9dfYW-Wc9xehyhw0cHbr7IKeZbJrTpq_aBBtYmkgOX0EdYNC5_YdYgFsEFM0oSusAVKqu4NBoTBQ";
//        return "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
    }

    private static String getOutTradeNo() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    public static void main(String[] args) throws Exception {
        try {
//            queryToken();

            String outTradeNo = "d7539e7143294c56a8c4bf5b0ca7aaf5";
            String prepayId = "2025043017331300000003631234";

//            String transactionId = "2025042515091301560003574676";
//
//            String outTradeNo = getOutTradeNo();
//            apply(outTradeNo);

            cashier(outTradeNo, prepayId);

//            queryOrder(transactionId);

//            cancelPay(transactionId);

//            String refundOutTradeNo = getOutTradeNo();
//            refund(refundOutTradeNo, outTradeNo);

//            download("20250424");

//            notify(outTradeNo, transactionId, XZXBizStatusEnum.SUCCEED.getCode());

        } catch (Exception e) {
            System.out.println("执行失败, 错误如下:");
            e.printStackTrace();
        }
    }
}

