package com.wosai.mpay.api.macaupass;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wosai.mpay.api.macaupass.constants.MacauPassRequestFieldsConstants;
import com.wosai.mpay.api.macaupass.constants.MacauPassServiceConstants;
import com.wosai.mpay.api.macaupass.enums.MacauPassChannelTypeEnum;
import com.wosai.mpay.api.macaupass.enums.MacauPassCurrencyEnum;
import com.wosai.mpay.api.macaupass.enums.MacauPassPayChannelEnum;
import com.wosai.mpay.api.macaupass.enums.MacauPassProductCodeEnum;
import com.wosai.mpay.api.macaupass.utils.MacauPassUtil;
import com.wosai.mpay.api.macaupass.utils.SignUtil;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.*;
/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className MacauPassClientTest
 * @description: 澳门通client测试
 * @create: 2025-04-28 11:05
 **/
public class MacauPassClientTest {

    public static final Logger logger = LoggerFactory.getLogger(MacauPassClientTest.class);

    public MacauPassClientTest() {}

    // 测试环境 配置
    // 澳门通公钥
    private static final String publicKey = "";
    // 商户私钥
    private static final String privateKey = "";
    // 测试环境访问地址
    private static final String REQUEST_URL = "https://uatopenapi.macaupay.com.mo/masl/umpg/gateway";

    //orgId
    private static final String orgId = "888535311246382";

    // 二级商户id
    private static final String subMerchantId = "888535311246382";

    // 二级商户名称
    private static final String subMerchantName = "新忠誠集團有限公司";

    // 商戶行業代碼
    private static final String subMerchantIndustry = "5311";

    // 门店名称
    private static final String storeName = "筷子基店";

    // 门店ID
    private static final String storeId = "0001";

    // 终端ID
    private static final String terminalId = "T80001";


//    // 生产环境配置，白名单限制本地无法调用
//    // 澳门通公钥
//    private static final String publicKey = "";
//    // 商户私钥
//    private static final String privateKey = "";
//    // 生产环境访问地址
//    private static final String REQUEST_URL = "https://openapi.macaupay.com.mo/masl/umpg/gateway";
//
//    //orgId
//    private static final String orgId = "888085359995324";
//
//    // 二级商户id
//    private static final String subMerchantId = "888085359995324";
//
//    // 二级商户名称
//    private static final String subMerchantName = "生產驗證";
//
//    // 商戶行業代碼
//    private static final String subMerchantIndustry = "5999";
//
//    // 门店名称
//    private static final String storeName = "生產驗證";
//
//    // 门店ID
//    private static final String storeId = "8880853599953240001";
//
//    // 终端ID
//    private static final String terminalId = "T80001";



    // 商品数量
    private static final Integer quantity = 1;

    // 支付金额
    private static final BigDecimal amount = new BigDecimal("0.02");

    // 退款金额
    private static final BigDecimal refundAmount = new BigDecimal("0.02");

    // 费用金额
    private static final BigDecimal feeAmount = new BigDecimal("0");

    private static final String notifyUrl = "https://www.shouqianba.com/";

    // 操作员id
    private static final String operatorId = "SQB0001";


    // 微信小程序id
    private static final String miniAppId = "wxd2f16468474f61b8";

    //openid
    private static final String openid = "ofDgL0dZw_1Ougj7naEB8Izfnvjk";


    /**
     * 条码支付
     *
     * @throws Exception
     */
    private static void mpayTradeSpotpay(String outTransId) throws Exception {
        String service = MacauPassServiceConstants.TRADE_SPOTPAY;
        MacauPassRequestBuilder requestBuilder = new MacauPassRequestBuilder(orgId, service, MacauPassChannelTypeEnum.SYSTEM.getType());

        Map<String, Object> extendParams = new HashMap<>();
        extendParams.put(MacauPassRequestFieldsConstants.SUB_MERCHANT_ID, subMerchantId);
        extendParams.put(MacauPassRequestFieldsConstants.SUB_MERCHANT_NAME, subMerchantName);
        extendParams.put(MacauPassRequestFieldsConstants.SUB_MERCHANT_INDUSTRY, subMerchantIndustry);
        extendParams.put(MacauPassRequestFieldsConstants.STORE_NAME, storeName);
        extendParams.put(MacauPassRequestFieldsConstants.STORE_ID, storeId);
        extendParams.put(MacauPassRequestFieldsConstants.TERMINAL_ID, terminalId);


        //构建应用级请求参数
        requestBuilder.set(MacauPassRequestFieldsConstants.QUANTITY, quantity);
        requestBuilder.set(MacauPassRequestFieldsConstants.TRANS_NAME, "交易名称_"+outTransId);
        requestBuilder.set(MacauPassRequestFieldsConstants.OUT_TRANS_ID, outTransId);
        requestBuilder.set(MacauPassRequestFieldsConstants.CURRENCY, MacauPassCurrencyEnum.MOP.getCode());
        requestBuilder.set(MacauPassRequestFieldsConstants.TRANS_AMOUNT, amount);
        requestBuilder.set(MacauPassRequestFieldsConstants.BUYER_IDENTITY_CODE, "88051303090553289851");

        requestBuilder.set(MacauPassRequestFieldsConstants.IDENTITY_CODE_TYPE, "barcode");
        requestBuilder.set(MacauPassRequestFieldsConstants.TRANS_CREATE_TIME, MacauPassUtil.formatRequestDate(new Date()));
        requestBuilder.set(MacauPassRequestFieldsConstants.NOTIFY_URL, notifyUrl);
        requestBuilder.set(MacauPassRequestFieldsConstants.MEMO, "交易备注_"+outTransId);
        requestBuilder.set(MacauPassRequestFieldsConstants.OPERATOR_ID, operatorId);
        requestBuilder.set(MacauPassRequestFieldsConstants.EXTEND_PARAMS, extendParams);


        Map<String, Object> result = new MacauPassClient().call(REQUEST_URL, service,  requestBuilder.build(), privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));
    }

    /**
     * 交易撤销
     *
     * @throws Exception
     */
    private static void mpayTradeCancel(String outTransId) throws Exception {
        String service = MacauPassServiceConstants.TRADE_CANCEL;
        MacauPassRequestBuilder requestBuilder = new MacauPassRequestBuilder(orgId, service, MacauPassChannelTypeEnum.SYSTEM.getType());

        //构建应用级请求参数
        requestBuilder.set(MacauPassRequestFieldsConstants.OUT_TRANS_ID, outTransId);

        Map<String, Object> result = new MacauPassClient().call(REQUEST_URL, service, requestBuilder.build(), privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));



        /** 响应结果
         {
         "data": {
         "business_type": "pay",
         "buyer_pay_amount": "6.12",
         "coupon_amount": "0",
         "crt_time": "2025-04-28 17:54:34",
         "currency": "MOP",
         "discount_amount": "0.00",
         "merchant_coupon_amt": "0",
         "merchant_discount_amt": "0.00",
         "order_amount": "6.12",
         "out_trans_id": "d65aba06-7eed-4d29-9cd4-442d5f9e9c85",
         "pay_channel_type": "mpay",
         "pay_time": "",
         "platform_coupon_amt": "0",
         "platform_discount_amt": "0.00",
         "receipt_amount": "6.12",
         "result_code": "0000",
         "result_msg": "狀態未知，需要查詢",
         "trans_amount": "6.12",
         "trans_id": "2025042817543410000001",
         "trans_status": "UNKNOW",
         "wallet_no": "108801"
         },
         "is_success": "T",
         "sign_type": "RSA2",
         "sign": "u67VNzs9Gsf85uSQvIai+oVSt+BiapbgikjFXj+ggwlcMtrPWAueemacgQ7T35FH75TToi+SK59eow3HGspvIuy/Ik0ZThEOvdzgKLxzXv4Hzzu7QLV4ML4M2zV7bmm/01dvAbyHKyUKdCuohLmfHD+NMiXI6RE38YQfh0s/uNdTqU+aDrGjNR34/zGOMVqw2te3WxziAEEALKRJcsqKkBk5c+RKIeuu0sjO/oQSIoS+4JLGtz2uCbONHovxvwBXznAibeH83exMFdJ9jQW284aCv4QJhuEMsqXQZ3z+bMWlqUs5z+ZbrzlMlpOFvULPzPaxgEVp4mXndRVmw71yKw=="
         }
         */
    }


    /**
     * 交易退款
     *
     * @throws Exception
     */
    private static void mpayTradeRefund(String outTransId, String outRefundId) throws Exception {
        String service = MacauPassServiceConstants.TRADE_REFUND;
        MacauPassRequestBuilder requestBuilder = new MacauPassRequestBuilder(orgId, service, MacauPassChannelTypeEnum.SYSTEM.getType());

        //构建应用级请求参数
        requestBuilder.set(MacauPassRequestFieldsConstants.OUT_TRANS_ID, outTransId);
        requestBuilder.set(MacauPassRequestFieldsConstants.OUT_REFUND_ID, outRefundId);
        requestBuilder.set(MacauPassRequestFieldsConstants.REFUND_AMOUNT, refundAmount);
        requestBuilder.set(MacauPassRequestFieldsConstants.REFUND_REASON, "退款_" + outTransId);

        Map<String, Object> result = new MacauPassClient().call(REQUEST_URL, service, requestBuilder.build(), privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));

        /** 响应结果
         {
         "data": {
         "cancel_time": "2025-04-28 18:00:36",
         "order_amount": "6.12",
         "out_trans_id": "d65aba06-7eed-4d29-9cd4-442d5f9e9c85",
         "pay_channel_type": "mpay",
         "result_code": "0000",
         "result_msg": "成功",
         "retry_flag": "N",
         "trade_no": "2025042817543400320395",
         "trans_id": "2025042817543410000001"
         },
         "is_success": "T"
         }
         */
    }


    /**
     * 交易查询
     *
     * @throws Exception
     */
    private static void mpayTradeQuery(String outTransId) throws Exception {
        String service = MacauPassServiceConstants.TRADE_QUERY;

        MacauPassRequestBuilder requestBuilder = new MacauPassRequestBuilder(orgId, service, MacauPassChannelTypeEnum.SYSTEM.getType());

        //构建应用级请求参数
        requestBuilder.set(MacauPassRequestFieldsConstants.OUT_TRANS_ID, outTransId);

        Map<String, Object> result = new MacauPassClient().call(REQUEST_URL, service, requestBuilder.build(), privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));

        /** 响应结果
         {
         "data": {
         "business_type": "pay",
         "buyer_login_id": "66***611",
         "buyer_pay_amount": "6.12",
         "buyer_user_id": "00000100052985",
         "coupon_amount": "0",
         "crt_time": "2025-04-28 17:54:34",
         "currency": "MOP",
         "discount_amount": "0.00",
         "merchant_coupon_amt": "0",
         "merchant_discount_amt": "0.00",
         "order_amount": "6.12",
         "order_flag": "100",
         "out_trans_id": "d65aba06-7eed-4d29-9cd4-442d5f9e9c85",
         "pay_channel_type": "mpay",
         "pay_time": "2025-04-28 17:55:00",
         "platform_coupon_amt": "0",
         "platform_discount_amt": "0.00",
         "receipt_amount": "6.12",
         "result_code": "0000",
         "result_msg": "交易成功",
         "store_id": "0001",
         "terminal_id": "T80001",
         "trade_no": "2025042817543400320395",
         "trans_amount": "6.12",
         "trans_id": "2025042817543410000001",
         "trans_status": "SUCCESS",
         "user_pay_amount": "6.12",
         "user_pay_ccy": "MOP",
         "user_site": "MO",
         "wallet_no": "108801"
         },
         "is_success": "T"
         }
         */
    }


    /**
     * 预创建订单
     *
     * @throws Exception
     */
    private static void mpayTradePrecreate(String outTransId) throws Exception {
        String service = MacauPassServiceConstants.TRADE_PRECREATE;
        MacauPassRequestBuilder requestBuilder = new MacauPassRequestBuilder(orgId, service, MacauPassChannelTypeEnum.SYSTEM.getType());

        Map<String, Object> extendParams = new HashMap<>();
        extendParams.put(MacauPassRequestFieldsConstants.SUB_MERCHANT_ID, subMerchantId);
        extendParams.put(MacauPassRequestFieldsConstants.SUB_MERCHANT_NAME, subMerchantName);
        extendParams.put(MacauPassRequestFieldsConstants.SUB_MERCHANT_INDUSTRY, subMerchantIndustry);
        extendParams.put(MacauPassRequestFieldsConstants.STORE_NAME, storeName);
        extendParams.put(MacauPassRequestFieldsConstants.STORE_ID, storeId);
        extendParams.put(MacauPassRequestFieldsConstants.TERMINAL_ID, terminalId);


        //构建应用级请求参数
        requestBuilder.set(MacauPassRequestFieldsConstants.OUT_TRANS_ID, outTransId);
        requestBuilder.set(MacauPassRequestFieldsConstants.SUBJECT, "商品名称_"+outTransId);
        requestBuilder.set(MacauPassRequestFieldsConstants.TOTAL_FEE, feeAmount);
        requestBuilder.set(MacauPassRequestFieldsConstants.BODY, MacauPassUtil.cleanAndTruncate("商品body_"+outTransId, 64));
        requestBuilder.set(MacauPassRequestFieldsConstants.CURRENCY, MacauPassCurrencyEnum.MOP.getCode());

        requestBuilder.set(MacauPassRequestFieldsConstants.EXTEND_PARAMS, extendParams);
        requestBuilder.set(MacauPassRequestFieldsConstants.IT_B_PAY, "3m");
        requestBuilder.set(MacauPassRequestFieldsConstants.PASSBACK_PARAMETERS, "PASSBACK_PARAMETERS_"+outTransId);
        requestBuilder.set(MacauPassRequestFieldsConstants.NOTIFY_URL, notifyUrl);


        Map<String, Object> result = new MacauPassClient().call(REQUEST_URL, service,  requestBuilder.build(), privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));
    }


    /**
     * 统一下单
     *
     * @throws Exception
     */
    private static void mpayTradeCreate(String outTransId) throws Exception {
        String service = MacauPassServiceConstants.TRADE_CREATE;

        MacauPassRequestBuilder requestBuilder = new MacauPassRequestBuilder(orgId, service, MacauPassChannelTypeEnum.SYSTEM.getType());

        Map<String, Object> extendParams = new HashMap<>();
        extendParams.put(MacauPassRequestFieldsConstants.SUB_MERCHANT_ID, subMerchantId);
        extendParams.put(MacauPassRequestFieldsConstants.SUB_MERCHANT_NAME, subMerchantName);
        extendParams.put(MacauPassRequestFieldsConstants.SUB_MERCHANT_INDUSTRY, subMerchantIndustry);
        extendParams.put(MacauPassRequestFieldsConstants.STORE_NAME, storeName);
        extendParams.put(MacauPassRequestFieldsConstants.STORE_ID, storeId);
        extendParams.put(MacauPassRequestFieldsConstants.TERMINAL_ID, terminalId);


        Map<String, Object> goodsParams = new HashMap<>();
        goodsParams.put(MacauPassRequestFieldsConstants.GOODS_DETAIL_GOODS_ID, "2000000");
        goodsParams.put(MacauPassRequestFieldsConstants.GOODS_DETAIL_GOODS_NAME, "测试商品");
        goodsParams.put(MacauPassRequestFieldsConstants.GOODS_DETAIL_GOODS_CATEGORY, "测试类目");
        goodsParams.put(MacauPassRequestFieldsConstants.GOODS_DETAIL_QUANTITY, "1");
        goodsParams.put(MacauPassRequestFieldsConstants.GOODS_DETAIL_BODY, "测试body");
        goodsParams.put(MacauPassRequestFieldsConstants.GOODS_DETAIL_PRICE, amount);
        goodsParams.put(MacauPassRequestFieldsConstants.GOODS_DETAIL_SHOW_URL, "https://www.shouqianba.com/");

        //构建应用级请求参数
        requestBuilder.set(MacauPassRequestFieldsConstants.OUT_TRANS_ID, outTransId);
        requestBuilder.set(MacauPassRequestFieldsConstants.SUBJECT, "商品名称_"+outTransId);
        requestBuilder.set(MacauPassRequestFieldsConstants.TOTAL_FEE, feeAmount);
        requestBuilder.set(MacauPassRequestFieldsConstants.PAY_CHANNEL, MacauPassPayChannelEnum.WECHAT.getType());
        requestBuilder.set(MacauPassRequestFieldsConstants.PRODUCT_CODE, MacauPassProductCodeEnum.JSAPI.getCode());
        requestBuilder.set(MacauPassRequestFieldsConstants.SUB_APPID, miniAppId);
        requestBuilder.set(MacauPassRequestFieldsConstants.SCENE, "ONLINE");
        requestBuilder.set(MacauPassRequestFieldsConstants.USER_ID, openid);
        requestBuilder.set(MacauPassRequestFieldsConstants.BODY, MacauPassUtil.cleanAndTruncate("商品body_"+outTransId, 64));
        requestBuilder.set(MacauPassRequestFieldsConstants.CURRENCY, MacauPassCurrencyEnum.MOP.getCode());

        requestBuilder.set(MacauPassRequestFieldsConstants.EXTEND_PARAMS, extendParams);
        requestBuilder.set(MacauPassRequestFieldsConstants.GOODS_DETAIL, Arrays.asList(goodsParams));
        requestBuilder.set(MacauPassRequestFieldsConstants.IT_B_PAY, "3m");
        requestBuilder.set(MacauPassRequestFieldsConstants.PASSBACK_PARAMETERS, "PASSBACK_PARAMETERS_"+outTransId);
        requestBuilder.set(MacauPassRequestFieldsConstants.NOTIFY_URL, notifyUrl);


        Map<String, Object> result = new MacauPassClient().call(REQUEST_URL, service,  requestBuilder.build(), privateKey, publicKey);
        System.out.println(JsonUtil.objectToJsonString(result));
    }




//    public static void testCleanAndTruncate() {
//        String originalString = "A这是BBB一abads个包含特AAAA殊字符的-`测试字 符串~!'/?.,@{}#[]$%^&*()_+/\\|\")，同时还有一些英文字符abc123。This is a test string with special characters and English chars.";
////        originalString = "Apple iPhone 6s plus (A1699) 16G";
//        String specialCharactersRegex = "[~!{}'[]?.,@^#\\$%&\\*\\(\\)_\\+\\/\\\\|)\"]"; // 定义特殊字符的正则表达式
//        String cleanedString = MacauPassUtil.cleanAndTruncate(originalString, 64);
//        System.out.println("原始字符串: " + originalString);
//        System.out.println("清理后的字符串: " + cleanedString);
//        System.out.println("字符串长度: " + cleanedString.length()); // 仅计算字符数，不考虑双字节字符
//
//
//
//
//        // 示例用法
//        System.out.println("100 分 -> " + MacauPassUtil.convertCentsToYuan("100") + " 元");   // 输出: 100 分 -> 1.00 元
//        System.out.println("12345 分 -> " + MacauPassUtil.convertCentsToYuan("12345") + " 元"); // 输出: 12345 分 -> 123.45 元
//        System.out.println("0 分 -> " + MacauPassUtil.convertCentsToYuan("0") + " 元");     // 输出: 0 分 -> 0.00 元
//        System.out.println("9999999 分 -> " + MacauPassUtil.convertCentsToYuan("9999999") + " 元"); // 输出: 9999999 分 -> 99999.99 元
//        System.out.println("无效输入 -> " + MacauPassUtil.convertCentsToYuan("abc") + " 元");   // 输出: 无效输入 -> null 元
//        System.out.println("空输入 -> " + MacauPassUtil.convertCentsToYuan(null) + " 元");    // 输出: 空输入 -> null 元
//        System.out.println("负数输入 -> " + MacauPassUtil.convertCentsToYuan("-12345") + " 元");    // 输出: 负数输入 -> -123.45 元
//    }
//
//
//
//
//
//    public static void testSign() throws JsonProcessingException, MpayException {
//        // 示例 Map
//        Map<String, Object> params = new HashMap<>();
//        params.put("zebra", "斑马");
//        params.put("age", 30);
//
//        Map<String, Object> personMap = new HashMap<>();
//        personMap.put("name", "Alice");
//        personMap.put("city", "Beijing");
//        personMap.put("address", "address");
//
//        Map<String, Object> personMap2 = new HashMap<>();
//        personMap2.put("name", "Alice");
//        personMap2.put("city", "Beijing");
//        personMap2.put("address", "address");
//        personMap2.put("son", personMap);
//
//        params.put("person", personMap2);
//        params.put("banana", "香蕉");
//        // 转换为排序后的字符串
//        Map<String, Object> result = SignUtil.generateSignStr(params, false);
//        // 打印结果
//        System.out.println("排序后的字符串: " + result.get(SignUtil.SIGN_STR));
//
//        String a = null;
//        if (a instanceof String) {
//            System.out.println("string");
//        }
//        if (a == null){
//            System.out.println("null");
//        }
//    }


    public static void main(String[] args) throws Exception {
        try {

            String outTransId = "7894259294305072";
//            String outTransId = UUID.randomUUID().toString();

//            System.out.println("outTransId: "+outTransId);
//            mpayTradeSpotpay(outTransId);

//            mpayTradeCancel(outTransId);

            mpayTradeQuery(outTransId);

//            mpayTradePrecreate(outTransId);

//            mpayTradeCreate(outTransId);

        } catch (Exception e) {
            System.out.println("执行失败, 错误如下:");
            e.printStackTrace();
        }
    }
}
