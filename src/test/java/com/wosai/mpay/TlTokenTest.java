package com.wosai.mpay;

import com.google.common.base.Optional;
import com.wosai.mpay.api.zjtlcb.TLCBTokenCache;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/12/25.
 */
public class TlTokenTest {

    public static void main(String[] args) {
        TLCBTokenCache cache = new TLCBTokenCache();
        String stringOptional = cache.getAccessToken("adfd6404-11f7-4f8b-998b-0c8b68567771",
                "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEQTDAwPQEB3pCxD8E/6xksZm1KLIpFqnjasTWeo5qIsrCizM5grKtBNPIpkxybmTg7xmrkwI9qf6Ub4zSLWFy2g==",
                "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgK8181M6GR3nvR7BOHL+M9UGBtZG2Li7gEy3EyG7EOh+gCgYIKoEcz1UBgi2hRANCAAQg25cdkxopEjOrfkF0TRb/fee82AEizHda1Ygedqrz5YAPo2ydhms8tcNlkUkvGdqreDqjCdqmaoeQsKKnB8cM",
                "c6206867-e0be-4755-8813-1f8d55aa253f",
                "https://dev.zjtlcb.com/api/SQB/");
        System.out.println(stringOptional);

    }

}
