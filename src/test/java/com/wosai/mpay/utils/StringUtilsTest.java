package com.wosai.mpay.utils;

import com.wosai.mpay.util.StringUtils;

public class StringUtilsTest {

    public static void main(String[] args) {
        System.out.println(StringUtils.cents2yuan(-1));
        System.out.println(StringUtils.cents2yuan(-11));
        System.out.println(StringUtils.cents2yuan(-121));

        System.out.println(StringUtils.yuan2cents(".1"));
        System.out.println(StringUtils.yuan2cents("-.11"));
        System.out.println(StringUtils.yuan2cents("-0.11"));
        System.out.println(StringUtils.yuan2cents("-.1"));
        System.out.println(StringUtils.yuan2cents("0."));
        System.out.println(StringUtils.yuan2cents("1."));
        System.out.println(StringUtils.yuan2cents("10."));
        System.out.println(StringUtils.yuan2cents("-1.10"));
        System.out.println(StringUtils.yuan2cents("1.01"));
        System.out.println(StringUtils.stringAppend("", ",", "ab", false));
        System.out.println(StringUtils.stringAppend("ab,ad", ",", "ab", false));
        System.out.println(StringUtils.stringAppend("ab,ad", ",", "ab", true));
        System.out.println(StringUtils.stringAppend("ab,ad,", ",", "ab", true));
        System.out.println(StringUtils.stringRemove("ab,ad,", ",", "ab"));
        System.out.println(StringUtils.stringRemove("ab,", ",", "ab"));

        System.out.println(StringUtils.stringRemove("ab,", ",", "ab"));

        System.out.println(StringUtils.byteTruncate("1903山东青岛二厂工厂店(19051495595506811中华人民共和国)", 50));
        System.out.println(StringUtils.byteTruncate("1903山东青岛二厂工厂店(190514955955068111234)", 50));
        System.out.println(StringUtils.byteTruncate("1903山东青岛二厂工厂店", 50));


    }
}
