package com.wosai.mpay.utils;

import com.wosai.mpay.api.fuyou.bean.RefundConcentrateTradeOut;
import com.wosai.mpay.api.fuyou.util.XmlConvertUtil;
import org.junit.Test;

import java.net.URLDecoder;

public class XmlConvertUtilTest {
    @Test
    public void Test() throws Exception {
        RefundConcentrateTradeOut refundConcentrateTradeOut = XmlConvertUtil.xml2Bean(URLDecoder.decode("%3Cxml%3E%3CrespCode%3E0000%3C%2FrespCode%3E%3CrespDesc%3E%BD%BB%D2%D7%B3%C9%B9%A6%3C%2FrespDesc%3E%3Csignature%3EjQJRe4X3vnazO2NDJXzW5GH0tcF%2FfrcAzYPrEnVCOKu4jEFSrRylUNmlQjYFrGLuWBFXL2pV4INczvGRTlLe8OfLxfzcN30yeUVZ%2Fv4WlKR9tKknRE61ccwA08KkwOgbz20e0mXSrrlJ1MZ%2B9FSMIoiCUKI7iImxYidUjcBIzf0%3D%3C%2Fsignature%3E%3CmchntCd%3E0002900F6571320%3C%2FmchntCd%3E%3CtraceNo%3E7897253078240974%3C%2FtraceNo%3E%3CsrcBatchNo%3E927379448309743616%3C%2FsrcBatchNo%3E%3Camt%3E9%3C%2Famt%3E%3CbusiType%3E02%3C%2FbusiType%3E%3CsrcFasSsn%2F%3E%3C%2Fxml%3E", "GBK"), RefundConcentrateTradeOut.class);
        assert refundConcentrateTradeOut!=null;
    }
}
