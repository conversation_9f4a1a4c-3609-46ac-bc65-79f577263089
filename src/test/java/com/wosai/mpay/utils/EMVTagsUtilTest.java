package com.wosai.mpay.utils;

import com.wosai.mpay.util.EMVTagsUtil;
import org.junit.Test;

import java.util.Map;

/**
 * @version 1.0
 * @author: yuhai
 * @program: mpay-sdk-homebrew
 * @className EMVTagsUtilTest
 * @description:
 * @create: 2025-07-02 16:51
 **/
public class EMVTagsUtilTest {

    @Test
    public void testEMVTags() {

        String emvTags = "9F34030000009F2608B22AF1A0C2087ED89F2701809F100706EB1203A000009F370427A36A209F36020002950500000000009A032312019C01009F02060000000077855F2A020156820220209F1A0201569F33036040C89F3501229F1E086D663636000000008407A00000000310109F0306000000000000";

        Map<String, String> emvMap = EMVTagsUtil.parseEMVTags(emvTags);

        for (String key : emvMap.keySet()) {
            System.out.println("key: "+ key +" value: " + emvMap.get(key));
        }
    }
}
